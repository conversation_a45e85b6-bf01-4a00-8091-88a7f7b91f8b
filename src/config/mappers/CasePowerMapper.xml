<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.changneng.sa.dao.CasePowerMapper">
  <resultMap id="BaseResultMap" type="com.changneng.sa.bean.CasePower">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="country" jdbcType="VARCHAR" property="country" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="is_power" jdbcType="INTEGER" property="isPower" />
    <result column="comment" jdbcType="VARCHAR" property="comment" />
    <result column="status" jdbcType="INTEGER" property="status" />
  </resultMap>
  <sql id="Base_Column_List">
    id, province, city, country, is_power, comment, code,status
  </sql>

  <select id="getCasePowerList" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from case_power
    where status = 0
  </select>
</mapper>
