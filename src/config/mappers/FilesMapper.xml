<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.changneng.sa.dao.FilesMapper">
	<resultMap id="BaseResultMap" type="com.changneng.sa.bean.Files">
		<id column="id" jdbcType="INTEGER" property="id" />
		<result column="areaCode" jdbcType="VARCHAR" property="areacode" />
		<result column="fileType" jdbcType="VARCHAR" property="filetype" />
		<result column="fileName" jdbcType="VARCHAR" property="filename" />
		<result column="fileCode" jdbcType="VARCHAR" property="filecode" />
		<result column="fileUrl" jdbcType="VARCHAR" property="fileurl" />
		<result column="expertAName" jdbcType="VARCHAR" property="expertaname" />
		<result column="fileMaterials" jdbcType="VARCHAR" property="fileMaterials" />
		<result column="expertAScore" jdbcType="REAL" property="expertascore" />
		<result column="expertBName" jdbcType="VARCHAR" property="expertbname" />
		<result column="expertBScore" jdbcType="REAL" property="expertbscore" />
		<result column="expertConsiderScore" jdbcType="REAL" property="expertconsiderscore" />
		<result column="CrossReviewNameA" jdbcType="VARCHAR" property="crossreviewnamea" />
		<result column="CrossReviewNameB" jdbcType="VARCHAR" property="crossreviewnameb" />
		<result column="CrossReviewAScore" jdbcType="REAL" property="crossreviewascore" />
		<result column="CrossReviewBScore" jdbcType="REAL" property="crossreviewbscore" />
		<result column="crossConsiderScore" jdbcType="REAL" property="crossconsiderscore" />

		<result property="inCheckCrossAScore" column="inCheckCrossAScore" />
		<result property="inCheckCrossBScore" column="inCheckCrossBScore" />
		<result property="inCheckCrossFinalScore" column="inCheckCrossFinalScore" />

		<result property="originalCrossScore" column="originalCrossScore" />
		<result property="originalExpertScore" column="originalExpertScore" />

		<result column="isConsider" jdbcType="VARCHAR" property="isconsider" />
		<result column="finalScore" jdbcType="REAL" property="finalscore" />
		<result column="expertAId" jdbcType="INTEGER" property="expertaid" />
		<result column="expertBId" jdbcType="INTEGER" property="expertbid" />
		<result column="crossReviewAId" jdbcType="INTEGER" property="crossreviewaid" />
		<result column="crossReviewBId" jdbcType="INTEGER" property="crossreviewbid" />
		<result column="type" jdbcType="INTEGER" property="type" />
		<result column="belongAreacode" jdbcType="INTEGER" property="belongareacode" />
		<result column="syncNumber" jdbcType="VARCHAR" property="syncnumber" />
		<result column="oldId" jdbcType="VARCHAR" property="oldid" />

		<result column="isConsiderCross" jdbcType="VARCHAR" property="isConsiderCross" />
		<result column="recommendFiles" jdbcType="VARCHAR" property="recommendFiles" />
		<result column="recommendRemarks" jdbcType="LONGVARCHAR" property="recommendRemarks" />
		<result column="isPublic"  jdbcType="INTEGER" property="isPublic"/>
		<result column="publicAddress"  jdbcType="VARCHAR" property="publicAddress"/>

		<result column="expertCommitAName"  jdbcType="VARCHAR" property="expertcommitaname"/>
		<result column="expertCommitBName"  jdbcType="VARCHAR" property="expertcommitbname"/>
		<result column="isConsiderExpCommit"  jdbcType="VARCHAR" property="isconsiderexpcommit"/>
		<result column="expertCommitAId" jdbcType="INTEGER" property="expertcommitaid" />
		<result column="expertCommitBId" jdbcType="INTEGER" property="expertcommitbid" />
		<result column="expertCommitAScore" jdbcType="REAL" property="expertcommitascore" />
		<result column="expertCommitBScore" jdbcType="REAL" property="expertcommitbscore" />
		<result column="recordCrossUser"  jdbcType="VARCHAR" property="recordCrossUser"/>

		<!-- 2018-11-23 新增  省内练兵单位表关联ID  字段 -->
		<result column="proID" jdbcType="INTEGER" property="proID" />
		<result column="closed" jdbcType="INTEGER" property="closed" />
	</resultMap>
	<resultMap extends="BaseResultMap" id="ResultMapWithBLOBs"
		type="com.changneng.sa.bean.FilesWithBLOBs">
		<result column="fileSimpleDesc" jdbcType="LONGVARCHAR"
			property="filesimpledesc" />
		<result column="fileDetiailDesc" jdbcType="LONGVARCHAR"
			property="filedetiaildesc" />
	</resultMap>
	<!-- 案卷概要信息结果集 -->
	<resultMap id="FilesSimpleResultMap" type="com.changneng.sa.bean.Files">
		<id column="id" jdbcType="INTEGER" property="id" />
		<result column="areaCode" jdbcType="VARCHAR" property="areacode" />
		<result column="fileType" jdbcType="VARCHAR" property="filetype" />
		<result column="fileName" jdbcType="VARCHAR" property="filename" />
		<result column="fileCode" jdbcType="VARCHAR" property="filecode" />
		<result column="fileUrl" jdbcType="VARCHAR" property="fileurl" />
		<result column="ocrFileUrl" jdbcType="VARCHAR" property="ocrFileUrl" />
		<result column="closed" jdbcType="INTEGER" property="closed" />
		<result column="workSpace" jdbcType="VARCHAR" property="workSpace" />
        <result column="expertAId" jdbcType="VARCHAR" property="expertaid"/>
        <result column="expertBId" jdbcType="VARCHAR" property="expertbid"/>
	</resultMap>
	<!-- 专家委员列表 -->
	<!-- e.id eID,f.id fID,e.scoredState ,f.fileCode ,f.expertAName,f.expertAScore,f.expertBName,f.expertBScore -->
	<resultMap type="com.changneng.sa.bean.CommitteeBean" id="committeeList">
		<result column="eID" jdbcType="INTEGER" property="eID"/>
		<result column="fID" jdbcType="INTEGER" property="fID"/>
		<result column="scoredState" jdbcType="VARCHAR" property="scoredState"/>
		<result column="fileCode" jdbcType="VARCHAR" property="fileCode"/>
		<result column="expertAName" jdbcType="VARCHAR" property="expertAName"/>
		<result column="expertAScore" jdbcType="REAL" property="expertAScore"/>
		<result column="expertBName" jdbcType="VARCHAR" property="expertBName"/>
		<result column="expertBScore" jdbcType="REAL" property="expertBScore"/>
		<result column="expertName" jdbcType="VARCHAR" property="expertName"/>
		<result column="expertFinalScore" jdbcType="REAL" property="expertFinalScore"/>
		<result column="fileType" jdbcType="VARCHAR" property="fileType"/>
		<!-- f.crossAID,f.crossBID,f.committAID,f.committBID -->
		<result column="crossAID" jdbcType="VARCHAR" property="crossAID"/>
		<result column="crossBID" jdbcType="VARCHAR" property="crossBID"/>
		<result column="expertAID" jdbcType="VARCHAR" property="expertAID"/>
		<result column="expertBID" jdbcType="VARCHAR" property="expertBID"/>

		<result column="committAID" jdbcType="VARCHAR" property="committAID"/>
		<result column="committAName" jdbcType="VARCHAR" property="committAName"/>
		<result column="committAScore" jdbcType="REAL" property="committAScore"/>
		<result column="committBID" jdbcType="VARCHAR" property="committBID"/>
		<result column="committBName" jdbcType="VARCHAR" property="committBName"/>
		<result column="committBScore" jdbcType="REAL" property="committBScore"/>
		<result column="caseCheckState" jdbcType="VARCHAR" property="caseCheckState"/>
		<result column="considerState" jdbcType="VARCHAR" property="considerState"/>
		<result column="entityState" jdbcType="VARCHAR" property="entityState"/>
		<result column="closed" jdbcType="INTEGER" property="closed" />
		<result column="errorState" jdbcType="INTEGER" property="errorState" />
		<result column="preRoundPaperScoreFlag" jdbcType="INTEGER" property="preRoundPaperScoreFlag" />
	</resultMap>
	<!-- 参选案卷公开情况列表 -->
	<resultMap  id="publicList" type="com.changneng.sa.bean.PublicListBean">
		<result column="id" jdbcType="INTEGER" property="id"/>
		<result column="fileCode" jdbcType="VARCHAR" property="fileCode"/>
		<result column="areaCode" jdbcType="VARCHAR" property="areaCode"/>
		<result column="fileType" jdbcType="VARCHAR" property="fileType"/>
		<result column="isPublic" jdbcType="INTEGER" property="isPublic"/>
		<result column="publicTypePenalize" jdbcType="VARCHAR" property="publicTypePenalize"/>
		<result column="publicAddressPenalize" jdbcType="VARCHAR" property="publicAddressPenalize"/>
		<result column="publicDescPenalize" jdbcType="VARCHAR" property="publicDescPenalize"/>
		<result column="province" jdbcType="VARCHAR" property="province"/>
		<result column="city" jdbcType="VARCHAR" property="city"/>
		<result column="Country" jdbcType="VARCHAR" property="Country"/>
		<result column="fileTypeName" jdbcType="VARCHAR" property="fileTypeName"/>
		<result column="publicAddress" jdbcType="VARCHAR" property="publicAddress"/>
		<result column="closed" jdbcType="INTEGER" property="closed" />
	</resultMap>

	<!-- 个人案卷信息 -->
	<resultMap  id="personFileList" type="com.changneng.sa.bean.PersonFileBean">
		<result column="id" jdbcType="INTEGER" property="id"/>
		<result column="fileCode" jdbcType="VARCHAR" property="fileCode"/>
		<result column="fileType" jdbcType="VARCHAR" property="fileType"/>
		<result column="fileSimpleDesc" jdbcType="VARCHAR" property="fileSimpleDesc"/>
		<result column="fileDetiailDesc" jdbcType="VARCHAR" property="fileDetiailDesc"/>
		<result column="closed" jdbcType="INTEGER" property="closed" />
	</resultMap>

	<sql id="Simple_Column_List">
		id, areaCode, fileType, fileName, fileCode,fileUrl,ocrFileUrl, closed,workSpace, expertAId, expertBId
	</sql>
	<sql id="Base_Column_List">
		id, areaCode, fileType, fileName, fileCode, fileUrl,ocrFileUrl, expertAName,
		expertAScore, expertBName,
		expertBScore, expertConsiderScore, CrossReviewNameA, CrossReviewNameB,
		CrossReviewAScore,CrossReviewBScore, crossConsiderScore,
		inCheckCrossAScore,inCheckCrossBScore,inCheckCrossFinalScore,
		isConsider, finalScore, expertAId, expertBId,
		crossReviewAId, crossReviewBId,type,belongAreacode,oldId,isPublic,publicAddress,
		expertCommitAName,expertCommitBName,isConsiderExpCommit,expertCommitAId,expertCommitBId,
		expertCommitAScore,expertCommitBScore,recordCrossUser,originalCrossScore,originalExpertScore,proID,closed,caseCheckState
	</sql>
	<sql id="Blob_Column_List">
		fileSimpleDesc, fileDetiailDesc
	</sql>
	<select id="selectByPrimaryKey" parameterType="java.lang.Integer"
		resultMap="ResultMapWithBLOBs">
		select
		<include refid="Base_Column_List" />
		,
		<include refid="Blob_Column_List" />
		from files
		where id = #{id,jdbcType=INTEGER}
	</select>

	<select id="selectFileByAreaCodeList" parameterType="java.lang.String"
		resultMap="ResultMapWithBLOBs">
		select
		<include refid="Base_Column_List" />
		,
		<include refid="Blob_Column_List" />
		from files
		where areaCode=#{areacode,jdbcType=VARCHAR} and
		fileType=#{type,jdbcType=VARCHAR} and type!=1 and reportType=0 order by id
	</select>

	<select id="queryFileinfoByTypeAndAreacode" parameterType="java.lang.String" resultMap="ResultMapWithBLOBs" >
		select
		<include refid="Base_Column_List" />
		,
		<include refid="Blob_Column_List" />
		from files
		where areaCode=#{areacode,jdbcType=VARCHAR} and
		fileType=#{type,jdbcType=VARCHAR} and type!=1 and reportType=0 order by id
	</select>

	<select id="checkFileExist" parameterType="java.lang.String"
		resultMap="ResultMapWithBLOBs">
		select
		<include refid="Base_Column_List" />
		,
		<include refid="Blob_Column_List" />
		from files
		where belongAreacode =#{belongareacode} and
		fileType=#{filetype,jdbcType=VARCHAR}
		and fileCode=#{filecode,jdbcType=VARCHAR}
		and reportType=0
	</select>

	<select id="checkJiChaAnJuanExist" parameterType="java.lang.String"
		resultMap="ResultMapWithBLOBs">
		select
		<include refid="Base_Column_List" />
		,
		<include refid="Blob_Column_List" />
		from files
		<where>
			1 = 1
			<if test="id != null and id != ''">
				and id != #{id}
			</if>
			<if test="filecode != null and filecode != ''">
				and filecode=#{filecode}
			</if>
			<if test="areacode != null and areacode != ''">
				and areacode=#{areacode}
			</if>
		</where>
	</select>

	<select id="getSelected" parameterType="java.lang.String"
		resultMap="ResultMapWithBLOBs">
		select * from files where areacode=#{areacode} and filetype=#{filetype} and
		filecode=#{filecode}
	</select>
	<delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
		delete from files
		where id = #{id,jdbcType=INTEGER}
	</delete>
	<insert id="batchInsert" parameterType="java.util.List">
		insert into files (id,areaCode, fileType,
		fileName, fileCode, fileUrl,
		expertAName, expertAScore, expertBName,
		expertBScore, expertConsiderScore, CrossReviewNameA,
		CrossReviewNameB, CrossReviewAScore, CrossReviewBScore,
		crossConsiderScore, isConsider, finalScore,
		fileSimpleDesc, fileDetiailDesc
		)
		values
		<foreach collection="list" item="obj" index="index" separator=",">
			(#{obj.id},#{obj.areacode}, #{obj.filetype},
			#{obj.filename}, #{obj.filecode}, #{obj.fileurl},
			#{obj.expertaname}, #{obj.expertascore}, #{obj.expertbname},
			#{obj.expertbscore}, #{obj.expertconsiderscore}, #{obj.crossreviewnamea},
			#{obj.crossreviewnameb}, #{obj.crossreviewascore},
			#{obj.crossreviewbscore},
			#{obj.crossconsiderscore}, #{obj.isconsider}, #{obj.finalscore},
			#{obj.filesimpledesc}, #{obj.filedetiaildesc}
			)
		</foreach>
	</insert>
	<insert id="insert" parameterType="com.changneng.sa.bean.FilesWithBLOBs">
		insert into files (id, areaCode, fileType,
		fileName, fileCode, fileUrl,
		expertAName, expertAScore, expertBName,
		expertBScore, expertConsiderScore, CrossReviewNameA,
		CrossReviewNameB, CrossReviewAScore, CrossReviewBScore,
		crossConsiderScore, isConsider, finalScore,
		expertAId, expertBId, crossReviewAId,
		crossReviewBId, fileSimpleDesc, fileDetiailDesc,type,belongAreacode,oldId,recordCrossUser
		)
		values (#{id,jdbcType=INTEGER}, #{areacode,jdbcType=VARCHAR},
		#{filetype,jdbcType=VARCHAR},
		#{filename,jdbcType=VARCHAR}, #{filecode,jdbcType=VARCHAR}, #{fileurl,jdbcType=VARCHAR},
		#{expertaname,jdbcType=VARCHAR}, #{expertascore,jdbcType=REAL},
		#{expertbname,jdbcType=VARCHAR},
		#{expertbscore,jdbcType=REAL}, #{expertconsiderscore,jdbcType=REAL},
		#{crossreviewnamea,jdbcType=VARCHAR},
		#{crossreviewnameb,jdbcType=VARCHAR},
		#{crossreviewascore,jdbcType=REAL},
		#{crossreviewbscore,jdbcType=REAL},
		#{crossconsiderscore,jdbcType=REAL}, #{isconsider,jdbcType=VARCHAR},
		#{finalscore,jdbcType=REAL},
		#{expertaid,jdbcType=INTEGER}, #{expertbid,jdbcType=INTEGER}, #{crossreviewaid,jdbcType=INTEGER},
		#{crossreviewbid,jdbcType=INTEGER},
		#{filesimpledesc,jdbcType=LONGVARCHAR},
		#{filedetiaildesc,jdbcType=LONGVARCHAR},
		#{type,jdbcType=INTEGER},#{belongareacode,jdbcType=INTEGER},#{oldid,jdbcType=INTEGER},
		#{recordCrossUser,jdbcType=VARCHAR}
		)
	</insert>
	<insert id="insertSelective" parameterType="com.changneng.sa.bean.FilesWithBLOBs"
		useGeneratedKeys="true" keyProperty="id">
		insert into files
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="id != null">
				id,
			</if>
			<if test="areacode != null">
				areaCode,
			</if>
			<if test="filetype != null">
				fileType,
			</if>
			<if test="filename != null">
				fileName,
			</if>
			<if test="filecode != null">
				fileCode,
			</if>
			<if test="fileurl != null">
				fileUrl,
			</if>
			<if test="expertaname != null">
				expertAName,
			</if>
			<if test="expertascore != null">
				expertAScore,
			</if>
			<if test="expertbname != null">
				expertBName,
			</if>
			<if test="expertbscore != null">
				expertBScore,
			</if>
			<if test="expertconsiderscore != null">
				expertConsiderScore,
			</if>
			<if test="crossreviewnamea != null">
				CrossReviewNameA,
			</if>
			<if test="crossreviewnameb != null">
				CrossReviewNameB,
			</if>
			<if test="crossreviewascore != null">
				CrossReviewAScore,
			</if>
			<if test="crossreviewbscore != null">
				CrossReviewBScore,
			</if>
			<if test="crossconsiderscore != null">
				crossConsiderScore,
			</if>
			<if test="isconsider != null">
				isConsider,
			</if>
			<if test="finalscore != null">
				finalScore,
			</if>
			<if test="expertaid != null">
				expertAId,
			</if>
			<if test="expertbid != null">
				expertBId,
			</if>
			<if test="crossreviewaid != null">
				crossReviewAId,
			</if>
			<if test="crossreviewbid != null">
				crossReviewBId,
			</if>
			<if test="filesimpledesc != null">
				fileSimpleDesc,
			</if>
			<if test="filedetiaildesc != null">
				fileDetiailDesc,
			</if>
			<if test="type != null">
				type,
			</if>
			<if test="belongareacode != null">
				belongAreacode,
			</if>
			<if test="oldid != null">
				oldId,
			</if>
			<if test="isPublic !=null">
				isPublic,
			</if>
			<if test="publicAddress !=null">
				publicAddress,
			</if>
			<if test="expertcommitaname !=null">
				expertCommitAName,
			</if>
			<if test="expertcommitbname !=null">
				expertCommitBName,
			</if>
			<if test="isconsiderexpcommit !=null">
				isConsiderExpCommit,
			</if>
			<if test="expertcommitaid !=null">
				expertCommitAId,
			</if>
			<if test="expertcommitbid !=null">
				expertCommitBId,
			</if>
			<if test="expertcommitascore !=null">
				expertCommitAScore,
			</if>
			<if test="expertcommitbscore !=null">
				expertCommitBScore,
			</if>
				<if test="recordCrossUser !=null">
				recordCrossUser,
			</if>
			<if test="proID !=null">
				proID,
			</if>
			<if test="isAi !=null">
				isAi,
			</if>
			<if test="closed !=null">
				closed,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="id != null">
				#{id,jdbcType=INTEGER},
			</if>
			<if test="areacode != null">
				#{areacode,jdbcType=VARCHAR},
			</if>
			<if test="filetype != null">
				#{filetype,jdbcType=VARCHAR},
			</if>
			<if test="filename != null">
				#{filename,jdbcType=VARCHAR},
			</if>
			<if test="filecode != null">
				#{filecode,jdbcType=VARCHAR},
			</if>
			<if test="fileurl != null">
				#{fileurl,jdbcType=VARCHAR},
			</if>
			<if test="expertaname != null">
				#{expertaname,jdbcType=VARCHAR},
			</if>
			<if test="expertascore != null">
				#{expertascore,jdbcType=REAL},
			</if>
			<if test="expertbname != null">
				#{expertbname,jdbcType=VARCHAR},
			</if>
			<if test="expertbscore != null">
				#{expertbscore,jdbcType=REAL},
			</if>
			<if test="expertconsiderscore != null">
				#{expertconsiderscore,jdbcType=REAL},
			</if>
			<if test="crossreviewnamea != null">
				#{crossreviewnamea,jdbcType=VARCHAR},
			</if>
			<if test="crossreviewnameb != null">
				#{crossreviewnameb,jdbcType=VARCHAR},
			</if>
			<if test="crossreviewascore != null">
				#{crossreviewascore,jdbcType=REAL},
			</if>
			<if test="crossreviewbscore != null">
				#{crossreviewbscore,jdbcType=REAL},
			</if>
			<if test="crossconsiderscore != null">
				#{crossconsiderscore,jdbcType=REAL},
			</if>
			<if test="isconsider != null">
				#{isconsider,jdbcType=VARCHAR},
			</if>
			<if test="finalscore != null">
				#{finalscore,jdbcType=REAL},
			</if>
			<if test="expertaid != null">
				#{expertaid,jdbcType=INTEGER},
			</if>
			<if test="expertbid != null">
				#{expertbid,jdbcType=INTEGER},
			</if>
			<if test="crossreviewaid != null">
				#{crossreviewaid,jdbcType=INTEGER},
			</if>
			<if test="crossreviewbid != null">
				#{crossreviewbid,jdbcType=INTEGER},
			</if>
			<if test="filesimpledesc != null">
				#{filesimpledesc,jdbcType=LONGVARCHAR},
			</if>
			<if test="filedetiaildesc != null">
				#{filedetiaildesc,jdbcType=LONGVARCHAR},
			</if>
			<if test="type != null">
				#{type,jdbcType=INTEGER},
			</if>
			<if test="belongareacode != null">
				#{belongareacode,jdbcType=INTEGER},
			</if>
			<if test="oldid != null">
				#{oldid,jdbcType=INTEGER},
			</if>
			<if test="isPublic !=null">
				#{isPublic,jdbcType=INTEGER},
			</if>
			<if test="publicAddress !=null">
				#{publicAddress,jdbcType=VARCHAR}
			</if>
			<if test="expertcommitaname !=null">
				#{expertcommitaname,jdbcType=VARCHAR}
			</if>
			<if test="expertcommitbname !=null">
				#{expertcommitbname,jdbcType=VARCHAR}
			</if>
			<if test="isconsiderexpcommit !=null">
				#{isconsiderexpcommit,jdbcType=VARCHAR}
			</if>
			<if test="expertcommitaid !=null">
				#{expertcommitaid,jdbcType=INTEGER}
			</if>
			<if test="expertcommitbid !=null">
				#{expertcommitbid,jdbcType=INTEGER}
			</if>
			<if test="expertcommitascore != null">
				#{expertcommitascore,jdbcType=REAL},
			</if>
			<if test="expertcommitbscore != null">
				#{expertcommitbscore,jdbcType=REAL},
			</if>
				<if test="recordCrossUser != null">
				#{recordCrossUser,jdbcType=VARCHAR},
			</if>
			<if test="proID !=null">
				#{proID,jdbcType=INTEGER},
			</if>
			<if test="isAi !=null">
				#{isAi,jdbcType=VARCHAR},
			</if>
			<if test="closed !=null">
				#{closed,jdbcType=INTEGER}
			</if>
		</trim>
	</insert>
	<update id="batchUpdate" parameterType="java.util.List">
		<foreach collection="list" item="item" index="index" open="" close="" separator="">
			<if test="item.id != null and item.id !=''">
				update files
				<set>
					<if test="item.areacode != null">
						areaCode = #{item.areacode,jdbcType=VARCHAR},
					</if>
					<if test="item.filetype != null">
						fileType = #{item.filetype,jdbcType=VARCHAR},
					</if>
					<if test="item.filecode != null">
						fileCode = #{item.filecode,jdbcType=VARCHAR},
					</if>
					<if test="item.filesimpledesc != null">
						fileSimpleDesc = #{item.filesimpledesc,jdbcType=LONGVARCHAR},
					</if>
					<if test="item.filedetiaildesc != null">
						fileDetiailDesc = #{item.filedetiaildesc,jdbcType=LONGVARCHAR},
					</if>
					<if test="item.belongareacode != null">
						belongAreacode = #{item.belongareacode,jdbcType=VARCHAR},
					</if>
					<if test="item.recommendFiles != null">
						recommendFiles = #{item.recommendFiles,jdbcType=VARCHAR},
					</if>
					<if test="item.recommendRemarks != null">
						recommendRemarks = #{item.recommendRemarks,jdbcType=LONGVARCHAR},
					</if>
				</set>
				<where>
					id=#{item.id};
				</where>
			</if>
		</foreach>
	</update>
	<update id="batchUpdateByAdditional" parameterType="java.util.List">
		<foreach collection="list" item="item" index="index" open="" close="" separator="">
			<if test="item.id != null and item.id !=''">
				update files
				<set>
					<if test="item.finalscore != null">
						finalScore = #{item.finalscore,jdbcType=REAL}
					</if>
				</set>
				<where>
					id=#{item.id};
				</where>
			</if>
		</foreach>
	</update>

	<update id="updateByPrimaryKeySetIspublicNull" parameterType="com.changneng.sa.bean.FilesWithBLOBs">
		update files
		<set>
				isPublic =null,
		</set>
		where id = #{id,jdbcType=INTEGER}
	</update>

	<update id="updateByPrimaryKeySelective" parameterType="com.changneng.sa.bean.FilesWithBLOBs">
		update files
		<set>
			<if test="areacode != null">
				areaCode = #{areacode,jdbcType=VARCHAR},
			</if>
			<if test="filetype != null">
				fileType = #{filetype,jdbcType=VARCHAR},
			</if>
			<if test="filename != null">
				fileName = #{filename,jdbcType=VARCHAR},
			</if>
			<if test="fileMaterials != null">
				fileMaterials = #{fileMaterials,jdbcType=VARCHAR},
			</if>
			<if test="filecode != null">
				fileCode = #{filecode,jdbcType=VARCHAR},
			</if>
			<if test="fileurl != null">
				fileUrl = #{fileurl,jdbcType=VARCHAR},
			</if>
			<if test="expertaname != null">
				expertAName = #{expertaname,jdbcType=VARCHAR},
			</if>
			<if test="expertascore != null">
				expertAScore = #{expertascore,jdbcType=REAL},
			</if>
			<if test="expertbname != null">
				expertBName = #{expertbname,jdbcType=VARCHAR},
			</if>
			<if test="expertbscore != null">
				expertBScore = #{expertbscore,jdbcType=REAL},
			</if>
			<if test="expertconsiderscore != null">
				expertConsiderScore = #{expertconsiderscore,jdbcType=REAL},
			</if>
			<if test="expertconsiderscore2 != null">
				expertconsiderscore2 = #{expertconsiderscore2,jdbcType=REAL},
			</if>
			<if test="crossreviewnamea != null">
				CrossReviewNameA = #{crossreviewnamea,jdbcType=VARCHAR},
			</if>
			<if test="crossreviewnameb != null">
				CrossReviewNameB = #{crossreviewnameb,jdbcType=VARCHAR},
			</if>
			<if test="crossreviewascore != null">
				CrossReviewAScore = #{crossreviewascore,jdbcType=REAL},
			</if>
			<if test="crossreviewbscore != null">
				CrossReviewBScore = #{crossreviewbscore,jdbcType=REAL},
			</if>
			<if test="crossconsiderscore != null">
				crossConsiderScore = #{crossconsiderscore,jdbcType=REAL},
			</if>

			<if test="inCheckCrossAScore != null">
				inCheckCrossAScore = #{inCheckCrossAScore,jdbcType=REAL},
			</if>
			<if test="inCheckCrossBScore != null">
				inCheckCrossBScore = #{inCheckCrossBScore,jdbcType=REAL},
			</if>
			<if test="inCheckCrossFinalScore != null">
				inCheckCrossFinalScore = #{inCheckCrossFinalScore,jdbcType=REAL},
			</if>

			<if test="isconsider != null">
				isConsider = #{isconsider,jdbcType=VARCHAR},
			</if>
			<if test="finalscore != null">
				finalScore = #{finalscore,jdbcType=REAL},
			</if>
			<if test="expertaid != null">
				expertAId = #{expertaid,jdbcType=INTEGER},
			</if>
			<if test="expertbid != null">
				expertBId = #{expertbid,jdbcType=INTEGER},
			</if>
			<if test="crossreviewaid != null">
				crossReviewAId = #{crossreviewaid,jdbcType=INTEGER},
			</if>
			<if test="crossreviewbid != null">
				crossReviewBId = #{crossreviewbid,jdbcType=INTEGER},
			</if>
			<if test="filesimpledesc != null">
				fileSimpleDesc = #{filesimpledesc,jdbcType=LONGVARCHAR},
			</if>
			<if test="filedetiaildesc != null">
				fileDetiailDesc = #{filedetiaildesc,jdbcType=LONGVARCHAR},
			</if>
			<if test="type != null">
				type = #{type,jdbcType=INTEGER},
			</if>
			<if test="belongareacode != null">
				belongAreacode = #{belongareacode,jdbcType=INTEGER},
			</if>
			<if test="isConsiderCross != null">
				isConsiderCross = #{isConsiderCross,jdbcType=INTEGER},
			</if>
			<if test="oldid != null">
				oldId = #{oldid,jdbcType=INTEGER},
			</if>
			<if test="isPublic != null">
				isPublic = #{isPublic,jdbcType=INTEGER},
			</if>
			<if test="publicAddress != null">
				publicAddress = #{publicAddress,jdbcType=VARCHAR},
			</if>
			<if test="expertcommitaname != null">
				expertCommitAName = #{expertcommitaname,jdbcType=VARCHAR},
			</if>

			<if test="expertcommitbname != null">
				expertCommitBName = #{expertcommitbname,jdbcType=VARCHAR},
			</if>

			<if test="isconsiderexpcommit != null">
				isConsiderExpCommit = #{isconsiderexpcommit,jdbcType=VARCHAR},
			</if>
				<if test="expertcommitaid != null">
				expertCommitAId = #{expertcommitaid,jdbcType=INTEGER},
			</if>
				<if test="expertcommitbid != null">
				expertCommitBId = #{expertcommitbid,jdbcType=INTEGER},
			</if>
				<if test="expertcommitascore != null">
				expertCommitAScore = #{expertcommitascore,jdbcType=REAL},
			</if>
				<if test="expertcommitbscore != null">
				expertCommitBScore = #{expertcommitbscore,jdbcType=REAL},
			</if>
			<if test="recordCrossUser != null">
				recordCrossUser = #{recordCrossUser,jdbcType=VARCHAR},
			</if>
			<if test="proID != null">
				proID = #{proID,jdbcType=INTEGER},
			</if>
			<if test="caseCheckState != null">
				caseCheckState = #{caseCheckState,jdbcType=VARCHAR},
			</if>
		</set>
		where id = #{id,jdbcType=INTEGER}
	</update>
	<update id="updateByPrimaryKeyWithBLOBs" parameterType="com.changneng.sa.bean.FilesWithBLOBs">
		update files
		set areaCode = #{areacode,jdbcType=VARCHAR},
		fileType = #{filetype,jdbcType=VARCHAR},
		fileName = #{filename,jdbcType=VARCHAR},
		fileCode = #{filecode,jdbcType=VARCHAR},
		fileUrl = #{fileurl,jdbcType=VARCHAR},
		expertAName = #{expertaname,jdbcType=VARCHAR},
		expertAScore = #{expertascore,jdbcType=REAL},
		expertBName = #{expertbname,jdbcType=VARCHAR},
		expertBScore = #{expertbscore,jdbcType=REAL},
		expertConsiderScore = #{expertconsiderscore,jdbcType=REAL},
		CrossReviewNameA = #{crossreviewnamea,jdbcType=VARCHAR},
		CrossReviewNameB = #{crossreviewnameb,jdbcType=VARCHAR},
		CrossReviewAScore = #{crossreviewascore,jdbcType=REAL},
		CrossReviewBScore = #{crossreviewbscore,jdbcType=REAL},
		crossConsiderScore = #{crossconsiderscore,jdbcType=REAL},
		isConsider = #{isconsider,jdbcType=VARCHAR},
		finalScore = #{finalscore,jdbcType=REAL},
		expertAId = #{expertaid,jdbcType=INTEGER},
		expertBId = #{expertbid,jdbcType=INTEGER},
		crossReviewAId = #{crossreviewaid,jdbcType=INTEGER},
		crossReviewBId = #{crossreviewbid,jdbcType=INTEGER},
		fileSimpleDesc = #{filesimpledesc,jdbcType=LONGVARCHAR},
		fileDetiailDesc = #{filedetiaildesc,jdbcType=LONGVARCHAR},
		type=#{type,jdbcType=INTEGER},
		belongAreacode=#{belongareacode,jdbcType=VARCHAR},
		recordCrossUser=#{recordCrossUser,jdbcType=VARCHAR},
		proID = #{proID,jdbcType=INTEGER}
		where id = #{id,jdbcType=INTEGER}
	</update>
	<update id="updateByPrimaryKey" parameterType="com.changneng.sa.bean.Files">
		update files
		<set>
			<if test="areacode != null">
				areaCode = #{areacode,jdbcType=VARCHAR},
			</if>
			<if test="filetype != null">
				fileType = #{filetype,jdbcType=VARCHAR},
			</if>
			<if test="filename != null">
				fileName = #{filename,jdbcType=VARCHAR},
			</if>
			<if test="filecode != null">
				fileCode = #{filecode,jdbcType=VARCHAR},
			</if>
			<if test="fileurl != null">
				fileUrl = #{fileurl,jdbcType=VARCHAR},
			</if>
			<if test="expertaname != null">
				expertAName = #{expertaname,jdbcType=VARCHAR},
			</if>
			<if test="expertascore != null">
				expertAScore = #{expertascore,jdbcType=REAL},
			</if>
			<if test="expertbname != null">
				expertBName = #{expertbname,jdbcType=VARCHAR},
			</if>
			<if test="expertbscore != null">
				expertBScore = #{expertbscore,jdbcType=REAL},
			</if>
			<if test="expertconsiderscore != null">
				expertConsiderScore = #{expertconsiderscore,jdbcType=REAL},
			</if>
			<if test="crossreviewnamea != null">
				CrossReviewNameA = #{crossreviewnamea,jdbcType=VARCHAR},
			</if>
			<if test="crossreviewnameb != null">
				CrossReviewNameB = #{crossreviewnameb,jdbcType=VARCHAR},
			</if>
			<if test="crossreviewascore != null">
				CrossReviewAScore = #{crossreviewascore,jdbcType=REAL},
			</if>
			<if test="crossreviewbscore != null">
				CrossReviewBScore = #{crossreviewbscore,jdbcType=REAL},
			</if>
			<if test="crossconsiderscore != null">
				crossConsiderScore = #{crossconsiderscore,jdbcType=REAL},
			</if>
			<if test="isconsider != null">
				isConsider = #{isconsider,jdbcType=VARCHAR},
			</if>
			<if test="finalscore != null">
				finalScore = #{finalscore,jdbcType=REAL},
			</if>
			<if test="expertaid != null">
				expertAId = #{expertaid,jdbcType=INTEGER},
			</if>
			<if test="expertbid != null">
				expertBId = #{expertbid,jdbcType=INTEGER},
			</if>
			<if test="crossreviewaid != null">
				crossReviewAId = #{crossreviewaid,jdbcType=INTEGER},
			</if>
			<if test="crossreviewbid != null">
				crossReviewBId = #{crossreviewbid,jdbcType=INTEGER},
			</if>
			<if test="type != null">
				type= #{type,jdbcType=INTEGER},
			</if>
			<if test="belongareacode != null">
				belongAreacode=#{belongareacode,jdbcType=VARCHAR},
			</if>
			<if test="syncnumber != null">
				syncNumber=#{syncnumber,jdbcType=VARCHAR}
			</if>
				<if test="expertcommitaname != null">
				expertCommitAName=#{expertcommitaname,jdbcType=VARCHAR}
			</if>
				<if test="expertcommitbname != null">
				expertCommitBName=#{expertcommitbname,jdbcType=VARCHAR}
			</if>
				<if test="isconsiderexpcommit != null">
				isConsiderExpCommit=#{isconsiderexpcommit,jdbcType=VARCHAR}
			</if>
			<if test="expertcommitaid != null">
				expertCommitAId=#{expertcommitaid,jdbcType=INTEGER}
			</if>
			<if test="expertcommitbid != null">
				expertCommitBId=#{expertcommitbid,jdbcType=INTEGER}
			</if>
			<if test="expertcommitascore != null">
				expertCommitAScore=#{expertcommitascore,jdbcType=REAL}
			</if>
			<if test="expertcommitbscore != null">
				expertCommitBScore=#{expertcommitbscore,jdbcType=REAL}
			</if>
				<if test="recordCrossUser != null">
				recordCrossUser=#{recordCrossUser,jdbcType=REAL}
			</if>
			<if test="proID != null">
				proID=#{proID,jdbcType=INTEGER}
			</if>

			<if test="ocrFileUrl != null">
				ocrFileUrl=#{ocrFileUrl,jdbcType=INTEGER}
			</if>
		</set>
		where id = #{id,jdbcType=INTEGER}
	</update>

	<!-- 根据案卷类型获取案卷信息 -->
	<select id="selectListByFileType" resultMap="FilesSimpleResultMap">
		select
		id, (case when IFNULL(areaCode,'')='' then belongAreacode else areaCode end) as areaCode, fileType, fileName, fileCode, closed
		from files
		where
			fileType = #{filetype,jdbcType=VARCHAR}
			AND expertConsiderScore is NULL
			and expertAId is  null
			and expertBId is  null
			and fileUrl is not null
			and isDeleted = 0
			and isOne = 0
	</select>

	<!-- 根据案卷类型获取案卷信息 -->
	<select id="selectFpzjQi" resultMap="FilesSimpleResultMap">
		select
		id, (case when IFNULL(areaCode,'')='' then belongAreacode else areaCode end) as areaCode, fileType, fileName, fileCode, closed
		from files
		where
		expertAId is  null
		and expertBId is  null
	</select>
	<!-- 根据案卷类型获取案卷信息 -->
	<select id="selectByExpertName" resultMap="FilesSimpleResultMap">
		select
		id, (case when IFNULL(areaCode,'')='' then belongAreacode else areaCode end) as areaCode, fileType, fileName, fileCode
		from files
		where 1=1
		<if test="expertclass=='0'.toString()" >
		    and expertAName = #{name}
		</if>
		<if test="expertclass=='1'.toString()" >
			and expertBName = #{name}
		</if>
	</select>
	<!-- 根据案卷类型获取案卷信息 -->
	<select id="selectFilesByIsCommit" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from files
		where isConsiderExpCommit = 1
	</select>

	<!-- 根据案卷类型获取案卷信息 -->
	<select id="selectListByFileTypeAndCrossConsiderScore"
		resultMap="FilesSimpleResultMap">
		select
		<include refid="Simple_Column_List" />
		from files
		where fileType = #{filetype,jdbcType=VARCHAR} and crossConsiderScore
		>=${crossConsiderScore}
	</select>


	<!-- 根据区划code前两位模糊查询案卷信息 -->

	<select id="selectListByAreaListPara" parameterType="java.util.List"
		resultMap="FilesSimpleResultMap">
		select
		<include refid="Simple_Column_List" />
		from files
		<where>
			<if test="list.size() != 0">
				<foreach collection="list" item="item" index="index"
					separator="">
					or belongAreacode like '${item}%'
				</foreach>
			</if>
		</where>

	</select>

	<!-- 批量更新案卷中：两位交叉评审人员信息 -->
	<update id="updateFilesOfCrossByBatchList" parameterType="java.util.List">
		update files
		<trim prefix="set" suffixOverrides=",">
			<trim prefix="crossReviewAId =case" suffix="end,">
				<foreach collection="list" item="i" index="index">
					<if test="i.crossreviewaid!=null">
						when id=#{i.id} then #{i.crossreviewaid}
					</if>
				</foreach>
			</trim>
			<trim prefix=" CrossReviewNameA =case" suffix="end,">
				<foreach collection="list" item="i" index="index">
					<if test="i.crossreviewnamea!=null">
						when id=#{i.id} then #{i.crossreviewnamea}
					</if>
				</foreach>
			</trim>

			<trim prefix="crossReviewBId =case" suffix="end,">
				<foreach collection="list" item="i" index="index">
					<if test="i.crossreviewbid!=null">
						when id=#{i.id} then #{i.crossreviewbid}
					</if>
				</foreach>
			</trim>
			<trim prefix=" CrossReviewNameB =case" suffix="end,">
				<foreach collection="list" item="i" index="index">
					<if test="i.crossreviewnameb!=null">
						when id=#{i.id} then #{i.crossreviewnameb}
					</if>
				</foreach>
			</trim>
		</trim>
		where
		<foreach collection="list" separator="or" item="i" index="index">
			id=#{i.id}
		</foreach>
	</update>

	<!-- 批量更新案卷中：两位专家信息 -->
	<update id="updateFilesOfExpertByBatchList" parameterType="java.util.List">
		update files
		<trim prefix="set" suffixOverrides=",">
			<trim prefix="expertAId =case" suffix="end,">
				<foreach collection="list" item="i" index="index">
					<if test="i.expertaid!=null">
						when id=#{i.id} then #{i.expertaid}
					</if>
				</foreach>
			</trim>
			<trim prefix=" expertAName =case" suffix="end,">
				<foreach collection="list" item="i" index="index">
					<if test="i.expertaname!=null">
						when id=#{i.id} then #{i.expertaname}
					</if>
				</foreach>
			</trim>

			<trim prefix="expertBId =case" suffix="end,">
				<foreach collection="list" item="i" index="index">
					<if test="i.expertbid!=null">
						when id=#{i.id} then #{i.expertbid}
					</if>
				</foreach>
			</trim>
			<trim prefix=" expertBName =case" suffix="end,">
				<foreach collection="list" item="i" index="index">
					<if test="i.expertbname!=null">
						when id=#{i.id} then #{i.expertbname}
					</if>
				</foreach>
			</trim>
			<trim prefix=" isConsider =case" suffix="end,">
				<foreach collection="list" item="i" index="index">
					<if test="i.isconsider!=null">
						when id=#{i.id} then #{i.isconsider}
					</if>
				</foreach>
			</trim>
		</trim>
		where
		<foreach collection="list" separator="or" item="i" index="index">
			id=#{i.id}
		</foreach>
	</update>

	<!-- 批量更新案卷中的 委员 信息-->
		<update id="updateFilesOfCommitByBatchList" parameterType="java.util.List">
		update files
		<trim prefix="set" suffixOverrides=",">
			<trim prefix="expertCommitAId =case" suffix="end,">
				<foreach collection="list" item="i" index="index">
					<if test="i.expertcommitaid!=null">
						when id=#{i.id} then #{i.expertcommitaid}
					</if>
				</foreach>
			</trim>
			<trim prefix=" expertCommitAName =case" suffix="end,">
				<foreach collection="list" item="i" index="index">
					<if test="i.expertcommitaname!=null">
						when id=#{i.id} then #{i.expertcommitaname}
					</if>
				</foreach>
			</trim>

			<trim prefix=" isConsiderExpCommit =case" suffix="end,">
				<foreach collection="list" item="i" index="index">
					<if test="i.isconsiderexpcommit!=null">
						when id=#{i.id} then #{i.isconsiderexpcommit}
					</if>
				</foreach>
			</trim>

			<trim prefix="expertCommitBId =case" suffix="end,">
				<foreach collection="list" item="i" index="index">
					<if test="i.expertcommitbid!=null">
						when id=#{i.id} then #{i.expertcommitbid}
					</if>
				</foreach>
			</trim>
			<trim prefix=" expertCommitBName =case" suffix="end,">
				<foreach collection="list" item="i" index="index">
					<if test="i.expertcommitbname!=null">
						when id=#{i.id} then #{i.expertcommitbname}
					</if>
				</foreach>
			</trim>
		</trim>
		where
		<foreach collection="list" separator="or" item="i" index="index">
			id=#{i.id}
		</foreach>
	</update>

	<!-- 行政处罚系统获取文件所准备bean -->
	<select id="getFilesByXzcf" parameterType="java.util.List"
		resultMap="BaseResultMap">
		select id, areaCode, fileType,oldId,fileCode from files where fileType !='5' and
		oldId is not null and oldId !='' and syncNumber is null  ORDER BY id
		ASC
	</select>

	<!-- 第二套方案查询files表集合数据 -->
	<select id="getFilesByTypeAndSyncnumber" parameterType="java.util.List"
		resultMap="BaseResultMap">
		select id, areaCode, syncNumber, fileName from files
		where fileUrl is null
		  and syncNumber !=''
		ORDER BY id ASC
	</select>
	<!-- 获得files案卷表的 总案卷数 getfilesNum -->
	<select id="getfilesNum" resultType="int">
		select count(*) from files
	</select>
	<!-- 需要合议数 getHeYiNum -->
	<select id="getHeYiNum" resultType="int">

		select count(*) from files
		where fileType!=5 and expertAId is not null and expertBId is not null and
		isConsider ='1'
	</select>
	<!--不需要合议数 -->
	<select id="getNoHeiYiNum" resultType="int">
		select count(*) from files where isConsider ='0'
	</select>
	<!-- 交叉阶段 需要合议数 getHeYiNum -->
	<select id="getCrossHeYiNum" resultType="int">
		select count(*) from files where isConsiderCross ='1'
	</select>
	<!--不需要合议数 -->
	<select id="getCrossNoHeYiNum" resultType="int">
		select count(*) from files where isConsiderCross ='0'
	</select>
	<select id="getcanXuanDanWeiZongShu" resultType="int">
		select count(*) from files where type ='0'
	</select>

	<!-- 案卷列表 -->
	<select id="getFilesList" parameterType="java.util.List"
		resultType="com.changneng.sa.bean.FilesList">
		select
		t.Country,t.city,t.province,f.fileName,f.fileCode,f.id,f.fileUrl,f.reportType FROM
		files f LEFT JOIN T_area t
		on f.belongAreacode=t.code
		<where>
			<if test="areaType != null and areaType != ''">
				and t.areaLevel=#{areaType}
			</if>

			<if test="type != null and type!=''">
				<if test="type ==1">
					and f.type = 1

				</if>
				<if test="type ==0">
					and f.type = 0

				</if>
				<if test="type ==2">
					and  f.type =2

				</if>
			</if>

			<if test="reportType != null and reportType != '' ">
				and f.reportType=#{reportType}
			</if>

			<if test="fileType != null and fileType != '' ">
				and f.fileType=#{fileType}
			</if>
			<if test="keyword != null and keyword != ''">
				and f.fileCode like '%${keyword}%'
			</if>
		</where>
		order by t.code
	</select>



	<!--专家 案卷列表 -->
	<select id="getRecommendFiles" parameterType="java.util.List"  resultType="com.changneng.sa.bean.FilesList">
		select
		t.Country,t.city,t.province, f.fileName,f.fileCode,f.id,f.fileUrl,f.expertAId,f.expertBId,f.expertAName,f.expertAScore,f.expertBName,f.expertBScore,f.recommendFiles,f.recommendRemarks
		FROM files f
		LEFT JOIN T_area t on f.belongAreacode=t.code
		<where>
			1=1  and f.recommendFiles = '1'
			<if test="areaType != null and areaType != ''">
				and t.areaLevel=#{areaType}
			</if>
			<if test="type != null">
				<if test="type ==1">
					and (f.type = 1 or f.type =2)
				</if>
				<if test="type ==0">
					and (f.type = 0 or f.type =2)
				</if>
			</if>
			<if test="fileType != null and fileType != '' ">
				and f.fileType=#{fileType}
			</if>
			<if test="keyword != null and keyword != ''">
				and f.fileCode like '%${keyword}%'
			</if>
		</where>
	</select>
	<!-- fileType:案卷类型， -->
	<resultMap  id="ExtractMap" type="com.changneng.sa.bean.ExtractBean">
	<id column="id" jdbcType="INTEGER" property="id" />
	<result column="fileType" jdbcType="VARCHAR" property="fileType"/>
	<result column="fileUrl" jdbcType="VARCHAR" property="fileUrl"/>
	<result column="fileCode" jdbcType="VARCHAR" property="fileCode"/>
	<result column="fileTypeCode" jdbcType="VARCHAR" property="fileTypeCode"/>
	<result column="areaCode" jdbcType="VARCHAR" property="areaCode"/>
	<result column="province" jdbcType="VARCHAR" property="province"/>
	<result column="city" jdbcType="VARCHAR" property="city"/>
	<result column="country" jdbcType="VARCHAR" property="country"/>
	<result column="totalArea" jdbcType="VARCHAR" property="totalArea"/>
	<result column="fileTypeName" jdbcType="VARCHAR" property="fileTypeName"/>
	<result column="belongAreacode" jdbcType="VARCHAR" property="belongAreacode"/>
	</resultMap>
	<select id="selectExtract" parameterType="java.util.Map" resultMap="ExtractMap">
	select f.id,f.fileType,f.belongAreacode,f.fileUrl,f.fileCode,f.fileTypeCode,f.areaCode,e.province as province,e.country as country,e.city as city,CONCAT(IFNULL(province,''),IFNULL(city,''),IFNULL(country,'')) as totalArea,
	(
		case f.fileType
		WHEN 0 THEN '行政处罚案卷'
		WHEN 1 THEN '按日计罚案卷'
		WHEN 2 THEN '移送行政拘留案卷'
		when 3 THEN '环境污染犯罪移送公安机关案卷'
		WHEN 4 THEN '申请法院强制执行案卷'
		WHEN 5 THEN '发现问题的污染源现场监督检查稽查案卷'
		WHEN 6 THEN '查封扣押案卷'
		WHEN 7 THEN '停产限产案卷'
		ELSE ''
		END
	) fileTypeName
	from files f,T_area e
	<where>

		<if test="areaType != null and ''!=areaType">
		f.areaCode in (select  code as areaCode  from T_area where areaLevel = #{areaType})
		</if>
	  	<if test="fileCode != null and ''!=fileCode">
	  	and f.fileCode like concat('%',concat(#{fileCode},'%'))
	  	</if>
	  	<if test="fileType !=null and ''!=fileType">
	  	and f.fileType = #{fileType}
	  	</if>
	  	and f.areaCode = e.code
	  	and f.reportType = 1

	</where>
	order by f.areaCode
	</select>

	<!--随机抽取，批量插入案卷表SQL  -->
	<insert id="insertBatchList" parameterType="java.util.List">
    insert into files(areaCode,fileType,belongAreacode,fileName,fileCode,oldId,reportType)
    values
    <foreach collection="list" item="item" index="index"  separator="," >
         (
         #{item.areacode,jdbcType=VARCHAR},
         #{item.filetype,jdbcType=VARCHAR},
         #{item.belongareacode,jdbcType=VARCHAR},
         #{item.filename,jdbcType=VARCHAR},
         #{item.filecode,jdbcType=VARCHAR},
         #{item.oldid,jdbcType=VARCHAR},
         #{item.reporttype,jdbcType=VARCHAR}
         )
    </foreach>
</insert>
	<!--案卷抽取,批量插入files案卷表-->
	<insert id="insertFileBatchList" useGeneratedKeys="true" keyProperty="id" keyColumn="id" parameterType="java.util.List">
		INSERT INTO files(areaCode,fileType,belongAreacode,fileName,fileCode,oldId,reportType,closed,faKuanShuE,fileSource,fileSimpleInfo,remark)
		values
		<foreach collection="list" item="item" index="index"  separator="," >
			(
			#{item.areaCode,jdbcType=VARCHAR},
			#{item.fileType,jdbcType=VARCHAR},
			#{item.belongAreaCode,jdbcType=VARCHAR},
			#{item.fileName,jdbcType=VARCHAR},
			#{item.fileCode,jdbcType=VARCHAR},
			#{item.oldId,jdbcType=VARCHAR},
			#{item.reportType,jdbcType=VARCHAR},
			#{item.closed,jdbcType=INTEGER},
			#{item.faKuanShuE,jdbcType=VARCHAR},
			#{item.fileSource,jdbcType=VARCHAR},
			#{item.fileSimpleInfo,jdbcType=VARCHAR},
			#{item.remark,jdbcType=VARCHAR}
			)
		</foreach>
	</insert>
	<insert id="insertRandomFile" parameterType="com.changneng.sa.bean.FilesExtractDto">
		<selectKey keyProperty="id" resultType="int" order="AFTER">
			SELECT LAST_INSERT_ID()
		</selectKey>
		insert into files(areaCode,fileType,belongAreacode,fileName,fileCode,oldId,
		                  fileUrl,reportType,closed,faKuanShuE,fileSource,fileSimpleInfo,remark,isPersonal,personId)
		values (
			#{areaCode,jdbcType=VARCHAR},
			#{fileType,jdbcType=VARCHAR},
			#{belongAreaCode,jdbcType=VARCHAR},
			#{fileName,jdbcType=VARCHAR},
			#{fileCode,jdbcType=VARCHAR},
			#{oldId,jdbcType=VARCHAR},
			#{fileUrl,jdbcType=VARCHAR},
			#{reportType,jdbcType=VARCHAR},
			#{closed,jdbcType=INTEGER},
			#{faKuanShuE,jdbcType=VARCHAR},
			#{fileSource,jdbcType=VARCHAR},
			#{fileSimpleInfo,jdbcType=VARCHAR},
			#{remark,jdbcType=VARCHAR},
			#{isPersonal,jdbcType=VARCHAR},
			#{personId,jdbcType=VARCHAR}
			)
	</insert>

	<select id="selectAnJuanHao" parameterType="java.util.Map" resultType="com.changneng.sa.bean.Files">
select p.id,p.fileCode from penalizeSyncFile as p ,T_area as t
<where>
  <if test="query != null">
  	p.fileCode like concat('%',concat(#{query},'%'))
  </if>
  <if test="areaCode !=null">
  and p.areaCode like concat(#{areaCode},'%')
  </if>
  <if test="fileType !=null">
  and p.fileType = #{fileType}
  </if>
  and p.fileCode not in (select fileCode from files )
  <if test="areaType!=null">
   and t.areaLevel = #{areaType}
  </if>
  <if test="'1'== '1'">
  and  p.areaCode = t.code
  </if>
</where>

</select>
<update id="updateCrossInChick">
	UPDATE files set isInCheck =1 where id in(
		SELECT DISTINCT cf.fileId  from crossHandlFileList cf
		  LEFT JOIN crossHandlIndexScore  cs
		  on cf.id = cs.crossHandlId
		  where cs.inCheckValue =1
	)
</update>

<update id="updateAnJuanHao" parameterType="java.util.Map" >
<!-- update files
  	set fileCode = #{fileCode},isUpdate ='1'
 where id = #{id}
 -->
 UPDATE files f,penalizeSyncFile p
SET f.fileType = p.fileType,f.fileName= p.fileName,f.fileCode = p.fileCode,f.belongAreacode=p.areaCode,
f.oldId = p.oldId,f.syncNumber=null,f.isUpdate='1'

WHERE f.id=#{fID} and  p.id =#{pID}
</update>
<!-- getDiscussFilesList  -->
<select id="getDiscussFilesList" resultType="list" resultMap="BaseResultMap">
	 	select
		id, areaCode, fileType, fileName, fileCode, fileUrl, expertAName,
		expertAScore, expertBName,
		expertBScore, expertConsiderScore, CrossReviewNameA, CrossReviewNameB,
		CrossReviewAScore,
		CrossReviewBScore, crossConsiderScore, isConsider, finalScore, expertAId, expertBId,
		crossReviewAId, crossReviewBId
			from files
		 where <!-- fileType!=5 and  -->
		  ( expertConsiderScore is null or expertConsiderScore ='' )
		 and expertAId  is not null and expertBId is not null
		 and (isConsider !=1 or isConsider is null)
	</select>

	<select id="getGroupLeaderFilesList" resultType="list" resultMap="BaseResultMap">
	 	select
			id, areaCode, fileType, fileName, fileCode, fileUrl, expertAName,
			expertAScore, expertBName,
			expertBScore, expertConsiderScore, CrossReviewNameA, CrossReviewNameB,
			CrossReviewAScore,
			CrossReviewBScore, crossConsiderScore, isConsider, finalScore, expertAId, expertBId,
			crossReviewAId, crossReviewBId,expertCommitAName,expertCommitBName,isConsiderExpCommit,expertCommitAId,
			expertCommitBId,expertCommitAScore,expertCommitBScore
		from files
		where fileType!=5 and  ( expertConsiderScore is null or expertConsiderScore ='' )
		and expertCommitAId  is not null and expertCommitBId is not null and (isConsiderExpCommit !=1 or isConsiderExpCommit is null)
	</select>

	<select id="getFinalingJichaFilesList" resultType="list" resultMap="BaseResultMap">
	 	select
			id, areaCode, fileType, fileName, fileCode, fileUrl, expertAName,
			expertAScore, expertBName,
			expertBScore, expertConsiderScore, CrossReviewNameA, CrossReviewNameB,
			CrossReviewAScore,
			CrossReviewBScore, crossConsiderScore, isConsider, finalScore, expertAId, expertBId,
			crossReviewAId, crossReviewBId,expertCommitAName,expertCommitBName,isConsiderExpCommit,expertCommitAId,
			expertCommitBId,expertCommitAScore,expertCommitBScore
		from files
		where fileType=5 and expertCommitAId is not null and expertCommitAScore is null and (isConsiderExpCommit !=1 or isConsiderExpCommit is null)
	</select>


	<!--  and  ( expertCommitAScore is not null or expertConsiderScore ='' )  -->
<select id="selectCommittee" resultMap="committeeList" parameterType="java.util.Map">
	select
		e.id eID,f.id fID,e.scoredState,e.expertName,
		e.expertFinalScore,f.fileType, f.fileCode ,f.expertAName,f.expertAScore,f.expertBName,f.expertBScore,
		f.crossReviewAId as crossAID,f.crossReviewAId as crossBID,e.considerState,f.closed,f.caseCheckState,e.errorState,
		f.expertCommitAId as committAID,f.expertCommitBId as committBID ,f.expertAId as expertAID,f.expertBId as expertBID,e.entityState as entityState
	from files f,expertHandlFileList e
	<where>
		<if test="expertID != null and expertID!=''">
		e.expertId = #{expertID}
		</if>
		<if test="fileCode != null and fileCode != ''">
		AND e.fileCode like concat('%',#{fileCode},'%')
		</if>
		<if test="scoredState !=null and scoredState != ''">
		and e.scoredState = #{scoredState}
		</if>
		<if test="entityState !=null and entityState != ''">
			and e.entityState = #{entityState}
		</if>
		<if test="'1' == '1'">
		AND e.fileId = f.id
		</if>
	 </where>
	 order by enabled desc,(case when scoredState=5 then 0 when scoredState=1 then 99 when scoredState=0 then 98 else scoredState end),e.expertFinalScore DESC,f.id
</select>


<select id="selectChiefList" resultMap="committeeList" parameterType="java.util.Map">
	select
		e.id eID,f.id fID,e.scoredState,e.expertName,
		e.expertFinalScore,f.fileType, f.fileCode ,f.expertAName,f.expertAScore,f.expertBName,f.expertBScore,
		f.crossReviewAId as crossAID,f.crossReviewAId as crossBID,e.considerState,f.closed,
		f.expertCommitBId as committBID,f.expertAId as expertAID,f.expertBId as expertBID,
		f.expertCommitAId as committAID,f.expertCommitAName as committAName,f.expertCommitAScore as committAScore,
		f.expertCommitBId as committBID,f.expertCommitBName as committBName,f.expertCommitBScore as committBScore,
		e.preRoundPaperScoreFlag
	from files f,expertHandlFileList e
	<where>
		<if test="expertID != null and expertID!=''">
		e.expertId = #{expertID}
		</if>
		<if test="fileCode != null and fileCode != ''">
		AND e.fileCode like concat('%',#{fileCode},'%')
		</if>
		<if test="scoredState !=null and scoredState != ''">
		and e.scoredState = #{scoredState}
		</if>
		<if test="'1' == '1'">
		AND e.fileId = f.id
		</if>
	 </where>
	 order by enabled desc,(case when scoredState=5 then 0 when scoredState=1 then 99 when scoredState=0 then 98 else scoredState end),e.expertFinalScore DESC,f.id
</select>

<select id="selectFileAlter" resultMap="publicList" parameterType="com.changneng.sa.bean.Files">
	select
	f.id,
	f.fileCode  ,
	f.areaCode,
	f.fileType,
	f.isPublic,
	f.publicTypePenalize,
	f.publicAddressPenalize,
	f.isPublicPenalize,
	f.publicAddress,
	f.publicDescPenalize,
	f.closed,
	t.province,
	t.city,
	t.Country,
	(
		case f.fileType
		WHEN 0 THEN '行政处罚案卷'
		WHEN 1 THEN '按日计罚案卷'
		WHEN 2 THEN '移送行政拘留案卷'
		when 3 THEN '环境污染犯罪移送公安机关案卷'
		WHEN 4 THEN '申请法院强制执行案卷'
		WHEN 5 THEN '发现问题的污染源现场监督检查稽查案卷'
		WHEN 6 THEN '查封扣押案卷'
		WHEN 7 THEN '停产限产案卷'
		ELSE ''
		END
	) fileTypeName
FROM
	files as f
LEFT JOIN T_area as t
ON f.belongAreacode = t.code
<where>
	<if test="reporttype!=null">
		f.reportType = #{reporttype}
	</if>
	<if test="filetype != null and ''!=filetype">
		and f.fileType = #{filetype}
	</if>
	<if test="filecode != null and filecode != ''">
		and f.fileCode like concat('%',#{filecode},'%')
	</if>
	<if test="type != null">
		and f.type = #{type}
	</if>
</where>
order by f.belongAreacode
</select>



<!-- 根据专家ID查询案卷信息 -->

<select id="selectListByIdListPara" parameterType="java.util.List"
	resultMap="FilesSimpleResultMap">
	select
	<include refid="Simple_Column_List" />
	from files
	<where>
	isConsiderExpCommit ='1' and
		<if test="list.size() != 0">
			<foreach collection="list" item="item" index="index" open="(" close=")"
				separator=" or ">
				expertAId =${item} or expertBId = ${item}
			</foreach>
		</if>
	</where>

</select>
<!-- 查询需要分配给专家委员的其他类型(非稽查)案卷 -->
<select id="selectFileListNeedHeYi" resultMap="FilesSimpleResultMap">
	select
	<include refid="Base_Column_List" />
	from files
	where isConsider ='1' and fileType!='5' and expertCommitAId is null and expertCommitBId is null
</select>

<!-- 查询需要分配给专家委员的稽查案卷 -->
<select id="selectFileListNeedHeYiForJiCha" resultMap="FilesSimpleResultMap">
	select
	<include refid="Base_Column_List" />
	from files
	where isConsider ='1' and fileType='5' and expertCommitAId is null and expertCommitBId is null
</select>


<!-- 查询没有被委任为委员的专家打过分的案卷 -->
<select id="selectListByPara" resultMap="FilesSimpleResultMap">
	select
	<include refid="Simple_Column_List" />
	from files
	where isConsiderExpCommit ='1' AND expertAId IS NOT NULL AND expertBId IS NOT NULL
	<if test="para!=null and para!=''">
		and expertAId not in (${para}) and expertBId not in (${para})
	</if>
</select>

<!-- 置入交叉人员AAAA的无需评审交叉分 -->
<update id="setInCheckCrossAScore">
	UPDATE (
			SELECT fileID,crossUserId,ROUND(sum(resultScore),2) as facScore,sum(standScore) as fulScore,ROUND(ROUND(sum(resultScore),2)/sum(standScore),4)*100 as checkScore from crossHandlIndexScore
			where fileID is not null and
						inCheckValue=0 and
						fileID in(SELECT id from files WHERE isInCheck=1 AND recordCrossUser is NULL)
			group by fileID,crossUserId
			) as cf,files as f
	SET inCheckCrossAScore=cf.checkScore
	WHERE f.id = cf.fileID and f.crossReviewAId = cf.crossUserId AND recordCrossUser is NULL and checkScore is not NULL
</update>

<!-- 置入交叉人员BBBBB的无需评审交叉分 -->
<update id="setInCheckCrossBScore">
	UPDATE (
			SELECT fileID,crossUserId,ROUND(sum(resultScore),2) as facScore,sum(standScore) as fulScore,ROUND(ROUND(sum(resultScore),2)/sum(standScore),4)*100 as checkScore from crossHandlIndexScore
			where fileID is not null and
						inCheckValue=0 and
						fileID in(SELECT id from files WHERE isInCheck=1 AND recordCrossUser is NULL)
			group by fileID,crossUserId
			) as cf,files as f
	SET inCheckCrossBScore=cf.checkScore
	WHERE f.id = cf.fileID and f.crossReviewBId = cf.crossUserId AND recordCrossUser is NULL AND checkScore is not NULL
</update>

<!-- 把原始的交叉最终分与专家最终分另存一份 -->
<update id="saveOriginalScore">
	update files SET originalCrossScore = crossConsiderScore,originalExpertScore = expertConsiderScore;
</update>

<!-- 查询需要计算无需评审指标交叉平均分的集合-->
<select id="getInCheckFilesList" resultType="list" resultMap="BaseResultMap">
	select
		id, areaCode, fileType, fileName, fileCode, fileUrl, CrossReviewNameA, CrossReviewNameB,
		CrossReviewAScore,CrossReviewBScore, crossConsiderScore,inCheckCrossAScore,inCheckCrossBScore,inCheckCrossFinalScore,
		crossReviewAId, crossReviewBId
	from files
	where recordCrossUser is null and inCheckCrossAScore is not null and inCheckCrossBScore is not null
</select>

<!-- 替换案卷的，交叉分扣20分 -->
<update id="setCrossFinalScoreForAddfiles">
	UPDATE files
	SET crossConsiderScore=(case when (crossConsiderScore-20)&lt;0 then 0 else ROUND(crossConsiderScore-20,2) END)
	WHERE fileCode like '%(已替换)%' AND crossConsiderScore is not NULL
</update>
<!-- 替换案卷的，专家分扣20分 -->
<update id="setExpertFinalScoreForAddfiles">
	UPDATE files
	SET expertConsiderScore=(case when (expertConsiderScore-20)&lt;0 then 0 else ROUND(expertConsiderScore-20,2) END)
	WHERE fileCode like '%(已替换)%' AND expertConsiderScore is not NULL
</update>

<select id="selectAllFiles" resultMap="BaseResultMap">
	select id, areaCode, fileType, fileName, fileCode, fileUrl,
	crossConsiderScore,expertAId,expertBId,expertConsiderScore,inCheckCrossFinalScore,
	round(expertConsiderScore*0.7+crossConsiderScore*0.3,3) as finalScore
	from files
</select>

<select id="selectBetweenPaperScore" resultMap="BaseResultMap">
	  select
	   f.id,
	   f.expertcommitaid,
	   f.expertcommitbid,
	   f.expertaid,
	   f.expertbid
	   from files f
	  LEFT JOIN expertHandlFileList ehf1 on f.expertAId = ehf1.expertId and f.id = ehf1.fileId and ehf1.handlType = 0
	  LEFT JOIN expertHandlFileList ehf2 on f.expertBId = ehf2.expertId and f.id = ehf2.fileId and ehf2.handlType = 0
	  where  (ehf1.paperScore-ehf2.paperScore) between -15 and 15 and f.isConsider = '1'
</select>

<select id="selectPersonFiles" resultMap="personFileList">
	SELECT
		f.id,
		f.fileCode,
		f.closed,
		case
		when f.fileType = 0 then '行政处罚'
		when f.fileType = 1 then '按日计罚案卷'
		when f.fileType = 2 then '移送行政拘留案卷'
		when f.fileType = 3 then '环境污染犯罪移送公安机关案卷'
		when f.fileType = 4 then '申请法院强制执行案卷'
		when f.fileType = 5 then '发现问题的污染源现场监督检查稽查案卷'
	    when f.fileType = 6 then '查封扣押案卷'
	    when f.fileType = 7 then '停产限产案卷'
		end as fileType,
		p.fileSimpleDesc,
		p.fileDetiailDesc
	FROM
		personalFiles p,
		files f
	WHERE
		p.fileId = f.id
	AND personalId = #{personalId}
</select>

<select id="getFilesNeedConsider" resultMap="BaseResultMap">
	select id from files WHERE isConsiderCross = 1;
</select>

<select id="getFilesByCrossUserID" resultMap="personFileList">
	SELECT
		id,
		closed,
		fileName,
		fileCode,
		fileUrl
	FROM
		files as f
	where
		id in
			(select fileId from crossHandlFileList where crossUserId = #{crossUserId}) order by id
</select>

<select id="selectJiChaFiles" resultMap="FilesSimpleResultMap">
	select
	<include refid="Simple_Column_List" />
	from files
	where fileType='5'
</select>


	<select id="selectAtvFiles" resultMap="FilesSimpleResultMap">
		select
		<include refid="Simple_Column_List" />
		from files
		where isAi = '1'
		<if test="fileType !=null and fileType !=''">
			and fileType = #{fileType}
		</if>
	</select>

	<select id="selectAtvFilesById" resultMap="FilesSimpleResultMap">
		select
		<include refid="Simple_Column_List" />
		from files
		where isAi = '1'
		and id = #{id}
	</select>

	<select id="selectAllNeedCheckFiles" resultMap="FilesSimpleResultMap">
		select
		<include refid="Simple_Column_List" />
		from files
		where isAi is not null and isAiNeedCheck = 0
	</select>
	<select id="selectAllEntityFiles" resultMap="FilesSimpleResultMap">
		select
		<include refid="Simple_Column_List" />
		from files
		where isAi is not null and isAiEntity = 0
	</select>
	<select id="selectAllEntityFilesFouJue" resultMap="FilesSimpleResultMap">
		select
		<include refid="Simple_Column_List" />
		from files_foujue
		where isAi is not null and isAiEntity = 0
	</select>

  <!-- ========================== 案卷评分统计  开始============================================================== -->
  <select id="participateFilesStatistics" resultType="java.util.HashMap">
  	SELECT count(a.id) AS ypaj, b.wpaj FROM (
		SELECT id FROM expertHandlFileList WHERE scoredState = 1 AND handlType = 0 GROUP BY fileId HAVING COUNT(fileId) = 2
	) AS a,
	(
		SELECT COUNT(DISTINCT fileId) AS wpaj FROM expertHandlFileList WHERE scoredState != 1 AND handlType = 0
	) AS b
  </select>

  <select id="evaluatedFilesStatistics" resultType="java.util.HashMap">
  	SELECT a.wxhy, b.xhy FROM (
		SELECT COUNT(1) AS wxhy FROM files WHERE isConsider = 0
	) AS a,
	(
		SELECT COUNT(1) AS xhy FROM files WHERE isConsider = 1
	) AS b
  </select>

  <select id="pushCollegialFilesStatistics" resultType="java.util.HashMap">
  	SELECT COUNT(a.fileId) AS yphyaj, b.wphyaj FROM (
		SELECT DISTINCT fileId FROM expertHandlFileList WHERE handlType = 1 AND scoredState = 1 GROUP BY fileId HAVING COUNT(fileId) = 2
	) AS a,
	(
		SELECT COUNT(DISTINCT fileId) AS wphyaj FROM expertHandlFileList WHERE handlType = 1 AND scoredState != 1
	) AS b
  </select>

  <select id="thisAccountFilesStatistics" parameterType="Integer" resultType="java.util.HashMap">
	SELECT a.ypaj, b.wpaj, c.pszaj FROM (
		SELECT COUNT(1) AS ypaj FROM expertHandlFileList WHERE scoredState = 1  AND expertId = #{expertId}
	)AS a,
	(
		SELECT COUNT(1) AS wpaj FROM expertHandlFileList WHERE scoredState in (0,7) AND expertId = #{expertId}
	)AS b,
	(
		SELECT COUNT(1) AS pszaj FROM expertHandlFileList WHERE scoredState IN (2, 3, 4) AND expertId = #{expertId}
	)AS c
  </select>

  <select id="thisAccountEvaluatedFilesStatistics" parameterType="Integer" resultType="java.util.HashMap">
  	SELECT a.wxhy, b.xhy FROM (
		SELECT COUNT(1) AS wxhy FROM files WHERE isConsider = 0 AND (expertAId = #{expertId} OR expertBId = #{expertId})
	)AS a,
	(
		SELECT COUNT(1) AS xhy FROM files WHERE isConsider = 1 AND (expertAId = #{expertId} OR expertBId = #{expertId})
	)AS b
  </select>
  <!-- ========================== 案卷评分统计  结束============================================================== -->

  <select id="queryAllFiles" resultMap="BaseResultMap">
  	SELECT <include refid="Base_Column_List" /> FROM files
  </select>

  <update id="updateQualityScoreById">
  	update files set qualityScore = #{ajzlScore} where id = #{id}
  </update>

	<update id="updateAiWorkspace">
  	update files set workSpace = #{workSpace},isAi =#{isAi} where id = #{id}
  </update>

	<update id="updateAiCheckStatus">
		update files
		set isAiNeedCheck = #{isAiNeedCheck}
		where id = #{id}
	</update>
	<update id="updateAiEntityStatus">
		update files
		set isAiEntity = #{isAiEntity},aiEntityStatus = #{aiEntityStatus}
		where id = #{id}
	</update>

	<!--批量逻辑删除-->
	<update id="batchDeleteByIdList">
		update files set isDeleted = 1 where id in
	 <foreach collection="list" item="id" open="(" separator="," close=")">
		#{id}
	</foreach>
	</update>

	<select id="getIsPs" resultMap="BaseResultMap">
		SELECT <include refid="Base_Column_List" /> FROM files where  is_ps IS NULL or is_ps !=1
	</select>

	<select id="getFilesByID" resultType="com.changneng.sa.bean.Files">
		SELECT <include refid="Base_Column_List" /> FROM files where 1=1 and  isConsider = 2 and id = #{filesId}
	</select>
    <select id="selectAllFilesByActivityId" resultType="com.changneng.sa.bean.FilesVo">
		select
			f.id,
			f.areaCode ,
			f.fileType ,
			f.fileName ,
			f.fileCode ,
			f.expertAId,
			f.expertCommitAId
		from filesactivityrelation fa
							left join files f on f.id = fa.filesId
		where
			fa.activityId = #{activityId}
	</select>
	<select id="selectAllFilesByAcIdAndType" resultType="com.changneng.sa.bean.FilesVo">
		select
			f.id,
			f.areaCode ,
			f.fileType ,
			f.fileName ,
			f.fileCode
		from filesactivityrelation fa
				 left join files f on f.id = fa.filesId
		where
			fa.activityId = #{activityId}
			and f.fileType=#{fileType}
	</select>

    <select id="selectMidYearFileAreaList" resultType="java.lang.String">
		SELECT DISTINCT SUBSTRING(areaCode, 1, 6) AS cityCode
		FROM files_mid_year
		WHERE fileType = '9';
	</select>

</mapper>
