<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.changneng.sa.dao.CrossHandlFileListMapper">
  <resultMap id="BaseResultMap" type="com.changneng.sa.bean.CrossHandlFileList">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="crossUserId" jdbcType="INTEGER" property="crossuserid" />
    <result column="fileId" jdbcType="INTEGER" property="fileid" />
    <result column="fileCode" jdbcType="VARCHAR" property="filecode" />
    <result column="scoredState" jdbcType="VARCHAR" property="scoredstate" />
    <result column="juanNeiMuLuScore" jdbcType="REAL" property="juanneimuluscore" />
    <result column="liAnShenPiBiaoScore" jdbcType="REAL" property="lianshenpibiaoscore" />
    <result column="xianChangJianChaScore" jdbcType="REAL" property="xianchangjianchascore" />
    <result column="xunWenBiLuScore" jdbcType="REAL" property="xunwenbiluscore" />
    <result column="diaoQuZhenJuScore" jdbcType="REAL" property="diaoquzhenjuscore" />
    <result column="diaoChaBaoGaoScore" jdbcType="REAL" property="diaochabaogaoscore" />
    <result column="gaoZhiShuScore" jdbcType="REAL" property="gaozhishuscore" />
    <result column="tongZhiShuScore" jdbcType="REAL" property="tongzhishuscore" />
    <result column="tinZhenBiLuScore" jdbcType="REAL" property="tinzhenbiluscore" />
    <result column="jueDingShuScore" jdbcType="REAL" property="juedingshuscore" />
    <result column="zlgzwfxwJueDingShuScore" jdbcType="REAL" property="zlgzwfxwjuedingshuscore" />
    <result column="fchLianXuJueDingScore" jdbcType="REAL" property="fchlianxujuedingscore" />
    <result column="zczlgzWeiFaXingWeiScore" jdbcType="REAL" property="zczlgzweifaxingweiscore" />
    <result column="cfajYiSongShuScore" jdbcType="REAL" property="cfajyisongshuscore" />
    <result column="cfajYiSongChaiLiaoScore" jdbcType="REAL" property="cfajyisongchailiaoscore" />
    <result column="yssxfzajShenPiBiaoScore" jdbcType="REAL" property="yssxfzajshenpibiaoscore" />
    <result column="sxhjfzajYiSongShuScore" jdbcType="REAL" property="sxhjfzajyisongshuscore" />
    <result column="sxhjfzajYiSongCaiLiaoScore" jdbcType="REAL" property="sxhjfzajyisongcailiaoscore" />
    <result column="qzzxShenQinShuScore" jdbcType="REAL" property="qzzxshenqinshuscore" />
    <result column="jiChaTongZhiShuScore" jdbcType="REAL" property="jichatongzhishuscore" />
    <result column="jiChaJiLuScore" jdbcType="REAL" property="jichajiluscore" />
    <result column="jiChaDQZLQingDanScore" jdbcType="REAL" property="jichadqzlqingdanscore" />
    <result column="jiChaBGShuScore" jdbcType="REAL" property="jichabgshuscore" />
    <result column="jiChaYiJianShuScore" jdbcType="REAL" property="jichayijianshuscore" />
    <result column="zhengGaiBGScore" jdbcType="REAL" property="zhenggaibgscore" />
    <result column="expertFinalScore" jdbcType="REAL" property="expertfinalscore" />
    
    <result column="anjianneibuspcx" jdbcType="REAL" property="anjianneibuspcx" />
    <result column="duchulvxingywcgs" jdbcType="REAL" property="duchulvxingywcgs" />
    <result column="considerState" jdbcType="INTEGER" property="considerState" />
    
    <result column="inputScore" jdbcType="REAL" property="inputScore" />
    <result column="wxpjScore" jdbcType="REAL" property="wxpjScore" />
    
    <!--  2018-11-28 新增专家是否评价  及  评价内容 字段 -->
    <result column="jcpjYxdxanlituijian"  property="jcpjYxdxanlituijian" />
    <result column="jcpjYxdxanlituijianreviews" property="jcpjYxdxanlituijianreviews" />
  </resultMap>
  
  <resultMap id="CrossHandFileResultMap" type="com.changneng.sa.bean.CrossHandlFileList">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="crossUserId" jdbcType="INTEGER" property="crossuserid" />
    <result column="fileId" jdbcType="INTEGER" property="fileid" />
    <result column="fileCode" jdbcType="VARCHAR" property="filecode" />
    <result column="scoredState" jdbcType="VARCHAR" property="scoredstate" />
    <result column="expertFinalScore" jdbcType="REAL" property="expertfinalscore" />
    <result column="considerState" jdbcType="INTEGER" property="considerState" />
  	<result column="filetype" jdbcType="VARCHAR" property="filetype" />
    <result column="fileurl" jdbcType="VARCHAR" property="fileurl" />
    <result column="filename" jdbcType="VARCHAR" property="filename" />
    <result column="inputScore" jdbcType="REAL" property="inputScore" />
    <result column="wxpjScore" jdbcType="REAL" property="wxpjScore" />
  </resultMap>
  <sql id="Base_Column_List">
    id, crossUserId, fileId, fileCode, scoredState, juanNeiMuLuScore, liAnShenPiBiaoScore, 
    xianChangJianChaScore, xunWenBiLuScore, diaoQuZhenJuScore, diaoChaBaoGaoScore, gaoZhiShuScore, 
    tongZhiShuScore, tinZhenBiLuScore, jueDingShuScore, zlgzwfxwJueDingShuScore, fchLianXuJueDingScore, 
    zczlgzWeiFaXingWeiScore, cfajYiSongShuScore, cfajYiSongChaiLiaoScore, yssxfzajShenPiBiaoScore, 
    sxhjfzajYiSongShuScore, sxhjfzajYiSongCaiLiaoScore, qzzxShenQinShuScore, jiChaTongZhiShuScore, 
    jiChaJiLuScore, jiChaDQZLQingDanScore, jiChaBGShuScore, jiChaYiJianShuScore, zhengGaiBGScore, 
    expertFinalScore, anjianneibuspcx, duchulvxingywcgs,inputScore,wxpjScore,jcpjYxdxanlituijian,jcpjYxdxanlituijianreviews
  </sql>
  <select id="getCrossHandlFileByFileTypeAndId" resultMap="BaseResultMap">
  	select 
  	<include refid="Base_Column_List" />
  	from crossHandlFileList
    where fileId  = #{fileID}  and  crossUserId = #{expertId}
  </select>
  
  
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from crossHandlFileList
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from crossHandlFileList
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.changneng.sa.bean.CrossHandlFileList">
    insert into crossHandlFileList (id, crossUserId, fileId, 
      fileCode, scoredState, juanNeiMuLuScore, 
      liAnShenPiBiaoScore, xianChangJianChaScore, xunWenBiLuScore, 
      diaoQuZhenJuScore, diaoChaBaoGaoScore, gaoZhiShuScore, 
      tongZhiShuScore, tinZhenBiLuScore, jueDingShuScore, 
      zlgzwfxwJueDingShuScore, fchLianXuJueDingScore, 
      zczlgzWeiFaXingWeiScore, cfajYiSongShuScore, 
      cfajYiSongChaiLiaoScore, yssxfzajShenPiBiaoScore, 
      sxhjfzajYiSongShuScore, sxhjfzajYiSongCaiLiaoScore, 
      qzzxShenQinShuScore, jiChaTongZhiShuScore, jiChaJiLuScore, 
      jiChaDQZLQingDanScore, jiChaBGShuScore, jiChaYiJianShuScore, 
      zhengGaiBGScore, expertFinalScore, anjianneibuspcx, 
      duchulvxingywcgs,jcpjYxdxanlituijian,jcpjYxdxanlituijianreviews,inputScore,wxpjScore)
    values (#{id,jdbcType=INTEGER}, #{crossuserid,jdbcType=INTEGER}, #{fileid,jdbcType=INTEGER}, 
      #{filecode,jdbcType=VARCHAR}, #{scoredstate,jdbcType=VARCHAR}, #{juanneimuluscore,jdbcType=REAL}, 
      #{lianshenpibiaoscore,jdbcType=REAL}, #{xianchangjianchascore,jdbcType=REAL}, #{xunwenbiluscore,jdbcType=REAL}, 
      #{diaoquzhenjuscore,jdbcType=REAL}, #{diaochabaogaoscore,jdbcType=REAL}, #{gaozhishuscore,jdbcType=REAL}, 
      #{tongzhishuscore,jdbcType=REAL}, #{tinzhenbiluscore,jdbcType=REAL}, #{juedingshuscore,jdbcType=REAL}, 
      #{zlgzwfxwjuedingshuscore,jdbcType=REAL}, #{fchlianxujuedingscore,jdbcType=REAL}, 
      #{zczlgzweifaxingweiscore,jdbcType=REAL}, #{cfajyisongshuscore,jdbcType=REAL}, 
      #{cfajyisongchailiaoscore,jdbcType=REAL}, #{yssxfzajshenpibiaoscore,jdbcType=REAL}, 
      #{sxhjfzajyisongshuscore,jdbcType=REAL}, #{sxhjfzajyisongcailiaoscore,jdbcType=REAL}, 
      #{qzzxshenqinshuscore,jdbcType=REAL}, #{jichatongzhishuscore,jdbcType=REAL}, #{jichajiluscore,jdbcType=REAL}, 
      #{jichadqzlqingdanscore,jdbcType=REAL}, #{jichabgshuscore,jdbcType=REAL}, #{jichayijianshuscore,jdbcType=REAL}, 
      #{zhenggaibgscore,jdbcType=REAL}, #{expertfinalscore,jdbcType=REAL}, #{anjianneibuspcx,jdbcType=REAL}, 
      #{duchulvxingywcgs,jdbcType=REAL},#{jcpjYxdxanlituijian,jdbcType=LONGVARCHAR},#{jcpjYxdxanlituijianreviews,jdbcType=LONGVARCHAR},#{inputScore,jdbcType=REAL},#{wxpjScore,jdbcType=REAL})
  </insert>
  <insert id="insertSelective" parameterType="com.changneng.sa.bean.CrossHandlFileList">
    insert into crossHandlFileList
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="crossuserid != null">
        crossUserId,
      </if>
      <if test="fileid != null">
        fileId,
      </if>
      <if test="filecode != null">
        fileCode,
      </if>
      <if test="scoredstate != null">
        scoredState,
      </if>
      <if test="juanneimuluscore != null">
        juanNeiMuLuScore,
      </if>
      <if test="lianshenpibiaoscore != null">
        liAnShenPiBiaoScore,
      </if>
      <if test="xianchangjianchascore != null">
        xianChangJianChaScore,
      </if>
      <if test="xunwenbiluscore != null">
        xunWenBiLuScore,
      </if>
      <if test="diaoquzhenjuscore != null">
        diaoQuZhenJuScore,
      </if>
      <if test="diaochabaogaoscore != null">
        diaoChaBaoGaoScore,
      </if>
      <if test="gaozhishuscore != null">
        gaoZhiShuScore,
      </if>
      <if test="tongzhishuscore != null">
        tongZhiShuScore,
      </if>
      <if test="tinzhenbiluscore != null">
        tinZhenBiLuScore,
      </if>
      <if test="juedingshuscore != null">
        jueDingShuScore,
      </if>
      <if test="zlgzwfxwjuedingshuscore != null">
        zlgzwfxwJueDingShuScore,
      </if>
      <if test="fchlianxujuedingscore != null">
        fchLianXuJueDingScore,
      </if>
      <if test="zczlgzweifaxingweiscore != null">
        zczlgzWeiFaXingWeiScore,
      </if>
      <if test="cfajyisongshuscore != null">
        cfajYiSongShuScore,
      </if>
      <if test="cfajyisongchailiaoscore != null">
        cfajYiSongChaiLiaoScore,
      </if>
      <if test="yssxfzajshenpibiaoscore != null">
        yssxfzajShenPiBiaoScore,
      </if>
      <if test="sxhjfzajyisongshuscore != null">
        sxhjfzajYiSongShuScore,
      </if>
      <if test="sxhjfzajyisongcailiaoscore != null">
        sxhjfzajYiSongCaiLiaoScore,
      </if>
      <if test="qzzxshenqinshuscore != null">
        qzzxShenQinShuScore,
      </if>
      <if test="jichatongzhishuscore != null">
        jiChaTongZhiShuScore,
      </if>
      <if test="jichajiluscore != null">
        jiChaJiLuScore,
      </if>
      <if test="jichadqzlqingdanscore != null">
        jiChaDQZLQingDanScore,
      </if>
      <if test="jichabgshuscore != null">
        jiChaBGShuScore,
      </if>
      <if test="jichayijianshuscore != null">
        jiChaYiJianShuScore,
      </if>
      <if test="zhenggaibgscore != null">
        zhengGaiBGScore,
      </if>
      <if test="expertfinalscore != null">
        expertFinalScore,
      </if>
      <if test="anjianneibuspcx != null">
        anjianneibuspcx,
      </if>
      <if test="duchulvxingywcgs != null">
        duchulvxingywcgs,
      </if>
      <if test="inputScore != null">
        inputScore,
      </if>
      <if test="wxpjScore != null">
        wxpjScore,
      </if>
       <if test="jcpjYxdxanlituijian != null">
        jcpjYxdxanlituijian,
      </if>
      <if test="jcpjYxdxanlituijianreviews != null">
        jcpjYxdxanlituijianreviews,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="crossuserid != null">
        #{crossuserid,jdbcType=INTEGER},
      </if>
      <if test="fileid != null">
        #{fileid,jdbcType=INTEGER},
      </if>
      <if test="filecode != null">
        #{filecode,jdbcType=VARCHAR},
      </if>
      <if test="scoredstate != null">
        #{scoredstate,jdbcType=VARCHAR},
      </if>
      <if test="juanneimuluscore != null">
        #{juanneimuluscore,jdbcType=REAL},
      </if>
      <if test="lianshenpibiaoscore != null">
        #{lianshenpibiaoscore,jdbcType=REAL},
      </if>
      <if test="xianchangjianchascore != null">
        #{xianchangjianchascore,jdbcType=REAL},
      </if>
      <if test="xunwenbiluscore != null">
        #{xunwenbiluscore,jdbcType=REAL},
      </if>
      <if test="diaoquzhenjuscore != null">
        #{diaoquzhenjuscore,jdbcType=REAL},
      </if>
      <if test="diaochabaogaoscore != null">
        #{diaochabaogaoscore,jdbcType=REAL},
      </if>
      <if test="gaozhishuscore != null">
        #{gaozhishuscore,jdbcType=REAL},
      </if>
      <if test="tongzhishuscore != null">
        #{tongzhishuscore,jdbcType=REAL},
      </if>
      <if test="tinzhenbiluscore != null">
        #{tinzhenbiluscore,jdbcType=REAL},
      </if>
      <if test="juedingshuscore != null">
        #{juedingshuscore,jdbcType=REAL},
      </if>
      <if test="zlgzwfxwjuedingshuscore != null">
        #{zlgzwfxwjuedingshuscore,jdbcType=REAL},
      </if>
      <if test="fchlianxujuedingscore != null">
        #{fchlianxujuedingscore,jdbcType=REAL},
      </if>
      <if test="zczlgzweifaxingweiscore != null">
        #{zczlgzweifaxingweiscore,jdbcType=REAL},
      </if>
      <if test="cfajyisongshuscore != null">
        #{cfajyisongshuscore,jdbcType=REAL},
      </if>
      <if test="cfajyisongchailiaoscore != null">
        #{cfajyisongchailiaoscore,jdbcType=REAL},
      </if>
      <if test="yssxfzajshenpibiaoscore != null">
        #{yssxfzajshenpibiaoscore,jdbcType=REAL},
      </if>
      <if test="sxhjfzajyisongshuscore != null">
        #{sxhjfzajyisongshuscore,jdbcType=REAL},
      </if>
      <if test="sxhjfzajyisongcailiaoscore != null">
        #{sxhjfzajyisongcailiaoscore,jdbcType=REAL},
      </if>
      <if test="qzzxshenqinshuscore != null">
        #{qzzxshenqinshuscore,jdbcType=REAL},
      </if>
      <if test="jichatongzhishuscore != null">
        #{jichatongzhishuscore,jdbcType=REAL},
      </if>
      <if test="jichajiluscore != null">
        #{jichajiluscore,jdbcType=REAL},
      </if>
      <if test="jichadqzlqingdanscore != null">
        #{jichadqzlqingdanscore,jdbcType=REAL},
      </if>
      <if test="jichabgshuscore != null">
        #{jichabgshuscore,jdbcType=REAL},
      </if>
      <if test="jichayijianshuscore != null">
        #{jichayijianshuscore,jdbcType=REAL},
      </if>
      <if test="zhenggaibgscore != null">
        #{zhenggaibgscore,jdbcType=REAL},
      </if>
      <if test="expertfinalscore != null">
        #{expertfinalscore,jdbcType=REAL},
      </if>
       <if test="anjianneibuspcx != null">
        #{anjianneibuspcx,jdbcType=REAL},
      </if>
      <if test="duchulvxingywcgs != null">
        #{duchulvxingywcgs,jdbcType=REAL},
      </if>
      <if test="inputScore != null">
        #{inputScore,jdbcType=REAL},
      </if>
      <if test="wxpjScore != null">
        #{wxpjScore,jdbcType=REAL},
      </if>
      <if test="jcpjYxdxanlituijian != null" >
        jcpjYxdxanlituijian = #{jcpjYxdxanlituijian,jdbcType=LONGVARCHAR},
      </if>
      <if test="jcpjYxdxanlituijianreviews != null" >
        jcpjYxdxanlituijianreviews = #{jcpjYxdxanlituijianreviews,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.changneng.sa.bean.CrossHandlFileList">
    update crossHandlFileList
    <set>
      <if test="crossuserid != null">
        crossUserId = #{crossuserid,jdbcType=INTEGER},
      </if>
      <if test="fileid != null">
        fileId = #{fileid,jdbcType=INTEGER},
      </if>
      <if test="filecode != null">
        fileCode = #{filecode,jdbcType=VARCHAR},
      </if>
      <if test="scoredstate != null">
        scoredState = #{scoredstate,jdbcType=VARCHAR},
      </if>
      <if test="juanneimuluscore != null">
        juanNeiMuLuScore = #{juanneimuluscore,jdbcType=REAL},
      </if>
      <if test="lianshenpibiaoscore != null">
        liAnShenPiBiaoScore = #{lianshenpibiaoscore,jdbcType=REAL},
      </if>
      <if test="xianchangjianchascore != null">
        xianChangJianChaScore = #{xianchangjianchascore,jdbcType=REAL},
      </if>
      <if test="xunwenbiluscore != null">
        xunWenBiLuScore = #{xunwenbiluscore,jdbcType=REAL},
      </if>
      <if test="diaoquzhenjuscore != null">
        diaoQuZhenJuScore = #{diaoquzhenjuscore,jdbcType=REAL},
      </if>
      <if test="diaochabaogaoscore != null">
        diaoChaBaoGaoScore = #{diaochabaogaoscore,jdbcType=REAL},
      </if>
      <if test="gaozhishuscore != null">
        gaoZhiShuScore = #{gaozhishuscore,jdbcType=REAL},
      </if>
      <if test="tongzhishuscore != null">
        tongZhiShuScore = #{tongzhishuscore,jdbcType=REAL},
      </if>
      <if test="tinzhenbiluscore != null">
        tinZhenBiLuScore = #{tinzhenbiluscore,jdbcType=REAL},
      </if>
      <if test="juedingshuscore != null">
        jueDingShuScore = #{juedingshuscore,jdbcType=REAL},
      </if>
      <if test="zlgzwfxwjuedingshuscore != null">
        zlgzwfxwJueDingShuScore = #{zlgzwfxwjuedingshuscore,jdbcType=REAL},
      </if>
      <if test="fchlianxujuedingscore != null">
        fchLianXuJueDingScore = #{fchlianxujuedingscore,jdbcType=REAL},
      </if>
      <if test="zczlgzweifaxingweiscore != null">
        zczlgzWeiFaXingWeiScore = #{zczlgzweifaxingweiscore,jdbcType=REAL},
      </if>
      <if test="cfajyisongshuscore != null">
        cfajYiSongShuScore = #{cfajyisongshuscore,jdbcType=REAL},
      </if>
      <if test="cfajyisongchailiaoscore != null">
        cfajYiSongChaiLiaoScore = #{cfajyisongchailiaoscore,jdbcType=REAL},
      </if>
      <if test="yssxfzajshenpibiaoscore != null">
        yssxfzajShenPiBiaoScore = #{yssxfzajshenpibiaoscore,jdbcType=REAL},
      </if>
      <if test="sxhjfzajyisongshuscore != null">
        sxhjfzajYiSongShuScore = #{sxhjfzajyisongshuscore,jdbcType=REAL},
      </if>
      <if test="sxhjfzajyisongcailiaoscore != null">
        sxhjfzajYiSongCaiLiaoScore = #{sxhjfzajyisongcailiaoscore,jdbcType=REAL},
      </if>
      <if test="qzzxshenqinshuscore != null">
        qzzxShenQinShuScore = #{qzzxshenqinshuscore,jdbcType=REAL},
      </if>
      <if test="jichatongzhishuscore != null">
        jiChaTongZhiShuScore = #{jichatongzhishuscore,jdbcType=REAL},
      </if>
      <if test="jichajiluscore != null">
        jiChaJiLuScore = #{jichajiluscore,jdbcType=REAL},
      </if>
      <if test="jichadqzlqingdanscore != null">
        jiChaDQZLQingDanScore = #{jichadqzlqingdanscore,jdbcType=REAL},
      </if>
      <if test="jichabgshuscore != null">
        jiChaBGShuScore = #{jichabgshuscore,jdbcType=REAL},
      </if>
      <if test="jichayijianshuscore != null">
        jiChaYiJianShuScore = #{jichayijianshuscore,jdbcType=REAL},
      </if>
      <if test="zhenggaibgscore != null">
        zhengGaiBGScore = #{zhenggaibgscore,jdbcType=REAL},
      </if>
      <if test="expertfinalscore != null">
        expertFinalScore = #{expertfinalscore,jdbcType=REAL},
      </if>
       <if test="anjianneibuspcx != null">
        anjianneibuspcx = #{anjianneibuspcx,jdbcType=REAL},
      </if>
      <if test="duchulvxingywcgs != null">
        duchulvxingywcgs = #{duchulvxingywcgs,jdbcType=REAL},
      </if>
      <if test="considerState != null">
        considerState = #{considerState,jdbcType=INTEGER},
      </if>
      <if test="inputScore != null">
        inputScore = #{inputScore,jdbcType=REAL},
      </if>
      <if test="wxpjScore != null">
        wxpjScore = #{wxpjScore,jdbcType=REAL},
      </if>
      <if test="jcpjYxdxanlituijian != null" >
        jcpjYxdxanlituijian = #{jcpjYxdxanlituijian,jdbcType=LONGVARCHAR},
      </if>
      <if test="jcpjYxdxanlituijianreviews != null" >
        jcpjYxdxanlituijianreviews = #{jcpjYxdxanlituijianreviews,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.changneng.sa.bean.CrossHandlFileList">
    update crossHandlFileList
    set crossUserId = #{crossuserid,jdbcType=INTEGER},
      fileId = #{fileid,jdbcType=INTEGER},
      fileCode = #{filecode,jdbcType=VARCHAR},
      scoredState = #{scoredstate,jdbcType=VARCHAR},
      juanNeiMuLuScore = #{juanneimuluscore,jdbcType=REAL},
      liAnShenPiBiaoScore = #{lianshenpibiaoscore,jdbcType=REAL},
      xianChangJianChaScore = #{xianchangjianchascore,jdbcType=REAL},
      xunWenBiLuScore = #{xunwenbiluscore,jdbcType=REAL},
      diaoQuZhenJuScore = #{diaoquzhenjuscore,jdbcType=REAL},
      diaoChaBaoGaoScore = #{diaochabaogaoscore,jdbcType=REAL},
      gaoZhiShuScore = #{gaozhishuscore,jdbcType=REAL},
      tongZhiShuScore = #{tongzhishuscore,jdbcType=REAL},
      tinZhenBiLuScore = #{tinzhenbiluscore,jdbcType=REAL},
      jueDingShuScore = #{juedingshuscore,jdbcType=REAL},
      zlgzwfxwJueDingShuScore = #{zlgzwfxwjuedingshuscore,jdbcType=REAL},
      fchLianXuJueDingScore = #{fchlianxujuedingscore,jdbcType=REAL},
      zczlgzWeiFaXingWeiScore = #{zczlgzweifaxingweiscore,jdbcType=REAL},
      cfajYiSongShuScore = #{cfajyisongshuscore,jdbcType=REAL},
      cfajYiSongChaiLiaoScore = #{cfajyisongchailiaoscore,jdbcType=REAL},
      yssxfzajShenPiBiaoScore = #{yssxfzajshenpibiaoscore,jdbcType=REAL},
      sxhjfzajYiSongShuScore = #{sxhjfzajyisongshuscore,jdbcType=REAL},
      sxhjfzajYiSongCaiLiaoScore = #{sxhjfzajyisongcailiaoscore,jdbcType=REAL},
      qzzxShenQinShuScore = #{qzzxshenqinshuscore,jdbcType=REAL},
      jiChaTongZhiShuScore = #{jichatongzhishuscore,jdbcType=REAL},
      jiChaJiLuScore = #{jichajiluscore,jdbcType=REAL},
      jiChaDQZLQingDanScore = #{jichadqzlqingdanscore,jdbcType=REAL},
      jiChaBGShuScore = #{jichabgshuscore,jdbcType=REAL},
      jiChaYiJianShuScore = #{jichayijianshuscore,jdbcType=REAL},
      zhengGaiBGScore = #{zhenggaibgscore,jdbcType=REAL},
      expertFinalScore = #{expertfinalscore,jdbcType=REAL},
       anjianneibuspcx = #{anjianneibuspcx,jdbcType=REAL},
      duchulvxingywcgs = #{duchulvxingywcgs,jdbcType=REAL},
      jcpjYxdxanlituijian = #{jcpjYxdxanlituijian,jdbcType=LONGVARCHAR},
      jcpjYxdxanlituijianreviews = #{jcpjYxdxanlituijianreviews,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
   <!-- ========================================交叉评分 - 交叉评分结果 - 交叉案卷评分结果集-jc=========================================== -->
  <select id="getCorssHandList"  resultType="com.changneng.sa.bean.CrossHandUnit_JXPF_jxpfList">
		SELECT 
		c.fileCode,
		t.name fileType,
		t.code,
		u.name,
		c.expertFinalScore,
		
		c.inputScore,
        c.wxpjScore,
		
		c.scoredState,
		c.id,
		c.considerState
		FROM TC_dictionary t , 
		crossHandlFileList c, 
		crossUser u,
		files f
		WHERE c.crossUserId = u.id 
		AND f.id = c.fileId 
		AND f.fileType = t.code
		AND c.crossUserId = #{jxpfList.crossUserId}
		AND t.temp= 'fileType'
	 	<if test="jxpfList.fileCode != null and jxpfList.fileCode != ''" >
        	 and  c.fileCode like CONCAT('%','${jxpfList.fileCode}','%')   
	    </if>
	    <if test="jxpfList.scoredState != null and jxpfList.scoredState != ''" >
        	and c.scoredState = #{jxpfList.scoredState}
	    </if>
	    <if test="jxpfList.fileType != null and jxpfList.fileType != ''" >
        	and  t.code= #{jxpfList.fileType}
	    </if>
	    ORDER BY c.expertFinalScore DESC , f.fileType DESC,c.id desc
  </select>
  
   <!-- lihongli 为维护案卷信息表 -->
  <select id="getCrossFileListByFileIdAndExpertId" resultMap="BaseResultMap" >
   	 select id,fileId,crossUserId,expertFinalScore,inputScore,wxpjScore  from  
   	 crossHandlFileList c where c.fileId=#{fileId}
   	   and considerState !=1
   	   and c.crossUserId in(#{crossUserAId},#{crossUserBId})  
  </select>


  <!-- 批量新增案卷分配的信息 -->
  
  <insert id="insertBatchList" parameterType="java.util.List">  
    insert into crossHandlFileList (crossUserId, fileId, fileCode)   
    values   
    <foreach collection="list" item="item" index="index"  separator="," >  
         (
         #{item.crossuserid,jdbcType=INTEGER}, 
         #{item.fileid,jdbcType=VARCHAR}, 
         #{item.filecode,jdbcType=VARCHAR}
         )
         
    </foreach>  
</insert> 
	<!-- 获得所有交叉未评的fileId的集合  getExpertWeiPingAllNum -->
	   <select id="getCrossWeiPingAllNum" resultType="int" >
	    select count(1) from (select  DISTINCT fileId    from crossHandlFileList c where  c.scoredState='1') s
	  </select>
	  
	 <!--  获得当前登录的交叉人的案卷总数getCrossFileNum-->
	   <select id="getCrossFileNum" resultType="int" >
	  	 select count(1)  from crossHandlFileList c where  c.crossUserId =#{crossId} 
	  </select>
	<!--  获得当前登录交叉人员未评案卷总数getCrossFileWeiPingNum -->
	<select id="getCrossFileWeiPingNum" resultType="int" >
	  	 select count(1)  from crossHandlFileList c where  c.crossUserId =#{crossId} and c.scoredState='0'
	</select>
	
	<!-- selectCrossHandFileListById -->
	<select id="selectCrossHandFileListById"  resultMap="CrossHandFileResultMap">
    select 
    cf.id, cf.crossUserId, cf.fileId, cf.fileCode, cf.scoredState, 
    cf.expertFinalScore,f.fileurl,f.filename,f.filetype,cf.jcpjYxdxanlituijianreviews,cf.jcpjYxdxanlituijian,cf.inputScore,cf.wxpjScore
    from  crossHandlFileList cf left join files f on cf.fileId = f.id
    where cf.id = #{crossId}
  </select>
</mapper>