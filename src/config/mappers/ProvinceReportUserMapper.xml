<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.changneng.sa.dao.ProvinceReportUserMapper">
  <resultMap id="BaseResultMap" type="com.changneng.sa.bean.ProvinceReportUser">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="areaCode" jdbcType="VARCHAR" property="areacode" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="loginName" jdbcType="VARCHAR" property="loginname" />
    <result column="loginPass" jdbcType="VARCHAR" property="loginpass" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="reportState" jdbcType="INTEGER" property="reportstate" />
    <result column="electCityState" jdbcType="INTEGER" property="electcityState" />
    <result column="electCountyState" jdbcType="INTEGER" property="electcountyState" />
    <result column="areaType" jdbcType="VARCHAR" property="areatype" />
    <result column="createTime" jdbcType="TIMESTAMP" property="createtime" />
    <result column="updateTime" jdbcType="TIMESTAMP" property="updatetime" />
    <result column="isDevInternalEva" jdbcType="INTEGER" property="isDevInternalEva" />
    <result column="start_ActivityTime" jdbcType="DATE" property="startActivityTime" />
    <result column="end_ActivityTime" jdbcType="DATE" property="endActivityTime" />
    <result column="electPerState" jdbcType="INTEGER" property="electPerState" />
    <result column="initPolluterNum" jdbcType="INTEGER" property="initpolluternum" />
      <result column="fileName" jdbcType="VARCHAR" property="fileName" />
      <result column="fileUrl" jdbcType="VARCHAR" property="fileUrl" />
  </resultMap>
 <resultMap id="BaseResultMapNew" type="com.changneng.sa.bean.province">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="areaCode" jdbcType="VARCHAR" property="areacode" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="loginName" jdbcType="VARCHAR" property="loginname" />
    <result column="loginPass" jdbcType="VARCHAR" property="loginpass" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="reportState" jdbcType="TINYINT" property="reportstate" />
    <result column="electCityState" jdbcType="TINYINT" property="electcitystate" />
    <result column="electCountyState" jdbcType="TINYINT" property="electCountyState" />
    <result column="areaType" jdbcType="VARCHAR" property="areatype" />
    <result column="city" jdbcType="VARCHAR"  property="city"/>
  </resultMap>
  <resultMap id="BaseResultMapCount" type="com.changneng.sa.bean.ProvinceCountBean">
    <result column="areaCode_pro" jdbcType="VARCHAR"  property="areaCodePro"/>
    <result column="start_ActivityTime" jdbcType="DATE" property="startActivityTime" />
    <result column="end_ActivityTime" jdbcType="DATE" property="endActivityTime" />
  </resultMap>
  
  <sql id="Base_Column_List">
    id, areaCode, name, loginName, loginPass, remark, reportState, electCityState, electCountyState, areaType
    , isDevInternalEva, start_ActivityTime, end_ActivityTime, createTime, updateTime, electPerState
    , initPolluterNum , fileName , fileUrl
  </sql>
  <sql id="Base_Column_List_New">
    id, areaCode, name, loginName, loginPass, remark, reportState, electCityState, electCountyState, 
    areaType 
  </sql>
  
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from provinceReportUser
    where id = #{id,jdbcType=INTEGER}
  </select>
  
  <!--根据登录id、密码获取用户信息 start -->
   <select id="selectByLoginidAndPassword" parameterType="com.changneng.sa.bean.LoginUser" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from provinceReportUser
    where loginName = #{loginid,jdbcType=VARCHAR} and loginPass= #{password,jdbcType=VARCHAR}
  </select>
  <!-- 根据登录id、密码获取用户信息 end -->
  
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from provinceReportUser
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.changneng.sa.bean.ProvinceReportUser">
    insert into provinceReportUser (id, areaCode, name, 
      loginName, loginPass, remark
      )
    values (#{id,jdbcType=INTEGER}, #{areacode,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, 
      #{loginname,jdbcType=VARCHAR}, #{loginpass,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.changneng.sa.bean.ProvinceReportUser">
    insert into provinceReportUser
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="areacode != null">
        areaCode,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="loginname != null">
        loginName,
      </if>
      <if test="loginpass != null">
        loginPass,
      </if>
      <if test="remark != null">
        remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="areacode != null">
        #{areacode,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="loginname != null">
        #{loginname,jdbcType=VARCHAR},
      </if>
      <if test="loginpass != null">
        #{loginpass,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.changneng.sa.bean.ProvinceReportUser">
    update provinceReportUser
    <set>
      <if test="areacode != null">
        areaCode = #{areacode,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="loginname != null">
        loginName = #{loginname,jdbcType=VARCHAR},
      </if>
      <if test="loginpass != null">
        loginPass = #{loginpass,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDevInternalEva != null">
        isDevInternalEva = #{isDevInternalEva,jdbcType=INTEGER},
      </if>
      <if test="startActivityTime != null">
        start_ActivityTime = #{startActivityTime,jdbcType=DATE},
      </if>
      <if test="endActivityTime != null">
        end_ActivityTime = #{endActivityTime,jdbcType=DATE},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <!-- 重置密码start -->
   <update id="updatePasswordById" parameterType="com.changneng.sa.bean.LoginUser">
	    update provinceReportUser
	    <set>
	      <if test="password != null">
	        loginPass = #{password,jdbcType=VARCHAR},
	      </if>
	    </set>
	    where id = #{id,jdbcType=INTEGER}
  </update>
  <!-- 重置密码end -->
  <update id="updateByPrimaryKey" parameterType="com.changneng.sa.bean.ProvinceReportUser">
    update provinceReportUser
    set areaCode = #{areacode,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      loginName = #{loginname,jdbcType=VARCHAR},
      loginPass = #{loginpass,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  
  <!-- 获取list start -->
      <select id="selectList"  resultMap="BaseResultMap">
	    select 
	    <include refid="Base_Column_List" />
	    from provinceReportUser
	    order by areaCode asc
  </select>
    <!-- 获取list end -->
  <!-- 获取省下面的市级单位 -->
  <select id="selectCityByProvince" parameterType="java.util.Map" resultMap="BaseResultMapNew">
 SELECT p.id id, p.areaCode areacode,p.name name, p.loginName loginname,p.loginPass loginpass,p.remark remark,p.reportState reportstate,p.electCityState electcitystate,p.electCountyState electCountyState,p.areaType areatype,c.city city
FROM provinceReportUser p,T_area  c
WHERE
   p.areaCode = c.code
  and
   p.areaCode like concat(#{areaCode},'%') and p.areaType = #{areaType}
  	
  </select>
  <!-- 删除市 -->
  <delete id="deleteCity" parameterType="java.lang.String" >
  delete from provinceReportUser where areaCode = #{areaCode}
  </delete>
  <update id="updateCitySubmit" parameterType="java.util.Map">
  	update provinceReportUser
  		set electCityState = 1,
  		loginPass = '6dc50b3801505fc2c0b0ac509ef1eacc'
  	where areaCode like concat(#{areaCode},'%') and areaType = #{areaType}
  </update>
  <update id="updateCountySubmit" parameterType="java.util.Map">
  	update provinceReportUser
  		set electCountyState = 1,
  		loginPass = '6dc50b3801505fc2c0b0ac509ef1eacc'
  	where areaCode like concat(#{areaCode},'%') and areaType = #{areaType}
  </update>
 
  <select id="selectAllByProvince" parameterType="java.lang.String" resultMap="BaseResultMapNew">
  	select 
  	<include refid="Base_Column_List_New"></include>
  	from
  	provinceReportUser
  	where areaCode like concat(#{areaCode},'%') and (areaType = '3' or areaType ='2') and (electCountyState = 1 or electCityState = 1)
  	order by areaType
  </select>
  <!-- 重置密码，根据areaCode -->
  <update id="updatePassByAreaCode" parameterType="java.util.Map">
  	update provinceReportUser
  	set loginPass = #{passWord}
  	where
  	 areaCode = #{areaCode} 	
  </update>
  <!--根据areacode查询省级报送状态 -->
   <select id="selectReportByAreaCode" parameterType="java.lang.String" resultType="java.lang.String">
    select 
    reportState
    from provinceReportUser
    where areaCode = #{areaCode}
  </select>
   <select id="countByAreaCode" parameterType="java.util.Map" resultType="java.lang.Integer" >
    select count(*) from provinceReportUser
    where areaCode like concat(#{areaCode},'%') and areaType = #{areaType}
   
  </select>
  
  <update id="updateReportState" parameterType="java.lang.String">
  	update provinceReportUser
  		set reportState = 1
  	where
  	 	areaCode like concat(#{areaCode},'%')
  </update>
  <resultMap type="com.changneng.sa.bean.UnitBean" id="unit">
  <result column="areaCode" jdbcType="VARCHAR" property="areaCode" />
  <result column="total" jdbcType="VARCHAR" property="total" />
  <result column="provinceName" jdbcType="VARCHAR" property="provinceName" />
  <result column="cityCount" jdbcType="VARCHAR" property="cityCount" />
  <result column="countryCount" jdbcType="VARCHAR" property="countryCount" />
  <result column="areaType" jdbcType="VARCHAR" property="areaType" />
  
  </resultMap>
  <select id="selectCountUnit" resultMap="unit" >
   SELECT CONCAT(SUBSTRING(areaCode,1,2),'000000') AS areaCode, COUNT(*) as 'total','2' as 'areaType' FROM provinceReportUser
  WHERE areaType=2 AND  electCityState = '1'
  GROUP BY  SUBSTRING(areaCode,1,2) 
   union all
  SELECT CONCAT(SUBSTRING(areaCode,1,2),'000000'), COUNT(*) AS 'total','3' as 'areaType' FROM provinceReportUser
  WHERE areaType=3 AND electCountyState = '1'
   GROUP BY  SUBSTRING(areaCode,1,2) 
    order by areaCode
  </select>
  <select id="selectProvince" resultMap="unit">
   SELECT  code AS areaCode,name as provinceName  FROM T_area WHERE areaLevel = '1'
  </select>
   <!-- 根据areaCode_pro查询省级下市级用户列表(2018)-->
    <select id="selectProvinceUser" parameterType="java.lang.String" resultMap="BaseResultMapNew">
	  	select 
	  	<include refid="Base_Column_List_New"></include>
	  	from
	  	provinceReportUser
	  	where areaCode like concat(#{areaCode},'%') and areaType ='2'
	  	order by loginname
   </select>
  
     <!-- 根据areaCode_pro查询省级下市级用户列表(2018)-->
    <select id="selectCountyUser" parameterType="java.lang.String" resultMap="BaseResultMapNew">
	  	select 
	  	<include refid="Base_Column_List_New"></include>
	  	from
	  	provinceReportUser
	  	where areaCode like concat(#{areaCode},'%') and areaType ='3'
	  	order by loginname
   </select>
   
        <!-- 根据areaCode_pro查询省级下市级用户列表(2018)-->
    <select id="selectcountryUser"  resultMap="BaseResultMapNew">
	  	select 
	  	<include refid="Base_Column_List_New"></include>
	  	from
	  	provinceReportUser
	  	where  areaType ='1'
	  	order by loginname
   </select>
   
  	<!--根据登录id获取用户信息用于判断推荐提交 -->
   <select id="selectUserByAeracode" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from provinceReportUser
    where  areaCode= #{areacode,jdbcType=VARCHAR}
  </select>
  	<!-- 用于市候选推荐提交 -->
  <update id="updateUserByCode" parameterType="java.lang.String">
  	update provinceReportUser
  		set electCityState = 1
  	where
  	 	 areaCode like '${areacode}%'
  </update>
   <!-- 用于县候选推荐提交 -->
  <update id="updateCountyByCode" parameterType="java.lang.String">
  	update provinceReportUser
  		set electCountyState = 1
  	where
  	 	 areaCode like '${areacode}%'
  </update>
  <!-- 用于个人候选推荐提交 -->
  <update id="updateElectPerStateByCode" parameterType="java.lang.String">
  	update provinceReportUser
  		set electPerState = 1
  	where
  	 	 areaCode like '${areacode}%'
  </update>
   <!-- 报送国家-->
  <update id="updateProvinceReportUserList" parameterType="java.lang.String" >
      update provinceReportUser
        set reportState = 1
      where
         areaCode like concat(#{areaCode},'%')
  </update>
  <!-- 用于是否自评市县状态修改 -->
  <update id="updateIsDevInternalEva" parameterType="com.changneng.sa.bean.ProvinceReportUser">
  	update provinceReportUser
  		set isDevInternalEva = #{isDevInternalEva,jdbcType=INTEGER}
  	where
  	 	 areaCode like '${areacode}%'
  </update>
    <update id="saveProvinceFile">
        update provinceReportUser
        set fileUrl = #{provinceFileUrl,jdbcType=VARCHAR},
            fileName = #{provinceFileName,jdbcType=VARCHAR}
        where
            areaCode = #{areaCode} and areaType ='1'
    </update>

    <!-- 根据areaCode_pro查询省级下市级用户列表(2018)-->
    <select id="getProvinceCountBean"  resultMap="BaseResultMapCount">
	  	SELECT
			unit.areaCode_pro,
			us.start_ActivityTime,
			us.end_ActivityTime
		FROM
			(
				SELECT
					areaCode_pro,
					areaName_pro,
					areaType_pro,
					provinceCaseNum,
					provinceFine,
					provinceMajorCaseNum
				FROM
					Province_Selection_Unit
				WHERE
					areaType_pro = '1'
			) AS unit
		LEFT JOIN provinceReportUser AS us ON unit.areaCode_pro = us.areaCode
		WHERE
			us.isDevInternalEva = '1'
		AND (
			provinceCaseNum IS NULL
			OR provinceFine IS NULL
			OR provinceMajorCaseNum IS NULL
		);
   </select>
   
   <select id="getFileCount"  resultType="java.lang.Integer">
	  	SELECT
			count(*)
		FROM
			penalizeNumFiles
		WHERE
			isEnbaled = '0'
		AND areaCode LIKE '${areaCode_pro}%'
		AND datediff(jueDingRiQi, #{start_ActivityTime}) &gt;= 0
		AND datediff(jueDingRiQi, #{end_ActivityTime}) &lt;= 0
		AND fileType IN (${ids})
   </select>
   
   <select id="getMoneyCount"  resultType="java.lang.Float">
	  	SELECT
			SUM(faKuanShuE)
		FROM
			penalizeNumFiles
		WHERE
			isEnbaled = '0'
		AND areaCode LIKE '${areaCode_pro}%'
		AND datediff(jueDingRiQi, #{start_ActivityTime}) &gt;= 0
		AND datediff(jueDingRiQi, #{end_ActivityTime}) &lt;= 0
		AND fileType IN (${ids})
   </select>
   
   <select id="selectProUserByPhone" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from provinceReportUser
    where remark = #{phone}
  </select>
</mapper>