<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.changneng.sa.dao.PhoneRUserMapper">
  <resultMap id="BaseResultMap" type="com.changneng.sa.bean.PhoneRUser">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="userID" jdbcType="INTEGER" property="userid" />
    <result column="userType" jdbcType="CHAR" property="usertype" />
    <result column="tableName" jdbcType="VARCHAR" property="tablename" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, phone, userID, userType, tableName, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from phone_r_user
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from phone_r_user
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.changneng.sa.bean.PhoneRUser" useGeneratedKeys="true">
    insert into phone_r_user (phone, userID, userType, 
      tableName, create_time, update_time
      )
    values (#{phone,jdbcType=VARCHAR}, #{userid,jdbcType=INTEGER}, #{usertype,jdbcType=CHAR}, 
      #{tablename,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.changneng.sa.bean.PhoneRUser" useGeneratedKeys="true">
    insert into phone_r_user
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="phone != null">
        phone,
      </if>
      <if test="userid != null">
        userID,
      </if>
      <if test="usertype != null">
        userType,
      </if>
      <if test="tablename != null">
        tableName,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="userid != null">
        #{userid,jdbcType=INTEGER},
      </if>
      <if test="usertype != null">
        #{usertype,jdbcType=CHAR},
      </if>
      <if test="tablename != null">
        #{tablename,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.changneng.sa.bean.PhoneRUser">
    update phone_r_user
    <set>
      <if test="phone != null">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="userid != null">
        userID = #{userid,jdbcType=INTEGER},
      </if>
      <if test="usertype != null">
        userType = #{usertype,jdbcType=CHAR},
      </if>
      <if test="tablename != null">
        tableName = #{tablename,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.changneng.sa.bean.PhoneRUser">
    update phone_r_user
    set phone = #{phone,jdbcType=VARCHAR},
      userID = #{userid,jdbcType=INTEGER},
      userType = #{usertype,jdbcType=CHAR},
      tableName = #{tablename,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectByPhone" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from phone_r_user
    where phone = #{phone,jdbcType=VARCHAR}
  </select>
</mapper>