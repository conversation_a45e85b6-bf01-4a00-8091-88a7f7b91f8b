<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.changneng.sa.dao.AjpfMapper">
	<!-- 集体案卷评分 -->
	<resultMap id="ajpfMap" type="com.changneng.sa.bean.AjpfBean">
		<id column="id" jdbcType="INTEGER" property="id" />
		<result property="areaCode" column="areaCode" />
		<result property="areaProvince" column="areaProvince" />
		<result property="areaCity" column="areaCity" />
		<result property="areaCounty" column="areaCounty" />
		<result property="areaName" column="areaName" />
		<result property="areaType" column="areaType" />
		<result property="province" column="province" />
		<result property="city" column="city" />
		<result property="country" column="Country" />
		<result property="fileCode" column="fileCode" />
		<result property="expertAName" column="expertAName" />
		<result property="expertAScore" column="expertAScore" />
		<result property="expertBName" column="expertBName" />
		<result property="expertBScore" column="expertBScore" />
		<result property="caseCheckState" column="caseCheckState" />
		<result property="expertConsiderScore" column="expertConsiderScore" />
		<result property="expertconsiderscore2" column="expertconsiderscore2" />
		<result property="crossReviewNameA" column="CrossReviewNameA" />
		<result property="crossReviewNameB" column="CrossReviewNameB" />
		<result property="crossReviewAScore" column="CrossReviewAScore" />
		<result property="crossReviewBScore" column="CrossReviewBScore" />
		<result property="crossConsiderScore" column="crossConsiderScore" />
		<!-- 2018 1210 -->
		<result property="reportType" column="reportType" />

		<!-- 2017 -->
		<result property="inCheckCrossAScore" column="inCheckCrossAScore" />
		<result property="inCheckCrossBScore" column="inCheckCrossBScore" />
		<result property="inCheckCrossFinalScore" column="inCheckCrossFinalScore" />
		<result property="originalCrossScore" column="originalCrossScore" />
		<result property="originalExpertScore" column="originalExpertScore" />

		<result property="expertCommitAName" column="expertCommitAName" />
		<result property="expertCommitAScore" column="expertCommitAScore" />
		<result property="expertCommitBName" column="expertCommitBName" />
		<result property="expertCommitBScore" column="expertCommitBScore" />
		<result property="expertCommitAId" column="expertCommitAId" />
		<result property="expertCommitBId" column="expertCommitBId" />

		<result property="isConsider" column="isConsider" />
		<result property="finalScore" column="finalScore" />
		<result column="expertAId" jdbcType="INTEGER" property="expertaid" />
		<result column="expertBId" jdbcType="INTEGER" property="expertbid" />
		<result column="crossReviewAId" jdbcType="INTEGER" property="crossreviewaid" />
		<result column="crossReviewBId" jdbcType="INTEGER" property="crossreviewbid" />
		<result column="fileType" jdbcType="VARCHAR" property="filetype" />
		<result column="isConsiderCross" jdbcType="VARCHAR" property="isConsiderCross" />
		<result property="fileTypeName" column="fileTypeName" />

		<result property="fileName" column="fileName" />
		<result property="fileUrl" column="fileUrl" />
		<!--实体否决项-->
		<result property="scoringIndexNames" column="scoringIndexNames" />
		<!--专家否决次数-->
		<result property="expertVetoNumber" column="expertVetoNumber" />

	</resultMap>

	<!-- 个人案卷评分 -->
	<resultMap id="ajpfPersonalMap" type="com.changneng.sa.bean.AjpfElectionPersonal">
		<id column="id" jdbcType="INTEGER" property="id" />
		<result property="areaCode" column="areaCode" />
		<result property="areaType" column="areaType" />
		<result property="province" column="province" />
		<result property="city" column="city" />
		<result property="country" column="country" />
		<result property="personalMaterialName" column="personalMaterialName" />
		<result property="perCrossReviewNameA" column="perCrossReviewNameA" />
		<result property="perCrossReviewNameB" column="perCrossReviewNameB" />
		<result property="perCrossReviewAScore" column="perCrossReviewAScore" />
		<result property="perCrossReviewBScore" column="perCrossReviewBScore" />
		<result property="perConsiderScore" column="perConsiderScore" />
		<result property="isConsider" column="isConsider" />
		<result property="perFinalScore" column="perFinalScore" />
		<result property="totalScore" column="totalScore" />
		<result column="crossReviewAId" property="crossreviewaid" />
		<result column="crossReviewBId" property="crossreviewbid" />

	</resultMap>
	<!-- 集体案件评分维护类 -->
	<resultMap id="filesBean" type="com.changneng.sa.bean.AjpfFilesBean">
		<id column="id" jdbcType="INTEGER" property="id" />

		<result property="isConsider" column="isConsider" />
		<result property="expertConsiderScore" column="expertConsiderScore" />
		<result property="expertAId" column="expertAId" />
		<result property="expertBId" column="expertBId" />
		<result property="expertAScore" column="expertAScore" />
		<result property="expertBScore" column="expertBScore" />

		<result property="crossConsiderScore" column="crossConsiderScore" />
		<result property="crossReviewAId" column="crossReviewAId" />
		<result property="crossReviewBId" column="crossReviewBId" />
		<result property="crossReviewAScore" column="CrossReviewAScore" />
		<result property="crossReviewBScore" column="CrossReviewBScore" />
		<result property="isConsiderCross" column="isConsiderCross" />
		<result column="fileType" jdbcType="VARCHAR" property="filetype" />
	</resultMap>

	<!-- changneng 帐号导出给报社信息表 -->
	<resultMap id="spaperBean" type="com.changneng.sa.bean.NewspaperBean">
		<result property="name" column="name" />
		<result property="cardID" column="cardID" />
		<result property="areaCode" column="areaCode" />
		<result property="province" column="province" />
		<result property="city" column="city" />
		<result property="country" column="country" />
		<result property="fileName" column="fileName" />
		<result property="types" column="types" />
		<result property="fileCode" column="fileCode" />
		<result property="fileSimpleDesc" column="fileSimpleDesc" />
		<result property="fileDetiailDesc" column="fileDetiailDesc" />
	</resultMap>

	<resultMap id="homeBean" type="com.changneng.sa.bean.NewHomeBean">
		<result property="filesCountNum" column="filesCountNum" />
		<result property="repeatFiles" column="repeatFiles" />
		<result property="filesTypeJt" column="filesTypeJt" />
		<result property="provinceCountNumJt" column="provinceCountNumJt" />
		<result property="cityCountNumJt" column="cityCountNumJt" />
		<result property="countryCountNumJt" column="countryCountNumJt" />
		<result property="province0NumJt" column="province0NumJt" />
		<result property="province1NumJt" column="province1NumJt" />
		<result property="province2NumJt" column="province2NumJt" />
		<result property="province3NumJt" column="province3NumJt" />
		<result property="province4NumJt" column="province4NumJt" />
		<result property="province5NumJt" column="province5NumJt" />
		<result property="city0NumJt" column="city0NumJt" />
		<result property="city1NumJt" column="city1NumJt" />
		<result property="city2NumJt" column="city2NumJt" />
		<result property="city3NumJt" column="city3NumJt" />
		<result property="city4NumJt" column="city4NumJt" />
		<result property="city5NumJt" column="city5NumJt" />
		<result property="country0NumJt" column="country0NumJt" />
		<result property="country1NumJt" column="country1NumJt" />
		<result property="country2NumJt" column="country2NumJt" />
		<result property="country3NumJt" column="country3NumJt" />
		<result property="country4NumJt" column="country4NumJt" />
		<result property="country5NumJt" column="country5NumJt" />
		<result property="filesTypeJtRandom" column="filesTypeJtRandom" />
		<result property="provinceCountNumJtRandom" column="provinceCountNumJtRandom" />
		<result property="cityCountNumJtRandom" column="cityCountNumJtRandom" />
		<result property="countryCountNumJtRandom" column="countryCountNumJtRandom" />
		<result property="province0NumJtRandom" column="province0NumJtRandom" />
		<result property="province1NumJtRandom" column="province1NumJtRandom" />
		<result property="province2NumJtRandom" column="province2NumJtRandom" />
		<result property="province3NumJtRandom" column="province3NumJtRandom" />
		<result property="province4NumJtRandom" column="province4NumJtRandom" />
		<result property="province5NumJtRandom" column="province5NumJtRandom" />
		<result property="city0NumJtRandom" column="city0NumJtRandom" />
		<result property="city1NumJtRandom" column="city1NumJtRandom" />
		<result property="city2NumJtRandom" column="city2NumJtRandom" />
		<result property="city3NumJtRandom" column="city3NumJtRandom" />
		<result property="city4NumJtRandom" column="city4NumJtRandom" />
		<result property="city5NumJtRandom" column="city5NumJtRandom" />
		<result property="country0NumJtRandom" column="country0NumJtRandom" />
		<result property="country1NumJtRandom" column="country1NumJtRandom" />
		<result property="country2NumJtRandom" column="country2NumJtRandom" />
		<result property="country3NumJtRandom" column="country3NumJtRandom" />
		<result property="country4NumJtRandom" column="country4NumJtRandom" />
		<result property="country5NumJtRandom" column="country5NumJtRandom" />
		<result property="filesTypeGr" column="filesTypeGr" />
		<result property="provinceCountNumGr" column="provinceCountNumGr" />
		<result property="cityCountNumGr" column="cityCountNumGr" />
		<result property="countryCountNumGr" column="countryCountNumGr" />
		<result property="province0NumGr" column="province0NumGr" />
		<result property="province1NumGr" column="province1NumGr" />
		<result property="province2NumGr" column="province2NumGr" />
		<result property="province3NumGr" column="province3NumGr" />
		<result property="province4NumGr" column="province4NumGr" />
		<result property="province5NumGr" column="province5NumGr" />
		<result property="city0NumGr" column="city0NumGr" />
		<result property="city1NumGr" column="city1NumGr" />
		<result property="city2NumGr" column="city2NumGr" />
		<result property="city3NumGr" column="city3NumGr" />
		<result property="city4NumGr" column="city4NumGr" />
		<result property="city5NumGr" column="city5NumGr" />
		<result property="country0NumGr" column="country0NumGr" />
		<result property="country1NumGr" column="country1NumGr" />
		<result property="country2NumGr" column="country2NumGr" />
		<result property="country3NumGr" column="country3NumGr" />
		<result property="country4NumGr" column="country4NumGr" />
		<result property="country5NumGr" column="country5NumGr" />
	</resultMap>



	  <resultMap id="tempFilesBean" type="com.changneng.sa.bean.tempFiles">
	    <id column="id" jdbcType="INTEGER" property="id" />
	    <result column="filesid" jdbcType="INTEGER" property="filesid" />
	    <result column="syncNumber" jdbcType="VARCHAR" property="syncnumber" />
	    <result column="filesCode" jdbcType="VARCHAR" property="filescode" />
	    <result column="filesType" jdbcType="VARCHAR" property="filestype" />
	  </resultMap>

	  <resultMap id="filesBeanByFilesCode" type="com.changneng.sa.bean.Files">
	    <id column="id" jdbcType="INTEGER" property="id" />
	    <result column="areaCode" jdbcType="VARCHAR" property="areacode" />
	    <result column="fileType" jdbcType="VARCHAR" property="filetype" />
	    <result column="fileName" jdbcType="VARCHAR" property="filename" />
	    <result column="fileCode" jdbcType="VARCHAR" property="filecode" />
	    <result column="fileUrl" jdbcType="VARCHAR" property="fileurl" />
	    <result column="belongAreacode" jdbcType="INTEGER" property="belongareacode" />
	    <result column="syncNumber" jdbcType="VARCHAR" property="syncnumber" />
	  </resultMap>

	<sql id="ajpf_Column_List">
		id,areaCode,areaName,areaType,province,city,country,fileCode,expertAName,expertAScore,expertBName,expertBScore,expertConsiderScore,
		crossReviewNameA,crossReviewNameB,crossReviewAScore,crossReviewBScore,crossConsiderScore,isConsider,finalScore
	</sql>

	<!-- 批量查询出 files 表信息需要 id 专家Aid 专家Bid 交叉Aid 交叉Bid -->
	<!-- <select id="getfilesList" resultType="list" resultMap="filesBean">
	 		select
			id,
			isConsider,expertAId,expertBId,expertAScore,expertBScore,expertConsiderScore,
			isConsiderCross,crossReviewAId,crossReviewBId,CrossReviewAScore,CrossReviewBScore,crossConsiderScore,
			fileType
		from files s
		 where fileType!=5 and  ( expertConsiderScore is null or expertConsiderScore ='' ) and expertAId  is not null and expertBId is not null
	</select> -->

	<select id="getfilesList" resultType="list" resultMap="filesBean">
	 		select
			id,
			isConsider,expertAId,expertBId,expertAScore,expertBScore,expertConsiderScore,
			isConsiderCross,crossReviewAId,crossReviewBId,CrossReviewAScore,CrossReviewBScore,crossConsiderScore,
			fileType
		from files s
		 where fileType!=5 and  ( expertConsiderScore is null or expertConsiderScore ='' )
		 and expertAId  is not null and expertBId is not null and expertConsiderScore is not null and expertConsiderScore != ''
	</select>

	<!-- 由于20170119修改附加分的需求，这里把所有的files案卷拿出来，统一计算案卷的最终得分         该方法为一次性方法-->
	<select id="getAllFilesListByAdditional" resultType="list" resultMap="filesBean" >
			select  id, expertConsiderScore, crossConsiderScore  from files s
	</select>
	<!-- 由于20170119修改附加分的需求，去除所有附加分，重新赋值。       该方法为一次性方法-->
	<select id ="getFilesListByAdditional" resultType="list" resultMap="filesBean">
			select  id, expertAId,expertBId
			from files s  where id in (select fileId from  expertHandlFileList  where  jiaFenQingKuang >0
			GROUP BY fileId)
	</select>


	<select id="getFilesByCrossList" resultType="list" resultMap="filesBean">
		select
			id,
			crossConsiderScore,crossReviewAId,crossReviewBId,isConsiderCross, CrossReviewAScore,CrossReviewBScore,
			fileType
		from files s
		where crossConsiderScore is null
	</select>
	<select id="getFilesByFileTypeList" resultType="list" resultMap="filesBean">
		select
			id,
			isConsider,expertAId,expertBId,expertAScore,expertBScore,expertConsiderScore,
			isConsiderCross,crossReviewAId,crossReviewBId,CrossReviewAScore,CrossReviewBScore,crossConsiderScore,
			fileType
		from files s
		 where  fileType = 5 and isConsiderCross = 1
	</select>



	<select id="getFilesListByIsConsider"  resultMap="filesBean">
		select id,isConsider,expertConsiderScore,expertAId,expertBId,crossConsiderScore,crossReviewAId,crossReviewBId,isConsiderCross,
		expertAScore,expertBScore,CrossReviewAScore,CrossReviewBScore
		from files s
		where isConsider ='1' and fileType!=5
	</select>

<!--	<select id="getAjpfListBean" resultType="list" resultMap="ajpfMap">-->
<!--		select *,	SUBSTRING_INDEX(ll.score, ',', 1) AS expertAScore,-->
<!--		SUBSTRING_INDEX(-->
<!--		SUBSTRING_INDEX(ll.score, ',', 2),-->
<!--		',',-->
<!--		- 1-->
<!--		) AS expertBScore,-->
<!--		SUBSTRING_INDEX(SUBSTRING_INDEX(ll.score, ',', 3), ',', -1) AS expertCommitAScore ,-->
<!--		SUBSTRING_INDEX(SUBSTRING_INDEX(ll.score, ',', 4), ',', -1) AS expertCommitBScore  FROM(-->

<!--		select-->
<!--		GROUP_CONCAT(-->
<!--		b.expertFinalScore SEPARATOR ','-->
<!--		) AS score,-->
<!--		s.id,e.province,e.city,e.Country,s.expertconsiderscore2,-->
<!--		s.fileCode,expertAName,expertBName,expertConsiderScore,-->
<!--		CrossReviewNameA,CrossReviewNameB,CrossReviewAScore,CrossReviewBScore,crossConsiderScore,inCheckCrossAScore,inCheckCrossBScore,inCheckCrossFinalScore,-->
<!--		isConsider,finalScore,expertAId, expertBId, crossReviewAId, crossReviewBId,fileType,isConsiderCross,-->
<!--		expertCommitAName,expertCommitBName,expertCommitAId,expertCommitBId,originalCrossScore,originalExpertScore,reportType-->
<!--		from-->
<!--		files s-->
<!--		JOIN expertHandlFileList b ON s.id = b.fileId-->
<!--		LEFT JOIN T_area AS e ON s.belongAreacode = e. CODE-->
<!--		where 1 = 1-->
<!--			<if test="ajpf.areaType != null and ajpf.areaType != ''">-->
<!--				and e.areaLevel = #{ajpf.areaType}-->
<!--			</if>-->
<!--			<if test="ajpf.isConsider != null and ajpf.isConsider != ''">-->
<!--				and s.isConsider = #{ajpf.isConsider}-->
<!--			</if>-->
<!--			<if test="ajpf.isConsiderCross != null and ajpf.isConsiderCross != ''">-->
<!--				and s.isConsiderCross = #{ajpf.isConsiderCross}-->
<!--			</if>-->
<!--			<if test="ajpf.recordCrossUser != null and ajpf.recordCrossUser != ''">-->
<!--				and s.recordCrossUser = #{ajpf.recordCrossUser}-->
<!--			</if>-->
<!--			<if test="ajpf.fileCode != null and ajpf.fileCode != ''">-->
<!--				and s.fileCode like CONCAT('%','${ajpf.fileCode}','%')-->
<!--			</if>-->
<!--			<if test="ajpf.areaCodeLeave != null and ajpf.areaCodeLeave != ''">-->
<!--				and e.areaLevel = #{ajpf.areaCodeLeave}-->
<!--			</if>-->
<!--			<if test="ajpf.isInCheck != null and ajpf.isInCheck != ''">-->
<!--				and s.isInCheck = #{ajpf.isInCheck}-->
<!--			</if>-->
<!--		GROUP BY-->
<!--		s.id) as ll-->
<!--	</select>-->


	<select id="getAjpfListBean" resultType="list" resultMap="ajpfMap">
		SELECT
		e.province as areaProvince,
		e.city as areaCity,
		e.Country as areaCounty,
		fs.id AS id,
		fs.fileCode,
		fs.fileType,
		fs.expertAName,
		fs.expertBName,
		fs.caseCheckState,
		isConsider,
		expertConsiderScore,
		expertAId,
		expertBId,
		AconsiderState expertAScore,
		BconsiderState expertBScore,
		expertCommitAName,
		cl.expertCommitAId,
		cl.expertCommitBId,
		cl.CconsiderState expertCommitAScore,
		expertCommitBName,
		cs.DconsiderState expertCommitBScore,
		AfileMaterials areaCode,
		BfileMaterials areaName,
		AfileMaterialsMsg province,
		BfileMaterialsMsg city
		FROM
		files fs
		JOIN (
		SELECT
		a.id,
		b.expertFinalScore AconsiderState,
		b.fileMaterials AfileMaterials,
		b.fileMaterialsMsg AfileMaterialsMsg
		FROM
		files a
		JOIN expertHandlFileList b ON a.id = b.fileId
		WHERE
		a.expertAId = b.expertId
		ORDER BY
		a.id ASC
		) AS ll ON fs.id = ll.id
		JOIN (
		SELECT
		a.id AS bid,
		b.expertFinalScore BconsiderState,
		b.fileMaterials BfileMaterials,
		b.fileMaterialsMsg BfileMaterialsMsg
		FROM
		files a
		JOIN expertHandlFileList b ON a.id = b.fileId
		WHERE
		a.expertBId = b.expertId
		ORDER BY
		a.id ASC
		) AS ls ON ll.id = ls.bid
		LEFT JOIN (
		SELECT
		a.id AS bid,
		b.expertFinalScore CconsiderState,
		b.fileMaterials CfileMaterials,
		b.fileMaterialsMsg CfileMaterialsMsg,
		a.expertCommitAId,
		a.expertCommitBId
		FROM
		files a
		JOIN expertHandlFileList b ON a.id = b.fileId
		WHERE
		a.expertCommitAId = b.expertId
		ORDER BY
		a.id ASC
		) AS cl ON ls.bid = cl.bid
		LEFT JOIN (
		SELECT
		a.id AS bid,
		b.expertFinalScore DconsiderState,
		b.fileMaterials DfileMaterials,
		b.fileMaterialsMsg DfileMaterialsMsg
		FROM
		files a
		JOIN expertHandlFileList b ON a.id = b.fileId
		WHERE
		a.expertCommitBId = b.expertId
		ORDER BY
		a.id ASC
		) cs ON ls.bid = cs.bid LEFT JOIN T_area AS e ON fs.belongAreacode = e. CODE
		where 1 = 1
		<if test="ajpf.areaType != null and ajpf.areaType != ''">
			and e.areaLevel = #{ajpf.areaType}
		</if>
		<if test="ajpf.isConsider != null and ajpf.isConsider != ''">
			and fs.isConsider = #{ajpf.isConsider}
		</if>
		<if test="ajpf.fileCode != null and ajpf.fileCode != ''">
			and fs.fileCode like CONCAT('%','${ajpf.fileCode}','%')
		</if>
		<if test="ajpf.areaCodeLeave != null and ajpf.areaCodeLeave != ''">
			and e.areaLevel = #{ajpf.areaCodeLeave}
		</if>
		<if test="ajpf.isInCheck != null and ajpf.isInCheck != ''">
			and s.isInCheck = #{ajpf.isInCheck}
		</if>
	</select>

	<!-- 会被专家推荐的案卷 -->
	<select id ="getExpertRecommendFilesList" resultType="list" resultMap="ajpfMap">
		select id,  expertAId, expertBId
		from files
		where 1=1  and fileType!=5  and  expertAId is not null  and expertBId is not null
	</select>


	<!-- 案卷评分：专家导出列表 -->
	<select id ="getExportExpertFileList" parameterType="Map" resultType="list" resultMap="ajpfMap">
		select
		e.province ,
		e.city ,
		e.Country ,
		s.id, fileCode,expertAName,expertAScore,expertBName,expertBScore,expertConsiderScore, isConsider ,expertAId, expertBId, fileType,tcd.name as fileTypeName,expertCommitAName,expertCommitAScore,expertCommitBName,expertCommitBScore
		from files as s
		LEFT JOIN TC_dictionary  as tcd on fileType = tcd.code and tcd.temp='fileType',T_area as e
		where 1=1
		and s.belongAreacode = e.code
		and fileType!=5  and  expertAId is not null  and expertBId is not null
		<if test="areaType!=null and areaType!=''">
			and e.areaLevel = #{areaType}
		</if>
		<if test="isConsider!=null and isConsider!=''">
			and s.isConsider = #{isConsider}
		</if>
		<if test="fileCode!=null and fileCode!=''">
			and s.fileCode like CONCAT('%','${fileCode}','%')
		</if>
		order by s.areaCode asc
	</select>
	<!-- 案卷评分：专家导出列表  end-->


	<select id="getAjpfExpertListBean" resultType="list" resultMap="ajpfMap">
		select
		e.province ,
		e.city ,
		e.Country ,
		s.id,fileName,fileUrl, fileCode,expertAName,expertAScore,expertBName,expertBScore,expertConsiderScore, isConsider ,expertAId, expertBId, fileType,
		expertCommitAName,expertCommitAId,expertCommitAScore,expertCommitBId,expertCommitBName,expertCommitBScore,expertconsiderscore2
		from files as s,
		 T_area as e where s.belongAreacode = e.code
			and fileType!=5  and  expertAId is not null  and expertBId is not null
			<if test="ajpf.areaType != null and ajpf.areaType != ''">
				and e.areaLevel = #{ajpf.areaType}
			</if>
			<if test="ajpf.isConsider != null and ajpf.isConsider != ''">
				and s.isConsider = #{ajpf.isConsider}
			</if>
			<if test="ajpf.fileCode != null and ajpf.fileCode != ''">
				and s.fileCode like CONCAT('%','${ajpf.fileCode}','%')
			</if>
			<if test="ajpf.filetype != null and ajpf.filetype != ''">
				and s.fileType  = #{ajpf.filetype}
			</if>
			<if test="ajpf.isInCheck != null and ajpf.isInCheck != ''">
				and s.isInCheck  = #{ajpf.isInCheck}
			</if>
			order by s.areaCode,s.fileType,s.id asc
	</select>

	<select id="getAjpfCrossListBean" resultType="list" resultMap="ajpfMap">
		select
		s.id, fileCode, CrossReviewNameA,CrossReviewNameB,CrossReviewAScore,CrossReviewBScore,crossConsiderScore, crossReviewAId, crossReviewBId,fileType,isConsiderCross
		from
		files as s
		left join T_area as e on s.belongAreacode = e.code
		<where>
			1 = 1
			<if test="ajpf.areaType != null and ajpf.areaType != ''">
				and e.areaLevel = #{ajpf.areaType}
			</if>
			<if test="ajpf.isConsiderCross != null and ajpf.isConsiderCross != ''">
				and s.isConsiderCross = #{ajpf.isConsiderCross}
			</if>
			<if test="ajpf.fileCode != null and ajpf.fileCode != ''">
				and s.fileCode like CONCAT('%','${ajpf.fileCode}','%')
			</if>
			<if test="ajpf.areaCodeLeave != null and ajpf.areaCodeLeave != ''">
				and e.areaLevel = #{ajpf.areaCodeLeave}
			</if>
			order by s.areaCode asc
		</where>
	</select>


	<select id="getAjpfElectionPersonalListBean" resultType="list"
		resultMap="ajpfPersonalMap">

		SELECT id, areaCode, areaType,province,
		city, country,
		personalMaterialName, perCrossReviewNameA,
		perCrossReviewNameB,perCrossReviewAScore,
		perCrossReviewBScore,perConsiderScore,
		isConsider,totalScore,
		perFinalScore,crossReviewAId,crossReviewBId from electionPersonal as e
		<where>
			1 = 1
			<if test="ajpfPersonal.areaType != null and ajpfPersonal.areaType != ''">
				and e.areaType = #{ajpfPersonal.areaType}
			</if>
			<if
				test="ajpfPersonal.isConsider != null and ajpfPersonal.isConsider != ''">
				and e.isConsider = #{ajpfPersonal.isConsider}
			</if>
			<if
				test="ajpfPersonal.personalMaterialName != null and ajpfPersonal.personalMaterialName != ''">
				and e.personalMaterialName like
				CONCAT('%','${ajpfPersonal.personalMaterialName}','%')
			</if>
			order by e.areaCode asc
		</where>
	</select>


	<!-- changneng 报社 ：集体 -->
	<select id="getNewspaperBeanJt" resultType="list" resultMap="spaperBean">
		select s.areaCode,u.province,u.city,u.country, s.fileName,tc.name as
		types ,s.fileCode,s.fileSimpleDesc,s.fileDetiailDesc
		from files s
		LEFT
		JOIN electionUnit as u ON u.areaCode = s.areaCode
		LEFT JOIN
		TC_dictionary as tc ON tc.code = s.fileType
		where tc.temp='fileType'
		and s.type!='1' and fileType!=4 and fileType!=5
		ORDER BY s.areaCode

	</select>
	<!-- changneng 报社 ：个人 -->
	<select id="getNewspaperBeanGr" resultType="list" resultMap="spaperBean">
		select e.name,e.cardID,e.areaCode,tar.province,tar.city,tar.Country as country,s.fileName ,tc.name as types ,
		s.fileCode,e.fileSimpleDesc,e.fileDetiailDesc
		from files s
		LEFT JOIN electionPersonal as e ON e.fileId = s.id
		LEFT JOIN TC_dictionary as tc ON tc.code = s.fileType
		LEFT JOIN T_area  as tar ON tar.code = e.areaCode
		where tc.temp='fileType' and s.type !=0 and fileType!=5
		ORDER BY e.areaCode
	</select>


	<select id="getNewHomeBean" parameterType="java.lang.String" resultMap="homeBean" >
		-- 案卷总数
		select count(1) as filesCountNum,
		-- 即属于集体又属于个人案卷
		(select count(1) from files as fs where fs.type =2  ${jtSqlAppend} ) as repeatFiles,
		-- 集体案卷总数量
		(select count(1) from files as fs where fs.type !=1 ${jtSqlAppend} ) as filesTypeJt,
		-- 省
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=1  ${jtSqlAppend} ) as provinceCountNumJt,
		-- 市
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=2  ${jtSqlAppend}) as cityCountNumJt,
		-- 县
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=3  ${jtSqlAppend} ) as countryCountNumJt,
		-- 省 0 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=1 and fs.fileType=0  ${jtSqlAppend} ) as province0NumJt,
		-- 省 1 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=1 and fs.fileType=1 ${jtSqlAppend} ) as province1NumJt,
		-- 省 2 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=1 and fs.fileType=2 ${jtSqlAppend} ) as province2NumJt,
		-- 省 3 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=1 and fs.fileType=3 ${jtSqlAppend} ) as province3NumJt,
		-- 省 4 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=1 and fs.fileType=4 ${jtSqlAppend} ) as province4NumJt,
		-- 省 5 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=1 and fs.fileType=5 ${jtSqlAppend} ) as province5NumJt,
		-- 省 6 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=1 and fs.fileType=6 ${jtSqlAppend} ) as province6NumJt,
		-- 省 7 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=1 and fs.fileType=7 ${jtSqlAppend} ) as province7NumJt,
		-- 市 0 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=2 and fs.fileType=0 ${jtSqlAppend} ) as city0NumJt,
		-- 市 1 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=2 and fs.fileType=1 ${jtSqlAppend} ) as city1NumJt,
		-- 市 2 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=2 and fs.fileType=2 ${jtSqlAppend} ) as city2NumJt,
		-- 市 3 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=2 and fs.fileType=3 ${jtSqlAppend} ) as city3NumJt,
		-- 市 4 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=2 and fs.fileType=4 ${jtSqlAppend} ) as city4NumJt,
		-- 市 5 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=2 and fs.fileType=5 ${jtSqlAppend} ) as city5NumJt,
		-- 市 6类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=2 and fs.fileType=6 ${jtSqlAppend} ) as city6NumJt,
		-- 市 7 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=2 and fs.fileType=7 ${jtSqlAppend} ) as city7NumJt,
		-- 县 0 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=3 and fs.fileType=0 ${jtSqlAppend} ) as country0NumJt,
		-- 县 1 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=3 and fs.fileType=1 ${jtSqlAppend} ) as country1NumJt,
		-- 县 2 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=3 and fs.fileType=2 ${jtSqlAppend} ) as country2NumJt,
		-- 县 3 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=3 and fs.fileType=3 ${jtSqlAppend} ) as country3NumJt,
		-- 县 4 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=3 and fs.fileType=4 ${jtSqlAppend} ) as country4NumJt,
		-- 县 5 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=3 and fs.fileType=5 ${jtSqlAppend} ) as country5NumJt,
		-- 县 6类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=3 and fs.fileType=6 ${jtSqlAppend} ) as country6NumJt,
		-- 县 7类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=3 and fs.fileType=7 ${jtSqlAppend} ) as country7NumJt,

		-- 个人案卷总数量
		(select count(1) from files as fs where fs.type !=0 ${grSqlAppend} ) as filesTypeGr,
		-- 省
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.belongAreacode
		where fs.type !=0 and t.areaLevel=1 ${grSqlAppend} ) as provinceCountNumGr,
		-- 市
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.belongAreacode
		where fs.type !=0 and t.areaLevel=2 ${grSqlAppend} ) as cityCountNumGr,
		-- 县
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.belongAreacode
		where fs.type !=0 and t.areaLevel=3 ${grSqlAppend} ) as countryCountNumGr,
		-- 省 0 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.belongAreacode
		where fs.type !=0 and t.areaLevel=1 and fs.fileType=0  ${grSqlAppend} ) as province0NumGr,
		-- 省 1 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.belongAreacode
		where fs.type !=0 and t.areaLevel=1 and fs.fileType=1 ${grSqlAppend} ) as province1NumGr,
		-- 省 2 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.belongAreacode
		where fs.type !=0 and t.areaLevel=1 and fs.fileType=2 ${grSqlAppend} ) as province2NumGr,
		-- 省 3 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.belongAreacode
		where fs.type !=0 and t.areaLevel=1 and fs.fileType=3 ${grSqlAppend} ) as province3NumGr,
		-- 省 4 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.belongAreacode
		where fs.type !=0 and t.areaLevel=1 and fs.fileType=4 ${grSqlAppend} ) as province4NumGr,
		-- 省 5 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.belongAreacode
		where fs.type !=0 and t.areaLevel=1 and fs.fileType=5 ${grSqlAppend} ) as province5NumGr,
		-- 省 6 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.belongAreacode
		where fs.type !=0 and t.areaLevel=1 and fs.fileType=6 ${grSqlAppend} ) as province6NumGr,
		-- 省 7 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.belongAreacode
		where fs.type !=0 and t.areaLevel=1 and fs.fileType=7 ${grSqlAppend} ) as province7NumGr,
		-- 市 0 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.belongAreacode
		where fs.type !=0 and t.areaLevel=2 and fs.fileType=0 ${grSqlAppend} ) as city0NumGr,
		-- 市 1 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.belongAreacode
		where fs.type !=0 and t.areaLevel=2 and fs.fileType=1 ${grSqlAppend} ) as city1NumGr,
		-- 市 2 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.belongAreacode
		where fs.type !=0 and t.areaLevel=2 and fs.fileType=2 ${grSqlAppend} ) as city2NumGr,
		-- 市 3 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.belongAreacode
		where fs.type !=0 and t.areaLevel=2 and fs.fileType=3 ${grSqlAppend} ) as city3NumGr,
		-- 市 4 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.belongAreacode
		where fs.type !=0 and t.areaLevel=2 and fs.fileType=4 ${grSqlAppend} ) as city4NumGr,
		-- 市 5 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.belongAreacode
		where fs.type !=0 and t.areaLevel=2 and fs.fileType=5 ${grSqlAppend} ) as city5NumGr,
		-- 市 6 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.belongAreacode
		where fs.type !=0 and t.areaLevel=2 and fs.fileType=6 ${grSqlAppend} ) as city6NumGr,
		-- 市 7 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.belongAreacode
		where fs.type !=0 and t.areaLevel=2 and fs.fileType=7 ${grSqlAppend} ) as city7NumGr,
		-- 县 0 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.belongAreacode
		where fs.type !=0 and t.areaLevel=3 and fs.fileType=0 ${grSqlAppend} ) as country0NumGr,
		-- 县 1 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.belongAreacode
		where fs.type !=0 and t.areaLevel=3 and fs.fileType=1 ${grSqlAppend} ) as country1NumGr,
		-- 县 2 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.belongAreacode
		where fs.type !=0 and t.areaLevel=3 and fs.fileType=2 ${grSqlAppend} ) as country2NumGr,
		-- 县 3 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.belongAreacode
		where fs.type !=0 and t.areaLevel=3 and fs.fileType=3 ${grSqlAppend} ) as country3NumGr,
		-- 县 4 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.belongAreacode
		where fs.type !=0 and t.areaLevel=3 and fs.fileType=4 ${grSqlAppend} ) as country4NumGr,
		-- 县 5 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.belongAreacode
		where fs.type !=0 and t.areaLevel=3 and fs.fileType=5 ${grSqlAppend} ) as country5NumGr,
		-- 县 6 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.belongAreacode
		where fs.type !=0 and t.areaLevel=3 and fs.fileType=6 ${grSqlAppend} ) as country6NumGr,
		-- 县 7 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.belongAreacode
		where fs.type !=0 and t.areaLevel=3 and fs.fileType=7 ${grSqlAppend} ) as country7NumGr

		from files

	</select>



	<select id="getNewHomeBean2017" parameterType="java.lang.String" resultMap="homeBean" >
		-- 案卷总数
		select count(1) as filesCountNum,
		-- 即属于集体又属于个人案卷
		(select count(1) from files as fs where fs.type =2  ${jtSqlAppend} ) as repeatFiles,
		-- 集体案卷总数量
		(select count(1) from files as fs where fs.type !=1 and fs.reportType=0  ${jtSqlAppend} ) as filesTypeJt,
		-- 个人和个人重复使用的案卷
		(
		select t.a-(select count(*) from (select count(1) from personalFiles as fs LEFT JOIN electionPersonal as ps ON  ps.id = fs.personalId WHERE 1=1 ${grSqlAppend}  GROUP BY fs.fileid) as t) as poor from (select count(1) as a from personalFiles as fs LEFT JOIN electionPersonal as ps ON  ps.id = fs.personalId WHERE 1=1 ${grSqlAppend}) as t
		) as personalRepeatFiles,
		-- 省
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=1  and fs.reportType=0 ${jtSqlAppend} ) as provinceCountNumJt,
		-- 市
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=2  and fs.reportType=0 ${jtSqlAppend}) as cityCountNumJt,
		-- 县
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=3  and fs.reportType=0 ${jtSqlAppend} ) as countryCountNumJt,
		-- 省 0 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=1 and fs.fileType=0   and fs.reportType=0 ${jtSqlAppend} ) as province0NumJt,
		-- 省 1 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=1 and fs.fileType=1  and fs.reportType=0 ${jtSqlAppend} ) as province1NumJt,
		-- 省 2 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=1 and fs.fileType=2  and fs.reportType=0 ${jtSqlAppend} ) as province2NumJt,
		-- 省 3 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=1 and fs.fileType=3  and fs.reportType=0 ${jtSqlAppend} ) as province3NumJt,
		-- 省 4 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=1 and fs.fileType=4  and fs.reportType=0 ${jtSqlAppend} ) as province4NumJt,
		-- 省 5 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=1 and fs.fileType=5   and fs.reportType=0 ${jtSqlAppend} ) as province5NumJt,
		-- 省 6 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=1 and fs.fileType=6  and fs.reportType=0 ${jtSqlAppend} ) as province6NumJt,
		-- 省 7 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=1 and fs.fileType=7   and fs.reportType=0 ${jtSqlAppend} ) as province7NumJt,
		-- 市 0 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=2 and fs.fileType=0  and fs.reportType=0 ${jtSqlAppend} ) as city0NumJt,
		-- 市 1 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=2 and fs.fileType=1 and fs.reportType=0  ${jtSqlAppend} ) as city1NumJt,
		-- 市 2 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=2 and fs.fileType=2  and fs.reportType=0 ${jtSqlAppend} ) as city2NumJt,
		-- 市 3 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=2 and fs.fileType=3  and fs.reportType=0 ${jtSqlAppend} ) as city3NumJt,
		-- 市 4 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=2 and fs.fileType=4  and fs.reportType=0 ${jtSqlAppend} ) as city4NumJt,
		-- 市 5 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=2 and fs.fileType=5  and fs.reportType=0 ${jtSqlAppend} ) as city5NumJt,
		-- 市 6类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=2 and fs.fileType=6  and fs.reportType=0 ${jtSqlAppend} ) as city6NumJt,
		-- 市 7 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=2 and fs.fileType=7  and fs.reportType=0 ${jtSqlAppend} ) as city7NumJt,
		-- 县 0 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=3 and fs.fileType=0  and fs.reportType=0 ${jtSqlAppend} ) as country0NumJt,
		-- 县 1 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=3 and fs.fileType=1 and fs.reportType=0 ${jtSqlAppend} ) as country1NumJt,
		-- 县 2 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=3 and fs.fileType=2  and fs.reportType=0 ${jtSqlAppend} ) as country2NumJt,
		-- 县 3 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=3 and fs.fileType=3  and fs.reportType=0 ${jtSqlAppend} ) as country3NumJt,
		-- 县 4 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=3 and fs.fileType=4  and fs.reportType=0 ${jtSqlAppend} ) as country4NumJt,
		-- 县 5 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=3 and fs.fileType=5 and fs.reportType=0 ${jtSqlAppend} ) as country5NumJt,
		-- 县 6类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=3 and fs.fileType=6  and fs.reportType=0 ${jtSqlAppend} ) as country6NumJt,
		-- 县 7类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=3 and fs.fileType=7  and fs.reportType=0 ${jtSqlAppend} ) as country7NumJt,


		-- 集体随机总数量
		(select count(1) from files as fs where fs.reportType=1  ${jtSqlAppend} ) as filesTypeJtRandom,
		-- 省
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=1  and fs.reportType=1 ${jtSqlAppend} ) as provinceCountNumJtRandom,
		-- 市
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=2  and fs.reportType=1 ${jtSqlAppend}) as cityCountNumJtRandom,
		-- 县
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=3  and fs.reportType=1 ${jtSqlAppend} ) as countryCountNumJtRandom,
		-- 省 0 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=1 and fs.fileType=0   and fs.reportType=1 ${jtSqlAppend} ) as province0NumJtRandom,
		-- 省 1 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=1 and fs.fileType=1  and fs.reportType=1 ${jtSqlAppend} ) as province1NumJtRandom,
		-- 省 2 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=1 and fs.fileType=2  and fs.reportType=1 ${jtSqlAppend} ) as province2NumJtRandom,
		-- 省 3 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=1 and fs.fileType=3  and fs.reportType=1 ${jtSqlAppend} ) as province3NumJtRandom,
		-- 省 4 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=1 and fs.fileType=4  and fs.reportType=1 ${jtSqlAppend} ) as province4NumJtRandom,
		-- 省 5 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=1 and fs.fileType=5   and fs.reportType=1 ${jtSqlAppend} ) as province5NumJtRandom,
		-- 省 6 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=1 and fs.fileType=6  and fs.reportType=1 ${jtSqlAppend} ) as province6NumJtRandom,
		-- 省 7 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=1 and fs.fileType=7   and fs.reportType=1 ${jtSqlAppend} ) as province7NumJtRandom,
		-- 市 0 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=2 and fs.fileType=0  and fs.reportType=1 ${jtSqlAppend} ) as city0NumJtRandom,
		-- 市 1 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=2 and fs.fileType=1 and fs.reportType=1  ${jtSqlAppend} ) as city1NumJtRandom,
		-- 市 2 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=2 and fs.fileType=2  and fs.reportType=1 ${jtSqlAppend} ) as city2NumJtRandom,
		-- 市 3 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=2 and fs.fileType=3  and fs.reportType=1 ${jtSqlAppend} ) as city3NumJtRandom,
		-- 市 4 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=2 and fs.fileType=4  and fs.reportType=1 ${jtSqlAppend} ) as city4NumJtRandom,
		-- 市 5 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=2 and fs.fileType=5  and fs.reportType=1 ${jtSqlAppend} ) as city5NumJtRandom,
		-- 市 6类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=2 and fs.fileType=6  and fs.reportType=1 ${jtSqlAppend} ) as city6NumJtRandom,
		-- 市 7 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=2 and fs.fileType=7  and fs.reportType=1 ${jtSqlAppend} ) as city7NumJtRandom,
		-- 县 0 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=3 and fs.fileType=0  and fs.reportType=1 ${jtSqlAppend} ) as country0NumJtRandom,
		-- 县 1 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=3 and fs.fileType=1 and fs.reportType=1 ${jtSqlAppend} ) as country1NumJtRandom,
		-- 县 2 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=3 and fs.fileType=2  and fs.reportType=1 ${jtSqlAppend} ) as country2NumJtRandom,
		-- 县 3 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=3 and fs.fileType=3  and fs.reportType=1 ${jtSqlAppend} ) as country3NumJtRandom,
		-- 县 4 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=3 and fs.fileType=4  and fs.reportType=1 ${jtSqlAppend} ) as country4NumJtRandom,
		-- 县 5 类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=3 and fs.fileType=5 and fs.reportType=1 ${jtSqlAppend} ) as country5NumJtRandom,
		-- 县 6类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=3 and fs.fileType=6  and fs.reportType=1 ${jtSqlAppend} ) as country6NumJtRandom,
		-- 县 7类型
		(select count(1) from files as fs LEFT JOIN T_area t ON t.code = fs.areaCode
		where fs.type !=1 and t.areaLevel=3 and fs.fileType=7  and fs.reportType=1 ${jtSqlAppend} ) as country7NumJtRandom,

		-- 个人案卷总数量
		(select count(1) from personalFiles as fs LEFT JOIN electionPersonal as ps ON  ps.id = fs.personalId WHERE 1=1 ${grSqlAppend} ) as filesTypeGr,
		-- 省
		(select count(1) from personalFiles as fs LEFT JOIN electionPersonal as ps ON  ps.id = fs.personalId
		where ps.areaType ='1' ${grSqlAppend} ) as provinceCountNumGr,
		-- 市
		(select count(1) from personalFiles as fs LEFT JOIN electionPersonal as ps ON  ps.id = fs.personalId
		where ps.areaType ='2' ${grSqlAppend} ) as cityCountNumGr,
		-- 县
		(select count(1) from personalFiles as fs LEFT JOIN electionPersonal as ps ON  ps.id = fs.personalId
		where ps.areaType = '3' ${grSqlAppend} ) as countryCountNumGr,
		-- 省 0 类型
		(select count(1) from personalFiles as fp LEFT JOIN electionPersonal as ps ON  ps.id = fp.personalId LEFT JOIN files as fs ON fp.fileId = fs.id
		where fs.type !=0 and ps.areaType=1 and fs.fileType=0  ${grSqlAppend} ) as province0NumGr,
		-- 省 1 类型
		(select count(1) from personalFiles as fp LEFT JOIN electionPersonal as ps ON  ps.id = fp.personalId LEFT JOIN files as fs ON fp.fileId = fs.id
		where fs.type !=0 and ps.areaType=1 and fs.fileType=1 ${grSqlAppend} ) as province1NumGr,
		-- 省 2 类型
		(select count(1) from personalFiles as fp LEFT JOIN electionPersonal as ps ON  ps.id = fp.personalId LEFT JOIN files as fs ON fp.fileId = fs.id
		where fs.type !=0 and ps.areaType=1 and fs.fileType=2 ${grSqlAppend} ) as province2NumGr,
		-- 省 3 类型
		(select count(1) from personalFiles as fp LEFT JOIN electionPersonal as ps ON  ps.id = fp.personalId LEFT JOIN files as fs ON fp.fileId = fs.id
		where fs.type !=0 and ps.areaType=1 and fs.fileType=3 ${grSqlAppend} ) as province3NumGr,
		-- 省 4 类型
		(select count(1) from personalFiles as fp LEFT JOIN electionPersonal as ps ON  ps.id = fp.personalId LEFT JOIN files as fs ON fp.fileId = fs.id
		where fs.type !=0 and ps.areaType=1 and fs.fileType=4 ${grSqlAppend} ) as province4NumGr,
		-- 省 5 类型
		(select count(1) from personalFiles as fp LEFT JOIN electionPersonal as ps ON  ps.id = fp.personalId LEFT JOIN files as fs ON fp.fileId = fs.id
		where fs.type !=0 and ps.areaType=1 and fs.fileType=5 ${grSqlAppend} ) as province5NumGr,
		-- 省 6 类型
		(select count(1) from personalFiles as fp LEFT JOIN electionPersonal as ps ON  ps.id = fp.personalId LEFT JOIN files as fs ON fp.fileId = fs.id
		where fs.type !=0 and ps.areaType=1 and fs.fileType=6 ${grSqlAppend} ) as province6NumGr,
		-- 省 7 类型
		(select count(1) from personalFiles as fp LEFT JOIN electionPersonal as ps ON  ps.id = fp.personalId LEFT JOIN files as fs ON fp.fileId = fs.id
		where fs.type !=0 and ps.areaType=1 and fs.fileType=7 ${grSqlAppend} ) as province7NumGr,
		-- 市 0 类型
		(select count(1) from personalFiles as fp LEFT JOIN electionPersonal as ps ON  ps.id = fp.personalId LEFT JOIN files as fs ON fp.fileId = fs.id
		where fs.type !=0 and ps.areaType=2 and fs.fileType=0 ${grSqlAppend} ) as city0NumGr,
		-- 市 1 类型
		(select count(1) from personalFiles as fp LEFT JOIN electionPersonal as ps ON  ps.id = fp.personalId LEFT JOIN files as fs ON fp.fileId = fs.id
		where fs.type !=0 and ps.areaType=2 and fs.fileType=1 ${grSqlAppend} ) as city1NumGr,
		-- 市 2 类型
		(select count(1) from personalFiles as fp LEFT JOIN electionPersonal as ps ON  ps.id = fp.personalId LEFT JOIN files as fs ON fp.fileId = fs.id
		where fs.type !=0 and ps.areaType=2 and fs.fileType=2 ${grSqlAppend} ) as city2NumGr,
		-- 市 3 类型
		(select count(1) from personalFiles as fp LEFT JOIN electionPersonal as ps ON  ps.id = fp.personalId LEFT JOIN files as fs ON fp.fileId = fs.id
		where fs.type !=0 and ps.areaType=2 and fs.fileType=3 ${grSqlAppend} ) as city3NumGr,
		-- 市 4 类型
		(select count(1) from personalFiles as fp LEFT JOIN electionPersonal as ps ON  ps.id = fp.personalId LEFT JOIN files as fs ON fp.fileId = fs.id
		where fs.type !=0 and ps.areaType=2 and fs.fileType=4 ${grSqlAppend} ) as city4NumGr,
		-- 市 5 类型
		(select count(1) from personalFiles as fp LEFT JOIN electionPersonal as ps ON  ps.id = fp.personalId LEFT JOIN files as fs ON fp.fileId = fs.id
		where fs.type !=0 and ps.areaType=2 and fs.fileType=5 ${grSqlAppend} ) as city5NumGr,
		-- 市 6 类型
		(select count(1) from personalFiles as fp LEFT JOIN electionPersonal as ps ON  ps.id = fp.personalId LEFT JOIN files as fs ON fp.fileId = fs.id
		where fs.type !=0 and ps.areaType=2 and fs.fileType=6 ${grSqlAppend} ) as city6NumGr,
		-- 市 7 类型
		(select count(1) from personalFiles as fp LEFT JOIN electionPersonal as ps ON  ps.id = fp.personalId LEFT JOIN files as fs ON fp.fileId = fs.id
		where fs.type !=0 and ps.areaType=2 and fs.fileType=7 ${grSqlAppend} ) as city7NumGr,
		-- 县 0 类型
		(select count(1) from personalFiles as fp LEFT JOIN electionPersonal as ps ON  ps.id = fp.personalId LEFT JOIN files as fs ON fp.fileId = fs.id
		where fs.type !=0 and ps.areaType=3 and fs.fileType=0 ${grSqlAppend} ) as country0NumGr,
		-- 县 1 类型
		(select count(1) from personalFiles as fp LEFT JOIN electionPersonal as ps ON  ps.id = fp.personalId LEFT JOIN files as fs ON fp.fileId = fs.id
		where fs.type !=0 and ps.areaType=3 and fs.fileType=1 ${grSqlAppend} ) as country1NumGr,
		-- 县 2 类型
		(select count(1) from personalFiles as fp LEFT JOIN electionPersonal as ps ON  ps.id = fp.personalId LEFT JOIN files as fs ON fp.fileId = fs.id
		where fs.type !=0 and ps.areaType=3 and fs.fileType=2 ${grSqlAppend} ) as country2NumGr,
		-- 县 3 类型
		(select count(1) from personalFiles as fp LEFT JOIN electionPersonal as ps ON  ps.id = fp.personalId LEFT JOIN files as fs ON fp.fileId = fs.id
		where fs.type !=0 and ps.areaType=3 and fs.fileType=3 ${grSqlAppend} ) as country3NumGr,
		-- 县 4 类型
		(select count(1) from personalFiles as fp LEFT JOIN electionPersonal as ps ON  ps.id = fp.personalId LEFT JOIN files as fs ON fp.fileId = fs.id
		where fs.type !=0 and ps.areaType=3 and fs.fileType=4 ${grSqlAppend} ) as country4NumGr,
		-- 县 5 类型
		(select count(1) from personalFiles as fp LEFT JOIN electionPersonal as ps ON  ps.id = fp.personalId LEFT JOIN files as fs ON fp.fileId = fs.id
		where fs.type !=0 and ps.areaType=3 and fs.fileType=5 ${grSqlAppend} ) as country5NumGr,
		-- 县 6 类型
		(select count(1) from personalFiles as fp LEFT JOIN electionPersonal as ps ON  ps.id = fp.personalId LEFT JOIN files as fs ON fp.fileId = fs.id
		where fs.type !=0 and ps.areaType=3 and fs.fileType=6 ${grSqlAppend} ) as country6NumGr,
		-- 县 7 类型
		(select count(1) from personalFiles as fp LEFT JOIN electionPersonal as ps ON  ps.id = fp.personalId LEFT JOIN files as fs ON fp.fileId = fs.id
		where fs.type !=0 and ps.areaType=3 and fs.fileType=7 ${grSqlAppend} ) as country7NumGr

		from files limit 1

	</select>





	<update id="changeCityBaseScore" >
	update files as fl
	set fl.expertConsiderScore = (select  ROUND(avgScore.score/avgScore.countnum * (fl.crossConsiderScore/100),3) as ttt from
				(	select count(1) as countnum ,sum(expertConsiderScore) as score
				from files f  where f.areaCode in
				(SELECT e.areaCode from electionUnit e where e.areaType='2')
				and  f.expertAId  is not NULL  and f.expertBId is not null
				) as avgScore
	)
	where fl.expertAId  is NULL  and fl.expertBId is null
	and fl.areaCode in (SELECT e.areaCode from electionUnit e where e.areaType='2')
    </update>
    <update id="changeCountyBaseScore" >
 	update files as fl
		set fl.expertConsiderScore = (select  ROUND(avgScore.score/avgScore.countnum * (fl.crossConsiderScore/100),3) as ttt from
		(	select count(1) as countnum ,sum(expertConsiderScore) as score
		from files f  where f.areaCode in
		(SELECT e.areaCode from electionUnit e where e.areaType='3')
		and  f.expertAId  is not NULL  and f.expertBId is not null
		) as avgScore
		)
		where fl.expertAId  is NULL  and fl.expertBId is null
		and fl.areaCode in (SELECT e.areaCode from electionUnit e where e.areaType='3')
    </update>




	<!--交叉评分结束后：补交案卷使用部分   -->

	 <select id="getAllTempFiles" resultType="list" resultMap="tempFilesBean">
 		select *  from  temp_files
	</select>

	 <update id="updateTempFilesById" parameterType="com.changneng.sa.bean.tempFiles">
	    update temp_files
	    <set>
	      <if test="filesid != null">
	        filesid = #{filesid,jdbcType=INTEGER},
	      </if>
	      <if test="syncnumber != null">
	        syncNumber = #{syncnumber,jdbcType=VARCHAR},
	      </if>
	      <if test="filescode != null">
	        filesCode = #{filescode,jdbcType=VARCHAR},
	      </if>
	      <if test="filestype != null">
	        filesType = #{filestype,jdbcType=VARCHAR},
	      </if>
	    </set>
	    where id = #{id,jdbcType=INTEGER}
  	</update>


	<select id="getFilesBeanListByFilesCode" resultType="list" resultMap="filesBeanByFilesCode">
 		select id,fileType,fileCode  from  files where fileCode = #{filesCode}
	</select>
	<!--查询实体否决模块List-->
	<select id="getEntityVetoList" resultType="list" resultMap="ajpfMap">
		select entityVeto.*
		from(
		SELECT
		s1.* ,
		s2.scoringIndexNames,
		s2.indexId
		FROM (
		select
		s.id,
		e.province,
		e.city,
		e.Country,
		s.fileCode,
		s.fileType,
		e.areaLevel,
		ef.expertName,
		COUNT(DISTINCT CASE WHEN es.resultScore = 1 THEN ef.expertId END) AS expertVetoNumber
		from
		files as s
		left join T_area e on s.belongAreacode = e.code
		left join expertHandlFileList ef on ef.fileId = s.id
		left join expertHandlIndexScore es on es.expertHandlId = ef.id and es.resultScore = 1
		LEFT JOIN scoringIndex si on si.id = es.indexId
		where
		si.fileType = '98'
		AND es.scoreType = '1'
		GROUP BY s.id
		) s1
		left join (
		select
		s.id,
		s.fileCode,
		ef.expertName,
		GROUP_CONCAT(DISTINCT si.indexName) as scoringIndexNames,
		GROUP_CONCAT(DISTINCT si.id) as indexId
		from
		files as s
		left join T_area e on s.belongAreacode = e.code
		left join expertHandlFileList ef on ef.fileId = s.id
		left join expertHandlIndexScore es on es.expertHandlId = ef.id
		LEFT JOIN scoringIndex si on si.id = es.indexId
		where
		si.fileType = '98'
		AND es.scoreType = '1'
		AND es.resultScore = '1'
		GROUP BY s.id
		)s2 on s2.id = s1.id

		<where>
			<if test="ajpf.areaType != null and ajpf.areaType != ''">
				and s1.areaLevel = #{ajpf.areaType}
			</if>
			<if test="ajpf.fileCode != null and ajpf.fileCode != ''">
				and s1.fileCode like CONCAT('%','${ajpf.fileCode}','%')
			</if>
		</where>
		ORDER BY s1.id desc
		) entityVeto
		<where>
			<if test="ajpf.indexId != null and ajpf.indexId != ''">
				and entityVeto.indexId like CONCAT('%','${ajpf.indexId}','%')
			</if>
		</where>

	</select>

	<resultMap id="entityVetoInfo" type="com.changneng.sa.bean.FilesEntityVetoInfoVo">
		<result property="filesId" column="filesId" />
		<result property="province" column="province" />
		<result property="city" column="city" />
		<result property="country" column="country" />
		<result property="fileCode" column="fileCode" />
		<result property="fileType" column="fileType" />
		<result property="scoringIndexNames" column="scoringIndexNames" />
		<result property="filesIssues" column="filesIssues" />

		<collection property="filesEntityExpertList" ofType="com.changneng.sa.bean.FilesEntityExpertVo">
			<result property="handlType" column="handlType"/>

			<collection property="filesEntityItemList" ofType="com.changneng.sa.bean.FilesEntityItemVo">
				<result property="indexId" column="indexId"/>
				<result property="indexName" column="indexName"/>
				<result property="itemId" column="itemId"/>
				<result property="itemName" column="itemName"/>

				<collection property="filesEntityHandlTypeList" ofType="com.changneng.sa.bean.FilesEntityHandlTypeVo">
					<result property="expertName" column="expertName"/>
					<result property="comment" column="comment"/>

				</collection>
			</collection>

		</collection>

	</resultMap>

	<!--根据案卷ID查询实体否决结果-->
	<select id="getEntityVetoInfoByFileId" resultMap="entityVetoInfo">
		select
		s.id as filesId,
		s.filesIssues as filesIssues,
		e.province,
		e.city,
		e.Country,
		s.fileCode,
		s.fileType,
		ef.handlType as handlType,
		ef.expertName,
		es.resultScore,
		ei.commeStr as comment,
		es.indexId as indexId,
		si.indexName as indexName,
		ei.itemId as itemId,
		sm.itemName as itemName
		from
		files as s
		left join T_area e on s.belongAreacode = e.code
		left join expertHandlFileList ef on ef.fileId = s.id
		left join expertHandlIndexScore es on es.expertHandlId = ef.id
		LEFT JOIN scoringIndex si on si.id = es.indexId
		left JOIN expertHandlItemScore ei on ei.expertIndexId = es.id AND ei.score = '1'
		LEFT JOIN scoringItem sm on sm.id = ei.itemId and sm.state = 0

		<where>
			si.fileType = '98'
			AND es.scoreType = '1'
			AND es.resultScore = '1'
			and s.id = #{filesId}
		</where>
		ORDER BY s.id ,ef.handlType

	</select>
	<!--修改案卷存在的问题-->
	<update id="updateEntityVeto">
		UPDATE
			files
		SET
			filesIssues = #{filesIssues}
		WHERE
			id = #{filesId};
	</update>

	<!-- 补交案卷使用部分结束  -->

	<select id="getEntityVetoExList" resultType="com.changneng.sa.bean.EntityVetoExcelVo">
		select
			'' as Region,
			'' as largeArea,
			ee.province as unitProvince,
			ee.city  as unitCity,
			ee.Country  as unitCountry,
			CASE ee.areaLevel when '1' then '省级' when '2' then '市级' when '3' then '区县级' END as unitAreaLevel,
			-- 行政处罚案卷0、按日计罚1、移送行政拘留2、涉嫌犯罪3、申请法院强制执行4、查封扣押6、停产限产7
			CASE s.fileType when '0' then '行政案卷处罚' when '1' then '按日计罚' when '2' then '移送行政拘留' when '3' then '涉嫌犯罪' when '4' then '申请法院强制执行' when '6' then '查封扣押' when '7' then '停产限产' end as fileType,
			s.fileCode as fileCode,
			e.province as fileProvince,
			e.city as fileCity,
			e.Country as fileCountry,
			CASE s.type when '0' then '集体' when '1' then '个人' when '2' then '集体和个人' END as useFile,
			pe.name_pro as personal,
			pe.cardID_pro as cardID,
			pe.province_pro as personalProvince,
			pe.city_pro as personalCity,
			pe.country_pro as personalCountry,
			CASE s.recommendFiles when '1' then '推荐' ELSE '随机' END as recommendFiles,
			a.*
		from
			files as s
				left join T_area e on s.belongAreacode = e.code
				left join T_area ee on s.areaCode = ee.code
				left join Province_ElectionPersonal pe on pe.id = s.proID

				LEFT join (
				select
					llm.hhh as id,
					concat_ws(' ',concat('初评A:   ',llm.第一轮A评审依据),concat('初评B:   ',lls.评审依据),concat('复评A:   ',llq.第二轮A评审依据),concat('复评B:   ',llw.第二轮B评审依据)) as a,
					concat_ws('  ',concat('初评A:   ',llm.第一轮A评审依据1),concat('初评B:   ',lls.评审依据1),concat('复评A:   ',llq.第二轮A评审依据1),concat('复评B:   ',llw.第二轮B评审依据1)) as b,
					concat_ws('  ',concat('初评A:   ',llm.第一轮A评审依据2),concat('初评B:   ',lls.评审依据2),concat('复评A:   ',llq.第二轮A评审依据2),concat('复评B:   ',llw.第二轮B评审依据2)) as c,
					concat_ws('  ',concat('初评A:   ',llm.第一轮A评审依据3),concat('初评B:   ',lls.评审依据3),concat('复评A:   ',llq.第二轮A评审依据3),concat('复评B:   ',llw.第二轮B评审依据3)) as d,
					concat_ws('  ',concat('初评A:   ',llm.第一轮A评审依据4),concat('初评B:   ',lls.评审依据4),concat('复评A:   ',llq.第二轮A评审依据4),concat('复评B:   ',llw.第二轮B评审依据4)) as e,
					concat_ws('  ',concat('初评A:   ',llm.第一轮A评审依据5),concat('初评B:   ',lls.评审依据5),concat('复评A:   ',llq.第二轮A评审依据5),concat('复评B:   ',llw.第二轮B评审依据5)) as f,
					concat_ws('  ',concat('初评A:   ',llm.第一轮A评审依据6),concat('初评B:   ',lls.评审依据6),concat('复评A:   ',llq.第二轮A评审依据6),concat('复评B:   ',llw.第二轮B评审依据6)) as g,
					concat_ws('  ',concat('初评A:   ',llm.第一轮A评审依据7),concat('初评B:   ',lls.评审依据7),concat('复评A:   ',llq.第二轮A评审依据7),concat('复评B:   ',llw.第二轮B评审依据7)) as h,
					concat_ws('  ',concat('初评A:   ',llm.第一轮A评审依据8),concat('初评B:   ',lls.评审依据8),concat('复评A:   ',llq.第二轮A评审依据8),concat('复评B:   ',llw.第二轮B评审依据8)) as i,
					concat_ws('  ',concat('初评A:   ',llm.第一轮A评审依据9),concat('初评B:   ',lls.评审依据9),concat('复评A:   ',llq.第二轮A评审依据9),concat('复评B:   ',llw.第二轮B评审依据9)) as j,
					concat_ws('  ',concat('初评A:   ',llm.第一轮A评审依据10),concat('初评B:   ',lls.评审依据10),concat('复评A:   ',llq.第二轮A评审依据10),concat('复评B:   ',llw.第二轮B评审依据10)) as k,
					concat_ws('  ',concat('初评A:   ',llm.第一轮A评审依据11),concat('初评B:   ',lls.评审依据11),concat('复评A:   ',llq.第二轮A评审依据11),concat('复评B:   ',llw.第二轮B评审依据11)) as l,
					concat_ws('  ',concat('初评A:   ',llm.第一轮A评审依据12),concat('初评B:   ',lls.评审依据12),concat('复评A:   ',llq.第二轮A评审依据12),concat('复评B:   ',llw.第二轮B评审依据12)) as m,
					concat_ws('  ',concat('初评A:   ',llm.第一轮A评审依据13),concat('初评B:   ',lls.评审依据13),concat('复评A:   ',llq.第二轮A评审依据13),concat('复评B:   ',llw.第二轮B评审依据13)) as n,
					concat_ws('  ',concat('初评A:   ',llm.第一轮A评审依据14),concat('初评B:   ',lls.评审依据14),concat('复评A:   ',llq.第二轮A评审依据14),concat('复评B:   ',llw.第二轮B评审依据14)) as o,
					concat_ws('  ',concat('初评A:   ',llm.第一轮A评审依据15),concat('初评B:   ',lls.评审依据15),concat('复评A:   ',llq.第二轮A评审依据15),concat('复评B:   ',llw.第二轮B评审依据15)) as p,
					concat_ws('  ',concat('初评A:   ',llm.第一轮A评审依据16),concat('初评B:   ',lls.评审依据16),concat('复评A:   ',llq.第二轮A评审依据16),concat('复评B:   ',llw.第二轮B评审依据16)) as q,
					concat_ws('  ',concat('初评A:   ',llm.第一轮A评审依据17),concat('初评B:   ',lls.评审依据17),concat('复评A:   ',llq.第二轮A评审依据17),concat('复评B:   ',llw.第二轮B评审依据17)) as r,
					concat_ws('  ',concat('初评A:   ',llm.第一轮A评审依据18),concat('初评B:   ',lls.评审依据18),concat('复评A:   ',llq.第二轮A评审依据18),concat('复评B:   ',llw.第二轮B评审依据18)) as s,
					concat_ws('  ',concat('初评A:   ',llm.第一轮A评审依据19),concat('初评B:   ',lls.评审依据19),concat('复评A:   ',llq.第二轮A评审依据19),concat('复评B:   ',llw.第二轮B评审依据19)) as t,
					concat_ws('  ',concat('初评A:   ',llm.第一轮A评审依据20),concat('初评B:   ',lls.评审依据20),concat('复评A:   ',llq.第二轮A评审依据20),concat('复评B:   ',llw.第二轮B评审依据20)) as u,
					concat_ws('  ',concat('初评A:   ',llm.第一轮A评审依据21),concat('初评B:   ',lls.评审依据21),concat('复评A:   ',llq.第二轮A评审依据21),concat('复评B:   ',llw.第二轮B评审依据21)) as v,
					concat_ws('  ',concat('初评A:   ',llm.第一轮A评审依据22),concat('初评B:   ',lls.评审依据22),concat('复评A:   ',llq.第二轮A评审依据22),concat('复评B:   ',llw.第二轮B评审依据22)) as w,
					concat_ws('  ',concat('初评A:   ',llm.第一轮A评审依据23),concat('初评B:   ',lls.评审依据23),concat('复评A:   ',llq.第二轮A评审依据23),concat('复评B:   ',llw.第二轮B评审依据23)) as x,
					concat_ws('  ',concat('初评A:   ',llm.第一轮A评审依据24),concat('初评B:   ',lls.评审依据24),concat('复评A:   ',llq.第二轮A评审依据24),concat('复评B:   ',llw.第二轮B评审依据24)) as y,
					concat_ws('  ',concat('初评A:   ',llm.第一轮A评审依据25),concat('初评B:   ',lls.评审依据25),concat('复评A:   ',llq.第二轮A评审依据25),concat('复评B:   ',llw.第二轮B评审依据25)) as z,
					concat_ws('  ',concat('初评A:   ',llm.第一轮A评审依据26),concat('初评B:   ',lls.评审依据26),concat('复评A:   ',llq.第二轮A评审依据26),concat('复评B:   ',llw.第二轮B评审依据26)) as aa,
					concat_ws('  ',concat('初评A:   ',llm.第一轮A评审依据27),concat('初评B:   ',lls.评审依据27),concat('复评A:   ',llq.第二轮A评审依据27),concat('复评B:   ',llw.第二轮B评审依据27)) as bb,
					concat_ws('  ',concat('初评A:   ',llm.第一轮A评审依据28),concat('初评B:   ',lls.评审依据28),concat('复评A:   ',llq.第二轮A评审依据28),concat('复评B:   ',llw.第二轮B评审依据28)) as cc,
					concat_ws('  ',concat('初评A:   ',llm.第一轮A评审依据29),concat('初评B:   ',lls.评审依据29),concat('复评A:   ',llq.第二轮A评审依据29),concat('复评B:   ',llw.第二轮B评审依据29)) as dd,
					concat_ws('  ',concat('初评A:   ',llm.第一轮A评审依据30),concat('初评B:   ',lls.评审依据30),concat('复评A:   ',llq.第二轮A评审依据30),concat('复评B:   ',llw.第二轮B评审依据30)) as ee
				from(
						select
							kk.*,
							mm.hhh
						from (
								 select
									 *
								 FROM(
										 select *,
												MAX(CASE aa.itemId WHEN 827 THEN commeStr END) 第一轮A评审依据,
												MAX(CASE aa.itemId WHEN 828 THEN commeStr END) 第一轮A评审依据1,
												MAX(CASE aa.itemId WHEN 830 THEN commeStr END) 第一轮A评审依据2,
												MAX(CASE aa.itemId WHEN 831 THEN commeStr END) 第一轮A评审依据3,
												MAX(CASE aa.itemId WHEN 835 THEN commeStr END) 第一轮A评审依据4,
												MAX(CASE aa.itemId WHEN 836 THEN commeStr END) 第一轮A评审依据5,
												MAX(CASE aa.itemId WHEN 837 THEN commeStr END) 第一轮A评审依据6,
												MAX(CASE aa.itemId WHEN 838 THEN commeStr END) 第一轮A评审依据7,
												MAX(CASE aa.itemId WHEN 1337 THEN commeStr END) 第一轮A评审依据8,
												MAX(CASE aa.itemId WHEN 839 THEN commeStr END) 第一轮A评审依据9,
												MAX(CASE aa.itemId WHEN 840 THEN commeStr END) 第一轮A评审依据10,
												MAX(CASE aa.itemId WHEN 841 THEN commeStr END) 第一轮A评审依据11,
												MAX(CASE aa.itemId WHEN 842 THEN commeStr END) 第一轮A评审依据12,
												MAX(CASE aa.itemId WHEN 843 THEN commeStr END) 第一轮A评审依据13,
												MAX(CASE aa.itemId WHEN 844 THEN commeStr END) 第一轮A评审依据14,
												MAX(CASE aa.itemId WHEN 1333 THEN commeStr END) 第一轮A评审依据15,
												MAX(CASE aa.itemId WHEN 1335 THEN commeStr END) 第一轮A评审依据16,
												MAX(CASE aa.itemId WHEN 845 THEN commeStr END) 第一轮A评审依据17,
												MAX(CASE aa.itemId WHEN 846 THEN commeStr END) 第一轮A评审依据18,
												MAX(CASE aa.itemId WHEN 847 THEN commeStr END) 第一轮A评审依据19,
												MAX(CASE aa.itemId WHEN 848 THEN commeStr END) 第一轮A评审依据20,
												MAX(CASE aa.itemId WHEN 849 THEN commeStr END) 第一轮A评审依据21,
												MAX(CASE aa.itemId WHEN 850 THEN commeStr END) 第一轮A评审依据22,
												MAX(CASE aa.itemId WHEN 851 THEN commeStr END) 第一轮A评审依据23,
												MAX(CASE aa.itemId WHEN 852 THEN commeStr END) 第一轮A评审依据24,
												MAX(CASE aa.itemId WHEN 853 THEN commeStr END) 第一轮A评审依据25,
												MAX(CASE aa.itemId WHEN 1038 THEN commeStr END) 第一轮A评审依据26,
												MAX(CASE aa.itemId WHEN 1331 THEN commeStr END) 第一轮A评审依据27,
												MAX(CASE aa.itemId WHEN 1332 THEN commeStr END) 第一轮A评审依据28,
												MAX(CASE aa.itemId WHEN 1338 THEN commeStr END) 第一轮A评审依据29,
												MAX(CASE aa.itemId WHEN 1339 THEN commeStr END) 第一轮A评审依据30
										 FROM (
												  SELECT
													  a.areaCode,
													  a.id,
													  a.oldId,
													  a.expertConsiderScore,
													  a.fileType,
													  a.fileCode,
													  b.expertFinalScore,
													  b.expertName,
													  b.ajpjYxdxanlituijianCode,
													  b.ajpjYxdxanlituijianName,
													  b.jcajtuijianCode,
													  b.jcajtuijianName,
													  c.indexId,
													  d.itemId,
													  c.resultScore,
													  d.commeStr,
													  b.fileMaterials,
													  a.expertconsiderscore2,
													  d.expertIndexId
												  FROM
													  files a
												  right JOIN expertHandlFileList b ON a.expertAId = b.expertId AND a.id = b.fileId
												  JOIN expertHandlIndexScore c ON b.id = c.expertHandlId AND c.scoreType = 1 AND c.resultScore = 1
												  JOIN expertHandlItemScore d ON c.id = d.expertIndexId AND d.score = 1
											  ) AS aa GROUP BY aa.id ORDER BY aa.id ASC
									 )as kkk
								 GROUP BY kkk.expertIndexId ORDER BY kkk.id ASC ) as kk
								 right JOIN ( select a.id as hhh,b.id as k FROM files a JOIN expertHandlFileList b ON a.expertAId = b.expertId AND a.id = b.fileId ) as mm ON kk.id = mm.hhh
						) as llm
						LEFT JOIN (
					select
						*
					from(
							select
								kk.*
							from (
									 select
										 *
									 FROM(
											 select
												 *,
												 MAX(CASE aa.itemId WHEN 827 THEN commeStr END) 评审依据,
												 MAX(CASE aa.itemId WHEN 828 THEN commeStr END) 评审依据1,
												 MAX(CASE aa.itemId WHEN 830 THEN commeStr END) 评审依据2,
												 MAX(CASE aa.itemId WHEN 831 THEN commeStr END) 评审依据3,
												 MAX(CASE aa.itemId WHEN 835 THEN commeStr END) 评审依据4,
												 MAX(CASE aa.itemId WHEN 836 THEN commeStr END) 评审依据5,
												 MAX(CASE aa.itemId WHEN 837 THEN commeStr END) 评审依据6,
												 MAX(CASE aa.itemId WHEN 838 THEN commeStr END) 评审依据7,
												 MAX(CASE aa.itemId WHEN 1337 THEN commeStr END) 评审依据8,
												 MAX(CASE aa.itemId WHEN 839 THEN commeStr END) 评审依据9,
												 MAX(CASE aa.itemId WHEN 840 THEN commeStr END) 评审依据10,
												 MAX(CASE aa.itemId WHEN 841 THEN commeStr END) 评审依据11,
												 MAX(CASE aa.itemId WHEN 842 THEN commeStr END) 评审依据12,
												 MAX(CASE aa.itemId WHEN 843 THEN commeStr END) 评审依据13,
												 MAX(CASE aa.itemId WHEN 844 THEN commeStr END) 评审依据14,
												 MAX(CASE aa.itemId WHEN 1333 THEN commeStr END) 评审依据15,
												 MAX(CASE aa.itemId WHEN 1335 THEN commeStr END) 评审依据16,
												 MAX(CASE aa.itemId WHEN 845 THEN commeStr END) 评审依据17,
												 MAX(CASE aa.itemId WHEN 846 THEN commeStr END) 评审依据18,
												 MAX(CASE aa.itemId WHEN 847 THEN commeStr END) 评审依据19,
												 MAX(CASE aa.itemId WHEN 848 THEN commeStr END) 评审依据20,
												 MAX(CASE aa.itemId WHEN 849 THEN commeStr END) 评审依据21,
												 MAX(CASE aa.itemId WHEN 850 THEN commeStr END) 评审依据22,
												 MAX(CASE aa.itemId WHEN 851 THEN commeStr END) 评审依据23,
												 MAX(CASE aa.itemId WHEN 852 THEN commeStr END) 评审依据24,
												 MAX(CASE aa.itemId WHEN 853 THEN commeStr END) 评审依据25,
												 MAX(CASE aa.itemId WHEN 1038 THEN commeStr END) 评审依据26,
												 MAX(CASE aa.itemId WHEN 1331 THEN commeStr END) 评审依据27,
												 MAX(CASE aa.itemId WHEN 1332 THEN commeStr END) 评审依据28,
												 MAX(CASE aa.itemId WHEN 1338 THEN commeStr END) 评审依据29,
												 MAX(CASE aa.itemId WHEN 1339 THEN commeStr END) 评审依据30
											 FROM (
													  SELECT
														  a.areaCode,
														  a.id,
														  a.oldId,
														  a.expertConsiderScore,
														  a.fileType,
														  a.fileCode,
														  b.expertFinalScore,
														  b.expertName,
														  b.ajpjYxdxanlituijianCode,
														  b.ajpjYxdxanlituijianName,
														  b.jcajtuijianCode,
														  b.jcajtuijianName,
														  c.indexId,
														  d.itemId,
														  c.resultScore,
														  d.commeStr,
														  b.fileMaterials,
														  a.expertconsiderscore2,
														  d.expertIndexId
													  FROM
														  files a
															  right JOIN expertHandlFileList b ON a.expertBId = b.expertId AND a.id = b.fileId
															  JOIN expertHandlIndexScore c ON b.id = c.expertHandlId AND c.scoreType = 1 AND c.resultScore = 1
															  JOIN expertHandlItemScore d ON c.id = d.expertIndexId AND d.score = 1
												  ) AS aa
											 GROUP BY aa.id ORDER BY aa.id ASC)as kkk
									 GROUP BY kkk.expertIndexId
									 ORDER BY kkk.id ASC ) as kk
									 right JOIN ( select a.id, b.id as k FROM files a JOIN expertHandlFileList b ON a.expertBId = b.expertId AND a.id = b.fileId ) as mm ON kk.id = mm.id
							) as lla
				) lls ON llm.hhh = lls.id
						LEFT JOIN (
					select
						*
					from
						(
							select
								kk.*
							from (
									 select
										 *
									 FROM
										 (
											 select
												 *,
												 MAX(CASE aa.itemId WHEN 827 THEN commeStr END) 第二轮A评审依据,
												 MAX(CASE aa.itemId WHEN 828 THEN commeStr END) 第二轮A评审依据1,
												 MAX(CASE aa.itemId WHEN 830 THEN commeStr END) 第二轮A评审依据2,
												 MAX(CASE aa.itemId WHEN 831 THEN commeStr END) 第二轮A评审依据3,
												 MAX(CASE aa.itemId WHEN 835 THEN commeStr END) 第二轮A评审依据4,
												 MAX(CASE aa.itemId WHEN 836 THEN commeStr END) 第二轮A评审依据5,
												 MAX(CASE aa.itemId WHEN 837 THEN commeStr END) 第二轮A评审依据6,
												 MAX(CASE aa.itemId WHEN 838 THEN commeStr END) 第二轮A评审依据7,
												 MAX(CASE aa.itemId WHEN 1337 THEN commeStr END) 第二轮A评审依据8,
												 MAX(CASE aa.itemId WHEN 839 THEN commeStr END) 第二轮A评审依据9,
												 MAX(CASE aa.itemId WHEN 840 THEN commeStr END) 第二轮A评审依据10,
												 MAX(CASE aa.itemId WHEN 841 THEN commeStr END) 第二轮A评审依据11,
												 MAX(CASE aa.itemId WHEN 842 THEN commeStr END) 第二轮A评审依据12,
												 MAX(CASE aa.itemId WHEN 843 THEN commeStr END) 第二轮A评审依据13,
												 MAX(CASE aa.itemId WHEN 844 THEN commeStr END) 第二轮A评审依据14,
												 MAX(CASE aa.itemId WHEN 1333 THEN commeStr END) 第二轮A评审依据15,
												 MAX(CASE aa.itemId WHEN 1335 THEN commeStr END) 第二轮A评审依据16,
												 MAX(CASE aa.itemId WHEN 845 THEN commeStr END) 第二轮A评审依据17,
												 MAX(CASE aa.itemId WHEN 846 THEN commeStr END) 第二轮A评审依据18,
												 MAX(CASE aa.itemId WHEN 847 THEN commeStr END) 第二轮A评审依据19,
												 MAX(CASE aa.itemId WHEN 848 THEN commeStr END) 第二轮A评审依据20,
												 MAX(CASE aa.itemId WHEN 849 THEN commeStr END) 第二轮A评审依据21,
												 MAX(CASE aa.itemId WHEN 850 THEN commeStr END) 第二轮A评审依据22,
												 MAX(CASE aa.itemId WHEN 851 THEN commeStr END) 第二轮A评审依据23,
												 MAX(CASE aa.itemId WHEN 852 THEN commeStr END) 第二轮A评审依据24,
												 MAX(CASE aa.itemId WHEN 853 THEN commeStr END) 第二轮A评审依据25,
												 MAX(CASE aa.itemId WHEN 1038 THEN commeStr END) 第二轮A评审依据26,
												 MAX(CASE aa.itemId WHEN 1331 THEN commeStr END) 第二轮A评审依据27,
												 MAX(CASE aa.itemId WHEN 1332 THEN commeStr END) 第二轮A评审依据28,
												 MAX(CASE aa.itemId WHEN 1338 THEN commeStr END) 第二轮A评审依据29,
												 MAX(CASE aa.itemId WHEN 1339 THEN commeStr END) 第二轮A评审依据30
											 FROM
												 (
													 SELECT
														 a.areaCode,
														 a.id,
														 a.oldId,
														 a.expertConsiderScore,
														 a.fileType,
														 a.fileCode,
														 b.expertFinalScore,
														 b.expertName,
														 b.ajpjYxdxanlituijianCode,
														 b.ajpjYxdxanlituijianName,
														 b.jcajtuijianCode,
														 b.jcajtuijianName,
														 c.indexId,
														 d.itemId,
														 c.resultScore,
														 d.commeStr,
														 b.fileMaterials,
														 a.expertconsiderscore2,
														 d.expertIndexId
													 FROM
														 files a
															 right JOIN expertHandlFileList b ON a.expertCommitAId = b.expertId AND a.id = b.fileId
															 JOIN expertHandlIndexScore c ON b.id = c.expertHandlId AND c.scoreType = 1 AND c.resultScore = 1
															 JOIN expertHandlItemScore d ON c.id = d.expertIndexId AND d.score = 1
												 ) AS aa
											 GROUP BY aa.id
											 ORDER BY aa.id ASC)as kkk
									 GROUP BY kkk.expertIndexId
									 ORDER BY kkk.id ASC ) as kk
									 right JOIN   (select a.id, b.id as k FROM files a JOIN expertHandlFileList b ON a.expertCommitAId = b.expertId AND a.id = b.fileId ) as mm ON kk.id = mm.id
							) as lla) llq ON llm.hhh = llq.id
						LEFT JOIN (
					select
						*
					from
						(
							select
								kk.*
							from (
									 select
										 *
									 FROM
										 (
											 select
												 *,
												 MAX(CASE aa.itemId WHEN 827 THEN commeStr END) 第二轮B评审依据,
												 MAX(CASE aa.itemId WHEN 828 THEN commeStr END) 第二轮B评审依据1,
												 MAX(CASE aa.itemId WHEN 830 THEN commeStr END) 第二轮B评审依据2,
												 MAX(CASE aa.itemId WHEN 831 THEN commeStr END) 第二轮B评审依据3,
												 MAX(CASE aa.itemId WHEN 835 THEN commeStr END) 第二轮B评审依据4,
												 MAX(CASE aa.itemId WHEN 836 THEN commeStr END) 第二轮B评审依据5,
												 MAX(CASE aa.itemId WHEN 837 THEN commeStr END) 第二轮B评审依据6,
												 MAX(CASE aa.itemId WHEN 838 THEN commeStr END) 第二轮B评审依据7,
												 MAX(CASE aa.itemId WHEN 1337 THEN commeStr END) 第二轮B评审依据8,
												 MAX(CASE aa.itemId WHEN 839 THEN commeStr END) 第二轮B评审依据9,
												 MAX(CASE aa.itemId WHEN 840 THEN commeStr END) 第二轮B评审依据10,
												 MAX(CASE aa.itemId WHEN 841 THEN commeStr END) 第二轮B评审依据11,
												 MAX(CASE aa.itemId WHEN 842 THEN commeStr END) 第二轮B评审依据12,
												 MAX(CASE aa.itemId WHEN 843 THEN commeStr END) 第二轮B评审依据13,
												 MAX(CASE aa.itemId WHEN 844 THEN commeStr END) 第二轮B评审依据14,
												 MAX(CASE aa.itemId WHEN 1333 THEN commeStr END) 第二轮B评审依据15,
												 MAX(CASE aa.itemId WHEN 1335 THEN commeStr END) 第二轮B评审依据16,
												 MAX(CASE aa.itemId WHEN 845 THEN commeStr END) 第二轮B评审依据17,
												 MAX(CASE aa.itemId WHEN 846 THEN commeStr END) 第二轮B评审依据18,
												 MAX(CASE aa.itemId WHEN 847 THEN commeStr END) 第二轮B评审依据19,
												 MAX(CASE aa.itemId WHEN 848 THEN commeStr END) 第二轮B评审依据20,
												 MAX(CASE aa.itemId WHEN 849 THEN commeStr END) 第二轮B评审依据21,
												 MAX(CASE aa.itemId WHEN 850 THEN commeStr END) 第二轮B评审依据22,
												 MAX(CASE aa.itemId WHEN 851 THEN commeStr END) 第二轮B评审依据23,
												 MAX(CASE aa.itemId WHEN 852 THEN commeStr END) 第二轮B评审依据24,
												 MAX(CASE aa.itemId WHEN 853 THEN commeStr END) 第二轮B评审依据25,
												 MAX(CASE aa.itemId WHEN 1038 THEN commeStr END) 第二轮B评审依据26,
												 MAX(CASE aa.itemId WHEN 1331 THEN commeStr END) 第二轮B评审依据27,
												 MAX(CASE aa.itemId WHEN 1332 THEN commeStr END) 第二轮B评审依据28,
												 MAX(CASE aa.itemId WHEN 1338 THEN commeStr END) 第二轮B评审依据29,
												 MAX(CASE aa.itemId WHEN 1339 THEN commeStr END) 第二轮B评审依据30
											 FROM
												 (
													 SELECT
														 a.areaCode,
														 a.id,
														 a.oldId,
														 a.expertConsiderScore,
														 a.fileType,
														 a.fileCode,
														 b.expertFinalScore,
														 b.expertName,
														 b.ajpjYxdxanlituijianCode,
														 b.ajpjYxdxanlituijianName,
														 b.jcajtuijianCode,
														 b.jcajtuijianName,
														 c.indexId,
														 d.itemId,
														 c.resultScore,
														 d.commeStr,
														 b.fileMaterials,
														 a.expertconsiderscore2,
														 d.expertIndexId
													 FROM
														 files a
															 right JOIN expertHandlFileList b ON a.expertCommitBId = b.expertId AND a.id = b.fileId
															 JOIN expertHandlIndexScore c ON b.id = c.expertHandlId AND c.scoreType = 1 AND c.resultScore = 1
															 JOIN expertHandlItemScore d ON c.id = d.expertIndexId AND d.score = 1
												 ) AS aa
											 GROUP BY aa.id
											 ORDER BY aa.id ASC)as kkk
									 GROUP BY kkk.expertIndexId
									 ORDER BY kkk.id ASC ) as kk
									 right JOIN   (select a.id, b.id as k FROM files a JOIN expertHandlFileList b ON a.expertCommitBId = b.expertId AND a.id = b.fileId ) as mm ON kk.id = mm.id
							) as lla) llw ON llm.hhh = llw.id
				ORDER BY llm.hhh ASC
			) a on a.id = s.id

		ORDER BY s.id

	</select>
	<!--根据案件ID查询案件基本信息-->
	<select id="getFileInfo" resultType="com.changneng.sa.bean.FilesEntityVetoInfoVo">
		select
		id as filesId,
		filesIssues as filesIssues,
		fileCode as fileCode
		from
		files
		<where>
			id = #{filesId}
		</where>
	</select>
</mapper>
