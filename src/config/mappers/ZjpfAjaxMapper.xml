<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.changneng.sa.dao.ZjpfAjaxMapper">
 <resultMap id="BaseResultMap" type="com.changneng.sa.bean.ExpertFileList">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="expertId" jdbcType="INTEGER" property="expertid" />
    <result column="expertName" jdbcType="VARCHAR" property="expertname" />
    <result column="fileId" jdbcType="INTEGER" property="fileid" />
    <result column="fileCode" jdbcType="VARCHAR" property="filecode" />
    <result column="scoredState" jdbcType="VARCHAR" property="scoredstate" />
    <result column="yxdxAnLiTuiJian" jdbcType="VARCHAR" property="yxdxanlituijian" />
    <result column="expertFinalScore" jdbcType="REAL" property="expertfinalscore" />

    <!-- 2018-11-28 新增专家是否评价字段 -->
    <result column="ajpjYxdxanlituijian" jdbcType="VARCHAR" property="ajpjYxdxanlituijian" />

    <result column="paperScore" jdbcType="REAL" property="paperscore" />
    <result column="entityScore" jdbcType="REAL" property="entityscore" />
    <result column="zeroScoreFileReviews" jdbcType="LONGVARCHAR" property="zeroScoreFileReviews" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.changneng.sa.bean.ExpertHandlFileListWithBLOBs">
    <result column="yxdxAnLiTuiJianReviews" jdbcType="LONGVARCHAR" property="yxdxanlituijianreviews" />
    <result column="ExpertReviews" jdbcType="LONGVARCHAR" property="expertreviews" />

      <!--  2024-06-04  新增字段 案卷不全缺少xx页、案卷不全缺少xx文书、案卷不全其他情况补充-->
      <result column="fileMaterialsNums" property="fileMaterialsNums" jdbcType="VARCHAR"/>
      <result column="fileMaterialsDocs" property="fileMaterialsDocs" jdbcType="VARCHAR"/>
      <result column="fileMaterialsSups" property="fileMaterialsSups" jdbcType="VARCHAR"/>

      <!--  2021-10-26  新增字段 优质案卷选项-字典表 CODE NAME -->
      <result column="ajpjYxdxanlituijianCode"  jdbcType="LONGVARCHAR" property="ajpjYxdxanlituijianCode" />
      <result column="ajpjYxdxanlituijianName" jdbcType="LONGVARCHAR" property="ajpjYxdxanlituijianName" />

      <!--  2021-10-26  新增字段 较差案卷选项-字典表 CODE NAME -->
      <result column="jcajtuijianCode"  property="jcajtuijianCode" />
      <result column="jcajtuijianName" property="jcajtuijianName" />
      <result column="fileMaterials" property="fileMaterials" jdbcType="VARCHAR"/>
      <result column="noTuiJian" property="noTuiJian" jdbcType="VARCHAR"/>
      <!-- 2018-11-28 新增专家评价内容字段 -->
      <result column="ajpjYxdxanlituijianreviews" jdbcType="LONGVARCHAR" property="ajpjYxdxanlituijianreviews" />
      <result column="handlType"  property="handlType" />
      <result column="expertId"  property="expertId" />
      <result column="caseCheckState"  property="caseCheckState" />
      <result column="isAiReview" property="isAiReview" />
      <result column="isAiView" property="isAiView" />
      <result column="isEntityAiView" property="isEntityAiView" />
  </resultMap>
  <sql id="Base_Column_List">
    id, expertId, expertName, fileId, fileCode, scoredState, yxdxAnLiTuiJian, expertFinalScore, ajpjYxdxanlituijian,
    paperScore,entityScore,zeroScoreFileReviews,problemRemark,noTuiJian,handlType,expertId,fileMaterialsMsg,isDispute
  </sql>
  <sql id="Blob_Column_List">
    yxdxAnLiTuiJianReviews,ExpertReviews, ajpjYxdxanlituijianreviews,ajpjYxdxanlituijianCode,ajpjYxdxanlituijianName,fileMaterials,noTuiJian,entityState,fileMaterialsNums,fileMaterialsDocs,fileMaterialsSups,isAiReview
  </sql>

  <update id="updateByPrimaryKeySelective" parameterType="com.changneng.sa.bean.ExpertHandlFileListWithBLOBs">
    update expertHandlFileList
    <set>
      <if test="expertid != null">
        expertId = #{expertid,jdbcType=INTEGER},
      </if>
      <if test="expertname != null">
        expertName = #{expertname,jdbcType=VARCHAR},
      </if>
      <if test="fileid != null">
        fileId = #{fileid,jdbcType=INTEGER},
      </if>
      <if test="filecode != null">
        fileCode = #{filecode,jdbcType=VARCHAR},
      </if>
      <if test="scoredstate != null">
        scoredState = #{scoredstate,jdbcType=VARCHAR},
      </if>
      <if test="yxdxanlituijian != null">
        yxdxAnLiTuiJian = #{yxdxanlituijian,jdbcType=VARCHAR},
      </if>
      <if test="ajpjYxdxanlituijian != null">
        ajpjYxdxanlituijian = #{ajpjYxdxanlituijian,jdbcType=VARCHAR},
      </if>
      <if test="expertfinalscore != null">
        expertFinalScore = #{expertfinalscore,jdbcType=REAL},
      </if>
      <if test="yxdxanlituijianreviews != null">
        yxdxAnLiTuiJianReviews = #{yxdxanlituijianreviews,jdbcType=LONGVARCHAR},
      </if>
      <if test="ajpjYxdxanlituijianreviews != null">
        ajpjYxdxanlituijianreviews = #{ajpjYxdxanlituijianreviews,jdbcType=LONGVARCHAR},
      </if>
      <if test="expertreviews != null">
        ExpertReviews = #{expertreviews,jdbcType=LONGVARCHAR},
      </if>
      <if test="paperscore != null">
        paperScore = #{paperscore,jdbcType=REAL},
      </if>
      <if test="entityscore != null">
        entityScore = #{entityscore,jdbcType=REAL},
      </if>
      <if test="zeroScoreFileReviews != null">
        zeroScoreFileReviews = #{zeroScoreFileReviews,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from expertHandlFileList
    where id = #{id,jdbcType=INTEGER}
  </select>

  <!--  专家评审案卷 - 评审案卷列表 -->
  <select id="getExpertHandList"  resultType="com.changneng.sa.bean.ExpertHandUnit_ZJPF_zjpfList">
		  SELECT
		  e.id,
		  e.fileCode,
		  e.expertFinalScore,
		  e.scoredState,
		  e.fileId,
		  e.expertId,
		  e.considerState,
		  e.paperScore,
		  e.entityScore,
		  e.zeroScoreFileReviews,
		  e.enabled,
          e.errorState,
          e.expertName,
          f.fileType,
            CASE e.ajpjYxdxanlituijianCode
                WHEN '1' THEN '是'
                WHEN '0' THEN '否'
                ELSE '-'
            end as   ajpjYxdxanlituijianCode,
          CASE f.fileType
              when '0' then '行政处罚案卷'
              when '1' then '按日计罚'
              when '2' then '移送行政拘留'
              when '3' then '涉嫌犯罪'
              when '6' then '查封扣押'
              when '7' then '停产限产'
              when '9' then '不予处罚'
          end as fileTypeName,
          (SELECT CASE WHEN COUNT(*) > 0 THEN '是' ELSE '否' END
           FROM experthandlindexscore eis
           INNER JOIN experthandlitemscore eits ON eis.id = eits.expertIndexId
           WHERE eis.expertHandlId = e.id AND eis.scoreType = 2 AND eits.score = 1) as hasPlusItems,
          (SELECT GROUP_CONCAT(DISTINCT si.indexName SEPARATOR ',')
           FROM experthandlindexscore eis
           INNER JOIN experthandlitemscore eits ON eis.id = eits.expertIndexId
           INNER JOIN scoringindex si ON eis.indexId = si.id
           WHERE eis.expertHandlId = e.id AND eis.scoreType = 2 AND eits.score = 1) as plusItemTypes
		  FROM expertHandlFileList e
		  left join files f on e.fileId = f.id
		  where
		  e.expertId = #{expertHandListVo.expertId}
	 	<if test="expertHandListVo.fileCode != null and expertHandListVo.fileCode != ''" >
        	 and  e.fileCode like CONCAT('%','${expertHandListVo.fileCode}','%')
	    </if>
	    <if test="expertHandListVo.scoredState != null and expertHandListVo.scoredState != ''" >
        	and e.scoredState = #{expertHandListVo.scoredState}
	    </if>
	    ORDER BY enabled DESC,(case when scoredState=1 then 99 when scoredState=0 then 98 else scoredState end),e.expertFinalScore DESC,fileId
  </select>

  <select id="getExpFilesNum" resultType="int">
   select count(*) from files
   where fileType!=5 and  expertAId is not null  and  expertBId is not null
  </select>



  <select id="getExpertHandlFileListWithBLOBsByFileTypeAndId" resultMap="ResultMapWithBLOBs">
  	select
     <include refid="Base_Column_List" />
   	 ,
    <include refid="Blob_Column_List" />
   	 from expertHandlFileList
   	 where fileId  =#{fileId}  and  expertId = #{expertId}
  </select>

  <select id="selectexpertHandlFileById" resultMap="ResultMapWithBLOBs">
  	select
    cf.id, cf.expertId, cf.fileId, cf.fileCode, cf.scoredState, cf.paperScore,cf.entityScore,
    cf.expertFinalScore,cf.ExpertReviews,f.fileurl,f.filename,f.filetype,f.closed,f.caseCheckState,
    cf.yxdxAnLiTuiJianReviews,cf.ajpjYxdxanlituijianreviews,cf.yxdxAnLiTuiJian,cf.ajpjYxdxanlituijian,cf.zeroScoreFileReviews,cf.problemRemark,
    cf.ajpjYxdxanlituijianCode,cf.ajpjYxdxanlituijianName,cf.jcajtuijianCode,cf.jcajtuijianName,cf.noTuiJian,cf.isAiReview,cf.isAiView,cf.isEntityAiView,
    cf.
    from
        expertHandlFileList cf
    left join files f on cf.fileId = f.id
    where cf.id = #{id}
  </select>
  <select id="selectExpertID" resultType="java.lang.Integer" parameterType="java.util.Map">
	select id from expertHandlFileList
	<where>
		<if test="expertID!=null">
			expertId = #{expertID}
		</if>
		<if test="fileID!=null">
			and	fileId = #{fileID}
		</if>
	</where>
  </select>
  <!-- 根据本轮的案卷分配ID查询上一轮打分的两人打分id -->
<!-- 2020-7-14 根据本轮的案卷分配ID查询上一轮打分的两人打分id    -->
  <select id="getHistoryExpertHandID" resultMap="getHistoryExpertHand">
  	select id,problemRemark,paperScore
  	from expertHandlFileList
  	where
  		fileId in(select fileId from expertHandlFileList where id=#{expertHantId})
  		and handlType=#{handType}
  	order by id
  </select>
    <select id="getOtherExpertScore" resultType="com.changneng.sa.bean.ExpertHandlIndexScore">
        select b.* from expertHandlFileList a
        inner join expertHandlIndexScore b on a.id=b.expertHandlId
        where a.fileId=#{fileId} and a.handlType=#{handlType} and b.indexId=#{indexId} and a.expertId !=#{expertId}
    </select>
    <resultMap id="getHistoryExpertHand" type="com.changneng.sa.bean.ExpertHandlFileList">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="problemRemark" jdbcType="VARCHAR" property="problemRemark" />
  </resultMap>
    <update id="updateAiView" >
        update  expertHandlFileList set isAiView = '1',isEntityAiView = '1'
            where id = #{id}
    </update>

    <update id="updateEntityAiView" >
        update  expertHandlFileList set isEntityAiView = "1"
            where id = #{id}
    </update>
</mapper>
