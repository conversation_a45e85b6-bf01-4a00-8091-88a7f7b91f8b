################################################################################ 
#â éç½®æ ¹Loggerï¼å¶è¯­æ³ä¸ºï¼ 
# 
#log4j.rootLogger = [level],appenderName,appenderName2,... 
#levelæ¯æ¥å¿è®°å½çä¼åçº§ï¼åä¸ºOFF,TRACE,DEBUG,INFO,WARN,ERROR,FATAL,ALL 
##Log4jå»ºè®®åªä½¿ç¨åä¸ªçº§å«ï¼ä¼åçº§ä»ä½å°é«åå«æ¯DEBUG,INFO,WARN,ERROR 
#éè¿å¨è¿éå®ä¹ççº§å«ï¼æ¨å¯ä»¥æ§å¶å°åºç¨ç¨åºä¸­ç¸åºçº§å«çæ¥å¿ä¿¡æ¯çå¼å³ 
#æ¯å¦å¨è¿éå®ä¹äºINFOçº§å«ï¼ååºç¨ç¨åºä¸­ææDEBUGçº§å«çæ¥å¿ä¿¡æ¯å°ä¸è¢«æå°åºæ¥ 
#appenderNameå°±æ¯æå®æ¥å¿ä¿¡æ¯è¾åºå°åªä¸ªå°æ¹ãå¯åæ¶æå®å¤ä¸ªè¾åºç®ç 
################################################################################ 
################################################################################ 
#â¡éç½®æ¥å¿ä¿¡æ¯è¾åºç®çå°Appenderï¼å¶è¯­æ³ä¸ºï¼ 
# 
#log4j.appender.appenderName = fully.qualified.name.of.appender.class 
#log4j.appender.appenderName.optionN = valueN 
# 
#Log4jæä¾çappenderæä»¥ä¸å ç§ï¼ 
#1)org.apache.log4j.ConsoleAppender(è¾åºå°æ§å¶å°) 
#2)org.apache.log4j.FileAppender(è¾åºå°æä»¶) 
#3)org.apache.log4j.DailyRollingFileAppender(æ¯å¤©äº§çä¸ä¸ªæ¥å¿æä»¶) 
#4)org.apache.log4j.RollingFileAppender(æä»¶å¤§å°å°è¾¾æå®å°ºå¯¸çæ¶åäº§çä¸ä¸ªæ°çæä»¶) 
#5)org.apache.log4j.WriterAppender(å°æ¥å¿ä¿¡æ¯ä»¥æµæ ¼å¼åéå°ä»»ææå®çå°æ¹) 
# 
#1)ConsoleAppenderéé¡¹å±æ§ 
# -Threshold = DEBUG:æå®æ¥å¿æ¶æ¯çè¾åºæä½å±æ¬¡ 
# -ImmediateFlush = TRUE:é»è®¤å¼æ¯true,ææçæ¶æ¯é½ä¼è¢«ç«å³è¾åº 
# -Target = System.err:é»è®¤å¼System.out,è¾åºå°æ§å¶å°(errä¸ºçº¢è²,outä¸ºé»è²) 
# 
#2)FileAppenderéé¡¹å±æ§ 
# -Threshold = INFO:æå®æ¥å¿æ¶æ¯çè¾åºæä½å±æ¬¡ 
# -ImmediateFlush = TRUE:é»è®¤å¼æ¯true,ææçæ¶æ¯é½ä¼è¢«ç«å³è¾åº 
# -File = C:\log4j.log:æå®æ¶æ¯è¾åºå°C:\log4j.logæä»¶ 
# -Append = FALSE:é»è®¤å¼true,å°æ¶æ¯è¿½å å°æå®æä»¶ä¸­ï¼falseæå°æ¶æ¯è¦çæå®çæä»¶åå®¹ 
# -Encoding = UTF-8:å¯ä»¥æå®æä»¶ç¼ç æ ¼å¼ 
# 
#3)DailyRollingFileAppenderéé¡¹å±æ§ 
# -Threshold = WARN:æå®æ¥å¿æ¶æ¯çè¾åºæä½å±æ¬¡ 
# -ImmediateFlush = TRUE:é»è®¤å¼æ¯true,ææçæ¶æ¯é½ä¼è¢«ç«å³è¾åº 
# -File = C:\log4j.log:æå®æ¶æ¯è¾åºå°C:\log4j.logæä»¶ 
# -Append = FALSE:é»è®¤å¼true,å°æ¶æ¯è¿½å å°æå®æä»¶ä¸­ï¼falseæå°æ¶æ¯è¦çæå®çæä»¶åå®¹ 
# -DatePattern='.'yyyy-ww:æ¯å¨æ»å¨ä¸æ¬¡æä»¶,å³æ¯å¨äº§çä¸ä¸ªæ°çæä»¶ãè¿å¯ä»¥æç¨ä»¥ä¸åæ°: 
#              '.'yyyy-MM:æ¯æ 
#              '.'yyyy-ww:æ¯å¨ 
#              '.'yyyy-MM-dd:æ¯å¤© 
#              '.'yyyy-MM-dd-a:æ¯å¤©ä¸¤æ¬¡ 
#              '.'yyyy-MM-dd-HH:æ¯å°æ¶ 
#              '.'yyyy-MM-dd-HH-mm:æ¯åé 
# -Encoding = UTF-8:å¯ä»¥æå®æä»¶ç¼ç æ ¼å¼ 
# 
#4)RollingFileAppenderéé¡¹å±æ§ 
# -Threshold = ERROR:æå®æ¥å¿æ¶æ¯çè¾åºæä½å±æ¬¡ 
# -ImmediateFlush = TRUE:é»è®¤å¼æ¯true,ææçæ¶æ¯é½ä¼è¢«ç«å³è¾åº 
# -File = C:/log4j.log:æå®æ¶æ¯è¾åºå°C:/log4j.logæä»¶ 
# -Append = FALSE:é»è®¤å¼true,å°æ¶æ¯è¿½å å°æå®æä»¶ä¸­ï¼falseæå°æ¶æ¯è¦çæå®çæä»¶åå®¹ 
# -MaxFileSize = 100KB:åç¼å¯ä»¥æ¯KB,MB,GB.å¨æ¥å¿æä»¶å°è¾¾è¯¥å¤§å°æ¶,å°ä¼èªå¨æ»å¨.å¦:log4j.log.1 
# -MaxBackupIndex = 2:æå®å¯ä»¥äº§ççæ»å¨æä»¶çæå¤§æ° 
# -Encoding = UTF-8:å¯ä»¥æå®æä»¶ç¼ç æ ¼å¼ 
################################################################################ 
################################################################################ 
#â¢éç½®æ¥å¿ä¿¡æ¯çæ ¼å¼(å¸å±)ï¼å¶è¯­æ³ä¸ºï¼ 
# 
#log4j.appender.appenderName.layout = fully.qualified.name.of.layout.class 
#log4j.appender.appenderName.layout.optionN = valueN 
# 
#Log4jæä¾çlayoutæä»¥ä¸å ç§ï¼ 
#5)org.apache.log4j.HTMLLayout(ä»¥HTMLè¡¨æ ¼å½¢å¼å¸å±) 
#6)org.apache.log4j.PatternLayout(å¯ä»¥çµæ´»å°æå®å¸å±æ¨¡å¼) 
#7)org.apache.log4j.SimpleLayout(åå«æ¥å¿ä¿¡æ¯ççº§å«åä¿¡æ¯å­ç¬¦ä¸²) 
#8)org.apache.log4j.TTCCLayout(åå«æ¥å¿äº§ççæ¶é´ãçº¿ç¨ãç±»å«ç­ç­ä¿¡æ¯) 
#9)org.apache.log4j.xml.XMLLayout(ä»¥XMLå½¢å¼å¸å±) 
# 
#5)HTMLLayoutéé¡¹å±æ§ 
# -LocationInfo = TRUE:é»è®¤å¼false,è¾åºjavaæä»¶åç§°åè¡å· 
# -Title=Struts Log Message:é»è®¤å¼ Log4J Log Messages 
# 
#6)PatternLayoutéé¡¹å±æ§ 
# -ConversionPattern = %m%n:æ ¼å¼åæå®çæ¶æ¯(åæ°ææä¸é¢æ) 
# 
#9)XMLLayoutéé¡¹å±æ§ 
# -LocationInfo = TRUE:é»è®¤å¼false,è¾åºjavaæä»¶åç§°åè¡å· 
# 
#Log4Jéç¨ç±»ä¼¼Cè¯­è¨ä¸­çprintfå½æ°çæå°æ ¼å¼æ ¼å¼åæ¥å¿ä¿¡æ¯ï¼æå°åæ°å¦ä¸ï¼ 
# %m è¾åºä»£ç ä¸­æå®çæ¶æ¯ 
# %p è¾åºä¼åçº§ï¼å³DEBUG,INFO,WARN,ERROR,FATAL 
# %r è¾åºèªåºç¨å¯å¨å°è¾åºè¯¥logä¿¡æ¯èè´¹çæ¯«ç§æ° 
# %c è¾åºæå±çç±»ç®,éå¸¸å°±æ¯æå¨ç±»çå¨å 
# %t è¾åºäº§çè¯¥æ¥å¿äºä»¶ççº¿ç¨å 
# %n è¾åºä¸ä¸ªåè½¦æ¢è¡ç¬¦ï¼Windowså¹³å°ä¸ºâ\r\nâï¼Unixå¹³å°ä¸ºâ\nâ 
# %d è¾åºæ¥å¿æ¶é´ç¹çæ¥æææ¶é´ï¼é»è®¤æ ¼å¼ä¸ºISO8601ï¼ä¹å¯ä»¥å¨å¶åæå®æ ¼å¼ 
#    å¦ï¼%d{yyyyå¹´MMæddæ¥ HH:mm:ss,SSS}ï¼è¾åºç±»ä¼¼ï¼2012å¹´01æ05æ¥ 22:10:28,921 
# %l è¾åºæ¥å¿äºä»¶çåçä½ç½®ï¼åæ¬ç±»ç®åãåçççº¿ç¨ï¼ä»¥åå¨ä»£ç ä¸­çè¡æ° 
#    å¦ï¼Testlog.main(TestLog.java:10) 
# %F è¾åºæ¥å¿æ¶æ¯äº§çæ¶æå¨çæä»¶åç§° 
# %L è¾åºä»£ç ä¸­çè¡å· 
# %x è¾åºåå½åçº¿ç¨ç¸å³èçNDC(åµå¥è¯æ­ç¯å¢),åjava servletså¤å®¢æ·å¤çº¿ç¨çåºç¨ä¸­ 
# %% è¾åºä¸ä¸ª"%"å­ç¬¦ 
# 
# å¯ä»¥å¨%ä¸æ¨¡å¼å­ç¬¦ä¹é´å ä¸ä¿®é¥°ç¬¦æ¥æ§å¶å¶æå°å®½åº¦ãæå¤§å®½åº¦ãåææ¬çå¯¹é½æ¹å¼ãå¦ï¼ 
#  %5c: è¾åºcategoryåç§°ï¼æå°å®½åº¦æ¯5ï¼category<5ï¼é»è®¤çæåµä¸å³å¯¹é½ 
#  %-5c:è¾åºcategoryåç§°ï¼æå°å®½åº¦æ¯5ï¼category<5ï¼"-"å·æå®å·¦å¯¹é½,ä¼æç©ºæ ¼ 
#  %.5c:è¾åºcategoryåç§°ï¼æå¤§å®½åº¦æ¯5ï¼category>5ï¼å°±ä¼å°å·¦è¾¹å¤åºçå­ç¬¦æªæï¼<5ä¸ä¼æç©ºæ ¼ 
#  %20.30c:categoryåç§°<20è¡¥ç©ºæ ¼ï¼å¹¶ä¸å³å¯¹é½ï¼>30å­ç¬¦ï¼å°±ä»å·¦è¾¹äº¤è¿éåºçå­ç¬¦æªæ 
################################################################################ 
################################################################################ 
#â£æå®ç¹å®åçè¾åºç¹å®ççº§å« 
#log4j.logger.org.springframework=DEBUG 
################################################################################ 

#OFF,systemOut,logFile,logDailyFile,logRollingFile,logMail,logDB,ALL 
#log4j.rootLogger =ALL,systemOut,logFile,logDailyFile,logRollingFile,logMail,logDB 

#è¾åºå°æ§å¶å° 
log4j.appender.systemOut = org.apache.log4j.ConsoleAppender 
log4j.appender.systemOut.layout = org.apache.log4j.PatternLayout 
log4j.appender.systemOut.layout.ConversionPattern = [%-5p][%-22d{yyyy/MM/dd HH:mm:ssS}][%l]%n%m%n 
log4j.appender.systemOut.Threshold = DEBUG 
log4j.appender.systemOut.ImmediateFlush = TRUE 
log4j.appender.systemOut.Target = System.out 

#è¾åºå°æä»¶ 
log4j.appender.logFile = org.apache.log4j.FileAppender 
log4j.appender.logFile.layout = org.apache.log4j.PatternLayout 
log4j.appender.logFile.layout.ConversionPattern = [%-5p][%-22d{yyyy/MM/dd HH:mm:ssS}][%l]%n%m%n 
log4j.appender.logFile.Threshold = DEBUG 
log4j.appender.logFile.ImmediateFlush = TRUE 
log4j.appender.logFile.Append = TRUE 
log4j.appender.logFile.File = D:/log/debug.log 
log4j.appender.logFile.Encoding = UTF-8 

#æDatePatternè¾åºå°æä»¶ 
log4j.appender.logDailyFile = org.apache.log4j.DailyRollingFileAppender 
log4j.appender.logDailyFile.layout = org.apache.log4j.PatternLayout 
log4j.appender.logDailyFile.layout.ConversionPattern = [%-5p][%-22d{yyyy/MM/dd HH:mm:ssS}][%l]%n%m%n 
log4j.appender.logDailyFile.Threshold = DEBUG 
log4j.appender.logDailyFile.ImmediateFlush = TRUE 
log4j.appender.logDailyFile.Append = TRUE 
log4j.appender.logDailyFile.File = D:/log/debug.log 
log4j.appender.logDailyFile.DatePattern = '.'yyyy-MM-dd-HH-mm'.log' 
log4j.appender.logDailyFile.Encoding = UTF-8 

#è®¾å®æä»¶å¤§å°è¾åºå°æä»¶ 
log4j.appender.logRollingFile = org.apache.log4j.RollingFileAppender 
log4j.appender.logRollingFile.layout = org.apache.log4j.PatternLayout 
log4j.appender.logRollingFile.layout.ConversionPattern = [%-5p][%-22d{yyyy/MM/dd HH:mm:ssS}][%l]%n%m%n 
log4j.appender.logRollingFile.Threshold = DEBUG 
log4j.appender.logRollingFile.ImmediateFlush = TRUE 
log4j.appender.logRollingFile.Append = TRUE 
log4j.appender.logRollingFile.File = D:/log/debug.log
log4j.appender.logRollingFile.MaxFileSize = 1MB 
log4j.appender.logRollingFile.MaxBackupIndex = 10 
log4j.appender.logRollingFile.Encoding = UTF-8 

#ç¨Emailåéæ¥å¿ 
#log4j.appender.logMail = org.apache.log4j.net.SMTPAppender 
#log4j.appender.logMail.layout = org.apache.log4j.HTMLLayout 
#log4j.appender.logMail.layout.LocationInfo = TRUE 
#log4j.appender.logMail.layout.Title = Struts2 Mail LogFile 
#log4j.appender.logMail.Threshold = DEBUG 
#log4j.appender.logMail.SMTPDebug = FALSE 
#log4j.appender.logMail.SMTPHost = SMTP.163.com 
#log4j.appender.logMail.From = <EMAIL> 
#log4j.appender.logMail.To = <EMAIL> 
#log4j.appender.logMail.Cc = <EMAIL> 
#log4j.appender.logMail.Bcc = <EMAIL> 
#log4j.appender.logMail.SMTPUsername = xly3000 
#log4j.appender.logMail.SMTPPassword = 1234567 
#log4j.appender.logMail.Subject = Log4j Log Messages 
#log4j.appender.logMail.BufferSize = 1024 
#log4j.appender.logMail.SMTPAuth = TRUE 

#å°æ¥å¿ç»å½å°MySQLæ°æ®åº 
#log4j.appender.logDB = org.apache.log4j.jdbc.JDBCAppender 
#log4j.appender.logDB.layout = org.apache.log4j.PatternLayout 
#log4j.appender.logDB.Driver = com.mysql.jdbc.Driver 
#log4j.appender.logDB.URL = ******************************* 
#log4j.appender.logDB.User = root 
#log4j.appender.logDB.Password = 123456 
#log4j.appender.logDB.Sql = INSERT INTOT_log4j(project_name,create_date,level,category,file_name,thread_name,line,all_category,message)values('Struts2','%d{yyyy-MM-ddHH:mm:ss}','%p','%c','%F','%t','%L','%l','%m')