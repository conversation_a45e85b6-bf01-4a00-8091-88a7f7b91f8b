package com.changneng.sa.bean;

/**
 * FileName: 一级指标案卷数据明细VO
 */
public class CaseDetailVo {
    //案卷类型
    private String fileType;
    //0一级指标  1二级指标
    private String targetChoose;
    //一二级指标共同项
    //区域
    private String part;
    //大区
    private String areaGroup;
    //参选单位省
    private String province;
    //参选单位市
    private String city;
    //参选单位县
    private String country;
    //参选单位行政区级别
    private String areaLevel;
    //案卷文号
    private String fileCode;
    //案卷所属地省
    private String province1;
    //案卷所属地市
    private String city1;
    //案卷所属地县
    private String country1;
    //案卷代表类型
    private String type;
    //参选个人姓名
    private String name;
    //身份证号
    private String cardID;
    //参选个人所属行政区省
    private String province2;
    //参选个人所属行政区市
    private String city2;
    //参选个人所属行政区县
    private String country2;
    //案卷参选类型
    private String recommend;
    //委员是否合议
    private String isConsider;
    //首席专家是否合议
    private String isConsiderExpCommit;
    //案卷最终分
    private String finalScore;

    //一级指标
    /*一般行政处罚*/
    //立案审批表（4分）
    private String voteDownValue_1;
    private String inCheckValue_1;
    private Double avg_score1;
    //调查询问笔录（12分）
    private String voteDownValue_2;
    private String inCheckValue_2;
    private Double avg_score2;
    //现场检查（勘察）笔录（12分）
    private String voteDownValue_3;
    private String inCheckValue_3;
    private Double avg_score3;
    //环境监测报告（4分）
    private String voteDownValue_4;
    private String inCheckValue_4;
    private Double avg_score4;
    //收集的证据（书证（3分）
    private String voteDownValue_5;
    private String inCheckValue_5;
    private Double avg_score5;
    //收集的证据（视听资料（1分）
    private String voteDownValue_6;
    private String inCheckValue_6;
    private Double avg_score6;
    //收集的证据（证人证言（1分）
    private String voteDownValue_7;
    private String inCheckValue_7;
    private Double avg_score7;
    //收集的证据（其他（1分）
    private String voteDownValue_8;
    private String inCheckValue_8;
    private Double avg_score8;
    //案件调查报告（7分）
    private String voteDownValue_9;
    private String inCheckValue_9;
    private Double avg_score9;
    //责令改正违法行为决定书（6分）
    private String voteDownValue_10;
    private String inCheckValue_10;
    private Double avg_score10;
    //行政处罚事先（听证）告知书（5分）
    private String voteDownValue_11;
    private String inCheckValue_11;
    private Double avg_score11;
    //行政处罚听证通知书（5分）
    private String voteDownValue_12;
    private String inCheckValue_12;
    private Double avg_score12;
    //听证笔录（6分）
    private String voteDownValue_13;
    private String inCheckValue_13;
    private Double avg_score13;
    //行政处罚决定书（15分）
    private String voteDownValue_14;
    private String inCheckValue_14;
    private Double avg_score14;
    //督促履行义务催告书（5分）
    private String voteDownValue_15;
    private String inCheckValue_15;
    private Double avg_score15;
    //强制执行申请书（5分）
    private String voteDownValue_16;
    private String inCheckValue_16;
    private Double avg_score16;
    //结案表（4分）
    private String voteDownValue_17;
    private String inCheckValue_17;
    private Double avg_score17;
    //案卷（4分）
    private String voteDownValue_18;
    private String inCheckValue_18;
    private Double avg_score18;
    //卷面平均分
    private Double paperScore;
    //实体和程序平均分
    private Double entityScore;
    //加分项（10）
    private Double avg_score19;

    /*按日连续计罚*/
    //再次责令改正违法行为决定书（6分）
    private String voteDownValue_20;
    private String inCheckValue_20;
    private Double avg_score20;
    //按日连续处罚决定书（14分）
    private String voteDownValue_21;
    private String inCheckValue_21;
    private Double avg_score21;

    /*查封扣押*/
    //查封（扣押）审批表
    private String voteDownValue_22;
    private String inCheckValue_22;
    private Double avg_score22;
    //查封（扣押）决定书（含清单）
    private String voteDownValue_23;
    private String inCheckValue_23;
    private Double avg_score23;
    //查封、扣押现场笔录（含清单）
    private String voteDownValue_24;
    private String inCheckValue_24;
    private Double avg_score24;
    //延长查封、扣押通知书
    private String voteDownValue_25;
    private String inCheckValue_25;
    private Double avg_score25;
    //解除查封、扣押决定书
    private String voteDownValue_26;
    private String inCheckValue_26;
    private Double avg_score26;

    /*限产停产*/
    //责令限制生产/停产整治事先（听证）告知书
    private String voteDownValue_27;
    private String inCheckValue_27;
    private Double avg_score27;
    //责令限制生产/停产整治听证通知书
    private String voteDownValue_28;
    private String inCheckValue_28;
    private Double avg_score28;
    //责令限制生产/停产整治决定书
    private String voteDownValue_29;
    private String inCheckValue_29;
    private Double avg_score29;

    /*移送拘留*/
    //移送涉嫌环境违法适用行政拘留处罚案件审批表
    private String voteDownValue_30;
    private String inCheckValue_30;
    private Double avg_score30;
    //移送涉嫌环境违法适用行政拘留处罚案件移送书（含移送材料清单）
    private String voteDownValue_31;
    private String inCheckValue_31;
    private Double avg_score31;

    /*涉嫌环境违法犯罪*/
    //移送涉嫌环境犯罪案件审批表
    private String voteDownValue_32;
    private String inCheckValue_32;
    private Double avg_score32;
    //移送涉嫌环境犯罪案件移送书（含移送材料清单）
    private String voteDownValue_33;
    private String inCheckValue_33;
    private Double avg_score33;

    /*实体*/
    //执法主体错误
    private String entity_score_1;
    //违法主体认定错误
    private String entity_score_2;
    //违法事实认定错误
    private String entity_score_3;
    //法律适用错误
    private String entity_score_4;
    //法律适用错误
    private String entity_score_5;


    //二级指标
    //立案审批表
    private Double is1_1;
    private Double is1_2;
    private Double is1_3;
    private Double is1_4;
    private Double is1_5;
    private Double is1_6;
    private Double is1_7;
    private Double is1_8;
    private String comme_str1;
    private Double laspSum;

    //调查询问笔录
    private Double is2_1;
    private Double is2_2;
    private Double is2_3;
    private Double is2_4;
    private Double is2_5;
    private Double is2_6;
    private Double is2_7;
    private Double is2_8;
    private Double is2_9;
    private Double is2_10;
    private Double is2_11;
    private String comme_str2;
    private Double dcxwllSum;

    //现场检查（勘察）笔录
    private Double is3_1;
    private Double is3_2;
    private Double is3_3;
    private Double is3_4;
    private Double is3_5;
    private Double is3_6;
    private Double is3_7;
    private Double is3_8;
    private Double is3_9;
    private Double is3_10;
    private Double is3_11;
    private String comme_str3;
    private Double xcjcllSum;

    //环境监测报告
    private Double is4_1;
    private Double is4_2;
    private Double is4_3;
    private Double is4_4;
    private Double is4_5;
    private Double is4_6;
    private Double is4_7;
    private Double is4_8;
    private String comme_str4;
    private Double hjjcbgSum;

    //收集的证据（书证
    private Double is5_1;
    private Double is5_2;
    private Double is5_3;
    private String comme_str5;
    private Double sjdzjszSum;

    //收集的证据（视听资料
    private Double is6_1;
    private String comme_str6;

    //收集的证据（证人证言
    private Double is7_1;
    private String comme_str7;

    //收集的证据（其他
    private Double is8_1;
    private String comme_str8;

    //案件调查报告
    private Double is9_1;
    private Double is9_2;
    private Double is9_3;
    private Double is9_4;
    private String comme_str9;
    private Double ajdcbgSum;

    //责令改正违法行为决定书
    private Double is10_1;
    private Double is10_2;
    private Double is10_3;
    private Double is10_4;
    private Double is10_5;
    private Double is10_6;
    private Double is10_7;
    private Double is10_8;
    private Double is10_9;
    private Double is10_10;
    private Double is10_11;
    private Double is10_12;
    private String comme_str10;
    private Double zlgzwfxwjdsSum;

    //行政处罚事先（听证）告知书
    private Double is11_1;
    private Double is11_2;
    private Double is11_3;
    private Double is11_4;
    private Double is11_5;
    private Double is11_6;
    private Double is11_7;
    private Double is11_8;
    private Double is11_9;
    private Double is11_10;
    private String comme_str11;
    private Double xzcfsxgzsSum;

    //行政处罚听证通知书
    private Double is12_1;
    private Double is12_2;
    private Double is12_3;
    private Double is12_4;
    private Double is12_5;
    private Double is12_6;
    private Double is12_7;
    private Double is12_8;
    private Double is12_9;
    private String comme_str12;
    private Double xzcftztzsSum;

    //听证笔录
    private Double is13_1;
    private Double is13_2;
    private Double is13_3;
    private Double is13_4;
    private Double is13_5;
    private Double is13_6;
    private Double is13_7;
    private Double is13_8;
    private Double is13_9;
    private Double is13_10;
    private Double is13_11;
    private Double is13_12;
    private String comme_str13;
    private Double tzblSum;

    //行政处罚决定书
    private Double is14_1;
    private Double is14_2;
    private Double is14_3;
    private Double is14_4;
    private Double is14_5;
    private Double is14_6;
    private Double is14_7;
    private Double is14_8;
    private Double is14_9;
    private Double is14_10;
    private Double is14_11;
    private Double is14_12;
    private Double is14_13;
    private Double is14_14;
    private Double is14_15;
    private Double is14_16;
    private Double is14_17;
    private Double is14_18;
    private String comme_str14;
    private Double xzcfjdsSum;

    //督促履行义务催告书
    private Double is15_1;
    private Double is15_2;
    private Double is15_3;
    private Double is15_4;
    private Double is15_5;
    private Double is15_6;
    private Double is15_7;
    private Double is15_8;
    private Double is15_9;
    private Double is15_10;
    private String comme_str15;
    private Double dclxywcgsSum;

    //强制执行申请书
    private Double is16_1;
    private Double is16_2;
    private Double is16_3;
    private Double is16_4;
    private Double is16_5;
    private Double is16_6;
    private Double is16_7;
    private Double is16_8;
    private Double is16_9;
    private Double is16_10;
    private String comme_str16;
    private Double qzzxsqsSum;

    //结案表
    private Double is17_1;
    private Double is17_2;
    private Double is17_3;
    private Double is17_4;
    private Double is17_5;
    private Double is17_6;
    private Double is17_7;
    private String comme_str17;
    private Double jabSum;

    //案卷
    private Double is18_1;
    private Double is18_2;
    private Double is18_3;
    private Double is18_4;
    private Double is18_5;
    private Double is18_6;
    private Double is18_7;
    private Double is18_8;
    private String comme_str18;
    private Double ajSum;

    //加分项
    private Double is19_1;
    private Double is19_2;
    private Double is19_3;
    private Double is19_4;
    private Double is19_5;
    private Double is19_6;
    private Double is19_7;
    private Double is19_8;
    private Double is19_9;
    private Double is19_10;
    private String comme_str19;
    private Double jfxSum;

    //按日连续处罚二级指标
    private Double is20_1;
    private Double is20_2;
    private Double is20_3;
    private Double is20_4;
    private Double is20_5;
    private Double is20_6;
    private Double is20_7;
    private Double is20_8;
    private Double is20_9;
    private Double is20_10;
    private Double is20_11;
    private String comme_str20;
    private Double zczlgzwfxwjdsSum;
    //按日连续处罚决定书
    private Double is21_1;
    private Double is21_2;
    private Double is21_3;
    private Double is21_4;
    private Double is21_5;
    private Double is21_6;
    private Double is21_7;
    private Double is21_8;
    private Double is21_9;
    private Double is21_10;
    private Double is21_11;
    private Double is21_12;
    private Double is21_13;
    private Double is21_14;
    private Double is21_15;
    private Double is21_16;
    private Double is21_17;
    private Double is21_18;
    private Double is21_19;
    private Double is21_20;
    private String comme_str21;
    private Double arlxcfjdsSum;

    //查封扣押二级指标
    private Double is22_1;
    private Double is22_2;
    private Double is22_3;
    private Double is22_4;
    private Double is22_5;
    private Double is22_6;
    private Double is22_7;
    private Double is22_8;
    private String comme_str22;
    private Double cfkyspbSum;
    //查封（扣押）决定书（含清单）
    private Double is23_1;
    private Double is23_2;
    private Double is23_3;
    private Double is23_4;
    private Double is23_5;
    private Double is23_6;
    private Double is23_7;
    private Double is23_8;
    private Double is23_9;
    private Double is23_10;
    private Double is23_11;
    private Double is23_12;
    private Double is23_13;
    private String comme_str23;
    private Double cfkyjdsSum;
    //查封、扣押现场笔录（含清单）
    private Double is24_1;
    private Double is24_2;
    private Double is24_3;
    private Double is24_4;
    private Double is24_5;
    private String comme_str24;
    private Double cfkyxcblSum;
    //延长查封、扣押通知书
    private Double is25_1;
    private Double is25_2;
    private Double is25_3;
    private Double is25_4;
    private Double is25_5;
    private Double is25_6;
    private Double is25_7;
    private Double is25_8;
    private String comme_str25;
    private Double yccfkytzsSum;
    //解除查封、扣押决定书
    private Double is26_1;
    private Double is26_2;
    private Double is26_3;
    private Double is26_4;
    private Double is26_5;
    private Double is26_6;
    private Double is26_7;
    private Double is26_8;
    private Double is26_9;
    private Double is26_10;
    private String comme_str26;
    private Double jccfkyjdsSum;

    //限产停产二级指标
    private Double is27_1;
    private Double is27_2;
    private Double is27_3;
    private Double is27_4;
    private Double is27_5;
    private Double is27_6;
    private Double is27_7;
    private Double is27_8;
    private Double is27_9;
    private Double is27_10;
    private String comme_str27;
    private Double zlxzscsxgzsSum;
    //责令限制生产/停产整治听证通知书
    private Double is28_1;
    private Double is28_2;
    private Double is28_3;
    private Double is28_4;
    private Double is28_5;
    private Double is28_6;
    private Double is28_7;
    private Double is28_8;
    private Double is28_9;
    private String comme_str28;
    private Double zlxzsctztzsSum;
    //责令限制生产/停产整治决定书
    private Double is29_1;
    private Double is29_2;
    private Double is29_3;
    private Double is29_4;
    private Double is29_5;
    private Double is29_6;
    private Double is29_7;
    private Double is29_8;
    private Double is29_9;
    private Double is29_10;
    private Double is29_11;
    private Double is29_12;
    private Double is29_13;
    private String comme_str29;
    private Double zlxzscjdsSum;

    //移送拘留二级指标
    private Double is30_1;
    private Double is30_2;
    private Double is30_3;
    private Double is30_4;
    private Double is30_5;
    private Double is30_6;
    private Double is30_7;
    private Double is30_8;
    private String comme_str30;
    private Double yssxhjwfspbSum;
    //移送涉嫌环境违法适用行政拘留处罚案件移送书（含移送材料清单）
    private Double is31_1;
    private Double is31_2;
    private Double is31_3;
    private Double is31_4;
    private Double is31_5;
    private Double is31_6;
    private Double is31_7;
    private Double is31_8;
    private Double is31_9;
    private Double is31_10;
    private Double is31_11;
    private Double is31_12;
    private String comme_str31;
    private Double yssxhjwfyssSum;

    //涉嫌环境犯罪二级指标
    private Double is32_1;
    private Double is32_2;
    private Double is32_3;
    private Double is32_4;
    private Double is32_5;
    private Double is32_6;
    private Double is32_7;
    private String comme_str32;
    private Double yssxhjfzajspbSum;
    //移送涉嫌环境犯罪案件移送书（含移送材料清单）
    private Double is33_1;
    private Double is33_2;
    private Double is33_3;
    private Double is33_4;
    private Double is33_5;
    private Double is33_6;
    private Double is33_7;
    private Double is33_8;
    private Double is33_9;
    private String comme_str33;
    private Double yssxhjfzajyssSum;

    /*实体二级指标*/
    private String entity_score_4_1;
    private String entity_score_4_2;
    private String entity_score_5_1;
    private String entity_score_5_2;
    private String entity_score_5_3;
    private String entity_score_5_4;
    private String entity_score_5_5;
    private String entity_score_5_6;

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public String getTargetChoose() {
        return targetChoose;
    }

    public void setTargetChoose(String targetChoose) {
        this.targetChoose = targetChoose;
    }

    public String getPart() {
        return part;
    }

    public void setPart(String part) {
        this.part = part;
    }

    public String getAreaGroup() {
        return areaGroup;
    }

    public void setAreaGroup(String areaGroup) {
        this.areaGroup = areaGroup;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getAreaLevel() {
        return areaLevel;
    }

    public void setAreaLevel(String areaLevel) {
        this.areaLevel = areaLevel;
    }

    public String getFileCode() {
        return fileCode;
    }

    public void setFileCode(String fileCode) {
        this.fileCode = fileCode;
    }

    public String getProvince1() {
        return province1;
    }

    public void setProvince1(String province1) {
        this.province1 = province1;
    }

    public String getCity1() {
        return city1;
    }

    public void setCity1(String city1) {
        this.city1 = city1;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCardID() {
        return cardID;
    }

    public void setCardID(String cardID) {
        this.cardID = cardID;
    }

    public String getProvince2() {
        return province2;
    }

    public void setProvince2(String province2) {
        this.province2 = province2;
    }

    public String getCity2() {
        return city2;
    }

    public void setCity2(String city2) {
        this.city2 = city2;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getCountry1() {
        return country1;
    }

    public void setCountry1(String country1) {
        this.country1 = country1;
    }

    public String getCountry2() {
        return country2;
    }

    public void setCountry2(String country2) {
        this.country2 = country2;
    }

    public String getRecommend() {
        return recommend;
    }

    public void setRecommend(String recommend) {
        this.recommend = recommend;
    }

    public String getIsConsider() {
        return isConsider;
    }

    public void setIsConsider(String isConsider) {
        this.isConsider = isConsider;
    }

    public String getIsConsiderExpCommit() {
        return isConsiderExpCommit;
    }

    public void setIsConsiderExpCommit(String isConsiderExpCommit) {
        this.isConsiderExpCommit = isConsiderExpCommit;
    }

    public String getFinalScore() {
        return finalScore;
    }

    public void setFinalScore(String finalScore) {
        this.finalScore = finalScore;
    }

    public String getVoteDownValue_1() {
        return voteDownValue_1;
    }

    public void setVoteDownValue_1(String voteDownValue_1) {
        this.voteDownValue_1 = voteDownValue_1;
    }

    public String getInCheckValue_1() {
        return inCheckValue_1;
    }

    public void setInCheckValue_1(String inCheckValue_1) {
        this.inCheckValue_1 = inCheckValue_1;
    }

    public Double getAvg_score1() {
        return avg_score1;
    }

    public void setAvg_score1(Double avg_score1) {
        this.avg_score1 = avg_score1;
    }

    public String getVoteDownValue_2() {
        return voteDownValue_2;
    }

    public void setVoteDownValue_2(String voteDownValue_2) {
        this.voteDownValue_2 = voteDownValue_2;
    }

    public String getInCheckValue_2() {
        return inCheckValue_2;
    }

    public void setInCheckValue_2(String inCheckValue_2) {
        this.inCheckValue_2 = inCheckValue_2;
    }

    public String getVoteDownValue_3() {
        return voteDownValue_3;
    }

    public void setVoteDownValue_3(String voteDownValue_3) {
        this.voteDownValue_3 = voteDownValue_3;
    }

    public String getInCheckValue_3() {
        return inCheckValue_3;
    }

    public void setInCheckValue_3(String inCheckValue_3) {
        this.inCheckValue_3 = inCheckValue_3;
    }

    public String getVoteDownValue_4() {
        return voteDownValue_4;
    }

    public void setVoteDownValue_4(String voteDownValue_4) {
        this.voteDownValue_4 = voteDownValue_4;
    }

    public String getInCheckValue_4() {
        return inCheckValue_4;
    }

    public void setInCheckValue_4(String inCheckValue_4) {
        this.inCheckValue_4 = inCheckValue_4;
    }

    public String getVoteDownValue_5() {
        return voteDownValue_5;
    }

    public void setVoteDownValue_5(String voteDownValue_5) {
        this.voteDownValue_5 = voteDownValue_5;
    }

    public String getInCheckValue_5() {
        return inCheckValue_5;
    }

    public void setInCheckValue_5(String inCheckValue_5) {
        this.inCheckValue_5 = inCheckValue_5;
    }

    public String getVoteDownValue_6() {
        return voteDownValue_6;
    }

    public void setVoteDownValue_6(String voteDownValue_6) {
        this.voteDownValue_6 = voteDownValue_6;
    }

    public String getInCheckValue_6() {
        return inCheckValue_6;
    }

    public void setInCheckValue_6(String inCheckValue_6) {
        this.inCheckValue_6 = inCheckValue_6;
    }

    public String getVoteDownValue_7() {
        return voteDownValue_7;
    }

    public void setVoteDownValue_7(String voteDownValue_7) {
        this.voteDownValue_7 = voteDownValue_7;
    }

    public String getInCheckValue_7() {
        return inCheckValue_7;
    }

    public void setInCheckValue_7(String inCheckValue_7) {
        this.inCheckValue_7 = inCheckValue_7;
    }

    public String getVoteDownValue_8() {
        return voteDownValue_8;
    }

    public void setVoteDownValue_8(String voteDownValue_8) {
        this.voteDownValue_8 = voteDownValue_8;
    }

    public String getInCheckValue_8() {
        return inCheckValue_8;
    }

    public void setInCheckValue_8(String inCheckValue_8) {
        this.inCheckValue_8 = inCheckValue_8;
    }

    public String getVoteDownValue_9() {
        return voteDownValue_9;
    }

    public void setVoteDownValue_9(String voteDownValue_9) {
        this.voteDownValue_9 = voteDownValue_9;
    }

    public String getInCheckValue_9() {
        return inCheckValue_9;
    }

    public void setInCheckValue_9(String inCheckValue_9) {
        this.inCheckValue_9 = inCheckValue_9;
    }

    public String getVoteDownValue_10() {
        return voteDownValue_10;
    }

    public void setVoteDownValue_10(String voteDownValue_10) {
        this.voteDownValue_10 = voteDownValue_10;
    }

    public String getInCheckValue_10() {
        return inCheckValue_10;
    }

    public void setInCheckValue_10(String inCheckValue_10) {
        this.inCheckValue_10 = inCheckValue_10;
    }

    public String getVoteDownValue_11() {
        return voteDownValue_11;
    }

    public void setVoteDownValue_11(String voteDownValue_11) {
        this.voteDownValue_11 = voteDownValue_11;
    }

    public String getInCheckValue_11() {
        return inCheckValue_11;
    }

    public void setInCheckValue_11(String inCheckValue_11) {
        this.inCheckValue_11 = inCheckValue_11;
    }

    public String getVoteDownValue_12() {
        return voteDownValue_12;
    }

    public void setVoteDownValue_12(String voteDownValue_12) {
        this.voteDownValue_12 = voteDownValue_12;
    }

    public String getInCheckValue_12() {
        return inCheckValue_12;
    }

    public void setInCheckValue_12(String inCheckValue_12) {
        this.inCheckValue_12 = inCheckValue_12;
    }

    public String getVoteDownValue_13() {
        return voteDownValue_13;
    }

    public void setVoteDownValue_13(String voteDownValue_13) {
        this.voteDownValue_13 = voteDownValue_13;
    }

    public String getInCheckValue_13() {
        return inCheckValue_13;
    }

    public void setInCheckValue_13(String inCheckValue_13) {
        this.inCheckValue_13 = inCheckValue_13;
    }

    public String getVoteDownValue_14() {
        return voteDownValue_14;
    }

    public void setVoteDownValue_14(String voteDownValue_14) {
        this.voteDownValue_14 = voteDownValue_14;
    }

    public String getInCheckValue_14() {
        return inCheckValue_14;
    }

    public void setInCheckValue_14(String inCheckValue_14) {
        this.inCheckValue_14 = inCheckValue_14;
    }

    public String getVoteDownValue_15() {
        return voteDownValue_15;
    }

    public void setVoteDownValue_15(String voteDownValue_15) {
        this.voteDownValue_15 = voteDownValue_15;
    }

    public String getInCheckValue_15() {
        return inCheckValue_15;
    }

    public void setInCheckValue_15(String inCheckValue_15) {
        this.inCheckValue_15 = inCheckValue_15;
    }

    public String getVoteDownValue_16() {
        return voteDownValue_16;
    }

    public void setVoteDownValue_16(String voteDownValue_16) {
        this.voteDownValue_16 = voteDownValue_16;
    }

    public String getInCheckValue_16() {
        return inCheckValue_16;
    }

    public void setInCheckValue_16(String inCheckValue_16) {
        this.inCheckValue_16 = inCheckValue_16;
    }

    public String getVoteDownValue_17() {
        return voteDownValue_17;
    }

    public void setVoteDownValue_17(String voteDownValue_17) {
        this.voteDownValue_17 = voteDownValue_17;
    }

    public String getInCheckValue_17() {
        return inCheckValue_17;
    }

    public void setInCheckValue_17(String inCheckValue_17) {
        this.inCheckValue_17 = inCheckValue_17;
    }

    public String getVoteDownValue_18() {
        return voteDownValue_18;
    }

    public void setVoteDownValue_18(String voteDownValue_18) {
        this.voteDownValue_18 = voteDownValue_18;
    }

    public String getInCheckValue_18() {
        return inCheckValue_18;
    }

    public void setInCheckValue_18(String inCheckValue_18) {
        this.inCheckValue_18 = inCheckValue_18;
    }

    public String getVoteDownValue_20() {
        return voteDownValue_20;
    }

    public void setVoteDownValue_20(String voteDownValue_20) {
        this.voteDownValue_20 = voteDownValue_20;
    }

    public String getInCheckValue_20() {
        return inCheckValue_20;
    }

    public void setInCheckValue_20(String inCheckValue_20) {
        this.inCheckValue_20 = inCheckValue_20;
    }

    public String getVoteDownValue_21() {
        return voteDownValue_21;
    }

    public void setVoteDownValue_21(String voteDownValue_21) {
        this.voteDownValue_21 = voteDownValue_21;
    }

    public String getInCheckValue_21() {
        return inCheckValue_21;
    }

    public void setInCheckValue_21(String inCheckValue_21) {
        this.inCheckValue_21 = inCheckValue_21;
    }

    public String getVoteDownValue_22() {
        return voteDownValue_22;
    }

    public void setVoteDownValue_22(String voteDownValue_22) {
        this.voteDownValue_22 = voteDownValue_22;
    }

    public String getInCheckValue_22() {
        return inCheckValue_22;
    }

    public void setInCheckValue_22(String inCheckValue_22) {
        this.inCheckValue_22 = inCheckValue_22;
    }

    public String getVoteDownValue_23() {
        return voteDownValue_23;
    }

    public void setVoteDownValue_23(String voteDownValue_23) {
        this.voteDownValue_23 = voteDownValue_23;
    }

    public String getInCheckValue_23() {
        return inCheckValue_23;
    }

    public void setInCheckValue_23(String inCheckValue_23) {
        this.inCheckValue_23 = inCheckValue_23;
    }

    public String getVoteDownValue_24() {
        return voteDownValue_24;
    }

    public void setVoteDownValue_24(String voteDownValue_24) {
        this.voteDownValue_24 = voteDownValue_24;
    }

    public String getInCheckValue_24() {
        return inCheckValue_24;
    }

    public void setInCheckValue_24(String inCheckValue_24) {
        this.inCheckValue_24 = inCheckValue_24;
    }

    public String getVoteDownValue_25() {
        return voteDownValue_25;
    }

    public void setVoteDownValue_25(String voteDownValue_25) {
        this.voteDownValue_25 = voteDownValue_25;
    }

    public String getInCheckValue_25() {
        return inCheckValue_25;
    }

    public void setInCheckValue_25(String inCheckValue_25) {
        this.inCheckValue_25 = inCheckValue_25;
    }

    public String getVoteDownValue_26() {
        return voteDownValue_26;
    }

    public void setVoteDownValue_26(String voteDownValue_26) {
        this.voteDownValue_26 = voteDownValue_26;
    }

    public String getInCheckValue_26() {
        return inCheckValue_26;
    }

    public void setInCheckValue_26(String inCheckValue_26) {
        this.inCheckValue_26 = inCheckValue_26;
    }

    public String getVoteDownValue_27() {
        return voteDownValue_27;
    }

    public void setVoteDownValue_27(String voteDownValue_27) {
        this.voteDownValue_27 = voteDownValue_27;
    }

    public String getInCheckValue_27() {
        return inCheckValue_27;
    }

    public void setInCheckValue_27(String inCheckValue_27) {
        this.inCheckValue_27 = inCheckValue_27;
    }

    public String getVoteDownValue_28() {
        return voteDownValue_28;
    }

    public void setVoteDownValue_28(String voteDownValue_28) {
        this.voteDownValue_28 = voteDownValue_28;
    }

    public String getInCheckValue_28() {
        return inCheckValue_28;
    }

    public void setInCheckValue_28(String inCheckValue_28) {
        this.inCheckValue_28 = inCheckValue_28;
    }

    public String getVoteDownValue_29() {
        return voteDownValue_29;
    }

    public void setVoteDownValue_29(String voteDownValue_29) {
        this.voteDownValue_29 = voteDownValue_29;
    }

    public String getInCheckValue_29() {
        return inCheckValue_29;
    }

    public void setInCheckValue_29(String inCheckValue_29) {
        this.inCheckValue_29 = inCheckValue_29;
    }

    public String getVoteDownValue_30() {
        return voteDownValue_30;
    }

    public void setVoteDownValue_30(String voteDownValue_30) {
        this.voteDownValue_30 = voteDownValue_30;
    }

    public String getInCheckValue_30() {
        return inCheckValue_30;
    }

    public void setInCheckValue_30(String inCheckValue_30) {
        this.inCheckValue_30 = inCheckValue_30;
    }

    public String getVoteDownValue_31() {
        return voteDownValue_31;
    }

    public void setVoteDownValue_31(String voteDownValue_31) {
        this.voteDownValue_31 = voteDownValue_31;
    }

    public String getInCheckValue_31() {
        return inCheckValue_31;
    }

    public void setInCheckValue_31(String inCheckValue_31) {
        this.inCheckValue_31 = inCheckValue_31;
    }

    public String getVoteDownValue_32() {
        return voteDownValue_32;
    }

    public void setVoteDownValue_32(String voteDownValue_32) {
        this.voteDownValue_32 = voteDownValue_32;
    }

    public String getInCheckValue_32() {
        return inCheckValue_32;
    }

    public void setInCheckValue_32(String inCheckValue_32) {
        this.inCheckValue_32 = inCheckValue_32;
    }

    public String getVoteDownValue_33() {
        return voteDownValue_33;
    }

    public void setVoteDownValue_33(String voteDownValue_33) {
        this.voteDownValue_33 = voteDownValue_33;
    }

    public String getInCheckValue_33() {
        return inCheckValue_33;
    }

    public void setInCheckValue_33(String inCheckValue_33) {
        this.inCheckValue_33 = inCheckValue_33;
    }

    public String getEntity_score_1() {
        return entity_score_1;
    }

    public void setEntity_score_1(String entity_score_1) {
        this.entity_score_1 = entity_score_1;
    }

    public String getEntity_score_2() {
        return entity_score_2;
    }

    public void setEntity_score_2(String entity_score_2) {
        this.entity_score_2 = entity_score_2;
    }

    public String getEntity_score_3() {
        return entity_score_3;
    }

    public void setEntity_score_3(String entity_score_3) {
        this.entity_score_3 = entity_score_3;
    }

    public String getEntity_score_4() {
        return entity_score_4;
    }

    public void setEntity_score_4(String entity_score_4) {
        this.entity_score_4 = entity_score_4;
    }

    public String getEntity_score_5() {
        return entity_score_5;
    }

    public void setEntity_score_5(String entity_score_5) {
        this.entity_score_5 = entity_score_5;
    }

    public Double getIs1_1() {
        return is1_1;
    }

    public void setIs1_1(Double is1_1) {
        this.is1_1 = is1_1;
    }

    public Double getIs1_2() {
        return is1_2;
    }

    public void setIs1_2(Double is1_2) {
        this.is1_2 = is1_2;
    }

    public Double getIs1_3() {
        return is1_3;
    }

    public void setIs1_3(Double is1_3) {
        this.is1_3 = is1_3;
    }

    public Double getIs1_4() {
        return is1_4;
    }

    public void setIs1_4(Double is1_4) {
        this.is1_4 = is1_4;
    }

    public Double getIs1_5() {
        return is1_5;
    }

    public void setIs1_5(Double is1_5) {
        this.is1_5 = is1_5;
    }

    public Double getIs1_6() {
        return is1_6;
    }

    public void setIs1_6(Double is1_6) {
        this.is1_6 = is1_6;
    }

    public Double getIs1_7() {
        return is1_7;
    }

    public void setIs1_7(Double is1_7) {
        this.is1_7 = is1_7;
    }

    public Double getIs1_8() {
        return is1_8;
    }

    public void setIs1_8(Double is1_8) {
        this.is1_8 = is1_8;
    }

    public String getComme_str1() {
        return comme_str1;
    }

    public void setComme_str1(String comme_str1) {
        this.comme_str1 = comme_str1;
    }

    public Double getLaspSum() {
        return laspSum;
    }

    public void setLaspSum(Double laspSum) {
        this.laspSum = laspSum;
    }

    public Double getIs2_1() {
        return is2_1;
    }

    public void setIs2_1(Double is2_1) {
        this.is2_1 = is2_1;
    }

    public Double getIs2_2() {
        return is2_2;
    }

    public void setIs2_2(Double is2_2) {
        this.is2_2 = is2_2;
    }

    public Double getIs2_3() {
        return is2_3;
    }

    public void setIs2_3(Double is2_3) {
        this.is2_3 = is2_3;
    }

    public Double getIs2_4() {
        return is2_4;
    }

    public void setIs2_4(Double is2_4) {
        this.is2_4 = is2_4;
    }

    public Double getIs2_5() {
        return is2_5;
    }

    public void setIs2_5(Double is2_5) {
        this.is2_5 = is2_5;
    }

    public Double getIs2_6() {
        return is2_6;
    }

    public void setIs2_6(Double is2_6) {
        this.is2_6 = is2_6;
    }

    public Double getIs2_7() {
        return is2_7;
    }

    public void setIs2_7(Double is2_7) {
        this.is2_7 = is2_7;
    }

    public Double getIs2_8() {
        return is2_8;
    }

    public void setIs2_8(Double is2_8) {
        this.is2_8 = is2_8;
    }

    public Double getIs2_9() {
        return is2_9;
    }

    public void setIs2_9(Double is2_9) {
        this.is2_9 = is2_9;
    }

    public Double getIs2_10() {
        return is2_10;
    }

    public void setIs2_10(Double is2_10) {
        this.is2_10 = is2_10;
    }

    public Double getIs2_11() {
        return is2_11;
    }

    public void setIs2_11(Double is2_11) {
        this.is2_11 = is2_11;
    }

    public String getComme_str2() {
        return comme_str2;
    }

    public void setComme_str2(String comme_str2) {
        this.comme_str2 = comme_str2;
    }

    public Double getDcxwllSum() {
        return dcxwllSum;
    }

    public void setDcxwllSum(Double dcxwllSum) {
        this.dcxwllSum = dcxwllSum;
    }

    public Double getIs3_1() {
        return is3_1;
    }

    public void setIs3_1(Double is3_1) {
        this.is3_1 = is3_1;
    }

    public Double getIs3_2() {
        return is3_2;
    }

    public void setIs3_2(Double is3_2) {
        this.is3_2 = is3_2;
    }

    public Double getIs3_3() {
        return is3_3;
    }

    public void setIs3_3(Double is3_3) {
        this.is3_3 = is3_3;
    }

    public Double getIs3_4() {
        return is3_4;
    }

    public void setIs3_4(Double is3_4) {
        this.is3_4 = is3_4;
    }

    public Double getIs3_5() {
        return is3_5;
    }

    public void setIs3_5(Double is3_5) {
        this.is3_5 = is3_5;
    }

    public Double getIs3_6() {
        return is3_6;
    }

    public void setIs3_6(Double is3_6) {
        this.is3_6 = is3_6;
    }

    public Double getIs3_7() {
        return is3_7;
    }

    public void setIs3_7(Double is3_7) {
        this.is3_7 = is3_7;
    }

    public Double getIs3_8() {
        return is3_8;
    }

    public void setIs3_8(Double is3_8) {
        this.is3_8 = is3_8;
    }

    public Double getIs3_9() {
        return is3_9;
    }

    public void setIs3_9(Double is3_9) {
        this.is3_9 = is3_9;
    }

    public Double getIs3_10() {
        return is3_10;
    }

    public void setIs3_10(Double is3_10) {
        this.is3_10 = is3_10;
    }

    public Double getIs3_11() {
        return is3_11;
    }

    public void setIs3_11(Double is3_11) {
        this.is3_11 = is3_11;
    }

    public String getComme_str3() {
        return comme_str3;
    }

    public void setComme_str3(String comme_str3) {
        this.comme_str3 = comme_str3;
    }

    public Double getXcjcllSum() {
        return xcjcllSum;
    }

    public void setXcjcllSum(Double xcjcllSum) {
        this.xcjcllSum = xcjcllSum;
    }

    public Double getIs4_1() {
        return is4_1;
    }

    public void setIs4_1(Double is4_1) {
        this.is4_1 = is4_1;
    }

    public Double getIs4_2() {
        return is4_2;
    }

    public void setIs4_2(Double is4_2) {
        this.is4_2 = is4_2;
    }

    public Double getIs4_3() {
        return is4_3;
    }

    public void setIs4_3(Double is4_3) {
        this.is4_3 = is4_3;
    }

    public Double getIs4_4() {
        return is4_4;
    }

    public void setIs4_4(Double is4_4) {
        this.is4_4 = is4_4;
    }

    public Double getIs4_5() {
        return is4_5;
    }

    public void setIs4_5(Double is4_5) {
        this.is4_5 = is4_5;
    }

    public Double getIs4_6() {
        return is4_6;
    }

    public void setIs4_6(Double is4_6) {
        this.is4_6 = is4_6;
    }

    public Double getIs4_7() {
        return is4_7;
    }

    public void setIs4_7(Double is4_7) {
        this.is4_7 = is4_7;
    }

    public Double getIs4_8() {
        return is4_8;
    }

    public void setIs4_8(Double is4_8) {
        this.is4_8 = is4_8;
    }

    public String getComme_str4() {
        return comme_str4;
    }

    public void setComme_str4(String comme_str4) {
        this.comme_str4 = comme_str4;
    }

    public Double getHjjcbgSum() {
        return hjjcbgSum;
    }

    public void setHjjcbgSum(Double hjjcbgSum) {
        this.hjjcbgSum = hjjcbgSum;
    }

    public Double getIs5_1() {
        return is5_1;
    }

    public void setIs5_1(Double is5_1) {
        this.is5_1 = is5_1;
    }

    public Double getIs5_2() {
        return is5_2;
    }

    public void setIs5_2(Double is5_2) {
        this.is5_2 = is5_2;
    }

    public Double getIs5_3() {
        return is5_3;
    }

    public void setIs5_3(Double is5_3) {
        this.is5_3 = is5_3;
    }

    public String getComme_str5() {
        return comme_str5;
    }

    public void setComme_str5(String comme_str5) {
        this.comme_str5 = comme_str5;
    }

    public Double getSjdzjszSum() {
        return sjdzjszSum;
    }

    public void setSjdzjszSum(Double sjdzjszSum) {
        this.sjdzjszSum = sjdzjszSum;
    }

    public Double getIs6_1() {
        return is6_1;
    }

    public void setIs6_1(Double is6_1) {
        this.is6_1 = is6_1;
    }

    public String getComme_str6() {
        return comme_str6;
    }

    public void setComme_str6(String comme_str6) {
        this.comme_str6 = comme_str6;
    }

    public Double getIs7_1() {
        return is7_1;
    }

    public void setIs7_1(Double is7_1) {
        this.is7_1 = is7_1;
    }

    public String getComme_str7() {
        return comme_str7;
    }

    public void setComme_str7(String comme_str7) {
        this.comme_str7 = comme_str7;
    }

    public Double getIs8_1() {
        return is8_1;
    }

    public void setIs8_1(Double is8_1) {
        this.is8_1 = is8_1;
    }

    public String getComme_str8() {
        return comme_str8;
    }

    public void setComme_str8(String comme_str8) {
        this.comme_str8 = comme_str8;
    }

    public Double getIs9_1() {
        return is9_1;
    }

    public void setIs9_1(Double is9_1) {
        this.is9_1 = is9_1;
    }

    public Double getIs9_2() {
        return is9_2;
    }

    public void setIs9_2(Double is9_2) {
        this.is9_2 = is9_2;
    }

    public Double getIs9_3() {
        return is9_3;
    }

    public void setIs9_3(Double is9_3) {
        this.is9_3 = is9_3;
    }

    public Double getIs9_4() {
        return is9_4;
    }

    public void setIs9_4(Double is9_4) {
        this.is9_4 = is9_4;
    }

    public String getComme_str9() {
        return comme_str9;
    }

    public void setComme_str9(String comme_str9) {
        this.comme_str9 = comme_str9;
    }

    public Double getAjdcbgSum() {
        return ajdcbgSum;
    }

    public void setAjdcbgSum(Double ajdcbgSum) {
        this.ajdcbgSum = ajdcbgSum;
    }

    public Double getIs10_1() {
        return is10_1;
    }

    public void setIs10_1(Double is10_1) {
        this.is10_1 = is10_1;
    }

    public Double getIs10_2() {
        return is10_2;
    }

    public void setIs10_2(Double is10_2) {
        this.is10_2 = is10_2;
    }

    public Double getIs10_3() {
        return is10_3;
    }

    public void setIs10_3(Double is10_3) {
        this.is10_3 = is10_3;
    }

    public Double getIs10_4() {
        return is10_4;
    }

    public void setIs10_4(Double is10_4) {
        this.is10_4 = is10_4;
    }

    public Double getIs10_5() {
        return is10_5;
    }

    public void setIs10_5(Double is10_5) {
        this.is10_5 = is10_5;
    }

    public Double getIs10_6() {
        return is10_6;
    }

    public void setIs10_6(Double is10_6) {
        this.is10_6 = is10_6;
    }

    public Double getIs10_7() {
        return is10_7;
    }

    public void setIs10_7(Double is10_7) {
        this.is10_7 = is10_7;
    }

    public Double getIs10_8() {
        return is10_8;
    }

    public void setIs10_8(Double is10_8) {
        this.is10_8 = is10_8;
    }

    public Double getIs10_9() {
        return is10_9;
    }

    public void setIs10_9(Double is10_9) {
        this.is10_9 = is10_9;
    }

    public Double getIs10_10() {
        return is10_10;
    }

    public void setIs10_10(Double is10_10) {
        this.is10_10 = is10_10;
    }

    public Double getIs10_11() {
        return is10_11;
    }

    public void setIs10_11(Double is10_11) {
        this.is10_11 = is10_11;
    }

    public Double getIs10_12() {
        return is10_12;
    }

    public void setIs10_12(Double is10_12) {
        this.is10_12 = is10_12;
    }

    public String getComme_str10() {
        return comme_str10;
    }

    public void setComme_str10(String comme_str10) {
        this.comme_str10 = comme_str10;
    }

    public Double getZlgzwfxwjdsSum() {
        return zlgzwfxwjdsSum;
    }

    public void setZlgzwfxwjdsSum(Double zlgzwfxwjdsSum) {
        this.zlgzwfxwjdsSum = zlgzwfxwjdsSum;
    }

    public Double getIs11_1() {
        return is11_1;
    }

    public void setIs11_1(Double is11_1) {
        this.is11_1 = is11_1;
    }

    public Double getIs11_2() {
        return is11_2;
    }

    public void setIs11_2(Double is11_2) {
        this.is11_2 = is11_2;
    }

    public Double getIs11_3() {
        return is11_3;
    }

    public void setIs11_3(Double is11_3) {
        this.is11_3 = is11_3;
    }

    public Double getIs11_4() {
        return is11_4;
    }

    public void setIs11_4(Double is11_4) {
        this.is11_4 = is11_4;
    }

    public Double getIs11_5() {
        return is11_5;
    }

    public void setIs11_5(Double is11_5) {
        this.is11_5 = is11_5;
    }

    public Double getIs11_6() {
        return is11_6;
    }

    public void setIs11_6(Double is11_6) {
        this.is11_6 = is11_6;
    }

    public Double getIs11_7() {
        return is11_7;
    }

    public void setIs11_7(Double is11_7) {
        this.is11_7 = is11_7;
    }

    public Double getIs11_8() {
        return is11_8;
    }

    public void setIs11_8(Double is11_8) {
        this.is11_8 = is11_8;
    }

    public Double getIs11_9() {
        return is11_9;
    }

    public void setIs11_9(Double is11_9) {
        this.is11_9 = is11_9;
    }

    public Double getIs11_10() {
        return is11_10;
    }

    public void setIs11_10(Double is11_10) {
        this.is11_10 = is11_10;
    }

    public String getComme_str11() {
        return comme_str11;
    }

    public void setComme_str11(String comme_str11) {
        this.comme_str11 = comme_str11;
    }

    public Double getXzcfsxgzsSum() {
        return xzcfsxgzsSum;
    }

    public void setXzcfsxgzsSum(Double xzcfsxgzsSum) {
        this.xzcfsxgzsSum = xzcfsxgzsSum;
    }

    public Double getIs12_1() {
        return is12_1;
    }

    public void setIs12_1(Double is12_1) {
        this.is12_1 = is12_1;
    }

    public Double getIs12_2() {
        return is12_2;
    }

    public void setIs12_2(Double is12_2) {
        this.is12_2 = is12_2;
    }

    public Double getIs12_3() {
        return is12_3;
    }

    public void setIs12_3(Double is12_3) {
        this.is12_3 = is12_3;
    }

    public Double getIs12_4() {
        return is12_4;
    }

    public void setIs12_4(Double is12_4) {
        this.is12_4 = is12_4;
    }

    public Double getIs12_5() {
        return is12_5;
    }

    public void setIs12_5(Double is12_5) {
        this.is12_5 = is12_5;
    }

    public Double getIs12_6() {
        return is12_6;
    }

    public void setIs12_6(Double is12_6) {
        this.is12_6 = is12_6;
    }

    public Double getIs12_7() {
        return is12_7;
    }

    public void setIs12_7(Double is12_7) {
        this.is12_7 = is12_7;
    }

    public Double getIs12_8() {
        return is12_8;
    }

    public void setIs12_8(Double is12_8) {
        this.is12_8 = is12_8;
    }

    public Double getIs12_9() {
        return is12_9;
    }

    public void setIs12_9(Double is12_9) {
        this.is12_9 = is12_9;
    }

    public String getComme_str12() {
        return comme_str12;
    }

    public void setComme_str12(String comme_str12) {
        this.comme_str12 = comme_str12;
    }

    public Double getXzcftztzsSum() {
        return xzcftztzsSum;
    }

    public void setXzcftztzsSum(Double xzcftztzsSum) {
        this.xzcftztzsSum = xzcftztzsSum;
    }

    public Double getIs13_1() {
        return is13_1;
    }

    public void setIs13_1(Double is13_1) {
        this.is13_1 = is13_1;
    }

    public Double getIs13_2() {
        return is13_2;
    }

    public void setIs13_2(Double is13_2) {
        this.is13_2 = is13_2;
    }

    public Double getIs13_3() {
        return is13_3;
    }

    public void setIs13_3(Double is13_3) {
        this.is13_3 = is13_3;
    }

    public Double getIs13_4() {
        return is13_4;
    }

    public void setIs13_4(Double is13_4) {
        this.is13_4 = is13_4;
    }

    public Double getIs13_5() {
        return is13_5;
    }

    public void setIs13_5(Double is13_5) {
        this.is13_5 = is13_5;
    }

    public Double getIs13_6() {
        return is13_6;
    }

    public void setIs13_6(Double is13_6) {
        this.is13_6 = is13_6;
    }

    public Double getIs13_7() {
        return is13_7;
    }

    public void setIs13_7(Double is13_7) {
        this.is13_7 = is13_7;
    }

    public Double getIs13_8() {
        return is13_8;
    }

    public void setIs13_8(Double is13_8) {
        this.is13_8 = is13_8;
    }

    public Double getIs13_9() {
        return is13_9;
    }

    public void setIs13_9(Double is13_9) {
        this.is13_9 = is13_9;
    }

    public Double getIs13_10() {
        return is13_10;
    }

    public void setIs13_10(Double is13_10) {
        this.is13_10 = is13_10;
    }

    public Double getIs13_11() {
        return is13_11;
    }

    public void setIs13_11(Double is13_11) {
        this.is13_11 = is13_11;
    }

    public Double getIs13_12() {
        return is13_12;
    }

    public void setIs13_12(Double is13_12) {
        this.is13_12 = is13_12;
    }

    public String getComme_str13() {
        return comme_str13;
    }

    public void setComme_str13(String comme_str13) {
        this.comme_str13 = comme_str13;
    }

    public Double getTzblSum() {
        return tzblSum;
    }

    public void setTzblSum(Double tzblSum) {
        this.tzblSum = tzblSum;
    }

    public Double getIs14_1() {
        return is14_1;
    }

    public void setIs14_1(Double is14_1) {
        this.is14_1 = is14_1;
    }

    public Double getIs14_2() {
        return is14_2;
    }

    public void setIs14_2(Double is14_2) {
        this.is14_2 = is14_2;
    }

    public Double getIs14_3() {
        return is14_3;
    }

    public void setIs14_3(Double is14_3) {
        this.is14_3 = is14_3;
    }

    public Double getIs14_4() {
        return is14_4;
    }

    public void setIs14_4(Double is14_4) {
        this.is14_4 = is14_4;
    }

    public Double getIs14_5() {
        return is14_5;
    }

    public void setIs14_5(Double is14_5) {
        this.is14_5 = is14_5;
    }

    public Double getIs14_6() {
        return is14_6;
    }

    public void setIs14_6(Double is14_6) {
        this.is14_6 = is14_6;
    }

    public Double getIs14_7() {
        return is14_7;
    }

    public void setIs14_7(Double is14_7) {
        this.is14_7 = is14_7;
    }

    public Double getIs14_8() {
        return is14_8;
    }

    public void setIs14_8(Double is14_8) {
        this.is14_8 = is14_8;
    }

    public Double getIs14_9() {
        return is14_9;
    }

    public void setIs14_9(Double is14_9) {
        this.is14_9 = is14_9;
    }

    public Double getIs14_10() {
        return is14_10;
    }

    public void setIs14_10(Double is14_10) {
        this.is14_10 = is14_10;
    }

    public Double getIs14_11() {
        return is14_11;
    }

    public void setIs14_11(Double is14_11) {
        this.is14_11 = is14_11;
    }

    public Double getIs14_12() {
        return is14_12;
    }

    public void setIs14_12(Double is14_12) {
        this.is14_12 = is14_12;
    }

    public Double getIs14_13() {
        return is14_13;
    }

    public void setIs14_13(Double is14_13) {
        this.is14_13 = is14_13;
    }

    public Double getIs14_14() {
        return is14_14;
    }

    public void setIs14_14(Double is14_14) {
        this.is14_14 = is14_14;
    }

    public Double getIs14_15() {
        return is14_15;
    }

    public void setIs14_15(Double is14_15) {
        this.is14_15 = is14_15;
    }

    public Double getIs14_16() {
        return is14_16;
    }

    public void setIs14_16(Double is14_16) {
        this.is14_16 = is14_16;
    }

    public Double getIs14_17() {
        return is14_17;
    }

    public void setIs14_17(Double is14_17) {
        this.is14_17 = is14_17;
    }

    public Double getIs14_18() {
        return is14_18;
    }

    public void setIs14_18(Double is14_18) {
        this.is14_18 = is14_18;
    }

    public String getComme_str14() {
        return comme_str14;
    }

    public void setComme_str14(String comme_str14) {
        this.comme_str14 = comme_str14;
    }

    public Double getXzcfjdsSum() {
        return xzcfjdsSum;
    }

    public void setXzcfjdsSum(Double xzcfjdsSum) {
        this.xzcfjdsSum = xzcfjdsSum;
    }

    public Double getIs15_1() {
        return is15_1;
    }

    public void setIs15_1(Double is15_1) {
        this.is15_1 = is15_1;
    }

    public Double getIs15_2() {
        return is15_2;
    }

    public void setIs15_2(Double is15_2) {
        this.is15_2 = is15_2;
    }

    public Double getIs15_3() {
        return is15_3;
    }

    public void setIs15_3(Double is15_3) {
        this.is15_3 = is15_3;
    }

    public Double getIs15_4() {
        return is15_4;
    }

    public void setIs15_4(Double is15_4) {
        this.is15_4 = is15_4;
    }

    public Double getIs15_5() {
        return is15_5;
    }

    public void setIs15_5(Double is15_5) {
        this.is15_5 = is15_5;
    }

    public Double getIs15_6() {
        return is15_6;
    }

    public void setIs15_6(Double is15_6) {
        this.is15_6 = is15_6;
    }

    public Double getIs15_7() {
        return is15_7;
    }

    public void setIs15_7(Double is15_7) {
        this.is15_7 = is15_7;
    }

    public Double getIs15_8() {
        return is15_8;
    }

    public void setIs15_8(Double is15_8) {
        this.is15_8 = is15_8;
    }

    public Double getIs15_9() {
        return is15_9;
    }

    public void setIs15_9(Double is15_9) {
        this.is15_9 = is15_9;
    }

    public Double getIs15_10() {
        return is15_10;
    }

    public void setIs15_10(Double is15_10) {
        this.is15_10 = is15_10;
    }

    public String getComme_str15() {
        return comme_str15;
    }

    public void setComme_str15(String comme_str15) {
        this.comme_str15 = comme_str15;
    }

    public Double getDclxywcgsSum() {
        return dclxywcgsSum;
    }

    public void setDclxywcgsSum(Double dclxywcgsSum) {
        this.dclxywcgsSum = dclxywcgsSum;
    }

    public Double getIs16_1() {
        return is16_1;
    }

    public void setIs16_1(Double is16_1) {
        this.is16_1 = is16_1;
    }

    public Double getIs16_2() {
        return is16_2;
    }

    public void setIs16_2(Double is16_2) {
        this.is16_2 = is16_2;
    }

    public Double getIs16_3() {
        return is16_3;
    }

    public void setIs16_3(Double is16_3) {
        this.is16_3 = is16_3;
    }

    public Double getIs16_4() {
        return is16_4;
    }

    public void setIs16_4(Double is16_4) {
        this.is16_4 = is16_4;
    }

    public Double getIs16_5() {
        return is16_5;
    }

    public void setIs16_5(Double is16_5) {
        this.is16_5 = is16_5;
    }

    public Double getIs16_6() {
        return is16_6;
    }

    public void setIs16_6(Double is16_6) {
        this.is16_6 = is16_6;
    }

    public Double getIs16_7() {
        return is16_7;
    }

    public void setIs16_7(Double is16_7) {
        this.is16_7 = is16_7;
    }

    public Double getIs16_8() {
        return is16_8;
    }

    public void setIs16_8(Double is16_8) {
        this.is16_8 = is16_8;
    }

    public Double getIs16_9() {
        return is16_9;
    }

    public void setIs16_9(Double is16_9) {
        this.is16_9 = is16_9;
    }

    public Double getIs16_10() {
        return is16_10;
    }

    public void setIs16_10(Double is16_10) {
        this.is16_10 = is16_10;
    }

    public String getComme_str16() {
        return comme_str16;
    }

    public void setComme_str16(String comme_str16) {
        this.comme_str16 = comme_str16;
    }

    public Double getQzzxsqsSum() {
        return qzzxsqsSum;
    }

    public void setQzzxsqsSum(Double qzzxsqsSum) {
        this.qzzxsqsSum = qzzxsqsSum;
    }

    public Double getIs17_1() {
        return is17_1;
    }

    public void setIs17_1(Double is17_1) {
        this.is17_1 = is17_1;
    }

    public Double getIs17_2() {
        return is17_2;
    }

    public void setIs17_2(Double is17_2) {
        this.is17_2 = is17_2;
    }

    public Double getIs17_3() {
        return is17_3;
    }

    public void setIs17_3(Double is17_3) {
        this.is17_3 = is17_3;
    }

    public Double getIs17_4() {
        return is17_4;
    }

    public void setIs17_4(Double is17_4) {
        this.is17_4 = is17_4;
    }

    public Double getIs17_5() {
        return is17_5;
    }

    public void setIs17_5(Double is17_5) {
        this.is17_5 = is17_5;
    }

    public Double getIs17_6() {
        return is17_6;
    }

    public void setIs17_6(Double is17_6) {
        this.is17_6 = is17_6;
    }

    public Double getIs17_7() {
        return is17_7;
    }

    public void setIs17_7(Double is17_7) {
        this.is17_7 = is17_7;
    }

    public String getComme_str17() {
        return comme_str17;
    }

    public void setComme_str17(String comme_str17) {
        this.comme_str17 = comme_str17;
    }

    public Double getJabSum() {
        return jabSum;
    }

    public void setJabSum(Double jabSum) {
        this.jabSum = jabSum;
    }

    public Double getIs18_1() {
        return is18_1;
    }

    public void setIs18_1(Double is18_1) {
        this.is18_1 = is18_1;
    }

    public Double getIs18_2() {
        return is18_2;
    }

    public void setIs18_2(Double is18_2) {
        this.is18_2 = is18_2;
    }

    public Double getIs18_3() {
        return is18_3;
    }

    public void setIs18_3(Double is18_3) {
        this.is18_3 = is18_3;
    }

    public Double getIs18_4() {
        return is18_4;
    }

    public void setIs18_4(Double is18_4) {
        this.is18_4 = is18_4;
    }

    public Double getIs18_5() {
        return is18_5;
    }

    public void setIs18_5(Double is18_5) {
        this.is18_5 = is18_5;
    }

    public Double getIs18_6() {
        return is18_6;
    }

    public void setIs18_6(Double is18_6) {
        this.is18_6 = is18_6;
    }

    public Double getIs18_7() {
        return is18_7;
    }

    public void setIs18_7(Double is18_7) {
        this.is18_7 = is18_7;
    }

    public Double getIs18_8() {
        return is18_8;
    }

    public void setIs18_8(Double is18_8) {
        this.is18_8 = is18_8;
    }

    public String getComme_str18() {
        return comme_str18;
    }

    public void setComme_str18(String comme_str18) {
        this.comme_str18 = comme_str18;
    }

    public Double getAjSum() {
        return ajSum;
    }

    public void setAjSum(Double ajSum) {
        this.ajSum = ajSum;
    }

    public Double getIs19_1() {
        return is19_1;
    }

    public void setIs19_1(Double is19_1) {
        this.is19_1 = is19_1;
    }

    public Double getIs19_2() {
        return is19_2;
    }

    public void setIs19_2(Double is19_2) {
        this.is19_2 = is19_2;
    }

    public Double getIs19_3() {
        return is19_3;
    }

    public void setIs19_3(Double is19_3) {
        this.is19_3 = is19_3;
    }

    public Double getIs19_4() {
        return is19_4;
    }

    public void setIs19_4(Double is19_4) {
        this.is19_4 = is19_4;
    }

    public Double getIs19_5() {
        return is19_5;
    }

    public void setIs19_5(Double is19_5) {
        this.is19_5 = is19_5;
    }

    public Double getIs19_6() {
        return is19_6;
    }

    public void setIs19_6(Double is19_6) {
        this.is19_6 = is19_6;
    }

    public Double getIs19_7() {
        return is19_7;
    }

    public void setIs19_7(Double is19_7) {
        this.is19_7 = is19_7;
    }

    public Double getIs19_8() {
        return is19_8;
    }

    public void setIs19_8(Double is19_8) {
        this.is19_8 = is19_8;
    }

    public Double getIs19_9() {
        return is19_9;
    }

    public void setIs19_9(Double is19_9) {
        this.is19_9 = is19_9;
    }

    public Double getIs19_10() {
        return is19_10;
    }

    public void setIs19_10(Double is19_10) {
        this.is19_10 = is19_10;
    }

    public String getComme_str19() {
        return comme_str19;
    }

    public void setComme_str19(String comme_str19) {
        this.comme_str19 = comme_str19;
    }

    public Double getJfxSum() {
        return jfxSum;
    }

    public void setJfxSum(Double jfxSum) {
        this.jfxSum = jfxSum;
    }

    public Double getIs20_1() {
        return is20_1;
    }

    public void setIs20_1(Double is20_1) {
        this.is20_1 = is20_1;
    }

    public Double getIs20_2() {
        return is20_2;
    }

    public void setIs20_2(Double is20_2) {
        this.is20_2 = is20_2;
    }

    public Double getIs20_3() {
        return is20_3;
    }

    public void setIs20_3(Double is20_3) {
        this.is20_3 = is20_3;
    }

    public Double getIs20_4() {
        return is20_4;
    }

    public void setIs20_4(Double is20_4) {
        this.is20_4 = is20_4;
    }

    public Double getIs20_5() {
        return is20_5;
    }

    public void setIs20_5(Double is20_5) {
        this.is20_5 = is20_5;
    }

    public Double getIs20_6() {
        return is20_6;
    }

    public void setIs20_6(Double is20_6) {
        this.is20_6 = is20_6;
    }

    public Double getIs20_7() {
        return is20_7;
    }

    public void setIs20_7(Double is20_7) {
        this.is20_7 = is20_7;
    }

    public Double getIs20_8() {
        return is20_8;
    }

    public void setIs20_8(Double is20_8) {
        this.is20_8 = is20_8;
    }

    public Double getIs20_9() {
        return is20_9;
    }

    public void setIs20_9(Double is20_9) {
        this.is20_9 = is20_9;
    }

    public Double getIs20_10() {
        return is20_10;
    }

    public void setIs20_10(Double is20_10) {
        this.is20_10 = is20_10;
    }

    public Double getIs20_11() {
        return is20_11;
    }

    public void setIs20_11(Double is20_11) {
        this.is20_11 = is20_11;
    }

    public String getComme_str20() {
        return comme_str20;
    }

    public void setComme_str20(String comme_str20) {
        this.comme_str20 = comme_str20;
    }

    public Double getZczlgzwfxwjdsSum() {
        return zczlgzwfxwjdsSum;
    }

    public void setZczlgzwfxwjdsSum(Double zczlgzwfxwjdsSum) {
        this.zczlgzwfxwjdsSum = zczlgzwfxwjdsSum;
    }

    public Double getIs21_1() {
        return is21_1;
    }

    public void setIs21_1(Double is21_1) {
        this.is21_1 = is21_1;
    }

    public Double getIs21_2() {
        return is21_2;
    }

    public void setIs21_2(Double is21_2) {
        this.is21_2 = is21_2;
    }

    public Double getIs21_3() {
        return is21_3;
    }

    public void setIs21_3(Double is21_3) {
        this.is21_3 = is21_3;
    }

    public Double getIs21_4() {
        return is21_4;
    }

    public void setIs21_4(Double is21_4) {
        this.is21_4 = is21_4;
    }

    public Double getIs21_5() {
        return is21_5;
    }

    public void setIs21_5(Double is21_5) {
        this.is21_5 = is21_5;
    }

    public Double getIs21_6() {
        return is21_6;
    }

    public void setIs21_6(Double is21_6) {
        this.is21_6 = is21_6;
    }

    public Double getIs21_7() {
        return is21_7;
    }

    public void setIs21_7(Double is21_7) {
        this.is21_7 = is21_7;
    }

    public Double getIs21_8() {
        return is21_8;
    }

    public void setIs21_8(Double is21_8) {
        this.is21_8 = is21_8;
    }

    public Double getIs21_9() {
        return is21_9;
    }

    public void setIs21_9(Double is21_9) {
        this.is21_9 = is21_9;
    }

    public Double getIs21_10() {
        return is21_10;
    }

    public void setIs21_10(Double is21_10) {
        this.is21_10 = is21_10;
    }

    public Double getIs21_11() {
        return is21_11;
    }

    public void setIs21_11(Double is21_11) {
        this.is21_11 = is21_11;
    }

    public Double getIs21_12() {
        return is21_12;
    }

    public void setIs21_12(Double is21_12) {
        this.is21_12 = is21_12;
    }

    public Double getIs21_13() {
        return is21_13;
    }

    public void setIs21_13(Double is21_13) {
        this.is21_13 = is21_13;
    }

    public Double getIs21_14() {
        return is21_14;
    }

    public void setIs21_14(Double is21_14) {
        this.is21_14 = is21_14;
    }

    public Double getIs21_15() {
        return is21_15;
    }

    public void setIs21_15(Double is21_15) {
        this.is21_15 = is21_15;
    }

    public Double getIs21_16() {
        return is21_16;
    }

    public void setIs21_16(Double is21_16) {
        this.is21_16 = is21_16;
    }

    public Double getIs21_17() {
        return is21_17;
    }

    public void setIs21_17(Double is21_17) {
        this.is21_17 = is21_17;
    }

    public Double getIs21_18() {
        return is21_18;
    }

    public void setIs21_18(Double is21_18) {
        this.is21_18 = is21_18;
    }

    public Double getIs21_19() {
        return is21_19;
    }

    public void setIs21_19(Double is21_19) {
        this.is21_19 = is21_19;
    }

    public Double getIs21_20() {
        return is21_20;
    }

    public void setIs21_20(Double is21_20) {
        this.is21_20 = is21_20;
    }

    public String getComme_str21() {
        return comme_str21;
    }

    public void setComme_str21(String comme_str21) {
        this.comme_str21 = comme_str21;
    }

    public Double getArlxcfjdsSum() {
        return arlxcfjdsSum;
    }

    public void setArlxcfjdsSum(Double arlxcfjdsSum) {
        this.arlxcfjdsSum = arlxcfjdsSum;
    }

    public Double getIs22_1() {
        return is22_1;
    }

    public void setIs22_1(Double is22_1) {
        this.is22_1 = is22_1;
    }

    public Double getIs22_2() {
        return is22_2;
    }

    public void setIs22_2(Double is22_2) {
        this.is22_2 = is22_2;
    }

    public Double getIs22_3() {
        return is22_3;
    }

    public void setIs22_3(Double is22_3) {
        this.is22_3 = is22_3;
    }

    public Double getIs22_4() {
        return is22_4;
    }

    public void setIs22_4(Double is22_4) {
        this.is22_4 = is22_4;
    }

    public Double getIs22_5() {
        return is22_5;
    }

    public void setIs22_5(Double is22_5) {
        this.is22_5 = is22_5;
    }

    public Double getIs22_6() {
        return is22_6;
    }

    public void setIs22_6(Double is22_6) {
        this.is22_6 = is22_6;
    }

    public Double getIs22_7() {
        return is22_7;
    }

    public void setIs22_7(Double is22_7) {
        this.is22_7 = is22_7;
    }

    public Double getIs22_8() {
        return is22_8;
    }

    public void setIs22_8(Double is22_8) {
        this.is22_8 = is22_8;
    }

    public String getComme_str22() {
        return comme_str22;
    }

    public void setComme_str22(String comme_str22) {
        this.comme_str22 = comme_str22;
    }

    public Double getCfkyspbSum() {
        return cfkyspbSum;
    }

    public void setCfkyspbSum(Double cfkyspbSum) {
        this.cfkyspbSum = cfkyspbSum;
    }

    public Double getIs23_1() {
        return is23_1;
    }

    public void setIs23_1(Double is23_1) {
        this.is23_1 = is23_1;
    }

    public Double getIs23_2() {
        return is23_2;
    }

    public void setIs23_2(Double is23_2) {
        this.is23_2 = is23_2;
    }

    public Double getIs23_3() {
        return is23_3;
    }

    public void setIs23_3(Double is23_3) {
        this.is23_3 = is23_3;
    }

    public Double getIs23_4() {
        return is23_4;
    }

    public void setIs23_4(Double is23_4) {
        this.is23_4 = is23_4;
    }

    public Double getIs23_5() {
        return is23_5;
    }

    public void setIs23_5(Double is23_5) {
        this.is23_5 = is23_5;
    }

    public Double getIs23_6() {
        return is23_6;
    }

    public void setIs23_6(Double is23_6) {
        this.is23_6 = is23_6;
    }

    public Double getIs23_7() {
        return is23_7;
    }

    public void setIs23_7(Double is23_7) {
        this.is23_7 = is23_7;
    }

    public Double getIs23_8() {
        return is23_8;
    }

    public void setIs23_8(Double is23_8) {
        this.is23_8 = is23_8;
    }

    public Double getIs23_9() {
        return is23_9;
    }

    public void setIs23_9(Double is23_9) {
        this.is23_9 = is23_9;
    }

    public Double getIs23_10() {
        return is23_10;
    }

    public void setIs23_10(Double is23_10) {
        this.is23_10 = is23_10;
    }

    public Double getIs23_11() {
        return is23_11;
    }

    public void setIs23_11(Double is23_11) {
        this.is23_11 = is23_11;
    }

    public Double getIs23_12() {
        return is23_12;
    }

    public void setIs23_12(Double is23_12) {
        this.is23_12 = is23_12;
    }

    public Double getIs23_13() {
        return is23_13;
    }

    public void setIs23_13(Double is23_13) {
        this.is23_13 = is23_13;
    }

    public String getComme_str23() {
        return comme_str23;
    }

    public void setComme_str23(String comme_str23) {
        this.comme_str23 = comme_str23;
    }

    public Double getCfkyjdsSum() {
        return cfkyjdsSum;
    }

    public void setCfkyjdsSum(Double cfkyjdsSum) {
        this.cfkyjdsSum = cfkyjdsSum;
    }

    public Double getIs24_1() {
        return is24_1;
    }

    public void setIs24_1(Double is24_1) {
        this.is24_1 = is24_1;
    }

    public Double getIs24_2() {
        return is24_2;
    }

    public void setIs24_2(Double is24_2) {
        this.is24_2 = is24_2;
    }

    public Double getIs24_3() {
        return is24_3;
    }

    public void setIs24_3(Double is24_3) {
        this.is24_3 = is24_3;
    }

    public Double getIs24_4() {
        return is24_4;
    }

    public void setIs24_4(Double is24_4) {
        this.is24_4 = is24_4;
    }

    public Double getIs24_5() {
        return is24_5;
    }

    public void setIs24_5(Double is24_5) {
        this.is24_5 = is24_5;
    }

    public String getComme_str24() {
        return comme_str24;
    }

    public void setComme_str24(String comme_str24) {
        this.comme_str24 = comme_str24;
    }

    public Double getCfkyxcblSum() {
        return cfkyxcblSum;
    }

    public void setCfkyxcblSum(Double cfkyxcblSum) {
        this.cfkyxcblSum = cfkyxcblSum;
    }

    public Double getIs25_1() {
        return is25_1;
    }

    public void setIs25_1(Double is25_1) {
        this.is25_1 = is25_1;
    }

    public Double getIs25_2() {
        return is25_2;
    }

    public void setIs25_2(Double is25_2) {
        this.is25_2 = is25_2;
    }

    public Double getIs25_3() {
        return is25_3;
    }

    public void setIs25_3(Double is25_3) {
        this.is25_3 = is25_3;
    }

    public Double getIs25_4() {
        return is25_4;
    }

    public void setIs25_4(Double is25_4) {
        this.is25_4 = is25_4;
    }

    public Double getIs25_5() {
        return is25_5;
    }

    public void setIs25_5(Double is25_5) {
        this.is25_5 = is25_5;
    }

    public Double getIs25_6() {
        return is25_6;
    }

    public void setIs25_6(Double is25_6) {
        this.is25_6 = is25_6;
    }

    public Double getIs25_7() {
        return is25_7;
    }

    public void setIs25_7(Double is25_7) {
        this.is25_7 = is25_7;
    }

    public Double getIs25_8() {
        return is25_8;
    }

    public void setIs25_8(Double is25_8) {
        this.is25_8 = is25_8;
    }

    public String getComme_str25() {
        return comme_str25;
    }

    public void setComme_str25(String comme_str25) {
        this.comme_str25 = comme_str25;
    }

    public Double getYccfkytzsSum() {
        return yccfkytzsSum;
    }

    public void setYccfkytzsSum(Double yccfkytzsSum) {
        this.yccfkytzsSum = yccfkytzsSum;
    }

    public Double getIs26_1() {
        return is26_1;
    }

    public void setIs26_1(Double is26_1) {
        this.is26_1 = is26_1;
    }

    public Double getIs26_2() {
        return is26_2;
    }

    public void setIs26_2(Double is26_2) {
        this.is26_2 = is26_2;
    }

    public Double getIs26_3() {
        return is26_3;
    }

    public void setIs26_3(Double is26_3) {
        this.is26_3 = is26_3;
    }

    public Double getIs26_4() {
        return is26_4;
    }

    public void setIs26_4(Double is26_4) {
        this.is26_4 = is26_4;
    }

    public Double getIs26_5() {
        return is26_5;
    }

    public void setIs26_5(Double is26_5) {
        this.is26_5 = is26_5;
    }

    public Double getIs26_6() {
        return is26_6;
    }

    public void setIs26_6(Double is26_6) {
        this.is26_6 = is26_6;
    }

    public Double getIs26_7() {
        return is26_7;
    }

    public void setIs26_7(Double is26_7) {
        this.is26_7 = is26_7;
    }

    public Double getIs26_8() {
        return is26_8;
    }

    public void setIs26_8(Double is26_8) {
        this.is26_8 = is26_8;
    }

    public Double getIs26_9() {
        return is26_9;
    }

    public void setIs26_9(Double is26_9) {
        this.is26_9 = is26_9;
    }

    public Double getIs26_10() {
        return is26_10;
    }

    public void setIs26_10(Double is26_10) {
        this.is26_10 = is26_10;
    }

    public String getComme_str26() {
        return comme_str26;
    }

    public void setComme_str26(String comme_str26) {
        this.comme_str26 = comme_str26;
    }

    public Double getJccfkyjdsSum() {
        return jccfkyjdsSum;
    }

    public void setJccfkyjdsSum(Double jccfkyjdsSum) {
        this.jccfkyjdsSum = jccfkyjdsSum;
    }

    public Double getIs27_1() {
        return is27_1;
    }

    public void setIs27_1(Double is27_1) {
        this.is27_1 = is27_1;
    }

    public Double getIs27_2() {
        return is27_2;
    }

    public void setIs27_2(Double is27_2) {
        this.is27_2 = is27_2;
    }

    public Double getIs27_3() {
        return is27_3;
    }

    public void setIs27_3(Double is27_3) {
        this.is27_3 = is27_3;
    }

    public Double getIs27_4() {
        return is27_4;
    }

    public void setIs27_4(Double is27_4) {
        this.is27_4 = is27_4;
    }

    public Double getIs27_5() {
        return is27_5;
    }

    public void setIs27_5(Double is27_5) {
        this.is27_5 = is27_5;
    }

    public Double getIs27_6() {
        return is27_6;
    }

    public void setIs27_6(Double is27_6) {
        this.is27_6 = is27_6;
    }

    public Double getIs27_7() {
        return is27_7;
    }

    public void setIs27_7(Double is27_7) {
        this.is27_7 = is27_7;
    }

    public Double getIs27_8() {
        return is27_8;
    }

    public void setIs27_8(Double is27_8) {
        this.is27_8 = is27_8;
    }

    public Double getIs27_9() {
        return is27_9;
    }

    public void setIs27_9(Double is27_9) {
        this.is27_9 = is27_9;
    }

    public Double getIs27_10() {
        return is27_10;
    }

    public void setIs27_10(Double is27_10) {
        this.is27_10 = is27_10;
    }

    public String getComme_str27() {
        return comme_str27;
    }

    public void setComme_str27(String comme_str27) {
        this.comme_str27 = comme_str27;
    }

    public Double getZlxzscsxgzsSum() {
        return zlxzscsxgzsSum;
    }

    public void setZlxzscsxgzsSum(Double zlxzscsxgzsSum) {
        this.zlxzscsxgzsSum = zlxzscsxgzsSum;
    }

    public Double getIs28_1() {
        return is28_1;
    }

    public void setIs28_1(Double is28_1) {
        this.is28_1 = is28_1;
    }

    public Double getIs28_2() {
        return is28_2;
    }

    public void setIs28_2(Double is28_2) {
        this.is28_2 = is28_2;
    }

    public Double getIs28_3() {
        return is28_3;
    }

    public void setIs28_3(Double is28_3) {
        this.is28_3 = is28_3;
    }

    public Double getIs28_4() {
        return is28_4;
    }

    public void setIs28_4(Double is28_4) {
        this.is28_4 = is28_4;
    }

    public Double getIs28_5() {
        return is28_5;
    }

    public void setIs28_5(Double is28_5) {
        this.is28_5 = is28_5;
    }

    public Double getIs28_6() {
        return is28_6;
    }

    public void setIs28_6(Double is28_6) {
        this.is28_6 = is28_6;
    }

    public Double getIs28_7() {
        return is28_7;
    }

    public void setIs28_7(Double is28_7) {
        this.is28_7 = is28_7;
    }

    public Double getIs28_8() {
        return is28_8;
    }

    public void setIs28_8(Double is28_8) {
        this.is28_8 = is28_8;
    }

    public Double getIs28_9() {
        return is28_9;
    }

    public void setIs28_9(Double is28_9) {
        this.is28_9 = is28_9;
    }

    public String getComme_str28() {
        return comme_str28;
    }

    public void setComme_str28(String comme_str28) {
        this.comme_str28 = comme_str28;
    }

    public Double getZlxzsctztzsSum() {
        return zlxzsctztzsSum;
    }

    public void setZlxzsctztzsSum(Double zlxzsctztzsSum) {
        this.zlxzsctztzsSum = zlxzsctztzsSum;
    }

    public Double getIs29_1() {
        return is29_1;
    }

    public void setIs29_1(Double is29_1) {
        this.is29_1 = is29_1;
    }

    public Double getIs29_2() {
        return is29_2;
    }

    public void setIs29_2(Double is29_2) {
        this.is29_2 = is29_2;
    }

    public Double getIs29_3() {
        return is29_3;
    }

    public void setIs29_3(Double is29_3) {
        this.is29_3 = is29_3;
    }

    public Double getIs29_4() {
        return is29_4;
    }

    public void setIs29_4(Double is29_4) {
        this.is29_4 = is29_4;
    }

    public Double getIs29_5() {
        return is29_5;
    }

    public void setIs29_5(Double is29_5) {
        this.is29_5 = is29_5;
    }

    public Double getIs29_6() {
        return is29_6;
    }

    public void setIs29_6(Double is29_6) {
        this.is29_6 = is29_6;
    }

    public Double getIs29_7() {
        return is29_7;
    }

    public void setIs29_7(Double is29_7) {
        this.is29_7 = is29_7;
    }

    public Double getIs29_8() {
        return is29_8;
    }

    public void setIs29_8(Double is29_8) {
        this.is29_8 = is29_8;
    }

    public Double getIs29_9() {
        return is29_9;
    }

    public void setIs29_9(Double is29_9) {
        this.is29_9 = is29_9;
    }

    public Double getIs29_10() {
        return is29_10;
    }

    public void setIs29_10(Double is29_10) {
        this.is29_10 = is29_10;
    }

    public Double getIs29_11() {
        return is29_11;
    }

    public void setIs29_11(Double is29_11) {
        this.is29_11 = is29_11;
    }

    public Double getIs29_12() {
        return is29_12;
    }

    public void setIs29_12(Double is29_12) {
        this.is29_12 = is29_12;
    }

    public Double getIs29_13() {
        return is29_13;
    }

    public void setIs29_13(Double is29_13) {
        this.is29_13 = is29_13;
    }

    public String getComme_str29() {
        return comme_str29;
    }

    public void setComme_str29(String comme_str29) {
        this.comme_str29 = comme_str29;
    }

    public Double getZlxzscjdsSum() {
        return zlxzscjdsSum;
    }

    public void setZlxzscjdsSum(Double zlxzscjdsSum) {
        this.zlxzscjdsSum = zlxzscjdsSum;
    }

    public Double getIs30_1() {
        return is30_1;
    }

    public void setIs30_1(Double is30_1) {
        this.is30_1 = is30_1;
    }

    public Double getIs30_2() {
        return is30_2;
    }

    public void setIs30_2(Double is30_2) {
        this.is30_2 = is30_2;
    }

    public Double getIs30_3() {
        return is30_3;
    }

    public void setIs30_3(Double is30_3) {
        this.is30_3 = is30_3;
    }

    public Double getIs30_4() {
        return is30_4;
    }

    public void setIs30_4(Double is30_4) {
        this.is30_4 = is30_4;
    }

    public Double getIs30_5() {
        return is30_5;
    }

    public void setIs30_5(Double is30_5) {
        this.is30_5 = is30_5;
    }

    public Double getIs30_6() {
        return is30_6;
    }

    public void setIs30_6(Double is30_6) {
        this.is30_6 = is30_6;
    }

    public Double getIs30_7() {
        return is30_7;
    }

    public void setIs30_7(Double is30_7) {
        this.is30_7 = is30_7;
    }

    public Double getIs30_8() {
        return is30_8;
    }

    public void setIs30_8(Double is30_8) {
        this.is30_8 = is30_8;
    }

    public String getComme_str30() {
        return comme_str30;
    }

    public void setComme_str30(String comme_str30) {
        this.comme_str30 = comme_str30;
    }

    public Double getYssxhjwfspbSum() {
        return yssxhjwfspbSum;
    }

    public void setYssxhjwfspbSum(Double yssxhjwfspbSum) {
        this.yssxhjwfspbSum = yssxhjwfspbSum;
    }

    public Double getIs31_1() {
        return is31_1;
    }

    public void setIs31_1(Double is31_1) {
        this.is31_1 = is31_1;
    }

    public Double getIs31_2() {
        return is31_2;
    }

    public void setIs31_2(Double is31_2) {
        this.is31_2 = is31_2;
    }

    public Double getIs31_3() {
        return is31_3;
    }

    public void setIs31_3(Double is31_3) {
        this.is31_3 = is31_3;
    }

    public Double getIs31_4() {
        return is31_4;
    }

    public void setIs31_4(Double is31_4) {
        this.is31_4 = is31_4;
    }

    public Double getIs31_5() {
        return is31_5;
    }

    public void setIs31_5(Double is31_5) {
        this.is31_5 = is31_5;
    }

    public Double getIs31_6() {
        return is31_6;
    }

    public void setIs31_6(Double is31_6) {
        this.is31_6 = is31_6;
    }

    public Double getIs31_7() {
        return is31_7;
    }

    public void setIs31_7(Double is31_7) {
        this.is31_7 = is31_7;
    }

    public Double getIs31_8() {
        return is31_8;
    }

    public void setIs31_8(Double is31_8) {
        this.is31_8 = is31_8;
    }

    public Double getIs31_9() {
        return is31_9;
    }

    public void setIs31_9(Double is31_9) {
        this.is31_9 = is31_9;
    }

    public Double getIs31_10() {
        return is31_10;
    }

    public void setIs31_10(Double is31_10) {
        this.is31_10 = is31_10;
    }

    public Double getIs31_11() {
        return is31_11;
    }

    public void setIs31_11(Double is31_11) {
        this.is31_11 = is31_11;
    }

    public Double getIs31_12() {
        return is31_12;
    }

    public void setIs31_12(Double is31_12) {
        this.is31_12 = is31_12;
    }

    public String getComme_str31() {
        return comme_str31;
    }

    public void setComme_str31(String comme_str31) {
        this.comme_str31 = comme_str31;
    }

    public Double getYssxhjwfyssSum() {
        return yssxhjwfyssSum;
    }

    public void setYssxhjwfyssSum(Double yssxhjwfyssSum) {
        this.yssxhjwfyssSum = yssxhjwfyssSum;
    }

    public Double getIs32_1() {
        return is32_1;
    }

    public void setIs32_1(Double is32_1) {
        this.is32_1 = is32_1;
    }

    public Double getIs32_2() {
        return is32_2;
    }

    public void setIs32_2(Double is32_2) {
        this.is32_2 = is32_2;
    }

    public Double getIs32_3() {
        return is32_3;
    }

    public void setIs32_3(Double is32_3) {
        this.is32_3 = is32_3;
    }

    public Double getIs32_4() {
        return is32_4;
    }

    public void setIs32_4(Double is32_4) {
        this.is32_4 = is32_4;
    }

    public Double getIs32_5() {
        return is32_5;
    }

    public void setIs32_5(Double is32_5) {
        this.is32_5 = is32_5;
    }

    public Double getIs32_6() {
        return is32_6;
    }

    public void setIs32_6(Double is32_6) {
        this.is32_6 = is32_6;
    }

    public Double getIs32_7() {
        return is32_7;
    }

    public void setIs32_7(Double is32_7) {
        this.is32_7 = is32_7;
    }

    public String getComme_str32() {
        return comme_str32;
    }

    public void setComme_str32(String comme_str32) {
        this.comme_str32 = comme_str32;
    }

    public Double getYssxhjfzajspbSum() {
        return yssxhjfzajspbSum;
    }

    public void setYssxhjfzajspbSum(Double yssxhjfzajspbSum) {
        this.yssxhjfzajspbSum = yssxhjfzajspbSum;
    }

    public Double getIs33_1() {
        return is33_1;
    }

    public void setIs33_1(Double is33_1) {
        this.is33_1 = is33_1;
    }

    public Double getIs33_2() {
        return is33_2;
    }

    public void setIs33_2(Double is33_2) {
        this.is33_2 = is33_2;
    }

    public Double getIs33_3() {
        return is33_3;
    }

    public void setIs33_3(Double is33_3) {
        this.is33_3 = is33_3;
    }

    public Double getIs33_4() {
        return is33_4;
    }

    public void setIs33_4(Double is33_4) {
        this.is33_4 = is33_4;
    }

    public Double getIs33_5() {
        return is33_5;
    }

    public void setIs33_5(Double is33_5) {
        this.is33_5 = is33_5;
    }

    public Double getIs33_6() {
        return is33_6;
    }

    public void setIs33_6(Double is33_6) {
        this.is33_6 = is33_6;
    }

    public Double getIs33_7() {
        return is33_7;
    }

    public void setIs33_7(Double is33_7) {
        this.is33_7 = is33_7;
    }

    public Double getIs33_8() {
        return is33_8;
    }

    public void setIs33_8(Double is33_8) {
        this.is33_8 = is33_8;
    }

    public Double getIs33_9() {
        return is33_9;
    }

    public void setIs33_9(Double is33_9) {
        this.is33_9 = is33_9;
    }

    public String getComme_str33() {
        return comme_str33;
    }

    public void setComme_str33(String comme_str33) {
        this.comme_str33 = comme_str33;
    }

    public Double getYssxhjfzajyssSum() {
        return yssxhjfzajyssSum;
    }

    public void setYssxhjfzajyssSum(Double yssxhjfzajyssSum) {
        this.yssxhjfzajyssSum = yssxhjfzajyssSum;
    }

    public String getEntity_score_4_1() {
        return entity_score_4_1;
    }

    public void setEntity_score_4_1(String entity_score_4_1) {
        this.entity_score_4_1 = entity_score_4_1;
    }

    public String getEntity_score_4_2() {
        return entity_score_4_2;
    }

    public void setEntity_score_4_2(String entity_score_4_2) {
        this.entity_score_4_2 = entity_score_4_2;
    }

    public String getEntity_score_5_1() {
        return entity_score_5_1;
    }

    public void setEntity_score_5_1(String entity_score_5_1) {
        this.entity_score_5_1 = entity_score_5_1;
    }

    public String getEntity_score_5_2() {
        return entity_score_5_2;
    }

    public void setEntity_score_5_2(String entity_score_5_2) {
        this.entity_score_5_2 = entity_score_5_2;
    }

    public String getEntity_score_5_3() {
        return entity_score_5_3;
    }

    public void setEntity_score_5_3(String entity_score_5_3) {
        this.entity_score_5_3 = entity_score_5_3;
    }

    public String getEntity_score_5_4() {
        return entity_score_5_4;
    }

    public void setEntity_score_5_4(String entity_score_5_4) {
        this.entity_score_5_4 = entity_score_5_4;
    }

    public String getEntity_score_5_5() {
        return entity_score_5_5;
    }

    public void setEntity_score_5_5(String entity_score_5_5) {
        this.entity_score_5_5 = entity_score_5_5;
    }

    public String getEntity_score_5_6() {
        return entity_score_5_6;
    }

    public void setEntity_score_5_6(String entity_score_5_6) {
        this.entity_score_5_6 = entity_score_5_6;
    }

    public Double getAvg_score2() {
        return avg_score2;
    }

    public void setAvg_score2(Double avg_score2) {
        this.avg_score2 = avg_score2;
    }

    public Double getAvg_score3() {
        return avg_score3;
    }

    public void setAvg_score3(Double avg_score3) {
        this.avg_score3 = avg_score3;
    }

    public Double getAvg_score4() {
        return avg_score4;
    }

    public void setAvg_score4(Double avg_score4) {
        this.avg_score4 = avg_score4;
    }

    public Double getAvg_score5() {
        return avg_score5;
    }

    public void setAvg_score5(Double avg_score5) {
        this.avg_score5 = avg_score5;
    }

    public Double getAvg_score6() {
        return avg_score6;
    }

    public void setAvg_score6(Double avg_score6) {
        this.avg_score6 = avg_score6;
    }

    public Double getAvg_score7() {
        return avg_score7;
    }

    public void setAvg_score7(Double avg_score7) {
        this.avg_score7 = avg_score7;
    }

    public Double getAvg_score8() {
        return avg_score8;
    }

    public void setAvg_score8(Double avg_score8) {
        this.avg_score8 = avg_score8;
    }

    public Double getAvg_score9() {
        return avg_score9;
    }

    public void setAvg_score9(Double avg_score9) {
        this.avg_score9 = avg_score9;
    }

    public Double getAvg_score10() {
        return avg_score10;
    }

    public void setAvg_score10(Double avg_score10) {
        this.avg_score10 = avg_score10;
    }

    public Double getAvg_score11() {
        return avg_score11;
    }

    public void setAvg_score11(Double avg_score11) {
        this.avg_score11 = avg_score11;
    }

    public Double getAvg_score12() {
        return avg_score12;
    }

    public void setAvg_score12(Double avg_score12) {
        this.avg_score12 = avg_score12;
    }

    public Double getAvg_score13() {
        return avg_score13;
    }

    public void setAvg_score13(Double avg_score13) {
        this.avg_score13 = avg_score13;
    }

    public Double getAvg_score14() {
        return avg_score14;
    }

    public void setAvg_score14(Double avg_score14) {
        this.avg_score14 = avg_score14;
    }

    public Double getAvg_score15() {
        return avg_score15;
    }

    public void setAvg_score15(Double avg_score15) {
        this.avg_score15 = avg_score15;
    }

    public Double getAvg_score16() {
        return avg_score16;
    }

    public void setAvg_score16(Double avg_score16) {
        this.avg_score16 = avg_score16;
    }

    public Double getAvg_score17() {
        return avg_score17;
    }

    public void setAvg_score17(Double avg_score17) {
        this.avg_score17 = avg_score17;
    }

    public Double getAvg_score18() {
        return avg_score18;
    }

    public void setAvg_score18(Double avg_score18) {
        this.avg_score18 = avg_score18;
    }

    public Double getPaperScore() {
        return paperScore;
    }

    public void setPaperScore(Double paperScore) {
        this.paperScore = paperScore;
    }

    public Double getEntityScore() {
        return entityScore;
    }

    public void setEntityScore(Double entityScore) {
        this.entityScore = entityScore;
    }

    public Double getAvg_score19() {
        return avg_score19;
    }

    public void setAvg_score19(Double avg_score19) {
        this.avg_score19 = avg_score19;
    }

    public Double getAvg_score20() {
        return avg_score20;
    }

    public void setAvg_score20(Double avg_score20) {
        this.avg_score20 = avg_score20;
    }

    public Double getAvg_score21() {
        return avg_score21;
    }

    public void setAvg_score21(Double avg_score21) {
        this.avg_score21 = avg_score21;
    }

    public Double getAvg_score22() {
        return avg_score22;
    }

    public void setAvg_score22(Double avg_score22) {
        this.avg_score22 = avg_score22;
    }

    public Double getAvg_score23() {
        return avg_score23;
    }

    public void setAvg_score23(Double avg_score23) {
        this.avg_score23 = avg_score23;
    }

    public Double getAvg_score24() {
        return avg_score24;
    }

    public void setAvg_score24(Double avg_score24) {
        this.avg_score24 = avg_score24;
    }

    public Double getAvg_score25() {
        return avg_score25;
    }

    public void setAvg_score25(Double avg_score25) {
        this.avg_score25 = avg_score25;
    }

    public Double getAvg_score26() {
        return avg_score26;
    }

    public void setAvg_score26(Double avg_score26) {
        this.avg_score26 = avg_score26;
    }

    public Double getAvg_score27() {
        return avg_score27;
    }

    public void setAvg_score27(Double avg_score27) {
        this.avg_score27 = avg_score27;
    }

    public Double getAvg_score28() {
        return avg_score28;
    }

    public void setAvg_score28(Double avg_score28) {
        this.avg_score28 = avg_score28;
    }

    public Double getAvg_score29() {
        return avg_score29;
    }

    public void setAvg_score29(Double avg_score29) {
        this.avg_score29 = avg_score29;
    }

    public Double getAvg_score30() {
        return avg_score30;
    }

    public void setAvg_score30(Double avg_score30) {
        this.avg_score30 = avg_score30;
    }

    public Double getAvg_score31() {
        return avg_score31;
    }

    public void setAvg_score31(Double avg_score31) {
        this.avg_score31 = avg_score31;
    }

    public Double getAvg_score32() {
        return avg_score32;
    }

    public void setAvg_score32(Double avg_score32) {
        this.avg_score32 = avg_score32;
    }

    public Double getAvg_score33() {
        return avg_score33;
    }

    public void setAvg_score33(Double avg_score33) {
        this.avg_score33 = avg_score33;
    }
}
