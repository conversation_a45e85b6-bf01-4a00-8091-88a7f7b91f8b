package com.changneng.sa.bean;


public class ElectionUnit_PXJG {
    private Integer id;

    private String areacode;

    private String areaname;

    private String areatype;

    private String province;

    private String city;
    
    private String country;

//    private Date reportdate;
//
//    private Integer qualityfilenumber;
//
    private Float qualityfiletotalscore;

    private Float qualityfinalscore;

//    private Integer areaguokongqynum;
//    
//    private Integer areazhongdianqynum;
//
//    private Float unitjizhunbili;
//
//    private Float unitcasenumbasevalue;
//
//    private Float unitfinetotalbasevalue;
//
//    private Float unitmajorcasebasevalue;
//
//    private Integer illegalcasenum;
//
//    private Float illegalcasenumscore;
//
//    private Float fineamount;
//
//    private Float fineamountscore;
//
//    private Integer majorcasenum;
//
//    private Float majorcasenumscore;

    private Float numberfinalscore;

    private Integer publicvotenum;

    private Float publicvotenumscore;

    private Byte isyipiaofoujue;

    private Float totalscore;

    private String ranking;
    
    private Float crosstotalscore;
    
    private Float comprehensivetotalscore;
    
    private String comprehensiveranking;
    
    //备注
    private String remark;
    

    public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getAreacode() {
        return areacode;
    }

    public void setAreacode(String areacode) {
        this.areacode = areacode == null ? null : areacode.trim();
    }

    public String getAreaname() {
        return areaname;
    }

    public void setAreaname(String areaname) {
        this.areaname = areaname == null ? null : areaname.trim();
    }

    public String getAreatype() {
        return areatype;
    }

    public void setAreatype(String areatype) {
        this.areatype = areatype == null ? null : areatype.trim();
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province == null ? null : province.trim();
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city == null ? null : city.trim();
    }
    
    public String getCountry() {
		return country;
	}

	public void setCountry(String country) {
		this.country = country;
	}

//    public Date getReportdate() {
//        return reportdate;
//    }
//
//    public void setReportdate(Date reportdate) {
//        this.reportdate = reportdate;
//    }
//
//    public Integer getQualityfilenumber() {
//        return qualityfilenumber;
//    }
//
//    public void setQualityfilenumber(Integer qualityfilenumber) {
//        this.qualityfilenumber = qualityfilenumber;
//    }
//
//    public Float getQualityfiletotalscore() {
//        return qualityfiletotalscore;
//    }
//
//    public void setQualityfiletotalscore(Float qualityfiletotalscore) {
//        this.qualityfiletotalscore = qualityfiletotalscore;
//    }

    public Float getQualityfinalscore() {
        return qualityfinalscore;
    }

    public void setQualityfinalscore(Float qualityfinalscore) {
        this.qualityfinalscore = qualityfinalscore;
    }

//    public Integer getAreaguokongqynum() {
//        return areaguokongqynum;
//    }
//
//    public void setAreaguokongqynum(Integer areaguokongqynum) {
//        this.areaguokongqynum = areaguokongqynum;
//    }
//    
//    public Integer getAreazhongdianqynum() {
//        return areazhongdianqynum;
//    }
//
//    public void setAreazhongdianqynum(Integer areazhongdianqynum) {
//        this.areazhongdianqynum = areazhongdianqynum;
//    }
//
//    public Float getUnitjizhunbili() {
//        return unitjizhunbili;
//    }
//
//    public void setUnitjizhunbili(Float unitjizhunbili) {
//        this.unitjizhunbili = unitjizhunbili;
//    }
//
//    public Float getUnitcasenumbasevalue() {
//        return unitcasenumbasevalue;
//    }
//
//    public void setUnitcasenumbasevalue(Float unitcasenumbasevalue) {
//        this.unitcasenumbasevalue = unitcasenumbasevalue;
//    }
//
//    public Float getUnitfinetotalbasevalue() {
//        return unitfinetotalbasevalue;
//    }
//
//    public void setUnitfinetotalbasevalue(Float unitfinetotalbasevalue) {
//        this.unitfinetotalbasevalue = unitfinetotalbasevalue;
//    }
//
//    public Float getUnitmajorcasebasevalue() {
//        return unitmajorcasebasevalue;
//    }
//
//    public void setUnitmajorcasebasevalue(Float unitmajorcasebasevalue) {
//        this.unitmajorcasebasevalue = unitmajorcasebasevalue;
//    }
//
//    public Integer getIllegalcasenum() {
//        return illegalcasenum;
//    }
//
//    public void setIllegalcasenum(Integer illegalcasenum) {
//        this.illegalcasenum = illegalcasenum;
//    }
//
//    public Float getIllegalcasenumscore() {
//        return illegalcasenumscore;
//    }
//
//    public void setIllegalcasenumscore(Float illegalcasenumscore) {
//        this.illegalcasenumscore = illegalcasenumscore;
//    }
//
//    public Float getFineamount() {
//        return fineamount;
//    }
//
//    public void setFineamount(Float fineamount) {
//        this.fineamount = fineamount;
//    }
//
//    public Float getFineamountscore() {
//        return fineamountscore;
//    }
//
//    public void setFineamountscore(Float fineamountscore) {
//        this.fineamountscore = fineamountscore;
//    }
//
//    public Integer getMajorcasenum() {
//        return majorcasenum;
//    }
//
//    public void setMajorcasenum(Integer majorcasenum) {
//        this.majorcasenum = majorcasenum;
//    }
//
//    public Float getMajorcasenumscore() {
//        return majorcasenumscore;
//    }
//
//    public void setMajorcasenumscore(Float majorcasenumscore) {
//        this.majorcasenumscore = majorcasenumscore;
//    }

    public Float getNumberfinalscore() {
        return numberfinalscore;
    }

    public void setNumberfinalscore(Float numberfinalscore) {
        this.numberfinalscore = numberfinalscore;
    }

    public Integer getPublicvotenum() {
        return publicvotenum;
    }

    public void setPublicvotenum(Integer publicvotenum) {
        this.publicvotenum = publicvotenum;
    }

    public Float getPublicvotenumscore() {
        return publicvotenumscore;
    }

    public void setPublicvotenumscore(Float publicvotenumscore) {
        this.publicvotenumscore = publicvotenumscore;
    }

    public Byte getIsyipiaofoujue() {
        return isyipiaofoujue;
    }

    public void setIsyipiaofoujue(Byte isyipiaofoujue) {
        this.isyipiaofoujue = isyipiaofoujue;
    }

    public Float getTotalscore() {
        return totalscore;
    }

    public void setTotalscore(Float totalscore) {
        this.totalscore = totalscore;
    }

    public String getRanking() {
        return ranking;
    }

    public void setRanking(String ranking) {
        this.ranking = ranking == null ? null : ranking.trim();
    }

	public void setQualityfiletotalscore(Float qualityfiletotalscore) {
		this.qualityfiletotalscore = qualityfiletotalscore;
	}

	public Float getQualityfiletotalscore() {
		return qualityfiletotalscore;
	}

	public void setCrosstotalscore(Float crosstotalscore) {
		this.crosstotalscore = crosstotalscore;
	}

	public Float getCrosstotalscore() {
		return crosstotalscore;
	}

	public void setComprehensivetotalscore(Float comprehensivetotalscore) {
		this.comprehensivetotalscore = comprehensivetotalscore;
	}

	public Float getComprehensivetotalscore() {
		return comprehensivetotalscore;
	}

	public void setComprehensiveranking(String comprehensiveranking) {
		this.comprehensiveranking = comprehensiveranking;
	}

	public String getComprehensiveranking() {
		return comprehensiveranking;
	}

    
}