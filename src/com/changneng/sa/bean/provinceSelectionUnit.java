package com.changneng.sa.bean;

import java.util.Date;

public class provinceSelectionUnit {
    private Integer id;

    private String areacodePro;

    private String areanamePro;

    private String areatypePro;

    private String provincePro;

    private String cityPro;

    private String countryPro;

    private String agencyName;

    private Date createdate;

    private Date updatedate;

    private String recommendsubmit;

    private String isrecommend;

    private String basicsubmit;

    private String isJili;

    private String jiliName;

    private String jiliUrlname;

    private String jiliUrl;

    private String isJingwu;

    private String isLianhe;

    private String jwInformUrlname;

    private String jwInformUrl;

    private String jwWebUrlname;

    private String jwWebUrl;

    private String isCasereview;

    private String isMeetRequirenum;

    private String isFeedback;

    private String isCasecollection;

    private String pingchaUrlname;

    private String pingchaUrl;

    private String worksummaryUrlname;

    private String worksummaryUrl;

    private String caseinfoUrlname;

    private String caseinfoUrl;

    private String caseEndUrlname;

    private String caseEndUrl;

    private String caseEndType;

    private  String caseEndType2;

    private String caseEndsupUrlname;

    private String caseEndsupUrl;

    private String isChutai;

    private String dwReportUrlname;

    private String dwReportUrl;

    private String dwJianUrlname;

    private String dwJianUrl;

    private String isLawcheck;

    private String lawCheckplanUrlname;

    private String lawCheckplanUrl;

    private String lawCheckreportUrlname;

    private String lawCheckreportUrl;

    private String isUnifyattire;

    private String isUnifycertificate;

    private String dwSupUrlname;

    private String dwSupUrl;

    private String dwCrossUrlname;

    private String dwCrossUrl;

    private String dwTaskUrlname;

    private String dwTaskUrl;

    private String dwLianUrlname;

    private String dwLianUrl;

    private String dwFulawUrlname;

    private String dwFulawUrl;

    private String dwLawUrlname;

    private String dwLawUrl;

    private String dwTinylawUrlname;

    private String dwTinylawUrl;

    private String isTypicalcase;

    private String isCover;

    private String systemSupUrlname;

    private String systemSupUrl;

    private String isPositivelistManage;

    private String isPositivelistWarn;

    private String positivelistSupUrlname;

    private String positivelistSupUrl;

    private Double randomRate;

    private String randomsupUrlname;

    private String randomsupUrl;

    private Integer typicalcasePici;

    private String typicalcaseUrlname;

    private String typicalcaseUrl;

    private String isGuide;

    private String guideUrlname;

    private String guideUrl;

    private String isPeopool;

    private String peopoolUrlname;

    private String peopoolUrl;

    private String isTrain;

    private String trainUrlname;

    private String trainUrl;

    private String isLicenselaw;

    private String licenselawUrlname;

    private String licenselawUrl;

    private String isDiscretion;

    private String discretionUrlname;

    private String discretionUrl;

    private String isLicensespecial;

    private String licensespecialUrlname;

    private String licensespecialUrl;

    private String isLicenselist;

    private String licenselistUrlname;

    private String licenselistUrl;

    private String isSave;

    private Integer caseCollectionNum;

    private String caseCollectionUrlname;

    private String caseCollectionUrl;

    private String isPlan;

    private String yearPlanUrlname;

    private String yearPlanUrl;

    private String monthPlanUrlname;

    private String monthPlanUrl;

    private String isInspetion;

    private String inspetionUrlname;

    private String inspetionUrl;

    private String positivelistManageUrlname;

    private String positivelistManageUrl;

    private String positivelistWarnUrlname;

    private String positivelistWarnUrl;

    private String isRandomFive;

    private String randomInfoUrlname;

    private String randomInfoUrl;

    private String randomDecisionUrlname;

    private String randomDecisionUrl;

    private Integer cityInfoNum;

    private Integer countyInfoNum;

    private Integer insiderInfoNum;

    private String caseInfoUrlname;

    private String caseInfoUrl;

    private String desicionUrlname;

    private String desicionUrl;

    private String isOffsiteFive;

    private String offsiteInfoUrlname;

    private String offsiteInfoUrl;

    private String offsiteDecisionUrlname;

    private String offsiteDecisionUrl;

    private String isLawplan;

    private String lawplanUrlname;

    private String lawplanUrl;

    private String lawplansupUrlname;

    private String lawplansupUrl;

    private String isFulawFive;

    private String fulawInfoUrlname;

    private String fulawInfoUrl;

    private String fwlawDesicionUrlname;

    private String fwlawDesicionUrl;

    private String isLawGuide;

    private String lawGuideUrlname;

    private String lawGuideUrl;

    private String isLawplanGuide;

    private String lawplanGuideUrlname;

    private String lawplanGuideUrl;

    private String isAssessment;

    private String assessmentUrlname;

    private String assessmentUrl;

    private String isDwPeople;

    private String dwPeopleUrlname;

    private String dwPeopleUrl;

    private String isDwTeam;

    private String dwTeamUrlname;

    private String dwTeamUrl;

    private String isDwSkill;

    private String dwSkillUrlname;

    private String dwSkillUrl;

    private String isLicenseLaw;

    private String isLicenseList;

    private String licensesupUrlname;

    private String licensesupUrl;

    private String lawSatisfactionUrlname;

    private String lawSatisfactionUrl;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getAreacodePro() {
        return areacodePro;
    }

    public void setAreacodePro(String areacodePro) {
        this.areacodePro = areacodePro;
    }

    public String getAreanamePro() {
        return areanamePro;
    }

    public void setAreanamePro(String areanamePro) {
        this.areanamePro = areanamePro;
    }

    public String getAreatypePro() {
        return areatypePro;
    }

    public void setAreatypePro(String areatypePro) {
        this.areatypePro = areatypePro;
    }

    public String getProvincePro() {
        return provincePro;
    }

    public void setProvincePro(String provincePro) {
        this.provincePro = provincePro;
    }

    public String getCityPro() {
        return cityPro;
    }

    public void setCityPro(String cityPro) {
        this.cityPro = cityPro;
    }

    public String getCountryPro() {
        return countryPro;
    }

    public void setCountryPro(String countryPro) {
        this.countryPro = countryPro;
    }

    public String getAgencyName() {
        return agencyName;
    }

    public void setAgencyName(String agencyName) {
        this.agencyName = agencyName;
    }

    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }

    public Date getUpdatedate() {
        return updatedate;
    }

    public void setUpdatedate(Date updatedate) {
        this.updatedate = updatedate;
    }

    public String getRecommendsubmit() {
        return recommendsubmit;
    }

    public void setRecommendsubmit(String recommendsubmit) {
        this.recommendsubmit = recommendsubmit;
    }

    public String getIsrecommend() {
        return isrecommend;
    }

    public void setIsrecommend(String isrecommend) {
        this.isrecommend = isrecommend;
    }

    public String getBasicsubmit() {
        return basicsubmit;
    }

    public void setBasicsubmit(String basicsubmit) {
        this.basicsubmit = basicsubmit;
    }

    public String getIsJili() {
        return isJili;
    }

    public void setIsJili(String isJili) {
        this.isJili = isJili;
    }

    public String getJiliName() {
        return jiliName;
    }

    public void setJiliName(String jiliName) {
        this.jiliName = jiliName;
    }

    public String getJiliUrlname() {
        return jiliUrlname;
    }

    public void setJiliUrlname(String jiliUrlname) {
        this.jiliUrlname = jiliUrlname;
    }

    public String getJiliUrl() {
        return jiliUrl;
    }

    public void setJiliUrl(String jiliUrl) {
        this.jiliUrl = jiliUrl;
    }

    public String getIsJingwu() {
        return isJingwu;
    }

    public void setIsJingwu(String isJingwu) {
        this.isJingwu = isJingwu;
    }

    public String getIsLianhe() {
        return isLianhe;
    }

    public void setIsLianhe(String isLianhe) {
        this.isLianhe = isLianhe;
    }

    public String getJwInformUrlname() {
        return jwInformUrlname;
    }

    public void setJwInformUrlname(String jwInformUrlname) {
        this.jwInformUrlname = jwInformUrlname;
    }

    public String getJwInformUrl() {
        return jwInformUrl;
    }

    public void setJwInformUrl(String jwInformUrl) {
        this.jwInformUrl = jwInformUrl;
    }

    public String getJwWebUrlname() {
        return jwWebUrlname;
    }

    public void setJwWebUrlname(String jwWebUrlname) {
        this.jwWebUrlname = jwWebUrlname;
    }

    public String getJwWebUrl() {
        return jwWebUrl;
    }

    public void setJwWebUrl(String jwWebUrl) {
        this.jwWebUrl = jwWebUrl;
    }

    public String getIsCasereview() {
        return isCasereview;
    }

    public void setIsCasereview(String isCasereview) {
        this.isCasereview = isCasereview;
    }

    public String getIsMeetRequirenum() {
        return isMeetRequirenum;
    }

    public void setIsMeetRequirenum(String isMeetRequirenum) {
        this.isMeetRequirenum = isMeetRequirenum;
    }

    public String getIsFeedback() {
        return isFeedback;
    }

    public void setIsFeedback(String isFeedback) {
        this.isFeedback = isFeedback;
    }

    public String getIsCasecollection() {
        return isCasecollection;
    }

    public void setIsCasecollection(String isCasecollection) {
        this.isCasecollection = isCasecollection;
    }

    public String getPingchaUrlname() {
        return pingchaUrlname;
    }

    public void setPingchaUrlname(String pingchaUrlname) {
        this.pingchaUrlname = pingchaUrlname;
    }

    public String getPingchaUrl() {
        return pingchaUrl;
    }

    public void setPingchaUrl(String pingchaUrl) {
        this.pingchaUrl = pingchaUrl;
    }

    public String getWorksummaryUrlname() {
        return worksummaryUrlname;
    }

    public void setWorksummaryUrlname(String worksummaryUrlname) {
        this.worksummaryUrlname = worksummaryUrlname;
    }

    public String getWorksummaryUrl() {
        return worksummaryUrl;
    }

    public void setWorksummaryUrl(String worksummaryUrl) {
        this.worksummaryUrl = worksummaryUrl;
    }

    public String getCaseinfoUrlname() {
        return caseinfoUrlname;
    }

    public void setCaseinfoUrlname(String caseinfoUrlname) {
        this.caseinfoUrlname = caseinfoUrlname;
    }

    public String getCaseinfoUrl() {
        return caseinfoUrl;
    }

    public void setCaseinfoUrl(String caseinfoUrl) {
        this.caseinfoUrl = caseinfoUrl;
    }

    public String getCaseEndUrlname() {
        return caseEndUrlname;
    }

    public void setCaseEndUrlname(String caseEndUrlname) {
        this.caseEndUrlname = caseEndUrlname;
    }

    public String getCaseEndUrl() {
        return caseEndUrl;
    }

    public void setCaseEndUrl(String caseEndUrl) {
        this.caseEndUrl = caseEndUrl;
    }

    public String getCaseEndType() {
        return caseEndType;
    }

    public void setCaseEndType(String caseEndType) {
        this.caseEndType = caseEndType;
    }

    public String getCaseEndType2() {
        return caseEndType2;
    }

    public void setCaseEndType2(String caseEndType2) {
        this.caseEndType2 = caseEndType2;
    }

    public String getCaseEndsupUrlname() {
        return caseEndsupUrlname;
    }

    public void setCaseEndsupUrlname(String caseEndsupUrlname) {
        this.caseEndsupUrlname = caseEndsupUrlname;
    }

    public String getCaseEndsupUrl() {
        return caseEndsupUrl;
    }

    public void setCaseEndsupUrl(String caseEndsupUrl) {
        this.caseEndsupUrl = caseEndsupUrl;
    }

    public String getIsChutai() {
        return isChutai;
    }

    public void setIsChutai(String isChutai) {
        this.isChutai = isChutai;
    }

    public String getDwReportUrlname() {
        return dwReportUrlname;
    }

    public void setDwReportUrlname(String dwReportUrlname) {
        this.dwReportUrlname = dwReportUrlname;
    }

    public String getDwReportUrl() {
        return dwReportUrl;
    }

    public void setDwReportUrl(String dwReportUrl) {
        this.dwReportUrl = dwReportUrl;
    }

    public String getDwJianUrlname() {
        return dwJianUrlname;
    }

    public void setDwJianUrlname(String dwJianUrlname) {
        this.dwJianUrlname = dwJianUrlname;
    }

    public String getDwJianUrl() {
        return dwJianUrl;
    }

    public void setDwJianUrl(String dwJianUrl) {
        this.dwJianUrl = dwJianUrl;
    }

    public String getIsLawcheck() {
        return isLawcheck;
    }

    public void setIsLawcheck(String isLawcheck) {
        this.isLawcheck = isLawcheck;
    }

    public String getLawCheckplanUrlname() {
        return lawCheckplanUrlname;
    }

    public void setLawCheckplanUrlname(String lawCheckplanUrlname) {
        this.lawCheckplanUrlname = lawCheckplanUrlname;
    }

    public String getLawCheckplanUrl() {
        return lawCheckplanUrl;
    }

    public void setLawCheckplanUrl(String lawCheckplanUrl) {
        this.lawCheckplanUrl = lawCheckplanUrl;
    }

    public String getLawCheckreportUrlname() {
        return lawCheckreportUrlname;
    }

    public void setLawCheckreportUrlname(String lawCheckreportUrlname) {
        this.lawCheckreportUrlname = lawCheckreportUrlname;
    }

    public String getLawCheckreportUrl() {
        return lawCheckreportUrl;
    }

    public void setLawCheckreportUrl(String lawCheckreportUrl) {
        this.lawCheckreportUrl = lawCheckreportUrl;
    }

    public String getIsUnifyattire() {
        return isUnifyattire;
    }

    public void setIsUnifyattire(String isUnifyattire) {
        this.isUnifyattire = isUnifyattire;
    }

    public String getIsUnifycertificate() {
        return isUnifycertificate;
    }

    public void setIsUnifycertificate(String isUnifycertificate) {
        this.isUnifycertificate = isUnifycertificate;
    }

    public String getDwSupUrlname() {
        return dwSupUrlname;
    }

    public void setDwSupUrlname(String dwSupUrlname) {
        this.dwSupUrlname = dwSupUrlname;
    }

    public String getDwSupUrl() {
        return dwSupUrl;
    }

    public void setDwSupUrl(String dwSupUrl) {
        this.dwSupUrl = dwSupUrl;
    }

    public String getDwCrossUrlname() {
        return dwCrossUrlname;
    }

    public void setDwCrossUrlname(String dwCrossUrlname) {
        this.dwCrossUrlname = dwCrossUrlname;
    }

    public String getDwCrossUrl() {
        return dwCrossUrl;
    }

    public void setDwCrossUrl(String dwCrossUrl) {
        this.dwCrossUrl = dwCrossUrl;
    }

    public String getDwTaskUrlname() {
        return dwTaskUrlname;
    }

    public void setDwTaskUrlname(String dwTaskUrlname) {
        this.dwTaskUrlname = dwTaskUrlname;
    }

    public String getDwTaskUrl() {
        return dwTaskUrl;
    }

    public void setDwTaskUrl(String dwTaskUrl) {
        this.dwTaskUrl = dwTaskUrl;
    }

    public String getDwLianUrlname() {
        return dwLianUrlname;
    }

    public void setDwLianUrlname(String dwLianUrlname) {
        this.dwLianUrlname = dwLianUrlname;
    }

    public String getDwLianUrl() {
        return dwLianUrl;
    }

    public void setDwLianUrl(String dwLianUrl) {
        this.dwLianUrl = dwLianUrl;
    }

    public String getDwFulawUrlname() {
        return dwFulawUrlname;
    }

    public void setDwFulawUrlname(String dwFulawUrlname) {
        this.dwFulawUrlname = dwFulawUrlname;
    }

    public String getDwFulawUrl() {
        return dwFulawUrl;
    }

    public void setDwFulawUrl(String dwFulawUrl) {
        this.dwFulawUrl = dwFulawUrl;
    }

    public String getDwLawUrlname() {
        return dwLawUrlname;
    }

    public void setDwLawUrlname(String dwLawUrlname) {
        this.dwLawUrlname = dwLawUrlname;
    }

    public String getDwLawUrl() {
        return dwLawUrl;
    }

    public void setDwLawUrl(String dwLawUrl) {
        this.dwLawUrl = dwLawUrl;
    }

    public String getDwTinylawUrlname() {
        return dwTinylawUrlname;
    }

    public void setDwTinylawUrlname(String dwTinylawUrlname) {
        this.dwTinylawUrlname = dwTinylawUrlname;
    }

    public String getDwTinylawUrl() {
        return dwTinylawUrl;
    }

    public void setDwTinylawUrl(String dwTinylawUrl) {
        this.dwTinylawUrl = dwTinylawUrl;
    }

    public String getIsTypicalcase() {
        return isTypicalcase;
    }

    public void setIsTypicalcase(String isTypicalcase) {
        this.isTypicalcase = isTypicalcase;
    }

    public String getIsCover() {
        return isCover;
    }

    public void setIsCover(String isCover) {
        this.isCover = isCover;
    }

    public String getSystemSupUrlname() {
        return systemSupUrlname;
    }

    public void setSystemSupUrlname(String systemSupUrlname) {
        this.systemSupUrlname = systemSupUrlname;
    }

    public String getSystemSupUrl() {
        return systemSupUrl;
    }

    public void setSystemSupUrl(String systemSupUrl) {
        this.systemSupUrl = systemSupUrl;
    }

    public String getIsPositivelistManage() {
        return isPositivelistManage;
    }

    public void setIsPositivelistManage(String isPositivelistManage) {
        this.isPositivelistManage = isPositivelistManage;
    }

    public String getIsPositivelistWarn() {
        return isPositivelistWarn;
    }

    public void setIsPositivelistWarn(String isPositivelistWarn) {
        this.isPositivelistWarn = isPositivelistWarn;
    }

    public String getPositivelistSupUrlname() {
        return positivelistSupUrlname;
    }

    public void setPositivelistSupUrlname(String positivelistSupUrlname) {
        this.positivelistSupUrlname = positivelistSupUrlname;
    }

    public String getPositivelistSupUrl() {
        return positivelistSupUrl;
    }

    public void setPositivelistSupUrl(String positivelistSupUrl) {
        this.positivelistSupUrl = positivelistSupUrl;
    }

    public Double getRandomRate() {
        return randomRate;
    }

    public void setRandomRate(Double randomRate) {
        this.randomRate = randomRate;
    }

    public String getRandomsupUrlname() {
        return randomsupUrlname;
    }

    public void setRandomsupUrlname(String randomsupUrlname) {
        this.randomsupUrlname = randomsupUrlname;
    }

    public String getRandomsupUrl() {
        return randomsupUrl;
    }

    public void setRandomsupUrl(String randomsupUrl) {
        this.randomsupUrl = randomsupUrl;
    }

    public Integer getTypicalcasePici() {
        return typicalcasePici;
    }

    public void setTypicalcasePici(Integer typicalcasePici) {
        this.typicalcasePici = typicalcasePici;
    }

    public String getTypicalcaseUrlname() {
        return typicalcaseUrlname;
    }

    public void setTypicalcaseUrlname(String typicalcaseUrlname) {
        this.typicalcaseUrlname = typicalcaseUrlname;
    }

    public String getTypicalcaseUrl() {
        return typicalcaseUrl;
    }

    public void setTypicalcaseUrl(String typicalcaseUrl) {
        this.typicalcaseUrl = typicalcaseUrl;
    }

    public String getIsGuide() {
        return isGuide;
    }

    public void setIsGuide(String isGuide) {
        this.isGuide = isGuide;
    }

    public String getGuideUrlname() {
        return guideUrlname;
    }

    public void setGuideUrlname(String guideUrlname) {
        this.guideUrlname = guideUrlname;
    }

    public String getGuideUrl() {
        return guideUrl;
    }

    public void setGuideUrl(String guideUrl) {
        this.guideUrl = guideUrl;
    }

    public String getIsPeopool() {
        return isPeopool;
    }

    public void setIsPeopool(String isPeopool) {
        this.isPeopool = isPeopool;
    }

    public String getPeopoolUrlname() {
        return peopoolUrlname;
    }

    public void setPeopoolUrlname(String peopoolUrlname) {
        this.peopoolUrlname = peopoolUrlname;
    }

    public String getPeopoolUrl() {
        return peopoolUrl;
    }

    public void setPeopoolUrl(String peopoolUrl) {
        this.peopoolUrl = peopoolUrl;
    }

    public String getIsTrain() {
        return isTrain;
    }

    public void setIsTrain(String isTrain) {
        this.isTrain = isTrain;
    }

    public String getTrainUrlname() {
        return trainUrlname;
    }

    public void setTrainUrlname(String trainUrlname) {
        this.trainUrlname = trainUrlname;
    }

    public String getTrainUrl() {
        return trainUrl;
    }

    public void setTrainUrl(String trainUrl) {
        this.trainUrl = trainUrl;
    }

    public String getIsLicenselaw() {
        return isLicenselaw;
    }

    public void setIsLicenselaw(String isLicenselaw) {
        this.isLicenselaw = isLicenselaw;
    }

    public String getLicenselawUrlname() {
        return licenselawUrlname;
    }

    public void setLicenselawUrlname(String licenselawUrlname) {
        this.licenselawUrlname = licenselawUrlname;
    }

    public String getLicenselawUrl() {
        return licenselawUrl;
    }

    public void setLicenselawUrl(String licenselawUrl) {
        this.licenselawUrl = licenselawUrl;
    }

    public String getIsDiscretion() {
        return isDiscretion;
    }

    public void setIsDiscretion(String isDiscretion) {
        this.isDiscretion = isDiscretion;
    }

    public String getDiscretionUrlname() {
        return discretionUrlname;
    }

    public void setDiscretionUrlname(String discretionUrlname) {
        this.discretionUrlname = discretionUrlname;
    }

    public String getDiscretionUrl() {
        return discretionUrl;
    }

    public void setDiscretionUrl(String discretionUrl) {
        this.discretionUrl = discretionUrl;
    }

    public String getIsLicensespecial() {
        return isLicensespecial;
    }

    public void setIsLicensespecial(String isLicensespecial) {
        this.isLicensespecial = isLicensespecial;
    }

    public String getLicensespecialUrlname() {
        return licensespecialUrlname;
    }

    public void setLicensespecialUrlname(String licensespecialUrlname) {
        this.licensespecialUrlname = licensespecialUrlname;
    }

    public String getLicensespecialUrl() {
        return licensespecialUrl;
    }

    public void setLicensespecialUrl(String licensespecialUrl) {
        this.licensespecialUrl = licensespecialUrl;
    }

    public String getIsLicenselist() {
        return isLicenselist;
    }

    public void setIsLicenselist(String isLicenselist) {
        this.isLicenselist = isLicenselist;
    }

    public String getLicenselistUrlname() {
        return licenselistUrlname;
    }

    public void setLicenselistUrlname(String licenselistUrlname) {
        this.licenselistUrlname = licenselistUrlname;
    }

    public String getLicenselistUrl() {
        return licenselistUrl;
    }

    public void setLicenselistUrl(String licenselistUrl) {
        this.licenselistUrl = licenselistUrl;
    }

    public String getIsSave() {
        return isSave;
    }

    public void setIsSave(String isSave) {
        this.isSave = isSave;
    }

    public Integer getCaseCollectionNum() {
        return caseCollectionNum;
    }

    public void setCaseCollectionNum(Integer caseCollectionNum) {
        this.caseCollectionNum = caseCollectionNum;
    }

    public String getCaseCollectionUrlname() {
        return caseCollectionUrlname;
    }

    public void setCaseCollectionUrlname(String caseCollectionUrlname) {
        this.caseCollectionUrlname = caseCollectionUrlname;
    }

    public String getCaseCollectionUrl() {
        return caseCollectionUrl;
    }

    public void setCaseCollectionUrl(String caseCollectionUrl) {
        this.caseCollectionUrl = caseCollectionUrl;
    }

    public String getIsPlan() {
        return isPlan;
    }

    public void setIsPlan(String isPlan) {
        this.isPlan = isPlan;
    }

    public String getYearPlanUrlname() {
        return yearPlanUrlname;
    }

    public void setYearPlanUrlname(String yearPlanUrlname) {
        this.yearPlanUrlname = yearPlanUrlname;
    }

    public String getYearPlanUrl() {
        return yearPlanUrl;
    }

    public void setYearPlanUrl(String yearPlanUrl) {
        this.yearPlanUrl = yearPlanUrl;
    }

    public String getMonthPlanUrlname() {
        return monthPlanUrlname;
    }

    public void setMonthPlanUrlname(String monthPlanUrlname) {
        this.monthPlanUrlname = monthPlanUrlname;
    }

    public String getMonthPlanUrl() {
        return monthPlanUrl;
    }

    public void setMonthPlanUrl(String monthPlanUrl) {
        this.monthPlanUrl = monthPlanUrl;
    }

    public String getIsInspetion() {
        return isInspetion;
    }

    public void setIsInspetion(String isInspetion) {
        this.isInspetion = isInspetion;
    }

    public String getInspetionUrlname() {
        return inspetionUrlname;
    }

    public void setInspetionUrlname(String inspetionUrlname) {
        this.inspetionUrlname = inspetionUrlname;
    }

    public String getInspetionUrl() {
        return inspetionUrl;
    }

    public void setInspetionUrl(String inspetionUrl) {
        this.inspetionUrl = inspetionUrl;
    }

    public String getPositivelistManageUrlname() {
        return positivelistManageUrlname;
    }

    public void setPositivelistManageUrlname(String positivelistManageUrlname) {
        this.positivelistManageUrlname = positivelistManageUrlname;
    }

    public String getPositivelistManageUrl() {
        return positivelistManageUrl;
    }

    public void setPositivelistManageUrl(String positivelistManageUrl) {
        this.positivelistManageUrl = positivelistManageUrl;
    }

    public String getPositivelistWarnUrlname() {
        return positivelistWarnUrlname;
    }

    public void setPositivelistWarnUrlname(String positivelistWarnUrlname) {
        this.positivelistWarnUrlname = positivelistWarnUrlname;
    }

    public String getPositivelistWarnUrl() {
        return positivelistWarnUrl;
    }

    public void setPositivelistWarnUrl(String positivelistWarnUrl) {
        this.positivelistWarnUrl = positivelistWarnUrl;
    }

    public String getIsRandomFive() {
        return isRandomFive;
    }

    public void setIsRandomFive(String isRandomFive) {
        this.isRandomFive = isRandomFive;
    }

    public String getRandomInfoUrlname() {
        return randomInfoUrlname;
    }

    public void setRandomInfoUrlname(String randomInfoUrlname) {
        this.randomInfoUrlname = randomInfoUrlname;
    }

    public String getRandomInfoUrl() {
        return randomInfoUrl;
    }

    public void setRandomInfoUrl(String randomInfoUrl) {
        this.randomInfoUrl = randomInfoUrl;
    }

    public String getRandomDecisionUrlname() {
        return randomDecisionUrlname;
    }

    public void setRandomDecisionUrlname(String randomDecisionUrlname) {
        this.randomDecisionUrlname = randomDecisionUrlname;
    }

    public String getRandomDecisionUrl() {
        return randomDecisionUrl;
    }

    public void setRandomDecisionUrl(String randomDecisionUrl) {
        this.randomDecisionUrl = randomDecisionUrl;
    }

    public Integer getCityInfoNum() {
        return cityInfoNum;
    }

    public void setCityInfoNum(Integer cityInfoNum) {
        this.cityInfoNum = cityInfoNum;
    }

    public Integer getCountyInfoNum() {
        return countyInfoNum;
    }

    public void setCountyInfoNum(Integer countyInfoNum) {
        this.countyInfoNum = countyInfoNum;
    }

    public Integer getInsiderInfoNum() {
        return insiderInfoNum;
    }

    public void setInsiderInfoNum(Integer insiderInfoNum) {
        this.insiderInfoNum = insiderInfoNum;
    }

    public String getCaseInfoUrlname() {
        return caseInfoUrlname;
    }

    public void setCaseInfoUrlname(String caseInfoUrlname) {
        this.caseInfoUrlname = caseInfoUrlname;
    }

    public String getCaseInfoUrl() {
        return caseInfoUrl;
    }

    public void setCaseInfoUrl(String caseInfoUrl) {
        this.caseInfoUrl = caseInfoUrl;
    }

    public String getDesicionUrlname() {
        return desicionUrlname;
    }

    public void setDesicionUrlname(String desicionUrlname) {
        this.desicionUrlname = desicionUrlname;
    }

    public String getDesicionUrl() {
        return desicionUrl;
    }

    public void setDesicionUrl(String desicionUrl) {
        this.desicionUrl = desicionUrl;
    }

    public String getIsOffsiteFive() {
        return isOffsiteFive;
    }

    public void setIsOffsiteFive(String isOffsiteFive) {
        this.isOffsiteFive = isOffsiteFive;
    }

    public String getOffsiteInfoUrlname() {
        return offsiteInfoUrlname;
    }

    public void setOffsiteInfoUrlname(String offsiteInfoUrlname) {
        this.offsiteInfoUrlname = offsiteInfoUrlname;
    }

    public String getOffsiteInfoUrl() {
        return offsiteInfoUrl;
    }

    public void setOffsiteInfoUrl(String offsiteInfoUrl) {
        this.offsiteInfoUrl = offsiteInfoUrl;
    }

    public String getOffsiteDecisionUrlname() {
        return offsiteDecisionUrlname;
    }

    public void setOffsiteDecisionUrlname(String offsiteDecisionUrlname) {
        this.offsiteDecisionUrlname = offsiteDecisionUrlname;
    }

    public String getOffsiteDecisionUrl() {
        return offsiteDecisionUrl;
    }

    public void setOffsiteDecisionUrl(String offsiteDecisionUrl) {
        this.offsiteDecisionUrl = offsiteDecisionUrl;
    }

    public String getIsLawplan() {
        return isLawplan;
    }

    public void setIsLawplan(String isLawplan) {
        this.isLawplan = isLawplan;
    }

    public String getLawplanUrlname() {
        return lawplanUrlname;
    }

    public void setLawplanUrlname(String lawplanUrlname) {
        this.lawplanUrlname = lawplanUrlname;
    }

    public String getLawplanUrl() {
        return lawplanUrl;
    }

    public void setLawplanUrl(String lawplanUrl) {
        this.lawplanUrl = lawplanUrl;
    }

    public String getLawplansupUrlname() {
        return lawplansupUrlname;
    }

    public void setLawplansupUrlname(String lawplansupUrlname) {
        this.lawplansupUrlname = lawplansupUrlname;
    }

    public String getLawplansupUrl() {
        return lawplansupUrl;
    }

    public void setLawplansupUrl(String lawplansupUrl) {
        this.lawplansupUrl = lawplansupUrl;
    }

    public String getIsFulawFive() {
        return isFulawFive;
    }

    public void setIsFulawFive(String isFulawFive) {
        this.isFulawFive = isFulawFive;
    }

    public String getFulawInfoUrlname() {
        return fulawInfoUrlname;
    }

    public void setFulawInfoUrlname(String fulawInfoUrlname) {
        this.fulawInfoUrlname = fulawInfoUrlname;
    }

    public String getFulawInfoUrl() {
        return fulawInfoUrl;
    }

    public void setFulawInfoUrl(String fulawInfoUrl) {
        this.fulawInfoUrl = fulawInfoUrl;
    }

    public String getFwlawDesicionUrlname() {
        return fwlawDesicionUrlname;
    }

    public void setFwlawDesicionUrlname(String fwlawDesicionUrlname) {
        this.fwlawDesicionUrlname = fwlawDesicionUrlname;
    }

    public String getFwlawDesicionUrl() {
        return fwlawDesicionUrl;
    }

    public void setFwlawDesicionUrl(String fwlawDesicionUrl) {
        this.fwlawDesicionUrl = fwlawDesicionUrl;
    }

    public String getIsLawGuide() {
        return isLawGuide;
    }

    public void setIsLawGuide(String isLawGuide) {
        this.isLawGuide = isLawGuide;
    }

    public String getLawGuideUrlname() {
        return lawGuideUrlname;
    }

    public void setLawGuideUrlname(String lawGuideUrlname) {
        this.lawGuideUrlname = lawGuideUrlname;
    }

    public String getLawGuideUrl() {
        return lawGuideUrl;
    }

    public void setLawGuideUrl(String lawGuideUrl) {
        this.lawGuideUrl = lawGuideUrl;
    }

    public String getIsLawplanGuide() {
        return isLawplanGuide;
    }

    public void setIsLawplanGuide(String isLawplanGuide) {
        this.isLawplanGuide = isLawplanGuide;
    }

    public String getLawplanGuideUrlname() {
        return lawplanGuideUrlname;
    }

    public void setLawplanGuideUrlname(String lawplanGuideUrlname) {
        this.lawplanGuideUrlname = lawplanGuideUrlname;
    }

    public String getLawplanGuideUrl() {
        return lawplanGuideUrl;
    }

    public void setLawplanGuideUrl(String lawplanGuideUrl) {
        this.lawplanGuideUrl = lawplanGuideUrl;
    }

    public String getIsAssessment() {
        return isAssessment;
    }

    public void setIsAssessment(String isAssessment) {
        this.isAssessment = isAssessment;
    }

    public String getAssessmentUrlname() {
        return assessmentUrlname;
    }

    public void setAssessmentUrlname(String assessmentUrlname) {
        this.assessmentUrlname = assessmentUrlname;
    }

    public String getAssessmentUrl() {
        return assessmentUrl;
    }

    public void setAssessmentUrl(String assessmentUrl) {
        this.assessmentUrl = assessmentUrl;
    }

    public String getIsDwPeople() {
        return isDwPeople;
    }

    public void setIsDwPeople(String isDwPeople) {
        this.isDwPeople = isDwPeople;
    }

    public String getDwPeopleUrlname() {
        return dwPeopleUrlname;
    }

    public void setDwPeopleUrlname(String dwPeopleUrlname) {
        this.dwPeopleUrlname = dwPeopleUrlname;
    }

    public String getDwPeopleUrl() {
        return dwPeopleUrl;
    }

    public void setDwPeopleUrl(String dwPeopleUrl) {
        this.dwPeopleUrl = dwPeopleUrl;
    }

    public String getIsDwTeam() {
        return isDwTeam;
    }

    public void setIsDwTeam(String isDwTeam) {
        this.isDwTeam = isDwTeam;
    }

    public String getDwTeamUrlname() {
        return dwTeamUrlname;
    }

    public void setDwTeamUrlname(String dwTeamUrlname) {
        this.dwTeamUrlname = dwTeamUrlname;
    }

    public String getDwTeamUrl() {
        return dwTeamUrl;
    }

    public void setDwTeamUrl(String dwTeamUrl) {
        this.dwTeamUrl = dwTeamUrl;
    }

    public String getIsDwSkill() {
        return isDwSkill;
    }

    public void setIsDwSkill(String isDwSkill) {
        this.isDwSkill = isDwSkill;
    }

    public String getDwSkillUrlname() {
        return dwSkillUrlname;
    }

    public void setDwSkillUrlname(String dwSkillUrlname) {
        this.dwSkillUrlname = dwSkillUrlname;
    }

    public String getDwSkillUrl() {
        return dwSkillUrl;
    }

    public void setDwSkillUrl(String dwSkillUrl) {
        this.dwSkillUrl = dwSkillUrl;
    }

    public String getIsLicenseLaw() {
        return isLicenseLaw;
    }

    public void setIsLicenseLaw(String isLicenseLaw) {
        this.isLicenseLaw = isLicenseLaw;
    }

    public String getIsLicenseList() {
        return isLicenseList;
    }

    public void setIsLicenseList(String isLicenseList) {
        this.isLicenseList = isLicenseList;
    }

    public String getLicensesupUrlname() {
        return licensesupUrlname;
    }

    public void setLicensesupUrlname(String licensesupUrlname) {
        this.licensesupUrlname = licensesupUrlname;
    }

    public String getLicensesupUrl() {
        return licensesupUrl;
    }

    public void setLicensesupUrl(String licensesupUrl) {
        this.licensesupUrl = licensesupUrl;
    }

    public String getLawSatisfactionUrlname() {
        return lawSatisfactionUrlname;
    }

    public void setLawSatisfactionUrlname(String lawSatisfactionUrlname) {
        this.lawSatisfactionUrlname = lawSatisfactionUrlname;
    }

    public String getLawSatisfactionUrl() {
        return lawSatisfactionUrl;
    }

    public void setLawSatisfactionUrl(String lawSatisfactionUrl) {
        this.lawSatisfactionUrl = lawSatisfactionUrl;
    }
}