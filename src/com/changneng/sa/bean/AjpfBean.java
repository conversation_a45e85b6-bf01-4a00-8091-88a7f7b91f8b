package com.changneng.sa.bean;

/**
 *
 * 案卷评分输出类
 * <AUTHOR>
 *
 */
public class AjpfBean {

	public Integer id ;
	//区划code
	public String areaCode;
	public String areaProvince;
	public String areaCity;
	public String areaCounty;
	//单位区划名称
	public String areaName;
	//区划类别:省级、市级、县级
	public String areaType;
	//所属省
	public String province;
	//所属地市
	public String city;
	//所属县
	public String country;
	//案卷唯一号--决定文书号
	public String fileCode;

	public String areaCodeLeave;

	private String isInCheck; //是否评查案件

	//专家A姓名
	public String expertAName;
	//专家A打分
	public Float expertAScore;
	//专家B姓名
	public String expertBName;
	//专家B打分
	public Float expertBScore;
	//专家合议评审得分
	public Float expertConsiderScore;
	//得分*1.2
	public Float expertconsiderscore2;


	//委员A姓名
	public String expertCommitAName;
	//委员A打分
	public Float expertCommitAScore;
	//委员B姓名
	public String expertCommitBName;
	//委员B打分
	public Float expertCommitBScore;
	//委员AID
	public String expertCommitAId;
	//委员BID
	public String expertCommitBId;

	//交叉评审A姓名
	public String crossReviewNameA;
	//交叉评审B姓名
	public String crossReviewNameB;
	//交叉评审A打分
	public Float crossReviewAScore;
	//交叉评审B打分
	public Float crossReviewBScore;
	//合议评审得分
	public Float crossConsiderScore;

	//交叉合议组长登录帐号
	private String recordCrossUser;

	//含有无需评审的-交叉评审A的打分（即去掉无需评审指标的交叉分）
	public Float inCheckCrossAScore;
	//含有无需评审的-交叉评审B的打分（即去掉无需评审指标的交叉分）
	public Float inCheckCrossBScore;
	//含有无需评审的-A和B的平均分
	public Float inCheckCrossFinalScore;

	//原始交叉分
  	public Float originalCrossScore;
  	//原始专家分
  	public Float originalExpertScore;

  	//案卷类型   随机，推选
  	public Integer reportType;

	/**
	 　* 实体否决项
	 　* <AUTHOR>
	 　* @date 2023-06-20 14:06:07
	 　*/
	public String scoringIndexNames;
	/**
	 　* 专家否决次数
	 　* <AUTHOR>
	 　* @date 2023-06-20 14:06:13
	 　*/
	public Integer expertVetoNumber;

	public String caseCheckState;

	//严重不全情况(导出)
	public String lackCase;
	//严重不全理由(导出)
	public String lackReason;

	public String getLackCase() {
		return lackCase;
	}

	public void setLackCase(String lackCase) {
		this.lackCase = lackCase;
	}

	public String getLackReason() {
		return lackReason;
	}

	public void setLackReason(String lackReason) {
		this.lackReason = lackReason;
	}

	public String getCaseCheckState() {
		return caseCheckState;
	}

	public void setCaseCheckState(String caseCheckState) {
		this.caseCheckState = caseCheckState;
	}

	public String getIndexId() {
		return indexId;
	}

	public void setIndexId(String indexId) {
		this.indexId = indexId;
	}

	public String indexId;


	public String getScoringIndexNames() {
		return scoringIndexNames;
	}

	public void setScoringIndexNames(String scoringIndexNames) {
		this.scoringIndexNames = scoringIndexNames;
	}

	public Integer getExpertVetoNumber() {
		return expertVetoNumber;
	}

	public void setExpertVetoNumber(Integer expertVetoNumber) {
		this.expertVetoNumber = expertVetoNumber;
	}

	public Integer getReportType() {
		return reportType;
	}

	public void setReportType(Integer reportType) {
		this.reportType = reportType;
	}

	public Float getOriginalCrossScore() {
		return originalCrossScore;
	}

	public void setOriginalCrossScore(Float originalCrossScore) {
		this.originalCrossScore = originalCrossScore;
	}

	public Float getOriginalExpertScore() {
		return originalExpertScore;
	}

	public void setOriginalExpertScore(Float originalExpertScore) {
		this.originalExpertScore = originalExpertScore;
	}

	//是否需要合议:0,1
	public String isConsider;
	//最终得分
	public Float finalScore;


    private Integer expertaid;

    private Integer expertbid;

    private Integer crossreviewaid;

    private Integer crossreviewbid;

    private String filetype;

    private String isConsiderCross;

    private String fileTypeName;

    private String fileName;
    private String fileUrl;
    private String downUrl;



	public String getDownUrl() {
		return downUrl;
	}

	public void setDownUrl(String downUrl) {
		this.downUrl = downUrl;
	}

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	public String getFileUrl() {
		return fileUrl;
	}

	public void setFileUrl(String fileUrl) {
		this.fileUrl = fileUrl;
	}

	public String getIsInCheck() {
		return isInCheck;
	}

	public void setIsInCheck(String isInCheck) {
		this.isInCheck = isInCheck;
	}

	public String getExpertCommitAId() {
		return expertCommitAId;
	}

	public void setExpertCommitAId(String expertCommitAId) {
		this.expertCommitAId = expertCommitAId;
	}

	public String getExpertCommitBId() {
		return expertCommitBId;
	}

	public void setExpertCommitBId(String expertCommitBId) {
		this.expertCommitBId = expertCommitBId;
	}

	public String getAreaCodeLeave() {
		return areaCodeLeave;
	}

	public void setAreaCodeLeave(String areaCodeLeave) {
		this.areaCodeLeave = areaCodeLeave;
	}

	public String getExpertCommitAName() {
		return expertCommitAName;
	}

	public void setExpertCommitAName(String expertCommitAName) {
		this.expertCommitAName = expertCommitAName;
	}

	public Float getExpertCommitAScore() {
		return expertCommitAScore;
	}

	public void setExpertCommitAScore(Float expertCommitAScore) {
		this.expertCommitAScore = expertCommitAScore;
	}

	public String getExpertCommitBName() {
		return expertCommitBName;
	}

	public void setExpertCommitBName(String expertCommitBName) {
		this.expertCommitBName = expertCommitBName;
	}

	public Float getExpertCommitBScore() {
		return expertCommitBScore;
	}

	public void setExpertCommitBScore(Float expertCommitBScore) {
		this.expertCommitBScore = expertCommitBScore;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getAreaCode() {
		return areaCode;
	}

	public void setAreaCode(String areaCode) {
		this.areaCode = areaCode;
	}

	public String getAreaName() {
		return areaName;
	}

	public void setAreaName(String areaName) {
		this.areaName = areaName;
	}

	public String getAreaType() {
		return areaType;
	}

	public void setAreaType(String areaType) {
		this.areaType = areaType;
	}

	public String getProvince() {
		return province;
	}

	public void setProvince(String province) {
		this.province = province;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getCountry() {
		return country;
	}

	public void setCountry(String country) {
		this.country = country;
	}

	public String getFileCode() {
		return fileCode;
	}

	public void setFileCode(String fileCode) {
		this.fileCode = fileCode;
	}

	public String getExpertAName() {
		return expertAName;
	}

	public void setExpertAName(String expertAName) {
		this.expertAName = expertAName;
	}

	public Float getExpertAScore() {
		return expertAScore;
	}

	public void setExpertAScore(Float expertAScore) {
		this.expertAScore = expertAScore;
	}

	public String getExpertBName() {
		return expertBName;
	}

	public void setExpertBName(String expertBName) {
		this.expertBName = expertBName;
	}

	public Float getExpertBScore() {
		return expertBScore;
	}

	public void setExpertBScore(Float expertBScore) {
		this.expertBScore = expertBScore;
	}

	public Float getExpertConsiderScore() {
		return expertConsiderScore;
	}

	public void setExpertConsiderScore(Float expertConsiderScore) {
		this.expertConsiderScore = expertConsiderScore;
	}

	public String getCrossReviewNameA() {
		return crossReviewNameA;
	}

	public void setCrossReviewNameA(String crossReviewNameA) {
		this.crossReviewNameA = crossReviewNameA;
	}

	public String getCrossReviewNameB() {
		return crossReviewNameB;
	}

	public void setCrossReviewNameB(String crossReviewNameB) {
		this.crossReviewNameB = crossReviewNameB;
	}

	public Float getCrossReviewAScore() {
		return crossReviewAScore;
	}

	public void setCrossReviewAScore(Float crossReviewAScore) {
		this.crossReviewAScore = crossReviewAScore;
	}

	public Float getCrossReviewBScore() {
		return crossReviewBScore;
	}

	public void setCrossReviewBScore(Float crossReviewBScore) {
		this.crossReviewBScore = crossReviewBScore;
	}

	public Float getCrossConsiderScore() {
		return crossConsiderScore;
	}

	public void setCrossConsiderScore(Float crossConsiderScore) {
		this.crossConsiderScore = crossConsiderScore;
	}

	public String getIsConsider() {
		return isConsider;
	}

	public void setIsConsider(String isConsider) {
		this.isConsider = isConsider;
	}

	public Float getFinalScore() {
		return finalScore;
	}

	public void setFinalScore(Float finalScore) {
		this.finalScore = finalScore;
	}

	public Integer getExpertaid() {
		return expertaid;
	}

	public void setExpertaid(Integer expertaid) {
		this.expertaid = expertaid;
	}

	public Integer getExpertbid() {
		return expertbid;
	}

	public void setExpertbid(Integer expertbid) {
		this.expertbid = expertbid;
	}

	public Integer getCrossreviewaid() {
		return crossreviewaid;
	}

	public void setCrossreviewaid(Integer crossreviewaid) {
		this.crossreviewaid = crossreviewaid;
	}

	public Integer getCrossreviewbid() {
		return crossreviewbid;
	}

	public void setCrossreviewbid(Integer crossreviewbid) {
		this.crossreviewbid = crossreviewbid;
	}

	public String getFiletype() {
		return filetype;
	}

	public void setFiletype(String filetype) {
		this.filetype = filetype;
	}

	public String getIsConsiderCross() {
		return isConsiderCross;
	}

	public void setIsConsiderCross(String isConsiderCross) {
		this.isConsiderCross = isConsiderCross;
	}

	public String getFileTypeName() {
		return fileTypeName;
	}

	public void setFileTypeName(String fileTypeName) {
		this.fileTypeName = fileTypeName;
	}

	public Float getInCheckCrossAScore() {
		return inCheckCrossAScore;
	}

	public void setInCheckCrossAScore(Float inCheckCrossAScore) {
		this.inCheckCrossAScore = inCheckCrossAScore;
	}

	public Float getInCheckCrossBScore() {
		return inCheckCrossBScore;
	}

	public void setInCheckCrossBScore(Float inCheckCrossBScore) {
		this.inCheckCrossBScore = inCheckCrossBScore;
	}

	public Float getInCheckCrossFinalScore() {
		return inCheckCrossFinalScore;
	}

	public void setInCheckCrossFinalScore(Float inCheckCrossFinalScore) {
		this.inCheckCrossFinalScore = inCheckCrossFinalScore;
	}

	public String getRecordCrossUser() {
		return recordCrossUser;
	}

	public void setRecordCrossUser(String recordCrossUser) {
		this.recordCrossUser = recordCrossUser;
	}

	public Float getExpertconsiderscore2() {
		return expertconsiderscore2;
	}

	public void setExpertconsiderscore2(Float expertconsiderscore2) {
		this.expertconsiderscore2 = expertconsiderscore2;
	}

	public String getAreaProvince() {
		return areaProvince;
	}

	public void setAreaProvince(String areaProvince) {
		this.areaProvince = areaProvince;
	}

	public String getAreaCity() {
		return areaCity;
	}

	public void setAreaCity(String areaCity) {
		this.areaCity = areaCity;
	}

	public String getAreaCounty() {
		return areaCounty;
	}

	public void setAreaCounty(String areaCounty) {
		this.areaCounty = areaCounty;
	}
}
