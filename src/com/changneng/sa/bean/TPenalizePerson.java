package com.changneng.sa.bean;

import java.util.Date;

/**
 　* 执法人员与spe总案卷关系表
 　* <AUTHOR>
 　* @date 2023-10-26 19:10:57
 　*/
public class TPenalizePerson {

    private Integer id;

    /** 同步主表id */
    private Integer filespeId;
    /** 执法人员姓名 */
    private String personName;
    /** 案卷类型 */
    private String bizType;
    /** 环境检查执法证号 */
    private Integer envNum;
    /** 地方行政执法证号 */
    private Integer lawNum;
    /** 执法日期 */
    private Date lawEnforceDate;

    private Date createTime;

    private Date updateTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getFilespeId() {
        return filespeId;
    }

    public void setFilespeId(Integer filespeId) {
        this.filespeId = filespeId;
    }

    public String getPersonName() {
        return personName;
    }

    public void setPersonName(String personName) {
        this.personName = personName;
    }

    public String getBizType() {
        return bizType;
    }

    public void setBizType(String bizType) {
        this.bizType = bizType;
    }

    public Integer getEnvNum() {
        return envNum;
    }

    public void setEnvNum(Integer envNum) {
        this.envNum = envNum;
    }

    public Integer getLawNum() {
        return lawNum;
    }

    public void setLawNum(Integer lawNum) {
        this.lawNum = lawNum;
    }

    public Date getLawEnforceDate() {
        return lawEnforceDate;
    }

    public void setLawEnforceDate(Date lawEnforceDate) {
        this.lawEnforceDate = lawEnforceDate;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
