package com.changneng.sa.util;

import java.io.*;
import java.lang.reflect.InvocationTargetException;
import java.net.URLEncoder;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.HSSFCellUtil;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 描述：Excel写操作帮助类
 * <AUTHOR>
 * @since   2010-11-24
 * @version 1.0v
 */
public class ExcelUtil {
    private static final Logger log=Logger.getLogger(ExcelUtil.class);
    /**
     * 功能：将HSSFWorkbook写入Excel文件
     * @param   wb      HSSFWorkbook
     * @param   absPath 写入文件的相对路径
     * @param   wbName  文件名
     */
    public static void writeWorkbook(HSSFWorkbook wb,String fileName){
        FileOutputStream fos=null;
        try {
            fos=new FileOutputStream(fileName);
            wb.write(fos);
        } catch (FileNotFoundException e) {
            log.error(new StringBuffer("[").append(e.getMessage()).append("]").append(e.getCause()));
        } catch (IOException e) {
            log.error(new StringBuffer("[").append(e.getMessage()).append("]").append(e.getCause()));
        } finally{
            try {
                if(fos!=null){
                    fos.close();
                }
            } catch (IOException e) {
                log.error(new StringBuffer("[").append(e.getMessage()).append("]").append(e.getCause()));
            }
        }
    }
    /**
     * 功能：创建HSSFSheet工作簿
     * @param   wb  HSSFWorkbook
     * @param   sheetName   String
     * @return  HSSFSheet
     */
    public static HSSFSheet createSheet(HSSFWorkbook wb,String sheetName){
        HSSFSheet sheet=wb.createSheet(sheetName);
        sheet.setDefaultColumnWidth(12);
        sheet.setGridsPrinted(false);
        sheet.setDisplayGridlines(false);
        return sheet;
    }
    /**
     * 功能：创建HSSFRow
     * @param   sheet   HSSFSheet
     * @param   rowNum  int
     * @param   height  int
     * @return  HSSFRow
     */
    public static HSSFRow createRow(HSSFSheet sheet,int rowNum,int height){
        HSSFRow row=sheet.createRow(rowNum);
        row.setHeight((short)height);
        return row;
    }
    /**
     * 创建单元格样式
     *
     * @param workbook
     *            工作簿
     * @param fontSize
     *            字体大小
     * @return 单元格样式
     */
    private static HSSFCellStyle createCellStyle(HSSFWorkbook workbook, short fontSize) {
        HSSFCellStyle style = workbook.createCellStyle();
        style.setAlignment(HSSFCellStyle.ALIGN_CENTER);// 水平居中
        style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);// 垂直居中
        // 创建字体
        HSSFFont font = workbook.createFont();
        font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);// 加粗字体
        font.setFontHeightInPoints(fontSize);
        // 加载字体
        style.setFont(font);
        return style;
    }

    private static XSSFCellStyle createCellStyle2(XSSFWorkbook workbook, short fontSize) {
        XSSFCellStyle style = workbook.createCellStyle();
        style.setAlignment(XSSFCellStyle.ALIGN_CENTER);// 水平居中
        style.setVerticalAlignment(XSSFCellStyle.VERTICAL_CENTER);// 垂直居中
        // 创建字体
        XSSFFont font = workbook.createFont();
        font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);// 加粗字体
        font.setFontHeightInPoints(fontSize);
        // 加载字体
        style.setFont(font);
        return style;
    }
    /**
     * 功能：创建带边框的CellStyle样式
     * @param   wb              HSSFWorkbook
     * @param   backgroundColor 背景色
     * @param   foregroundColor 前置色
     * @param   font            字体
     * @return  CellStyle
     */
    public static CellStyle createBorderCellStyle(HSSFWorkbook wb,short backgroundColor,short foregroundColor,short halign,Font font){
        CellStyle cs=wb.createCellStyle();
        cs.setAlignment(halign);
        cs.setVerticalAlignment(CellStyle.VERTICAL_CENTER);
        cs.setFillBackgroundColor(backgroundColor);
        cs.setFillForegroundColor(foregroundColor);
        cs.setFillPattern(CellStyle.SOLID_FOREGROUND);
        cs.setFont(font);
        cs.setBorderLeft(CellStyle.BORDER_DASHED);
        cs.setBorderRight(CellStyle.BORDER_DASHED);
        cs.setBorderTop(CellStyle.BORDER_DASHED);
        cs.setBorderBottom(CellStyle.BORDER_DASHED);
        return cs;
    }
    /**
     * 功能：创建CELL
     * @param   row     HSSFRow
     * @param   cellNum int
     * @param   style   HSSFStyle
     * @return  HSSFCell
     */
    public static HSSFCell createCell(HSSFRow row,int cellNum,CellStyle style){
        HSSFCell cell=row.createCell(cellNum);
        cell.setCellStyle(style);
        return cell;
    }
    /**
     * 功能：合并单元格
     * @param   sheet       HSSFSheet
     * @param   firstRow    int
     * @param   lastRow     int
     * @param   firstColumn int
     * @param   lastColumn  int
     * @return  int         合并区域号码
     */
    public static int mergeCell(HSSFSheet sheet,int firstRow,int lastRow,int firstColumn,int lastColumn){
        return sheet.addMergedRegion(new CellRangeAddress(firstRow,lastRow,firstColumn,lastColumn));
    }
    /**
     * 功能：创建字体
     * @param   wb          HSSFWorkbook
     * @param   boldweight  short
     * @param   color       short
     * @return  Font
     */
    public static Font createFont(HSSFWorkbook wb,short boldweight,short color,short size){
        Font font=wb.createFont();
        font.setBoldweight(boldweight);
        font.setColor(color);
        font.setFontHeightInPoints(size);
        return font;
    }
    /**
     * 设置合并单元格的边框样式
     * @param   sheet   HSSFSheet
     * @param   ca      CellRangAddress
     * @param   style   CellStyle
     */
    public static void setRegionStyle(HSSFSheet sheet, CellRangeAddress ca,CellStyle style) {
        for (int i = ca.getFirstRow(); i <= ca.getLastRow(); i++) {
            HSSFRow row = HSSFCellUtil.getRow(i, sheet);
            for (int j = ca.getFirstColumn(); j <= ca.getLastColumn(); j++) {
                HSSFCell cell = HSSFCellUtil.getCell(row, j);
                cell.setCellStyle(style);
            }
        }
    }

    /**
 	 * 获得基本的单元格格式
 	 * @param workbook
 	 * @return normalCS
 	 */
 	public static HSSFCellStyle getNormalCS(HSSFWorkbook workbook) {
 		HSSFCellStyle normalCS = workbook.createCellStyle();
 		HSSFFont font = workbook.createFont();
 		font.setFontHeightInPoints((short) 10);// 设置字体大小
 		normalCS.setFont(font);
 		//normalCS.setAlignment(HSSFCellStyle.ALIGN_LEFT);//表格居左
 		normalCS.setAlignment(HSSFCellStyle.ALIGN_CENTER);//表格居中
 		normalCS.setBorderBottom(HSSFCellStyle.BORDER_THIN);
 		normalCS.setBorderLeft(HSSFCellStyle.BORDER_NONE);
 		normalCS.setBorderRight(HSSFCellStyle.BORDER_THIN);
 		normalCS.setBorderTop(HSSFCellStyle.BORDER_THIN);
 		normalCS.setFillForegroundColor(HSSFColor.WHITE.index);
 		normalCS.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
 		return normalCS;
 	}

 	/**
 	 * 获取单元格
 	 * @param HSSFRow-行
 	 * @param col-列
 	 * @return cell
 	 */
 	public static HSSFCell getCell(HSSFRow row, int col, HSSFCellStyle cs){
 		HSSFCell cell = row.getCell(col);
 		if (cell == null) {
 			cell = row.createCell(col);
 		}
 		//		cell.setEncoding(HSSFCell.ENCODING_UTF_16);
 		if(cs!=null){
 			cell.setCellStyle(cs);
 		}
 		return cell;
 	}
 	/**
 	 * 获取较大集合的长度
 	 * @param list1
 	 * @param list2
 	 * @return
 	 */
	public static int getMaxSize(List list1,List list2){
    	int maxSize = 0;
     	if(list1.size()!=0&&list2.size()!=0){
     		maxSize = (list1.size()>list2.size()?list1.size():list2.size());
     	}
    	return maxSize;
	}

    /**
     * 字符串滤空
     *
     * @param str 需要过滤得字符串
     * @return 过滤后的字符串
     */
    public static String filterNull(final String str) {
        String s = "";
        if (str == null || "null".equals(str.trim())) {
            s = "";
        } else {
            s = new String(str.trim());
        }

        return s;
    }

    /**
     * Double去空
     *
     * @param d 需要过滤得Double
     * @return 过滤后的Double
     */
    public static Double filterNullToDouble(final Double d) {
    	Double s = 0.0;
        if (d == null) {
        	return s;
        } else {
            s = d;
        }
        return s;
    }

    /**
     * 字符串滤空
     *
     * @param str 需要过滤得字符串
     * @return 过滤后的字符串
     */
    public static String filterNull(final Object str) {
        String rs = str == null ? "" : str.toString().trim();
        return rs.equals("null") ? "" : rs;
    }
    /**
     * 字符串滤空
     *
     * @param str 需要过滤得字符串
     * @return 过滤后的字符串
     */
    public static String filterNullToz(final Object str) {
        String rs = str == null ? "0" : str.toString().trim();
        return rs.equals("null") ? "" : rs;
    }

    public static String filterNullToZero(final Object stro) {
        String s = filterNull(stro);
        if ("".equals(s)) {
            s = "0";
        }
        return s;
    }

    /**
     * 根据传入的日期格式化pattern将传入的日期格式化成字符串。
     * @param date    要格式化的日期对象
     * @param pattern 日期格式化pattern
     * @return 格式化后的日期字符串
     */
    public static String format(final Date date, final String pattern) {
        if (null == date) return "";
        DateFormat df = new SimpleDateFormat(pattern);
        return df.format(date);
    }


    /**
     * list<Object> 转 list<Map<String,Object>>
     *
     * @param list
     * @param <T>
     * @return
     */
    public static <T> List<Map<String, Object>> listConvert(List<T> list) {
        List<Map<String, Object>> list_map = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(item -> {
                Map<String, Object> map = null;
                try {
                    map = (Map<String, Object>) PropertyUtils.describe(item);
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                } catch (InvocationTargetException e) {
                    e.printStackTrace();
                } catch (NoSuchMethodException e) {
                    e.printStackTrace();
                }
                list_map.add(map);
            });
        }
        return list_map;
    }

    /**
     * 多行表头
     * dataList：导出的数据；sheetName：表头名称； head0：表头第一行列名；headnum0：第一行合并单元格的参数
     * head1：表头第二行列名；headnum1：第二行合并单元格的参数；detail：导出的表体字段
     *
     */
    public  static void reportMergeXls(HttpServletRequest request,
                                       HttpServletResponse response, List<Map<String, Object>> dataList,
                                       String sheetName, String[] head0, String[] headnum0,
                                       String[] head1, String[] headnum1, String[] detail)
            throws Exception {
        HSSFWorkbook workbook = new HSSFWorkbook();
        // 1、创建标题
        String headRow = sheetName;
        // 1.1、头标题样式
        HSSFCellStyle headstyle = createCellStyle(workbook, (short) 18);
        // 1.2、列标题样式
        HSSFCellStyle style1 = createCellStyle(workbook, (short) 13);
        style1.setWrapText(true);
        // 2、创建工作表
        HSSFSheet sheet = workbook.createSheet(headRow);
        // 设置默认列宽
        sheet.setDefaultColumnWidth(22);
        sheet.setDefaultRowHeightInPoints(22);
        sheet.autoSizeColumn(1, true);
        // 3、创建行
        // 3.1、创建头标题行；并且设置头标题
        HSSFRow row1 = sheet.createRow(0);
        row1.setHeightInPoints(50);
        HSSFCell cell1 = row1.createCell(0);
        // 加载单元格样式
        cell1.setCellStyle(headstyle);
        cell1.setCellValue(headRow);
        // 3.1、创建副标题行；并且设置
        HSSFRow row2 = sheet.createRow(1);
        row2.setHeightInPoints(25);
        HSSFCell cell2 = row2.createCell(0);
        // 3.2、创建列标题行；并且设置列标题
        HSSFRow row3 = sheet.createRow(2);
        // 第一行表头标题
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, head0.length-1));
        HSSFRow row = sheet.createRow(0);
        row.setHeight((short) 0x349);

        HSSFCell cell = row.createCell(0);
        cell.setCellStyle(headstyle);
        CellUtil.setCellValue(cell, sheetName);
        // 第二行表头列名
        row = sheet.createRow(1);
        for (int i = 0; i < head0.length; i++) {
            row.setHeight((short) 0x340);
            cell = row.createCell(i);
            cell.setCellValue(head0[i]);
            cell.setCellStyle(style1);
        }


        // 1.3、普通单元格样式（中文）样式
        HSSFCellStyle style2 =  workbook.createCellStyle();
        //HSSFCellStyle style36 = workbook.createCellStyle();
        style2.setAlignment(HSSFCellStyle.ALIGN_LEFT);// 水平居左
        style2.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);// 垂直居中
        HSSFFont font = workbook.createFont();
        font.setBoldweight(HSSFFont.BOLDWEIGHT_NORMAL);// 加粗字体
        font.setFontHeightInPoints((short) 10);
        style2.setFont(font);

        //动态合并单元格
        for (int i = 0; i < headnum0.length; i++) {
            String[] temp = headnum0[i].split(",");
            Integer startrow = Integer.parseInt(temp[0]);
            Integer overrow = Integer.parseInt(temp[1]);
            Integer startcol = Integer.parseInt(temp[2]);
            Integer overcol = Integer.parseInt(temp[3]);
            sheet.addMergedRegion(new CellRangeAddress(startrow, overrow,
                    startcol, overcol));
        }
        //设置合并单元格的参数并初始化带边框的表头（这样做可以避免因为合并单元格后有的单元格的边框显示不出来）
        row = sheet.createRow(2);//因为下标从0开始，所以这里表示的是excel中的第三行
        for (int i = 0; i < head0.length; i++) {
            cell = row.createCell(i);
            cell.setCellStyle(style1);//设置excel中第四行的1、2、7、8列的边框
            if(i > 1 ) {
                for (int j = 0; j < head1.length; j++) {
                    cell = row.createCell(j + 1);
                    cell.setCellValue(head1[j]);//给excel中第三行的3、4、5、6列赋值（"温度℃", "湿度%", "温度℃", "湿度%"）
                    cell.setCellStyle(style1);//设置excel中第三行的3、4、5、6列的边框
                }
            }
        }
        //动态合并单元格
        for (int i = 0; i < headnum1.length; i++) {
            String[] temp = headnum1[i].split(",");
            Integer startrow = Integer.parseInt(temp[0]);
            Integer overrow = Integer.parseInt(temp[1]);
            Integer startcol = Integer.parseInt(temp[2]);
            Integer overcol = Integer.parseInt(temp[3]);
            sheet.addMergedRegion(new CellRangeAddress(startrow, overrow,
                    startcol, overcol));
        }

        // 设置列值-内容
        for (int i = 0; i < dataList.size(); i++) {
            row = sheet.createRow(i + 3);//标题、时间、表头字段共占了3行，所以在填充数据的时候要加3，也就是数据要从第4行开始填充
            for (int j = 0; j < detail.length; j++) {
                Map tempmap = (HashMap) dataList.get(i);
                Object data = tempmap.get(detail[j]);
                cell = row.createCell(j);
                cell.setCellStyle(style2);
                cell.setCellType(HSSFCell.CELL_TYPE_STRING);
                CellUtil.setCellValue(cell, data);
            }
        }
        String fileName = new String(sheetName.getBytes("gb2312"), "ISO8859-1");
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        workbook.write(baos);
        response.setContentType("application/x-download;charset=utf-8");
        response.addHeader("Content-Disposition", "attachment;filename="
                + fileName + ".xls");
        OutputStream os = response.getOutputStream();
        ByteArrayInputStream bais = new ByteArrayInputStream(baos.toByteArray());
        byte[] b = new byte[1024];
        while ((bais.read(b)) > 0) {
            os.write(b);
        }
        bais.close();
        os.flush();
        os.close();
    }


    /**
     * 多行表头
     * dataList：导出的数据；sheetName：表头名称； head0：表头第一行列名；headnum0：第一行合并单元格的参数
     * head1：表头第二行列名；headnum1：第二行合并单元格的参数；detail：导出的表体字段
     *
     */
    public  static void reportMergeXlsx(HttpServletRequest request,
                                       HttpServletResponse response, List<Map<String, Object>> dataList,
                                       String sheetName, String[] head0, String[] headnum0,
                                       String[] head1, String[] headnum1, String[] detail)
            throws Exception {
        XSSFWorkbook workbook = new XSSFWorkbook();
        // 1、创建标题
        String headRow = sheetName;
        // 1.1、头标题样式
        XSSFCellStyle headstyle = createCellStyle2(workbook, (short) 18);
        // 1.2、列标题样式
        XSSFCellStyle style1 = createCellStyle2(workbook, (short) 13);
        style1.setWrapText(true);
        // 2、创建工作表
        XSSFSheet sheet = workbook.createSheet(headRow);
        // 设置默认列宽
        sheet.setDefaultColumnWidth(22);
        sheet.setDefaultRowHeightInPoints(22);
        sheet.autoSizeColumn(1, true);
        // 3、创建行
        // 3.1、创建头标题行；并且设置头标题
        XSSFRow row1 = sheet.createRow(0);
        row1.setHeightInPoints(50);
        XSSFCell cell1 = row1.createCell(0);
        // 加载单元格样式
        cell1.setCellStyle(headstyle);
        cell1.setCellValue(headRow);
        // 3.1、创建副标题行；并且设置
        XSSFRow row2 = sheet.createRow(1);
        row2.setHeightInPoints(25);
        XSSFCell cell2 = row2.createCell(0);
        // 3.2、创建列标题行；并且设置列标题
        XSSFRow row3 = sheet.createRow(2);
        // 第一行表头标题
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, head0.length-1));
        XSSFRow row = sheet.createRow(0);
        row.setHeight((short) 0x349);

        XSSFCell cell = row.createCell(0);
        cell.setCellStyle(headstyle);
        CellUtil.setXssCellValue(cell, sheetName);
        // 第二行表头列名
        row = sheet.createRow(1);
        for (int i = 0; i < head0.length; i++) {
            row.setHeight((short) 0x340);
            cell = row.createCell(i);
            cell.setCellValue(head0[i]);
            cell.setCellStyle(style1);
        }


        // 1.3、普通单元格样式（中文）样式
        XSSFCellStyle style2 =  workbook.createCellStyle();
        //HSSFCellStyle style36 = workbook.createCellStyle();
        style2.setAlignment(XSSFCellStyle.ALIGN_LEFT);// 水平居左
        style2.setVerticalAlignment(XSSFCellStyle.VERTICAL_CENTER);// 垂直居中
        XSSFFont font = workbook.createFont();
        font.setBoldweight(XSSFFont.BOLDWEIGHT_NORMAL);// 加粗字体
        font.setFontHeightInPoints((short) 10);
        style2.setFont(font);

        //动态合并单元格
        for (int i = 0; i < headnum0.length; i++) {
            String[] temp = headnum0[i].split(",");
            Integer startrow = Integer.parseInt(temp[0]);
            Integer overrow = Integer.parseInt(temp[1]);
            Integer startcol = Integer.parseInt(temp[2]);
            Integer overcol = Integer.parseInt(temp[3]);
            sheet.addMergedRegion(new CellRangeAddress(startrow, overrow,
                    startcol, overcol));
        }
        //设置合并单元格的参数并初始化带边框的表头（这样做可以避免因为合并单元格后有的单元格的边框显示不出来）
        row = sheet.createRow(2);//因为下标从0开始，所以这里表示的是excel中的第三行
        for (int i = 0; i < head0.length; i++) {
            cell = row.createCell(i);
            cell.setCellStyle(style1);//设置excel中第四行的1、2、7、8列的边框
            if(i > 1 ) {
                for (int j = 0; j < head1.length; j++) {
                    cell = row.createCell(j + 1);
                    cell.setCellValue(head1[j]);//给excel中第三行的3、4、5、6列赋值（"温度℃", "湿度%", "温度℃", "湿度%"）
                    cell.setCellStyle(style1);//设置excel中第三行的3、4、5、6列的边框
                }
            }
        }
        //动态合并单元格
        for (int i = 0; i < headnum1.length; i++) {
            String[] temp = headnum1[i].split(",");
            Integer startrow = Integer.parseInt(temp[0]);
            Integer overrow = Integer.parseInt(temp[1]);
            Integer startcol = Integer.parseInt(temp[2]);
            Integer overcol = Integer.parseInt(temp[3]);
            sheet.addMergedRegion(new CellRangeAddress(startrow, overrow,
                    startcol, overcol));
        }

        // 设置列值-内容
        for (int i = 0; i < dataList.size(); i++) {
            row = sheet.createRow(i + 3);//标题、时间、表头字段共占了3行，所以在填充数据的时候要加3，也就是数据要从第4行开始填充
            for (int j = 0; j < detail.length; j++) {
                Map tempmap = (HashMap) dataList.get(i);
                Object data = tempmap.get(detail[j]);
                cell = row.createCell(j);
                cell.setCellStyle(style2);
                cell.setCellType(HSSFCell.CELL_TYPE_STRING);
                CellUtil.setCellValue(cell, data);
            }
        }
        String fileName = new String(sheetName.getBytes("gb2312"), "ISO8859-1");
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        workbook.write(baos);
        response.setContentType("application/x-download;charset=utf-8");
        response.addHeader("Content-Disposition", "attachment;filename="
                + fileName + ".xlsx");
        OutputStream os = response.getOutputStream();
        ByteArrayInputStream bais = new ByteArrayInputStream(baos.toByteArray());
        byte[] b = new byte[1024];
        while ((bais.read(b)) > 0) {
            os.write(b);
        }
        bais.close();
        os.flush();
        os.close();
    }


    /**
     * 多行表头
     * dataList：导出的数据；sheetName：表头名称； head0：表头第一行列名；headnum0：第一行合并单元格的参数
     * head1：表头第二行列名；headnum1：第二行合并单元格的参数；detail：导出的表体字段
     *
     */
    public  static void reportMergeXlsx2(HttpServletRequest request,
                                        HttpServletResponse response, List<Map<String, Object>> dataList,
                                        String sheetName, String[] head0, String[] headnum0,
                                        String[] head1, String[] headnum1, String[] head2, String[] headnum2, String[] detail)
            throws Exception {
        XSSFWorkbook workbook = new XSSFWorkbook();
        // 1、创建标题
        String headRow = sheetName;
        // 1.1、头标题样式
        XSSFCellStyle headstyle = createCellStyle2(workbook, (short) 18);
        // 1.2、列标题样式
        XSSFCellStyle style1 = createCellStyle2(workbook, (short) 13);
        style1.setWrapText(true);
        // 2、创建工作表
        XSSFSheet sheet = workbook.createSheet(headRow);
        // 设置默认列宽
        sheet.setDefaultColumnWidth(28);
        sheet.setDefaultRowHeightInPoints(22);
        sheet.autoSizeColumn(1, true);
        // 3、创建行
        // 3.1、创建头标题行；并且设置头标题
        XSSFRow row1 = sheet.createRow(0);
        row1.setHeightInPoints(50);
        XSSFCell cell1 = row1.createCell(0);
        // 加载单元格样式
        cell1.setCellStyle(headstyle);
        cell1.setCellValue(headRow);
        // 3.1、创建副标题行；并且设置
        XSSFRow row2 = sheet.createRow(1);
        row2.setHeightInPoints(25);
        XSSFCell cell2 = row2.createCell(0);
        // 3.2、创建列标题行；并且设置列标题
        XSSFRow row3 = sheet.createRow(2);
        // 第一行表头标题
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, head0.length-1));
        XSSFRow row = sheet.createRow(0);
        row.setHeight((short) 0x349);

        XSSFCell cell = row.createCell(0);
        cell.setCellStyle(headstyle);
        CellUtil.setXssCellValue(cell, sheetName);
        // 第二行表头列名
        row = sheet.createRow(1);
        for (int i = 0; i < head0.length; i++) {
            row.setHeight((short) 0x340);
            cell = row.createCell(i);
            cell.setCellValue(head0[i]);
            cell.setCellStyle(style1);
        }


        // 1.3、普通单元格样式（中文）样式
        XSSFCellStyle style2 =  workbook.createCellStyle();
        //HSSFCellStyle style36 = workbook.createCellStyle();
        style2.setAlignment(XSSFCellStyle.ALIGN_LEFT);// 水平居左
        style2.setVerticalAlignment(XSSFCellStyle.VERTICAL_CENTER);// 垂直居中
        XSSFFont font = workbook.createFont();
        font.setBoldweight(XSSFFont.BOLDWEIGHT_NORMAL);// 加粗字体
        font.setFontHeightInPoints((short) 10);
        style2.setFont(font);

        //动态合并单元格
        for (int i = 0; i < headnum0.length; i++) {
            String[] temp = headnum0[i].split(",");
            Integer startrow = Integer.parseInt(temp[0]);
            Integer overrow = Integer.parseInt(temp[1]);
            Integer startcol = Integer.parseInt(temp[2]);
            Integer overcol = Integer.parseInt(temp[3]);
            sheet.addMergedRegion(new CellRangeAddress(startrow, overrow,
                    startcol, overcol));
        }
        //设置合并单元格的参数并初始化带边框的表头（这样做可以避免因为合并单元格后有的单元格的边框显示不出来）
        row = sheet.createRow(2);//因为下标从0开始，所以这里表示的是excel中的第三行
        for (int i = 0; i < head0.length; i++) {
            cell = row.createCell(i);
            cell.setCellStyle(style1);//设置excel中第四行的1、2、7、8列的边框
            if(i > 1 ) {
                for (int j = 0; j < head1.length; j++) {
                    cell = row.createCell(j + 1);
                    cell.setCellValue(head1[j]);//给excel中第三行的3、4、5、6列赋值（"温度℃", "湿度%", "温度℃", "湿度%"）
                    cell.setCellStyle(style1);//设置excel中第三行的3、4、5、6列的边框
                }
            }
        }
        //动态合并单元格
        for (int i = 0; i < headnum1.length; i++) {
            String[] temp = headnum1[i].split(",");
            Integer startrow = Integer.parseInt(temp[0]);
            Integer overrow = Integer.parseInt(temp[1]);
            Integer startcol = Integer.parseInt(temp[2]);
            Integer overcol = Integer.parseInt(temp[3]);
            sheet.addMergedRegion(new CellRangeAddress(startrow, overrow,
                    startcol, overcol));
        }


        //设置合并单元格的参数并初始化带边框的表头（这样做可以避免因为合并单元格后有的单元格的边框显示不出来）
        row = sheet.createRow(3);//因为下标从0开始，所以这里表示的是excel中的第四行
        for (int i = 0; i < head0.length; i++) {
            cell = row.createCell(i);
            cell.setCellStyle(style1);//设置excel中第四行的1、2、7、8列的边框
            if(i > 1 ) {
                for (int j = 0; j < head2.length; j++) {
                    cell = row.createCell(j + 1);
                    cell.setCellValue(head2[j]);//给excel中第三行的3、4、5、6列赋值（"温度℃", "湿度%", "温度℃", "湿度%"）
                    cell.setCellStyle(style1);//设置excel中第三行的3、4、5、6列的边框
                }
            }
        }
        //动态合并单元格
        for (int i = 0; i < headnum2.length; i++) {
            String[] temp = headnum2[i].split(",");
            Integer startrow = Integer.parseInt(temp[0]);
            Integer overrow = Integer.parseInt(temp[1]);
            Integer startcol = Integer.parseInt(temp[2]);
            Integer overcol = Integer.parseInt(temp[3]);
            sheet.addMergedRegion(new CellRangeAddress(startrow, overrow,
                    startcol, overcol));
        }

        // 设置列值-内容
        for (int i = 0; i < dataList.size(); i++) {
            row = sheet.createRow(i + 4);//标题、时间、表头字段共占了4行，所以在填充数据的时候要加4，也就是数据要从第5行开始填充
            for (int j = 0; j < detail.length; j++) {
                Map tempmap = (HashMap) dataList.get(i);
                Object data = tempmap.get(detail[j]);
                cell = row.createCell(j);
                cell.setCellStyle(style2);
                cell.setCellType(HSSFCell.CELL_TYPE_STRING);
                CellUtil.setCellValue(cell, data);
            }
        }
        String fileName = new String(sheetName.getBytes("gb2312"), "ISO8859-1");
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        workbook.write(baos);
        response.setContentType("application/x-download;charset=utf-8");
        response.addHeader("Content-Disposition", "attachment;filename="
                + fileName + ".xlsx");
        OutputStream os = response.getOutputStream();
        ByteArrayInputStream bais = new ByteArrayInputStream(baos.toByteArray());
        byte[] b = new byte[1024];
        while ((bais.read(b)) > 0) {
            os.write(b);
        }
        bais.close();
        os.flush();
        os.close();
    }

    /**
     * 多行表头
     * dataList：导出的数据；sheetName：表头名称； head0：表头第一行列名；headnum0：第一行合并单元格的参数
     * head1：表头第二行列名；headnum1：第二行合并单元格的参数；detail：导出的表体字段
     *
     */
    public  static void reportMergeVetoXlsx(HttpServletRequest request,
                                            HttpServletResponse response, List<Map<String, Object>> dataList,
                                            String excelName, String[] head0, String[] headnum0,
                                            String[] head1, String[] headnum1, String[] detail)
            throws Exception {
        //创建工作簿
        HSSFWorkbook workbook = new HSSFWorkbook();
        //创建工作表
        HSSFSheet sheet = workbook.createSheet("Sheet1");
        // 设置默认列宽
        sheet.setDefaultColumnWidth(28);
        sheet.setDefaultRowHeightInPoints(22);
        sheet.autoSizeColumn(1, true);

        HSSFCellStyle headerStyles1 = createCellStyles(workbook, (short) 12);
        HSSFCellStyle headerStyles2 = createCellStyles(workbook, (short) 12);

        // 创建表头  行1
        Row headerRow0 = sheet.createRow(0);
        headerRow0.setHeightInPoints(30);
        for (int i = 0; i < head0.length; i++) {
            Cell headerCell = headerRow0.createCell(i);
            headerCell.setCellStyle(headerStyles1);
            headerCell.setCellValue(head0[i]);
        }
        //合并单元格: 合并表头行1单元格
        for (int i = 0; i < headnum0.length; i++) {
            String[] item = headnum0[i].split(",");
            int rowStart = Integer.parseInt(item[0])-1; // 行起始位置
            int rowEnd = Integer.parseInt(item[1])-1; // 行结束位置
            int colStart = Integer.parseInt(item[2]); // 列起始位置
            int colEnd = Integer.parseInt(item[3]); // 列结束位置
            CellRangeAddress region = new CellRangeAddress(rowStart, rowEnd, colStart, colEnd);
            sheet.addMergedRegion(region);
        }
        // 创建表头  行2
        Row headerRow1 = sheet.createRow(1);
        headerRow1.setHeightInPoints(30);
        for (int i = 0; i < head1.length; i++) {
            Cell headerCell = headerRow1.createCell(i);
            headerCell.setCellStyle(headerStyles2);
            headerCell.setCellValue(head1[i]);
        }
        //合并单元格: 合并表头行2单元格
        for (int i = 0; i < headnum1.length; i++) {
            String[] item = headnum1[i].split(",");
            int rowStart = Integer.parseInt(item[0])-1; // 行起始位置
            int rowEnd = Integer.parseInt(item[1])-1; // 行结束位置
            int colStart = Integer.parseInt(item[2]); // 列起始位置
            int colEnd = Integer.parseInt(item[3]); // 列结束位置
            CellRangeAddress region = new CellRangeAddress(rowStart, rowEnd, colStart, colEnd);
            sheet.addMergedRegion(region);
        }

        // 正文内容样式
        HSSFCellStyle style2 =  workbook.createCellStyle();
        style2.setAlignment(HSSFCellStyle.ALIGN_LEFT);// 水平居左
        style2.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);// 垂直居中
        HSSFFont font = workbook.createFont();
        font.setBoldweight(XSSFFont.BOLDWEIGHT_NORMAL);// 加粗字体
        font.setFontHeightInPoints((short) 12);
        style2.setFont(font);
        // 正文内容 赋值
        int rowNum = 2;
        for (int i = 0; i < dataList.size(); i++) {
            HSSFRow row = sheet.createRow(rowNum++);
            row.setHeightInPoints(20);
            for (int j = 0; j < detail.length; j++) {
                Map tempmap = (HashMap) dataList.get(i);
                Object data = tempmap.get(detail[j]);
                HSSFCell cell = row.createCell(j);
//                cell = row.createCell(j);
                cell.setCellStyle(style2);
                cell.setCellType(HSSFCell.CELL_TYPE_STRING);
                CellUtil.setCellValue(cell, data);
            }
        }


        //将工作簿写入到字节数组中
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        workbook.write(bos);

        String fileName = new String(excelName.getBytes("gb2312"), "ISO8859-1");
        response.setContentType("application/x-download;charset=utf-8");
        response.addHeader("Content-Disposition", "attachment;filename="+fileName+".xlsx");
        OutputStream os = response.getOutputStream();
        ByteArrayInputStream bais = new ByteArrayInputStream(bos.toByteArray());
        byte[] b = new byte[1024];
        while ((bais.read(b)) > 0) {
            os.write(b);
        }
        bais.close();
        os.flush();
        os.close();
        //关闭流
        bos.close();



    }
    private static HSSFCellStyle createCellStyles(HSSFWorkbook workbook, short fontSize) {
        HSSFCellStyle style = workbook.createCellStyle();
        style.setAlignment(XSSFCellStyle.ALIGN_CENTER);// 水平居中
        style.setVerticalAlignment(XSSFCellStyle.VERTICAL_CENTER);// 垂直居中
        // 创建字体
        HSSFFont font = workbook.createFont();
        font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);// 加粗字体
        font.setFontHeightInPoints(fontSize);
        // 加载字体
        style.setFont(font);
        return style;
    }
     /**
     　* 抽取的案卷导出
     　* <AUTHOR>
     　* @date 2023-08-10 19:08:20
     　*/
    public static void exportFilesExcel(HttpServletRequest request, HttpServletResponse response,
                                        List<Map<String, Object>> dataList,
                                        String excelName,
                                        String[] filesHead,
                                        String[] dbEntityVeto) throws Exception {
        //创建工作簿
        HSSFWorkbook workbook = new HSSFWorkbook();
        //创建工作表
        HSSFSheet sheet = workbook.createSheet("Sheet1");
        // 设置默认列宽
        sheet.setDefaultColumnWidth(28);
        sheet.setDefaultRowHeightInPoints(22);
        sheet.autoSizeColumn(1, true);

        HSSFCellStyle headerStyles = createCellStyles(workbook, (short) 12);

        // 创建表头  行1
        Row headerRow0 = sheet.createRow(0);
        headerRow0.setHeightInPoints(30);
        for (int i = 0; i < filesHead.length; i++) {
            Cell headerCell = headerRow0.createCell(i);
            headerCell.setCellStyle(headerStyles);
            headerCell.setCellValue(filesHead[i]);
        }
        // 正文内容样式
        HSSFCellStyle style2 =  workbook.createCellStyle();
        style2.setAlignment(HSSFCellStyle.ALIGN_LEFT);// 水平居左
        style2.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);// 垂直居中
        HSSFFont font = workbook.createFont();
        font.setBoldweight(XSSFFont.BOLDWEIGHT_NORMAL);// 加粗字体
        font.setFontHeightInPoints((short) 12);
        style2.setFont(font);

        // 正文内容 赋值
        int rowNum = 1;
        for (int i = 0; i < dataList.size(); i++) {
            HSSFRow row = sheet.createRow(rowNum++);
            row.setHeightInPoints(20);
            for (int j = 0; j < dbEntityVeto.length; j++) {
                Map tempmap = (HashMap) dataList.get(i);
                Object data = tempmap.get(dbEntityVeto[j]);
                HSSFCell cell = row.createCell(j);
//                cell = row.createCell(j);
                cell.setCellStyle(style2);
                cell.setCellType(HSSFCell.CELL_TYPE_STRING);
                CellUtil.setCellValue(cell, data);
            }
        }
        //将工作簿写入到字节数组中
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        workbook.write(bos);

        String fileName = new String(excelName.getBytes("gb2312"), "ISO8859-1");
        response.setContentType("application/x-download;charset=utf-8");
        response.addHeader("Content-Disposition", "attachment;filename="+fileName+".xlsx");
        OutputStream os = response.getOutputStream();
        ByteArrayInputStream bais = new ByteArrayInputStream(bos.toByteArray());
        byte[] b = new byte[1024];
        while ((bais.read(b)) > 0) {
            os.write(b);
        }
        bais.close();
        os.flush();
        os.close();
        //关闭流
        bos.close();

    }
}