package com.changneng.sa.util;

public class EncryptUtil {

	/**
	 * 密文转明文
	 * @param para
	 * @return
	 */
	public static String decrypt(String para){
		 Encrypt des;
		try {
			des = new Encrypt();
			return des.decrypt(para);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}//自定义密钥   
		return null;
	}
	/**
	 * 密文转明文
	 * @param para
	 * @return
	 */
	public static byte[] decrypt(byte[] para){
		 Encrypt des;
		try {
			des = new Encrypt();
			return des.decrypt(para);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}//自定义密钥   
		return null;
	}
	
	/**
	 * 明文转密文
	 * @param para
	 * @return
	 */
	public static String encrypt(String para){
		 Encrypt des;
			try {
				des = new Encrypt();
				return des.encrypt(para);
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}//自定义密钥   
			return null;
	}
	
	/**
	 * 明文转密文
	 * @param para
	 * @return
	 */
	public static byte[] encrypt(byte[] para){
		 Encrypt des;
			try {
				des = new Encrypt();
				return des.encrypt(para);
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}//自定义密钥   
			return null;
	}
	
	
	public static void main(String[] args) throws Exception {
		// TODO Auto-generated method stub
		System.out.println(encrypt("jcps666666"));
		System.out.println("----------");
		System.out.println(decrypt("09d3e3cfdaa2c41a"));
		System.out.println("****************");
		System.out.println(decrypt("09d3e3cfdaa2c41a"));
		/**
		 * 057bbcdeb77effe210bc78b9749d96b6
			----------
			dlbdb12369
			****************
			888888
		 * 
		 * */
		
	}

}
