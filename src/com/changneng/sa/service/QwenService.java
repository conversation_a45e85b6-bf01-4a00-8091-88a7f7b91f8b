package com.changneng.sa.service;

import com.changneng.sa.bean.QwenLongFiles;

import java.util.List;

public interface QwenService {

    /**
     * 上传文件到百炼千问
     *
     * @param filePath 文件路径
     * @return 返回千问服务器上的文件id
     */
    String updateFileToQwen(String  filePath);

    /**
     * 根据qwen文件Id与qwen-long对话获取结果
     *
     * @param questions 问题列表
     * @param fileId 千问的文件id
     * @return 模型结果列表
     */
    List<String> queryModelWIthQwenFileId(List<String> questions, String fileId)  throws Exception;

    /**
     * 从千问上删除文件
     *
     * @param qwenFileId
     */
    void deleteFileFromQwen(String qwenFileId) throws Exception;

    /**
     * qwen-max多轮对话
     *
     * @param userMessage 用户对话
     * @return qwen-max的多轮回复
     */
    List<String> chatMoreTImes(List<String> userMessage) throws Exception ;

    /**
     * 查询所有已上传到qwen-long的文件信息
     *
     * @return
     * @throws Exception
     */
    List<QwenLongFiles> queryQwenLongFiles() throws Exception;
}
