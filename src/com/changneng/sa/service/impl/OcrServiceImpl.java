package com.changneng.sa.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.changneng.sa.bean.*;
import com.changneng.sa.dao.*;
import com.changneng.sa.service.OcrService;
import com.changneng.sa.service.OcrTextDetailService;
import com.changneng.sa.service.ZjpfService;
import com.changneng.sa.util.FastDFSClient;
import com.changneng.sa.util.FileUtil;
import com.changneng.sa.util.Message;
import com.changneng.sa.util.MoonshotAiUtils2;
import com.changneng.sa.util.MoonshotTest3;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.io.*;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @title
 * @description
 * @date 2025年04月03日 14:28
 */
@Service
public class OcrServiceImpl implements OcrService {

    @Autowired
    private FilesMapper filesMapper;
    @Autowired
    private OcrMapper ocrMapper;
    @Autowired
    private OcrResponseInfoMapper ocrResponseInfoDao;

    @Autowired
    private CaseOverviewMapper caseOverviewMapper;
    @Autowired
    private OcrIdentifyContentMapper ocrIdentifyContentMapper;
    @Autowired
    private OcrTextDetailService detailService;


    @Value("${textin.pdfToMarkdownUrl}")
    private String API_URL ;
    // private static final String APP_ID = "28509db396bfc03787950c2b0b1a290c";
    @Value("${textin.appId}")
    private String APP_ID;
    @Value("${textin.appSecret}")
    private String APP_SECRET;
    // private static final String APP_SECRET = "dd023b027808f35ee5b802ea92829127";

    @Autowired
    private ZjpfService zjpfService;

    @Override
    public void getInfoWithOcr(Integer fileId) throws Exception {
        Files files = null;
        if(fileId != null ){
            files = filesMapper.selectByPrimaryKey(fileId);
        }
        //FastDFSClient fastDFSClient = new FastDFSClient("classpath:config/fdfs_client.conf");
        //byte[] bytes = fastDFSClient.download_file_byte(files.getFileurl());
       // File file = new File("E:\\123\\行政处罚003结案.pdf");
        // byte[] fileContent = java.nio.file.Files.readAllBytes(Paths.get("E:\\123\\行政处罚003结案.pdf"));
        PdfToMarkdownResponse rst = null;
        try (
                FileInputStream fis = new FileInputStream("E:\\123\\行政处罚003结案.pdf");
                ByteArrayOutputStream bos = new ByteArrayOutputStream()
        ) {
            byte[] buffer = new byte[4096]; // 4KB 缓冲区
            int bytesRead;

            // 分块读取文件内容
            while ((bytesRead = fis.read(buffer)) != -1) {
                bos.write(buffer, 0, bytesRead);
            }

            // 获取最终字节数组
            byte[] bytes = bos.toByteArray();
            System.out.println("文件大小（字节）: " + bytes.length);
            rst = convertPdfToMarkdown(bytes, new PdfToMarkdownParams(), false);
        } catch (IOException e) {
            System.err.println("读取文件失败: " + e.getMessage());
        }

        System.out.println(rst);
        if (200!= rst.getCode()) {
            return;
        }
        List<Detail> details = rst.getResult().getDetail();
        try {
        // 查询配置信息
        List<OcrTextlib> textlibs = ocrMapper.queryOcrTextlib("0");
        List<String> indexs = textlibs.stream().map(OcrTextlib::getIndexName).distinct().collect(Collectors.toList());

        Map<String, List<OcrTextlib>> ocrTextlibMap = textlibs.stream().collect(Collectors.groupingBy(OcrTextlib::getIndexName));

        // 解析结果按pageId分组

        Map<Long, List<Detail>> map = details.stream().collect(Collectors.groupingBy(Detail::getPageId, LinkedHashMap::new, Collectors.toCollection(ArrayList::new)));

        Map<String, Long> indexMap = new HashMap<>();

        map.forEach((k,v)->{
            v.forEach(detail -> {
                for (String index : indexs) {
                    if (detail.getText().indexOf(index) > -1) {
                        // 匹配到索引，进行处理
                        indexMap.put(index, k);
                    }
                }
            });
        });
        List<OcrIdentifyContent> ocrIdentifyContents = new ArrayList<>();

            ocrTextlibMap.forEach((k,v)->{
                if (indexMap.containsKey(k)) {
                    // 找到索引对应的pageId
                    Long pageId = indexMap.get(k);
                    List<Detail> detailsByPageId = map.get(pageId);
                    // 解析详情
                    v.forEach(ocrTextlib -> {
                        for (Detail detail : detailsByPageId){
                            if (detail.getText().indexOf(ocrTextlib.getKeyElementsName()) > -1) {
                                // 匹配到关键元素，进行处理
                                if (StringUtils.isNotEmpty(detail.getType())) {
                                    if ("table".equals(detail.getType())) {
                                        String tableText = detail.getText();
                                        if (map.get(pageId + 1) != null && map.get(pageId + 1).size() > 0) {
                                            List<Detail> nextDetails = map.get(pageId + 1);
                                            nextDetails = nextDetails.subList(0, nextDetails.size() > 2 ? 2 : nextDetails.size());
                                            for (Detail nextDetail : nextDetails) {
                                                if (StringUtils.isNotEmpty(nextDetail.getType()) && "table".equals(nextDetail.getType())) {
                                                    if(nextDetail.getText().indexOf("colspan=\"1\"") > -1) {

                                                        tableText = tableText + nextDetail.getText();
                                                    }
                                                    break;
                                                }
                                            }
                                            tableText = tableText.replace("</td>\n</tr></table><table border=\"1\" ><tr>\n<td colspan=\"1\" rowspan=\"1\"></td>\n<td colspan=\"1\" rowspan=\"1\">", "");


                                            OcrIdentifyContent ocrIdentifyContent = new OcrIdentifyContent();
                                            ocrIdentifyContent.setFileId(fileId);
                                            ocrIdentifyContent.setIndexId(ocrTextlib.getIndexId());
                                            ocrIdentifyContent.setIndexName(ocrTextlib.getIndexName());
                                            ocrIdentifyContent.setKeyElementId(ocrTextlib.getKeyElementsId());
                                            ocrIdentifyContent.setKeyElementName(ocrTextlib.getKeyElementsName());

                                            Document doc = Jsoup.parse(tableText);

                                            Elements postalCodeCells = doc.select("td:containsOwn(" + ocrTextlib.getKeyElementsName() + ")");

                                            if (!postalCodeCells.isEmpty()) {
                                                Element cell = postalCodeCells.first().nextElementSibling();
                                                String info = cell.text().trim();
                                                ocrIdentifyContent.setOcrContent(info);
                                            }
                                            if (StringUtils.isNotEmpty(ocrIdentifyContent.getOcrContent())) {
                                                ocrIdentifyContent.setIsSuccess(1);
                                            }
                                            ocrIdentifyContents.add(ocrIdentifyContent);
                                        }
                                    }
                                }
                            }
                        }
                    });
                }
            });
            if (CollectionUtils.isNotEmpty(ocrIdentifyContents)) {
                try {
                    ocrIdentifyContentMapper.insertBatch(ocrIdentifyContents);
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new RuntimeException(e);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }

    }

    @Override
    public PdfToMarkdownResponse getDocMarkdwonInfo(Integer id) throws Exception {
        Files files = null;
        if(id != null ){
            files = filesMapper.selectByPrimaryKey(id);
        }

//        File file = new File(files.getFileurl());
//        FastDFSClient fastDFSClient = new FastDFSClient("classpath:config/fdfs_client.conf");
//        byte[] fileContent = fastDFSClient.download_file_byte(files.getFileurl());
        // File file = new File("E:\\123\\行政处罚003结案.pdf");
        // todo lf 临时测试写死
        // byte[] fileContent = java.nio.file.Files.readAllBytes(Paths.get("E:\\123\\3x2.pdf"));

        File pdfFile = new File(files.getFileurl());

        byte[] fileBytes;
        try (FileInputStream fis = new FileInputStream(pdfFile);
             ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = fis.read(buffer)) != -1) {
                baos.write(buffer, 0, bytesRead);
            }
            fileBytes = baos.toByteArray();
        }

//        byte[] pdfBytes = Files.readAllBytes(pdfFile.toPath());

        System.out.println("文件读取OK"+files.getFileurl());
        PdfToMarkdownResponse rst = convertPdfToMarkdown(fileBytes, new PdfToMarkdownParams(), false);
//        System.out.println(rst);
        return rst;
    }

    @Override
    public void recordOverview(Integer id) {
        {
            Files files = null;
            if (id != null) {
                files = filesMapper.selectByPrimaryKey(id);
            }
            String objectId = "";
            Integer promptTokens = 0;

            Pattern pattern2 = Pattern.compile("具体内容是([：:])\\s*(.+)");

            List<String> phrasesList = Arrays.asList("未识别", "未提到", "没有提到", "没有提供", "没有提及", "未明确提及", "没有明确提及", "未提及", "没有包含");
            // String[] phrases = {"未识别", "未提到", "没有提到", "没有提供", "没有提及", "未明确提及", "没有明确提及", "未提及", "没有包含"};

            HashMap<String, Object> cacheUploadFile = new HashMap<>();
            cacheUploadFile = zjpfService.getCacheUploadFile(files.getOcrFileUrl());
            objectId = String.valueOf(cacheUploadFile.get("objId"));
            promptTokens = (Integer) cacheUploadFile.get("promptTokens");
            try {
                OcrCaseOverview ocrCaseOverview = new OcrCaseOverview();
                ocrCaseOverview.setFileId(id);

                Message guideMessage = new Message("system", "你是 Kimi，由 Moonshot AI 提供的人工智能助手，你更擅长中文和英文的对话。你会为用户提供安全，有帮助，准确的回答。同时，你会拒绝一切涉及恐怖主义，种族歧视，黄色暴力等问题的回答。Moonshot AI 为专有名词，不可翻译成其他语言。");
                Message cacheMessage = new Message("cache", "tag=" + objectId + ";reset_ttl=1200");

                String userContent = buildContentByFileType(files.getFiletype());

                Message probleMessage = new Message("user", userContent);


                List<Message> messages = CollUtil.newArrayList(
                        cacheMessage,
                        guideMessage,
                        probleMessage
                );

                String resp = MoonshotAiUtils2.chat("moonshot-v1-128k", messages, promptTokens);
                JSONArray results = new JSONArray();
                if (resp != null) {
                    System.out.println(resp);
                    String result = JSONUtil.parseObj(resp).getStr("results");
                    System.out.println("获取到的conten内容是：" + result);
                    JSONArray jsonArray = JSONUtil.parseArray(result);
                    System.out.println(jsonArray);
                    results.addAll(jsonArray);
                }

                List<ZjpfServiceImpl.Item> itemList = new ArrayList<>();
                itemList = results.toList(ZjpfServiceImpl.Item.class);

                if (itemList.size() != 8) {
                    return;
                }

                //"具体内容是:"
                String content1 = "";
                String content2 = "";
                String content3 = "";
                String content4 = "";
                String content5 = "";
                String content6 = "";
                String content7 = "";
                String content8 = "";


                //解析识别内容
                Matcher matcher1 = pattern2.matcher(itemList.get(0).getContent());
                if (matcher1.find()) {
                    content1 = matcher1.group(2);

                    if (!phrasesList.contains(content1)) {
                        ocrCaseOverview.setLawObjectName(content1);
                    }
                }

                Matcher matcher2 = pattern2.matcher(itemList.get(1).getContent());
                if (matcher2.find()) {
                    content2 = matcher2.group(2);
                    if (!phrasesList.contains(content2)) {
                        ocrCaseOverview.setLitigantManType(content2);
                    }
                }
                Matcher matcher3 = pattern2.matcher(itemList.get(2).getContent());
                if (matcher3.find()) {
                    content3 = matcher3.group(2);

                    if (!phrasesList.contains(content3)) {
                        ocrCaseOverview.setCaseIntro(content3);
                    }
                }

                Matcher matcher4 = pattern2.matcher(itemList.get(3).getContent());
                if (matcher4.find()) {
                    content4 = matcher4.group(2);

                    if (!phrasesList.contains(content4)) {
                        ocrCaseOverview.setPunishSubjectName(content4);
                    }
                }

                Matcher matcher5 = pattern2.matcher(itemList.get(4).getContent());
                if (matcher5.find()) {
                    content5 = matcher5.group(2);

                    if (!phrasesList.contains(content5)) {
                        ocrCaseOverview.setLawTerm(content5);
                    }
                }
                Matcher matcher6 = pattern2.matcher(itemList.get(5).getContent());
                if (matcher6.find()) {
                    content6 = matcher6.group(2);

                    if (!phrasesList.contains(content6)) {
                        ocrCaseOverview.setLawResp(content6);
                    }
                }
                Matcher matcher7 = pattern2.matcher(itemList.get(6).getContent());
                if (matcher7.find()) {
                    content7 = matcher7.group(2);

                    if (!phrasesList.contains(content7)) {
                        ocrCaseOverview.setPunishType(content7);
                    }
                }
                Matcher matcher8 = pattern2.matcher(itemList.get(7).getContent());
                if (matcher8.find()) {
                    content8 = matcher8.group(2);

                    if (!phrasesList.contains(content8)) {
                        ocrCaseOverview.setLawFacts(content8);
                    }
                }

                List<OcrDocQuestion> timeAxisQuestions = ocrMapper.queryOcrTimeAxisQuestion(files.getFiletype());

                List<List<OcrDocQuestion>> questionList = MoonshotTest3.splitList(timeAxisQuestions, 6);


                JSONArray timeAxisResults = new JSONArray();

                for (int i = 0; i < questionList.size(); i++) {
                    String timeAxisContent = "请根据如下的问题列表提取文件内容:\n" ;
                    for (OcrDocQuestion question : questionList.get(i)){
                        timeAxisContent = timeAxisContent + question.getId() + "、" + question.getQuestionText() + "\n";
                    }
                    timeAxisContent = timeAxisContent + "请使用如下 JSON 格式输出你的回复:\n" +
                            "```\n" +
                            "{\n" +
                            "    \"results\": [\n" +
                            "        {\"id\":问题序号, \"content\":\"回答的内容，例如“具体内容是：未识别到具体日期”\"},\n" +
                            "        {\"id\":问题序号, \"content\":\"回答的内容，例如“具体内容是：2022年8月12日”\"}\n" +
                            "    ]\n" +
                            "}\n" +
                            "```";

                    Message timateMessage = new Message("user", timeAxisContent);

                    List<Message> messages2 = CollUtil.newArrayList(
                            cacheMessage,
                            guideMessage,
                            timateMessage
                    );

                    String docResp = MoonshotAiUtils2.chat("moonshot-v1-128k", messages2, promptTokens);
                    if (docResp != null) {
                        System.out.println(docResp);
                        String result = JSONUtil.parseObj(docResp).getStr("results");
                        System.out.println("获取到的conten内容是：" + result);
                        JSONArray jsonArray = JSONUtil.parseArray(result);
                        System.out.println(jsonArray);
                        timeAxisResults.addAll(jsonArray);
                    }
                    // timeAxisQuestions.get(i).setAnswerText(docResp);
                }

                String timeAxisContent = buildTimeAxisContent(timeAxisQuestions, timeAxisResults);
                ocrCaseOverview.setTimeAxis(timeAxisContent);
                // 保存到数据库
                caseOverviewMapper.saveOcrCaseOverview(ocrCaseOverview);

            } catch (IOException e) {
                throw new RuntimeException(e);
            }

        }
    }

    private String buildTimeAxisContent(List<OcrDocQuestion> timeAxisQuestions, JSONArray timeAxisResults) {
        if (CollectionUtils.isEmpty(timeAxisQuestions)) {
            return null;
        }
        List<ZjpfServiceImpl.Item> itemList = timeAxisResults.toList(ZjpfServiceImpl.Item.class);
        Map<Integer, ZjpfServiceImpl.Item> itemMap = itemList.stream().collect(Collectors.toMap(ZjpfServiceImpl.Item::getId, Function.identity()));

        Map<String, List<OcrDocQuestion>> map = timeAxisQuestions.stream().collect(Collectors.groupingBy(OcrDocQuestion::getSegmentName));
        List<TimeAxisVO> rst = new ArrayList<>();
        map.forEach((k,v)->{
            TimeAxisVO timeAxisVO = new TimeAxisVO();
            timeAxisVO.setText(k);
            timeAxisVO.setSort(v.get(0).getSegementSort());
            List<TimeAxisVO> child = new ArrayList<>();
            v.forEach(question -> {
                TimeAxisVO childVO = new TimeAxisVO();
                childVO.setDocumentName(question.getDocumentName());
                childVO.setSort(question.getSort());
                if (itemMap.containsKey(question.getId())) {
                    childVO.setText(itemMap.get(question.getId()).getContent().replace("具体内容是：",""));
                }
                child.add(childVO);
            });
            timeAxisVO.setChild(child.stream().sorted(Comparator.comparing(TimeAxisVO::getSort)).collect(Collectors.toList()));
            rst.add(timeAxisVO);
        });

        return JSONObject.toJSONString(rst);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<OcrTextDetail> getOcrTextDetailList(Integer id) throws Exception {
        List<OcrTextDetail> details = detailService.getOcrTextDetailByFileId(id);
        if (CollectionUtils.isEmpty(details)) {
            return saveMarkdwonInfo(id, false);
        }
        List<OcrTextDetailCell> detailCells = detailService.getOcrTextDetailCellByFileId(id);
        if (CollectionUtils.isNotEmpty(detailCells)) {
            buildDetailInfo(detailCells, details);
        }
        return details;
    }

    @Override
    public OcrResponseInfo getResponseInfo(String itemId, String quesInfo) {
        String keyword = "具体如下";

        // 找到关键词的索引
        int index = quesInfo.indexOf(keyword);

        // 如果找到关键词，提取关键词前面的内容
        if (index != -1) {
             quesInfo = quesInfo.substring(0, index).trim(); // 使用trim去除多余的空格
            System.out.println("提取的内容为：");
            System.out.println(quesInfo);
        } else {
            System.out.println("未找到关键词，无法提取内容。");
        }

        OcrResponseInfo responseInfo = ocrResponseInfoDao.getResponseInfo(itemId, quesInfo);
        return responseInfo;

    }

    private void buildDetailInfo(List<OcrTextDetailCell> detailCells, List<OcrTextDetail> details) {
        Map<String, List<OcrTextDetailCell>> map = detailCells.stream().collect(Collectors.groupingBy(cell -> cell.getPageId() + "" + cell.getParagraphId()));
        details.forEach(detail -> {
            if (StringUtils.equals("table", detail.getType())) {
                String key = detail.getPageId() + "" + detail.getParagraphId();
                if (map.containsKey(key)) {
                    List<OcrTextDetailCell> cells = map.get(key);
                    detail.setCells(cells);
                }
            }
        });
    }

    public List<OcrTextDetail> saveMarkdwonInfo(Integer id,boolean cleanOldData) throws Exception {
        if (cleanOldData) {
            detailService.deleteOcrTextDetailByFileId(id);
        }
        PdfToMarkdownResponse markdownResponse = getDocMarkdwonInfo(id);
        List<Detail> details = markdownResponse.getResult().getDetail();
        List<OcrTextDetail> ocrTextDetails = new ArrayList<>();
        List<OcrTextDetailCell> detailCells = new ArrayList<>();
        StringBuilder resultAll = new StringBuilder();

        details.forEach(detail -> {
            OcrTextDetail ocrTextDetail = new OcrTextDetail();
            ocrTextDetail.setFileId(id);
            ocrTextDetail.setPageId(detail.getPageId());
            ocrTextDetail.setParagraphId(detail.getParagraphId());
            ocrTextDetail.setType(detail.getType());
            ocrTextDetail.setTextContent(detail.getText());



            if (detail.getPosition() != null) {
                ocrTextDetail.setPosition(detail.getPosition().stream().map(String::valueOf).collect(Collectors.joining(",")));
            }
            if (CollectionUtils.isNotEmpty(detail.getCells())) {
                detail.getCells().forEach(cell -> {
                    resultAll.append(cell.getText());
                    resultAll.append(System.lineSeparator());
                    OcrTextDetailCell ocrTextDetailCell = new OcrTextDetailCell();
                    ocrTextDetailCell.setFileId(id);
                    ocrTextDetailCell.setPageId(detail.getPageId());
                    ocrTextDetailCell.setParagraphId(detail.getParagraphId());
                    ocrTextDetailCell.setRowId(cell.getRow());
                    ocrTextDetailCell.setTextContent(cell.getText());
                    if (cell.getPosition() != null) {
                        ocrTextDetailCell.setPosition(cell.getPosition().stream().map(String::valueOf).collect(Collectors.joining(",")));
                    }
                    detailCells.add(ocrTextDetailCell);
                });
            }else {
                resultAll.append(detail.getText());
                resultAll.append(System.lineSeparator());

            }
            resultAll.append(System.lineSeparator());
//
//            try (BufferedWriter writer = new BufferedWriter(new FileWriter(outputDir + "OCR_" + file.getName().substring(0, file.getName().length() - 4) + ".txt"))) {
//                writer.write(resultAll.toString()); // 写入内容
//            } catch (IOException e) {
//                System.err.println("写入文件时发生错误: " + e.getMessage());
//            }

            ocrTextDetails.add(ocrTextDetail);
        });

        //测试环境。

        FilesWithBLOBs filesWithBLOBs = filesMapper.selectByPrimaryKey(id);
        String url  ="";
        url=  "F:\\DLB2025\\sd\\ocr\\"+filesWithBLOBs.getFilename().substring(0, filesWithBLOBs.getFilename().length() - 4) + ".txt";
        try (BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(url), "UTF-8"))) {
            writer.write(resultAll.toString()); // 写入内容
        } catch (IOException e) {
            System.err.println("写入文件时发生错误: " + e.getMessage());
        }

        Files files = new Files();
        files.setId(id);
        files.setOcrFileUrl(url);
        filesMapper.updateByPrimaryKey(files);


        //------------------------服务器环境。
//         创建一个临时文本文件
//        File tempFile = File.createTempFile("upload_", ".txt");
//        // 使用 OutputStreamWriter 指定 UTF-8 编码
//        try (OutputStreamWriter writer = new OutputStreamWriter(new FileOutputStream(tempFile), "UTF-8")) {
//            writer.write(resultAll.toString()); // 写入内容
//        }
//        FilesWithBLOBs filesWithBLOBs = filesMapper.selectByPrimaryKey(id);
//
//        // 使用 FileInputStream 和 ByteArrayOutputStream 读取文件内容
//        byte[] fileBytes;
//        try (FileInputStream fis = new FileInputStream(tempFile);
//             ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
//            byte[] buffer = new byte[1024];
//            int bytesRead;
//            while ((bytesRead = fis.read(buffer)) != -1) {
//                baos.write(buffer, 0, bytesRead);
//            }
//            fileBytes = baos.toByteArray();
//        }
//
//        // 上传该文本文件
//        FastDFSClient fd = new FastDFSClient("classpath:config/fdfs_client.conf");
//        String  url = fd.uploadFile(fileBytes, "txt"); // 指定文件类型为txt
//
//        Files files = new Files();
//        files.setId(id);
//
//        files.setOcrFileUrl(url.replace("group1/M00","/home/<USER>/storage/data"));
//        files.setFileurl(filesWithBLOBs.getFileurl().replace("/home/<USER>/storage/data","group1/M00"));
//
//        filesMapper.updateByPrimaryKey(files);
////         删除临时文件
//        tempFile.delete();


        buildDetailInfo(detailCells, ocrTextDetails);
        detailService.saveOcrTextDetailAndCell(ocrTextDetails,detailCells);
        return ocrTextDetails;
    }

    private String buildContentByFileType(String filetype) {
        String userContent = null;
        switch (filetype){
            case "0":
                userContent="请根据如下的问题列表提取文件内容：" +
                        "1、只在行政处罚决定书中检索当事人名称，请以固定格式:“具体内容是：”回答是否及具体内容;\n" +
                        "2、根据材料分析当事人类型为以下哪一个：企业、事业单位、个体工商户、社会组织、自然人，请以固定格式:“具体内容是：”回答是否及具体内容;\n" +
                        "3、只在立案审批表中检索案由，请以固定格式:“具体内容是：”回答是否及具体内容;\n" +
                        "4、只在现场检查（勘察）表中检索执法主体，请以固定格式:“具体内容是：”回答是否及具体内容;\n" +
                        "5、只在行政处罚决定书中检索违反条款内容，请以固定格式:“具体内容是：”回答是否及具体内容;\n" +
                        "6、只在行政处罚决定书中检索处罚依据内容，请以固定格式:“具体内容是：”回答是否及具体内容;\n" +
                        "7、只在行政处罚决定书中检索处罚内容，请以固定格式:“具体内容是：”回答是否及具体内容;\n" +
                        "8、只在行政处罚决定书中检索证据内容，请以固定格式:“具体内容是：”回答是否及具体内容;\n"+
                        "请使用如下 JSON 格式输出你的回复:\n" +
                        "```\n" +
                        "{\n" +
                        "    \"results\": [\n" +
                        "        {\"id\":问题序号, \"content\":\"回答的内容，例如“具体内容是：未识别”\"},\n" +
                        "        {\"id\":问题序号, \"content\":\"回答的内容，例如“具体内容是：2022年8月12日”\"}\n" +
                        "    ]\n" +
                        "}\n" +
                        "```"
                        +"注意，其中id是int类型，content是string类型。根据问题序号id依次回答每一个问题，不能有遗漏，如果未识别就按照格式要求返回未识别。"
                ;
                break;
            case "1":
                break;
            case "2":
                break;
            case "3":
                break;
            case "6":
                userContent="请根据如下的问题列表提取文件内容：" +
                        "1、只在查封扣押决定书中检索当事人名称，请以固定格式:“具体内容是：”回答是否及具体内容;\n" +
                        "2、根据材料分析当事人类型为以下哪一个：企业、事业单位、个体工商户、社会组织、自然人，请以固定格式:“具体内容是：”回答是否及具体内容;\n" +
                        "3、只在立案审批表中检索案由，请以固定格式:“具体内容是：”回答是否及具体内容;\n" +
                        "4、只在现场检查（勘察）表中检索执法主体，请以固定格式:“具体内容是：”回答是否及具体内容;\n" +
                        "5、只在查封扣押决定书中检索违反条款内容，请以固定格式:“具体内容是：”回答是否及具体内容;\n" +
                        "6、只在查封扣押决定书中检索处罚依据内容，请以固定格式:“具体内容是：”回答是否及具体内容;\n" +
                        "7、只在查封扣押决定书中检索处罚内容，请以固定格式:“具体内容是：”回答是否及具体内容;\n" +
                        "8、只在查封扣押决定书中检索证据内容，请以固定格式:“具体内容是：”回答是否及具体内容;\n"+
                        "请使用如下 JSON 格式输出你的回复:\n" +
                        "```\n" +
                        "{\n" +
                        "    \"results\": [\n" +
                        "        {\"id\":问题序号, \"content\":\"回答的内容，例如“具体内容是：未识别”\"},\n" +
                        "        {\"id\":问题序号, \"content\":\"回答的内容，例如“具体内容是：2022年8月12日”\"}\n" +
                        "    ]\n" +
                        "}\n" +
                        "```"
                        +"注意，其中id是int类型，content是string类型。根据问题序号id依次回答每一个问题，不能有遗漏，如果未识别就按照格式要求返回未识别。"
                ;
                break;
            case "7":
                break;
            case "9":
                break;
            default:
                break;
        }
        return userContent;
    }

    public PdfToMarkdownResponse convertPdfToMarkdown(Object content, PdfToMarkdownParams params, boolean isUrl) {
        RestTemplate restTemplate = new RestTemplate();

        HttpHeaders headers = new HttpHeaders();
        headers.set("x-ti-app-id", APP_ID);
        headers.set("x-ti-secret-code", APP_SECRET);
        // 修改请求头Content-Type逻辑
        headers.setContentType(isUrl ? MediaType.TEXT_PLAIN : MediaType.APPLICATION_OCTET_STREAM);
        String url = buildUrl(params);
        HttpEntity<Object> requestEntity = new HttpEntity<>(content, headers);

        ResponseEntity<PdfToMarkdownResponse> response = null;
        try {
            response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    requestEntity,
                    PdfToMarkdownResponse.class
            );
        } catch (RestClientException e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }

        return response.getBody();
    }

    private String buildUrl(PdfToMarkdownParams params) {
        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl(API_URL);
        if (params.getPdfPwd() != null) {
            uriBuilder.queryParam("pdf_pwd", params.getPdfPwd());
        }
        if (params.getDpi() != null) {
            uriBuilder.queryParam("dpi", params.getDpi());
        }
        if (params.getPageStart() != null) {
            uriBuilder.queryParam("page_start", params.getPageStart());
        }
        if (params.getPageCount() != null) {
            uriBuilder.queryParam("page_count", params.getPageCount());
        }
        if (params.getApplyDocumentTree() != null) {
            uriBuilder.queryParam("apply_document_tree", params.getApplyDocumentTree());
        }
        if (params.getMarkdownDetails() != null) {
            uriBuilder.queryParam("markdown_details", params.getMarkdownDetails());
        }
        if (params.getTableFlavor() != null) {
            uriBuilder.queryParam("table_flavor", params.getTableFlavor());
        }
        if (params.getGetImage() != null) {
            uriBuilder.queryParam("get_image", params.getGetImage());
        }
        if (params.getParseMode() != null) {
            uriBuilder.queryParam("parse_mode", params.getParseMode());
        }
        return uriBuilder.build().toUriString();
    }
}
