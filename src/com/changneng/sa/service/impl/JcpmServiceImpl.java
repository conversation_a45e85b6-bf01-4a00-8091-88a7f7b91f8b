package com.changneng.sa.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.changneng.sa.bean.ElectionPersonal;
import com.changneng.sa.bean.ElectionUnit;
import com.changneng.sa.bean.ElectionUnitFilesCollect;
import com.changneng.sa.bean.ExpertHandlFileList;
import com.changneng.sa.bean.Files;
import com.changneng.sa.bean.WeightBean;
import com.changneng.sa.dao.ElectionPersonalMapper;
import com.changneng.sa.dao.ElectionUnitMapper;
import com.changneng.sa.dao.ExpertHandlFileListMapper;
import com.changneng.sa.dao.FilesMapper;
import com.changneng.sa.service.JcpmService;
import com.changneng.sa.util.Const;
import com.changneng.sa.util.PageBean;
import com.github.pagehelper.PageHelper;

@Service("jcpmService")
public class JcpmServiceImpl implements JcpmService {

	@Autowired
	public ElectionPersonalMapper personalMapper;
	@Autowired
	public ElectionUnitMapper unitMapper;
	@Autowired
	private FilesMapper filesMapper;
	@Autowired
	private ExpertHandlFileListMapper expertHandlFileListMapper;
	
	@Override
	public String changeElectionPersonalAndelUnit(WeightBean weightBean) {
		StringBuffer sb = new StringBuffer();
		// 省------>17(普通省) 10(直辖市)份案卷； 市------>8(普通市)7(直辖市中区) 份案卷； 县--->7份案卷
		// (直辖市没有县单位)
		unitMapper.updateStandardFileNumber();
		// 交叉 个人
		// 交叉排名不计算个人，则这部分代码注释
		// try {
		// // 交叉排名 分数汇总
		// int countPersonal = personalMapper.updateJcpmPersonalList();
		// // 交叉排名 排名统计
		// int personalRanking = personalMapper.setJcpmPersonRanking();
		// sb.append("交叉个人排名汇分" + countPersonal + "条，排名" +
		// personalRanking+"条；");
		// } catch (Exception e) {
		// sb.append("交叉个人排名汇分错误");
		// e.printStackTrace();
		// }

		// 交叉 集体
		try {
			// 1 :只更新市县下的案卷总数量
			// 这里更新后 质量评分则不需要再次更新 ZlpfServiceImpl
			unitMapper.updateElectionUnitAityAndCountryFileNumber();

			// 普通省
			List<ElectionUnit> provincePtLsit = unitMapper.selectUnitProvinceOrCityPtList("1");
			// 普通市
			List<ElectionUnit> cityPtLsit = unitMapper.selectUnitProvinceOrCityPtList("2");
			// 普通县
			List<ElectionUnit> countryLsit = unitMapper.selectUnitList("3");
			// 直辖市
			List<ElectionUnit> provinceZxLsit = unitMapper.selectUnitProvinceOrCityZxList("1");
			// 直辖市的区县
			List<ElectionUnit> cityZxLsit = unitMapper.selectUnitProvinceOrCityZxList("2");

			// 只统计交叉合议的分值
			String columnName = "crossConsiderScore";

			// 县级案卷质量总分=3份行政处罚案卷总分/3×0.4 + 按日计罚案卷总分×0.15 +移送拘留案卷总分×0.15+
			// 涉嫌犯罪移送案卷总分×0.15+ 申请强制执行案卷总分×0.15
			for (int i = 0; i < countryLsit.size(); i++) {

				Float countryXz = null, countryAr = null, countryYs = null, countrySx = null, countryFy = null;
				ElectionUnit countryBean = countryLsit.get(i);
				// 2计算分值
				if (countryBean.getAreacode() == null)
					continue;
				for (int j = 0; j <= 4; j++) { // 0-4 共计5中类型案卷
					// 案卷都存在这没分现象，没分则赋值基准分， 基准分：未确定
					if (j == 0) // 行政处罚案卷
						countryXz = unitMapper.getSUMScoreByAreacodeAndFileType(countryBean.getAreacode(), j + "",
								columnName);
					if (j == 1)
						countryAr = unitMapper.getSUMScoreByAreacodeAndFileType(countryBean.getAreacode(), j + "",
								columnName);
					if (j == 2)
						countryYs = unitMapper.getSUMScoreByAreacodeAndFileType(countryBean.getAreacode(), j + "",
								columnName);
					if (j == 3)
						countrySx = unitMapper.getSUMScoreByAreacodeAndFileType(countryBean.getAreacode(), j + "",
								columnName);
					if (j == 4)
						countryFy = unitMapper.getSUMScoreByAreacodeAndFileType(countryBean.getAreacode(), j + "",
								columnName);

				}

				if (countryXz == null)
					countryXz = (float) 0;
				if (countryAr == null)
					countryAr = (float) 0;
				if (countryYs == null)
					countryYs = (float) 0;
				if (countrySx == null)
					countrySx = (float) 0;
				// 申请强制执行案卷总分 没有 就默认50分
				if (countryFy == null)
					countryFy = (float) 50;
				Float countryFianScore = (float) AjpfServiceImpl
						.getThreeDecimalDouble((double) (countryXz / 3 * weightBean.getCountryXz()
								+ countryAr * weightBean.getCountryAr() + countryYs * weightBean.getCountryYs()
								+ countrySx * weightBean.getCountrySx() + countryFy * weightBean.getCountryFy()));

				countryBean.setCrosstotalscore(countryFianScore);
				unitMapper.updateByPrimaryKeySelective(countryBean);

			}

			// 市级案卷质量总分=3份行政处罚案卷总分/3×0.25+按日计罚案卷总分×0.15+移送拘留案卷总分×0.15+涉嫌犯罪移送案卷总分×0.15+申请强制执行案卷总分×0.15+稽查案卷总分×0.15
			// 普通市
			for (int i = 0; i < cityPtLsit.size(); i++) {
				Float cityPtXz = null, cityPtAr = null, cityPtYs = null, cityPtSx = null, cityPtFy = null,
						cityPtJc = null;
				ElectionUnit countryBean = cityPtLsit.get(i);
				// 2计算分值
				if (countryBean.getAreacode() == null)
					continue;
				for (int j = 0; j <= 5; j++) { // 0-5 共计6中类型案卷 普通市
					// 案卷都存在这没分现象，没分则赋值基准分， 基准分：未确定
					if (j == 0) // 行政处罚案卷
						cityPtXz = unitMapper.getSUMScoreByAreacodeAndFileType(countryBean.getAreacode(), j + "",
								columnName);
					if (j == 1)
						cityPtAr = unitMapper.getSUMScoreByAreacodeAndFileType(countryBean.getAreacode(), j + "",
								columnName);
					if (j == 2)
						cityPtYs = unitMapper.getSUMScoreByAreacodeAndFileType(countryBean.getAreacode(), j + "",
								columnName);
					if (j == 3)
						cityPtSx = unitMapper.getSUMScoreByAreacodeAndFileType(countryBean.getAreacode(), j + "",
								columnName);
					if (j == 4)
						cityPtFy = unitMapper.getSUMScoreByAreacodeAndFileType(countryBean.getAreacode(), j + "",
								columnName);
					if (j == 5)
						cityPtJc = unitMapper.getSUMScoreByAreacodeAndFileType(countryBean.getAreacode(), j + "",
								columnName);
				}
				if (cityPtXz == null)
					cityPtXz = (float) 0;
				if (cityPtAr == null)
					cityPtAr = (float) 0;
				if (cityPtYs == null)
					cityPtYs = (float) 0;
				if (cityPtSx == null)
					cityPtSx = (float) 0;
				if (cityPtJc == null)
					cityPtJc = (float) 0;

				// 申请强制执行案卷总分 没有 就默认50分
				if (cityPtFy == null)
					cityPtFy = (float) 50;

				Float cityPtFianScore = (float) AjpfServiceImpl.getThreeDecimalDouble(
						(double) (cityPtXz / 3 * weightBean.getCityPtXz() + cityPtAr * weightBean.getCityPtAr()
								+ cityPtYs * weightBean.getCityPtYs() + cityPtSx * weightBean.getCityPtSx()
								+ cityPtFy * weightBean.getCityPtFy() + cityPtJc * weightBean.getCityPtJc()));
				countryBean.setCrosstotalscore(cityPtFianScore);
				unitMapper.updateByPrimaryKeySelective(countryBean);
			}
			// 直辖市区县级案卷质量总分=3份行政处罚案卷总分/3×0.25+按日计罚案卷总分×0.2+移送拘留案卷总分×0.2+涉嫌犯罪移送案卷总分×0.2+申请强制执行案卷总分×0.15
			// 直辖市区县
			for (int i = 0; i < cityZxLsit.size(); i++) {

				Float cityZxXz = null, cityZxAr = null, cityZxYs = null, cityZxSx = null, cityZxFy = null;
				ElectionUnit countryBean = cityZxLsit.get(i);
				// 2计算分值
				if (countryBean.getAreacode() == null)
					continue;
				for (int j = 0; j <= 4; j++) { // 0-4 共计5中类型案卷
					// 案卷都存在这没分现象，没分则赋值基准分， 基准分：未确定
					if (j == 0) // 行政处罚案卷
						cityZxXz = unitMapper.getSUMScoreByAreacodeAndFileType(countryBean.getAreacode(), j + "",
								columnName);
					if (j == 1)
						cityZxAr = unitMapper.getSUMScoreByAreacodeAndFileType(countryBean.getAreacode(), j + "",
								columnName);
					if (j == 2)
						cityZxYs = unitMapper.getSUMScoreByAreacodeAndFileType(countryBean.getAreacode(), j + "",
								columnName);
					if (j == 3)
						cityZxSx = unitMapper.getSUMScoreByAreacodeAndFileType(countryBean.getAreacode(), j + "",
								columnName);
					if (j == 4)
						cityZxFy = unitMapper.getSUMScoreByAreacodeAndFileType(countryBean.getAreacode(), j + "",
								columnName);

				}
				if (cityZxXz == null)
					cityZxXz = (float) 0;
				if (cityZxAr == null)
					cityZxAr = (float) 0;
				if (cityZxYs == null)
					cityZxYs = (float) 0;
				if (cityZxSx == null)
					cityZxSx = (float) 0;

				// 申请强制执行案卷总分 没有 就默认50分
				if (cityZxFy == null)
					cityZxFy = (float) 50;
				Float cityZxFianScore = (float) AjpfServiceImpl
						.getThreeDecimalDouble((double) (cityZxXz / 3 * weightBean.getCityZxXz()
								+ cityZxAr * weightBean.getCityZxAr() + cityZxYs * weightBean.getCityZxYs()
								+ cityZxSx * weightBean.getCityZxSx() + cityZxFy * weightBean.getCityZxFy()));

				countryBean.setCrosstotalscore(cityZxFianScore);
				unitMapper.updateByPrimaryKeySelective(countryBean);

			}
			String crossTotalScore = "crossTotalScore";
			// 省级案卷质量总分=县级案卷质量总分×0.3+市级案卷质量总分×0.3+省级行政处罚案卷总分×0.2+省级稽查案卷总分×0.2
			// 普通省
			for (int i = 0; i < provincePtLsit.size(); i++) {

				ElectionUnit provincePtBean = provincePtLsit.get(i);
				// 2计算分值
				if (provincePtBean.getAreacode() == null)
					continue;
				// 汇总省归属下的所有案卷数量
				Integer provinceNumber = null;
				provinceNumber = unitMapper.getProvinceNumber(provincePtBean.getAreacode());
				if (provinceNumber == null)
					provinceNumber = 0;
				Float provincePtXz = null, provincePtJc = null, provincePtCityScore = null,
						provincePtCountryScore = null;
				provincePtXz = unitMapper.getSUMScoreByAreacodeAndFileType(provincePtBean.getAreacode(), 0 + "",
						columnName);
				provincePtJc = unitMapper.getSUMScoreByAreacodeAndFileType(provincePtBean.getAreacode(), 5 + "",
						columnName);
				// 省下最高分市
				provincePtCityScore = unitMapper.getProvinceGoverningCitySUMScore(provincePtBean.getAreacode(),
						crossTotalScore);
				// 省下最高分县
				provincePtCountryScore = unitMapper.getProvinceGoverningCountySUMScore(provincePtBean.getAreacode(),
						crossTotalScore);

				if (provincePtXz == null)
					provincePtXz = (float) 0;
				if (provincePtJc == null)
					provincePtJc = (float) 0;
				if (provincePtCityScore == null)
					provincePtCityScore = (float) 0;
				if (provincePtCountryScore == null)
					provincePtCountryScore = (float) 0;

				// 总分相加写入数据库
				Float provincePtFianScore = (float) AjpfServiceImpl
						.getThreeDecimalDouble((double) (provincePtCountryScore * weightBean.getProvincePtCountryScore()
								+ provincePtCityScore * weightBean.getProvincePtCityScore()
								+ provincePtXz * weightBean.getProvincePtXz()
								+ provincePtJc * weightBean.getProvincePtJc()));
				provincePtBean.setCrosstotalscore(provincePtFianScore);

				// 总数量
				provincePtBean.setQualityfilenumber(provinceNumber);

				unitMapper.updateByPrimaryKeySelective(provincePtBean);
			}
			// 直辖市案卷质量总分=区县级案卷质量总分×0.6+直辖市行政处罚案卷总分×0.2+直辖市稽查案卷总分×0.2
			// 直辖市
			for (int i = 0; i < provinceZxLsit.size(); i++) {
				ElectionUnit provinceZxBean = provinceZxLsit.get(i);
				// 2计算分值
				if (provinceZxBean.getAreacode() == null)
					continue;
				// 汇总省归属下的所有案卷数量
				Integer provinceNumber = null;
				provinceNumber = unitMapper.getProvinceNumber(provinceZxBean.getAreacode());
				if (provinceNumber == null)
					provinceNumber = 0;
				Float provinceZxXz = null, provinceZxJc = null, provinceZxCityScore = null;
				provinceZxXz = unitMapper.getSUMScoreByAreacodeAndFileType(provinceZxBean.getAreacode(), 0 + "",
						columnName);
				provinceZxJc = unitMapper.getSUMScoreByAreacodeAndFileType(provinceZxBean.getAreacode(), 5 + "",
						columnName);
				// 省下最高分市
				provinceZxCityScore = unitMapper.getProvinceGoverningCitySUMScore(provinceZxBean.getAreacode(),
						crossTotalScore);

				if (provinceZxXz == null)
					provinceZxXz = (float) 0;
				if (provinceZxJc == null)
					provinceZxJc = (float) 0;
				if (provinceZxCityScore == null)
					provinceZxCityScore = (float) 0;

				Float provincePtFianScore = (float) AjpfServiceImpl
						.getThreeDecimalDouble((double) (provinceZxCityScore * weightBean.getProvinceZxCityScore()
								+ provinceZxXz * weightBean.getProvinceZxXz()
								+ provinceZxJc / 2 * weightBean.getProvinceZxJc()));
				provinceZxBean.setCrosstotalscore(provincePtFianScore);
				// 总数量
				provinceZxBean.setQualityfilenumber(provinceNumber);

				unitMapper.updateByPrimaryKeySelective(provinceZxBean);

			}

			// List<ElectionUnit> electionUnitsLsit =
			// unitMapper.selectUnitList("1");
			//
			// for (int i = 0; i < electionUnitsLsit.size(); i++) {
			// ElectionUnit unit = electionUnitsLsit.get(i);
			//
			// String areacode = unit.getAreacode();
			// // 总分数
			// Float provinceScore = null;
			// Float cityScore = null;
			// Float countyScore = null;
			// // 汇总省总分
			// provinceScore = unitMapper.getJcpmProvinceSUMScore(areacode);
			// // 汇总省下市 最高得分的市
			// cityScore = unitMapper.getJcpmCitySUMScore(areacode);
			// // 汇总省下县 最高得分的县
			// countyScore = unitMapper.getJcpmCountySUMScore(areacode);
			// if (provinceScore == null)
			// provinceScore = (float) 0;
			// if (cityScore == null)
			// cityScore = (float) 0;
			// if (countyScore == null)
			// countyScore = (float) 0;
			// // 总分相加写入数据库
			// unit.setCrosstotalscore(provinceScore + cityScore + countyScore);
			// unitMapper.updateByPrimaryKeySelective(unit);
			// }
			// // 汇总 市县的总数量总分数，相互独立
			// unitMapper.updateJcpmElectionUnitlList();
			// // 计算省市县的各条平均分
			// unitMapper.updateJcpmElectionUnitAverage();

			// 计算排名
			int unitRanking = unitMapper.setGroupJcpmRanking();
			sb.append("交叉集体排名汇分" + unitRanking);
		} catch (Exception e) {
			// TODO: handle exception
			e.printStackTrace();
			sb.append("交叉集体排名汇分错误");
		}
		return sb.toString();
	}
	
	/**
	 * 集体==交叉分==汇总以及排名2017
	 */
	@Override
	public String setCrossScorePersonalAndUnit2017(WeightBean weightBean){
		StringBuffer sb = new StringBuffer();
		// 省(直辖市)------>n+3份案卷,n为地级市的数量； 市------>9份(普通市)、7份(直辖市中区) 份案卷； 县--->7份案卷 (直辖市没有县单位)
		
		//置入参选单位应参评的标准案卷数量
		unitMapper.updateStandardFileNumber2017();
		

		// 交叉 集体
		try {
			// 1 :更新省、市、县下的实际的案卷总数量
			// 这里更新后 质量评分则不需要再次更新 ZlpfServiceImpl
			///unitMapper.updateElectionUnitAityAndCountryFileNumber();//-----------------------------------2016
			unitMapper.updateElectionUnitFileNumber2017();

			//查询所有参选单位及各类案卷概要情况
			List<ElectionUnit> allUnitList = unitMapper.getElectionFilesCollect();
			
			for(ElectionUnit unit:allUnitList){//循环所有的参选单位
				
				//定义推选变量
				double countryXzcf = 0.0, countryJc = 0.0, countryYsgajg = 0.0;	
				//定义抽取变量
				double countryXzcf_B = 0.0, countryPtss_B = 0.0;
				
				
				//【交叉】省级案卷质量得分X1=推选案卷（行政处罚案卷A得分×0.5+2份稽查案卷/2×0.5）×0.5+抽选案卷（n份行政处罚案卷B合计得分/n）×0.5
				//这里的行政处罚案卷是泛指，包含（一般行政处罚案卷0、按日计罚1、查封扣押6、停产限产7）
				if("1".equals(unit.getAreatype())){//================================省(直辖市)级
					//该行政区下的各类案卷数量及分数和
					List<ElectionUnitFilesCollect> filesCollect = unit.getFilesCollect();
					
					for(ElectionUnitFilesCollect file:filesCollect ){
						if("0".equals(file.getReporttype())){//======================推选案卷
							if("0".equals(file.getFiletype())||"1".equals(file.getFiletype())||"2".equals(file.getFiletype())||"3".equals(file.getFiletype())
								||"6".equals(file.getFiletype())||"7".equals(file.getFiletype())){//1份行政处罚案卷得分
								countryXzcf += file.getCrossconsiderscoreSum();
							}else if("5".equals(file.getFiletype())){//2份稽查案卷得分
								countryJc += file.getCrossconsiderscoreSum();
							}
						}else if("1".equals(file.getReporttype())){//================抽取案卷
							/*if("0".equals(file.getFiletype())||"1".equals(file.getFiletype())
									||"6".equals(file.getFiletype())||"7".equals(file.getFiletype())){*///n份行政处罚案卷得分,(n=省级应参评案件数-3)
								countryXzcf_B += file.getCrossconsiderscoreSum();
							/*}*/
						}
					}
					int n=0;
					Integer standFileNum = unit.getStandardfilenumber();//应参评案卷数
					if(standFileNum==null||standFileNum==0||standFileNum<=3){
						
					}else{
						n = standFileNum-3;
					}
					if(n>0){
						Double crossTotalScore = (countryXzcf*0.5+countryJc/2*0.5)*0.5+(countryXzcf_B/n)*0.5;
						Float unitCrossScore = (float) AjpfServiceImpl.getFourDecimalDouble( crossTotalScore);
						
						unit.setCrosstotalscore(unitCrossScore);
						unitMapper.updateScoreByPrimaryKey(unit);
						
					}else{
						sb.append(unit.getAreacode()+"省应抽取的地级市数量n为0不合法");
					}
					
				//【交叉】市级案卷质量得分X2=推选案卷（2份行政处罚案卷得分/2×0.5+移送公安案卷×0.3+2份稽查案卷/2×0.2）×0.5+抽选案卷（2份一般行政处罚案卷合计得分/2×0.6+适用四个配套办法案卷/2×0.4）×0.5
				//推选的行政处罚案卷（指一般行政处罚案卷0、按日计罚1、查封扣押6、停产限产7），推选的移送公安机关案卷（指适用行政拘留2、涉嫌犯罪3）；适用四个配套办法（指按日计罚1、查封扣押6、限产停产7、适用行政拘留2、涉嫌犯罪3）
				}else if("2".equals(unit.getAreatype())){//=============================-地市级
					//该行政区下的各类案卷数量及分数和
					List<ElectionUnitFilesCollect> filesCollect = unit.getFilesCollect();
					
					if(unit.getAreacode().startsWith("11")||unit.getAreacode().startsWith("12")||unit.getAreacode().startsWith("31")
							||unit.getAreacode().startsWith("55")||unit.getAreacode().startsWith("66")){//直辖市（兵团）的区县计分方法与普通区县（areaType=3）相同
						
						for(ElectionUnitFilesCollect file:filesCollect ){
							if("0".equals(file.getReporttype())){//======================推选案卷
								if("0".equals(file.getFiletype())||"1".equals(file.getFiletype())
										||"6".equals(file.getFiletype())||"7".equals(file.getFiletype())){//2份行政处罚案卷得分
									countryXzcf += file.getCrossconsiderscoreSum();
								}else if("2".equals(file.getFiletype())||"3".equals(file.getFiletype())){//1份移送公安案卷得分
									countryYsgajg += file.getCrossconsiderscoreSum();
								}
							}else if("1".equals(file.getReporttype())){//================抽取案卷
								if("0".equals(file.getFiletype())){//2份一般行政处罚案得分
									countryXzcf_B += file.getCrossconsiderscoreSum();
								}else if("1".equals(file.getFiletype())||"2".equals(file.getFiletype())||"3".equals(file.getFiletype())
										||"6".equals(file.getFiletype())||"7".equals(file.getFiletype())){//1份适用四个配套办法案卷得分
									countryPtss_B += file.getCrossconsiderscoreSum();
								}
							}
						}
						
						Double crossTotalScore = (countryXzcf/2*0.6+countryYsgajg*0.4)*0.5+(countryXzcf_B/2*0.6+countryPtss_B/2*0.4)*0.5;
						Float unitCrossScore = (float) AjpfServiceImpl.getFourDecimalDouble( crossTotalScore);
						
						unit.setCrosstotalscore(unitCrossScore);
						unitMapper.updateScoreByPrimaryKey(unit);
						
					}else{//普通省下的地级市
						
						for(ElectionUnitFilesCollect file:filesCollect ){
							if("0".equals(file.getReporttype())){//======================推选案卷
								if("0".equals(file.getFiletype())||"1".equals(file.getFiletype())
										||"6".equals(file.getFiletype())||"7".equals(file.getFiletype())){//2份行政处罚案卷得分
									countryXzcf += file.getCrossconsiderscoreSum();
								}else if("2".equals(file.getFiletype())||"3".equals(file.getFiletype())){//1份移送公安案卷得分
									countryYsgajg += file.getCrossconsiderscoreSum();
								}else if("5".equals(file.getFiletype())){//2份稽查案卷得分
									countryJc += file.getCrossconsiderscoreSum();
								}
							}else if("1".equals(file.getReporttype())){//================抽取案卷
								if("0".equals(file.getFiletype())){//2份一般行政处罚案得分
									countryXzcf_B += file.getCrossconsiderscoreSum();
								}else if("1".equals(file.getFiletype())||"2".equals(file.getFiletype())||"3".equals(file.getFiletype())
										||"6".equals(file.getFiletype())||"7".equals(file.getFiletype())){//1份适用四个配套办法案卷得分
									countryPtss_B += file.getCrossconsiderscoreSum();
								}
							}
						}
					
						Double crossTotalScore = (countryXzcf/2*0.5+countryYsgajg*0.3+countryJc/2*0.2)*0.5+(countryXzcf_B/2*0.6+countryPtss_B/2*0.4)*0.5;
						Float unitCrossScore = (float) AjpfServiceImpl.getFourDecimalDouble( crossTotalScore);
						
						unit.setCrosstotalscore(unitCrossScore);
						unitMapper.updateScoreByPrimaryKey(unit);
					}
					
				//【交叉】县级案卷质量得分X3=推选案卷（2份行政处罚案卷得分/2×0.6+移送公安案卷×0.4）×0.5+抽选案卷（2份一般行政处罚案卷合计得分/2×0.6+适用四个配套办法案卷/2×0.4）×0.5
				//推选的行政处罚案卷（指一般行政处罚案卷0、按日计罚1、查封扣押6、停产限产7），推选的移送公安机关案卷（指适用行政拘留2、涉嫌犯罪3）；适用四个配套办法（指按日计罚1、适用行政拘留2、涉嫌犯罪3、查封扣押6、限产停产7）
				}else if("3".equals(unit.getAreatype())){//================================区县级
					//该行政区下的各类案卷数量及分数和
					List<ElectionUnitFilesCollect> filesCollect = unit.getFilesCollect();
					
					for(ElectionUnitFilesCollect file:filesCollect ){
						if("0".equals(file.getReporttype())){//======================推选案卷
							if("0".equals(file.getFiletype())||"1".equals(file.getFiletype())
									||"6".equals(file.getFiletype())||"7".equals(file.getFiletype())){//2份行政处罚案卷得分
								countryXzcf += file.getCrossconsiderscoreSum();
							}else if("2".equals(file.getFiletype())||"3".equals(file.getFiletype())){//1份移送公安案卷得分
								countryYsgajg += file.getCrossconsiderscoreSum();
							}
						}else if("1".equals(file.getReporttype())){//================抽取案卷
							if("0".equals(file.getFiletype())){//2份一般行政处罚案得分
								countryXzcf_B += file.getCrossconsiderscoreSum();
							}else if("1".equals(file.getFiletype())||"2".equals(file.getFiletype())||"3".equals(file.getFiletype())
									||"6".equals(file.getFiletype())||"7".equals(file.getFiletype())){//1份适用四个配套办法案卷得分
								countryPtss_B += file.getCrossconsiderscoreSum();
							}
						}
					}
					
					Double crossTotalScore = (countryXzcf/2*0.6+countryYsgajg*0.4)*0.5+(countryXzcf_B/2*0.6+countryPtss_B/2*0.4)*0.5;
					Float unitCrossScore = (float) AjpfServiceImpl.getFourDecimalDouble( crossTotalScore);
					
					unit.setCrosstotalscore(unitCrossScore);
					unitMapper.updateScoreByPrimaryKey(unit);
				}
			}

			// 按省、市、县计算交叉排名
			int unitRanking = unitMapper.setGroupJcpmRanking();
			sb.append("交叉集体排名汇分成功" + unitRanking);
		} catch (Exception e) {
			e.printStackTrace();
			sb.append("交叉集体排名汇分错误");
		}
		return sb.toString();
	}

	@Override
	public PageBean<ElectionUnit> selectUnitList(String areaType, int pageNum) {
		// TODO Auto-generated method stub

		if (pageNum == 0)
			pageNum = 1;
		// Const.NOPERPAGE
		PageHelper.startPage(pageNum, Const.NOPERPAGE);
		return new PageBean<ElectionUnit>(unitMapper.selectJcpmUnitList(areaType));
	}
	
	
	@Override
	public PageBean<ElectionUnit> selectUnitZjpmList(String areaType, int pageNum) {
		// TODO Auto-generated method stub

		if (pageNum == 0)
			pageNum = 1;
		// Const.NOPERPAGE
		PageHelper.startPage(pageNum, Const.NOPERPAGE);
		return new PageBean<ElectionUnit>(unitMapper.selectZjpmUnitList(areaType));
	}
	
	
	

	@Override
	public PageBean<ElectionPersonal> selectPersonalList(String areaType, int pageNum) {
		// TODO Auto-generated method stub
		if (pageNum == 0)
			pageNum = 1;
		// Const.NOPERPAGE
		PageHelper.startPage(pageNum, Const.NOPERPAGE);
		return new PageBean<ElectionPersonal>(personalMapper.selectJcpmPersonalList(areaType));
	}

	@Override
	public List<ElectionUnit> selectUnitByAreaType(String areaType) {
		// TODO Auto-generated method stub
		return unitMapper.selectJcpmUnitList(areaType);
	}
	
	
	@Override
	public List<ElectionUnit> selectUnitZjpmByAreaType(String areaType) {
		// TODO Auto-generated method stub
		return unitMapper.selectZjpmUnitList(areaType);
	}
	
	@Override
	public List<ElectionPersonal> selectElectionPersonalByAreaType(String areaType) {
		// TODO Auto-generated method stub
		return personalMapper.selectJcpmPersonalList(areaType);
	}

	@Override
	public String changeElectionPersonalAndelUnit2(WeightBean weightBean) {
		// TODO Auto-generated method stub
		StringBuffer sb = new StringBuffer();

		// 交叉 集体
		try {
			// 普通省
			List<ElectionUnit> provincePtLsit = unitMapper.selectUnitProvinceOrCityPtList("1");
			// 普通市
			List<ElectionUnit> cityPtLsit = unitMapper.selectUnitProvinceOrCityPtList("2");
			// 普通县
			List<ElectionUnit> countryLsit = unitMapper.selectUnitList("3");
			// 直辖市
			List<ElectionUnit> provinceZxLsit = unitMapper.selectUnitProvinceOrCityZxList("1");
			// 直辖市的区县
			List<ElectionUnit> cityZxLsit = unitMapper.selectUnitProvinceOrCityZxList("2");

			// 只统计交叉合议的分值
			String columnName = "expertConsiderScore";

			// 县级案卷质量总分=3份行政处罚案卷总分/3×0.4 + 按日计罚案卷总分×0.15 +移送拘留案卷总分×0.15+
			// 涉嫌犯罪移送案卷总分×0.15+ 申请强制执行案卷总分×0.15
			for (int i = 0; i < countryLsit.size(); i++) {

				Float countryXz = null, countryAr = null, countryYs = null, countrySx = null, countryFy = null;
				ElectionUnit countryBean = countryLsit.get(i);
				// 2计算分值
				if (countryBean.getAreacode() == null)
					continue;
				for (int j = 0; j <= 4; j++) { // 0-4 共计5中类型案卷
					// 案卷都存在这没分现象，没分则赋值基准分， 基准分：未确定
					if (j == 0) // 行政处罚案卷
						countryXz = unitMapper.getSUMScoreByAreacodeAndFileType(countryBean.getAreacode(), j + "",
								columnName);
					if (j == 1)
						countryAr = unitMapper.getSUMScoreByAreacodeAndFileType(countryBean.getAreacode(), j + "",
								columnName);
					if (j == 2)
						countryYs = unitMapper.getSUMScoreByAreacodeAndFileType(countryBean.getAreacode(), j + "",
								columnName);
					if (j == 3)
						countrySx = unitMapper.getSUMScoreByAreacodeAndFileType(countryBean.getAreacode(), j + "",
								columnName);
					if (j == 4)
						countryFy = unitMapper.getSUMScoreByAreacodeAndFileType(countryBean.getAreacode(), j + "",
								columnName);

				}

				if (countryXz == null)
					countryXz = (float) 0;
				if (countryAr == null)
					countryAr = (float) 0;
				if (countryYs == null)
					countryYs = (float) 0;
				if (countrySx == null)
					countrySx = (float) 0;
				// 申请强制执行案卷总分 没有 就默认50分
				if (countryFy == null)
					countryFy = (float) 50;
				Float countryFianScore = (float) AjpfServiceImpl
						.getThreeDecimalDouble((double) (countryXz / 3 * weightBean.getCountryXz()
								+ countryAr * weightBean.getCountryAr() + countryYs * weightBean.getCountryYs()
								+ countrySx * weightBean.getCountrySx() + countryFy * weightBean.getCountryFy()));

				countryBean.setExpertTotalScore(countryFianScore);
				unitMapper.updateByPrimaryKeySelective(countryBean);
			}

			// 市级案卷质量总分=3份行政处罚案卷总分/3×0.25+按日计罚案卷总分×0.15+移送拘留案卷总分×0.15+涉嫌犯罪移送案卷总分×0.15+申请强制执行案卷总分×0.15+稽查案卷总分×0.15
			// 普通市
			for (int i = 0; i < cityPtLsit.size(); i++) {
				Float cityPtXz = null, cityPtAr = null, cityPtYs = null, cityPtSx = null, cityPtFy = null,
						cityPtJc = null;
				ElectionUnit countryBean = cityPtLsit.get(i);
				// 2计算分值
				if (countryBean.getAreacode() == null)
					continue;
				for (int j = 0; j <= 5; j++) { // 0-5 共计6中类型案卷 普通市
					// 案卷都存在这没分现象，没分则赋值基准分， 基准分：未确定
					if (j == 0) // 行政处罚案卷
						cityPtXz = unitMapper.getSUMScoreByAreacodeAndFileType(countryBean.getAreacode(), j + "",
								columnName);
					if (j == 1)
						cityPtAr = unitMapper.getSUMScoreByAreacodeAndFileType(countryBean.getAreacode(), j + "",
								columnName);
					if (j == 2)
						cityPtYs = unitMapper.getSUMScoreByAreacodeAndFileType(countryBean.getAreacode(), j + "",
								columnName);
					if (j == 3)
						cityPtSx = unitMapper.getSUMScoreByAreacodeAndFileType(countryBean.getAreacode(), j + "",
								columnName);
					if (j == 4)
						cityPtFy = unitMapper.getSUMScoreByAreacodeAndFileType(countryBean.getAreacode(), j + "",
								columnName);
					if (j == 5)
						cityPtJc = unitMapper.getSUMScoreByAreacodeAndFileType(countryBean.getAreacode(), j + "",
								columnName);
				}
				if (cityPtXz == null)
					cityPtXz = (float) 0;
				if (cityPtAr == null)
					cityPtAr = (float) 0;
				if (cityPtYs == null)
					cityPtYs = (float) 0;
				if (cityPtSx == null)
					cityPtSx = (float) 0;
				if (cityPtJc == null)
					cityPtJc = (float) 0;

				// 申请强制执行案卷总分 没有 就默认50分
				if (cityPtFy == null)
					cityPtFy = (float) 50;

				Float cityPtFianScore = (float) AjpfServiceImpl.getThreeDecimalDouble(
						(double) (cityPtXz / 3 * weightBean.getCityPtXz() + cityPtAr * weightBean.getCityPtAr()
								+ cityPtYs * weightBean.getCityPtYs() + cityPtSx * weightBean.getCityPtSx()
								+ cityPtFy * weightBean.getCityPtFy() + cityPtJc * weightBean.getCityPtJc()));
				countryBean.setExpertTotalScore(cityPtFianScore);
				unitMapper.updateByPrimaryKeySelective(countryBean);
			}
			// 直辖市区县级案卷质量总分=3份行政处罚案卷总分/3×0.25+按日计罚案卷总分×0.2+移送拘留案卷总分×0.2+涉嫌犯罪移送案卷总分×0.2+申请强制执行案卷总分×0.15
			// 直辖市区县
			for (int i = 0; i < cityZxLsit.size(); i++) {

				Float cityZxXz = null, cityZxAr = null, cityZxYs = null, cityZxSx = null, cityZxFy = null;
				ElectionUnit countryBean = cityZxLsit.get(i);
				// 2计算分值
				if (countryBean.getAreacode() == null)
					continue;
				for (int j = 0; j <= 4; j++) { // 0-4 共计5中类型案卷
					// 案卷都存在这没分现象，没分则赋值基准分， 基准分：未确定
					if (j == 0) // 行政处罚案卷
						cityZxXz = unitMapper.getSUMScoreByAreacodeAndFileType(countryBean.getAreacode(), j + "",
								columnName);
					if (j == 1)
						cityZxAr = unitMapper.getSUMScoreByAreacodeAndFileType(countryBean.getAreacode(), j + "",
								columnName);
					if (j == 2)
						cityZxYs = unitMapper.getSUMScoreByAreacodeAndFileType(countryBean.getAreacode(), j + "",
								columnName);
					if (j == 3)
						cityZxSx = unitMapper.getSUMScoreByAreacodeAndFileType(countryBean.getAreacode(), j + "",
								columnName);
					if (j == 4)
						cityZxFy = unitMapper.getSUMScoreByAreacodeAndFileType(countryBean.getAreacode(), j + "",
								columnName);

				}
				if (cityZxXz == null)
					cityZxXz = (float) 0;
				if (cityZxAr == null)
					cityZxAr = (float) 0;
				if (cityZxYs == null)
					cityZxYs = (float) 0;
				if (cityZxSx == null)
					cityZxSx = (float) 0;

				// 申请强制执行案卷总分 没有 就默认50分
				if (cityZxFy == null)
					cityZxFy = (float) 50;
				Float cityZxFianScore = (float) AjpfServiceImpl
						.getThreeDecimalDouble((double) (cityZxXz / 3 * weightBean.getCityZxXz()
								+ cityZxAr * weightBean.getCityZxAr() + cityZxYs * weightBean.getCityZxYs()
								+ cityZxSx * weightBean.getCityZxSx() + cityZxFy * weightBean.getCityZxFy()));

				countryBean.setExpertTotalScore(cityZxFianScore);
				unitMapper.updateByPrimaryKeySelective(countryBean);

			}
			String crossTotalScore = "expertTotalScore";
			// 省级案卷质量总分=县级案卷质量总分×0.3+市级案卷质量总分×0.3+省级行政处罚案卷总分×0.2+省级稽查案卷总分×0.2
			// 普通省
			for (int i = 0; i < provincePtLsit.size(); i++) {

				ElectionUnit provincePtBean = provincePtLsit.get(i);
				// 2计算分值
				if (provincePtBean.getAreacode() == null)
					continue;
				// 汇总省归属下的所有案卷数量
				Integer provinceNumber = null;
				provinceNumber = unitMapper.getProvinceNumber(provincePtBean.getAreacode());
				if (provinceNumber == null)
					provinceNumber = 0;
				Float provincePtXz = null, provincePtJc = null, provincePtCityScore = null,
						provincePtCountryScore = null;
				provincePtXz = unitMapper.getSUMScoreByAreacodeAndFileType(provincePtBean.getAreacode(), 0 + "",
						columnName);
				provincePtJc = unitMapper.getSUMScoreByAreacodeAndFileType(provincePtBean.getAreacode(), 5 + "",
						columnName);
				// 省下最高分市
				provincePtCityScore = unitMapper.getProvinceGoverningCitySUMScore(provincePtBean.getAreacode(),
						crossTotalScore);
				// 省下最高分县
				provincePtCountryScore = unitMapper.getProvinceGoverningCountySUMScore(provincePtBean.getAreacode(),
						crossTotalScore);

				if (provincePtXz == null)
					provincePtXz = (float) 0;
				if (provincePtJc == null)
					provincePtJc = (float) 0;
				if (provincePtCityScore == null)
					provincePtCityScore = (float) 0;
				if (provincePtCountryScore == null)
					provincePtCountryScore = (float) 0;

				// 总分相加写入数据库
				Float provincePtFianScore = (float) AjpfServiceImpl
						.getThreeDecimalDouble((double) (provincePtCountryScore * weightBean.getProvincePtCountryScore()
								+ provincePtCityScore * weightBean.getProvincePtCityScore()
								+ provincePtXz * weightBean.getProvincePtXz()
								+ provincePtJc * weightBean.getProvincePtJc()));
				provincePtBean.setExpertTotalScore(provincePtFianScore);

				// 总数量
				//provincePtBean.setQualityfilenumber(provinceNumber);

				unitMapper.updateByPrimaryKeySelective(provincePtBean);
			}
			// 直辖市案卷质量总分=区县级案卷质量总分×0.6+直辖市行政处罚案卷总分×0.2+直辖市稽查案卷总分×0.2
			// 直辖市
			for (int i = 0; i < provinceZxLsit.size(); i++) {
				ElectionUnit provinceZxBean = provinceZxLsit.get(i);
				// 2计算分值
				if (provinceZxBean.getAreacode() == null)
					continue;
				// 汇总省归属下的所有案卷数量
				Integer provinceNumber = null;
				provinceNumber = unitMapper.getProvinceNumber(provinceZxBean.getAreacode());
				if (provinceNumber == null)
					provinceNumber = 0;
				Float provinceZxXz = null, provinceZxJc = null, provinceZxCityScore = null;
				provinceZxXz = unitMapper.getSUMScoreByAreacodeAndFileType(provinceZxBean.getAreacode(), 0 + "",
						columnName);
				provinceZxJc = unitMapper.getSUMScoreByAreacodeAndFileType(provinceZxBean.getAreacode(), 5 + "",
						columnName);
				// 省下最高分市
				provinceZxCityScore = unitMapper.getProvinceGoverningCitySUMScore(provinceZxBean.getAreacode(),
						crossTotalScore);

				if (provinceZxXz == null)
					provinceZxXz = (float) 0;
				if (provinceZxJc == null)
					provinceZxJc = (float) 0;
				if (provinceZxCityScore == null)
					provinceZxCityScore = (float) 0;

				Float provincePtFianScore = (float) AjpfServiceImpl
						.getThreeDecimalDouble((double) (provinceZxCityScore * weightBean.getProvinceZxCityScore()
								+ provinceZxXz * weightBean.getProvinceZxXz()
								+ provinceZxJc / 2 * weightBean.getProvinceZxJc()));
				provinceZxBean.setExpertTotalScore(provincePtFianScore);
				// 总数量
				//provinceZxBean.setQualityfilenumber(provinceNumber);

				unitMapper.updateByPrimaryKeySelective(provinceZxBean);

			}
 
			// 计算排名
			int unitRanking = unitMapper.setGroupJcpmRanking2();
			sb.append("专家集体排名汇分" + unitRanking);
		} catch (Exception e) {
			// TODO: handle exception
			e.printStackTrace();
			sb.append("交叉集体排名汇分错误");
		}
		return sb.toString();
	}
	
	/**
	 * 集体==专家分==汇总以及排名2017
	 */
	@Override
	public String setExpertScorePersonalAndUnit2017(WeightBean weightBean){
		StringBuffer sb = new StringBuffer();
		// 省(直辖市)------>n+3份案卷,n为地级市的数量； 市------>9份(普通市)、7份(直辖市中区) 份案卷； 县--->7份案卷 (直辖市没有县单位)
		
		//置入参选单位应参评的标准案卷数量
		unitMapper.updateStandardFileNumber2017();
		

		//专家==集体
		try {
			// 1 :只更新市县下的实际的案卷总数量
			// 这里更新后 质量评分则不需要再次更新 ZlpfServiceImpl
			unitMapper.updateElectionUnitFileNumber2017();

			//查询所有参选单位及各类案卷概要情况
			List<ElectionUnit> allUnitList = unitMapper.getElectionFilesCollect();
			
			for(ElectionUnit unit:allUnitList){//循环所有的参选单位
				
				//定义推选变量
				double countryXzcf = 0.0, countryYsgajg = 0.0;	
				//定义抽取变量
				double countryXzcf_B = 0.0, countryPtss_B = 0.0;
				
				
				//【专家】省级案卷质量得分Y1=推选案卷（行政处罚案卷得分）×0.5+抽选案卷（n份行政处罚案卷合计得分/n）×0.5
				//这里的行政处罚案卷是泛指，包含（一般行政处罚案卷0、按日计罚1、查封扣押6、停产限产7）
				if("1".equals(unit.getAreatype())){//================================省(直辖市)级
					//该行政区下的各类案卷数量及分数和
					List<ElectionUnitFilesCollect> filesCollect = unit.getFilesCollect();
					
					for(ElectionUnitFilesCollect file:filesCollect ){
						if("0".equals(file.getReporttype())){//======================推选案卷
							/*if("0".equals(file.getFiletype())||"1".equals(file.getFiletype())
								||"6".equals(file.getFiletype())||"7".equals(file.getFiletype())){//1份行政处罚案卷得分
*/								countryXzcf += file.getExpertconsiderscoreSum();
							/*}*/
						}else if("1".equals(file.getReporttype())){//================抽取案卷
							/*if("0".equals(file.getFiletype())||"1".equals(file.getFiletype())
									||"6".equals(file.getFiletype())||"7".equals(file.getFiletype())){//n份行政处罚案卷得分,(n=省级应参评案件数-3)
*/								countryXzcf_B += file.getExpertconsiderscoreSum();
							/*}*/
						}
					}
					int n=0;
					Integer standFileNum = unit.getStandardfilenumber();//应参评案卷数
					if(standFileNum==null||standFileNum==0||standFileNum<=3){
						
					}else{
						n = standFileNum-3;
					}
					if(n>0){
						Double expertTotalScore = (countryXzcf*0.5)+(countryXzcf_B/n)*0.5;
						Float unitExpertScore = (float) AjpfServiceImpl.getFourDecimalDouble( expertTotalScore);
						
						unit.setExpertTotalScore(unitExpertScore);
						unitMapper.updateScoreByPrimaryKey(unit);
						
					}else{
						sb.append(unit.getAreacode()+"省应抽取的地级市数量n为0不合法");
					}
					
				//【专家】市级案卷质量得分Y2=推选案卷（2份行政处罚案卷得分/2×0.6+移送公安案卷×0.4）×0.6+抽选案卷（2份一般行政处罚案卷合计得分/2×0.5+适用四个配套办法案卷/2×0.4）×0.5
				//推选的行政处罚案卷（指一般行政处罚案卷0、按日计罚1、查封扣押6、停产限产7），推选的移送公安机关案卷（指适用行政拘留2、涉嫌犯罪3）；适用四个配套办法（指按日计罚1、查封扣押6、限产停产7、适用行政拘留2、涉嫌犯罪3）
				}else if("2".equals(unit.getAreatype())){//=============================-地市级
					//该行政区下的各类案卷数量及分数和
					List<ElectionUnitFilesCollect> filesCollect = unit.getFilesCollect();
					
					if(unit.getAreacode().startsWith("11")||unit.getAreacode().startsWith("12")||unit.getAreacode().startsWith("31")
							||unit.getAreacode().startsWith("55")||unit.getAreacode().startsWith("66")){//直辖市（兵团）的区县计分方法与普通区县（areaType=3）相同
						
						for(ElectionUnitFilesCollect file:filesCollect ){
							if("0".equals(file.getReporttype())){//======================推选案卷
								if("0".equals(file.getFiletype())||"1".equals(file.getFiletype())
										||"6".equals(file.getFiletype())||"7".equals(file.getFiletype())){//2份行政处罚案卷得分
									countryXzcf += file.getExpertconsiderscoreSum();
								}else if("2".equals(file.getFiletype())||"3".equals(file.getFiletype())){//1份移送公安案卷得分
									countryYsgajg += file.getExpertconsiderscoreSum();
								}
							}else if("1".equals(file.getReporttype())){//================抽取案卷
								if("0".equals(file.getFiletype())){//2份一般行政处罚案得分
									countryXzcf_B += file.getExpertconsiderscoreSum();
								}else if("1".equals(file.getFiletype())||"2".equals(file.getFiletype())||"3".equals(file.getFiletype())
										||"6".equals(file.getFiletype())||"7".equals(file.getFiletype())){//1份适用四个配套办法案卷得分
									countryPtss_B += file.getExpertconsiderscoreSum();
								}
							}
						}
						
						Double expertTotalScore = (countryXzcf/2*0.6+countryYsgajg*0.4)*0.5+(countryXzcf_B/2*0.6+countryPtss_B/2*0.4)*0.5;
						Float unitExpertScore = (float) AjpfServiceImpl.getFourDecimalDouble( expertTotalScore);
						
						unit.setExpertTotalScore(unitExpertScore);
						unitMapper.updateScoreByPrimaryKey(unit);
					}else{//普通省下的地级市
						
						for(ElectionUnitFilesCollect file:filesCollect ){
							if("0".equals(file.getReporttype())){//======================推选案卷
								if("0".equals(file.getFiletype())||"1".equals(file.getFiletype())
										||"6".equals(file.getFiletype())||"7".equals(file.getFiletype())){//2份行政处罚案卷得分
									countryXzcf += file.getExpertconsiderscoreSum();
								}else if("2".equals(file.getFiletype())||"3".equals(file.getFiletype())){//1份移送公安案卷得分
									countryYsgajg += file.getExpertconsiderscoreSum();
								}
							}else if("1".equals(file.getReporttype())){//================抽取案卷
								if("0".equals(file.getFiletype())){//2份一般行政处罚案得分
									countryXzcf_B += file.getExpertconsiderscoreSum();
								}else if("1".equals(file.getFiletype())||"2".equals(file.getFiletype())||"3".equals(file.getFiletype())
										||"6".equals(file.getFiletype())||"7".equals(file.getFiletype())){//1份适用四个配套办法案卷得分
									countryPtss_B += file.getExpertconsiderscoreSum();
								}
							}
						}
					
						Double expertTotalScore = (countryXzcf/2*0.6+countryYsgajg*0.4)*0.5+(countryXzcf_B/2*0.6+countryPtss_B/2*0.4)*0.5;
						Float unitExpertScore = (float) AjpfServiceImpl.getFourDecimalDouble( expertTotalScore);
						
						unit.setExpertTotalScore(unitExpertScore);
						unitMapper.updateScoreByPrimaryKey(unit);
					}
					
				//【专家】县级案卷质量得分Y3=推选案卷（2份行政处罚案卷得分/2×0.6+移送公安案卷×0.4）×0.5+抽选案卷（2份一般行政处罚案卷合计得分/2×0.6+适用四个配套办法案卷/2×0.4）×0.5
				//推选的行政处罚案卷（指一般行政处罚案卷0、按日计罚1、查封扣押6、停产限产7），推选的移送公安机关案卷（指适用行政拘留2、涉嫌犯罪3）；适用四个配套办法（指按日计罚1、适用行政拘留2、涉嫌犯罪3、查封扣押6、限产停产7）
				}else if("3".equals(unit.getAreatype())){//================================区县级
					//该行政区下的各类案卷数量及分数和
					List<ElectionUnitFilesCollect> filesCollect = unit.getFilesCollect();
					
					for(ElectionUnitFilesCollect file:filesCollect ){
						if("0".equals(file.getReporttype())){//======================推选案卷
							if("0".equals(file.getFiletype())||"1".equals(file.getFiletype())
									||"6".equals(file.getFiletype())||"7".equals(file.getFiletype())){//2份行政处罚案卷得分
								countryXzcf += file.getExpertconsiderscoreSum();
							}else if("2".equals(file.getFiletype())||"3".equals(file.getFiletype())){//1份移送公安案卷得分
								countryYsgajg += file.getExpertconsiderscoreSum();
							}
						}else if("1".equals(file.getReporttype())){//================抽取案卷
							if("0".equals(file.getFiletype())){//2份一般行政处罚案得分
								countryXzcf_B += file.getExpertconsiderscoreSum();
							}else if("1".equals(file.getFiletype())||"2".equals(file.getFiletype())||"3".equals(file.getFiletype())
									||"6".equals(file.getFiletype())||"7".equals(file.getFiletype())){//1份适用四个配套办法案卷得分
								countryPtss_B += file.getExpertconsiderscoreSum();
							}
						}
					}
					
					Double expertTotalScore = (countryXzcf/2*0.6+countryYsgajg*0.4)*0.5+(countryXzcf_B/2*0.6+countryPtss_B/2*0.4)*0.5;
					Float unitExpertScore = (float) AjpfServiceImpl.getFourDecimalDouble( expertTotalScore);
					
					unit.setExpertTotalScore(unitExpertScore);
					unitMapper.updateScoreByPrimaryKey(unit);
				}
			}

			// 按省、市、县计算专家排名
			int unitRanking = unitMapper.setGroupJcpmRanking2();
			sb.append("交叉集体排名汇分成功" + unitRanking);
		} catch (Exception e) {
			// TODO: handle exception
			e.printStackTrace();
			sb.append("交叉集体排名汇分错误");
		}
		return sb.toString();
	}

	@Override
	public String setFileScorePersonal2017() {
		String res ="";
		int A = personalMapper.updatePersonalFileScorePlus();
		if(A>0){
			int B = personalMapper.setPersonalFileScore();
			if(B>0){
				res = "计算个人案卷分成功：计算总分"+A+"个，计算个人案卷分"+B+"个";
			}else{
				res = "error: 计算个人案卷质量分失败!";
			}
		}else{
			res = "error: 计算个人的案卷分总和失败!";
		}
		
		return res;
	}
	
	@Override
	public String setFileScorePersonal2019() {
		String resultStr = "";
		try{
			// 查询所有案卷
			List<Files> filesList = filesMapper.queryAllFiles();
			for (Files file : filesList) {
				if(file.getType() != 0) {
					// 案卷类型
					String fileType = file.getFiletype();
					// A专家
					Integer expertAId ;
					// B专家
					Integer expertBId;
					if ("1".equals(file.getIsconsider())) {
						expertAId = file.getExpertcommitaid();
						expertBId = file.getExpertcommitbid();
					} else {
						expertAId = file.getExpertaid();
						expertBId = file.getExpertbid();
					}
					if(null != expertAId && null != expertBId){
						List<Integer> indexIds;
						// 根据案卷类型确定要计算的指标项
						if ("0".equals(fileType)) { // 行政处罚案卷
							indexIds = Arrays.asList(2 , 3, 5, 6, 7, 8, 9);
						} else if ("1".equals(fileType)) { // 按日计罚案卷
							indexIds = Arrays.asList(20, 21, 23, 24, 25, 26, 27);
						} else if ("2".equals(fileType)) { // 移送行政拘留案卷
							indexIds = Arrays.asList(65, 66, 68, 69, 70, 71, 72);
						} else if ("3".equals(fileType)) { // 环境污染犯罪移送公安机关案卷
							indexIds = Arrays.asList(81, 82, 84, 85, 86, 87, 88);
						} else if ("6".equals(fileType)) { // 查封扣押案卷
							indexIds = Arrays.asList(39, 40, 42, 43, 44, 45);
						} else if ("7".equals(fileType)) { // 停产限产案卷
							indexIds = Arrays.asList(50, 51, 53, 54, 55, 56);
						} else {
							indexIds = null;
						}
						
						// 计算A专家质量分
						Float indexScoreA = expertHandlFileListMapper.selectScoreDivisorByExpertId(expertAId, file.getId(), indexIds);
						// 计算B专家质量分
						Float indexScoreB = expertHandlFileListMapper.selectScoreDivisorByExpertId(expertBId, file.getId(), indexIds);
						
						List<ExpertHandlFileList> fileList = expertHandlFileListMapper.getExpertFileListByFileId(file.getId());
						for (ExpertHandlFileList expertHandlFileList : fileList) {
							Integer expertid = expertHandlFileList.getExpertid();
							if(expertAId.equals(expertid)) {
								expertHandlFileListMapper.updateQualityScoreById(indexScoreA, expertHandlFileList.getId());
							}
							if(expertBId.equals(expertid)) {
								expertHandlFileListMapper.updateQualityScoreById(indexScoreB, expertHandlFileList.getId());
							}
						}
						// 案卷质量分
						float ajzlScore = (indexScoreA + indexScoreB) / 2;
						file.setQualityScore(ajzlScore);
						filesMapper.updateQualityScoreById(ajzlScore, file.getId());
					}
				}
			}
			
			personalMapper.setQualityScore();
			
			resultStr="success提示:计算案卷质量分成功；\n";
    	 
		} catch(Exception ex) {
			ex.printStackTrace();
			resultStr="exception提示：后台出现未预料的异常；\n";
		}
		return resultStr;
	}

	/**
	 * 方案2==计算集体案卷质量分(客户一时兴起,属于非正式操作)
	 * 1.案卷没有专家分的，复制交叉分作为专家分
	 * 2.根据专家与交叉7:3的比例计算一个案卷分A;
	 * 3.把案卷分A代入集体的交叉评审分计算公式，求出一个分数作为集体的质量分;
	 */
	@Override
	public String setFileQuality2017two(){
		StringBuffer sb = new StringBuffer();
		// 省(直辖市)------>n+3份案卷,n为地级市的数量； 市------>9份(普通市)、7份(直辖市中区) 份案卷； 县--->7份案卷 (直辖市没有县单位)
		
		//置入参选单位应参评的标准案卷数量
		//unitMapper.updateStandardFileNumber2017();
		

		// 交叉 集体
		try {
			// 1 :更新省、市、县下的实际的案卷总数量
			// 这里更新后 质量评分则不需要再次更新 ZlpfServiceImpl
			///unitMapper.updateElectionUnitAityAndCountryFileNumber();//-----------------------------------2016
			///unitMapper.updateElectionUnitFileNumber2017();

			//查询所有参选单位及各类案卷概要情况
			List<ElectionUnit> allUnitList = unitMapper.getElectionFilesCollectTwo();
			
			for(ElectionUnit unit:allUnitList){//循环所有的参选单位
				
				//定义推选变量
				double countryXzcf = 0.0, countryJc = 0.0, countryYsgajg = 0.0;	
				//定义抽取变量
				double countryXzcf_B = 0.0, countryPtss_B = 0.0;
				
				
				//【交叉】省级案卷质量得分X1=推选案卷（行政处罚案卷A得分×0.5+2份稽查案卷/2×0.5）×0.5+抽选案卷（n份行政处罚案卷B合计得分/n）×0.5
				//这里的行政处罚案卷是泛指，包含（一般行政处罚案卷0、按日计罚1、查封扣押6、停产限产7）
				if("1".equals(unit.getAreatype())){//================================省(直辖市)级
					//该行政区下的各类案卷数量及分数和
					List<ElectionUnitFilesCollect> filesCollect = unit.getFilesCollect();
					
					for(ElectionUnitFilesCollect file:filesCollect ){
						if("0".equals(file.getReporttype())){//======================推选案卷
							if("0".equals(file.getFiletype())||"1".equals(file.getFiletype())||"2".equals(file.getFiletype())||"3".equals(file.getFiletype())
								||"6".equals(file.getFiletype())||"7".equals(file.getFiletype())){//1份行政处罚案卷得分
								countryXzcf += file.getCrossconsiderscoreSum();
							}else if("5".equals(file.getFiletype())){//2份稽查案卷得分
								countryJc += file.getCrossconsiderscoreSum();
							}
						}else if("1".equals(file.getReporttype())){//================抽取案卷
							/*if("0".equals(file.getFiletype())||"1".equals(file.getFiletype())
									||"6".equals(file.getFiletype())||"7".equals(file.getFiletype())){*///n份行政处罚案卷得分,(n=省级应参评案件数-3)
								countryXzcf_B += file.getCrossconsiderscoreSum();
							/*}*/
						}
					}
					int n=0;
					Integer standFileNum = unit.getStandardfilenumber();//应参评案卷数
					if(standFileNum==null||standFileNum==0||standFileNum<=3){
						
					}else{
						n = standFileNum-3;
					}
					if(n>0){
						Double crossTotalScore = (countryXzcf*0.5+countryJc/2*0.5)*0.5+(countryXzcf_B/n)*0.5;
						Float unitCrossScore = (float) AjpfServiceImpl.getFourDecimalDouble( crossTotalScore);
						
						unit.setQualityfiletotalscore(unitCrossScore);
						unitMapper.updateScoreByPrimaryKey(unit);
						
					}else{
						sb.append(unit.getAreacode()+"省应抽取的地级市数量n为0不合法");
					}
					
				//【交叉】市级案卷质量得分X2=推选案卷（2份行政处罚案卷得分/2×0.5+移送公安案卷×0.3+2份稽查案卷/2×0.2）×0.5+抽选案卷（2份一般行政处罚案卷合计得分/2×0.6+适用四个配套办法案卷/2×0.4）×0.5
				//推选的行政处罚案卷（指一般行政处罚案卷0、按日计罚1、查封扣押6、停产限产7），推选的移送公安机关案卷（指适用行政拘留2、涉嫌犯罪3）；适用四个配套办法（指按日计罚1、查封扣押6、限产停产7、适用行政拘留2、涉嫌犯罪3）
				}else if("2".equals(unit.getAreatype())){//=============================-地市级
					//该行政区下的各类案卷数量及分数和
					List<ElectionUnitFilesCollect> filesCollect = unit.getFilesCollect();
					
					if(unit.getAreacode().startsWith("11")||unit.getAreacode().startsWith("12")||unit.getAreacode().startsWith("31")
							||unit.getAreacode().startsWith("55")||unit.getAreacode().startsWith("66")){//直辖市（兵团）的区县计分方法与普通区县（areaType=3）相同
						
						for(ElectionUnitFilesCollect file:filesCollect ){
							if("0".equals(file.getReporttype())){//======================推选案卷
								if("0".equals(file.getFiletype())||"1".equals(file.getFiletype())
										||"6".equals(file.getFiletype())||"7".equals(file.getFiletype())){//2份行政处罚案卷得分
									countryXzcf += file.getCrossconsiderscoreSum();
								}else if("2".equals(file.getFiletype())||"3".equals(file.getFiletype())){//1份移送公安案卷得分
									countryYsgajg += file.getCrossconsiderscoreSum();
								}
							}else if("1".equals(file.getReporttype())){//================抽取案卷
								if("0".equals(file.getFiletype())){//2份一般行政处罚案得分
									countryXzcf_B += file.getCrossconsiderscoreSum();
								}else if("1".equals(file.getFiletype())||"2".equals(file.getFiletype())||"3".equals(file.getFiletype())
										||"6".equals(file.getFiletype())||"7".equals(file.getFiletype())){//1份适用四个配套办法案卷得分
									countryPtss_B += file.getCrossconsiderscoreSum();
								}
							}
						}
						
						Double crossTotalScore = (countryXzcf/2*0.6+countryYsgajg*0.4)*0.5+(countryXzcf_B/2*0.6+countryPtss_B/2*0.4)*0.5;
						Float unitCrossScore = (float) AjpfServiceImpl.getFourDecimalDouble( crossTotalScore);
						
						unit.setQualityfiletotalscore(unitCrossScore);
						unitMapper.updateScoreByPrimaryKey(unit);
						
					}else{//普通省下的地级市
						
						for(ElectionUnitFilesCollect file:filesCollect ){
							if("0".equals(file.getReporttype())){//======================推选案卷
								if("0".equals(file.getFiletype())||"1".equals(file.getFiletype())
										||"6".equals(file.getFiletype())||"7".equals(file.getFiletype())){//2份行政处罚案卷得分
									countryXzcf += file.getCrossconsiderscoreSum();
								}else if("2".equals(file.getFiletype())||"3".equals(file.getFiletype())){//1份移送公安案卷得分
									countryYsgajg += file.getCrossconsiderscoreSum();
								}else if("5".equals(file.getFiletype())){//2份稽查案卷得分
									countryJc += file.getCrossconsiderscoreSum();
								}
							}else if("1".equals(file.getReporttype())){//================抽取案卷
								if("0".equals(file.getFiletype())){//2份一般行政处罚案得分
									countryXzcf_B += file.getCrossconsiderscoreSum();
								}else if("1".equals(file.getFiletype())||"2".equals(file.getFiletype())||"3".equals(file.getFiletype())
										||"6".equals(file.getFiletype())||"7".equals(file.getFiletype())){//1份适用四个配套办法案卷得分
									countryPtss_B += file.getCrossconsiderscoreSum();
								}
							}
						}
					
						Double crossTotalScore = (countryXzcf/2*0.5+countryYsgajg*0.3+countryJc/2*0.2)*0.5+(countryXzcf_B/2*0.6+countryPtss_B/2*0.4)*0.5;
						Float unitCrossScore = (float) AjpfServiceImpl.getFourDecimalDouble( crossTotalScore);
						
						unit.setQualityfiletotalscore(unitCrossScore);
						unitMapper.updateScoreByPrimaryKey(unit);
					}
					
				//【交叉】县级案卷质量得分X3=推选案卷（2份行政处罚案卷得分/2×0.6+移送公安案卷×0.4）×0.5+抽选案卷（2份一般行政处罚案卷合计得分/2×0.6+适用四个配套办法案卷/2×0.4）×0.5
				//推选的行政处罚案卷（指一般行政处罚案卷0、按日计罚1、查封扣押6、停产限产7），推选的移送公安机关案卷（指适用行政拘留2、涉嫌犯罪3）；适用四个配套办法（指按日计罚1、适用行政拘留2、涉嫌犯罪3、查封扣押6、限产停产7）
				}else if("3".equals(unit.getAreatype())){//================================区县级
					//该行政区下的各类案卷数量及分数和
					List<ElectionUnitFilesCollect> filesCollect = unit.getFilesCollect();
					
					for(ElectionUnitFilesCollect file:filesCollect ){
						if("0".equals(file.getReporttype())){//======================推选案卷
							if("0".equals(file.getFiletype())||"1".equals(file.getFiletype())
									||"6".equals(file.getFiletype())||"7".equals(file.getFiletype())){//2份行政处罚案卷得分
								countryXzcf += file.getCrossconsiderscoreSum();
							}else if("2".equals(file.getFiletype())||"3".equals(file.getFiletype())){//1份移送公安案卷得分
								countryYsgajg += file.getCrossconsiderscoreSum();
							}
						}else if("1".equals(file.getReporttype())){//================抽取案卷
							if("0".equals(file.getFiletype())){//2份一般行政处罚案得分
								countryXzcf_B += file.getCrossconsiderscoreSum();
							}else if("1".equals(file.getFiletype())||"2".equals(file.getFiletype())||"3".equals(file.getFiletype())
									||"6".equals(file.getFiletype())||"7".equals(file.getFiletype())){//1份适用四个配套办法案卷得分
								countryPtss_B += file.getCrossconsiderscoreSum();
							}
						}
					}
					
					Double crossTotalScore = (countryXzcf/2*0.6+countryYsgajg*0.4)*0.5+(countryXzcf_B/2*0.6+countryPtss_B/2*0.4)*0.5;
					Float unitCrossScore = (float) AjpfServiceImpl.getFourDecimalDouble( crossTotalScore);
					
					unit.setQualityfiletotalscore(unitCrossScore);
					unitMapper.updateScoreByPrimaryKey(unit);
				}
			}

			// 按省、市、县计算交叉排名
			//int unitRanking = unitMapper.setGroupJcpmRanking();
			sb.append("集体质量分方案2（非正式）成功");
		} catch (Exception e) {
			e.printStackTrace();
			sb.append("交叉集体排名汇分错误");
		}
		return sb.toString();
	}
	
	
	
	/**
	 * 集体==交叉分===计算详细分项：交叉推选分;交叉抽选分
	 */
	@Override
	public String setCrossScoreIndexUnit2017(){
		StringBuffer sb = new StringBuffer();
		// 省(直辖市)------>n+3份案卷,n为地级市的数量； 市------>9份(普通市)、7份(直辖市中区) 份案卷； 县--->7份案卷 (直辖市没有县单位)
		
		//置入参选单位应参评的标准案卷数量
		unitMapper.updateStandardFileNumber2017();

		// 交叉 集体
		try {
			// 1 :更新省、市、县下的实际的案卷总数量
			// 这里更新后 质量评分则不需要再次更新 ZlpfServiceImpl
			///unitMapper.updateElectionUnitAityAndCountryFileNumber();//-----------------------------------2016
			unitMapper.updateElectionUnitFileNumber2017();

			//查询所有参选单位及各类案卷概要情况
			List<ElectionUnit> allUnitList = unitMapper.getElectionFilesCollect();
			
			for(ElectionUnit unit:allUnitList){//循环所有的参选单位
				
				//定义推选变量
				double countryXzcf = 0.0, countryJc = 0.0, countryYsgajg = 0.0;	
				//定义抽取变量
				double countryXzcf_B = 0.0, countryPtss_B = 0.0;
				
				
				//【交叉】省级案卷质量得分X1=推选案卷（行政处罚案卷A得分×0.5+2份稽查案卷/2×0.5）×0.5+抽选案卷（n份行政处罚案卷B合计得分/n）×0.5
				//这里的行政处罚案卷是泛指，包含（一般行政处罚案卷0、按日计罚1、查封扣押6、停产限产7）
				if("1".equals(unit.getAreatype())){//================================省(直辖市)级
					//该行政区下的各类案卷数量及分数和
					List<ElectionUnitFilesCollect> filesCollect = unit.getFilesCollect();
					
					for(ElectionUnitFilesCollect file:filesCollect ){
						if("0".equals(file.getReporttype())){//======================推选案卷
							if("0".equals(file.getFiletype())||"1".equals(file.getFiletype())||"2".equals(file.getFiletype())||"3".equals(file.getFiletype())
								||"6".equals(file.getFiletype())||"7".equals(file.getFiletype())){//1份行政处罚案卷得分
								countryXzcf += file.getCrossconsiderscoreSum();
							}else if("5".equals(file.getFiletype())){//2份稽查案卷得分
								countryJc += file.getCrossconsiderscoreSum();
							}
						}else if("1".equals(file.getReporttype())){//================抽取案卷
							/*if("0".equals(file.getFiletype())||"1".equals(file.getFiletype())
									||"6".equals(file.getFiletype())||"7".equals(file.getFiletype())){*///n份行政处罚案卷得分,(n=省级应参评案件数-3)
								countryXzcf_B += file.getCrossconsiderscoreSum();
							/*}*/
						}
					}
					int n=0;
					Integer standFileNum = unit.getStandardfilenumber();//应参评案卷数
					if(standFileNum==null||standFileNum==0||standFileNum<=3){
						
					}else{
						n = standFileNum-3;
					}
					if(n>0){
						Double crossTotalScore_report = (countryXzcf*0.5+countryJc/2*0.5)*0.5;
						Double crossTotalScore_random = (countryXzcf_B/n)*0.5;
						Float unitCrossScore_report = (float) AjpfServiceImpl.getFourDecimalDouble( crossTotalScore_report);
						Float unitCrossScore_random = (float) AjpfServiceImpl.getFourDecimalDouble( crossTotalScore_random);
						
						unit.setReportCrossScore(unitCrossScore_report);
						unit.setRandomCrossScore(unitCrossScore_random);
						unitMapper.update2RScoreByPrimaryKey(unit);
						
					}else{
						sb.append(unit.getAreacode()+"省应抽取的地级市数量n为0不合法");
					}
					
				//【交叉】市级案卷质量得分X2=推选案卷（2份行政处罚案卷得分/2×0.5+移送公安案卷×0.3+2份稽查案卷/2×0.2）×0.5+抽选案卷（2份一般行政处罚案卷合计得分/2×0.6+适用四个配套办法案卷/2×0.4）×0.5
				//推选的行政处罚案卷（指一般行政处罚案卷0、按日计罚1、查封扣押6、停产限产7），推选的移送公安机关案卷（指适用行政拘留2、涉嫌犯罪3）；适用四个配套办法（指按日计罚1、查封扣押6、限产停产7、适用行政拘留2、涉嫌犯罪3）
				}else if("2".equals(unit.getAreatype())){//=============================-地市级
					//该行政区下的各类案卷数量及分数和
					List<ElectionUnitFilesCollect> filesCollect = unit.getFilesCollect();
					
					if(unit.getAreacode().startsWith("11")||unit.getAreacode().startsWith("12")||unit.getAreacode().startsWith("31")
							||unit.getAreacode().startsWith("55")||unit.getAreacode().startsWith("66")){//直辖市（兵团）的区县计分方法与普通区县（areaType=3）相同
						
						for(ElectionUnitFilesCollect file:filesCollect ){
							if("0".equals(file.getReporttype())){//======================推选案卷
								if("0".equals(file.getFiletype())||"1".equals(file.getFiletype())
										||"6".equals(file.getFiletype())||"7".equals(file.getFiletype())){//2份行政处罚案卷得分
									countryXzcf += file.getCrossconsiderscoreSum();
								}else if("2".equals(file.getFiletype())||"3".equals(file.getFiletype())){//1份移送公安案卷得分
									countryYsgajg += file.getCrossconsiderscoreSum();
								}
							}else if("1".equals(file.getReporttype())){//================抽取案卷
								if("0".equals(file.getFiletype())){//2份一般行政处罚案得分
									countryXzcf_B += file.getCrossconsiderscoreSum();
								}else if("1".equals(file.getFiletype())||"2".equals(file.getFiletype())||"3".equals(file.getFiletype())
										||"6".equals(file.getFiletype())||"7".equals(file.getFiletype())){//1份适用四个配套办法案卷得分
									countryPtss_B += file.getCrossconsiderscoreSum();
								}
							}
						}
						
						Double crossTotalScore_report = (countryXzcf/2*0.6+countryYsgajg*0.4)*0.5;
						Double crossTotalScore_random = (countryXzcf_B/2*0.6+countryPtss_B/2*0.4)*0.5;
						
						Float unitCrossScore_report = (float) AjpfServiceImpl.getFourDecimalDouble( crossTotalScore_report);
						Float unitCrossScore_random = (float) AjpfServiceImpl.getFourDecimalDouble( crossTotalScore_random);
						
						unit.setReportCrossScore(unitCrossScore_report);
						unit.setRandomCrossScore(unitCrossScore_random);
						unitMapper.update2RScoreByPrimaryKey(unit);
						
					}else{//普通省下的地级市
						
						for(ElectionUnitFilesCollect file:filesCollect ){
							if("0".equals(file.getReporttype())){//======================推选案卷
								if("0".equals(file.getFiletype())||"1".equals(file.getFiletype())
										||"6".equals(file.getFiletype())||"7".equals(file.getFiletype())){//2份行政处罚案卷得分
									countryXzcf += file.getCrossconsiderscoreSum();
								}else if("2".equals(file.getFiletype())||"3".equals(file.getFiletype())){//1份移送公安案卷得分
									countryYsgajg += file.getCrossconsiderscoreSum();
								}else if("5".equals(file.getFiletype())){//2份稽查案卷得分
									countryJc += file.getCrossconsiderscoreSum();
								}
							}else if("1".equals(file.getReporttype())){//================抽取案卷
								if("0".equals(file.getFiletype())){//2份一般行政处罚案得分
									countryXzcf_B += file.getCrossconsiderscoreSum();
								}else if("1".equals(file.getFiletype())||"2".equals(file.getFiletype())||"3".equals(file.getFiletype())
										||"6".equals(file.getFiletype())||"7".equals(file.getFiletype())){//1份适用四个配套办法案卷得分
									countryPtss_B += file.getCrossconsiderscoreSum();
								}
							}
						}
					
						Double crossTotalScore_report = (countryXzcf/2*0.5+countryYsgajg*0.3+countryJc/2*0.2)*0.5;
						Double crossTotalScore_random = (countryXzcf_B/2*0.6+countryPtss_B/2*0.4)*0.5;
						
						Float unitCrossScore_report = (float) AjpfServiceImpl.getFourDecimalDouble( crossTotalScore_report);
						Float unitCrossScore_random = (float) AjpfServiceImpl.getFourDecimalDouble( crossTotalScore_random);
						
						unit.setReportCrossScore(unitCrossScore_report);
						unit.setRandomCrossScore(unitCrossScore_random);
						unitMapper.update2RScoreByPrimaryKey(unit);
					}
					
				//【交叉】县级案卷质量得分X3=推选案卷（2份行政处罚案卷得分/2×0.6+移送公安案卷×0.4）×0.5+抽选案卷（2份一般行政处罚案卷合计得分/2×0.6+适用四个配套办法案卷/2×0.4）×0.5
				//推选的行政处罚案卷（指一般行政处罚案卷0、按日计罚1、查封扣押6、停产限产7），推选的移送公安机关案卷（指适用行政拘留2、涉嫌犯罪3）；适用四个配套办法（指按日计罚1、适用行政拘留2、涉嫌犯罪3、查封扣押6、限产停产7）
				}else if("3".equals(unit.getAreatype())){//================================区县级
					//该行政区下的各类案卷数量及分数和
					List<ElectionUnitFilesCollect> filesCollect = unit.getFilesCollect();
					
					for(ElectionUnitFilesCollect file:filesCollect ){
						if("0".equals(file.getReporttype())){//======================推选案卷
							if("0".equals(file.getFiletype())||"1".equals(file.getFiletype())
									||"6".equals(file.getFiletype())||"7".equals(file.getFiletype())){//2份行政处罚案卷得分
								countryXzcf += file.getCrossconsiderscoreSum();
							}else if("2".equals(file.getFiletype())||"3".equals(file.getFiletype())){//1份移送公安案卷得分
								countryYsgajg += file.getCrossconsiderscoreSum();
							}
						}else if("1".equals(file.getReporttype())){//================抽取案卷
							if("0".equals(file.getFiletype())){//2份一般行政处罚案得分
								countryXzcf_B += file.getCrossconsiderscoreSum();
							}else if("1".equals(file.getFiletype())||"2".equals(file.getFiletype())||"3".equals(file.getFiletype())
									||"6".equals(file.getFiletype())||"7".equals(file.getFiletype())){//1份适用四个配套办法案卷得分
								countryPtss_B += file.getCrossconsiderscoreSum();
							}
						}
					}
					
					Double crossTotalScore_report = (countryXzcf/2*0.6+countryYsgajg*0.4)*0.5;
					Double crossTotalScore_random = (countryXzcf_B/2*0.6+countryPtss_B/2*0.4)*0.5;
					
					Float unitCrossScore_report = (float) AjpfServiceImpl.getFourDecimalDouble( crossTotalScore_report);
					Float unitCrossScore_random = (float) AjpfServiceImpl.getFourDecimalDouble( crossTotalScore_random);
					
					unit.setReportCrossScore(unitCrossScore_report);
					unit.setRandomCrossScore(unitCrossScore_random);
					unitMapper.update2RScoreByPrimaryKey(unit);
				}
			}

			// 按省、市、县计算交叉排名
			sb.append("集体交叉质量分算分细项计算成功");
		} catch (Exception e) {
			e.printStackTrace();
			sb.append("集体交叉质量分细项计算失败");
		}
		return sb.toString();
	}
	
	
	
	
	/**
	 * 集体==专家分===计算详细分项：专家推选分;专家抽选分
	 */
	@Override
	public String setExpertScoreIndexUnit2017(){
		StringBuffer sb = new StringBuffer();
		// 省(直辖市)------>n+3份案卷,n为地级市的数量； 市------>9份(普通市)、7份(直辖市中区) 份案卷； 县--->7份案卷 (直辖市没有县单位)
		
		//置入参选单位应参评的标准案卷数量
		unitMapper.updateStandardFileNumber2017();
		

		//专家==集体
		try {
			// 1 :只更新市县下的实际的案卷总数量
			// 这里更新后 质量评分则不需要再次更新 ZlpfServiceImpl
			unitMapper.updateElectionUnitFileNumber2017();

			//查询所有参选单位及各类案卷概要情况
			List<ElectionUnit> allUnitList = unitMapper.getElectionFilesCollect();
			
			for(ElectionUnit unit:allUnitList){//循环所有的参选单位
				
				//定义推选变量
				double countryXzcf = 0.0, countryYsgajg = 0.0;	
				//定义抽取变量
				double countryXzcf_B = 0.0, countryPtss_B = 0.0;
				
				
				//【专家】省级案卷质量得分Y1=推选案卷（行政处罚案卷得分）×0.5+抽选案卷（n份行政处罚案卷合计得分/n）×0.5
				//这里的行政处罚案卷是泛指，包含（一般行政处罚案卷0、按日计罚1、查封扣押6、停产限产7）
				if("1".equals(unit.getAreatype())){//================================省(直辖市)级
					//该行政区下的各类案卷数量及分数和
					List<ElectionUnitFilesCollect> filesCollect = unit.getFilesCollect();
					
					for(ElectionUnitFilesCollect file:filesCollect ){
						if("0".equals(file.getReporttype())){//======================推选案卷
							/*if("0".equals(file.getFiletype())||"1".equals(file.getFiletype())
								||"6".equals(file.getFiletype())||"7".equals(file.getFiletype())){//1份行政处罚案卷得分
*/								countryXzcf += file.getExpertconsiderscoreSum();
							/*}*/
						}else if("1".equals(file.getReporttype())){//================抽取案卷
							/*if("0".equals(file.getFiletype())||"1".equals(file.getFiletype())
									||"6".equals(file.getFiletype())||"7".equals(file.getFiletype())){//n份行政处罚案卷得分,(n=省级应参评案件数-3)
*/								countryXzcf_B += file.getExpertconsiderscoreSum();
							/*}*/
						}
					}
					int n=0;
					Integer standFileNum = unit.getStandardfilenumber();//应参评案卷数
					if(standFileNum==null||standFileNum==0||standFileNum<=3){
						
					}else{
						n = standFileNum-3;
					}
					if(n>0){
						Double expertTotalScore_report = (countryXzcf*0.5);
						Double expertTotalScore_random = (countryXzcf_B/n)*0.5;
						
						Float unitExpertScore_report = (float) AjpfServiceImpl.getFourDecimalDouble( expertTotalScore_report);
						Float unitExpertScore_random = (float) AjpfServiceImpl.getFourDecimalDouble( expertTotalScore_random);
						
						unit.setReportExpertScore(unitExpertScore_report);
						unit.setRandomExpertScore(unitExpertScore_random);
						unitMapper.update2RScoreByPrimaryKey(unit);
						
					}else{
						sb.append(unit.getAreacode()+"省应抽取的地级市数量n为0不合法");
					}
					
				//【专家】市级案卷质量得分Y2=推选案卷（2份行政处罚案卷得分/2×0.6+移送公安案卷×0.4）×0.6+抽选案卷（2份一般行政处罚案卷合计得分/2×0.5+适用四个配套办法案卷/2×0.4）×0.5
				//推选的行政处罚案卷（指一般行政处罚案卷0、按日计罚1、查封扣押6、停产限产7），推选的移送公安机关案卷（指适用行政拘留2、涉嫌犯罪3）；适用四个配套办法（指按日计罚1、查封扣押6、限产停产7、适用行政拘留2、涉嫌犯罪3）
				}else if("2".equals(unit.getAreatype())){//=============================-地市级
					//该行政区下的各类案卷数量及分数和
					List<ElectionUnitFilesCollect> filesCollect = unit.getFilesCollect();
					
					if(unit.getAreacode().startsWith("11")||unit.getAreacode().startsWith("12")||unit.getAreacode().startsWith("31")
							||unit.getAreacode().startsWith("55")||unit.getAreacode().startsWith("66")){//直辖市（兵团）的区县计分方法与普通区县（areaType=3）相同
						
						for(ElectionUnitFilesCollect file:filesCollect ){
							if("0".equals(file.getReporttype())){//======================推选案卷
								if("0".equals(file.getFiletype())||"1".equals(file.getFiletype())
										||"6".equals(file.getFiletype())||"7".equals(file.getFiletype())){//2份行政处罚案卷得分
									countryXzcf += file.getExpertconsiderscoreSum();
								}else if("2".equals(file.getFiletype())||"3".equals(file.getFiletype())){//1份移送公安案卷得分
									countryYsgajg += file.getExpertconsiderscoreSum();
								}
							}else if("1".equals(file.getReporttype())){//================抽取案卷
								if("0".equals(file.getFiletype())){//2份一般行政处罚案得分
									countryXzcf_B += file.getExpertconsiderscoreSum();
								}else if("1".equals(file.getFiletype())||"2".equals(file.getFiletype())||"3".equals(file.getFiletype())
										||"6".equals(file.getFiletype())||"7".equals(file.getFiletype())){//1份适用四个配套办法案卷得分
									countryPtss_B += file.getExpertconsiderscoreSum();
								}
							}
						}
						
						Double expertTotalScore_report = (countryXzcf/2*0.6+countryYsgajg*0.4)*0.5;
						Double expertTotalScore_random = (countryXzcf_B/2*0.6+countryPtss_B/2*0.4)*0.5;
						
						Float unitExpertScore_report = (float) AjpfServiceImpl.getFourDecimalDouble( expertTotalScore_report);
						Float unitExpertScore_random = (float) AjpfServiceImpl.getFourDecimalDouble( expertTotalScore_random);
						
						unit.setReportExpertScore(unitExpertScore_report);
						unit.setRandomExpertScore(unitExpertScore_random);
						unitMapper.update2RScoreByPrimaryKey(unit);
					}else{//普通省下的地级市
						
						for(ElectionUnitFilesCollect file:filesCollect ){
							if("0".equals(file.getReporttype())){//======================推选案卷
								if("0".equals(file.getFiletype())||"1".equals(file.getFiletype())
										||"6".equals(file.getFiletype())||"7".equals(file.getFiletype())){//2份行政处罚案卷得分
									countryXzcf += file.getExpertconsiderscoreSum();
								}else if("2".equals(file.getFiletype())||"3".equals(file.getFiletype())){//1份移送公安案卷得分
									countryYsgajg += file.getExpertconsiderscoreSum();
								}
							}else if("1".equals(file.getReporttype())){//================抽取案卷
								if("0".equals(file.getFiletype())){//2份一般行政处罚案得分
									countryXzcf_B += file.getExpertconsiderscoreSum();
								}else if("1".equals(file.getFiletype())||"2".equals(file.getFiletype())||"3".equals(file.getFiletype())
										||"6".equals(file.getFiletype())||"7".equals(file.getFiletype())){//1份适用四个配套办法案卷得分
									countryPtss_B += file.getExpertconsiderscoreSum();
								}
							}
						}
					
						Double expertTotalScore_report = (countryXzcf/2*0.6+countryYsgajg*0.4)*0.5;
						Double expertTotalScore_random = (countryXzcf_B/2*0.6+countryPtss_B/2*0.4)*0.5;
						
						Float unitExpertScore_report = (float) AjpfServiceImpl.getFourDecimalDouble( expertTotalScore_report);
						Float unitExpertScore_random = (float) AjpfServiceImpl.getFourDecimalDouble( expertTotalScore_random);
						
						unit.setReportExpertScore(unitExpertScore_report);
						unit.setRandomExpertScore(unitExpertScore_random);
						unitMapper.update2RScoreByPrimaryKey(unit);
					}
					
				//【专家】县级案卷质量得分Y3=推选案卷（2份行政处罚案卷得分/2×0.6+移送公安案卷×0.4）×0.5+抽选案卷（2份一般行政处罚案卷合计得分/2×0.6+适用四个配套办法案卷/2×0.4）×0.5
				//推选的行政处罚案卷（指一般行政处罚案卷0、按日计罚1、查封扣押6、停产限产7），推选的移送公安机关案卷（指适用行政拘留2、涉嫌犯罪3）；适用四个配套办法（指按日计罚1、适用行政拘留2、涉嫌犯罪3、查封扣押6、限产停产7）
				}else if("3".equals(unit.getAreatype())){//================================区县级
					//该行政区下的各类案卷数量及分数和
					List<ElectionUnitFilesCollect> filesCollect = unit.getFilesCollect();
					
					for(ElectionUnitFilesCollect file:filesCollect ){
						if("0".equals(file.getReporttype())){//======================推选案卷
							if("0".equals(file.getFiletype())||"1".equals(file.getFiletype())
									||"6".equals(file.getFiletype())||"7".equals(file.getFiletype())){//2份行政处罚案卷得分
								countryXzcf += file.getExpertconsiderscoreSum();
							}else if("2".equals(file.getFiletype())||"3".equals(file.getFiletype())){//1份移送公安案卷得分
								countryYsgajg += file.getExpertconsiderscoreSum();
							}
						}else if("1".equals(file.getReporttype())){//================抽取案卷
							if("0".equals(file.getFiletype())){//2份一般行政处罚案得分
								countryXzcf_B += file.getExpertconsiderscoreSum();
							}else if("1".equals(file.getFiletype())||"2".equals(file.getFiletype())||"3".equals(file.getFiletype())
									||"6".equals(file.getFiletype())||"7".equals(file.getFiletype())){//1份适用四个配套办法案卷得分
								countryPtss_B += file.getExpertconsiderscoreSum();
							}
						}
					}
					
					Double expertTotalScore_report = (countryXzcf/2*0.6+countryYsgajg*0.4)*0.5;
					Double expertTotalScore_random = (countryXzcf_B/2*0.6+countryPtss_B/2*0.4)*0.5;
					
					Float unitExpertScore_report = (float) AjpfServiceImpl.getFourDecimalDouble( expertTotalScore_report);
					Float unitExpertScore_random = (float) AjpfServiceImpl.getFourDecimalDouble( expertTotalScore_random);
					
					unit.setReportExpertScore(unitExpertScore_report);
					unit.setRandomExpertScore(unitExpertScore_random);
					unitMapper.update2RScoreByPrimaryKey(unit);
				}
			}

			// 按省、市、县计算专家排名
			sb.append("集体专家质量分算分细项计算成功");
		} catch (Exception e) {
			// TODO: handle exception
			e.printStackTrace();
			sb.append("集体专家质量分算分细项计算错误");
		}
		return sb.toString();
	}
	
}
