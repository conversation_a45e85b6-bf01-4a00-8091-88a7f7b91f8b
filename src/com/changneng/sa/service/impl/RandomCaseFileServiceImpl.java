package com.changneng.sa.service.impl;

import com.changneng.sa.bean.*;
import com.changneng.sa.dao.*;
import com.changneng.sa.service.FileExtraction;
import com.changneng.sa.service.RandomCaseFileService;

import com.changneng.sa.util.Const;
import com.changneng.sa.util.JsonResult;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.FileWriter;
import java.util.*;
import java.util.stream.Collectors;

/**
 　* 案卷抽取实现类
 　* <AUTHOR>
 　* @date 2024-05-11 21:05:24
 　*/
@Service
public class RandomCaseFileServiceImpl implements RandomCaseFileService {
    private static final Logger log = Logger.getLogger(RandomCaseFileServiceImpl.class);

    /** 案卷表 */
    @Autowired
    private FilesMapper filesMapper;
    /** 活动表 */
    @Autowired
    private FilesDrawActivityMapper filesDrawActivityMapper;
    /** 行政区划表 */
    @Autowired
    private TareaMapper tareaMapper;

    @Autowired
    private RandomCaseFileMapper randomCaseFileMapper;

    //专家库
    @Autowired
    private ExpertUserMapper expertUserMapper;
    @Autowired
    private ExpertHandlFileListMapper expertHandlFileMapper;


    /**
     * 　* 2024上半年案卷抽取
     * 　* <AUTHOR>
     * 　* @date 2024-05-11 21:05:00
     * @param activityId 活动ID
     *
     *                   规则简述(正常抽取):
     *                   1.数量:省内市*2
     *                   2.不限制是否结案,但是必须有附件
     *                   3.行政处罚:每个市抽一个
     *                   4.配套:每个省抽5个(尽量不同类型不同市)
     *                   5.不予处罚:优先已结案,其次未结案有附件
     */
    @Override
    public JsonResult get2024UpRandomFiles(HttpServletRequest request, Integer activityId) {
        JsonResult jsonResult = new JsonResult();
        //TODO 案卷地址保存成txt文件保存地址
//        String filePath = "/usr/local/2023DaLianBingFileUrl/fileUrl"+activityId+".txt";
        String filePath = System.getProperty("user.home")+"/dlb2024/fileUrl"+activityId+".txt";    //开发环境

        //已抽取的案卷
        List<FilesExtractDto> extractFileList = new ArrayList<>();
        try{

            //获取要抽取的省市信息,T-area表,每个Map的key是一个省,value里面存这个省所有的市
            Map<String, List<Tarea>> provinceMap = getCityMap();
            //需要补抽行政处罚的省市
            Map<String, List<Tarea>> cfNeedFillMap = new HashMap<>();
            //配套每个省需要补抽的个数
            Map<String, Integer> ptNeedFillMap = new HashMap<>();
            //记录每个省:每个市抽取到的案卷数量
            Map<String,Map<String, Integer>> provinceCityFilesCountMap = new HashMap<>();

            ////分别获取所有要抽取的已结案的案卷和未结案的案卷(附件都不为空)
            //List<PenalizeSyncFileSpe> allOkFileList = randomCaseFileMapper.selectAllCaseFiles(activityId,1);//已结案
            //List<PenalizeSyncFileSpe> allUnfinishFileList = randomCaseFileMapper.selectAllCaseFiles(activityId,0);//未结案

            //所有待抽取案卷
            List<PenalizeSyncFileSpe> allFileList = randomCaseFileMapper.selectAllCaseFiles(activityId,null);//所有
            //把所有待抽取的案卷分为2023和2024两个List
            List<PenalizeSyncFileSpe> allFileList_2023 = allFileList.stream().filter(file -> "2023".equals(file.getFileYear())).collect(Collectors.toList());
            List<PenalizeSyncFileSpe> allFileList_2024 = allFileList.stream().filter(file -> "2024".equals(file.getFileYear())).collect(Collectors.toList());


            //处罚案卷格式转换Map<省Code,Map<市code,Map<案卷类型,List<案卷>>>>
            Map<String,Map<String, Map<String,List<PenalizeSyncFileSpe>>>> allFileMap = new HashMap<>();

            //查询各省各案卷类型的数量(SQL中写死了查询你条件:时间是2023年11月1日以后的,未结案案卷大小大于3M)
            List<FilesVo> filesCountByProvince = randomCaseFileMapper.selectCountByType();
            //各省内配套案卷排序(按类型和数量).Map<省,List<类型>>  list排序数量从低到高
            Map<String,List<String>> fileTypeCounts = typeCountsListToMap(filesCountByProvince);


//            allFileMap = getAllFileMap(allFileList);

            //1.行政处罚:正常抽取,
            //先处理待抽取的案卷,分为2023和2024两组
            Map<String,Map<String, Map<String,List<PenalizeSyncFileSpe>>>> allFileMap_2023 = new HashMap<>();
            allFileMap_2023 = getAllFileMap(allFileList_2023);
            Map<String,Map<String, Map<String,List<PenalizeSyncFileSpe>>>> allFileMap_2024 = new HashMap<>();
            allFileMap_2024 = getAllFileMap(allFileList_2024);
            //循环省市进行正常抽取行政处罚
            for (Map.Entry<String, List<Tarea>> provinceEntry : provinceMap.entrySet()) {
                String provinceCode = provinceEntry.getKey();
                //获取当前省下面所有的市
                List<Tarea> cityList = provinceEntry.getValue();
                //获取这个省内的案卷:   一般行政处罚不关心已结案还是未结案,直接抽全部,关心2023年和2024年
                Map<String, Map<String, List<PenalizeSyncFileSpe>>> allCityFileMap_2023 = allFileMap_2023.get(provinceCode);
                Map<String, Map<String, List<PenalizeSyncFileSpe>>> allCityFileMap_2024 = allFileMap_2024.get(provinceCode);

                //抽取行政处罚案卷(正常抽取)
                extractCfOkFile(activityId,cfNeedFillMap,provinceCode,cityList,extractFileList,allCityFileMap_2023,allCityFileMap_2024,allFileList,provinceCityFilesCountMap);

            }


            //2. 抽配套
            //记录每个省需要补抽的配套类型
            Map<String,List<String>> provinceNeedType = new HashMap<>();
            //剩下的所有的已结案和未结案的案卷
            List<PenalizeSyncFileSpe> allOkFileList = allFileList.stream().filter(file -> file.getIsClosed() == 1).collect(Collectors.toList());
            List<PenalizeSyncFileSpe> allUnfinishFileList = allFileList.stream().filter(file -> file.getIsClosed() == 0).collect(Collectors.toList());
            //2024年已结案的和未结案的,2023年已结案的和未结案的.  分出4个List来. 这里是从总待抽取案卷List里面拿出来然后分类的,不会为空的
            List<PenalizeSyncFileSpe> allOkFileList_2023 = allOkFileList.stream().filter(file -> "2023".equals(file.getFileYear())).collect(Collectors.toList());
            List<PenalizeSyncFileSpe> allOkFileList_2024 = allOkFileList.stream().filter(file -> "2024".equals(file.getFileYear())).collect(Collectors.toList());
            List<PenalizeSyncFileSpe> allUnfinishFileList_2023 = allUnfinishFileList.stream().filter(file -> "2023".equals(file.getFileYear())).collect(Collectors.toList());
            List<PenalizeSyncFileSpe> allUnfinishFileList_2024 = allUnfinishFileList.stream().filter(file -> "2024".equals(file.getFileYear())).collect(Collectors.toList());

            //把4个List改为Map的形式
            Map<String,Map<String, Map<String,List<PenalizeSyncFileSpe>>>> allOkFileMap_2023 = new HashMap<>();
            Map<String,Map<String, Map<String,List<PenalizeSyncFileSpe>>>> allOkFileMap_2024 = new HashMap<>();
            Map<String,Map<String, Map<String,List<PenalizeSyncFileSpe>>>> allUnfinishFileMap_2023 = new HashMap<>();
            Map<String,Map<String, Map<String,List<PenalizeSyncFileSpe>>>> allUnfinishFileMap_2024 = new HashMap<>();
            allOkFileMap_2023 = getAllFileMap(allOkFileList_2023);
            allOkFileMap_2024 = getAllFileMap(allOkFileList_2024);
            allUnfinishFileMap_2023 = getAllFileMap(allUnfinishFileList_2023);
            allUnfinishFileMap_2024 = getAllFileMap(allUnfinishFileList_2024);

            //配套:正常抽取  (正常抽取完行政处罚再正常抽配套)
            for (Map.Entry<String, List<Tarea>> provinceEntry : provinceMap.entrySet()) {
                //循环获取当前省市信息
                String provinceCode = provinceEntry.getKey();
                List<Tarea> cityList = provinceEntry.getValue();
                //当前省内2023年和2024年 已结案和未结案的案卷
                Map<String, Map<String, List<PenalizeSyncFileSpe>>> okCityFileMap_2023 = allOkFileMap_2023.get(provinceCode);
                Map<String, Map<String, List<PenalizeSyncFileSpe>>> okCityFileMap_2024 = allOkFileMap_2024.get(provinceCode);
                Map<String, Map<String, List<PenalizeSyncFileSpe>>> unfinishCityFileMap_2023 = allUnfinishFileMap_2023.get(provinceCode);
                Map<String, Map<String, List<PenalizeSyncFileSpe>>> unfinishCityFileMap_2024 = allUnfinishFileMap_2024.get(provinceCode);
                //当前省内案卷类型数量排行的list
                List<String> typeList = fileTypeCounts.get(provinceCode);
                //抽配套案卷(正常抽取)
                extractPtOkFile(activityId,typeList,cfNeedFillMap,ptNeedFillMap,provinceCode,cityList,extractFileList,
                        okCityFileMap_2023,okCityFileMap_2024,
                        unfinishCityFileMap_2023,unfinishCityFileMap_2024,
                        allFileList,provinceCityFilesCountMap,provinceNeedType);

            }

            //3.配套补抽
            allOkFileList = allFileList.stream().filter(file -> file.getIsClosed() == 1).collect(Collectors.toList());
            allUnfinishFileList = allFileList.stream().filter(file -> file.getIsClosed() == 0).collect(Collectors.toList());

            Map<String,Map<String, Map<String,List<PenalizeSyncFileSpe>>>> allOkFileMap = new HashMap<>();
            Map<String,Map<String, Map<String,List<PenalizeSyncFileSpe>>>> allUnfinishFileMap = new HashMap<>();
            allOkFileMap = getAllFileMap(allOkFileList);
            allUnfinishFileMap = getAllFileMap(allUnfinishFileList);
            //补抽配套:配套不够,补抽行政处罚
            for (Map.Entry<String, List<Tarea>> provinceEntry : provinceMap.entrySet()) {
                //循环获取当前省市信息
                String provinceCode = provinceEntry.getKey();
                List<Tarea> cityList = provinceEntry.getValue();

                Integer needCount = ptNeedFillMap.get(provinceCode);
                List<String> needTypeList = provinceNeedType.get(provinceCode);
                if (needCount != null && needCount >0){
                    //当前省内已结案和未结案的案卷
                    Map<String, Map<String, List<PenalizeSyncFileSpe>>> okCityFileMap = allOkFileMap.get(provinceCode);
                    Map<String, Map<String, List<PenalizeSyncFileSpe>>> unfinishCityFileMap = allUnfinishFileMap.get(provinceCode);
                    repairExtractPtOkFile(provinceCode,activityId,cityList,extractFileList,okCityFileMap,unfinishCityFileMap,needCount,allOkFileList,allUnfinishFileList,allFileList,provinceCityFilesCountMap,needTypeList);
                }

            }

            //4.不予处罚
            List<PenalizeSyncFileSpe> allOkFileEndList = allFileList.stream().filter(file -> file.getIsClosed() == 1).collect(Collectors.toList());
            List<PenalizeSyncFileSpe> allUnfinishFileEndList = allFileList.stream().filter(file -> file.getIsClosed() == 0).collect(Collectors.toList());
            //2024年已结案的和未结案的,2023年已结案的和未结案的.  分出4个List来. 这里是从总待抽取案卷List里面拿出来然后分类的,不会为空的
            List<PenalizeSyncFileSpe> allOkFileEndList_2023 = allOkFileEndList.stream().filter(file -> "2023".equals(file.getFileYear())).collect(Collectors.toList());
            List<PenalizeSyncFileSpe> allOkFileEndList_2024 = allOkFileEndList.stream().filter(file -> "2024".equals(file.getFileYear())).collect(Collectors.toList());
            List<PenalizeSyncFileSpe> allUnfinishFileEndList_2023 = allUnfinishFileEndList.stream().filter(file -> "2023".equals(file.getFileYear())).collect(Collectors.toList());
            List<PenalizeSyncFileSpe> allUnfinishFileEndList_2024 = allUnfinishFileEndList.stream().filter(file -> "2024".equals(file.getFileYear())).collect(Collectors.toList());

            //把4个List改为Map的形式
            Map<String,Map<String, Map<String,List<PenalizeSyncFileSpe>>>> allOkFileEndMap_2023 = new HashMap<>();
            Map<String,Map<String, Map<String,List<PenalizeSyncFileSpe>>>> allOkFileEndMap_2024 = new HashMap<>();
            Map<String,Map<String, Map<String,List<PenalizeSyncFileSpe>>>> allUnfinishFileEndMap_2023 = new HashMap<>();
            Map<String,Map<String, Map<String,List<PenalizeSyncFileSpe>>>> allUnfinishFileEndMap_2024 = new HashMap<>();
            allOkFileEndMap_2023 = getAllFileMap(allOkFileEndList_2023);
            allOkFileEndMap_2024 = getAllFileMap(allOkFileEndList_2024);
            allUnfinishFileEndMap_2023 = getAllFileMap(allUnfinishFileEndList_2023);
            allUnfinishFileEndMap_2024 = getAllFileMap(allUnfinishFileEndList_2024);

            //记录不予处罚需要补抽的个数
            Map<String,Integer> needNotCountMap = new HashMap<>();

            //不予处罚:正常只抽取5个
            for (Map.Entry<String, List<Tarea>> provinceEntry : provinceMap.entrySet()) {
                String provinceCode = provinceEntry.getKey();
                List<Tarea> cityList = provinceEntry.getValue();

                //当前省内已结案和未结案的案卷
                Map<String, Map<String, List<PenalizeSyncFileSpe>>> okCityFileMap_2023 = allOkFileEndMap_2023.get(provinceCode);
                Map<String, Map<String, List<PenalizeSyncFileSpe>>> okCityFileMap_2024 = allOkFileEndMap_2024.get(provinceCode);
                Map<String, Map<String, List<PenalizeSyncFileSpe>>> unfinishCityFileMap_2023 = allUnfinishFileEndMap_2023.get(provinceCode);
                Map<String, Map<String, List<PenalizeSyncFileSpe>>> unfinishCityFileMap_2024 = allUnfinishFileEndMap_2024.get(provinceCode);

                //抽不予处罚
                extractNotPunishableOkFile(activityId,provinceCode,cityList,extractFileList,
                        okCityFileMap_2023,okCityFileMap_2024,unfinishCityFileMap_2023,unfinishCityFileMap_2024,
                        allFileList,provinceCityFilesCountMap,needNotCountMap);
            }

            //5.不予处罚 补抽!!
            allOkFileList = allFileList.stream().filter(file -> file.getIsClosed() == 1).collect(Collectors.toList());
            allUnfinishFileList = allFileList.stream().filter(file -> file.getIsClosed() == 0).collect(Collectors.toList());

            allOkFileMap = getAllFileMap(allOkFileList);
            allUnfinishFileMap = getAllFileMap(allUnfinishFileList);

            for (Map.Entry<String, List<Tarea>> provinceEntry : provinceMap.entrySet()) {
                String provinceCode = provinceEntry.getKey();
                Integer needCount = needNotCountMap.get(provinceCode);
                if (needCount != null && needCount>0){
                    List<Tarea> cityList = provinceEntry.getValue();
                    Map<String, Map<String, List<PenalizeSyncFileSpe>>> okCityFileMap = allOkFileMap.get(provinceCode);
                    Map<String, Map<String, List<PenalizeSyncFileSpe>>> unfinishCityFileMap = allUnfinishFileMap.get(provinceCode);

                    repairExtractNotFile(provinceCode,activityId,cityList,extractFileList,okCityFileMap,unfinishCityFileMap,needCount,allOkFileList,allUnfinishFileList,provinceCityFilesCountMap);

                }
            }


            //6 处理抽出来的案卷
            if (CollectionUtils.isNotEmpty(extractFileList)){
                //将oldFileUrl写入到指定路径下的txt文件
                saveFileUrlTxt(filePath,extractFileList);

                int insertCount = saveExtractFilesInfo(extractFileList);
                if (insertCount>0){
                    filesDrawActivityMapper.updateActivityStateById(activityId);
                }

//                int insertCount = 0;
                if (insertCount > 0){
                    jsonResult.setResult(Const.RESULT_SUCCESS);
                    jsonResult.setMessage("抽取案卷成功");
                }else {
                    jsonResult.setResult(Const.RESULT_ERROR);
                    jsonResult.setMessage("抽取案卷失败");
                }
            }else {
                jsonResult.setResult(Const.RESULT_ERROR);
                jsonResult.setMessage("未抽取到符合条件的案卷");
            }
        }catch (Exception e){
            jsonResult.setResult(Const.RESULT_ERROR);
            jsonResult.setMessage("抽取案卷异常");
            jsonResult.setData(e.getMessage());
            e.printStackTrace();

        }


        return jsonResult;
    }

    /**
     　* 抽取方法:
     *         一般行政处罚,正常抽取
     *         20240611调整:一般行政处罚抽取时不再区分已结案和未结案...改为区分2024年和2023年
     　* <AUTHOR>
     　* @date 2024-05-11 23:05:44
     *
     * @param cfNeedFillMap 记录需要补抽的省市
     * @param provinceCode 省code
     * @param cityList 省内所有市
     * @param extractFileList 抽到的案卷
     * @param activityId 活动id
     * @param allCityFileMap_2023 2023年的案卷
     * @param allCityFileMap_2024 2024年的案卷
    　*/
    public void extractCfOkFile(
            Integer activityId,
            Map<String, List<Tarea>> cfNeedFillMap,//需要补抽的省市
            String provinceCode,//省code
            List<Tarea> cityList,//省内所有的市
            List<FilesExtractDto> extractFileList,//抽到的案卷
            Map<String, Map<String, List<PenalizeSyncFileSpe>>> allCityFileMap_2023,  //2023年的案卷
            Map<String, Map<String, List<PenalizeSyncFileSpe>>> allCityFileMap_2024,  //2024年的案卷
            List<PenalizeSyncFileSpe> allFileList, //所有案卷
            Map<String,Map<String, Integer>> filesCountMap //抽取到的案卷数记录
    ){
        int cityNum = cityList.size();
        int doubleCity = cityNum * 2;
        //计算需要多抽取的个数: 市*2 - 5(配套) - 5(处罚)
        int cfCount = 0;
        int totalNumber = doubleCity - 5 - 5;
        //如果市*2 -配套-不予处罚 小于等于市的数量,那行政处罚就抽市的数量,否则就抽计算的数值
        if (totalNumber <= cityNum){
            cfCount = cityNum;
        }else {
            cfCount = totalNumber;
        }
        //抽取的个数
        int cfNum = 0;
        try {
            //一般行政处罚案卷类型
            String cfFileType = "0";
            List<Tarea> needFillCityList = new ArrayList<>();
            //定义当前省内市的Map,记录抽取数量
            Map<String, Integer> cityMap = new HashMap<>();

            //第一轮循环,按市,每个市抽一个
            for (Tarea tarea : cityList) {
                String areaCode = tarea.getCode();
                //天津特殊处理滨海新区,有些区的案卷都在滨海新区
                if (areaCode.equals("120317") || areaCode.equals("120318") || areaCode.equals("120361")|| areaCode.equals("120363")|| areaCode.equals("120319")){
                    //保税区120317  高新区120318  中新生态城120361  东疆保税港区120363  开发区120319
                    //如果是天津的这些地区,则直接从滨海新区抽取:120116 滨海新区,
                    areaCode = "120116";
                }

                //获取这个市的2023年和2024年的案卷
                Map<String, List<PenalizeSyncFileSpe>> fileTypeMap_2023 = allCityFileMap_2023.get(areaCode);
                Map<String, List<PenalizeSyncFileSpe>> fileTypeMap_2024 = allCityFileMap_2024.get(areaCode);

                //2024年取全部,2023年取30% 两个年度的案卷放一块抽
                List<PenalizeSyncFileSpe> cityAllFileList = new ArrayList<>();
                List<PenalizeSyncFileSpe> cityCfFileList_2024 = new ArrayList<>();
                if (fileTypeMap_2024 != null){
                    cityCfFileList_2024 = fileTypeMap_2024.get(cfFileType);
                    if (CollectionUtils.isNotEmpty(cityCfFileList_2024)){
                        cityAllFileList.addAll(cityCfFileList_2024);
                    }
                }
                //2023年 只取20%参与抽取
                List<PenalizeSyncFileSpe> cityCfFileList_2023_All = new ArrayList<>();
                if (fileTypeMap_2023 != null){
                    cityCfFileList_2023_All = fileTypeMap_2023.get(cfFileType);
                    if (CollectionUtils.isNotEmpty(cityCfFileList_2023_All)){
                        List<PenalizeSyncFileSpe> cityCfFileList_2023 = new ArrayList<>(cityCfFileList_2023_All);
                        if (CollectionUtils.isNotEmpty(cityCfFileList_2023)){
                            //当前市,2023年案卷总数
                            int cityCount_2023 = cityCfFileList_2023.size();
                            if (cityCount_2023>0){
                                int twenty = Math.round(cityCount_2023 * 0.2f);
                                //2 * 0.2f 的结果是 0.4，然后 Math.round 函数会将其四舍五入到最接近的整数。在这种情况下，0.4 会被四舍五入为 0。
                                if (twenty==0){
                                    twenty = 1;
                                }
                                for (int i = 0; i < twenty; i++) {
                                    PenalizeSyncFileSpe file = cityCfFileList_2023.remove(new Random().nextInt(cityCfFileList_2023.size()));
                                    cityAllFileList.add(file);
                                }
                            }

                        }
                    }
                }
                //cityAllFileList 是这个市的 2024年的案卷+  2023年的案卷的百分之二十
                if (CollectionUtils.isNotEmpty(cityAllFileList)){
                    PenalizeSyncFileSpe fileSpe = cityAllFileList.get(new Random().nextInt(cityAllFileList.size()));
                    extractFileList.add(mergeFileInfo(fileSpe,tarea,activityId,""));
                    allFileList.removeIf(item -> fileSpe.getId() == item.getId());
                    if (CollectionUtils.isNotEmpty(cityCfFileList_2023_All)){
                        cityCfFileList_2023_All.removeIf(item -> fileSpe.getId() == item.getId());
                    }
                    if (CollectionUtils.isNotEmpty(cityCfFileList_2024)){
                        cityCfFileList_2024.removeIf(item -> fileSpe.getId() == item.getId());
                    }
                    cityMap.put(areaCode,1);
                    cfNum++;
                    continue;
                }

                //如果都没抽到,标记这个市,后面进行补抽
                needFillCityList.add(tarea);
                cityMap.put(areaCode,0);

            }
            //记录当前省所有市抽取到的案卷个数
            filesCountMap.put(provinceCode,cityMap);
            if (CollectionUtils.isNotEmpty(needFillCityList)){
                cfNeedFillMap.put(provinceCode,needFillCityList);
            }

            //除了要抽取市级个数外,还要抽取总数减去配套和不予处罚之后的数量
            //第二轮循环,按应抽数量,抽除了市个数之外的数量
            int okIndex = 0;
            while (okIndex<=cfCount && cfNum<=cfCount){
                okIndex++;
                //根据已抽取到的案卷数量对市进行排序
                List<String> cityListOrderByCount = cityMap.entrySet().stream().sorted(Map.Entry.comparingByValue()).map(Map.Entry::getKey).collect(Collectors.toList());
                cityList.sort((o1, o2) -> {
                    int index1 = cityListOrderByCount.indexOf(o1.getCode());
                    int index2 = cityListOrderByCount.indexOf(o2.getCode());
                    return Integer.compare(index1, index2);
                });
                //循环一轮市,抽24年的
                for (Tarea tarea : cityList) {
                    if (cfNum>=cfCount){
                        break;
                    }
                    String areaCode = tarea.getCode();
                    Map<String, List<PenalizeSyncFileSpe>> fileTypeMap_2024 = allCityFileMap_2024.get(areaCode);
                    if (fileTypeMap_2024 != null){
                        List<PenalizeSyncFileSpe> cityCfFileList_2024_All = fileTypeMap_2024.get(cfFileType);
                        if (CollectionUtils.isNotEmpty(cityCfFileList_2024_All)){
                            PenalizeSyncFileSpe fileSpe = cityCfFileList_2024_All.remove(new Random().nextInt(cityCfFileList_2024_All.size()));
                            extractFileList.add(mergeFileInfo(fileSpe,tarea,activityId,""));
                            allFileList.removeIf(item -> fileSpe.getId() == item.getId());
                            cfNum++;
                            //在当前市的map中获取值,如果没有值,就给1,如果有就+1
                            cityMap.merge(areaCode, 1, Integer::sum);
                        }
                    }
                }
                //根据已抽取到的案卷数量对市进行排序
                List<String> cityListOrderByCount2 = cityMap.entrySet().stream().sorted(Map.Entry.comparingByValue()).map(Map.Entry::getKey).collect(Collectors.toList());
                cityList.sort((o1, o2) -> {
                    int index1 = cityListOrderByCount2.indexOf(o1.getCode());
                    int index2 = cityListOrderByCount2.indexOf(o2.getCode());
                    return Integer.compare(index1, index2);
                });
                //循环一轮市,抽23年的
                for (Tarea tarea : cityList) {
                    if (cfNum>=cfCount){
                        break;
                    }
                    String areaCode = tarea.getCode();
                    Map<String, List<PenalizeSyncFileSpe>> fileTypeMap_2023 = allCityFileMap_2024.get(areaCode);
                    if (fileTypeMap_2023 != null){
                        List<PenalizeSyncFileSpe> cityCfFileList_2023_All = fileTypeMap_2023.get(cfFileType);
                        if (CollectionUtils.isNotEmpty(cityCfFileList_2023_All)){
                            PenalizeSyncFileSpe fileSpe = cityCfFileList_2023_All.remove(new Random().nextInt(cityCfFileList_2023_All.size()));
                            extractFileList.add(mergeFileInfo(fileSpe,tarea,activityId,""));
                            allFileList.removeIf(item -> fileSpe.getId() == item.getId());
                            cfNum++;
                            //在当前市的map中获取值,如果没有值,就给1,如果有就+1
                            cityMap.merge(areaCode, 1, Integer::sum);
                        }
                    }
                }
            }

            filesCountMap.put(provinceCode,cityMap);
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    /**
     　* 抽取方法:
     *         配套案卷,正常抽取
     　* <AUTHOR>
     　* @date 2024-05-13 14:05:02
     *
     * @param typeList 省内配套类型数量排行
     * @param cfNeedFillMap 每个省配套缺的个数
     * @param ptNeedFillMap 配套需要补抽的省市
     * @param provinceCode 省code
     * @param cityList 省内所有的市
     * @param extractFileList 抽到的案卷
    　*/
    public void extractPtOkFile(
            Integer activityId,
            List<String> typeList,//省内个配套类型
            Map<String, List<Tarea>> cfNeedFillMap,//缺处罚的省市
            Map<String, Integer> ptNeedFillMap,//每个省配套缺的个数
            String provinceCode,//省code
            List<Tarea> allCityList,//省内所有的市
            List<FilesExtractDto> extractFileList,//抽到的案卷
            Map<String, Map<String, List<PenalizeSyncFileSpe>>> okCityFileMap_2023,//2023已结案,已结案案卷Map<市code,Map<案卷类型,List<案卷>>>
            Map<String, Map<String, List<PenalizeSyncFileSpe>>> okCityFileMap_2024,//2024已结案
            Map<String, Map<String, List<PenalizeSyncFileSpe>>> unfinishCityFileMap_2023, //2023未结案案卷
            Map<String, Map<String, List<PenalizeSyncFileSpe>>> unfinishCityFileMap_2024, //2024已结案
            List<PenalizeSyncFileSpe> allFileList,
            Map<String,Map<String, Integer>> provinceCityFilesCountMap,
            Map<String,List<String>> provinceNeedType //记录这个省还需要补抽的类型
    ){
        try{
            //配套每个省需要抽取5个,要保证每个类型都抽到一个
            int ptCount = 5;
            int ptNum = 0;
            //需要抽取的案卷类型
            List<String> fileType = new ArrayList<>(typeList);
            //初始化每个配套类型抽取的案卷都是0个
            Map<String, Integer> fileTypeCountMap =new HashMap<>();
            for (String type : fileType) {
                 fileTypeCountMap.put(type,0);
            }
            //获取当前省每个市的抽取个数
            Map<String, Integer> cityFileCountMap = provinceCityFilesCountMap.get(provinceCode);

            int index = 0;
            while (index<=5 && ptNum<=ptCount){
                index++;
                if (ptNum >= ptCount || fileType.size()<1 || CollectionUtils.isEmpty(fileType)){break;}
                List<Tarea> cityList = new ArrayList<>(allCityList);
                //循环类型,抽取
                Iterator<String> fileTypeIterator = fileType.iterator();
                oneFor:
                while (fileTypeIterator.hasNext()){
                    if (ptNum >= ptCount || fileType.size()<1 || CollectionUtils.isEmpty(fileType)){break;}
                    String type = fileTypeIterator.next();
                    if ("11".equals(provinceCode) && "6".equals(type)){
                        System.out.println("北京6类型配套");
                    }
                    //按市抽到的案卷数量排序
                    List<String> cityListOrderByCount = cityFileCountMap.entrySet().stream().sorted(Map.Entry.comparingByValue()).map(Map.Entry::getKey).collect(Collectors.toList());
                    cityList.sort((o1, o2) -> {
                        int index1 = cityListOrderByCount.indexOf(o1.getCode());
                        int index2 = cityListOrderByCount.indexOf(o2.getCode());
                        return Integer.compare(index1, index2);
                    });

                    if (okCityFileMap_2024!=null){
                        if (ptNum >= ptCount || fileType.size()<1 || CollectionUtils.isEmpty(fileType)){break;}
                        //2024已结案
                        Iterator<Tarea> cityIterator = cityList.iterator();
                        while (cityIterator.hasNext()){
                            if (ptNum >= ptCount || fileType.size()<1 || CollectionUtils.isEmpty(fileType)){break;}
                            Tarea tarea = cityIterator.next();
                            String cityCode = tarea.getCode();
                            Map<String, List<PenalizeSyncFileSpe>> typeFileMap = okCityFileMap_2024.get(cityCode);
                            if (typeFileMap !=null){
                                List<PenalizeSyncFileSpe> speList = typeFileMap.get(type);
                                if (CollectionUtils.isNotEmpty(speList)){
                                    //随机抽一个案卷
                                    PenalizeSyncFileSpe fileSpe = speList.get(new Random().nextInt(speList.size()));
                                    //移除这个抽到的案卷,不再参与后续抽取
                                    speList.removeIf(item -> fileSpe.getId() == item.getId());
                                    allFileList.removeIf(item -> fileSpe.getId() == item.getId());
                                    //插入到已抽取案卷的List中
                                    extractFileList.add(mergeFileInfo(fileSpe,tarea,activityId,""));
                                    //配套抽到的个数+1
                                    ptNum ++;
                                    //在当前市的map中获取值,如果没有值,就给1,如果有就+1
                                    cityFileCountMap.merge(cityCode, 1, Integer::sum);
                                    //配套类型抽到个数计数
                                    fileTypeCountMap.merge(type,1, Integer::sum);
                                    //移除这个配套类型
                                    fileTypeIterator.remove();
                                    fileType.remove(type);
                                    //移除这个市
                                    cityIterator.remove();
                                    continue oneFor;
                                }
                            }
                        }
                    }
                    if (unfinishCityFileMap_2024!=null){
                        if (ptNum >= ptCount || fileType.size()<1 || CollectionUtils.isEmpty(fileType)){break;}
                        //2024已结案
                        Iterator<Tarea> cityIterator = cityList.iterator();
                        while (cityIterator.hasNext()){
                            if (ptNum >= ptCount || fileType.size()<1 || CollectionUtils.isEmpty(fileType)){break;}
                            Tarea tarea = cityIterator.next();
                            String cityCode = tarea.getCode();
                            Map<String, List<PenalizeSyncFileSpe>> typeFileMap = unfinishCityFileMap_2024.get(cityCode);
                            if (typeFileMap !=null){
                                List<PenalizeSyncFileSpe> speList = typeFileMap.get(type);
                                if (CollectionUtils.isNotEmpty(speList)){
                                    //随机抽一个案卷
                                    PenalizeSyncFileSpe fileSpe = speList.get(new Random().nextInt(speList.size()));
                                    //移除这个抽到的案卷,不再参与后续抽取
                                    speList.removeIf(item -> fileSpe.getId() == item.getId());
                                    allFileList.removeIf(item -> fileSpe.getId() == item.getId());
                                    //插入到已抽取案卷的List中
                                    extractFileList.add(mergeFileInfo(fileSpe,tarea,activityId,""));
                                    //配套抽到的个数+1
                                    ptNum ++;
                                    //在当前市的map中获取值,如果没有值,就给1,如果有就+1
                                    cityFileCountMap.merge(cityCode, 1, Integer::sum);
                                    //配套类型抽到个数计数
                                    fileTypeCountMap.merge(type,1, Integer::sum);
                                    //移除这个配套类型
                                    fileTypeIterator.remove();
                                    fileType.remove(type);
                                    //移除这个市
                                    cityIterator.remove();
                                    continue oneFor;
                                }
                            }
                        }
                    }
                    if (okCityFileMap_2023!=null){
                        if (ptNum >= ptCount || fileType.size()<1 || CollectionUtils.isEmpty(fileType)){break;}
                        //2023已结案
                        Iterator<Tarea> cityIterator = cityList.iterator();
                        while (cityIterator.hasNext()){
                            if (ptNum >= ptCount || fileType.size()<1 || CollectionUtils.isEmpty(fileType)){break;}
                            Tarea tarea = cityIterator.next();
                            String cityCode = tarea.getCode();
                            Map<String, List<PenalizeSyncFileSpe>> typeFileMap = okCityFileMap_2023.get(cityCode);
                            if (typeFileMap !=null){
                                List<PenalizeSyncFileSpe> speList = typeFileMap.get(type);
                                if (CollectionUtils.isNotEmpty(speList)){
                                    //随机抽一个案卷
                                    PenalizeSyncFileSpe fileSpe = speList.get(new Random().nextInt(speList.size()));
                                    //移除这个抽到的案卷,不再参与后续抽取
                                    speList.removeIf(item -> fileSpe.getId() == item.getId());
                                    allFileList.removeIf(item -> fileSpe.getId() == item.getId());
                                    //插入到已抽取案卷的List中
                                    extractFileList.add(mergeFileInfo(fileSpe,tarea,activityId,""));
                                    //配套抽到的个数+1
                                    ptNum ++;
                                    //在当前市的map中获取值,如果没有值,就给1,如果有就+1
                                    cityFileCountMap.merge(cityCode, 1, Integer::sum);
                                    //配套类型抽到个数计数
                                    fileTypeCountMap.merge(type,1, Integer::sum);
                                    //移除这个配套类型
                                    fileTypeIterator.remove();
                                    fileType.remove(type);
                                    //移除这个市
                                    cityIterator.remove();
                                    continue oneFor;
                                }
                            }
                        }
                    }
                    if (unfinishCityFileMap_2023!=null){
                        if (ptNum >= ptCount || fileType.size()<1 || CollectionUtils.isEmpty(fileType)){break;}
                        //2024已结案
                        Iterator<Tarea> cityIterator = cityList.iterator();
                        while (cityIterator.hasNext()){
                            if (ptNum >= ptCount || fileType.size()<1 || CollectionUtils.isEmpty(fileType)){break;}
                            Tarea tarea = cityIterator.next();
                            String cityCode = tarea.getCode();
                            Map<String, List<PenalizeSyncFileSpe>> typeFileMap = unfinishCityFileMap_2023.get(cityCode);
                            if (typeFileMap !=null){
                                List<PenalizeSyncFileSpe> speList = typeFileMap.get(type);
                                if (CollectionUtils.isNotEmpty(speList)){
                                    //随机抽一个案卷
                                    PenalizeSyncFileSpe fileSpe = speList.get(new Random().nextInt(speList.size()));
                                    //移除这个抽到的案卷,不再参与后续抽取
                                    speList.removeIf(item -> fileSpe.getId() == item.getId());
                                    allFileList.removeIf(item -> fileSpe.getId() == item.getId());
                                    //插入到已抽取案卷的List中
                                    extractFileList.add(mergeFileInfo(fileSpe,tarea,activityId,""));
                                    //配套抽到的个数+1
                                    ptNum ++;
                                    //在当前市的map中获取值,如果没有值,就给1,如果有就+1
                                    cityFileCountMap.merge(cityCode, 1, Integer::sum);
                                    //配套类型抽到个数计数
                                    fileTypeCountMap.merge(type,1, Integer::sum);
                                    //移除这个配套类型
                                    fileTypeIterator.remove();
                                    fileType.remove(type);
                                    //移除这个市
                                    cityIterator.remove();
                                    continue oneFor;
                                }
                            }
                        }
                    }
                }
            }



            //优先从拥有配套类型少的市开始抽,四个循环,2024已结案>2024未结案>2023已结案>2023未结案
/*
            //2024已结案
            if (okCityFileMap_2024!=null){
                int okIndex = 0;
                while (okIndex<=5 && ptNum<=ptCount){
                    okIndex++;
                    //按市抽到的案卷数量排序
                    List<String> cityListOrderByCount = cityFileCountMap.entrySet().stream().sorted(Map.Entry.comparingByValue()).map(Map.Entry::getKey).collect(Collectors.toList());
                    cityList.sort((o1, o2) -> {
                        int index1 = cityListOrderByCount.indexOf(o1.getCode());
                        int index2 = cityListOrderByCount.indexOf(o2.getCode());
                        return Integer.compare(index1, index2);
                    });
                    okFor:
                    for (Tarea tarea : cityList) {
                        if (ptNum >= ptCount || fileType.size()<1 || CollectionUtils.isEmpty(fileType)){break;}
                        String cityCode = tarea.getCode();
                        //获取当前市的所有已结案的案卷
                        Map<String, List<PenalizeSyncFileSpe>> typeFileMap = okCityFileMap_2024.get(cityCode);
                        if (typeFileMap !=null){
                            Iterator<String> typeIterator = fileType.iterator();
                            while (typeIterator.hasNext()) {
                                String type = typeIterator.next();
//                            for (String type : fileType) {
                                List<PenalizeSyncFileSpe> speList = typeFileMap.get(type);
                                if (CollectionUtils.isNotEmpty(speList)){
                                    //随机抽一个案卷
                                    PenalizeSyncFileSpe fileSpe = speList.get(new Random().nextInt(speList.size()));
                                    //移除这个抽到的案卷,不再参与后续抽取
                                    speList.removeIf(item -> fileSpe.getId() == item.getId());
                                    allFileList.removeIf(item -> fileSpe.getId() == item.getId());
                                    //插入到已抽取案卷的List中
                                    extractFileList.add(mergeFileInfo(fileSpe,tarea,activityId,""));
                                    //配套抽到的个数+1
                                    ptNum ++;
                                    //在当前市的map中获取值,如果没有值,就给1,如果有就+1
                                    cityFileCountMap.merge(cityCode, 1, Integer::sum);
                                    //配套类型抽到个数计数
                                    fileTypeCountMap.merge(type,1, Integer::sum);
                                    //移除这个配套类型
//                                    fileType.remove(type);
                                    typeIterator.remove();
                                    continue okFor;
                                }
                            }
                        }
                    }
                }
            }
            //2024未结案
            if (unfinishCityFileMap_2024!=null){
                int okIndex = 0;
                while (okIndex<=5 && ptNum<=ptCount){
                    okIndex++;
                    //按市抽到的案卷数量排序
                    List<String> cityListOrderByCount = cityFileCountMap.entrySet().stream().sorted(Map.Entry.comparingByValue()).map(Map.Entry::getKey).collect(Collectors.toList());
                    cityList.sort((o1, o2) -> {
                        int index1 = cityListOrderByCount.indexOf(o1.getCode());
                        int index2 = cityListOrderByCount.indexOf(o2.getCode());
                        return Integer.compare(index1, index2);
                    });
                    okFor:
                    for (Tarea tarea : cityList) {
                        if (ptNum >= ptCount || fileType.size()<1 || CollectionUtils.isEmpty(fileType)){break;}
                        String cityCode = tarea.getCode();
                        //获取当前市的所有已结案的案卷
                        Map<String, List<PenalizeSyncFileSpe>> typeFileMap = unfinishCityFileMap_2024.get(cityCode);
                        if (typeFileMap !=null){
                            Iterator<String> typeIterator = fileType.iterator();
                            while (typeIterator.hasNext()) {
                                String type = typeIterator.next();
//                            for (String type : fileType) {
                                List<PenalizeSyncFileSpe> speList = typeFileMap.get(type);
                                if (CollectionUtils.isNotEmpty(speList)){
                                    //随机抽一个案卷
                                    PenalizeSyncFileSpe fileSpe = speList.get(new Random().nextInt(speList.size()));
                                    //移除这个抽到的案卷,不再参与后续抽取
                                    speList.removeIf(item -> fileSpe.getId() == item.getId());
                                    allFileList.removeIf(item -> fileSpe.getId() == item.getId());
                                    //插入到已抽取案卷的List中
                                    extractFileList.add(mergeFileInfo(fileSpe,tarea,activityId,""));
                                    //配套抽到的个数+1
                                    ptNum ++;
                                    //在当前市的map中获取值,如果没有值,就给1,如果有就+1
                                    cityFileCountMap.merge(cityCode, 1, Integer::sum);
                                    //配套类型抽到个数计数
                                    fileTypeCountMap.merge(type,1, Integer::sum);
                                    //移除这个配套类型
//                                    fileType.remove(type);
                                    typeIterator.remove();
                                    continue okFor;
                                }
                            }
                        }
                    }
                }
            }
            //2023已结案
            if (okCityFileMap_2023!=null){
                int okIndex = 0;
                while (okIndex<=5 && ptNum<=ptCount){
                    okIndex++;
                    //按市抽到的案卷数量排序
                    List<String> cityListOrderByCount = cityFileCountMap.entrySet().stream().sorted(Map.Entry.comparingByValue()).map(Map.Entry::getKey).collect(Collectors.toList());
                    cityList.sort((o1, o2) -> {
                        int index1 = cityListOrderByCount.indexOf(o1.getCode());
                        int index2 = cityListOrderByCount.indexOf(o2.getCode());
                        return Integer.compare(index1, index2);
                    });
                    okFor:
                    for (Tarea tarea : cityList) {
                        if (ptNum >= ptCount || fileType.size()<1 || CollectionUtils.isEmpty(fileType)){break;}
                        String cityCode = tarea.getCode();
                        //获取当前市的所有已结案的案卷
                        Map<String, List<PenalizeSyncFileSpe>> typeFileMap = okCityFileMap_2023.get(cityCode);
                        if (typeFileMap !=null){
                            Iterator<String> typeIterator = fileType.iterator();
                            while (typeIterator.hasNext()) {
                                String type = typeIterator.next();
//                            for (String type : fileType) {
                                List<PenalizeSyncFileSpe> speList = typeFileMap.get(type);
                                if (CollectionUtils.isNotEmpty(speList)){
                                    //随机抽一个案卷
                                    PenalizeSyncFileSpe fileSpe = speList.get(new Random().nextInt(speList.size()));
                                    //移除这个抽到的案卷,不再参与后续抽取
                                    speList.removeIf(item -> fileSpe.getId() == item.getId());
                                    allFileList.removeIf(item -> fileSpe.getId() == item.getId());
                                    //插入到已抽取案卷的List中
                                    extractFileList.add(mergeFileInfo(fileSpe,tarea,activityId,""));
                                    //配套抽到的个数+1
                                    ptNum ++;
                                    //在当前市的map中获取值,如果没有值,就给1,如果有就+1
                                    cityFileCountMap.merge(cityCode, 1, Integer::sum);
                                    //配套类型抽到个数计数
                                    fileTypeCountMap.merge(type,1, Integer::sum);
                                    //移除这个配套类型
//                                    fileType.remove(type);
                                    typeIterator.remove();
                                    continue okFor;
                                }
                            }
                        }
                    }
                }
            }
            //2023未结案
            if (unfinishCityFileMap_2023!=null){
                int okIndex = 0;
                while (okIndex<=5 && ptNum<=ptCount){
                    okIndex++;
                    //按市抽到的案卷数量排序
                    List<String> cityListOrderByCount = cityFileCountMap.entrySet().stream().sorted(Map.Entry.comparingByValue()).map(Map.Entry::getKey).collect(Collectors.toList());
                    cityList.sort((o1, o2) -> {
                        int index1 = cityListOrderByCount.indexOf(o1.getCode());
                        int index2 = cityListOrderByCount.indexOf(o2.getCode());
                        return Integer.compare(index1, index2);
                    });
                    okFor:
                    for (Tarea tarea : cityList) {
                        if (ptNum >= ptCount || fileType.size()<1 || CollectionUtils.isEmpty(fileType)){break;}
                        String cityCode = tarea.getCode();
                        //获取当前市的所有已结案的案卷
                        Map<String, List<PenalizeSyncFileSpe>> typeFileMap = unfinishCityFileMap_2023.get(cityCode);
                        if (typeFileMap !=null){
                            Iterator<String> typeIterator = fileType.iterator();
                            while (typeIterator.hasNext()) {
                                String type = typeIterator.next();
//                            for (String type : fileType) {
                                List<PenalizeSyncFileSpe> speList = typeFileMap.get(type);
                                if (CollectionUtils.isNotEmpty(speList)){
                                    //随机抽一个案卷
                                    PenalizeSyncFileSpe fileSpe = speList.get(new Random().nextInt(speList.size()));
                                    //移除这个抽到的案卷,不再参与后续抽取
                                    speList.removeIf(item -> fileSpe.getId() == item.getId());
                                    allFileList.removeIf(item -> fileSpe.getId() == item.getId());
                                    //插入到已抽取案卷的List中
                                    extractFileList.add(mergeFileInfo(fileSpe,tarea,activityId,""));
                                    //配套抽到的个数+1
                                    ptNum ++;
                                    //在当前市的map中获取值,如果没有值,就给1,如果有就+1
                                    cityFileCountMap.merge(cityCode, 1, Integer::sum);
                                    //配套类型抽到个数计数
                                    fileTypeCountMap.merge(type,1, Integer::sum);
                                    //移除这个配套类型
//                                    fileType.remove(type);
                                    typeIterator.remove();
                                    continue okFor;
                                }
                            }
                        }
                    }
                }
            }
*/
            //当前省四种情况抽完后,记录还缺几个
            int lackCount = ptCount - ptNum;

            provinceNeedType.put(provinceCode,fileType);
            ptNeedFillMap.put(provinceCode,lackCount);
            provinceCityFilesCountMap.put(provinceCode,cityFileCountMap);
        }catch (Exception e){
            e.printStackTrace();
        }

    }



    /**
     　* 抽取方法:(补抽)
      *         配套补抽
      *         补抽行政处罚案卷
     　* <AUTHOR>
     　* @date 2024-05-15 21:05:48
     　*/
    private void repairExtractPtOkFile(String provinceCode,
                                       Integer activityId,
                                       List<Tarea> cityList,
                                       List<FilesExtractDto> extractFileList,
                                       Map<String, Map<String, List<PenalizeSyncFileSpe>>> okCityFileMap,
                                       Map<String, Map<String, List<PenalizeSyncFileSpe>>> unfinishCityFileMap,
                                       int needCount,
                                       List<PenalizeSyncFileSpe> allOkFileList,
                                       List<PenalizeSyncFileSpe> allUnfinishFileList,
                                       List<PenalizeSyncFileSpe> allFileList,
                                       Map<String,Map<String, Integer>> provinceCityFilesCountMap,
                                       List<String> needTypeList) {
        int ptNum = 0;
        String cfFileType = "0";
        try {
            //获取当前省每个市的抽取个数
            Map<String, Integer> cityMap = provinceCityFilesCountMap.get(provinceCode);
            int okIndex = 0;
            while (okIndex <= needCount && ptNum<=needCount){
                okIndex ++;
                List<String> cityListOrderByCount = cityMap.entrySet().stream().sorted(Map.Entry.comparingByValue()).map(Map.Entry::getKey).collect(Collectors.toList());
                cityList.sort((o1, o2) -> {
                    int index1 = cityListOrderByCount.indexOf(o1.getCode());
                    int index2 = cityListOrderByCount.indexOf(o2.getCode());
                    return Integer.compare(index1, index2);
                });
                for (Tarea tarea : cityList) {
                    if (ptNum>=needCount){ continue; }
                    String areaCode = tarea.getCode();
                    //1.先抽已结案案卷,已结案没抽到再去抽未结案案卷
                    if (okCityFileMap != null){
                        if (ptNum>=needCount){ continue; }
                        //优先抽取已结案的案卷
                        Map<String, List<PenalizeSyncFileSpe>> okFileTypeMap = okCityFileMap.get(areaCode);
                        if (okFileTypeMap != null){
                            List<PenalizeSyncFileSpe> okFileList = okFileTypeMap.get(cfFileType);
                            if (CollectionUtils.isNotEmpty(okFileList)){
                                PenalizeSyncFileSpe okFileSpe = okFileList.get(new Random().nextInt(okFileList.size()));
                                okFileList.removeIf(item -> okFileSpe.getId() == item.getId());
                                allOkFileList.removeIf(item -> okFileSpe.getId() == item.getId());
                                allFileList.removeIf(item -> okFileSpe.getId() == item.getId());
                                String type = needTypeList.get(0);
                                extractFileList.add(mergeFileInfo(okFileSpe,tarea,activityId,"配套补抽:已结案行政处罚,代替原类型:"+type));
                                needTypeList.remove(0);
                                ptNum++;
                                //在当前市的map中获取值,如果没有值,就给1,如果有就+1
                                cityMap.merge(areaCode, 1, Integer::sum);
                                continue;
                            }
                        }
                    }
                    if (unfinishCityFileMap != null){
                        if (ptNum>=needCount){ continue; }
                        //如果已结案的案卷抽不到,就抽未结案有附件的案卷
                        Map<String, List<PenalizeSyncFileSpe>> unfinishFileTypeMap = unfinishCityFileMap.get(areaCode);
                        if (unfinishFileTypeMap != null){
                            List<PenalizeSyncFileSpe> unfinishFileList = unfinishFileTypeMap.get(cfFileType);
                            if (CollectionUtils.isNotEmpty(unfinishFileList)){
                                PenalizeSyncFileSpe unfinishFile = unfinishFileList.get(new Random().nextInt(unfinishFileList.size()));
                                unfinishFileList.removeIf(item -> unfinishFile.getId() == item.getId());
                                allUnfinishFileList.removeIf(item -> unfinishFile.getId() == item.getId());
                                allFileList.removeIf(item -> unfinishFile.getId() == item.getId());
                                String type = needTypeList.get(0);
                                extractFileList.add(mergeFileInfo(unfinishFile,tarea,activityId,"配套补抽:未结案行政处罚,代替原类型:"+type));
                                needTypeList.remove(0);
                                ptNum++;
                                //在当前市的map中获取值,如果没有值,就给1,如果有就+1
                                cityMap.merge(areaCode, 1, Integer::sum);
                                continue;
                            }
                        }
                    }
                }
            }
//            int unfinishIndex = 0;
//            while (unfinishIndex<=needCount && ptNum<=unfinishIndex){
//                unfinishIndex++;
//                List<String> cityListOrderByCount = cityMap.entrySet().stream().sorted(Map.Entry.comparingByValue()).map(Map.Entry::getKey).collect(Collectors.toList());
//                cityList.sort((o1, o2) -> {
//                    int index1 = cityListOrderByCount.indexOf(o1.getCode());
//                    int index2 = cityListOrderByCount.indexOf(o2.getCode());
//                    return Integer.compare(index1, index2);
//                });
//                for (Tarea tarea : cityList) {
//                    if (ptNum>=needCount){ continue; }
//                    String areaCode = tarea.getCode();
//                    //2.已结案没抽到,抽未结案
//                    if (unfinishCityFileMap != null){
//
//                        //如果已结案的案卷抽不到,就抽未结案有附件的案卷
//                        Map<String, List<PenalizeSyncFileSpe>> unfinishFileTypeMap = unfinishCityFileMap.get(areaCode);
//                        if (unfinishFileTypeMap != null){
//                            List<PenalizeSyncFileSpe> unfinishFileList = unfinishFileTypeMap.get(cfFileType);
//                            if (CollectionUtils.isNotEmpty(unfinishFileList)){
//                                PenalizeSyncFileSpe unfinishFile = unfinishFileList.get(new Random().nextInt(unfinishFileList.size()));
//                                unfinishFileList.removeIf(item -> unfinishFile.getId() == item.getId());
//                                allUnfinishFileList.removeIf(item -> unfinishFile.getId() == item.getId());
//                                extractFileList.add(mergeFileInfo(unfinishFile,tarea,activityId,"配套补抽:未结案行政处罚"));
//                                ptNum++;
//                                //在当前市的map中获取值,如果没有值,就给1,如果有就+1
//                                cityMap.merge(areaCode, 1, Integer::sum);
//                            }
//                        }
//                    }
//                }
//            }
            provinceCityFilesCountMap.put(provinceCode,cityMap);
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    /**
     　* 抽取方法:
     *         不予处罚,正常抽取(共抽5个)
     　* <AUTHOR>
     　* @date 2024-05-11 23:05:44
     *
     * @param provinceCode 省code
     * @param cityList 省内所有市
     * @param extractFileList 抽到的案卷
    　*/
    public void extractNotPunishableOkFile(
            Integer activityId,
            String provinceCode,//省code
            List<Tarea> allCityList,//省内所有的市
            List<FilesExtractDto> extractFileList,//抽到的案卷
            Map<String, Map<String, List<PenalizeSyncFileSpe>>> okCityFileMap_2023,//已结案案卷
            Map<String, Map<String, List<PenalizeSyncFileSpe>>> okCityFileMap_2024, //
            Map<String, Map<String, List<PenalizeSyncFileSpe>>> unfinishCityFileMap_2023, //未结案案卷
            Map<String, Map<String, List<PenalizeSyncFileSpe>>> unfinishCityFileMap_2024, //
            List<PenalizeSyncFileSpe> allFileList,
            Map<String,Map<String, Integer>> provinceCityFilesCountMap,
            Map<String,Integer> needNotCountMap //不予处罚需要补抽的个数
    ){
        String notPunishableType = "9";
        //需要抽取的总数:5个
        int notCount = 5;
        //记录抽取到几个
        int notNum = 0;
        if (provinceCode.equals("46")){
            System.out.println("海南");
        }
        try {
            List<Tarea> cityList = new ArrayList<>(allCityList);
            Map<String, Integer> cityMap = provinceCityFilesCountMap.get(provinceCode);


            //按优先级走四次循环, 2024已结案>2024未结案>2023已结案>2023未结案
            if (okCityFileMap_2024!=null && CollectionUtils.isNotEmpty(cityList)){
                List<String> cityListOrderByCount1 = cityMap.entrySet().stream().sorted(Map.Entry.comparingByValue()).map(Map.Entry::getKey).collect(Collectors.toList());
                cityList.sort((o1, o2) -> {
                    int index1 = cityListOrderByCount1.indexOf(o1.getCode());
                    int index2 = cityListOrderByCount1.indexOf(o2.getCode());
                    return Integer.compare(index1, index2);
                });
                Iterator<Tarea> cityIterator = cityList.iterator();
                while (cityIterator.hasNext()) {
                    if (notNum>= notCount){
                        break;
                    }
                    Tarea tarea = cityIterator.next();
                    String areaCode = tarea.getCode();
                    Map<String, List<PenalizeSyncFileSpe>> okFileTypeMap_2024 = okCityFileMap_2024.get(areaCode);
                    if (okFileTypeMap_2024!=null){
                        List<PenalizeSyncFileSpe> okFileList_2024 = okFileTypeMap_2024.get(notPunishableType);
                        if (CollectionUtils.isNotEmpty(okFileList_2024)){
                            PenalizeSyncFileSpe fileSpe = okFileList_2024.remove(new Random().nextInt(okFileList_2024.size()));
                            extractFileList.add(mergeFileInfo(fileSpe,tarea,activityId,""));
                            allFileList.removeIf(item -> fileSpe.getId() == item.getId());
                            notNum++;
                            cityMap.merge(areaCode, 1, Integer::sum);
                            cityIterator.remove();
                        }
                    }
                }
            }

            //2024未结案
            if (unfinishCityFileMap_2024!=null && CollectionUtils.isNotEmpty(cityList)){
                List<String> cityListOrderByCount1 = cityMap.entrySet().stream().sorted(Map.Entry.comparingByValue()).map(Map.Entry::getKey).collect(Collectors.toList());
                cityList.sort((o1, o2) -> {
                    int index1 = cityListOrderByCount1.indexOf(o1.getCode());
                    int index2 = cityListOrderByCount1.indexOf(o2.getCode());
                    return Integer.compare(index1, index2);
                });
                Iterator<Tarea> cityIterator = cityList.iterator();
                while (cityIterator.hasNext()) {
                    if (notNum>= notCount){
                        break;
                    }
                    Tarea tarea = cityIterator.next();
                    String areaCode = tarea.getCode();
                    Map<String, List<PenalizeSyncFileSpe>> unfinishFileTypeMap_2024 = unfinishCityFileMap_2024.get(areaCode);
                    if (unfinishFileTypeMap_2024!=null){
                        List<PenalizeSyncFileSpe> unfinishFileList_2024 = unfinishFileTypeMap_2024.get(notPunishableType);
                        if (CollectionUtils.isNotEmpty(unfinishFileList_2024)){
                            PenalizeSyncFileSpe fileSpe = unfinishFileList_2024.remove(new Random().nextInt(unfinishFileList_2024.size()));
                            extractFileList.add(mergeFileInfo(fileSpe,tarea,activityId,""));
                            allFileList.removeIf(item -> fileSpe.getId() == item.getId());
                            notNum++;
                            cityMap.merge(areaCode, 1, Integer::sum);
                            cityIterator.remove();
                        }
                    }
                }
            }
            //2023已结案
            if (okCityFileMap_2023!=null && CollectionUtils.isNotEmpty(cityList)){
                List<String> cityListOrderByCount1 = cityMap.entrySet().stream().sorted(Map.Entry.comparingByValue()).map(Map.Entry::getKey).collect(Collectors.toList());
                cityList.sort((o1, o2) -> {
                    int index1 = cityListOrderByCount1.indexOf(o1.getCode());
                    int index2 = cityListOrderByCount1.indexOf(o2.getCode());
                    return Integer.compare(index1, index2);
                });
                Iterator<Tarea> cityIterator = cityList.iterator();
                while (cityIterator.hasNext()) {
                    if (notNum>= notCount){
                        break;
                    }
                    Tarea tarea = cityIterator.next();
                    String areaCode = tarea.getCode();
                    Map<String, List<PenalizeSyncFileSpe>> okFileTypeMap_2023 = okCityFileMap_2023.get(areaCode);
                    if (okFileTypeMap_2023!=null){
                        List<PenalizeSyncFileSpe> okFileList_2023 = okFileTypeMap_2023.get(notPunishableType);
                        if (CollectionUtils.isNotEmpty(okFileList_2023)){
                            PenalizeSyncFileSpe fileSpe = okFileList_2023.remove(new Random().nextInt(okFileList_2023.size()));
                            extractFileList.add(mergeFileInfo(fileSpe,tarea,activityId,""));
                            allFileList.removeIf(item -> fileSpe.getId() == item.getId());
                            notNum++;
                            cityMap.merge(areaCode, 1, Integer::sum);
                            cityIterator.remove();
                        }
                    }
                }
            }
            //2023未结案
            if (unfinishCityFileMap_2023!=null && CollectionUtils.isNotEmpty(cityList)){
                List<String> cityListOrderByCount1 = cityMap.entrySet().stream().sorted(Map.Entry.comparingByValue()).map(Map.Entry::getKey).collect(Collectors.toList());
                cityList.sort((o1, o2) -> {
                    int index1 = cityListOrderByCount1.indexOf(o1.getCode());
                    int index2 = cityListOrderByCount1.indexOf(o2.getCode());
                    return Integer.compare(index1, index2);
                });
                Iterator<Tarea> cityIterator = cityList.iterator();
                while (cityIterator.hasNext()) {
                    if (notNum>= notCount){
                        break;
                    }
                    Tarea tarea = cityIterator.next();
                    String areaCode = tarea.getCode();
                    Map<String, List<PenalizeSyncFileSpe>> unfinishFileTypeMap_2023 = unfinishCityFileMap_2023.get(areaCode);
                    if (unfinishFileTypeMap_2023!=null){
                        List<PenalizeSyncFileSpe> unfinishFileList_2023 = unfinishFileTypeMap_2023.get(notPunishableType);
                        if (CollectionUtils.isNotEmpty(unfinishFileList_2023)){
                            PenalizeSyncFileSpe fileSpe = unfinishFileList_2023.remove(new Random().nextInt(unfinishFileList_2023.size()));
                            extractFileList.add(mergeFileInfo(fileSpe,tarea,activityId,""));
                            allFileList.removeIf(item -> fileSpe.getId() == item.getId());
                            notNum++;
                            cityMap.merge(areaCode, 1, Integer::sum);
                            cityIterator.remove();
                        }
                    }
                }
            }

            provinceCityFilesCountMap.put(provinceCode,cityMap);
            //计算需要补抽的个数
            int needNotCount = notCount - notNum;
            needNotCountMap.put(provinceCode,needNotCount);

        }catch (Exception e){
            e.printStackTrace();
            log.error("不予处罚抽取报错:",e);
        }


    }

     /**
     　*  抽取方法
      *         补抽 不予处罚补抽
     　* <AUTHOR>
     　* @date 2024-06-11 22:06:31
     　*/
    private void repairExtractNotFile(String provinceCode, Integer activityId, List<Tarea> cityList, List<FilesExtractDto> extractFileList, Map<String, Map<String, List<PenalizeSyncFileSpe>>> okCityFileMap, Map<String, Map<String, List<PenalizeSyncFileSpe>>> unfinishCityFileMap, Integer needCount, List<PenalizeSyncFileSpe> allOkFileList, List<PenalizeSyncFileSpe> allUnfinishFileList, Map<String, Map<String, Integer>> provinceCityFilesCountMap) {
        int ptNum = 0;
        String cfFileType = "0";
        try{
            //获取当前省每个市的抽取个数
            Map<String, Integer> cityMap = provinceCityFilesCountMap.get(provinceCode);
            int okIndex = 0;
            while (okIndex <= needCount && ptNum<=needCount){
                okIndex ++;
                if (ptNum>= needCount){break;}
                List<String> cityListOrderByCount = cityMap.entrySet().stream().sorted(Map.Entry.comparingByValue()).map(Map.Entry::getKey).collect(Collectors.toList());
                cityList.sort((o1, o2) -> {
                    int index1 = cityListOrderByCount.indexOf(o1.getCode());
                    int index2 = cityListOrderByCount.indexOf(o2.getCode());
                    return Integer.compare(index1, index2);
                });
                for (Tarea tarea : cityList) {
                    if (ptNum>= needCount){break;}
                    String areaCode = tarea.getCode();
                    if (okCityFileMap != null){
                        if (ptNum>= needCount){break;}
                        //优先抽取已结案的案卷
                        Map<String, List<PenalizeSyncFileSpe>> okFileTypeMap = okCityFileMap.get(areaCode);
                        if (okFileTypeMap != null){
                            List<PenalizeSyncFileSpe> okFileList = okFileTypeMap.get(cfFileType);
                            if (CollectionUtils.isNotEmpty(okFileList)){
                                PenalizeSyncFileSpe okFileSpe = okFileList.get(new Random().nextInt(okFileList.size()));
                                okFileList.removeIf(item -> okFileSpe.getId() == item.getId());
                                allOkFileList.removeIf(item -> okFileSpe.getId() == item.getId());
                                extractFileList.add(mergeFileInfo(okFileSpe,tarea,activityId,"不予处罚补抽:已结案行政处罚"));
                                ptNum++;
                                //在当前市的map中获取值,如果没有值,就给1,如果有就+1
                                cityMap.merge(areaCode, 1, Integer::sum);
                                continue;
                            }
                        }
                        if (unfinishCityFileMap != null){
                            if (ptNum>= needCount){break;}
                            //如果已结案的案卷抽不到,就抽未结案有附件的案卷
                            Map<String, List<PenalizeSyncFileSpe>> unfinishFileTypeMap = unfinishCityFileMap.get(areaCode);
                            if (unfinishFileTypeMap != null){
                                List<PenalizeSyncFileSpe> unfinishFileList = unfinishFileTypeMap.get(cfFileType);
                                if (CollectionUtils.isNotEmpty(unfinishFileList)){
                                    PenalizeSyncFileSpe unfinishFile = unfinishFileList.get(new Random().nextInt(unfinishFileList.size()));
                                    unfinishFileList.removeIf(item -> unfinishFile.getId() == item.getId());
                                    allUnfinishFileList.removeIf(item -> unfinishFile.getId() == item.getId());
                                    extractFileList.add(mergeFileInfo(unfinishFile,tarea,activityId,"不予处罚补抽:未结案行政处罚"));
                                    ptNum++;
                                    //在当前市的map中获取值,如果没有值,就给1,如果有就+1
                                    cityMap.merge(areaCode, 1, Integer::sum);
                                    continue;
                                }
                            }
                        }
                    }
                }
            }
            provinceCityFilesCountMap.put(provinceCode,cityMap);
        }catch (Exception e){
            e.printStackTrace();
            log.error("不予处罚补抽报错",e);
        }
    }

    /**
      * 方法:
     　*     获取每个省配套案卷的数量
     　* <AUTHOR>
     　* @date 2024-05-13 19:05:12
     　*/
    private Map<String, List<String>> typeCountsListToMap(List<FilesVo> dataList) {
        Map<String, List<String>> dataMap = new HashMap<>();
        //这个List只会有32个
        for (FilesVo filesVo : dataList) {
            String areaCode = filesVo.getAreaCode();
            String proCode = areaCode.substring(0,2);

            //1按日计罚,2移送行政拘留,3涉嫌犯罪,6查封扣押,7限产停产
            Map<String, Integer> countMap = new HashMap<>();
            countMap.put("1",filesVo.getArjfCount());
            countMap.put("2",filesVo.getYsxzjlCount());
            countMap.put("3",filesVo.getSxfzCount());
            countMap.put("6",filesVo.getCfkyCount());
            countMap.put("7",filesVo.getXctcCount());

            List<String> typeKey = countMap.entrySet().stream()
                    .sorted(Map.Entry.comparingByValue())
                    .map(Map.Entry::getKey)
                    .collect(Collectors.toList());

            dataMap.put(proCode,typeKey);
        }
        return dataMap;
    }

     /**
     　* 格式转换方法:
      *         待抽取案卷Map转List
      *         数据结构: Map<省Code,Map<市code,Map<案卷类型,List<案卷>>>>
      *
     　* <AUTHOR>
     　* @date 2024-05-11 21:05:02
     　*/
    public Map<String,Map<String, Map<String,List<PenalizeSyncFileSpe>>>> getAllFileMap(List<PenalizeSyncFileSpe> allFileList){
        /** 数据结构: Map<省Code,Map<市code,Map<案卷类型,List<案卷>>>> */
        Map<String,Map<String, Map<String,List<PenalizeSyncFileSpe>>>> allFileMap = new HashMap<>();
        try {

            //循环所有案卷,List转换成Map格式
            for (PenalizeSyncFileSpe fileSpe : allFileList) {
                //案卷类型
                String fileType = fileSpe.getFileType();
                //区划代码
                String areaCode = fileSpe.getAreaCode();
                //省
                String province = areaCode.substring(0,2);
                //获取当前省的Map
                Map<String, Map<String, List<PenalizeSyncFileSpe>>> cityMap = allFileMap.get(province);
                //如果为空,则创建这个省的key
                if (cityMap == null){
                    //案卷List
                    List<PenalizeSyncFileSpe> newFileList = new ArrayList<>();
                    newFileList.add(fileSpe);
                    //类型Map
                    Map<String, List<PenalizeSyncFileSpe>> fileTypeMap = new HashMap<>();
                    fileTypeMap.put(fileType,newFileList);
                    //城市Map
                    Map<String, Map<String, List<PenalizeSyncFileSpe>>> newCityMap = new HashMap<>();
                    newCityMap.put(areaCode,fileTypeMap);

                    //省--市--类型--案卷list
                    allFileMap.put(province,newCityMap);
                }else{
                    //如果省不为空,就继续获取市的Map
                    Map<String, List<PenalizeSyncFileSpe>> fileTypeMap = cityMap.get(areaCode);
                    //如果为空,则创建这个市的key
                    if(fileTypeMap ==null){
                        //案卷List
                        List<PenalizeSyncFileSpe> newFileList = new ArrayList<>();
                        newFileList.add(fileSpe);
                        //类型Map
                        Map<String, List<PenalizeSyncFileSpe>> newFileTypeMap = new HashMap<>();
                        newFileTypeMap.put(fileType,newFileList);
                        //--市--类型--案卷list
                        cityMap.put(areaCode,newFileTypeMap);
                    }else {
                        //如果市的Map不为空,继续获取类型的Map
                        List<PenalizeSyncFileSpe> fileList = fileTypeMap.get(fileType);
                        if (CollectionUtils.isEmpty(fileList)){
                            //案卷List
                            List<PenalizeSyncFileSpe> newFileList = new ArrayList<>();
                            newFileList.add(fileSpe);
                            //--类型--案卷list
                            fileTypeMap.put(fileType,newFileList);
                        }else {
                            fileList.add(fileSpe);
                        }
                    }
                }
            }

        }catch (Exception e){
            e.printStackTrace();
        }
        log.info("m-------案卷list转Map成功");
        return allFileMap;
    }

     /**
     　* 格式转换方法:
      *         获取所有的省市的信息,
      *         以Map格式返回,每个Map的key是一个省,value里面存这个省所有的市
      *         Map<省,List<市>>
      *
     　* <AUTHOR>
     　* @date 2024-05-11 21:05:52
     　*/
    private Map<String, List<Tarea>> getCityMap() {
        //获取所有市县级的城市areaLevel = '3'
        List<Tarea> tAreaList = tareaMapper.selectProvinceCity();
        //将市List转为Map
        Map<String, List<Tarea>> provinceMap = new HashMap<>();
        for (Tarea tarea : tAreaList) {
            String code = tarea.getCode();
            String proCode = code.substring(0,2);
            List<Tarea> aList = provinceMap.get(proCode);
            if (CollectionUtils.isNotEmpty(aList)){
                aList.add(tarea);
            }else {
                List<Tarea> tareas = new ArrayList<>();
                tareas.add(tarea);
                provinceMap.put(proCode,tareas);
            }
        }
        return provinceMap;
    }


    public FilesExtractDto mergeFileInfo(PenalizeSyncFileSpe penalizeSyncFileSpe,Tarea tarea,Integer activityId,String remark){
            FilesExtractDto filesExtractDto = new FilesExtractDto();
        //活动ID
        filesExtractDto.setActivityId(activityId);
        //所抽取区域
        filesExtractDto.setAreaCode(tarea.getCode());
        //案卷类型
        filesExtractDto.setFileType(penalizeSyncFileSpe.getFileType());
        //案卷名称
        filesExtractDto.setFileName(penalizeSyncFileSpe.getFileName());
        //案卷文书号
        filesExtractDto.setFileCode(penalizeSyncFileSpe.getFileCode());
        //老ID
        filesExtractDto.setOldId(penalizeSyncFileSpe.getOldId());
        // 案卷地址   获取原地址的文件后缀部分,存入库中
        if (penalizeSyncFileSpe!= null && StringUtils.isNotEmpty(penalizeSyncFileSpe.getFileUrl())){
            String fileUrl = penalizeSyncFileSpe.getFileUrl();
            filesExtractDto.setOldFileUrl(fileUrl);
            String resultFileUrl = fileUrl.substring(fileUrl.lastIndexOf("/") + 1);
//            System.out.println(resultFileUrl);
            filesExtractDto.setFileUrl(resultFileUrl);
        }
        //案卷归属地区域code
        filesExtractDto.setBelongAreaCode(penalizeSyncFileSpe.getAreaCode());
        //是否结案
        filesExtractDto.setClosed(penalizeSyncFileSpe.getIsClosed());
        //1代表随机抽取类型
        filesExtractDto.setReportType("1");
        //案卷来源ID
        filesExtractDto.setSyncFileSpeId(penalizeSyncFileSpe.getId());

        //处罚金额
        filesExtractDto.setFaKuanShuE(penalizeSyncFileSpe.getFaKuanShuE());
        //案件来源
        filesExtractDto.setFileSource(filesExtractDto.getFileSource());
        //案情简介
        filesExtractDto.setFileSimpleInfo(filesExtractDto.getFileSimpleInfo());
        //备注
        filesExtractDto.setRemark(remark);

        return filesExtractDto;
    }
    /**
     　* 将抽取的案卷保存到files表和关系表
     　* <AUTHOR>
     　* @date 2023-08-10 16:08:11
     　*/
    public int saveExtractFilesInfo(List<FilesExtractDto> extractFileList){
        int saveCount = -1;
        int i = 0;
        try {
            if (CollectionUtils.isNotEmpty(extractFileList)){
                for (FilesExtractDto filesExtractDto : extractFileList) {
                    int flag = filesMapper.insertRandomFile(filesExtractDto);
                    filesExtractDto.setFilesId(filesExtractDto.getId());
                    if (flag>0){
                        i++;
                    }
                }
            }
            if (i>0){
                saveCount = filesDrawActivityMapper.insertFilesActivityRe(extractFileList);
            }
        }catch (Exception e){
            e.printStackTrace();
            log.error("---------抽取结果保存失败:",e);
        }


        return saveCount;
    }
    /**
     　* 将案卷地址写入到指定路径下的txt文件中
     　* <AUTHOR>
     　* @date 2023-08-28 18:08:55
     　*/
    private void saveFileUrlTxt(String filePath ,List<FilesExtractDto> extractFileList) {
        File file = new File(filePath);
        System.out.println("案卷url保存地址:"+filePath);
        try{
            if (!file.exists()) {
                file.createNewFile();
            }
//            System.err.println(extractFileList.size());
            FileWriter writer = new FileWriter(file);
            for (FilesExtractDto filesExtractDto : extractFileList) {
                if (filesExtractDto!=null && StringUtils.isNotEmpty(filesExtractDto.getOldFileUrl())){
                    String oldFileUrl = filesExtractDto.getOldFileUrl();
//                    System.out.println(oldFileUrl);
                    writer.write(oldFileUrl + "\n");
                }
            }
            writer.close();
        }catch (Exception e){
            e.printStackTrace();
            System.err.println("案卷地址写入失败,目标路径:"+filePath);
        }
    }

    /**
     * 2024抽取合格情况
     * @param activityId 活动ID
     * @param provinceName 省份
     * @return
     */
    @Override
    public List<FilesVo> selectResultCount(Integer activityId,String provinceName) {
        try{
            return randomCaseFileMapper.selectResultCount2024Down(activityId,provinceName);
        }catch (Exception e){
            e.printStackTrace();
        }
        return new ArrayList<FilesVo>();
    }

    /**
     * 　* 2024抽取合格情况,导出结果
     * 　* <AUTHOR>
     * 　* @date `2024-05-16` 21:05:27
     *
     * @param activityId 活动ID
     */
    @Override
    public List<FilesVo> selectResultCountExport(Integer activityId) {
        try {
            return randomCaseFileMapper.selectResultCountExport2024Down(activityId);
        }catch (Exception e){
            e.printStackTrace();
        }
        return new ArrayList<FilesVo>();
    }

    /**
     * 　* 专家分配案卷
     * 2024,年中,国家大练兵
     * 　* <AUTHOR>
     * 　* @date 2024-05-30 22:05:35
     *
     * @param activityId 活动ID
     */
    @Override
    public JsonResult distributeFileOfExpert(HttpServletRequest request, Integer activityId) {
        JsonResult jsonResult = new JsonResult();
        //根据活动ID查询出所有待分配的案卷
        List<FilesVo> allFileList = filesMapper.selectAllFilesByActivityId(activityId);
        //查询所有需要分配案卷的专家
        int level = 0;
        //查询所有的A专家(地方执法专家)expertClass=0
        List<ExpertUser> aAllUserList = expertUserMapper.selectExpertUserByClass("0",level);
        //查询所有的B专家(司法专家)expertClass=1
        List<ExpertUser> bAllUserList = expertUserMapper.selectExpertUserByClass("1",level);

        if (CollectionUtils.isEmpty(allFileList) || CollectionUtils.isEmpty(aAllUserList)|| CollectionUtils.isEmpty(bAllUserList)){
            jsonResult.setResult(Const.RESULT_ERROR);
            jsonResult.setMessage("案卷或专家为空");
            return jsonResult;
        }


        //查询32个待分配的省 的code的前两位
        List<FileIsDrawProvince> provinceList = tareaMapper.selectProvince();

        //初始化专家已抽到的案卷个数都为0
        Map<String, Integer> userHasNumberMap = new HashMap<>();
        for (ExpertUser user : aAllUserList) {
            userHasNumberMap.put(user.getAreacode()+user.getName(), 0);
        }
        for (ExpertUser user : bAllUserList) {
            userHasNumberMap.put(user.getAreacode()+user.getName(), 0);
        }

        //计算专家分配个数,这个个数是所有专家的总数求的平均数
        int avg_a = computNumber(allFileList, aAllUserList);
        int avg_b = computNumber(allFileList, bAllUserList);
        //记录每个专家应该被分多少个案卷
        Map<String, Integer> userNeedNumberMap = new HashMap<>();
        for (ExpertUser user : aAllUserList) {
            userNeedNumberMap.put(user.getAreacode()+user.getName(), user.getFilenum());
        }
        for (ExpertUser user : bAllUserList) {
            userNeedNumberMap.put(user.getAreacode()+user.getName(), user.getFilenum());
        }

        //每个类型的案卷都需要单独的账号,先查一下每个类型都有多少个案卷
        //0行政处罚案卷，1按日计罚，2移送行政拘留，3涉嫌犯罪，6查封扣押，7停产限产，9不予处罚
        List<FilesVo> fileList_0 = filesMapper.selectAllFilesByAcIdAndType(activityId,"0");
        List<FilesVo> fileList_1 = filesMapper.selectAllFilesByAcIdAndType(activityId,"1");
        List<FilesVo> fileList_2 = filesMapper.selectAllFilesByAcIdAndType(activityId,"2");
        List<FilesVo> fileList_3 = filesMapper.selectAllFilesByAcIdAndType(activityId,"3");
        List<FilesVo> fileList_6 = filesMapper.selectAllFilesByAcIdAndType(activityId,"6");
        List<FilesVo> fileList_7 = filesMapper.selectAllFilesByAcIdAndType(activityId,"7");
        List<FilesVo> fileList_9 = filesMapper.selectAllFilesByAcIdAndType(activityId,"9");

        List<List<FilesVo>> allTypeFileList = new ArrayList<>();
        allTypeFileList.add(fileList_1);
        allTypeFileList.add(fileList_2);
        allTypeFileList.add(fileList_3);
        allTypeFileList.add(fileList_6);
        allTypeFileList.add(fileList_7);
        allTypeFileList.add(fileList_9);
        allTypeFileList.add(fileList_0);

        //记录每种类型分配到了哪个省的专家(记录第一轮的,让第二轮同省规避)(主要是配套)
        Map<String, List<String>> typeProvinceCountMap = new HashMap<>();


        //计算每个专家应该被分多少个案卷
        List<ExpertUser> expertUsersA = avgFilesToUserA(allTypeFileList, avg_a, aAllUserList, userHasNumberMap, userNeedNumberMap,typeProvinceCountMap,provinceList);
        List<ExpertUser> expertUsersB = avgFilesToUserB(allTypeFileList, avg_b, bAllUserList, userHasNumberMap, userNeedNumberMap,typeProvinceCountMap,provinceList);


        //将计算好的专家信息写入到数据库
        for (ExpertUser expertUser : expertUsersA) {
            expertUser.setActivityId(activityId);
            int isNeedNew = expertUser.getIsNeedNew();
            //判断是新增还是修改
            if (isNeedNew ==1){
                //如果是新增,就要先去数据库查询用户名的最大值
                String maxLoginName = expertUserMapper.selectMaxLoginName(level);
                String maxStr = maxLoginName.substring(3);
                int maxInt = Integer.parseInt(maxStr);
                String newLoginName = "exp" + String.format("%03d", maxInt + 1);
                expertUser.setLoginname(newLoginName);

                expertUserMapper.insertSelective(expertUser);
            }else {
                expertUserMapper.updateById(expertUser);
            }
        }
        System.err.println("---------A入库完毕");
        for (ExpertUser expertUser : expertUsersB) {
            expertUser.setActivityId(activityId);
            int isNeedNew = expertUser.getIsNeedNew();
            //判断是新增还是修改
            if (isNeedNew ==1){
                //如果是新增,就要先去数据库查询用户名的最大值
                String maxLoginName = expertUserMapper.selectMaxLoginName(level);
                String maxStr = maxLoginName.substring(3);
                int maxInt = Integer.parseInt(maxStr);
                String newLoginName = "exp" + String.format("%03d", maxInt + 1);
                expertUser.setLoginname(newLoginName);

                expertUserMapper.insertSelective(expertUser);
            }else {
                expertUserMapper.updateById(expertUser);
            }
        }
        System.err.println("---------B入库完毕");


        //重新查询计算完了的  所有的A专家(地方执法专家)expertClass=0
        List<ExpertUser> newAUserList = expertUserMapper.selectExpertUserByClassActivityId(activityId,"0",level);
        System.out.println("1-------------A查询完毕");
        //重新查询计算完了的 所有的B专家(司法专家)expertClass=1
        List<ExpertUser> newBUserList = expertUserMapper.selectExpertUserByClassActivityId(activityId,"1",level);
        System.out.println("1-------------B查询完毕");

        //记录每个专家已结被分了多少个案卷
        Map<String, Integer> userHasCountMap = new HashMap<>();

        //结构化专家ID和对应的省份
        Map<Integer, String> userProvinceMap = new HashMap<>();
        for (ExpertUser user : newAUserList) {
            userProvinceMap.put(user.getId(),user.getAreacode().substring(0,2));
            userHasCountMap.put(user.getName(),0);
        }
        for (ExpertUser user : newBUserList) {
            userProvinceMap.put(user.getId(),user.getAreacode().substring(0,2));
            userHasCountMap.put(user.getName(),0);
        }

        intFileToExperts(newAUserList,allFileList,userProvinceMap,"A",userHasCountMap);
        System.err.println("-----批量写A结束");

        List<FilesVo> allFileList2 = filesMapper.selectAllFilesByActivityId(activityId);
        intFileToExperts(newBUserList,allFileList2,userProvinceMap,"B",userHasCountMap);
        System.err.println("-----批量写B结束");

        //修改活动分配状态
        filesDrawActivityMapper.updateUseExpertById(activityId);

        jsonResult.setResult(Const.RESULT_SUCCESS);
        jsonResult.setMessage("分配完成");
        return jsonResult;
    }


     /**
     　* 把案卷分配给专家
      *
      * @param userList 专家列表
      * @param allFileList 所有待分配的案卷
      * @param userProvinceMap 专家ID和专家对应的省份的Map
      * @param AOrB A专家还是B专家的标识
      * @param userHasCountMap 记录专家已经被分了多少案卷
      *
     　* <AUTHOR>
     　* @date 2024-06-12 22:06:45
     　*/
    private void intFileToExperts(List<ExpertUser> userList,
                                  List<FilesVo> allFileList,
                                  Map<Integer, String> userProvinceMap,
                                 String AOrB,
                                  Map<String, Integer> userHasCountMap){
        //
        List<ExpertHandlFileList> handleList = new ArrayList<ExpertHandlFileList>();
        List<Files> updateFileList = new ArrayList<Files>();

        List<FilesVo> fileList = new ArrayList<>(allFileList);
        List<ExpertUser> bakUserList = new ArrayList<>(userList);

        //给专家账号排个序,先从要评的数量少的开始分
//        userList.sort((Comparator.comparingInt(ExpertUser::getFilenum)));

        //专家排序,根据案卷省数量排序, 案卷最多的省的专家先抽

        //循环这个类型的案卷List,转成Map记录每个省有多少案卷个数,然后根据数量排序,数量多的在前
        Map<String, Integer> provinceNum = new HashMap<>();
        for (FilesVo filesVo : fileList) {
            String areaCode = filesVo.getAreaCode().substring(0,2);
            Integer num = provinceNum.get(areaCode);
            if (num!=null && num>0){
                provinceNum.put(areaCode,num+1);
            }else {
                provinceNum.put(areaCode,1);
            }
        }
        //排序从大到小
//        List<String> provinceNumList = provinceNum.entrySet().stream()
//                .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
//                .map(Map.Entry::getKey)
//                .collect(Collectors.toList());
//
//        userList.sort((o1, o2) -> {
//            int index1 = provinceNumList.indexOf(o1.getAreacode().substring(0,2));
//            int index2 = provinceNumList.indexOf(o2.getAreacode().substring(0,2));
//            return Integer.compare(index1, index2);
//        });
        int num = 0;
        while (userList.size()>=1 && fileList.size()>0 && num <= allFileList.size()){
            num++;

            List<String> userHasNumList = userHasCountMap.entrySet().stream().sorted(Map.Entry.comparingByValue()).map(Map.Entry::getKey).collect(Collectors.toList());
            userList.sort((o1, o2) -> {
                int index1 = userHasNumList.indexOf(o1.getName());
                int index2 = userHasNumList.indexOf(o2.getName());
                return Integer.compare(index1, index2);
            });

            Iterator<ExpertUser> userIterator = userList.iterator();
            while (userIterator.hasNext()) {
                ExpertUser expertUser = userIterator.next();
                //专家省份
                String userAreaCode = expertUser.getAreacode().substring(0, 2);
                //这个专家类型
                int type = Integer.parseInt(expertUser.getType());
                //这个专家能评多少个案卷
                int filenum = expertUser.getFilenum();
                //如果这个专家分满了,就跳过他
                if (filenum<=0){
                    System.out.println(AOrB+"轮时,"+expertUser.getName()+"专家,剩余fileNum:"+filenum+",移除");
                    userIterator.remove();
                    continue;
                }

                //获取type这个类型的案卷
                List<FilesVo> typeList = fileList.stream().filter(filesVo -> type == filesVo.getFileType()).collect(Collectors.toList());
                if (typeList.size()<1 || CollectionUtils.isEmpty(typeList)){
                    System.out.println("**************类型:"+type+"获取案卷异常!!获取到的案卷数量为:"+typeList.size());
                    continue;
                }
                int anInt = new Random().nextInt(typeList.size());
                FilesVo file = typeList.get(anInt);
                String fileAreaCode = file.getAreaCode().substring(0, 2);
                if (!fileAreaCode.equals(userAreaCode)){
                    if ("A".equals(AOrB)){
                        //同省规避,案卷和专家不能是同一个省才能往下进行
                        typeList.remove(file);
                        fileList.remove(file);
                        ExpertHandlFileList handel = new ExpertHandlFileList();
                        handel.setFileid(file.getId());
                        handel.setFilecode(file.getFileCode());
                        handel.setExpertid(expertUser.getId());
                        handel.setExpertname(expertUser.getName());
                        handel.setScoredstate("0");//0代表未评审
                        handel.setHandltype(0);//0代表普通专家打分
                        handleList.add(handel);

                        Files files = new Files();
                        //回写案卷信息表中：专家信息
                        files.setId(file.getId());
                        files.setExpertaid(expertUser.getId());
                        files.setExpertaname(expertUser.getName());
                        updateFileList.add(files);
                        expertUser.setFilenum(filenum-1);
                        Integer hasNum = userHasCountMap.get(expertUser.getName());
                        userHasCountMap.put(expertUser.getName(),hasNum+1);

                    }else {
                        //专家B和案卷同省规避
                        Integer aId = file.getExpertAId();
                        String aAreaCode = userProvinceMap.get(aId);
                        //AB专家同省规避
                        if (!aAreaCode.equals(userAreaCode)){
                            typeList.remove(file);
                            fileList.remove(file);
                            ExpertHandlFileList handel = new ExpertHandlFileList();
                            handel.setFileid(file.getId());
                            handel.setFilecode(file.getFileCode());
                            handel.setExpertid(expertUser.getId());
                            handel.setExpertname(expertUser.getName());
                            handel.setScoredstate("0");//0代表未评审
                            handel.setHandltype(0);//0代表普通专家打分
                            handleList.add(handel);

                            Files files = new Files();
                            files.setId(file.getId());
                            files.setExpertbid(expertUser.getId());
                            files.setExpertbname(expertUser.getName());
                            updateFileList.add(files);
                            expertUser.setFilenum(filenum-1);
                            Integer hasNum = userHasCountMap.get(expertUser.getName());
                            userHasCountMap.put(expertUser.getName(),hasNum+1);
                        }
                    }
                }


            }
        }
        System.out.println("---------------------------------------------");
        System.out.println("循环了"+num+"次,剩下"+fileList.size()+"个案卷没分,专家还剩"+userList.size()+"个,案卷总数:"+allFileList.size()+"---------------------------------------------");
        if (fileList.size()>0 && userList.size()>0){
            for (ExpertUser user : userList) {
                System.out.println(user.getName()+"专家,剩余fileNum:"+user.getFilenum()+",已经被分了:"+userHasCountMap.get(user.getId())+"个------------end");
            }
        }
//        List<String> ceshi = userHasCountMap.entrySet().stream().sorted(Map.Entry.comparingByValue()).map(Map.Entry::getKey).collect(Collectors.toList());
//        for (String name : ceshi) {
//            System.out.println("**************专家:"+name+"的已抽取的个数是:"+userHasCountMap.get(name));
//        }

        //如果还有案卷没分出去,说明因同省规避问题,专家接收不了这个案卷,需要给这个案卷重新找个专家安排上,如果是A轮就只和案卷同省规避,如果是B轮,就需要案卷+A专家+B专家同省规避
        if (fileList.size()>0){
            bakFor:
            for (FilesVo filesVo : fileList) {
                Integer fileType = filesVo.getFileType();
                String areaCode = filesVo.getAreaCode().substring(0, 2);

                List<String> userHasNumList = userHasCountMap.entrySet().stream().sorted(Map.Entry.comparingByValue()).map(Map.Entry::getKey).collect(Collectors.toList());
                bakUserList.sort((o1, o2) -> {
                    int index1 = userHasNumList.indexOf(o1.getName());
                    int index2 = userHasNumList.indexOf(o2.getName());
                    return Integer.compare(index1, index2);
                });

//                bakUserList.sort((Comparator.comparingInt(ExpertUser::getFilenum)));
                for (ExpertUser bakUser : bakUserList) {
                    String userAreaCode = bakUser.getAreacode().substring(0, 2);
                    int bakUserType = Integer.parseInt(bakUser.getType());
                    if (userAreaCode.equals(areaCode)){continue;}
                    if (bakUserType != fileType){continue;}
                    if ("A".equals(AOrB)){
                        ExpertHandlFileList handel = new ExpertHandlFileList();
                        handel.setFileid(filesVo.getId());
                        handel.setFilecode(filesVo.getFileCode());
                        handel.setExpertid(bakUser.getId());
                        handel.setExpertname(bakUser.getName());
                        handel.setScoredstate("0");//0代表未评审
                        handel.setHandltype(0);//0代表普通专家打分
                        handleList.add(handel);

                        Files files = new Files();
                        //回写案卷信息表中：专家信息
                        files.setId(filesVo.getId());
                        files.setExpertaid(bakUser.getId());
                        files.setExpertaname(bakUser.getName());
                        updateFileList.add(files);

                        Integer hasNum = userHasCountMap.get(bakUser.getName());
                        userHasCountMap.put(bakUser.getName(),hasNum+1);
                        continue bakFor;
                    }else {
                        Integer aUserId = filesVo.getExpertAId();
                        String aAreaCode = userProvinceMap.get(aUserId);
                        if (aAreaCode.equals(userAreaCode)){continue;}
                        ExpertHandlFileList handel = new ExpertHandlFileList();
                        handel.setFileid(filesVo.getId());
                        handel.setFilecode(filesVo.getFileCode());
                        handel.setExpertid(bakUser.getId());
                        handel.setExpertname(bakUser.getName());
                        handel.setScoredstate("0");//0代表未评审
                        handel.setHandltype(0);//0代表普通专家打分
                        handleList.add(handel);

                        Files files = new Files();
                        files.setId(filesVo.getId());
                        files.setExpertbid(bakUser.getId());
                        files.setExpertbname(bakUser.getName());
                        updateFileList.add(files);

                        Integer hasNum = userHasCountMap.get(bakUser.getName());
                        userHasCountMap.put(bakUser.getName(),hasNum+1);
                        continue bakFor;
                    }
                }

            }

        }

        // 批量新增专家评审
        expertHandlFileMapper.insertBatchList(handleList);
        //批量更新案卷信息（回写专家评审人员信息）
        filesMapper.updateFilesOfExpertByBatchList(updateFileList);

    }

    /***
     *  A专家计算fileNum,计算每个专家评什么类型的案卷,会被分到多少案卷
     *
     * @param allTypeFileList 所有 待分配案卷
     * @param avgUserFile 平均每个用户大概分多少案卷
     * @param userList 专家用户List
     * @param userHasNumberMap 记录专家已经被分了多少个了
     * @param typeProvinceCountMap 记录这个类型被分给了哪些省
     *
     */
    private List<ExpertUser> avgFilesToUserA(List<List<FilesVo>> allTypeFileList,
                                int avgUserFile,
                                List<ExpertUser> userList,
                                Map<String, Integer> userHasNumberMap,
                                Map<String, Integer> userNeedNumberMap,
                                            Map<String, List<String>> typeProvinceCountMap,
                                            List<FileIsDrawProvince> provinceList){
        List<ExpertUser> userFileList = new ArrayList<>();

        //待分配的专家
        List<ExpertUser> expertUserList = new ArrayList<>();
        expertUserList = userList;

        //循环7种类型的案卷List
        for (List<FilesVo> fileList : allTypeFileList) {
            //案卷类型
            Integer fileType = fileList.get(0).getFileType();
            List<String> initProvinceList = new ArrayList<>();
            //记录每个省有多少案卷
            Map<String, Integer> provinceFileCountMap = new HashMap<>();
            for (FileIsDrawProvince province : provinceList) {
                provinceFileCountMap.put(province.getProvince().substring(0,2),0);
            }
            for (FilesVo filesVo : fileList) {
                String province = filesVo.getAreaCode().substring(0, 2);
                Integer num = provinceFileCountMap.get(province);
                provinceFileCountMap.put(province,num+1);
            }

            // 根据数量排序一下省,根据排序的省,在排序一下剩余的专家
            //将provinceFileCountMap通过个数排序,生成省的List
            List<String> provinceOrderList = provinceFileCountMap.entrySet().stream().sorted(Map.Entry.comparingByValue()).map(Map.Entry::getKey).collect(Collectors.toList());
            //配套时:配套5种类型,每种类型的平均个数少,优先让没有这个类型的省的专家评这个类型
            expertUserList.sort((o1, o2) -> {
                int index1 = provinceOrderList.indexOf(o1.getAreacode().substring(0,2));
                int index2 = provinceOrderList.indexOf(o2.getAreacode().substring(0,2));
                return Integer.compare(index1, index2);
            });
            //调整这个List排序,不重复排序,将重复的排到最后面去
            //

            //案卷数量
            int fileCount = fileList.size();
            //计算大概需要分给多少专家用户
            int avgUser = fileCount / avgUserFile;
            //分配出去的总数
            int useFileCount = 0;
            for (int i = 0; i <= avgUser; i++) {
                if (useFileCount>=fileCount){continue;}
                //获取用户,并获取这个用户可以评多少个案卷
                ExpertUser expertUser = expertUserList.get(0);
                //这个用户可以评的数量
                int filenum = expertUser.getFilenum();
                //剩余待分配的数量
                int balance = fileCount - useFileCount;
                if (balance>0){
                    //如果这个用户的数量,大于剩余案卷数量,则拆分用户.否则就正常分个数
                    if (filenum > balance){
                        ExpertUser newUser = new ExpertUser();
                        BeanUtils.copyProperties(expertUser,newUser);
                        newUser.setId(null);
                        //分配剩余案卷个数
                        newUser.setFilenum(balance);
                        //赋值类型
                        newUser.setType(fileType.toString());
                        newUser.setIsNeedNew(1);
                        //把他的登录名值设为空,重新赋值新的登录名
                        newUser.setLoginname(null);

                        //获取这个用户已被分配的个数
                        Integer userHasNum = userHasNumberMap.get(newUser.getAreacode() + newUser.getName());
                        //给这个用户赋值新的分给他的案卷数
                        userHasNumberMap.put(newUser.getAreacode()+newUser.getName(),balance+userHasNum);
                        //添加到List
                        initProvinceList.add(newUser.getAreacode().substring(0,2));
                        userFileList.add(newUser);
                        useFileCount = useFileCount + balance;
                        //剩余可评个数
                        int moreCount = filenum - balance;
                        //如果这个专家还有可评额度,就更新剩余额度,没有的话,就移除这个用户
                        if (moreCount>0){
                            //这个专家还可以评的个数
                            expertUser.setFilenum(moreCount);
                        }else {
                            expertUserList.remove(0);
                        }

                    }else {
                        //剩余案卷数大于专家可评数量 //filenum这个专家可评的数量
                        //获取这个用户已被分配的个数
                        Integer userHasNum = userHasNumberMap.get(expertUser.getAreacode() + expertUser.getName());
                        //给这个用户赋值新的分给他的案卷数
                        userHasNumberMap.put(expertUser.getAreacode()+expertUser.getName(),userHasNum+filenum);
                        //赋值类型
                        expertUser.setType(fileType.toString());

                        useFileCount = useFileCount + filenum;
                        initProvinceList.add(expertUser.getAreacode().substring(0,2));
                        userFileList.add(expertUser);
                        expertUserList.remove(0);
                    }

                }

            }
            //如果上面循环没分完这个类型,就要走下面
            //剩余要分的案卷,剩的这个案卷一定比专家能评的案卷数量小
            int splitCount = fileCount - useFileCount;
            if (splitCount>0){
                //获取一个新的专家去处理剩余的这些案卷
                ExpertUser expertUser = expertUserList.get(0);
                //这个专家可以评的案卷数
                int filenum = expertUser.getFilenum();
                //剩余可评个数
                int moreCount = filenum - splitCount;

                //如果这个用户的数量,大于剩余案卷数量,则拆分用户.否则就正常分个数
                if (filenum > splitCount){
                    ExpertUser newUser = new ExpertUser();
                    BeanUtils.copyProperties(expertUser,newUser);
                    newUser.setId(null);
                    newUser.setFilenum(splitCount);
                    newUser.setType(fileType.toString());
                    newUser.setIsNeedNew(1);
                    //把他的登录名值设为空,重新赋值新的登录名
                    newUser.setLoginname(null);
                    Integer userHasNum = userHasNumberMap.get(expertUser.getAreacode() + expertUser.getName());

                    userHasNumberMap.put(newUser.getAreacode()+newUser.getName(),userHasNum+filenum);
                    initProvinceList.add(newUser.getAreacode().substring(0,2));
                    userFileList.add(newUser);
                    if (moreCount>0){
                        //这个专家还可以评的个数
                        expertUser.setFilenum(moreCount);
                    }else {
                        expertUserList.remove(0);
                    }
                }else {
                    //剩余案卷数大于专家可评数量 //filenum这个专家可评的数量
                    //获取这个用户已被分配的个数
                    Integer userHasNum = userHasNumberMap.get(expertUser.getAreacode() + expertUser.getName());
                    //给这个用户赋值新的分给他的案卷数
                    userHasNumberMap.put(expertUser.getAreacode()+expertUser.getName(),userHasNum+filenum);
                    //赋值类型
                    expertUser.setType(fileType.toString());

                    useFileCount = useFileCount + filenum;
                    initProvinceList.add(expertUser.getAreacode().substring(0,2));
                    userFileList.add(expertUser);
                    expertUserList.remove(0);
                }
            }
            typeProvinceCountMap.put(fileType.toString(),initProvinceList);
        }
        return userFileList;
    }
    private List<ExpertUser> avgFilesToUserB(List<List<FilesVo>> allTypeFileList,
                                int avgUserFile,
                                List<ExpertUser> userList,
                                Map<String, Integer> userHasNumberMap,
                                Map<String, Integer> userNeedNumberMap,
                                            Map<String, List<String>> typeProvinceCountMap,
                                            List<FileIsDrawProvince> provinceList){
        List<ExpertUser> userFileList = new ArrayList<>();

        //待分配的专家
        List<ExpertUser> expertUserList = new ArrayList<>();
        expertUserList = userList;

        //循环7种类型的案卷List
        for (List<FilesVo> fileList : allTypeFileList) {
            //案卷类型
            Integer fileType = fileList.get(0).getFileType();
//            List<String> initProvinceList = new ArrayList<>();
            //查看这个类型在A专家那里被分给了哪些省
            List<String> AProvinceList = typeProvinceCountMap.get(fileType.toString());
            //记录每个省有多少案卷
            Map<String, Integer> provinceFileCountMap = new HashMap<>();
            for (FileIsDrawProvince province : provinceList) {
                provinceFileCountMap.put(province.getProvince().substring(0,2),0);
            }
            for (FilesVo filesVo : fileList) {
                String province = filesVo.getAreaCode().substring(0, 2);
                Integer num = provinceFileCountMap.get(province);
                provinceFileCountMap.put(province,num+1);
            }

            // 根据数量排序一下省,根据排序的省,在排序一下剩余的专家
            //将provinceFileCountMap通过个数排序,生成省的List
            List<String> provinceOrderList = provinceFileCountMap.entrySet().stream().sorted(Map.Entry.comparingByValue()).map(Map.Entry::getKey).collect(Collectors.toList());
            //配套时:配套5种类型,每种类型的平均个数少,优先让没有这个类型的省的专家评这个类型
            expertUserList.sort((o1, o2) -> {
                int index1 = provinceOrderList.indexOf(o1.getAreacode().substring(0,2));
                int index2 = provinceOrderList.indexOf(o2.getAreacode().substring(0,2));
                return Integer.compare(index1, index2);
            });
            expertUserList = expertUserList.stream()
                    .sorted((u1, u2) -> {
                        boolean u1InList = AProvinceList.contains(u1.getAreacode().substring(0,2));
                        boolean u2InList = AProvinceList.contains(u2.getAreacode().substring(0,2));
                        if (u1InList && !u2InList) {
                            return 1;
                        } else if (!u1InList && u2InList) {
                            return -1;
                        } else {
                            return 0;
                        }
                    })
                    .collect(Collectors.toList());

            //案卷数量
            int fileCount = fileList.size();
            //计算大概需要分给多少专家用户
            int avgUser = fileCount / avgUserFile;
            //分配出去的总数
            int useFileCount = 0;
            for (int i = 0; i <= avgUser; i++) {
                if (useFileCount>=fileCount){continue;}
                //获取用户,并获取这个用户可以评多少个案卷
                ExpertUser expertUser = expertUserList.get(0);
                //这个用户可以评的数量
                int filenum = expertUser.getFilenum();
                //剩余待分配的数量
                int balance = fileCount - useFileCount;
                if (balance>0){
                    //如果这个用户的数量,大于剩余案卷数量,则拆分用户.否则就正常分个数
                    if (filenum > balance){
                        ExpertUser newUser = new ExpertUser();
                        BeanUtils.copyProperties(expertUser,newUser);
                        newUser.setId(null);
                        //分配剩余案卷个数
                        newUser.setFilenum(balance);
                        //赋值类型
                        newUser.setType(fileType.toString());
                        newUser.setIsNeedNew(1);
                        //把他的登录名值设为空,重新赋值新的登录名
                        newUser.setLoginname(null);

                        //获取这个用户已被分配的个数
                        Integer userHasNum = userHasNumberMap.get(newUser.getAreacode() + newUser.getName());
                        //给这个用户赋值新的分给他的案卷数
                        userHasNumberMap.put(newUser.getAreacode()+newUser.getName(),balance+userHasNum);

                        userFileList.add(newUser);
                        useFileCount = useFileCount + balance;
                        //剩余可评个数
                        int moreCount = filenum - balance;
                        //如果这个专家还有可评额度,就更新剩余额度,没有的话,就移除这个用户
                        if (moreCount>0){
                            //这个专家还可以评的个数
                            expertUser.setFilenum(moreCount);
                        }else {
                            expertUserList.remove(0);
                        }

                    }else {
                        //剩余案卷数大于专家可评数量 //filenum这个专家可评的数量
                        //获取这个用户已被分配的个数
                        Integer userHasNum = userHasNumberMap.get(expertUser.getAreacode() + expertUser.getName());
                        //给这个用户赋值新的分给他的案卷数
                        userHasNumberMap.put(expertUser.getAreacode()+expertUser.getName(),userHasNum+filenum);
                        //赋值类型
                        expertUser.setType(fileType.toString());

                        useFileCount = useFileCount + filenum;

                        userFileList.add(expertUser);
                        expertUserList.remove(0);
                    }

                }

            }
            //如果上面循环没分完这个类型,就要走下面
            //剩余要分的案卷,剩的这个案卷一定比专家能评的案卷数量小
            int splitCount = fileCount - useFileCount;
            if (splitCount>0){
                //获取一个新的专家去处理剩余的这些案卷
                ExpertUser expertUser = expertUserList.get(0);
                //这个专家可以评的案卷数
                int filenum = expertUser.getFilenum();
                //剩余可评个数
                int moreCount = filenum - splitCount;

                //如果这个用户的数量,大于剩余案卷数量,则拆分用户.否则就正常分个数
                if (filenum > splitCount){
                    ExpertUser newUser = new ExpertUser();
                    BeanUtils.copyProperties(expertUser,newUser);
                    newUser.setId(null);
                    newUser.setFilenum(splitCount);
                    newUser.setType(fileType.toString());
                    newUser.setIsNeedNew(1);
                    //把他的登录名值设为空,重新赋值新的登录名
                    newUser.setLoginname(null);
                    Integer userHasNum = userHasNumberMap.get(expertUser.getAreacode() + expertUser.getName());

                    userHasNumberMap.put(newUser.getAreacode()+newUser.getName(),userHasNum+filenum);

                    userFileList.add(newUser);
                    if (moreCount>0){
                        //这个专家还可以评的个数
                        expertUser.setFilenum(moreCount);
                    }else {
                        expertUserList.remove(0);
                    }
                }else {
                    //剩余案卷数大于专家可评数量 //filenum这个专家可评的数量
                    //获取这个用户已被分配的个数
                    Integer userHasNum = userHasNumberMap.get(expertUser.getAreacode() + expertUser.getName());
                    //给这个用户赋值新的分给他的案卷数
                    userHasNumberMap.put(expertUser.getAreacode()+expertUser.getName(),userHasNum+filenum);
                    //赋值类型
                    expertUser.setType(fileType.toString());

                    useFileCount = useFileCount + filenum;

                    userFileList.add(expertUser);
                    expertUserList.remove(0);
                }
            }

        }
        return userFileList;
    }



     /**
     　* 方法:
       * 计算专家分配个数
     　* <AUTHOR>
     　* @date 2024-05-31 16:05:53
     　*/
    private int computNumber(List<FilesVo> fileList,List<ExpertUser> userList){
        //总案卷数
        int fileSize = fileList.size();
        //a专家总数
        int userSize = userList.size();
        // 计算每位专家基本分配到的案卷数
        int filesPerUser = fileSize / userSize;

        for (ExpertUser expertUser : userList) {
            expertUser.setFilenum(filesPerUser);
        }
        // 计算不能均匀分配的剩余案卷数
        int remainingFiles = fileSize % userSize;

        if (remainingFiles>0){
            // 为部分专家分配额外的案卷，直到剩余案卷分配完毕
            for (int i = 0; i < remainingFiles; i++) {
                ExpertUser expertUser = userList.get(i);
                int filenum = expertUser.getFilenum();
                expertUser.setFilenum(filenum + 1);
            }
        }
        return filesPerUser;
    }












}
