package com.changneng.sa.service.impl;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import com.changneng.sa.bean.*;
import com.changneng.sa.dao.*;
import com.changneng.sa.util.JsonResult;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.changneng.sa.service.SysManageService;
import com.changneng.sa.util.Const;
import com.changneng.sa.util.PageBean;
import com.github.pagehelper.PageHelper;

import static java.math.BigDecimal.ROUND_HALF_UP;

@Service
public class SysManageServiceIm implements SysManageService {

	@Autowired
	private ProvinceReportUserMapper provinceReportUserMapper;
	@Autowired
	private TareaMapper treaMapper;
	@Autowired
	private ElectionUnitMapper electionUnitMapper;
	@Autowired
	private FilesMapper fileMapper;
	@Autowired
	private ScoringIndexMapper scoringIndexMapper;
	@Autowired
	private ScoringItemMapper scoringItemMapper;
	@Autowired
	private ProvinceIndexMapper provinceIndexMapper;


	@Override
	public List<province> getCityByProvince(HashMap<String, String> map) {
		return provinceReportUserMapper.selectCityByProvince(map);
	}
	@Override
	public int deleteCity(String areaCode) {
		int result = provinceReportUserMapper.deleteCity(areaCode)+electionUnitMapper.deleteByCode(areaCode);
		return result;
	}
	@Override
	public int updateSaveCity(LoginUser loginUser ) {
		HashMap<String, String> map = new HashMap<>();
		map.put("areaCode", loginUser.getAreaCode().substring(0, 2));
		map.put("areaType", "2");
		return provinceReportUserMapper.updateCitySubmit(map);
	}
	@Override
	public List<Tarea> getCountyCityByCode(HashMap<String, String> map) {
		return treaMapper.selectcountyCityByCode(map);
	}
	@Override
	public List<Tarea> getCountyByCode(HashMap<String, String> map) {
		return treaMapper.selectCountyByCode(map);
	}
	@Override
	public Tarea getCountyOne(String areaCode) {
		
		return treaMapper.selectTarea(areaCode);
	}
	@Override
	public int updateSaveCounty(LoginUser loginUser) {
		HashMap<String, String> map = new HashMap<>();
		map.put("areaCode", loginUser.getAreaCode().substring(0, 2));
		map.put("areaType","3");
		return provinceReportUserMapper.updateCountySubmit(map);
	}
	@Override
	public List<province> selectAllByProvince(String areaCode) {
		String areacode = areaCode.substring(0, 2);
		return provinceReportUserMapper.selectAllByProvince(areacode);
	}
	
	
	@Override
	public int alterPass(HashMap<String, String> map) {
		return provinceReportUserMapper.updatePassByAreaCode(map);
	}
	@Override
	public int getSumUser(HashMap<String, String> map) {
		
		return provinceReportUserMapper.countByAreaCode(map);
	}
	@Override
	public PageBean<ExtractBean> getExtractBean(int pageNum,HashMap<String, String> map) {
		if(pageNum == 0){
			pageNum =1;
		}
		PageHelper.startPage(pageNum, 15);
		return  new PageBean<ExtractBean>(fileMapper.selectExtract(map))  ;
	}
	@Override
	public List<ExtractBean> getExtractExcel(HashMap<String, String> map) {
		return fileMapper.selectExtract(map);
	}
	@Override
	public List<Files> getAnJuanHao(HashMap<String, String> map) {
		return fileMapper.selectAnJuanHao(map);
	}
	@Override
	public int updateAnJuanHao(HashMap<String, String> map) {
		return fileMapper.updateAnJuanHao(map);
	}
	@Override
	public List<UnitBean> getCount() {
		List<UnitBean> listResult =  provinceReportUserMapper.selectProvince();
		List<UnitBean> listCount = provinceReportUserMapper.selectCountUnit();
		for(int i=0;i<listCount.size();i++){
			for(int j=0;j<listResult.size();j++){
				if(listResult.get(j).getAreaCode().equals(listCount.get(i).getAreaCode())){
					if(listCount.get(i).getAreaType().equals("2")){
						listResult.get(j).setCityCount(Integer.valueOf(listCount.get(i).getTotal()));
						listResult.get(j).setAreaCode(listCount.get(i).getAreaCode());
					}
					if(listCount.get(i).getAreaType().equals("3")){
						listResult.get(j).setCountryCount(Integer.valueOf(listCount.get(i).getTotal()));
						listResult.get(j).setAreaCode(listCount.get(i).getAreaCode());
					}
				}
			}
		}
		 Collections.sort(listResult, new Comparator<UnitBean>() {
			public int compare(UnitBean u1,UnitBean u2){
				if(Integer.valueOf(u1.getAreaCode())>Integer.valueOf(u2.getAreaCode())){
					return 1;
				}
				if(Integer.valueOf(u1.getAreaCode())==Integer.valueOf(u2.getAreaCode())){
					return 0;
				}
				return -1;
			}
		});
		
		return listResult ;
	}
	@Override
	public int setScoreIndex(ScoringIndex scoringIndex) {
		return scoringIndexMapper.insertSelective(scoringIndex);
	}
	@Override
	public List<ScoringIndex> getScoringIndex(HashMap<String, String> map) {
		return scoringIndexMapper.selectAll(map);
	}
	@Override
	public int updateScoringIndex(ScoringIndex scoringIndex) {
		return scoringIndexMapper.updateByPrimaryKeySelective(scoringIndex);
	}
	@Override
	public int deleteScoringIndexByID(int id) {
		return scoringIndexMapper.deleteById(id);
	}
	@Override
	public int setScoringItem(ScoringItem scoringItem) {
		return scoringItemMapper.insertSelective(scoringItem);
	}
	@Override
	public List<ScoringItem> getScoringItem(Integer indexID) {
		return scoringItemMapper.getScoringItemList(indexID);
	}
	@Override
	public int updateScoringItem(ScoringItem scoringItem) {
		return scoringItemMapper.updateByPrimaryKeySelective(scoringItem);
	}
	@Override
	public int deleteScoringItem(Integer id) {
		return scoringItemMapper.deleteByPrimaryKey(id);
	}
	@Override
	public PageBean<PublicListBean> getPublicList(int pageNum,Files file) {
		if(pageNum == 0){
			pageNum =1;
		}
		PageHelper.startPage(pageNum, Const.NOPERPAGE);
		return   new PageBean<>(fileMapper.selectFileAlter(file));
	}
	@Override
	public List<PublicListBean> getOutExcel(Files files) {
		return fileMapper.selectFileAlter(files);
	}
	@Override
	public int deleteScoringItemByID(int id) {
		return scoringItemMapper.deleteByPrimaryKey(id);
	}
	@Override
	public int configDevStaDlbInner(ProvinceReportUser record) {
		
		return provinceReportUserMapper.updateByPrimaryKeySelective(record);
	}
	@Override
	public ProvinceReportUser getUnitReportUserById(Integer id) {
		
		return provinceReportUserMapper.selectByPrimaryKey(id);
	}
	@Override
	public List<Map<String, Object>> totalIndexByAreaCode(String areaCode) {
		
		return provinceIndexMapper.totalIndexByAreaCode(areaCode);
	}
	
	/**
	 * <AUTHOR> 2018-07-11
	 * 根据areaCode获取当前省级下市级用户列表(2018)
	 * @param areaCode
	 * @return
	 */
	@Override
	public List<province> selectProvinceUser(String areaCode) {
		String areacode = areaCode.substring(0, 2);
		return provinceReportUserMapper.selectProvinceUser(areacode);
	}
	
	/**
	 * <AUTHOR> 2018-07-11
	 * 根据areaCode获取当市级以下县级用户列表(2018)
	 * @param areaCode
	 * @return
	 */
	@Override
	public List<province> selectCountyUser(String areaCode) {
		String areacode = areaCode.substring(0, 4);
		return provinceReportUserMapper.selectCountyUser(areacode);
	}
	/**
	 * 省级是否自评选定后修改所有本省的市县状态；
	 */
	@Override
	public int updateIsDevInternalEva(ProvinceReportUser record) {
		
		return provinceReportUserMapper.updateIsDevInternalEva(record);
	}
	/**
	 * <AUTHOR> 2018-07-27
	 * 国家级账号登录查询省级用户数据(2018)
	 * @return
	 */
	@Override
	public List<province> selectcountryUser() {
		return provinceReportUserMapper.selectcountryUser();
	}

}
