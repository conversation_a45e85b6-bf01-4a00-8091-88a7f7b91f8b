package com.changneng.sa.service.impl;

import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;


import com.changneng.sa.bean.*;
import com.changneng.sa.dao.*;
import com.changneng.sa.util.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.apache.poi.ss.formula.functions.T;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import com.changneng.sa.service.ISysManageService;
import com.github.pagehelper.PageHelper;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpServletResponseWrapper;

import static com.changneng.sa.util.DownloadFilesUrl.readInputStream;

/**
 * 系统管理service
 *
 * <AUTHOR>
 *
 */
@Service("sysManageService")
public class SysManageServiceImpl implements ISysManageService {

	public static Logger logger = Logger.getLogger(SysManageServiceImpl.class);
	@Autowired
	private TareaMapper areaMapper;
	@Autowired
	private ProvinceReportUserMapper provinceUserMapper;
	@Autowired
	private ExpertUserMapper expertUserMapper;
	@Autowired
	private CrossUserMapper crossUserMapper;
	@Autowired
	private SysUserMapper sysUserMapper;
	@Autowired
	private SysInitConfigMapper sysInitMapper;
	@Autowired
	private FilesMapper filesMapper;
	@Autowired
	public AjpfMapper ajpfMapper;
	@Autowired
	private ExpertHandlFileListMapper expertHandlFileMapper;
	@Autowired
	private ExpertHandlIndexScoreMapper expertHandlIndexScoreMapper;
	@Autowired
	private ExpertHandlItemScoreMapper expertHandlItemScoreMapper;
	@Autowired
	private CrossHandlFileListMapper crossHandlFileListMapper;
	@Autowired
	private ElectionPersonalMapper electionPersonalMapper;
	@Autowired
	private CrossPersonalFileListMapper crossPersonalFileListMapper;
	@Autowired
	private SysLogMapper sysLogMapper;
	@Autowired
	private PenalizeSyncFileMapper penalizeSyncFileMapper;
	@Autowired
	private ElectionUnitMapper electionUnitMapper;
	@Autowired
	private provinceSelectionUnitMapper provinceSelectionUnitMapper;

	@Autowired
	private ProvinceElectionPersonalMapper provinceElectionPersonalMapper;

	@Autowired
	public ExpertHandlFileListMapper expertMapper;

	@Autowired
	private TCDictionaryMapper tCDictionaryMapper;

	@Autowired
	private TempAddFilesMapper tempAddFileMapper;

	@Autowired
	private zhpgUserRelunitListMapper zhpg_UserRelunitListMapper;

	@Autowired
	private zhpgExtrudeScoreMapper zhpg_ExtrudeScoreMapper;

	@Autowired
    private PhoneRUserMapper phoneRUserMapper;

	@Autowired
	private StringRedisTemplate stringRedisTemplate;

	/**
	 * 根据登录id、密码获取area信息
	 */
	@Override
	public Tarea getTareaByLoginIdAndPass(Tarea record) throws Exception {
		return areaMapper.getTareaByLoginIdAndPass(record);
	}

	/**
	 * 重置密码
	 */
	@Override
	public void resetPassword(Tarea record) throws Exception {
		areaMapper.updatePasswordById(record);
	}

	/**
	 * 根据登录id,密码，用户类型获取登录用户信息
	 */
	@Override
	public void getLoginUserByLoginIdAndPassAndType(LoginUser record) throws Exception {
		if (record != null) {
			record.setFlag(false);
			String userTypeCode = record.getUserTypeCode();
			if (userTypeCode.equals("1")) {
				// 省级登录用户
				ProvinceReportUser user = provinceUserMapper.selectByLoginidAndPassword(record);
				if (user != null) {
					String reportState = ""+user.getReportstate();
					if("null".equals(reportState)||"".equals(reportState)){
						reportState = provinceUserMapper.selectReportByAreaCode(user.getAreacode().substring(0, 2)+"000000");
					}

					record.setId(user.getId());
					record.setIsreg(Const.YES);
					record.setAreaCode(user.getAreacode());
					record.setUserName(user.getName());
					record.setFlag(true);
					record.setArealevel(user.getAreatype());
					if(reportState!=null){
						record.setReportState(reportState);
					}
					//省盖章文件
					record.setFileName(user.getFileName());
					record.setFileUrl(user.getFileUrl());
					/*record.setIsDevDlbInner(user.getIsDevInternalEva());
					record.setElectcityState(user.getElectcityState());
					record.setElectcountyState(user.getElectcountyState());*/

					record.setUserType("填报用户");
				}
			} else if (userTypeCode.equals("2")) {
				// 专家评审
				ExpertUser user = expertUserMapper.selectByloginNameAndPassword(record);
				if (user != null) {
					record.setId(user.getId());
					record.setIsreg(Const.YES);
					record.setUserName(user.getName());
					record.setFlag(true);
					record.setUserType("专家评审用户");
					record.setExpertType(user.getType());
					record.setLevel(user.getLevel());
					record.setErrorState(user.getErrorState());
					record.setErrorRate(user.getErrorRate());
				}

			} else if (userTypeCode.equals("3")) {
				// 交叉评审
				CrossUser user = crossUserMapper.selectByLoginNameAndPassword(record);
				if (user != null) {
					record.setId(user.getId());
					record.setAreaCode(user.getAreacode());
					record.setUserName(user.getName());
					record.setFlag(true);
					record.setUserType("交叉评审用户");
					record.setCrossType(user.getCrossType());
				}
			}else{
				// 系统管理
				SysUser user = sysUserMapper.selectByLoginNameAndPassword(record);
				if (user != null) {
					record.setId(user.getId());
					record.setAreaCode("00000000");
					record.setIsreg(Const.YES);
					record.setUserName(user.getName());
					record.setLoginid(user.getLoginname());
					record.setFlag(true);
					record.setUserType("系统管理员");
				}
			}

		}

	}

	@Override
	public void resetPassword(LoginUser record) throws Exception {
		String userTypeCode = record.getUserTypeCode();
		if (userTypeCode.equals("1")) {
			// 省级登录用户密码重置
			provinceUserMapper.updatePasswordById(record);
		} else if (userTypeCode.equals("2")) {
			// 专家评审用户密码重置
			expertUserMapper.updatePasswordByIdSelective(record);

		} else if (userTypeCode.equals("3")) {
			// 交叉评审用户密码修改
			crossUserMapper.updatePasswordByIdSelective(record);
		}else{
			// 系统管理用户重置密码
			sysUserMapper.updatePasswordByIdSelective(record);
		}
	}

	/**
	 * 获取省级填报人员List
	 */
	@Override
	public List<ProvinceReportUser> getProvinceReportUserList() throws Exception {
		return provinceUserMapper.selectList();

	}

	@Override
	public PageBean<ExpertUser> getExpertUserByPara(Integer pageNum, ExpertUser user) throws Exception {
		if (pageNum == null || pageNum == 0)
			pageNum = 1;
		// Const.NOPERPAGE
//		if (!ChangnengUtil.isNull(user.getType())) {
//			user.setType(new String(user.getType().getBytes("ISO8859-1"), "UTF-8"));
//		}
//		if (!ChangnengUtil.isNull(user.getName())) {
//			user.setName(new String(user.getName().getBytes("ISO8859-1"), "UTF-8"));
//		}
		PageHelper.startPage(pageNum, Const.NOPERPAGE);
		return new PageBean<ExpertUser>(expertUserMapper.selectExpertUserByPara(user));

	}




	@Override
	public PageBean<ExpertUser> getExpertUserByParaCount(Integer pageNum, ExpertUser user) {
		if (pageNum == null || pageNum == 0)
			pageNum = 1;
		PageHelper.startPage(pageNum, Const.NOPERPAGE);
		return new PageBean<ExpertUser>(expertUserMapper.selectExpertUserByParaCount(user));

	}

	@Override
	public List<ExpertUser> getExpertUserByParaCountExecl() {
		return (expertUserMapper.getExpertUserByParaCountExecl());
	}


	@Override
	public PageBean<CrossUser> getCrosstUserByPara(Integer pageNum, CrossUser user) throws Exception {
		if (pageNum == null || pageNum == 0)
			pageNum = 1;
		// Const.NOPERPAGE

//		if (!ChangnengUtil.isNull(user.getName())) {
//			user.setName(new String(user.getName().getBytes("ISO8859-1"), "UTF-8"));
//		}
		PageHelper.startPage(pageNum, Const.NOPERPAGE);
		return new PageBean<CrossUser>(crossUserMapper.selectCrosstUserByPara(user));
	}

	@Override
	public List<SysInitConfig> getSysInitListByType(String type) throws Exception {
		return sysInitMapper.selectListByActionType(type);
	}

	/**
	 * 分配专家评审案卷
	 */
	@Override
	public String initDistributeFileOfExpert() throws Exception {
		// 按案卷类型获取案件列表
		ExpertUser expert = new ExpertUser();
		StringBuffer result = new StringBuffer("");
		for (int type = 0; type < 6; type++) {

			// 当前类型下的案卷
			List<Files> fileList = filesMapper.selectListByFileType(type + "");
			expert.setType(type + "");
			// 当前类型的专家
			List<ExpertUser> expertList = expertUserMapper.selectExpertUserByPara(expert);
			if (fileList.size() > 0 && expertList.size() > 1) {
				initFileListToExpertHandle(fileList, expertList,"0",0,expertList.size()-1);
				result.append("success提示：案卷类型--【" + type + "】的案卷数量为" + fileList.size() +"，专家人数为："+expertList.size()+ ",分配成功*****<br/>");
				//System.out.println("提示：案卷类型--" + type + "的案卷数量为" + fileList.size() + ",分配成功*****<br/>");
			} else if (fileList.size() == 0) {
				result.append("error提示：案卷类型--" + type + "的案卷数量为零,没有分配案卷*****<br/>");
			} else {
				result.append("error提示：专家类型--" + type + "的专家数量不足两位,没有分配案卷*****<br/>");
			}
		}

		return result.toString();
	}



	/**
	 * 2019年
	 * 分配专家评审案卷
	 * 不包含发现问题的污染源现场监督检查稽查案卷   --》TC_dictionary-》6类型
	 */
	@Override
	public String initDistributeFileOfExpert2019() throws Exception {
		// 按案卷类型获取案件列表
		ExpertUser expertAseach = new ExpertUser();
		ExpertUser expertBseach = new ExpertUser();
		StringBuffer result = new StringBuffer("");
		for (int type = 0; type < 15; type++) {
			 if(type == 4 || type ==5 ){
				 continue;
			 }
			//当前类型下的案卷
			List<Files> fileList = filesMapper.selectListByFileType(type + "");//2019年
			//List<Files> fileList = filesMapper.selectListByFileTypeAndCrossConsiderScore(type + "",60+"");

			expertAseach.setType(type + "");
			expertBseach.setType(type + "");

			expertAseach.setExpertclass("0");
			expertAseach.setLevel(0);
			expertBseach.setExpertclass("1");
			expertBseach.setLevel(0);
			// 当前类型的专家
			List<ExpertUser> expertListA=null;//地方专家,有区划之分，尽量避免本地区的
			List<ExpertUser> expertListB=null;//司法专家，无区划之分，平均分配即可
			try {
				expertListA = expertUserMapper.selectGeneralExpertUserByPara2019(expertAseach);
				expertListB = expertUserMapper.selectGeneralExpertUserByPara(expertBseach);
			} catch (Exception e) {
				e.printStackTrace();
			}
			//0-地方专家分配方式
			if (fileList.size() > 0 && expertListA.size() > 0) {

				//实现目标：均分（不绝对）、随机且回避本省打本省的
				String res = initFilesToExpertsAvoid2019(fileList, expertListA);
				System.out.println("11111》》"+res);
				result.append("success提示：案卷类型--【" + type + "】的案卷数量为" + fileList.size() +"，专家人数为："+expertListA.size()+ ",分配成功*****<br/>");
			} else if (fileList.size() == 0) {
				result.append("error提示：案卷类型--" + type + "的案卷数量为零,地方专家无法分配案卷*****<br/>");
			} else {
				result.append("error提示：专家类型--" + type + "的地方专家数量不足,没有分配案卷*****<br/>");
			}

			//1-司法专家分配方式
			if (fileList.size() > 0 && expertListB.size() > 0) {

				//平均（不绝对）且随机分配
				String res = initFilesToExpertsAverage2019(fileList, expertListB);
				System.out.println("22222》》"+res);
				result.append("success提示：案卷类型--【" + type + "】的案卷数量为" + fileList.size() +"，专家人数为："+expertListB.size()+ ",分配成功*****<br/>");
			} else if (fileList.size() == 0) {
				result.append("error提示：案卷类型--" + type + "的案卷数量为零,司法专家无法分配案卷*****<br/>");
			} else {
				result.append("error提示：专家类型--" + type + "的司法专家数量不足,没有分配案卷*****<br/>");
			}

		}

		return result.toString();
	}

	/**
	 * 2019平均（非绝对）随机且回避原则分配（1个案卷分1个人）
	 * @param fileList
	 * @param expertList
	 * @return
	 */
	private String initFilesToExpertsAvoid2019(List<Files> files, List<ExpertUser> expertList){
		try {
			List<ExpertHandlFileList> handleList = new ArrayList<ExpertHandlFileList>();
			List<Files> updateFileList = new ArrayList<Files>();

			List<Files> fileList = new ArrayList<Files>();
			fileList.addAll(files);//这两句是因为remove会对传入的实参产生影响，所以必须另复制一份

			int fileNum = 0;//每个人大概的平均案卷数量
			if (fileList.size() > 0 && expertList.size() > 0) {
				fileNum = fileList.size()% expertList.size() > 0 ? fileList.size()/expertList.size() + 1 : fileList.size()/ expertList.size();

				for (ExpertUser user : expertList) {

					List<Files> userFiles = new ArrayList<Files>();
					boolean flag = true;
					List<Files> outFiles = new ArrayList<Files>();//本轮淘汰的案卷
					while((userFiles.size()<user.getFilenum()-1 ||userFiles.size()==user.getFilenum()-1)&&flag){
						if(fileList.size()>0){
							Files file = fileList.remove(new Random().nextInt(fileList.size()));
							if(!file.getAreacode().startsWith(user.getAreacode().substring(0, 2))){
//							if(!file.getAreacode().startsWith(user.getAreacode().substring(0, 4))){
								userFiles.add(file);
							}else{
								outFiles.add(file);
							}
						}else{//所有的案卷已经走完一遍，还没有够，也要退出循环
							flag =false;
						}
					}
					fileList.addAll(outFiles);


					for(Files expertFile:userFiles){
						ExpertHandlFileList handel = new ExpertHandlFileList();
						handel.setFileid(expertFile.getId());
						handel.setFilecode(expertFile.getFilecode());
						handel.setExpertid(user.getId());
						handel.setExpertname(user.getName());
						handel.setScoredstate("0");//0代表未评审
						handel.setHandltype(0);//0代表普通专家打分
						handleList.add(handel);

						//回写案卷信息表中：专家信息
						expertFile.setExpertaid(user.getId());
						expertFile.setExpertaname(user.getName());
						updateFileList.add(expertFile);
					}
				}


				//为了防止最后一个专家被同省规避  再走一遍循环逻辑
				if (fileList.size()>0){
					for (Files files1:fileList){
						for (ExpertUser user : expertList) {
							if(!files1.getAreacode().startsWith(user.getAreacode().substring(0, 2))){
								ExpertHandlFileList handel = new ExpertHandlFileList();
								handel.setFileid(files1.getId());
								handel.setFilecode(files1.getFilecode());
								handel.setExpertid(user.getId());
								handel.setExpertname(user.getName());
								handel.setScoredstate("0");//0代表未评审
								handel.setHandltype(0);//0代表普通专家打分
								handleList.add(handel);

								//回写案卷信息表中：专家信息
								files1.setExpertaid(user.getId());
								files1.setExpertaname(user.getName());
								updateFileList.add(files1);
								break;
							}
						}
					}
				}
			}else{
				return "案卷或人员信息异常：案卷"+fileList.size()+"份,地方专家人员"+expertList.size()+"个";
			}
			Collections.shuffle(handleList);//打乱顺序

			// 批量新增专家评审
			expertHandlFileMapper.insertBatchList(handleList);
			//批量更新案卷信息（回写专家评审人员信息）
		    filesMapper.updateFilesOfExpertByBatchList(updateFileList);

			return "地方专家人员案卷分配完毕,案卷"+fileList.size()+"份,地方专家人员"+expertList.size()+"个,每个地方专家分配约"+fileNum+"(+_1)份案卷";
		}catch(Exception e){
			e.printStackTrace();
			return "后台出现未知异常，执行失败";
		}
	}

	/**
	 * 2019平均（非绝对）随机原则分配（1个案卷分1个人）
	 * @param fileList
	 * @param expertList
	 * @return
	 */
	private String initFilesToExpertsAverage2019(List<Files> files, List<ExpertUser> expertList){
		try {
			List<ExpertHandlFileList> handleList = new ArrayList<ExpertHandlFileList>();
			List<Files> updateFileList = new ArrayList<Files>();

			List<Files> fileList = new ArrayList<Files>();
			fileList.addAll(files);//这两句是因为remove会对传入的实参产生影响，所以必须另复制一份

			int fileNum = 0;//每个人大概的平均案卷数量
			if (fileList.size() > 0 && expertList.size() > 0) {
				fileNum = fileList.size()% expertList.size() > 0 ? fileList.size()/ expertList.size() + 1 : fileList.size()/ expertList.size();

				for (ExpertUser user : expertList) {

					List<Files> userFiles = new ArrayList<Files>();
                    List<Files> outFiles = new ArrayList<Files>();//本轮淘汰的案卷
					boolean flag = true;
					while((userFiles.size()<user.getFilenum()-1 ||userFiles.size()==user.getFilenum()-1)&&flag){
						if(fileList.size()>0){
							Files file = fileList.remove(new Random().nextInt(fileList.size()));
							Files files1 = filesMapper.selectByPrimaryKey(file.getId());
							ExpertUser expertUser = expertUserMapper.selectByPrimaryKey(files1.getExpertaid());
                            if(!file.getAreacode().startsWith(user.getAreacode().substring(0, 2))){
								//if(!expertUser.getAreacode().startsWith(user.getAreacode().substring(0,2))){
									userFiles.add(file);
								}else {
									outFiles.add(file);
								}
						}else{//所有的案卷已经走完一遍，还没有够，也要退出循环
							flag =false;
						}
					}
					fileList.addAll(outFiles);

					for(Files expertFile:userFiles){
						ExpertHandlFileList handel = new ExpertHandlFileList();
						handel.setFileid(expertFile.getId());
						handel.setFilecode(expertFile.getFilecode());
						handel.setExpertid(user.getId());
						handel.setExpertname(user.getName());
						handel.setScoredstate("0");//0代表未评审
						handel.setHandltype(0);//0代表普通专家打分
						handleList.add(handel);


						//回写案卷信息表中：专家信息
						expertFile.setExpertbid(user.getId());
						expertFile.setExpertbname(user.getName());
						updateFileList.add(expertFile);
					}
				}
				//为了防止最后一个专家被同省规避  再走一遍循环逻辑
				if (fileList.size()>0){
					for (Files files1:fileList){
						for (ExpertUser user : expertList) {
							ExpertUser expertUser = expertUserMapper.selectByPrimaryKey(files1.getExpertaid());
							if(!files1.getAreacode().startsWith(user.getAreacode().substring(0, 2) )){
								//if(!expertUser.getAreacode().startsWith(user.getAreacode().substring(0,2))){
									ExpertHandlFileList handel = new ExpertHandlFileList();
									handel.setFileid(files1.getId());
									handel.setFilecode(files1.getFilecode());
									handel.setExpertid(user.getId());
									handel.setExpertname(user.getName());
									handel.setScoredstate("0");//0代表未评审
									handel.setHandltype(0);//0代表普通专家打分
									handleList.add(handel);
									//回写案卷信息表中：专家信息
									files1.setExpertbid(user.getId());
									files1.setExpertbname(user.getName());
									updateFileList.add(files1);
									break;
								//}
							}
						}
					}
				}
			}else{
				return "案卷或人员信息异常：案卷"+fileList.size()+"份,司法专家人员"+expertList.size()+"个";
			}

			Collections.shuffle(handleList);//打乱顺序
			// 批量新增专家评审
			expertHandlFileMapper.insertBatchList(handleList);
			//批量更新案卷信息（回写专家评审人员信息）
		    filesMapper.updateFilesOfExpertByBatchList(updateFileList);

			return "司法专家人员案卷分配完毕,案卷"+fileList.size()+"份,司法专家人员"+expertList.size()+"个,每个司法专家分配约"+fileNum+"(+_1)份案卷";
		}catch(Exception e){
			e.printStackTrace();
			return "后台出现未知异常，执行失败";
		}
	}

	/**
	 * 单独新增逻辑 无法复用
	 * @return
	 */
	@Override
	@Transactional
	public String initfpzj() {
		try {
			List<Files> files =  filesMapper.selectFpzjQi();
			// 按案卷类型获取案件列表
			ExpertUser expertAseach = new ExpertUser();
			ExpertUser expertBseach = new ExpertUser();

			expertAseach.setExpertclass("0");
			expertAseach.setLevel(0);
			expertBseach.setExpertclass("1");
			expertBseach.setLevel(0);
			// 当前类型的专家
			List<ExpertUser> expertListA=null;//地方专家,有区划之分，尽量避免本地区的
			List<ExpertUser> expertListB=null;//司法专家，无区划之分，平均分配即可

			expertListA = expertUserMapper.selectGeneralExpertUserByPara2019(expertAseach);
			expertListB = expertUserMapper.selectGeneralExpertUserByPara(expertBseach);


			List<ExpertHandlFileList> handleList = new ArrayList<ExpertHandlFileList>();

			int count = 0;

			for (Files files1:files){
				List<ExpertUser> expertUserListA = new ArrayList<>();
				List<ExpertUser> expertUserListB = new ArrayList<>();


				//分司法专家时 有一个人为0
				for (ExpertUser expertUser :expertListB){
					if (files1.getFiletype().equals(expertUser.getType())){
						expertUserListB.add(expertUser);
					}
				}
				if (CollectionUtils.isNotEmpty(expertUserListB)){
					for (ExpertUser expertUser:expertUserListB){
						 if (files1.getFiletype().equals("0")&&count<=26 &&expertUser.getFilenum()!= 0 ){
						 	continue;
						 }

						 if (files1.getFiletype().equals("0")&&expertUser.getFilenum()== 0 && count<=26){
							 if(!files1.getAreacode().startsWith(expertUser.getAreacode().substring(0, 2))){
								 ExpertHandlFileList handel = new ExpertHandlFileList();
								 handel.setFileid(files1.getId());
								 handel.setFilecode(files1.getFilecode());
								 handel.setExpertid(expertUser.getId());
								 handel.setExpertname(expertUser.getName());
								 handel.setScoredstate("0");//0代表未评审
								 handel.setHandltype(0);//0代表普通专家打分
								 handleList.add(handel);


								 //回写案卷信息表中：专家信息
								 files1.setExpertbid(expertUser.getId());
								 files1.setExpertbname(expertUser.getName());
								 //updateFileList.add(files1);
								 filesMapper.updateByPrimaryKey(files1);
								 count++;
								 break;
							 }
						 }else{
							List<Files> fileList =  filesMapper.selectByExpertName(expertUser.getName(),"1");


							 boolean flag = false;
							 if (files1.getFiletype().equals("0")&&fileList.size()<27){
								 flag = true;
							 }
							 if (files1.getFiletype().equals("1")){
								 flag = true;
							 }
							 if (files1.getFiletype().equals("2")&&fileList.size()<30){
								 flag = true;
							 }
							 if (files1.getFiletype().equals("3")&&fileList.size()<26){
								 flag = true;
							 }
							 if (files1.getFiletype().equals("6")&&fileList.size()<30){
								 flag = true;
							 }
							 if (files1.getFiletype().equals("7")){
								 flag = true;
							 }

							if(flag&&!files1.getAreacode().startsWith(expertUser.getAreacode().substring(0, 2))){
								ExpertHandlFileList handel = new ExpertHandlFileList();
								handel.setFileid(files1.getId());
								handel.setFilecode(files1.getFilecode());
								handel.setExpertid(expertUser.getId());
								handel.setExpertname(expertUser.getName());
								handel.setScoredstate("0");//0代表未评审
								handel.setHandltype(0);//0代表普通专家打分
								handleList.add(handel);


								//回写案卷信息表中：专家信息
								files1.setExpertbid(expertUser.getId());
								files1.setExpertbname(expertUser.getName());
								filesMapper.updateByPrimaryKey(files1);
								break;
							}
						 }

					}
				}

				//地方专家分配
				for (ExpertUser expertUser :expertListA){
					if (files1.getFiletype().equals(expertUser.getType())){
						expertUserListA.add(expertUser);
					}
				}
				if (CollectionUtils.isNotEmpty(expertUserListA)){
					for (ExpertUser expertUser:expertUserListA){
						List<Files> fileList =  filesMapper.selectByExpertName(expertUser.getName(),"0");

						boolean flag = false;
						if (files1.getFiletype().equals("0")&&fileList.size()<28){
							flag = true;
						}
						if (files1.getFiletype().equals("1")){
							flag = true;
						}
						if (files1.getFiletype().equals("2")&&fileList.size()<30){
							flag = true;
						}
						if (files1.getFiletype().equals("3")&&fileList.size()<26){
							flag = true;
						}
						if (files1.getFiletype().equals("6")&&fileList.size()<30){
							flag = true;
						}
						if (files1.getFiletype().equals("7")){
							flag = true;
						}

						if(flag&&!files1.getAreacode().startsWith(expertUser.getAreacode().substring(0, 2))){
							ExpertHandlFileList handel = new ExpertHandlFileList();
							handel.setFileid(files1.getId());
							handel.setFilecode(files1.getFilecode());
							handel.setExpertid(expertUser.getId());
							handel.setExpertname(expertUser.getName());
							handel.setScoredstate("0");//0代表未评审
							handel.setHandltype(0);//0代表普通专家打分
							handleList.add(handel);


							//回写案卷信息表中：专家信息
							files1.setExpertaid(expertUser.getId());
							files1.setExpertaname(expertUser.getName());
							filesMapper.updateByPrimaryKey(files1);
							break;
						}
					}
				}
			}
			// 批量新增专家评审
			expertHandlFileMapper.insertBatchList(handleList);
			return "操作成功";

		} catch (Exception e) {
			e.printStackTrace();
			return ""+e;
		}

	}

	//山东案卷分配附件接口
	@Override
	public String fpfj() {
		List<Files> files = filesMapper.selectAllFiles();
		for (Files files1:files){
			if(StringUtils.isEmpty(files1.getFileurl())){
				String filename = files1.getFilename();
				String url = "/Users/<USER>/Downloads/附件";
				//如果不存在就创建下载文件夹
				File fileD = new File(url);
				if (!fileD.exists()) {
					fileD.mkdirs();
				}
				//先删除后生成
				String filePath =  url+"/"+filename;
				System.out.println(filePath);

				String uploadFile = "";
				String suffix="";
				if (StringUtils.isNotEmpty(filename)){
					suffix=filename.substring(filename.lastIndexOf(".")+1);
				}
				try {
					FastDFSClient fd=new FastDFSClient("classpath:config/fdfs_client.conf");
					uploadFile = fd.uploadFile(filePath,suffix);
				} catch (Exception e) {
					e.printStackTrace();
				}

				files1.setFileurl(uploadFile);
				filesMapper.updateByPrimaryKey(files1);

			}
		}
		return "操作成功";
	}

	/**
	 * 下载附件接口
	 * @param response
	 * @return
	 */
	@Override
	public String xiazaiFJ(HttpServletResponse response ) {
		List<Files> filesList =  filesMapper.selectFilesByIsCommit(1);
		String u = "";
		for (Files files:filesList){
			String fileurl = "http://219.143.244.190:8089/" +files.getFileurl();
			try {
				DownloadFilesUrl.downLoadFromUrl(fileurl,files.getFilecode()+".pdf","/Users/<USER>/Desktop/fj");
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		return u;
	}
	public String caseThirdSync(){
		filesMapper.caseThirdSync();
		return null;
	}



	/*@Override
	public String initDistributeFileOfExpert2() throws Exception {
		// 按案卷类型获取案件列表
		ExpertUser expert = new ExpertUser();
		StringBuffer result = new StringBuffer("");
		for (int type = 0; type < 6; type++) {
			// 方案2 去临时参选案卷表进行分配
			List<CrossElectionFiles> electionFilesList = electionFilesMapper.getCrossElectionFilesByFileType(type + "");
			List<Files> fileList = new ArrayList<Files>();
			// 有则把id等
			if(electionFilesList.size()>0){
				for (int i = 0; i < electionFilesList.size(); i++) {
					Files files  = new Files();
					CrossElectionFiles electionFilesBean  =  electionFilesList.get(i);
					files.setId(electionFilesBean.getFilesId());
					files.setFiletype(electionFilesBean.getFileType());
					files.setAreacode(electionFilesBean.getAreaCode());
					files.setFilename(electionFilesBean.getFileName());
					files.setFilecode(electionFilesBean.getFileCode());
					files.setType(electionFilesBean.getType());
					fileList.add(files);
				}
			}
			expert.setType(type + "");
			// 当前类型的专家
			List<ExpertUser> expertList = expertUserMapper.selectExpertUserByPara(expert);
			if (fileList.size() > 0 && expertList.size() > 1) {
				//initFileListToExpertHandle(fileList, expertList);
				initFileListToExpertHandle(fileList, expertList,"0",0,expertList.size()-1);
				result.append("success提示：案卷类型--【" + type + "】的案卷数量为" + fileList.size() +"，专家人数为："+expertList.size()+ ",分配成功*****<br/>");
				//System.out.println("提示：案卷类型--" + type + "的案卷数量为" + fileList.size() + ",分配成功*****<br/>");
			} else if (fileList.size() == 0) {
				result.append("error提示：案卷类型--" + type + "的案卷数量为零,没有分配案卷*****<br/>");
			} else {
				result.append("error提示：专家类型--" + type + "的专家数量不足两位,没有分配案卷*****<br/>");
			}
		}

		return result.toString();
	}*/
	/**
	 *
	 * @param fileList 案件集合
	 * @param expertList 专家集合
	 * @param status 是否为初始化操作 0初始化操作， 1不为初始化操作
	 * @param startIndex 开始指针
	 * @param endIndex 结束指针
	 */
	private void initFileListToExpertHandle(List<Files> fileList, List<ExpertUser> expertList,String status,Integer startIndex,Integer endIndex ) {
			List<ExpertHandlFileList> handleList = new ArrayList<ExpertHandlFileList>();
			List<Files> updateFileList = new ArrayList<Files>();
			//int startIndex = 0;
			int temp1 = 1;
			int temp2 = -1;
			//int endIndex = expertList.size() - 1;
			for (Files file : fileList) {
				ExpertHandlFileList handel1 = new ExpertHandlFileList();
				ExpertHandlFileList handel2 = new ExpertHandlFileList();

				handel1.setFileid(file.getId());
				handel1.setFilecode(file.getFilecode());
				handel1.setExpertid(expertList.get(startIndex).getId());
				handel1.setExpertname(expertList.get(startIndex).getName());
				handel1.setScoredstate("0");
				handleList.add(handel1);

			    handel2.setFileid(file.getId());
				handel2.setFilecode(file.getFilecode());
				handel2.setExpertid(expertList.get(endIndex).getId());
				handel2.setExpertname(expertList.get(endIndex).getName());
				handel2.setScoredstate("0");
				handleList.add(handel2);

				//回写案卷信息表中：2位专家信息
				file.setExpertaid(expertList.get(startIndex).getId());
				file.setExpertaname(expertList.get(startIndex).getName());
				file.setExpertbid(expertList.get(endIndex).getId());
				file.setExpertbname(expertList.get(endIndex).getName());
				file.setIsconsider("0");
				updateFileList.add(file);

				// 专家list：开始，结尾索引更新
				if (temp1 == 1) {
					if (startIndex + temp1 > expertList.size() - 1){
						temp1 = -1;
					}else{
						startIndex = startIndex + temp1;
					}
				} else {
					if (startIndex + temp1 < 0){
						temp1 = 1;
					}else{
					   startIndex = startIndex + temp1;
					}
				}


				if (temp2 == -1) {
					if (endIndex + temp2 < 0){
						temp2 = 1;
					}else{
						endIndex = endIndex + temp2;
					}

				} else {
					if (endIndex + temp2 > expertList.size() - 1){
						temp2 = -1;
					}else{
						endIndex = endIndex + temp2;
					}
				}


				while (startIndex == endIndex) {
					if (temp2 == -1) {
						if (endIndex + temp2 < 0)
							temp2 = 1;
					} else {
						if (endIndex + temp2 > expertList.size() - 1)
							temp2 = -1;
					}
					endIndex = endIndex + temp2;
				}

			}
			// 批量新增
			expertHandlFileMapper.insertBatchList(handleList);
		    filesMapper.updateFilesOfExpertByBatchList(fileList);
		    if("1".equals(status)){
		    	//不是初始化状态，记录开始指针，和结束指针
		    	tCDictionaryMapper.insertIndex(endIndex.toString(),"endIndex");
		    	tCDictionaryMapper.insertIndex(startIndex.toString(),"startIndex");
		    }
	}

	/**
	 * 2017版专家分配案卷方法
	 * @param fileList 案件集合
	 * @param expertList 专家集合
	 */
	/*private void initFileListToExpertHandle2017(List<Files> fileList, List<ExpertUser> expertList){
			List<ExpertHandlFileList> handleList = new ArrayList<ExpertHandlFileList>();
			List<Files> updateFileList = new ArrayList<Files>();

			CombineUtil t = new CombineUtil();
			String [] data = new String [expertList.size()];
	        for(int i = 0; i < expertList.size(); i++){
	        	data[i]= i+"";
	        }

	        List<String> res = t.combine(data, 2);//方案1：n个专家任意抽2个拼一组的集合。适用案卷能均分的时候，则用该方案随机抽
	        List<String> res1 = t.spellStr(data, 2);//方案2：n个专家临近2个拼一组的集合。适用案卷不能均分的时候，则采用轮流制
	        int mod = fileList.size()%res.size();//案卷是否能均分

			for (int j=0;j<fileList.size();j++) {
				Files file = fileList.get(j);

				String indexStr = "";
				if(j<(fileList.size()-mod)){//能平均分的时候，则采用方案1随机抽
					if(res.size()>0){//list的size()是在逐次递减，需要每次都判断
						indexStr = res.remove(new Random().nextInt(res.size()));
					}else{
						res = t.combine(data, 2);
						indexStr = res.remove(new Random().nextInt(res.size()));
					}
				}else{//不能均分则案卷循环的最后一波采用方案2（一波指“专家两两组合”的集合）
					if(res1.size()>0){//list的size()是在逐次递减，需要每次都判断
						indexStr = res1.remove(0);
					}else{
						res1 =  t.spellStr(data, 2);
						indexStr = res1.remove(0);
					}
				}
				//System.out.println("indexStr>>"+indexStr);
				String[] intArr = indexStr.split(",");


				ExpertHandlFileList handel1 = new ExpertHandlFileList();
				ExpertHandlFileList handel2 = new ExpertHandlFileList();

				handel1.setFileid(file.getId());
				handel1.setFilecode(file.getFilecode());
				handel1.setExpertid(expertList.get(Integer.parseInt(intArr[0])).getId());
				handel1.setExpertname(expertList.get(Integer.parseInt(intArr[0])).getName());
				handel1.setScoredstate("0");
				handleList.add(handel1);

			    handel2.setFileid(file.getId());
				handel2.setFilecode(file.getFilecode());
				handel2.setExpertid(expertList.get(Integer.parseInt(intArr[1])).getId());
				handel2.setExpertname(expertList.get(Integer.parseInt(intArr[1])).getName());
				handel2.setScoredstate("0");
				handleList.add(handel2);

				//回写案卷信息表中：2位专家信息
				file.setExpertaid(expertList.get(Integer.parseInt(intArr[0])).getId());
				file.setExpertaname(expertList.get(Integer.parseInt(intArr[0])).getName());
				file.setExpertbid(expertList.get(Integer.parseInt(intArr[1])).getId());
				file.setExpertbname(expertList.get(Integer.parseInt(intArr[1])).getName());
				file.setIsconsider("0");
				updateFileList.add(file);

			}

			// 批量新增
			expertHandlFileMapper.insertBatchList(handleList);
		    filesMapper.updateFilesOfExpertByBatchList(fileList);
		    if("1".equals(status)){
		    	//不是初始化状态，记录开始指针，和结束指针
		    	tCDictionaryMapper.insertIndex(endIndex.toString(),"endIndex");
		    	tCDictionaryMapper.insertIndex(startIndex.toString(),"startIndex");
		    }
	}*/


	private void initFileListToCommitHandle(List<Files> fileList, List<ExpertUser> expertList,String status,Integer startIndex,Integer endIndex ) {
		List<ExpertHandlFileList> handleList = new ArrayList<ExpertHandlFileList>();
		List<Files> updateFileList = new ArrayList<Files>();
		//int startIndex = 0;
		int temp1 = 1;
		int temp2 = -1;
		//int endIndex = expertList.size() - 1;
		for (Files file : fileList) {
			ExpertHandlFileList handel1 = new ExpertHandlFileList();
			ExpertHandlFileList handel2 = new ExpertHandlFileList();

			handel1.setFileid(file.getId());
			handel1.setFilecode(file.getFilecode());
			handel1.setExpertid(expertList.get(startIndex).getId());
			handel1.setExpertname(expertList.get(startIndex).getName());
			handel1.setScoredstate("0");
			handleList.add(handel1);

		    handel2.setFileid(file.getId());
			handel2.setFilecode(file.getFilecode());
			handel2.setExpertid(expertList.get(endIndex).getId());
			handel2.setExpertname(expertList.get(endIndex).getName());
			handel2.setScoredstate("0");
			handleList.add(handel2);

			//回写案卷信息表中：2位委员信息
			file.setExpertcommitaid(expertList.get(startIndex).getId());
			file.setExpertcommitaname(expertList.get(startIndex).getName());

			file.setExpertcommitbid(expertList.get(endIndex).getId());
			file.setExpertcommitbname(expertList.get(endIndex).getName());
		/*	file.setExpertaid(expertList.get(startIndex).getId());
			file.setExpertaname(expertList.get(startIndex).getName());
			file.setExpertbid(expertList.get(endIndex).getId());
			file.setExpertbname(expertList.get(endIndex).getName());*/
			file.setIsconsider("0");
			file.setIsconsiderexpcommit("1");

			updateFileList.add(file);

			// 专家list：开始，结尾索引更新
			if (temp1 == 1) {
				if (startIndex + temp1 > expertList.size() - 1){
					temp1 = -1;
				}else{
					startIndex = startIndex + temp1;
				}
			} else {
				if (startIndex + temp1 < 0){
					temp1 = 1;
				}else{
				   startIndex = startIndex + temp1;
				}
			}


			if (temp2 == -1) {
				if (endIndex + temp2 < 0){
					temp2 = 1;
				}else{
					endIndex = endIndex + temp2;
				}

			} else {
				if (endIndex + temp2 > expertList.size() - 1){
					temp2 = -1;
				}else{
					endIndex = endIndex + temp2;
				}
			}


			while (startIndex == endIndex) {
				if (temp2 == -1) {
					if (endIndex + temp2 < 0)
						temp2 = 1;
				} else {
					if (endIndex + temp2 > expertList.size() - 1)
						temp2 = -1;
				}
				endIndex = endIndex + temp2;
			}

		}
		// 批量新增
		expertHandlFileMapper.insertBatchList(handleList);
	    //filesMapper.updateFilesOfExpertByBatchList(fileList);
	    filesMapper.updateFilesOfCommitByBatchList(fileList);
	    if("1".equals(status)){
	    	//不是初始化状态，记录开始指针，和结束指针
	    	tCDictionaryMapper.insertIndex(endIndex.toString(),"endIndex");
	    	tCDictionaryMapper.insertIndex(startIndex.toString(),"startIndex");
	    }
}

	/**
	 * 2017版【专家委员】分配案卷方法
	 * @param fileList 案件集合
	 * @param expertList 专家集合
	 */
	private void initFileListToCommitHandle2017(List<Files> fileList, List<ExpertUser> expertList){
			List<ExpertHandlFileList> handleList = new ArrayList<ExpertHandlFileList>();
			List<Files> updateFileList = new ArrayList<Files>();

			CombineUtil t = new CombineUtil();
			String [] data = new String [expertList.size()];
	        for(int i = 0; i < expertList.size(); i++){
	        	data[i]= i+"";
	        }

	        List<String> res = t.combine(data, 2);//方案1：n个专家任意抽2个拼一组的集合。适用案卷能均分的时候，则用该方案随机抽
	        List<String> res1 = t.spellStr(data, 2);//方案2：n个专家临近2个拼一组的集合。适用案卷不能均分的时候，则采用轮流制
	        int mod = fileList.size()%res.size();//案卷是否能均分

			for (int j=0;j<fileList.size();j++) {
				Files file = fileList.get(j);

				String indexStr = "";
				if(j<(fileList.size()-mod)){//能平均分的时候，则采用方案1随机抽
					if(res.size()>0){//list的size()是在逐次递减，需要每次都判断
						indexStr = res.remove(new Random().nextInt(res.size()));
					}else{
						res = t.combine(data, 2);
						indexStr = res.remove(new Random().nextInt(res.size()));
					}
				}else{//不能均分则案卷循环的最后一波采用方案2（一波指“专家两两组合”的集合）
					if(res1.size()>0){//list的size()是在逐次递减，需要每次都判断
						indexStr = res1.remove(0);
					}else{
						res1 =  t.spellStr(data, 2);
						indexStr = res1.remove(0);
					}
				}
				//System.out.println("indexStr>>"+indexStr);
				String[] intArr = indexStr.split(",");


				ExpertHandlFileList handel1 = new ExpertHandlFileList();
				ExpertHandlFileList handel2 = new ExpertHandlFileList();

				handel1.setFileid(file.getId());
				handel1.setFilecode(file.getFilecode());
				handel1.setExpertid(expertList.get(Integer.parseInt(intArr[0])).getId());
				handel1.setExpertname(expertList.get(Integer.parseInt(intArr[0])).getName());
				handel1.setScoredstate("0");
				handleList.add(handel1);

			    handel2.setFileid(file.getId());
				handel2.setFilecode(file.getFilecode());
				handel2.setExpertid(expertList.get(Integer.parseInt(intArr[1])).getId());
				handel2.setExpertname(expertList.get(Integer.parseInt(intArr[1])).getName());
				handel2.setScoredstate("0");
				handleList.add(handel2);

				//回写案卷信息表中：2位专家委员的信息
				file.setExpertcommitaid(expertList.get(Integer.parseInt(intArr[0])).getId());
				file.setExpertcommitaname(expertList.get(Integer.parseInt(intArr[0])).getName());

				file.setExpertcommitbid(expertList.get(Integer.parseInt(intArr[1])).getId());
				file.setExpertcommitbname(expertList.get(Integer.parseInt(intArr[1])).getName());
				file.setIsconsider("0");
				file.setIsconsiderexpcommit("1");
				updateFileList.add(file);

			}

			// 批量新增
			expertHandlFileMapper.insertBatchListCommit(handleList);
		    filesMapper.updateFilesOfCommitByBatchList(updateFileList);
		    /*if("1".equals(status)){
		    	//不是初始化状态，记录开始指针，和结束指针
		    	tCDictionaryMapper.insertIndex(endIndex.toString(),"endIndex");
		    	tCDictionaryMapper.insertIndex(startIndex.toString(),"startIndex");
		    }*/
	}




	@Override
	public int updateSysInitConfigByCodeAndActionType(Integer code, String actionType, String result) throws Exception {
		return sysInitMapper.updateSysInitConfigByCodeAndActionType(code, actionType, result);
	}

	/**
	 * 系统分配交叉评审案卷
	 * 2019年进行修改
	 */
	@Override
	public String initDistributeFileOfCross() throws Exception {
		//Map<String,List<String>> ret = getNoOrderAreaList();
		//List<String> areaPara1 = ret.get("area1");
		//List<String> areaPara2 = ret.get("area2");
		/*List<String> areaPara1 = Const.crossAList;
		List<String> areaPara2 = Const.crossBList;
		//根据区划信息案卷信息
		List<Files> fileList1  = filesMapper.selectListByAreaListPara(areaPara1);
		List<Files> fileList2  = filesMapper.selectListByAreaListPara(areaPara2);
		//根据区划信息获取交叉评审人员
		List<CrossUser> crossList1 = crossUserMapper.selectCrossUserByAreaListPara(areaPara1);
		List<CrossUser> crossList2 = crossUserMapper.selectCrossUserByAreaListPara(areaPara2);

		//分配案卷
		StringBuffer result = new StringBuffer("");

		if (fileList1.size() > 0 && crossList2.size() > 1) {
			initFileListToCrossHandle2018(fileList1, crossList2);
			result.append("success提示：第一部分区划【" + UtilOperate.listToString(areaPara1) + "】下的案卷数量为:" + fileList1.size() +"，第二部分区划【" +UtilOperate.listToString(areaPara2) +"】下交叉评审人员数量为:"+crossList2.size()+",分配成功*****<br/>");
			System.out.println("success提示：第一部分区划【" + UtilOperate.listToString(areaPara1) + "】下的案卷数量为:" + fileList1.size() +"，第二部分区划【" +UtilOperate.listToString(areaPara2) +"】下交叉评审人员数量为:"+crossList2.size()+",分配成功*****<br/>");
		} else if (fileList1.size() == 0) {
			result.append("error提示：第一部分区划【" + UtilOperate.listToString(areaPara1) + "】下的案卷数量为零,没有分配案卷*****<br/>");
		} else {
			result.append("error提示：第二部分区划【" + UtilOperate.listToString(areaPara2) + "】下的交叉评审人员数量不足两位,没有分配案卷*****<br/>");
		}
		if (fileList2.size() > 0 && crossList1.size() > 1) {
			initFileListToCrossHandle2018(fileList2, crossList1);
			result.append("success提示：第二部分区划【" + UtilOperate.listToString(areaPara2) + "】下的案卷数量为" + fileList2.size() +"，第一部分区划【" +UtilOperate.listToString(areaPara1) +"】下交叉评审人员数量为:"+crossList1.size()+ ",分配成功*****<br/>");
			System.out.println("success提示：第二部分区划【" + UtilOperate.listToString(areaPara2) + "】下的案卷数量为" + fileList2.size() +"，第一部分区划【" +UtilOperate.listToString(areaPara1) +"】下交叉评审人员数量为:"+crossList1.size()+ ",分配成功*****<br/>");
		} else if (fileList2.size() == 0) {
			result.append("error提示：第二部分区划【" + UtilOperate.listToString(areaPara2) + "】下的案卷数量为零,没有分配案卷*****<br/>");
		} else {
			result.append("error提示：第一部分区划【" + UtilOperate.listToString(areaPara1) + "】下的交叉评审人员数量不足两位,没有分配案卷*****<br/>");
		}

		return result.toString();*/

		try {
			List<Files> fileList = filesMapper.selectListByAreaListPara(new ArrayList<String>());
			List<CrossUser> crossList = crossUserMapper.selectCrossUserByAreaListPara(new ArrayList<String>());

			List<CrossHandlFileList> handleList = new ArrayList<CrossHandlFileList>();
			List<Files> updateFileList = new ArrayList<Files>();
			int fileNum = 0;//每个人大概的平均案卷数量
			if (fileList.size() > 0 && crossList.size() > 0) {
				fileNum = (fileList.size() * 2) % crossList.size() > 0 ? (fileList.size() * 2) / crossList.size() + 1 : (fileList.size() * 2) / crossList.size();

				for (Files file : fileList) {
					CrossHandlFileList handel1 = new CrossHandlFileList();
					CrossHandlFileList handel2 = new CrossHandlFileList();

					//存放该案卷分配的人员
					List<CrossUser> f_users = new ArrayList<CrossUser>();

					List<CrossUser> copy = new ArrayList<CrossUser>();
					copy.addAll(crossList);
					while (f_users.size() < 2) {//每1个案卷至到分配到两个合适的用户为止

						CrossUser cross = copy.remove(new Random().nextInt(copy.size()));
						if (!file.getAreacode().equals(cross.getAreacode()) && cross.getNum() < fileNum-1) {//优先走分配数量小于正常数量2个的用户
							f_users.add(cross);
						}

						if(copy.size()==0 &&f_users.size()<2){//前一轮用户全部被分配完毕，再重新复制一份所有用户
							copy.addAll(crossList);
							CrossUser cross1 = copy.remove(new Random().nextInt(copy.size()));

							if (!file.getAreacode().equals(cross1.getAreacode()) && cross1.getNum() < fileNum) {//然后再分配小于平均数量的用户
								f_users.add(cross1);
							}

						}
					}

					if(f_users.get(0).getId()==f_users.get(1).getId()){//如果存在1个案卷分给1个人分了两次的情况，做特殊处理
						f_users.remove(1);
						List<CrossUser> copy2 = new ArrayList<CrossUser>();
						copy2.addAll(crossList);
						while(f_users.size()<2){
							CrossUser u = copy2.remove(new Random().nextInt(copy2.size()));
							if(!file.getAreacode().equals(u.getAreacode())){
								if(u.getNum()<fileNum&&f_users.get(0).getId()!=u.getId()){
									f_users.add(u);
								}
							}
						}
					}

					for (CrossUser user : f_users) {
						for (CrossUser croUser : crossList) {
							if (croUser.getId() == user.getId()) {
								croUser.setNum(croUser.getNum() + 1);//被分配的用户，案件数量会加1
							}
						}
					}

					handel1.setFileid(file.getId());
					handel1.setFilecode(file.getFilecode());
					handel1.setCrossuserid(f_users.get(0).getId());
					handel1.setScoredstate("0");
					handleList.add(handel1);

					handel2.setFileid(file.getId());
					handel2.setFilecode(file.getFilecode());
					handel2.setCrossuserid(f_users.get(1).getId());
					handel2.setScoredstate("0");
					handleList.add(handel2);


					//回写案卷信息表中：2位交叉评审人员信息
					file.setCrossreviewaid(f_users.get(0).getId());
					file.setCrossreviewnamea(f_users.get(0).getName());
					file.setCrossreviewbid(f_users.get(1).getId());
					file.setCrossreviewnameb(f_users.get(1).getName());
					file.setIsconsider("0");
					updateFileList.add(file);
				}
			}else{
				return "案卷或人员信息异常：案卷"+fileList.size()+"份,交叉人员"+crossList.size()+"个";
			}
			// 批量新增交叉评审
			crossHandlFileListMapper.insertBatchList(handleList);
			//批量更新案卷信息（回写专家评审、交叉评审人员信息）
			filesMapper.updateFilesOfCrossByBatchList(updateFileList);

			return "交叉人员案卷分配完毕,案卷"+fileList.size()+"*2份,交叉人员"+crossList.size()+"个,每个交叉专家分配约"+fileNum+"(+_1)份案卷";
		}catch(Exception e){
			return "后台出现未知异常，执行失败";
		}
	}

	/*2017年用
	private void initFileListToCrossHandle(List<Files> fileList, List<CrossUser> crossList) {


			List<CrossHandlFileList> handleList = new ArrayList<CrossHandlFileList>();
			List<Files> updateFileList = new ArrayList<Files>();
			int startIndex = 0;
			int temp1 = 1;
			int temp2 = -1;
			int endIndex = crossList.size() - 1;
			for (Files file : fileList) {
				CrossHandlFileList handel1 = new CrossHandlFileList();
				CrossHandlFileList handel2 = new CrossHandlFileList();

				handel1.setFileid(file.getId());
				handel1.setFilecode(file.getFilecode());
				handel1.setCrossuserid(crossList.get(startIndex).getId());
				handel1.setScoredstate("0");
				handleList.add(handel1);

				handel2.setFileid(file.getId());
				handel2.setFilecode(file.getFilecode());
				handel2.setCrossuserid(crossList.get(endIndex).getId());
				handel2.setScoredstate("0");
				handleList.add(handel2);

				//回写案卷信息表中：2位交叉评审人员信息
				file.setCrossreviewaid(crossList.get(startIndex).getId());
				file.setCrossreviewnamea(crossList.get(startIndex).getName());
				file.setCrossreviewbid(crossList.get(endIndex).getId());
				file.setCrossreviewnameb(crossList.get(endIndex).getName());
				file.setIsconsider("0");
                updateFileList.add(file);
				System.out.println("*********start【"+startIndex+"】"+crossList.get(startIndex).getId());
				System.out.println("&&&&&&&&&&end【"+endIndex+"】"+crossList.get(endIndex).getId());
				// 专家list：开始，结尾索引更新
				if (temp1 == 1) {
					if (startIndex + temp1 > crossList.size() - 1){
						temp1 = -1;
					}else{
						startIndex = startIndex + temp1;
					}
				} else {
					if (startIndex + temp1 < 0){
						temp1 = 1;
					}else{
					   startIndex = startIndex + temp1;
					}
				}


				if (temp2 == -1) {
					if (endIndex + temp2 < 0){
						temp2 = 1;
					}else{
						endIndex = endIndex + temp2;
					}

				} else {
					if (endIndex + temp2 > crossList.size() - 1){
						temp2 = -1;
					}else{
						endIndex = endIndex + temp2;
					}
				}



				while (startIndex == endIndex) {
					if (temp2 == -1) {
						if (endIndex + temp2 < 0)
							temp2 = 1;
					} else {
						if (endIndex + temp2 > crossList.size() - 1)
							temp2 = -1;
					}
					endIndex = endIndex + temp2;
				}

			}
			// 批量新增交叉评审
			crossHandlFileListMapper.insertBatchList(handleList);
			//批量更新案卷信息（回写专家评审、交叉评审人员信息）
			filesMapper.updateFilesOfCrossByBatchList(updateFileList);

	}*/


/*	private void initFileListToCrossHandle2018(List<Files> fileList, List<CrossUser> crossList) {

		List<CrossUser> crossList1 = new ArrayList<CrossUser>(crossList);
		List<CrossHandlFileList> handleList = new ArrayList<CrossHandlFileList>();
		List<Files> updateFileList = new ArrayList<Files>();

		for (Files file : fileList) {
			CrossHandlFileList handel1 = new CrossHandlFileList();
			CrossHandlFileList handel2 = new CrossHandlFileList();

			CrossUser cro1= new CrossUser();//人员1
			CrossUser cro2= new CrossUser();//人员2

			if(crossList1.size()>1){
				cro1 = crossList1.remove(new Random().nextInt(crossList1.size()));
				cro2 = crossList1.remove(new Random().nextInt(crossList1.size()));
				System.out.println("111111");
			}else if(crossList1.size()==1){//人员集合中只剩下1个人
				List<CrossUser> removeList = new ArrayList<CrossUser>();

				int r1 = new Random().nextInt(crossList1.size());
				cro1 = crossList1.remove(r1);//人员中只剩一人，张三
				removeList.add(cro1);
				crossList1.addAll(crossList);
				crossList1.removeAll(removeList);//新一轮再分，就不能分给张三，因为该案卷一分他1次（cro1）,所以要删除
				cro2 = crossList1.remove(new Random().nextInt(crossList1.size()));

				crossList1.addAll(removeList);//把删除的用户张三再补充上，不然该人员会少一次分案卷的机会，循环累积下去可能会少的更多
				System.out.println("222222");
			}else{
				crossList1.addAll(crossList);
				cro1 = crossList1.remove(new Random().nextInt(crossList1.size()));
				cro2 = crossList1.remove(new Random().nextInt(crossList1.size()));
				System.out.println("333333");
			}


			handel1.setFileid(file.getId());
			handel1.setFilecode(file.getFilecode());
			handel1.setCrossuserid(cro1.getId());
			handel1.setScoredstate("0");
			handleList.add(handel1);

			handel2.setFileid(file.getId());
			handel2.setFilecode(file.getFilecode());
			handel2.setCrossuserid(cro2.getId());
			handel2.setScoredstate("0");
			handleList.add(handel2);

			//回写案卷信息表中：2位交叉评审人员信息
			file.setCrossreviewaid(cro1.getId());
			file.setCrossreviewnamea(cro1.getName());
			file.setCrossreviewbid(cro2.getId());
			file.setCrossreviewnameb(cro2.getName());
			file.setIsconsider("0");
            updateFileList.add(file);

		}
		// 批量新增交叉评审
		crossHandlFileListMapper.insertBatchList(handleList);
		//批量更新案卷信息（回写专家评审、交叉评审人员信息）
		filesMapper.updateFilesOfCrossByBatchList(updateFileList);

	}
	*/

/*	private Map<String,List<String>> getNoOrderAreaList(){
		List<String> provinceAreaList = new ArrayList<String>() {
			{
				add("11");
				add("12");
				add("13");
				add("14");
				add("15");
				add("16");
				add("21");
				add("22");
				add("23");
				add("31");
				add("32");
				add("33");
				add("34");
				add("35");
				add("36");
				add("37");
				add("42");
				add("43");
				add("44");
				add("45");
				add("46");
				add("51");
				add("52");
				add("53");
				add("54");
				add("55");
				add("61");
				add("62");
				add("63");
				add("64");
				add("65");
				add("66");
			}
		};

		//打乱list排序:随机排序
		Collections.shuffle(provinceAreaList);

		List<String> areaPara1 = new ArrayList<String>();
		List<String> areaPara2 = new ArrayList<String>();
		for(int i=0;i<provinceAreaList.size();i++){
				if(i%2==0){
					areaPara1.add(provinceAreaList.get(i));
				}else{
					areaPara2.add(provinceAreaList.get(i));
				}
			}
		Map<String,List<String>> ret = new HashMap<String,List<String>>();
		ret.put("area1", areaPara1);
		ret.put("area2", areaPara2);
		return ret;
	}
*/

	/**
	 * 个人事迹材料分配
	 *
	 * 2016-12-19:改变分配方案，把所有的个人事迹案卷统一分配给不同类型的交叉，统一打分。
	 * @return
	 * @throws Exception
	 */
	@Override
	public String initDistributePersonalFileOfCross() throws Exception {


		/*//根据区划获取个人事迹材料 - lhl
		List<ElectionPersonal> personalFileList1 = electionPersonalMapper.selectListByAreaListParaAll();
		//根据区划信息获取交叉评审人员 - lhl
		List<CrossUser> crossList2 = crossUserMapper.selectCrossUserByAreaListParaByType();*/

		//分配案卷
		StringBuffer result = new StringBuffer("");

		/*if (personalFileList1.size() > 0 && crossList2.size() > 1) {
			initPersonalFileListToCrossHandle2(personalFileList1, crossList2);

		} else if (personalFileList1.size() == 0) {
		} else {
		}*/
		return result.toString();
	}

	/*private void initPersonalFileListToCrossHandle(List<ElectionPersonal> fileList, List<CrossUser> crossList) {


		List<CrossPersonalFileList> handleList = new ArrayList<CrossPersonalFileList>();
		List<ElectionPersonal> updatePersonalFileList = new ArrayList<ElectionPersonal>();
		int startIndex = 0;
		int temp1 = 1;
		int temp2 = -1;
		int endIndex = crossList.size() - 1;
		for (ElectionPersonal person : fileList) {
			CrossPersonalFileList handel1 = new CrossPersonalFileList();
			CrossPersonalFileList handel2 = new CrossPersonalFileList();

			handel1.setPersonalid(person.getId());
			handel1.setPersonalmaterialname(person.getPersonalmaterialname());
			handel1.setCrossuserid(crossList.get(startIndex).getId());
			handel1.setScoredstate("0");
			handleList.add(handel1);

			handel2.setPersonalid(person.getId());
			handel2.setPersonalmaterialname(person.getPersonalmaterialname());
			handel2.setCrossuserid(crossList.get(endIndex).getId());
			handel2.setScoredstate("0");
			handleList.add(handel2);

			//回写个人信息表中：2位交叉评审人员信息
			person.setCrossreviewaid(crossList.get(startIndex).getId());
			person.setPercrossreviewnamea(crossList.get(startIndex).getName());
			person.setCrossreviewbid(crossList.get(endIndex).getId());
			person.setPercrossreviewnameb(crossList.get(endIndex).getName());
			person.setIsconsider("0");
			updatePersonalFileList.add(person);

			// 专家list：开始，结尾索引更新
			if (temp1 == 1) {
				if (startIndex + temp1 > crossList.size() - 1){
					temp1 = -1;
				}else{
					startIndex = startIndex + temp1;
				}
			} else {
				if (startIndex + temp1 < 0){
					temp1 = 1;
				}else{
				   startIndex = startIndex + temp1;
				}
			}


			if (temp2 == -1) {
				if (endIndex + temp2 < 0){
					temp2 = 1;
				}else{
					endIndex = endIndex + temp2;
				}

			} else {
				if (endIndex + temp2 > crossList.size() - 1){
					temp2 = -1;
				}else{
					endIndex = endIndex + temp2;
				}
			}


			while (startIndex == endIndex) {
				if (temp2 == -1) {
					if (endIndex + temp2 < 0)
						temp2 = 1;
				} else {
					if (endIndex + temp2 > crossList.size() - 1)
						temp2 = -1;
				}
				endIndex = endIndex + temp2;
			}

		}
		// 批量新增交叉评审
		crossPersonalFileListMapper.insertBatchList(handleList);
		//批量更新个人事迹评审人员信息（交叉评审人员信息）
		electionPersonalMapper.updatePersonalFilesOfCrossByBatchList(updatePersonalFileList);

}*/

/*	private void initPersonalFileListToCrossHandle2(List<ElectionPersonal> fileList, List<CrossUser> crossList) {


		List<CrossPersonalFileList> handleList = new ArrayList<CrossPersonalFileList>();
		List<ElectionPersonal> updatePersonalFileList = new ArrayList<ElectionPersonal>();
		int startIndex = 0;
		int temp1 = 1;
		int temp2 = -1;
		int endIndex = crossList.size() - 1;
		for (ElectionPersonal person : fileList) {
			CrossPersonalFileList handel1 = new CrossPersonalFileList();
//			CrossPersonalFileList handel2 = new CrossPersonalFileList();

			handel1.setPersonalid(person.getId());
			handel1.setPersonalmaterialname(person.getPersonalmaterialname());
			handel1.setCrossuserid(crossList.get(startIndex).getId());
			handel1.setScoredstate("0");
			handleList.add(handel1);

//			handel2.setPersonalid(person.getId());
//			handel2.setPersonalmaterialname(person.getPersonalmaterialname());
//			handel2.setCrossuserid(crossList.get(endIndex).getId());
//			handel2.setScoredstate("0");
//			handleList.add(handel2);

			//回写个人信息表中：2位交叉评审人员信息
			person.setCrossreviewaid(crossList.get(startIndex).getId());
			person.setPercrossreviewnamea(crossList.get(startIndex).getName());
			person.setCrossreviewbid(crossList.get(endIndex).getId());
			person.setPercrossreviewnameb(crossList.get(endIndex).getName());
			person.setIsconsider("0");
			updatePersonalFileList.add(person);

			// 案卷集合大于交叉人员集合，
			if(startIndex + temp1 >= crossList.size()){
				startIndex = 0;
			}else{
				 startIndex = startIndex + temp1;
			}




			// 专家list：开始，结尾索引更新
			if (temp1 == 1) {
				if (startIndex + temp1 > crossList.size() - 1){
					temp1 = -1;
				}else{
					startIndex = startIndex + temp1;
				}
			} else {
				if (startIndex + temp1 < 0){
					temp1 = 1;
				}else{
				   startIndex = startIndex + temp1;
				}
			}


			if (temp2 == -1) {
				if (endIndex + temp2 < 0){
					temp2 = 1;
				}else{
					endIndex = endIndex + temp2;
				}

			} else {
				if (endIndex + temp2 > crossList.size() - 1){
					temp2 = -1;
				}else{
					endIndex = endIndex + temp2;
				}
			}


			while (startIndex == endIndex) {
				if (temp2 == -1) {
					if (endIndex + temp2 < 0)
						temp2 = 1;
				} else {
					if (endIndex + temp2 > crossList.size() - 1)
						temp2 = -1;
				}
				endIndex = endIndex + temp2;
			}

		}
		// 批量新增交叉评审
		crossPersonalFileListMapper.insertBatchList(handleList);
		//批量更新个人事迹评审人员信息（交叉评审人员信息）
		electionPersonalMapper.updatePersonalFilesOfCrossByBatchList(updatePersonalFileList);

	}*/

	//查询专家用户列表
	@Override
	public List<ExpertUser> getExpertUserList() {
		return expertUserMapper.selectExpertUserByPara(null) ;
	}
	//查询交叉评审用户列表
	@Override
	public List<CrossUser> getCrossUserList() {
		return crossUserMapper.selectCrosstUserByPara(null);
	}

	@Override
	public PageBean<SysLog> getLogList(int pNum) {
		if(pNum==0){
			pNum=1;
	       }
		PageHelper.startPage(pNum,Const.NOPERPAGE);
		return new PageBean<SysLog>(sysLogMapper.getLogList());

	}

	public PageBean<SysLog> getLogSeach(int pNum, String dateStart,
			String dateEnd,String ip) {
		if(pNum==0){
			pNum=1;
	       }
		PageHelper.startPage(pNum,Const.NOPERPAGE);
		return new PageBean<SysLog>(sysLogMapper.getLogSeach(dateStart,dateEnd, ip));
	}
	//系统状态的设置
	@Override
	public void setStsStatus(String sysStatus) {
			sysInitMapper.setNeSysStatus(sysStatus);
			sysInitMapper.setEqSysStatus(sysStatus);
	}

	//获取系统的状态
	@Override
	public List<SysInitConfig> getsysStatus() {
		return sysInitMapper.selectListByActionType("sysState");
	}

	//获取tarea
	@Override
	public List<Tarea> getTarea(HashMap<String, String> map) {

		return areaMapper.selectCityByParentCode(map);
	}



	@Override
	public String randomGetFiles_city(){
		return randomGetFiles_City();
	}

	@Override
	public String randomGetFiles_county(){
		return randomGetFiles_County();
	}

	/**
	 * 随机抽取方法
	 * @param list-抽取文件池
	 * @param num-随机抽取个数
	 * @return
	 */
	public List<PenalizeSyncFile> getRandomFile(List<PenalizeSyncFile> list,int num){

		List<PenalizeSyncFile> files = new ArrayList<PenalizeSyncFile>();

		List<PenalizeSyncFile> files_bak = new ArrayList<PenalizeSyncFile>();//存放金额小于0.5
		PenalizeSyncFile file = null;
		if(list.size()>0){
			for(int i = 0;i<num;i++){
				if(list.size()>0){//list的size()是在逐次递减，需要每次都判断
					file = list.remove(new Random().nextInt(list.size()));
					if("0".equals(file.getFiletype())||"1".equals(file.getFiletype())){
						double cash = 0;
						try{
							String cashStr = file.getCash();
							if(cashStr==null||"".equals(cashStr)){
								cashStr = "0";
							}
							cash = Double.parseDouble(cashStr);
						}catch(Exception e){
							cash = 0;
						}
						if(cash<0.5){
							files_bak.add(file);
						}else{
							files.add(file);
						}
					}else{
						files.add(file);
					}

				}
			}
			if(files.size()<num){//如果没有抽够，则从小于5000的里边再随机抽取一个
				int fag = num - files.size();
				PenalizeSyncFile file1 = null;
				if(files_bak.size()>0){
					for(int i = 0;i<fag;i++){
						if(files_bak.size()>0){//list的size()是在逐次递减，需要每次都判断
							file1 = files_bak.remove(new Random().nextInt(files_bak.size()));
							files.add(file1);
						}
					}
				}
			}
		}

		return files;
	}

	/**
	 * 特殊随机抽取方法-5种类型中，随意抽取2种
	 * @param list-抽取文件池
	 * @param num-随机抽取个数
	 * @return
	 */
	public List<PenalizeSyncFile> getRandomFileSpecial(List<PenalizeSyncFile> list,int num){

		List<PenalizeSyncFile> files = new ArrayList<PenalizeSyncFile>();
		StringBuffer strbuf = new StringBuffer("");//存放当前已经抽中的类型，格式如：123456
		PenalizeSyncFile file = null;
		if(list.size()>0){
			for(int i = 0;i<num;i++){
				if(list.size()>0){//list的size()是在逐次递减，需要每次都判断
					file = list.remove(new Random().nextInt(list.size()));
					if(strbuf.indexOf(file.getFiletype())==-1){//如果抽中的类型没有包含当前类型,则放入结果容器
						files.add(file);
						strbuf.append(file.getFiletype());
					}
				}
			}
		}
		return files;
	}

	/**
	 * 把随机抽取的案卷插入案卷表
	 * @param list-随机抽取案卷的集合
	 * @param areaLevel-抽去单位的级别（省1、市2、县3）
	 */
	public int insertRandomFileToFiles(List<PenalizeSyncFile> list,String areaLevel){

		int resQU= -1;

		List<Files> fileList = new ArrayList<Files>();
		for(PenalizeSyncFile syncFile:list){
			Files file = new Files();
			if("1".equals(areaLevel)){//省级抽取（抽取范围每个下辖地级市（抽取的范围含包括其市所有的下辖县）抽取1份）
				file.setAreacode(syncFile.getAreacode().substring(0, 2)+"000000");
			}else if("2".equals(areaLevel)){//市级抽取（抽取本级）
				String areacode = syncFile.getAreacode();
				if(areacode.startsWith("11")||areacode.startsWith("12")||areacode.startsWith("31")||areacode.startsWith("55")){
					file.setAreacode(areacode);
				}else{
					file.setAreacode(areacode.substring(0, 4)+"0000");
				}

			}else if("3".equals(areaLevel)){//县级抽取（抽取本级）
				file.setAreacode(syncFile.getAreacode());
			}
			file.setFiletype(syncFile.getFiletype());
			file.setBelongareacode(syncFile.getAreacode());
			file.setFilename(syncFile.getFilename());
			file.setFilecode(syncFile.getFilecode());
			file.setOldid(syncFile.getOldid());
			file.setReporttype("1");//1代表随机抽取类型
			fileList.add(file);
		}
		int res= filesMapper.insertBatchList(fileList);//批量插入案卷
		if(res>0){//把成功插入的案卷，在随机池中标记，下次不能被选中
			if(list.size()>0){
				resQU = penalizeSyncFileMapper.updateBatchFilse(list,areaLevel);//把随机成功的案卷标记为已被随机抽选过
			}
		}
		return resQU;
	}


	/**
	 * 省级随机抽取案卷
	 */
//	public String randomGetFiles_Pro(){
//		try{
//			//一次查询所有的省级单位
//			List<Tarea> area_pro = areaMapper.getAareaListByArearLevel("2");
//
//			//存放结果的容器
//			List<PenalizeSyncFile> container = new ArrayList<PenalizeSyncFile>();
//
//			//查询所有的市级
//			List<Tarea> citys = areaMapper.getAareaListByArearLevel("2");
//			for(Tarea city:citys){
//				//循环去库中查询所有该市的案卷
//				List<PenalizeSyncFile> files = null;//penalize SyncFileMapper.selectPenalize SyncFile(ChangnengUtil.trimEx(city.getCode()), "'0','1','6','7','2','3'");
//				container.addAll(getRandomFile(files,1));
//			}
//			if(container.size()>0){
				//int a = insertRandomFileToFiles(container,"1");//把随机抽取的案卷插入案卷表
//				if(a>0){
//					return "省级随机案卷抽取成功（"+a+"）";
//				}else{
//					return "省级随机案卷抽取失败=插入案卷表未成功";
//				}
//			}else{
//				return "省级随机案卷抽取失败=抽取案卷未成功";
//			}
//
//			//按省分类
//			for(Tarea pro:area_pro){
//				for(PenalizeSyncFile file:container){
//
//				}
//			}
//		}catch(Exception e){
//			e.printStackTrace();
//			return "后台出现异常，请联系管理员";
//		}
//
//
//	}

	/**
	 * 市级随机抽取案卷=================================2019
	 * 2019更新过
	 * 2020更新过
	 */
	public String randomGetFiles_City(){
		try{
			//存放随机结果的容器
			List<PenalizeSyncFile> container = new ArrayList<PenalizeSyncFile>();

			//查询被推选的市级
			List<ElectionUnit> citys = electionUnitMapper.selectElectionUintListByType(null,"'2'");

			for(ElectionUnit elcetionCity:citys){

				String subAreaCode = elcetionCity.getAreacode();
				if(subAreaCode.startsWith("11")||subAreaCode.startsWith("12")||subAreaCode.startsWith("31")||subAreaCode.startsWith("55")){
					subAreaCode = elcetionCity.getAreacode().substring(0, 6);
				}else{
					subAreaCode = elcetionCity.getAreacode().substring(0, 4);
				}
				//===2018年被推选的案卷该从个人案卷同步表penalizeSyncFileSpe（区别：含未结案的）中随机抽取
				//直辖市与兵团的市级单位抽取数量跟县级一样--这里犯错线上未升级
				if(subAreaCode.startsWith("11")||subAreaCode.startsWith("12")||subAreaCode.startsWith("31")||subAreaCode.startsWith("55")||subAreaCode.startsWith("66")){
					//循环去库中查询本区县所有一般行政处罚案卷
					List<PenalizeSyncFile> files1 = penalizeSyncFileMapper.selectPenalizeSyncFileSpe(subAreaCode, "'0'");
					//抽取行一般行政处罚案卷2份
					//container.addAll(getRandomFile(files1,2));这是20年需求

					//抽取行一般行政处罚案卷3份
					container.addAll(getRandomFile(files1,1));

					//循环去库中查询本区县所有===适用四个配套办法（指按日计罚、查封扣押、限产停产、适用行政拘留、涉嫌犯罪）
					List<PenalizeSyncFile> files2 = penalizeSyncFileMapper.selectPenalizeSyncFileSpe(elcetionCity.getAreacode(), "'1','2','3','6','7'");
					/*//抽取本市适用四个配套办法2份（指按日计罚、查封扣押、限产停产、适用行政拘留、涉嫌犯罪）
					container.addAll(getRandomFile(files2,2));*///这是18年需求

					//抽取本市适用四个配套办法任意1份（指按日计罚、查封扣押、限产停产、适用行政拘留、涉嫌犯罪）
					container.addAll(getRandomFile(files2,1));

				}else{
					//循环去库中查询本市所有一般行政处罚案卷（含县级）
					List<PenalizeSyncFile> files1 = penalizeSyncFileMapper.selectPenalizeSyncFileSpe(subAreaCode, "'0'");
					//抽取行政区域内一般行政处罚案卷2份（含县级）
					//container.addAll(getRandomFile(files1,2));这是19年需求

					//抽取行一般行政处罚案卷3份
					container.addAll(getRandomFile(files1,1));

					//循环去库中查询本市所有===适用四个配套办法（指按日计罚1、查封扣押6、限产停产7、适用行政拘留2、涉嫌犯罪3）（含县级）
					List<PenalizeSyncFile> files2 = penalizeSyncFileMapper.selectPenalizeSyncFileSpe(subAreaCode, "'1','2','3','6','7'");
					//适用四个配套办法案卷任意2份不同类型的（按日计罚-1、查封扣押-6、限产停产-7、移送拘留-2、犯罪移送-3，其中任意2份不同类型的）；（含县级）
					//container.addAll(getRandomFileSpecial(files2,2));//必须任意2个不同类型
					//container.addAll(getRandomFile(files2,2));//任意2个即可 2019年需求

					//抽取本市适用四个配套办法任意1份（指按日计罚、查封扣押、限产停产、适用行政拘留、涉嫌犯罪）
					container.addAll(getRandomFile(files2,1));

					/*List<PenalizeSyncFile> files2 = penalizeSyncFileMapper.selectPenalizeSyncFileSpe2018(subAreaCode, "'1'");
					List<PenalizeSyncFile> files3 = penalizeSyncFileMapper.selectPenalizeSyncFileSpe2018(subAreaCode, "'6'");
					List<PenalizeSyncFile> files4 = penalizeSyncFileMapper.selectPenalizeSyncFileSpe2018(subAreaCode, "'7'");
					List<PenalizeSyncFile> files5 = penalizeSyncFileMapper.selectPenalizeSyncFileSpe2018(subAreaCode, "'2','3'");
					//适用四个配套办法案卷各1份（按日计罚-1、查封扣押-6、限产停产-7、移送拘留-2或犯罪移送-3，即共4份）；（含县级）
					container.addAll(getRandomFile(files2,1));
					container.addAll(getRandomFile(files3,1));
					container.addAll(getRandomFile(files4,1));
					container.addAll(getRandomFile(files5,1));*///这是18年需求

				}
			}
			if(container.size()>0){
				int a =insertRandomFileToFiles(container,"2");
				if(a>0){
					return "市级随机案卷抽取成功（"+a+"）";
				}else{
					return "市级随机案卷抽取失败=插入案卷表未成功";
				}
			}else{
				return "市级随机案卷抽取失败=抽取案卷未成功";
			}
		}catch(Exception e){
			e.printStackTrace();
			return "后台出现异常，请联系管理员";
		}

	}



	/**
	 * 市级随机抽取案卷=================================2019
	 * 2019更新过
	 * 2020更新过
	 */
	public String randomGetFiles(){
		try{
			//存放随机结果的容器
			List<PenalizeSyncFile> container = new ArrayList<PenalizeSyncFile>();

			//查询被推选的市级
			List<ElectionUnit> citys = electionUnitMapper.selectElectionUintListByType(null,"'2'");

			for(ElectionUnit elcetionCity:citys){

				String subAreaCode = elcetionCity.getAreacode();
				if(subAreaCode.startsWith("11")||subAreaCode.startsWith("12")||subAreaCode.startsWith("31")||subAreaCode.startsWith("55")){
					subAreaCode = elcetionCity.getAreacode().substring(0, 6);
				}else{
					subAreaCode = elcetionCity.getAreacode().substring(0, 4);
				}
				//===2018年被推选的案卷该从个人案卷同步表penalizeSyncFileSpe（区别：含未结案的）中随机抽取
				//直辖市与兵团的市级单位抽取数量跟县级一样--这里犯错线上未升级
				if(subAreaCode.startsWith("11")||subAreaCode.startsWith("12")||subAreaCode.startsWith("31")||subAreaCode.startsWith("55")||subAreaCode.startsWith("66")){
					//循环去库中查询本区县所有一般行政处罚案卷
					List<PenalizeSyncFile> files1 = penalizeSyncFileMapper.selectPenalizeSyncFileSpe(subAreaCode, "'0'");
					//抽取行一般行政处罚案卷2份
					//container.addAll(getRandomFile(files1,2));这是20年需求

					//抽取行一般行政处罚案卷3份
					container.addAll(getRandomFile(files1,1));

					//循环去库中查询本区县所有===适用四个配套办法（指按日计罚、查封扣押、限产停产、适用行政拘留、涉嫌犯罪）
					List<PenalizeSyncFile> files2 = penalizeSyncFileMapper.selectPenalizeSyncFileSpe(elcetionCity.getAreacode(), "'1','2','3','6','7'");
					/*//抽取本市适用四个配套办法2份（指按日计罚、查封扣押、限产停产、适用行政拘留、涉嫌犯罪）
					container.addAll(getRandomFile(files2,2));*///这是18年需求

					//抽取本市适用四个配套办法任意1份（指按日计罚、查封扣押、限产停产、适用行政拘留、涉嫌犯罪）
					container.addAll(getRandomFile(files2,1));

				}else{
					//循环去库中查询本市所有一般行政处罚案卷（含县级）
					List<PenalizeSyncFile> files1 = penalizeSyncFileMapper.selectPenalizeSyncFileSpe(subAreaCode, "'0'");
					//抽取行政区域内一般行政处罚案卷2份（含县级）
					//container.addAll(getRandomFile(files1,2));这是19年需求

					//抽取行一般行政处罚案卷3份
					container.addAll(getRandomFile(files1,1));

					//循环去库中查询本市所有===适用四个配套办法（指按日计罚1、查封扣押6、限产停产7、适用行政拘留2、涉嫌犯罪3）（含县级）
					List<PenalizeSyncFile> files2 = penalizeSyncFileMapper.selectPenalizeSyncFileSpe(subAreaCode, "'1','2','3','6','7'");
					//适用四个配套办法案卷任意2份不同类型的（按日计罚-1、查封扣押-6、限产停产-7、移送拘留-2、犯罪移送-3，其中任意2份不同类型的）；（含县级）
					//container.addAll(getRandomFileSpecial(files2,2));//必须任意2个不同类型
					//container.addAll(getRandomFile(files2,2));//任意2个即可 2019年需求

					//抽取本市适用四个配套办法任意1份（指按日计罚、查封扣押、限产停产、适用行政拘留、涉嫌犯罪）
					container.addAll(getRandomFile(files2,1));

					/*List<PenalizeSyncFile> files2 = penalizeSyncFileMapper.selectPenalizeSyncFileSpe2018(subAreaCode, "'1'");
					List<PenalizeSyncFile> files3 = penalizeSyncFileMapper.selectPenalizeSyncFileSpe2018(subAreaCode, "'6'");
					List<PenalizeSyncFile> files4 = penalizeSyncFileMapper.selectPenalizeSyncFileSpe2018(subAreaCode, "'7'");
					List<PenalizeSyncFile> files5 = penalizeSyncFileMapper.selectPenalizeSyncFileSpe2018(subAreaCode, "'2','3'");
					//适用四个配套办法案卷各1份（按日计罚-1、查封扣押-6、限产停产-7、移送拘留-2或犯罪移送-3，即共4份）；（含县级）
					container.addAll(getRandomFile(files2,1));
					container.addAll(getRandomFile(files3,1));
					container.addAll(getRandomFile(files4,1));
					container.addAll(getRandomFile(files5,1));*///这是18年需求

				}
			}
			if(container.size()>0){
				int a =insertRandomFileToFiles(container,"2");
				if(a>0){
					return "市级随机案卷抽取成功（"+a+"）";
				}else{
					return "市级随机案卷抽取失败=插入案卷表未成功";
				}
			}else{
				return "市级随机案卷抽取失败=抽取案卷未成功";
			}
		}catch(Exception e){
			e.printStackTrace();
			return "后台出现异常，请联系管理员";
		}

	}

	/**
	 * 县级随机抽取案卷=================================2019
	 * 2019更新过
	 * 2020更新过
	 */
	public String randomGetFiles_County() {
		try {

			//存放随机结果的容器
			List<PenalizeSyncFile> container = new ArrayList<PenalizeSyncFile>();

			//查询被推选的县级
			List<ElectionUnit> citys = electionUnitMapper.selectElectionUintListByType(null, "'3'");
			//List<ElectionUnit> citys = electionUnitMapper.selectElectionUintListByType(null,"'2'");
			for (ElectionUnit elcetionCity : citys) {
				System.out.println(elcetionCity);
				List<PenalizeSyncFile> files1 = penalizeSyncFileMapper.selectPenalizeSyncFileSpe(elcetionCity.getAreacode(), "'0'");
				for (PenalizeSyncFile file : files1) {
					System.out.println(file);

				}
			}
			for (ElectionUnit elcetionCity : citys) {
				//2018抽取行政区本级一般行政处罚案卷2份、适用四个配套办法案卷2份（按日计罚、查封扣押、限产停产、移送拘留、犯罪移送，共任意2份）

				//循环去库中查询本区县所有一般行政处罚案卷

//				正式用
				List<PenalizeSyncFile> files1 = penalizeSyncFileMapper.selectPenalizeSyncFileSpe(elcetionCity.getAreacode(), "'0'");
				//调查机构处理县级
				//List<PenalizeSyncFile> files1 = penalizeSyncFileMapper.selectPenalizeSyncFileSpeS(elcetionCity.getAreacode().substring(0, 4), "'0'",elcetionCity.getAreaname().substring(0, 2));
				//抽取行政区本级一般行政处罚案卷2份
				container.addAll(getRandomFile(files1, 1));//2019年需求

				//抽取行政区本级一般行政处罚案卷3份(2020年需求)
//				List<PenalizeSyncFile> files1 = penalizeSyncFileMapper.selectPenalizeSyncFileSpeS();
//
//				container.addAll(files1);

				//循环去库中查询本区县所有===适用四个配套办法2份（指按日计罚、查封扣押、限产停产、适用行政拘留、涉嫌犯罪）
				List<PenalizeSyncFile> files2 = penalizeSyncFileMapper.selectPenalizeSyncFileSpe(elcetionCity.getAreacode(), "'1','2','3','6','7'");
				/*//抽取本市适用四个配套办法2份（指按日计罚、查封扣押、限产停产、适用行政拘留、涉嫌犯罪）
				container.addAll(getRandomFile(files2,2));*////这是18年需求

				//抽取本市适用四个配套办法任意1份（指按日计罚、查封扣押、限产停产、适用行政拘留、涉嫌犯罪）
				container.addAll(getRandomFile(files2, 1));

			}
			if (container.size() > 0) {
				int a = insertRandomFileToFiles(container, "3");
				if (a > 0) {
					return "县级随机案卷抽取成功（" + a + "）";
				} else {
					return "县级随机案卷抽取失败=插入案卷表未成功";
				}
			} else {
				return "县级随机案卷抽取失败=抽取案卷未成功";
			}
		} catch (Exception e) {
			e.printStackTrace();
			return "后台出现异常，请联系管理员";

		}
	}
	@Override
	public String setPenalizeSycFilesIsSetected() {
		try{
			int res = penalizeSyncFileMapper.updateSELECTED();
			return "推选案卷锁定成功("+res+")";
		}catch(Exception e){
			e.printStackTrace();
			return "error:案卷标记失败";
		}
	}

	/**
	 * 初始专家合议案件给专家委员
	 */
	@Override
	public String randomExpertCommitFiles() {
		//获取专家打分大于15分的案件
		// 一次性获得到 案件表中信息，除去 稽查案卷 类型案卷
		// 2016-12-26 修改需求为：可重复合议，这里取值则取合议分为空的
		StringBuffer result = new StringBuffer("");
		List<Files> filesCommitList = new  ArrayList<Files>();
		List<Files> filesList = filesMapper.getDiscussFilesList();
		// 记录错误信息 , 错误id号
		for(Files files :filesList){
			if(ChangnengUtil.isNull(files.getIsconsiderexpcommit())){
				//委员未合议的案卷，需要重新分配合议
				List<ExpertHandlFileList> expertList = expertMapper.getExpertFileListByFileIdAndExpertId2017(files.getId(), files.getExpertaid(), files.getExpertbid());
				if(expertList.size()==2){
					FilesWithBLOBs record = new FilesWithBLOBs();
					record.setId(files.getId());
					// 专家AB打分 ，专家合议分，
					Double scoreExpertA = null, scoreExpertB = null;
					Boolean isConsiderA = false;
					// 下面连个map是因为mysql用 in 的形式查询不会返回顺序集合，
					Map<Integer, Float> scoreExpert = new HashMap<Integer, Float>();
					// 获得专家分值，这里集合是无序的，所以要放map确定唯一标志
					if (expertList.get(0).getExpertfinalscore() != null) {
						scoreExpert.put(expertList.get(0).getExpertid(), expertList.get(0).getExpertfinalscore());
						scoreExpertA = BigDecimalUtil.getBigDecimalByFloat(expertList.get(0).getExpertfinalscore(), 2);
					}
					if (expertList.get(1).getExpertfinalscore() != null) {
						scoreExpert.put(expertList.get(1).getExpertid(), expertList.get(1).getExpertfinalscore());
						scoreExpertB = BigDecimalUtil.getBigDecimalByFloat(expertList.get(1).getExpertfinalscore(), 2);
					}
					// 保证两份案卷都被打分
					if(expertList.get(0).getExpertfinalscore()==null || expertList.get(1).getExpertfinalscore() ==null ){
						continue;
					}
					record.setExpertascore(scoreExpert.get(files.getExpertaid()));
					record.setExpertbscore(scoreExpert.get(files.getExpertbid()));
					if (scoreExpertA != null && scoreExpertB != null) {
						ExpertHandlFileList  expertHandlFileList1  =  new ExpertHandlFileList();
						ExpertHandlFileList  expertHandlFileList2  =  new ExpertHandlFileList();
						Double expertfinalscoreAnJuan = getTwoDecimalDouble(scoreExpertA* - scoreExpertB);
						isConsiderA = getAccordWithTwenty(expertfinalscoreAnJuan);
						// 若相差不超过20分 则 平均值= （两分相加） /2
						if (isConsiderA) {
							BigDecimal bd1 = new BigDecimal(Double.toString(scoreExpertA));
							BigDecimal bd2 = new BigDecimal(Double.toString(scoreExpertB));
							// 稽查案卷 最终得分为交叉合议分不参与专家评分
							if(files.getFiletype()!=null && "5".equals(files.getFiletype())){
								// 5类型案卷（稽查案卷）专家无需合议
								record.setIsconsider("0");
								record.setFinalscore((float) getTwoDecimalFloat((float)(bd1.add(bd2).doubleValue() /2.0)));
							}else{
								record.setExpertconsiderscore((float) getTwoDecimalFloat((float)(bd1.add(bd2).doubleValue() /2.0)));
								record.setFinalscore((float)BigDecimalUtil.getCustomFormula(record.getExpertconsiderscore(), files.getCrossconsiderscore(), 0.7, 0.3));
							}
							expertHandlFileList1.setHandltype(0);
							expertHandlFileList2.setHandltype(0);
						}else{
							//需要合议的案件放到集合中，委员从新分配案件
							expertHandlFileList1.setHandltype(1);
							expertHandlFileList2.setHandltype(1);
							filesCommitList.add(files);
						}
						// 已经合议的案卷就不可以在进行修改分值
						// id 必须有
						expertHandlFileList1.setId(expertList.get(0).getId());
						expertHandlFileList1.setConsiderState(1);

						expertHandlFileList2.setId(expertList.get(1).getId());
						expertHandlFileList2.setConsiderState(1);

						expertMapper.updateByPrimaryKeySelective(expertHandlFileList1);
						expertMapper.updateByPrimaryKeySelective(expertHandlFileList2);

					}
					filesMapper.updateByPrimaryKeySelective(record);
					}
				}
			}
			ExpertUser expert = new ExpertUser();
			TCDictionary startTcDictionary = tCDictionaryMapper.selectDataByTemp("startIndex");
			TCDictionary endIndexTcDictionary = tCDictionaryMapper.selectDataByTemp("endIndex");
			//获取委员专家
			expert.setLevel(1);
			List<ExpertUser> expertList = expertUserMapper.selectExpertCommitUser(expert);
			if (filesCommitList.size() > 0 && expertList.size() > 1) {
				//initFileListToExpertHandle(fileList, expertList);
				if(startTcDictionary != null && ChangnengUtil.isNull(startTcDictionary.getCode())){
					startTcDictionary.setCode("0");
				}
				if(endIndexTcDictionary != null && ChangnengUtil.isNull(endIndexTcDictionary.getCode())){
					Integer size = expertList.size()-1;
					endIndexTcDictionary.setCode(size.toString());
				}
				initFileListToCommitHandle(filesCommitList, expertList,"1",Integer.valueOf(startTcDictionary.getCode()),Integer.valueOf(endIndexTcDictionary.getCode()));
				result.append("success提示：案卷数量为" + filesCommitList.size() +"，专家人数为："+expertList.size()+ ",分配成功*****<br/>");
				//System.out.println("提示：案卷类型--" + type + "的案卷数量为" + fileList.size() + ",分配成功*****<br/>");
			} else if (filesCommitList.size() == 0) {
				result.append("success提示：案卷数量为零,没有分配案卷*****<br/>");
			} else {
				result.append("success提示：专家数量不足两位,没有分配案卷*****<br/>");
			}
		return result.toString();
	}


	/**
	 * 初始专家合议案件给专家委员第二套方案的第1步（专家委员不能分配到自己打过分的案卷）
	 * ZJ
	 */
	@Override
	public String initExpertScoreHeYi2019() {
		//获取专家打分大于15分的案件
		//2016-12-26 修改需求为：可重复合议，这里取值则取合议分为空的
		StringBuffer result = new StringBuffer("");
		List<Files> filesList = filesMapper.getDiscussFilesList();//查询所有待比较，判断是否进行合一的案卷
		int count =0;//用来记录合议数量

		for(Files files :filesList){
			if("0".equals(files.getIsconsider())){//委员未合议的案卷，需要重新分配合议
				/**
				 * 查询A轮该案卷分配2个专家的打分情况
				 */
				List<ExpertHandlFileList> expertList = expertMapper.getExpertFileListByFileIdAndExpertId2017(files.getId(), files.getExpertaid(), files.getExpertbid());

				if(expertList.size()==2){//确保是每个案卷分两个人
					FilesWithBLOBs record = new FilesWithBLOBs();
					record.setId(files.getId());

					/**
					 * 保证两份案卷都被打分,若有1份未打分，则退出继续下一个案卷
					 */
					/**
					 * 2023 wangjy修改一人严重不全另外一人正常评审进入第二轮
					 */
					//	if(expertList.get(0).getExpertfinalscore()==null || expertList.get(1).getExpertfinalscore() ==null  ){

					if(expertList.get(0).getExpertfinalscore()==null || expertList.get(1).getExpertfinalscore() ==null || expertList.get(0).getFileMaterials().equals("1") || expertList.get(1).getFileMaterials().equals("1") ){
						continue;
					}

					/**
					 * 把两个专家打的分数置入案卷表
					 */
					Double expertA1Score = null, expertA2Score = null;//专家A1A2打分
					Double pageA1Score = null, pageA2Score = null;//实体分专家A1A2打分
					Double entityA1Score = null, entityA2Score = null;//实体分专家A1A2打分
					Boolean isConsiderA = false;//是否需要专家合议的标识
					Boolean isConsiderT = false;//是否需要专家合议的标识
					Map<Integer, Float> scoreExpertMap = new HashMap<Integer, Float>();//获得专家分值，这里集合是无序的，所以要放map确定唯一标志

					if (expertList.get(0).getExpertfinalscore() != null) {//专家A1打完分
						scoreExpertMap.put(expertList.get(0).getExpertid(), expertList.get(0).getExpertfinalscore());
						expertA1Score = BigDecimalUtil.getBigDecimalByFloat(expertList.get(0).getExpertfinalscore(), 2);
						pageA1Score = BigDecimalUtil.getBigDecimalByFloat(expertList.get(0).getPaperscore(), 2);
						entityA1Score = BigDecimalUtil.getBigDecimalByFloat(expertList.get(0).getEntityscore(), 2);
					}
					if (expertList.get(1).getExpertfinalscore() != null) {//专家A2打完分
						scoreExpertMap.put(expertList.get(1).getExpertid(), expertList.get(1).getExpertfinalscore());
						expertA2Score = BigDecimalUtil.getBigDecimalByFloat(expertList.get(1).getExpertfinalscore(), 2);
						pageA2Score = BigDecimalUtil.getBigDecimalByFloat(expertList.get(1).getPaperscore(), 2);
						entityA2Score = BigDecimalUtil.getBigDecimalByFloat(expertList.get(1).getEntityscore(), 2);
					}
					record.setExpertascore(scoreExpertMap.get(files.getExpertaid()));
					record.setExpertbscore(scoreExpertMap.get(files.getExpertbid()));

					/**
					 * 更新打分信息，经过合议初始化操作的，无论是否合议，分数不可再编辑
					 */
					ExpertHandlFileList  expertHandlFileList1  =  new ExpertHandlFileList();
					expertHandlFileList1.setId(expertList.get(0).getId());//id必须有
					expertHandlFileList1.setConsiderState(1);//已经合议的案卷就不可以在进行修改分值
					ExpertHandlFileList  expertHandlFileList2  =  new ExpertHandlFileList();
					expertHandlFileList2.setId(expertList.get(1).getId());//id必须有
					expertHandlFileList2.setConsiderState(1);//已经合议的案卷就不可以在进行修改分值


					/**
					 * 根据两个专家分判断是否需要合议，同属置入标记
					 */
					if (pageA1Score != null && pageA2Score != null) {
						Double expertfinalscoreAnJuan = getTwoDecimalDouble(pageA1Score - pageA2Score);//求两个专家打分差
						isConsiderT = getAccordWithTwenty(expertfinalscoreAnJuan);//若差15分，false代表需要合议,true代表不需要
						if (!isConsiderT) {
							if (entityA1Score == 50 && entityA2Score == 50) {
								record.setCaseCheckState("1");
							}
							if (entityA1Score == 0 || entityA2Score == 0) {
								record.setCaseCheckState("3");
							}
						} else {
							if (entityA1Score == 0 || entityA2Score == 0) {
								record.setCaseCheckState("2");
							}
						}
					}

					if (expertA1Score != null && expertA2Score != null) {
						Double expertfinalscoreAnJuan = getTwoDecimalDouble(expertA1Score - expertA2Score);//求两个专家打分差
						isConsiderA = getAccordWithTwenty(expertfinalscoreAnJuan);//若差15分，false代表需要合议,true代表不需要
						if(entityA1Score==0 || entityA2Score==0 || (!isConsiderA)){
							record.setIsconsider("1");//1代表合议状态，需要进行B轮评分
							count++;
							/**
							 * 假如只进实体页面放开此注释 wangjy
							 */
//							if(entityA1Score==0 && entityA2Score==0 && isConsiderA){//若两人都否决，并且卷面相差小于15分
//								expertHandlFileList1.setEntityState("1");
//								expertHandlFileList2.setEntityState("1");
//							}
						}else{
							//若实体都是满分且
							//若相差不超过15分，即不需要合议，计算 平均值=（两分相加） /2并置入最终专家确认分
							BigDecimal bd1 = new BigDecimal(Double.toString(expertA1Score));
							BigDecimal bd2 = new BigDecimal(Double.toString(expertA2Score));
							record.setExpertconsiderscore((float) getTwoDecimalFloat((float)(bd1.add(bd2).doubleValue() /2.0)));
							FilesWithBLOBs f = filesMapper.selectByPrimaryKey(record.getId());
							Double rate="0".equals(f.getFiletype())?1.0:1.0;
							record.setExpertconsiderscore2((float) getTwoDecimalFloat((float)(bd1.add(bd2).doubleValue()*rate /2.0)));

							String fileMaterials="1".equals(expertHandlFileList1.getScoredstate())?expertHandlFileList1.getFileMaterials():expertHandlFileList2.getFileMaterials();
							record.setFileMaterials(fileMaterials);
							//判断案卷类型  一般行政处罚*1 其它案卷*1.2

						}
//						if (isConsiderA) {//若相差不超过15分，即不需要合议，计算 平均值=（两分相加） /2并置入最终专家确认分
//							BigDecimal bd1 = new BigDecimal(Double.toString(expertA1Score));
//							BigDecimal bd2 = new BigDecimal(Double.toString(expertA2Score));
//							record.setExpertconsiderscore((float) getTwoDecimalFloat((float)(bd1.add(bd2).doubleValue() /2.0)));
//						}else{//需要合议的案件，置入合议标记，进入下一轮打分
//							/**
//							 *如果相差超过15分，则分3个区间（标记）： 具体到是那一部分需要合议，
//							 */
//							record.setIsconsider("1");//1代表合议状态，需要进行B轮评分
//							count++;
//						}
						expertMapper.updateByPrimaryKeySelective(expertHandlFileList1);
						expertMapper.updateByPrimaryKeySelective(expertHandlFileList2);
					}


					/**
					 * 更新案卷信息
					 */
					filesMapper.updateByPrimaryKeySelective(record);
				}
			}
		}
		Date now =  new Date();
		DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm");
		result.append(df.format(now)+"专家打分共有" + count + "份需要委员合议！");
		return result.toString();
	}

	/**
	 * 系统给专家委员分配随机案卷
	 */
	@Override
	public String randomExpertCommitFiles2017() throws Exception {
		List<String> areaParaA = Const.expertCommitAList;
		List<String> areaParaB = Const.expertCommitBList;

		List<Files> expertFilesA = new ArrayList<Files>();//A组委员在专家评分阶段打过分的案卷
		List<Files> expertFilesB = new ArrayList<Files>();//B组委员在专家评分阶段打过分的案卷
		//根据区划信息案卷信息
		List<Files> fileList1  = filesMapper.selectListByIdListPara(areaParaA);
		expertFilesA.addAll(fileList1);
		List<Files> fileList2  = filesMapper.selectListByIdListPara(areaParaB);
		expertFilesB.addAll(fileList2);
		List<Files> expertFilesO = filesMapper.selectListByPara(UtilOperate.listToString(areaParaA)+","+UtilOperate.listToString(areaParaB));//其他既没有被A组委员打过分，也没有被B组委员打过分的案卷，简称【其他案卷】

		List<Files> expertFilesOA = new ArrayList<Files>();//存放根据平均数计算需要给A组委员补充的【其他案卷】数
		List<Files> expertFilesOB = new ArrayList<Files>();//存放根据平均数计算需要给B组委员补充的【其他案卷】数

		int allSize = expertFilesA.size()+expertFilesB.size()+expertFilesO.size();//所有案卷;
		int averSize = allSize/7+1;//按7个委员平均分


		if(allSize>0){
			int oaAdd = averSize*3-expertFilesA.size();//按B组3个人的标准，把B组打过分的分给A组后，然后计算出A组应该补充的案卷数量（其实该部分案卷最后是要给B组委员随机分配的）
			for(int i=0;i<expertFilesO.size();i++){
				if(i<oaAdd){
					expertFilesOA.add(expertFilesO.remove(i));
				}
			}
			expertFilesOB.addAll(expertFilesO);//剩余的全分给B组（其实该部分案卷最后是要给A组委员随机分配的）
		}


		expertFilesA.addAll(expertFilesOA);//A组专家委员打过分的案卷+其他普通案卷（其实该部分案卷最后是要给B组委员随机分配的）
		expertFilesB.addAll(expertFilesOB);//B组专家委员打过分的案卷+其他普通案卷（其实该部分案卷最后是要给A组委员随机分配的）




		//根据区划信息获取交叉评审人员
		List<ExpertUser> expertListA = expertUserMapper.selectExpertCommitUserByIds(areaParaA);
		List<ExpertUser> expertListB = expertUserMapper.selectExpertCommitUserByIds(areaParaB);

		//分配案卷
		StringBuffer result = new StringBuffer("");

		if (expertFilesA.size() > 0 && expertListB.size() > 1) {
			initFileListToCommitHandle2017(expertFilesA, expertListB);
			result.append("success提示：第一组专家委员【" + UtilOperate.listToString(areaParaA) + "】下的案卷数量为:" + expertFilesA.size() +"，第二组专家委员【" +UtilOperate.listToString(areaParaB) +"】下交叉评审人员数量为:"+expertListB.size()+",分配成功*****<br/>");
			//System.out.println("success提示：第一部分区划【" + UtilOperate.listToString(areaPara1) + "】下的案卷数量为:" + fileList1.size() +"，第二部分区划【" +UtilOperate.listToString(areaPara2) +"】下交叉评审人员数量为:"+crossList2.size()+",分配成功*****<br/>");
		} else if (expertFilesA.size() == 0) {
			result.append("error提示：第一组专家委员【" + UtilOperate.listToString(areaParaA) + "】下的案卷数量为零,没有分配案卷*****<br/>");
		} else {
			result.append("error提示：第二组专家委员【" + UtilOperate.listToString(areaParaB) + "】下的专家委员人数不足两位,没有分配案卷*****<br/>");
		}
		if (expertFilesB.size() > 0 && expertListA.size() > 1) {
			initFileListToCommitHandle2017(expertFilesB, expertListA);
			result.append("success提示：第二组专家委员【" + UtilOperate.listToString(areaParaB) + "】下的案卷数量为" + expertFilesB.size() +"，第一组专家委员【" +UtilOperate.listToString(areaParaA) +"】下交叉评审人员数量为:"+expertListA.size()+ ",分配成功*****<br/>");
			//System.out.println("success提示：第二部分区划【" + UtilOperate.listToString(areaPara2) + "】下的案卷数量为" + fileList2.size() +"，第一部分区划【" +UtilOperate.listToString(areaPara1) +"】下交叉评审人员数量为:"+crossList1.size()+ ",分配成功*****<br/>");
		} else if (fileList2.size() == 0) {
			result.append("error提示：第二组专家委员【" + UtilOperate.listToString(areaParaB) + "】下的案卷数量为零,没有分配案卷*****<br/>");
		} else {
			result.append("error提示：第一组专家委员【" + UtilOperate.listToString(areaParaA) + "】下的专家委员人数不足两位,没有分配案卷*****<br/>");
		}

		return result.toString();
	}

	/**
	 * 给专家委员分配案卷
	 */
	@Override
	public String initFileListToExpertCommitHandle2018(){
		/**
		 * 查询所有需要合议的案卷
		 */
		List<Files> fileList = filesMapper.selectFileListNeedHeYi();

		/**
		 * 查询所有的专家委员
		 */
		ExpertUser search = new ExpertUser();
		search.setLevel(1);
		List<ExpertUser> expCommitList = expertUserMapper.selectExpertCommitUser(search);


		List<ExpertHandlFileList> handleList = new ArrayList<ExpertHandlFileList>();//定义存放更新【案卷与专家用户的关联信息】的集合
		List<Files> updateFileList = new ArrayList<Files>();//定义存放更新【案卷信息】的集合

		//记录A专家分到了哪些B专家
		Map<String,Map<String,Integer>> userAMap = new HashMap<>();

		for(Files file : fileList){//循环所有的待合议案卷
			/**
			 * 获取前一轮专家的ID
			 */
			int a = file.getExpertaid();
			int b = file.getExpertbid();

			/**
			 * 从专家委员中筛选出没有打过该案卷的所有专家委员
			 */
			List<ExpertUser> tempExpertList1 = new ArrayList<ExpertUser>();//地方专家临时容器
			List<ExpertUser> tempExpertList2 = new ArrayList<ExpertUser>();//司法专家临时容器
			//循环一遍专家
			for(ExpertUser exp:expCommitList){
				if(a==exp.getRelatedexpID()||b==exp.getRelatedexpID()){//回避A轮打过的案卷

				}else{
					if("1".equals(exp.getExpertclass())){//司法专家
						if(file.getBelongareacode().startsWith(exp.getAreacode().substring(0,2))){
						}else{
							tempExpertList2.add(exp);
						}

						//tempExpertList2.add(exp);
					}else{//地方专家
						if(file.getBelongareacode().startsWith(exp.getAreacode().substring(0,2))){//回避本省案卷

						}else{
							tempExpertList1.add(exp);
						}
					}
				}
			}

			/**
			 * 地方专家临时容器升序排列后,取案卷最少的1位专家
			 */
			Collections.sort(tempExpertList1, new Comparator<ExpertUser>(){//集合排序
		            /*
		             * int compare(Student o1, Student o2) 返回一个基本类型的整型，
		             * 返回负数表示：o1 小于o2，
		             * 返回0 表示：o1和o2相等，
		             * 返回正数表示：o1大于o2。
		             */
		            public int compare(ExpertUser o1, ExpertUser o2) {
		                //按照数量大小进行升序排列
		                if(o1.getFilenum() > o2.getFilenum()){
		                    return 1;
		                }
		                if(o1.getFilenum() == o2.getFilenum()){
		                    return 0;
		                }
		                return -1;
		            }
		        });
			ExpertUser commitB1= tempExpertList1.get(0);//人员1

			/**
			 * 司法专家临时容器升序排列后,取案卷最少的1位专家
			 */
			Collections.sort(tempExpertList2, new Comparator<ExpertUser>(){//集合排序
	            /*
	             * int compare(Student o1, Student o2) 返回一个基本类型的整型，
	             * 返回负数表示：o1 小于o2，
	             * 返回0 表示：o1和o2相等，
	             * 返回正数表示：o1大于o2。
	             */
	            public int compare(ExpertUser o1, ExpertUser o2) {
	                //按照数量大小进行升序排列
	                if(o1.getFilenum() > o2.getFilenum()){
	                    return 1;
	                }
	                if(o1.getFilenum() == o2.getFilenum()){
	                    return 0;
	                }
	                return -1;
	            }
	        });
			//获取专家1的行政区划,做同省规避
			String areacode = commitB1.getAreacode();
			ExpertUser commitB2 = null;

			//记录可选的专家B
			List<ExpertUser> commitB2List2 = new ArrayList<>();
			for (ExpertUser expertUser : tempExpertList2) {
				if (expertUser.getAreacode().startsWith(areacode.substring(0,2))){
				}else {
					//专家B
					commitB2List2.add(expertUser);
				}
			}
			Collections.sort(commitB2List2, Comparator.comparingInt(ExpertUser::getFilenum));

			//取前四个专家
			List<ExpertUser> commitB2List = new ArrayList<>();
			for (int i = 0; i < commitB2List2.size(); i++) {
				if(i>2){
					break;
				}
				commitB2List.add(commitB2List2.get(i));
			}

			Map<String, Integer> integerMap = userAMap.get(commitB1.getName());
			if (integerMap!=null){
				List<ExpertUser> unselectedExperts = new ArrayList<>();
				// 找出未被选中过的专家
				for (ExpertUser expert : commitB2List) {
					if (!integerMap.containsKey(expert.getName())) {
						unselectedExperts.add(expert);
					}
				}
				// 如果有未被选中过的专家，随机返回其中一个
				if (CollectionUtils.isNotEmpty(unselectedExperts)) {
					commitB2 = unselectedExperts.get(new Random().nextInt(unselectedExperts.size()));
					HashMap<String, Integer> userBnum = new HashMap<>();
					userBnum.put(commitB2.getName(), 1);
					userAMap.put(commitB1.getName(), userBnum);
				}else {
					// 所有专家都选过，找出选中次数最少的专家中的一个
					ExpertUser leastSelectedExpert = null;
					int minCount = Integer.MAX_VALUE;

					for (ExpertUser expert : commitB2List) {
						int count = integerMap.getOrDefault(expert.getName(), 0);
						if (count < minCount) {
							minCount = count;
							leastSelectedExpert = expert;
						}
					}
					commitB2 =  leastSelectedExpert;
					Integer numB = integerMap.get(commitB2.getName());
					integerMap.put(commitB2.getName(),numB+1);
					userAMap.put(commitB1.getName(), integerMap);

				}

			}else{
				commitB2 = commitB2List.get(new Random().nextInt(commitB2List.size()));
				HashMap<String, Integer> userBnum = new HashMap<>();
				userBnum.put(commitB2.getName(), 1);
				userAMap.put(commitB1.getName(), userBnum);
			}


			/**
			 * 被分配过案卷的专家，其案件数量都加1
			 */
			for(ExpertUser exp:expCommitList){
				if(exp.getId()==commitB1.getId()||exp.getId()==commitB2.getId()){
					exp.setFilenum(exp.getFilenum()+1);
				}
			}

			/**
			 * 更新专家委员与案卷的关联信息
			 */
			ExpertHandlFileList handel1 = new ExpertHandlFileList();
			handel1.setFileid(file.getId());
			handel1.setFilecode(file.getFilecode());
			handel1.setExpertid(commitB1.getId());
			handel1.setExpertname(commitB1.getName());
			handel1.setScoredstate("0");//0代表未评审状态
			handel1.setHandltype(1);//1代表B轮专家委员打分
			handleList.add(handel1);

			ExpertHandlFileList handel2 = new ExpertHandlFileList();
			handel2.setFileid(file.getId());
			handel2.setFilecode(file.getFilecode());
			handel2.setExpertid(commitB2.getId());
			handel2.setExpertname(commitB2.getName());
			handel2.setScoredstate("0");//0代表未评审状态
			handel2.setHandltype(1);//1代表B轮专家委员打分
			handleList.add(handel2);

			/**
			 * 更新案卷表中的案卷信息
			 */
			file.setExpertcommitaid(commitB1.getId());
			file.setExpertcommitaname(commitB1.getName());
			file.setExpertcommitbid(commitB2.getId());
			file.setExpertcommitbname(commitB2.getName());
			file.setIsconsiderexpcommit("0");//默认专家委员不需要合议
            updateFileList.add(file);

		}

		/**
		 * 批量更新专家用户的分配案卷数量
		 */
		expertUserMapper.setCommitExpertFileNum(expCommitList);
		int res = expertHandlFileMapper.insertBatchListCommit(handleList);//批量新增专家委员分配评审
//		int res = -1;
//		int res2 = -1;

		StringBuffer msg = new StringBuffer("0开始初始化;");
		if(res>-1){
			Date now =  new Date();
			DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm");
			msg.append(df.format(now)+">>1案卷初始化分配成功;");
			//批量更新案卷信息（回写专家评审、交叉评审人员信息）
			int res2 = filesMapper.updateFilesOfCommitByBatchList(updateFileList);
			if(res2>-1){
				msg.append("2案卷表初始专家委员信息成功;");
			}else{
				msg.append("2案卷表初始专家委员信息失败;");
			}
		}else{
			msg.append("1案卷初始化分配失败;");
		}
		return msg.toString();
	}



	/**
	 * 传递 Double类型返回精确的两位小数的Double类型
	 *
	 * @param dbl
	 * @return
	 */
	public static Double getTwoDecimalDouble(Double dbl) {
		return (Double) new BigDecimal(dbl).setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue();
	}
	/**
	 * 传递 Float类型返回精确的两位小数的Double类型
	 *
	 * @param float1
	 * @return
	 */
	public static double getTwoDecimalFloat(Float float1) {
		return (Double) new BigDecimal(float1).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
	}
	public static boolean getAccordWithTwenty(Double score) {
		if (score <= -15 || score >= 15)
			return false;
		else
			return true;
	}


	/**
	 * 初始委员合议案件给专家组长
	 */
	@Override
	public String groupLeaderMettingFiles() {
		//StringBuffer result = new StringBuffer("");
		String  result = "";
		try {
			//查询案卷表中所有非稽查案卷，且进入第二轮打分的所有案卷
			List<Files> filesList = filesMapper.getGroupLeaderFilesList();

			/**
			 * 查询所有的首席专家
			 */
			int count=0;
			ExpertUser search = new ExpertUser();
			search.setLevel(2);//首席专家只有一个
			List<ExpertUser> expCommitList = expertUserMapper.selectExpertCommitUser(search);

			List<ExpertHandlFileList> handleList = new ArrayList<ExpertHandlFileList>();//定义存放更新【案卷与专家用户的关联信息】的集合

			if(filesList !=null && filesList.size()>0){
				for(Files files :filesList){//开始循环
					if(ChangnengUtil.isNull(files.getIsconsiderexpcommit()) || "0".equals(files.getIsconsiderexpcommit()) ){//已经标记需要合议的除外

						if(ChangnengUtil.isNull(files.getExpertconsiderscore() )){//是否有最终分，有最终分不需要走此步

							//获取被专家委员打的两条分数
							List<ExpertHandlFileList> expertList = expertMapper.getExpertFileListByFileIdAndCommintId(files.getId(), files.getExpertcommitaid(), files.getExpertcommitbid());
							if(expertList!=null && expertList.size()>1){//专家委员打分必须有且只有两条信息
								FilesWithBLOBs record = new FilesWithBLOBs();
								record.setId(files.getId());

								// 保证两份案卷都被打分
								if(expertList.get(0).getExpertfinalscore()==null || expertList.get(1).getExpertfinalscore() ==null ){
									continue;
								}

								/**
								 * 把两个专家打的分数置入案卷表
								 */
								Double scoreExpertB1 = null, scoreExpertB2 = null;//专家委员B1B2打分
								Boolean isConsiderB = false;//是否需要首席专家合议的标识
								Map<Integer, Float> scoreExpert = new HashMap<Integer, Float>();//获得专家委员的分值，这里集合是无序的，所以要放map确定唯一标志

								if (expertList.get(0).getExpertfinalscore() != null) {//专家委员B1打完分
									scoreExpert.put(expertList.get(0).getExpertid(), expertList.get(0).getExpertfinalscore());
									scoreExpertB1 = BigDecimalUtil.getBigDecimalByFloat(expertList.get(0).getExpertfinalscore(), 2);
								}
								if (expertList.get(1).getExpertfinalscore() != null) {//专家委员B2打完分
									scoreExpert.put(expertList.get(1).getExpertid(), expertList.get(1).getExpertfinalscore());
									scoreExpertB2 = BigDecimalUtil.getBigDecimalByFloat(expertList.get(1).getExpertfinalscore(), 2);
								}
								record.setExpertcommitascore(scoreExpert.get(files.getExpertcommitaid()));
								record.setExpertcommitbscore(scoreExpert.get(files.getExpertcommitbid()));




								/**
								 * 根据两个专家分判断是否需要合议，同属置入标记
								 */
								ExpertHandlFileList handel1 = new ExpertHandlFileList();
								if (scoreExpertB1 != null && scoreExpertB2 != null) {
									Double expertfinalscoreAnJuan = getTwoDecimalDouble(scoreExpertB1 - scoreExpertB2);
									isConsiderB = getAccordWithTwenty(expertfinalscoreAnJuan);
									if (isConsiderB && (expertList.get(0).getExpertfinalscore() != 0 || expertList.get(1).getExpertfinalscore() != 0)) {//若相差不超过15分，即不需要合议，计算 平均值=（两分相加） /2并置入最终专家确认分
										BigDecimal bd1 = new BigDecimal(Double.toString(scoreExpertB1));
										BigDecimal bd2 = new BigDecimal(Double.toString(scoreExpertB2));
										record.setExpertconsiderscore((float) getTwoDecimalFloat((float)(bd1.add(bd2).doubleValue() /2.0)));

										FilesWithBLOBs f = filesMapper.selectByPrimaryKey(record.getId());
										Double rate="0".equals(f.getFiletype())?1.0:1.0;
										record.setExpertconsiderscore2((float) getTwoDecimalFloat((float)(bd1.add(bd2).doubleValue()*rate /2.0)));
										record.setIsconsiderexpcommit("0");

										String fileMaterials="1".equals(expertList.get(0).getScoredstate())?expertList.get(0).getFileMaterials():expertList.get(1).getFileMaterials();
										record.setFileMaterials(fileMaterials);

									}else{//需要合议的案件，置入合议标记，进入下一轮打分
										record.setIsconsiderexpcommit("1");//1代表合议状态，需要进行B轮评分
										handel1.setFileid(files.getId());
										handel1.setFilecode(files.getFilecode());
										handel1.setExpertid(expCommitList.get(0).getId());
										handel1.setExpertname(expCommitList.get(0).getName());
										handel1.setScoredstate("0");//0代表未评审状态
										handel1.setHandltype(2);//2代表首席专家打分
										handleList.add(handel1);

										count++;
									}

									/**
									 * 更新打分信息，经过合议初始化操作的，无论是否合议，分数不可再编辑
									 */
									ExpertHandlFileList  expertHandlFileList1  =  new ExpertHandlFileList();
									expertHandlFileList1.setId(expertList.get(0).getId());
									expertHandlFileList1.setConsiderState(1);
									expertMapper.updateByPrimaryKeySelective(expertHandlFileList1);

									ExpertHandlFileList  expertHandlFileList2  =  new ExpertHandlFileList();
									expertHandlFileList2.setId(expertList.get(1).getId());
									expertHandlFileList2.setConsiderState(1);
									expertMapper.updateByPrimaryKeySelective(expertHandlFileList2);


								}


								/**
								 * 更新案卷信息
								 */
								 filesMapper.updateByPrimaryKeySelective(record);
							}
						}
						//result.append("success提示：合议成功！*****<br/>");
					}
				}
				if(handleList.size()>0){
					int res = expertHandlFileMapper.insertBatchListCommit(handleList);//批量新增专家委员分配评审
					if(res>-1){
						Date now =  new Date();
						DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm");
						result= df.format(now)+">>>1案卷初始化分配成功;";
					}
				}

				result = result+"success提示："+count+"份案卷合议成功！*****<br/>";

			}else{
				result= result + "success提示：没有要待合议案卷*****<br/>";
			}
		} catch (Exception e) {
			e.printStackTrace();
			result = "error提示：合议失败！*****<br/>";
		}
		return result.toString();
	}


	@Override
	public String setFinalScoreForJichaFiles() {
		//StringBuffer result = new StringBuffer("");
		String  result = "";
		try {
			//查询案卷表中进入第二轮打分的稽查案卷
			List<Files> filesList = filesMapper.getFinalingJichaFilesList();//待置入最终分的稽查案卷

			int count=0;

			if(filesList !=null && filesList.size()>0){
				for(Files files :filesList){//开始循环
					if(ChangnengUtil.isNull(files.getIsconsiderexpcommit()) || "0".equals(files.getIsconsiderexpcommit()) ){//已经标记需要合议的除外

						if(ChangnengUtil.isNull(files.getExpertconsiderscore() )){//是否有最终分，有最终分不需要走此步

							//获取被专家委员打的分数
							List<ExpertHandlFileList> expertList = expertMapper.getExpertFileListByFileIdAndCommintId(files.getId(), files.getExpertcommitaid(), files.getExpertcommitbid());
							if(expertList!=null && expertList.size()==1){//稽查案卷专家委员打分有且只有1条信息
								FilesWithBLOBs record = new FilesWithBLOBs();
								record.setId(files.getId());

								// 保证两份案卷都被打分
								if(expertList.get(0).getExpertfinalscore()==null){
									continue;
								}

								/**
								 * 把专家打的分数置入案卷表
								 */

								if (expertList.get(0).getExpertfinalscore() != null) {//专家委员B1打完分
									record.setExpertcommitascore(expertList.get(0).getExpertfinalscore());
									record.setExpertconsiderscore(expertList.get(0).getExpertfinalscore());
									record.setIsconsiderexpcommit("0");
									count++;


									/**
									 * 更新案卷信息
									 */
									 filesMapper.updateByPrimaryKeySelective(record);



									/**
									 * 更新打分信息，经过合议初始化操作的，无论是否合议，分数不可再编辑
									 */
									ExpertHandlFileList  expertHandlFileList1  =  new ExpertHandlFileList();
									expertHandlFileList1.setId(expertList.get(0).getId());
									expertHandlFileList1.setConsiderState(1);
									expertMapper.updateByPrimaryKeySelective(expertHandlFileList1);
								}

							}
						}
					}
				}

				Date now =  new Date();
				DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm");
				result = df.format(now)+">>>>success提示："+count+"份稽查案卷置入最终分成功！*****<br/>";

			}else{
				Date now =  new Date();
				DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm");
				result= df.format(now)+">>>>success提示：没有要待置入最终分的稽查案卷*****<br/>";
			}
		} catch (Exception e) {
			e.printStackTrace();
			result = "error提示：稽查案卷置入最终分失败！*****<br/>";
		}
		return result.toString();
	}


	@Override
	public int reUpdateSysInitConfigByCodeAndActionType(Integer code, String actionType, String result) {
		return sysInitMapper.reUpdateSysInitConfigByCodeAndActionType(code, actionType, result);
	}

	@Override
	public String initCrossInChick() {
		String str = "";
		try {
		 int total = filesMapper.updateCrossInChick();
		 str = "共有需要评查的案卷"+total+"份！";
		} catch (Exception e) {
			e.printStackTrace();
			str = "error:执行错误！更新失败！";
		}
		return str;
	}

	/**
	 * 根据查询把已知目录下的附近上传fastDfs并回写fileurl
	 * @return
	 */
	@Override
	public JsonResult setAddFilesUrl() {
		JsonResult jsonResult = new JsonResult();

		List<TempAddFiles> filesList = tempAddFileMapper.getTempAddFiles();
		try {
			FastDFSClient fastDFSClient = new FastDFSClient("classpath:config/fdfs_client.conf");
			for (int i = 0; i < filesList.size(); i++) {
				TempAddFiles files = filesList.get(i);
				if (files.getAnjuanhao() == null||"".equals(files.getAnjuanhao())||files.getFujianName() == null||"".equals(files.getFujianName()))
					continue;
				String filePath = Const.XZCF_HTTPURL + files.getProStr()+"/"+files.getFujianName();
				File file=new File(filePath);
				if(file.exists()){//若文件存在
					String dfsUrl = fastDFSClient.uploadFile(filePath);
					if(!"".equals(dfsUrl)&&dfsUrl!=null){//上传成功后返回url
						String file_name = files.getProStr()+"=|="+files.getAnjuanhao()+"=&="+files.getFujianName();
						sysLogMapper.insertFastDFS_Log(file_name, dfsUrl);//记录上传日志

						TempAddFiles update_file = new TempAddFiles();
						update_file.setId(files.getId());
						update_file.setFileUrl(dfsUrl);
						tempAddFileMapper.updateByPrimaryKeySelective(update_file);
	                }
				}
			}
			jsonResult.setResult(Const.RESULT_SUCCESS);
			jsonResult.setMessage("导入数据成功");
		} catch (Exception e) {
			e.printStackTrace();
			jsonResult.setResult(Const.RESULT_ERROR);
			jsonResult.setMessage("导入数据失败");

		}

		return jsonResult;
	}

	@Override
	public String setInCheckCrossABScore2017() {
		String str = "";
		try {
		 filesMapper.updateCrossInChick();//防止前边未执行，在此处再次执行
		 int totalA = filesMapper.setInCheckCrossAScore();
		 int totalB = filesMapper.setInCheckCrossBScore();
		 str = "执行成功:交叉A的案卷数："+totalA+"；交叉B的案卷数："+totalB;
		} catch (Exception e) {
			e.printStackTrace();
			str = "error:执行错误！更新失败！";
		}
		return str;
	}

	@Override
	public String setInCheckCrossFinalScore2017() {
		String str = "";
		List<Files> filesList = filesMapper.getInCheckFilesList();
		int resT = filesMapper.saveOriginalScore();
		int Count=0;
		if(resT>-1){
			for(Files files :filesList){
				Double scoreCrossA = null, scoreCrossB = null;
				scoreCrossA = BigDecimalUtil.getBigDecimalByFloat(files.getInCheckCrossAScore(), 2);
				scoreCrossB = BigDecimalUtil.getBigDecimalByFloat(files.getInCheckCrossBScore(), 2);
				BigDecimal bd1 = new BigDecimal(Double.toString(scoreCrossA));
				BigDecimal bd2 = new BigDecimal(Double.toString(scoreCrossB));

				FilesWithBLOBs record = new FilesWithBLOBs();
				record.setId(files.getId());


				double res = bd1.add(bd2).doubleValue() /2.0;
				DecimalFormat df = new DecimalFormat("#.##");
				df.setRoundingMode(RoundingMode.HALF_UP);
				String testStr = df.format(res);

				res = Double.valueOf(testStr);

				record.setCrossconsiderscore((float)res);
				record.setFinalscore((float)res);

				filesMapper.updateByPrimaryKeySelective(record);
				Count++;
			}
			str = "计分成功：共有"+Count+"份";
		}else{
			str = "error:执行失败：保存原始数据失败！";
		}

		return str;
	}

	@Override
	public String setFileCrossScoreAndExperScore2017() {
		String str = "";
		try {
		 int totalCross = filesMapper.setCrossFinalScoreForAddfiles();
		 int totalExpert = filesMapper.setExpertFinalScoreForAddfiles();
		 str = "执行成功:交叉分扣20分的案卷数："+totalCross+"；专家分扣20分的案卷数："+totalExpert;
		} catch (Exception e) {
			e.printStackTrace();
			str = "error:执行错误！更新失败！";
		}
		return str;
	}

	@Override
	public String setFileFinalScore2017() {
		List<Files> filesList = filesMapper.selectAllFiles();

		int Count=0;
		for(Files files :filesList){
			FilesWithBLOBs record = new FilesWithBLOBs();
			record.setId(files.getId());

			//Float crossScore = files.getCrossconsiderscore();
			Float expertScore = files.getExpertconsiderscore();
			if(expertScore == null||files.getExpertaid()==null ||files.getExpertbid()==null){
				record.setFinalscore(files.getCrossconsiderscore());
			}else{
				Float finalScore = files.getFinalscore();

				DecimalFormat df = new DecimalFormat("#.##");
				df.setRoundingMode(RoundingMode.HALF_UP);

				String testStr = df.format(finalScore);
				double res = Double.valueOf(testStr);

				record.setFinalscore((float)res);
			}

			int a= filesMapper.updateByPrimaryKeySelective(record);
			if(a>0){
				Count++;
			}

		}

		return "计分成功：共重置"+Count+"份";
	}

	@Override
	public String downloadUnitFiles2017() {
		List<ElectionUnit> units = electionUnitMapper.selectUnitLists();
		int count = 0;
		for(ElectionUnit unit:units){
			if(unit.getTeamMaterialURL()!=null&&!"".equals(unit.getTeamMaterialURL())){
				try {
					DownloadFilesUrl.downLoadFromUrl("http://10.87.10.83:8089/"+unit.getTeamMaterialURL(), unit.getTeamMaterialName(), "/usr/local/UnitFile");
					count++;
				} catch (Exception e) {
					System.out.println("集体走到"+count+"异常停止");
					e.printStackTrace();
					return "error:集体下载失败了";
				}
			}
		}
		return "集体下载了"+count+"个文件";
	}

	@Override
	public String downloadPersonFiles2018() {
		//个人案卷批量下载   外层循环人  里层循环个人下的案卷
		int count = 0;
		try {
		//查询所有个人  crossUser表
		List<CrossUser> crossUserList=crossUserMapper.getCrossUser();
		    //循环个人
			for(CrossUser crossUser:crossUserList){
				//根据个人查询对应的案卷s
				List<Files> files=filesMapper.getFilesByCrossUserID(crossUser.getId());
				//便利案卷
				for(Files file:files){
					if(file.getFileurl()!=null && !"".equals(file.getFileurl())){
						String name = file.getFilename();
						String subName = "";
						if(name!=null && "".equals(name)){
							subName=name.substring(name.lastIndexOf("."));
						}
						DownloadFilesUrl.downLoadFromUrl("http://192.168.32.206:8089/"+file.getFileurl(), file.getFilecode()+subName, "/usr/local/"+crossUser.getLoginname());
						count++;
					}
				}
			}
		} catch (Exception e) {
			System.out.println("集体走到"+count+"异常停止");
			e.printStackTrace();
			return "error:集体下载失败了";
		}
		return "集体下载了"+count+"个文件";
	}


	@Override
	public String downloadUnitPhotos2018() {
		int count = 0;
//		try {
//		//查询所有集体照片  表
//		List<provinceSelectionUnit> proUnitList=provinceSelectionUnitMapper.getProUnit();
//				//便利案卷
//				for(provinceSelectionUnit proUnit:proUnitList){
//					if(proUnit.getDeedpictureurl()!=null && !"".equals(proUnit.getDeedpictureurl())){
//						DownloadFilesUrl.downLoadFromUrl("http://10.87.10.83:8089/"+proUnit.getDeedpictureurl(), proUnit.getProvincePro(), "/usr/local/unitPhoto");
//						count++;
//					}
//				}
//		} catch (Exception e) {
//			System.out.println("集体走到"+count+"异常停止");
//			e.printStackTrace();
//			return "error:集体下载失败了";
//		}
		return "集体下载了"+count+"个文件";
	}


	@Override
	public String downloadPersonPhotos2018() {
		int count = 0;
		try {
		//查询所有个人照片  表
		List<ProvinceElectionPersonal> proPersonList=provinceElectionPersonalMapper.getPersonPhotolList();
				//便利个人
				for(ProvinceElectionPersonal proElectionPersonal:proPersonList){
					if(proElectionPersonal.getPersonalphotouploadurlPro()!=null && !"".equals(proElectionPersonal.getPersonalphotouploadurlPro())){
						DownloadFilesUrl.downLoadFromUrl("http://10.87.10.83:8089/"+proElectionPersonal.getPersonalphotouploadurlPro(), proElectionPersonal.getProvincePro(),  "/usr/local/personPhoto");
						count++;
					}
				}
		} catch (Exception e) {
			System.out.println("个人走到"+count+"异常停止");
			e.printStackTrace();
			return "error:个人下载失败了";
		}
		return "个人下载了"+count+"个文件";
	}


	@Override
	public String randomSetCrossScore2018() {
		StringBuffer msg= new StringBuffer("");
		int countOne=provinceElectionPersonalMapper.randomSetCrossScoreOne();
		if(countOne>-1){
			int countTwo=provinceElectionPersonalMapper.randomSetCrossScoreTwo();
			if(countTwo>-1){
				int countThree_XZCF=provinceElectionPersonalMapper.randomSetCrossScoreThree_XZCF();
				int countThree_ARJF=provinceElectionPersonalMapper.randomSetCrossScoreThree_ARJF();
				int countThree_XCTC=provinceElectionPersonalMapper.randomSetCrossScoreThree_XCTC();
				if(countThree_XZCF>-1 && countThree_ARJF>-1 && countThree_XCTC>-1){
					msg.append("执行成功ok");
				}else if(countThree_XZCF<0){
					msg.append("行政处罚执行失败3.1||");
				}else if(countThree_ARJF<0){
					msg.append("按日计罚执行失败3.2||");
				}else if(countThree_XCTC<0){
					msg.append("限产停产执行失败3.3||");
				}else{
					msg.append("第三部执行失败3.0||");
				}
			}else{
				msg.append("第二步执行失败2||");
			}
		}else{
			msg.append("第一步执行失败1||");
		}
		return msg.toString();
	}

	@Override
	public String downloadPersonalFiles2017() {
		List<ElectionPersonal> personls = electionPersonalMapper.getPersonalList();
		int count = 0;
		for(ElectionPersonal person:personls){
			if(person.getPerhonestfileurl()!=null&&!"".equals(person.getPerhonestfileurl())){
				try {
					DownloadFilesUrl.downLoadFromUrl("http://219.143.244.190:8089/"+person.getPerhonestfileurl(), person.getPerhonestfilename(), "D:/PersonLZZFCL");
					count++;
				} catch (IOException e) {
					System.out.println("个人廉洁执法材料走到"+count+"异常停止");
					e.printStackTrace();
					return "error:个人廉洁执法材料下载失败了";
				}
			}
		}
		return "个人下载了"+count+"个廉洁执法材料文件";
	}

	@Override
	public String downloadPersonalFiles_xjsj2017() {
		List<ElectionPersonal> personls = electionPersonalMapper.getPersonalList();
		int count = 0;
		for(ElectionPersonal person:personls){
			if(person.getPersonalmaterialurl()!=null&&!"".equals(person.getPersonalmaterialname())){
				try {
					/*DownloadFilesUrl.downLoadFromUrl("http://192.168.32.206:8089/"+person.getPersonalmaterialurl(), person.getPersonalmaterialname(), "/usr/local/PerFile_XJSJ");*/
					DownloadFilesUrl.downLoadFromUrl("http://10.87.10.83:8089/"+person.getPersonalmaterialurl(), person.getPersonalmaterialname(), "/usr/local/PerFile_XJSJ");
					count++;
				} catch (IOException e) {
					System.out.println("个人先进事迹材料走到"+count+"异常停止");
					e.printStackTrace();
					return "error:先进事迹材料下载失败了";
				}
			}
		}
		return "个人下载了"+count+"个先进事迹材料文件";
	}

	@Override
	public String baseDataCount() {
//		List<ProvinceCountBean> provinceList = provinceUserMapper.getProvinceCountBean();
//
//		for (int i = 0; i < provinceList.size(); i++) {
//			ProvinceCountBean provinceCountBean = provinceList.get(i);
//			//全省环境违法案件数量
//			String ids = "'0','1','2','3','6','7'";
//			int provincecasenum = provinceUserMapper.getFileCount(provinceCountBean.getAreaCode_pro().substring(0, 2),provinceCountBean.getStart_ActivityTime(),provinceCountBean.getEnd_ActivityTime(),ids);
//
//			//全省罚款金额
//			Float provincefine = provinceUserMapper.getMoneyCount(provinceCountBean.getAreaCode_pro().substring(0, 2),provinceCountBean.getStart_ActivityTime(),provinceCountBean.getEnd_ActivityTime(),"'0','1'");
//
//			//全省重大案件数量
//			ids = "'1','2','3','6','7'";
//			int provincemajorcasenum = provinceUserMapper.getFileCount(provinceCountBean.getAreaCode_pro().substring(0, 2),provinceCountBean.getStart_ActivityTime(),provinceCountBean.getEnd_ActivityTime(),ids);
//
//			//更新省的数据
//			provinceSelectionUnit provinceSelectionUnit = provinceSelectionUnitMapper.selectByAreaCode(provinceCountBean.getAreaCode_pro());
//			provinceSelectionUnit.setAreacodePro(provinceCountBean.getAreaCode_pro());
////			provinceSelectionUnit.setProvincecasenum(provincecasenum);
////			provinceSelectionUnit.setProvincefine(provincefine);
////			provinceSelectionUnit.setProvincemajorcasenum(provincemajorcasenum);
//			provinceSelectionUnitMapper.updateByAreacode(provinceSelectionUnit);
//
//			//计算全省基准比例
////			int Provincepolluternumber = provinceSelectionUnit.getProvincepolluternumber();//全省重大排污单位数量
//			String subString = provinceCountBean.getAreaCode_pro().substring(0,2);
//			provinceSelectionUnitMapper.calculateBaseRatio(subString,Provincepolluternumber);
//
//			//计算三项基准值
//			provinceSelectionUnitMapper.calculateBaseDate(provincecasenum,provincemajorcasenum,provincefine,subString);
//		}

		return "行政区基础数据统计成功";
	}

	@Override
	public String setGjDlb_unit() {
		try{
			int res= sysInitMapper.batchInsertUnit();
			return "成功同步"+res+"个单位参与国家大练兵";
		}catch(Exception e){
			e.printStackTrace();
			return "后台异常，参选单位同步失败";
		}

	}

	@Override
	public String setGjDlb_person() {
		try{
			int res = sysInitMapper.batchInsertPerson();
			return "成功同步"+res+"个个人参与国家大练兵";
		}catch(Exception e){
			e.printStackTrace();
			return "后台异常，参选个人同步失败";
		}
	}

	@Override
	public String setGjDlb_file () {
//		StringBuffer result = new StringBuffer("");
//		try{
//			int res1 = sysInitMapper.batchInsertFiles_unit();
//			int res2 = -1;
//			int res3 = -1;
//			int res4 = -1;
//			int res5 = -1;
//			if(res1>-1){
//				result.append("【1】同步参选单位案卷(含与个人共用的)"+res1+"份");
//				res2 = sysInitMapper.batchInsertFiles_person();
//				if(res2>-1){
//					result.append("【2】同步参选个人案卷"+res2+"份");
//					res3 = sysInitMapper.batchInsertPerReFiles();
//					if(res3>-1){
//						result.append("【3】同步参选个人与案卷关联表"+res3+"条");
//						res4 = sysInitMapper.updatePerFile_personID();
//						if(res4>-1){
//							result.append("【4】个人与案卷关联表personID更新成功"+res4+"条");
//							res5 = sysInitMapper.updatePerFile_fileID();
//							if(res5>-1){
//								result.append("【5】个人与案卷关联表fileID更新成功"+res5+"条");
//							}
//						}
//					}
//				}
//			}
//
//			if(res1>-1&&res2>-1&&res3>-1&&res4>-1&&res5>-1){
//				return "成功！！！"+result.toString();
//			}else{
//				return "失败！！！"+result.toString();
//			}
//
//		}catch(Exception e){
//			e.printStackTrace();
//			return "后台异常，案卷同步失败";
//		}

		 String baseUrl = "http://210.12.80.51:8091/"; //远程url地址
		// String localBasePath = "D:/dd/ttt"; //本地远程地址
		List<Files> list = filesMapper.getIsPs();
		if (list != null) {
			logger.info("总共获取数据：" + list.size() + "条");
			for (Files p : list) {
				//信息判断
//                    if (StringUtils.isBlank(p.getProvincePro())) {
//
//                       throw new Exception("主键为" + p.getId() + "，省市县的信息不全");
//                    }
				//1.1省级大练兵组织Excel
				if (StringUtils.isNotBlank(p.getFileurl()) && StringUtils.isNotBlank(p.getFileurl())) {
					try {
						int index = p.getFilename().indexOf(".");
						String result = p.getFilename().substring(index );

						downLoadFromUrl(baseUrl + p.getFileurl(), p.getFilecode()+result, formatLocalPath("个人案卷"));
					} catch (IOException e) {
						System.out.println(e.getMessage());
//						throw new RuntimeException(e);
					}
				}
			}
		}
		return "dasd";
	}

	public static String formatLocalPath(String path) {
		return "E:/dd/new" + "/" + path + "/";
	}

	/**
	 * 从网络Url中下载文件
	 *
	 * @param urlStr
	 * @param fileName
	 * @param savePath
	 * @throws IOException
	 */
	public static void downLoadFromUrl(String urlStr, String fileName, String savePath) throws IOException {
		URL url = new URL(urlStr);
		HttpURLConnection conn = (HttpURLConnection) url.openConnection();
		//设置超时间为3秒
		conn.setConnectTimeout(30 * 1000);
		//防止屏蔽程序抓取而返回403错误
		conn.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");

		//文件保存位置
		File saveDir = new File(savePath);
		if (!saveDir.exists()) {
			saveDir.mkdir();
		}
		File file = new File(saveDir + File.separator + fileName);
		if(file.exists()){
			System.out.println("文件已存在 跳过---"+fileName+"---info:" + url + " download success");
			return;
		}else {
			//得到输入流
			InputStream inputStream = conn.getInputStream();
			//获取自己数组
			byte[] getData = readInputStream(inputStream);

			FileOutputStream fos = new FileOutputStream(file);
			fos.write(getData);

			System.out.println(fileName+"--info:" + url + " download success");

			if (fos != null) {
				fos.close();
			}
			if (inputStream != null) {
				inputStream.close();
			}

		}
	}


	@Override
	public String tczz_initFenPei() {
		StringBuffer message = new StringBuffer("");
		List<zhpgUserRelunitList> handleList = new ArrayList<zhpgUserRelunitList>();
		List<zhpgExtrudeScore> zhpg_unitList = new ArrayList<zhpgExtrudeScore>();

		//从参选单位表中获取32个省
		List<ElectionUnit> provinceUnitList = electionUnitMapper.selectUnitList("1");

		//根据获取交叉评审突出组织的用户人员
		ExpertUser searchExp = new ExpertUser();
		searchExp.setExpertclass("unit");//
		searchExp.setLevel(6);//

		//List<CrossUser> crossList = crossUserMapper.selectCrossUserByAreaListParaByType2018();
		List<ExpertUser> expertList = expertUserMapper.selectGeneralExpertUserByPara(searchExp);//查询优秀组织评审用户

		List<ElectionUnit> unit1 = new ArrayList<ElectionUnit>();
		List<ElectionUnit> unit2 = new ArrayList<ElectionUnit>();
		if(provinceUnitList.size()>0){
			List<List<ElectionUnit>> provinceArray = averageAssign(provinceUnitList,2);
			if(provinceArray.size()>1){
				unit1 = provinceArray.get(0);
				unit2 = provinceArray.get(1);
			}

			if(unit1.size()>0){
				for(ElectionUnit unit:unit1){
					ExpertUser expA = expertList.get(0);
					ExpertUser expB = expertList.get(2);

					zhpgUserRelunitList hand1 = new zhpgUserRelunitList();
					zhpgUserRelunitList hand2 = new zhpgUserRelunitList();

					zhpgExtrudeScore unitAdd = new zhpgExtrudeScore();


					hand1.setUnitid(unit.getId());
					hand1.setUnitarea(unit.getAreacode());
					hand1.setUserid(expA.getId());
					handleList.add(hand1);

					hand2.setUnitid(unit.getId());
					hand2.setUnitarea(unit.getAreacode());
					hand2.setUserid(expB.getId());
					handleList.add(hand2);

					unitAdd.setCrossuserid(expA.getId());
					unitAdd.setNamea(expA.getName());
					unitAdd.setCrossuserBid(expB.getId());
					unitAdd.setNameb(expB.getName());
					unitAdd.setUnitid(unit.getId());
					unitAdd.setUnitname(unit.getAreaname());
					unitAdd.setAreacode(unit.getAreacode());
					zhpg_unitList.add(unitAdd);
				}
				message.append("【1和3用户分配"+unit1.size()+"个省；】");
			}
			if(unit2.size()>0){
				for(ElectionUnit unit:unit2){
					ExpertUser expA = expertList.get(1);
					ExpertUser expB = expertList.get(3);

					zhpgUserRelunitList hand1 = new zhpgUserRelunitList();
					zhpgUserRelunitList hand2 = new zhpgUserRelunitList();

					zhpgExtrudeScore unitAdd1 = new zhpgExtrudeScore();

					hand1.setUnitid(unit.getId());
					hand1.setUnitarea(unit.getAreacode());
					hand1.setUserid(expA.getId());
					handleList.add(hand1);

					hand2.setUnitid(unit.getId());
					hand2.setUnitarea(unit.getAreacode());
					hand2.setUserid(expB.getId());
					handleList.add(hand2);

					unitAdd1.setCrossuserid(expA.getId());
					unitAdd1.setNamea(expA.getName());
					unitAdd1.setCrossuserBid(expB.getId());
					unitAdd1.setNameb(expB.getName());
					unitAdd1.setUnitid(unit.getId());
					unitAdd1.setUnitname(unit.getAreaname());
					unitAdd1.setAreacode(unit.getAreacode());
					zhpg_unitList.add(unitAdd1);
				}
				message.append("【2和4用户分配"+unit2.size()+"个省；】");
			}
			if(handleList.size()>0){
				int res = zhpg_UserRelunitListMapper.insertBatchList(handleList);
				if(res>-1){
					int re = zhpg_ExtrudeScoreMapper.insertBatchList(zhpg_unitList);;
					if(re>-1){
						message.append("【分配成功】");
					}else{
						message.append("【评估表未置入分数】");
					}
				}else{
					message.append("【分配成功一半】");
				}
			}else{
				message.append("【分配失败】");
			}

			return message.toString();
		}else{
			message.append("【没有参选省份】");
			return message.toString();
		}
	}

	@Override
	public String tczz_initHeYi() {
		StringBuffer resStr= new StringBuffer("");
		//获得到所有【未交叉合议评审】的案卷信息
		List<zhpgExtrudeScore> wqrList = zhpg_ExtrudeScoreMapper.selectNoConsiderList();
		int no =0;//不满足合议条件的
		int count =0;
		for (int i = 0; i < wqrList.size(); i++) {
			zhpgExtrudeScore zhpg_unitScore = wqrList.get(i);

			zhpgExtrudeScore unit = new zhpgExtrudeScore();
			unit.setId(zhpg_unitScore.getId());
			Float scoreA = zhpg_unitScore.getNameaScore();
			Float scoreB = zhpg_unitScore.getNamebScore();
			if(scoreA!=null&&scoreB!=null){
				int unitID = zhpg_unitScore.getUnitid();
				boolean flag = false;//是否需要合议的标记，默认不需要合议
				List<Map<String,Object>> diffList = zhpg_ExtrudeScoreMapper.selectDifferScore(unitID);
				if(diffList.size()>0){
					for(Map<String,Object> diffObj:diffList){
						double diffScore = getTwoDouble(Double.parseDouble(diffObj.get("differ").toString()));
						double score = Double.parseDouble(diffObj.get("standScore").toString());
						System.out.println("<<标准分的20%>>"+score+"<<<指标差>>>"+diffScore);
						if(diffScore>=score){
							flag = true;
							break;
						}
					}
					if(flag){//需要合议
						count = count + 1;
						//置需要合议标记
						unit.setIsheyi(1);
					}else{
						unit.setIsheyi(0);
						//计算平均分
						unit.setPrefourscore((float) getTwoDouble(((getTwoDecimalFloat(scoreA) + getTwoDecimalFloat(scoreB)) / 2)));
					}
					int res = zhpg_ExtrudeScoreMapper.updateByPrimaryKeySelective(unit);
					if(res>-1){
						zhpg_ExtrudeScoreMapper.setIsCosider(unitID);
					}
				}
			}else{
				no = no+1;
			}

		}
		resStr.append("【尚未满足合议条件】的有"+no+"个省；");
		resStr.append("【满足且需要合议】的有"+count+"个省；");
		return resStr.toString();
	}

	/**
	 * 计算最终得分保留2位小数
	 *
	 * @param dbl
	 * @return
	 */
	public static double getTwoDouble(Double dbl) {
		return (double) new BigDecimal(dbl).setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue();
	}

	public static <T> List<List<T>> averageAssign(List<T> source, int n) {
		List<List<T>> result = new ArrayList<List<T>>();
		int remaider = source.size() % n; //(先计算出余数)
		int number = source.size() / n; //然后是商
		int offset = 0;//偏移量
		for (int i = 0; i < n; i++) {
			List<T> value = null;
			if (remaider > 0) {
				value = source.subList(i * number + offset, (i + 1) * number + offset + 1);
				remaider--; offset++;
			} else {
				value = source.subList(i * number + offset, (i + 1) * number + offset);
			}
			result.add(value);
		}
		return result;
	}

	@Override
	public String initDistrFiles() {
		//需要合议的案卷
		List<Files> fileList = filesMapper.getFilesNeedConsider();
		//交叉合议组成员
		List<SysUser> userList = sysUserMapper.getCrossAdmin();

		List<SysUser> userList_copy = new ArrayList<SysUser>(userList);
		try{
		for(Files file:fileList){
			FilesWithBLOBs add_file = new FilesWithBLOBs();
			add_file.setId(file.getId());

			SysUser user = new SysUser();

			if(userList_copy.size()>0){
				user = userList_copy.remove(new Random().nextInt(userList_copy.size()));
			}else{
				userList_copy.addAll(userList);
				user = userList_copy.remove(new Random().nextInt(userList_copy.size()));
			}
			add_file.setRecordCrossUser(user.getLoginname());
			filesMapper.updateByPrimaryKeySelective(add_file);
		}
		}catch(Exception e){
			e.printStackTrace();
			return "后台异常,初始化失败";
		}
		return "交叉合议组案卷于"+DateUtil.getDateTime("yyyy-MM-dd hh:mm:ss")+"分配成功";
	}

	@Override
	public void getLoginUserByPhone(LoginUser record) throws Exception {
		if (record != null) {
			record.setFlag(false);//登陆成功的标识

			String phone = record.getLoginPhone();
			String captCode = record.getCaptCode();

			if(stringRedisTemplate.opsForValue().get(phone).equals(captCode)){//登录成功

				PhoneRUser phoneUser = phoneRUserMapper.selectByPhone(phone);
				if(phoneUser!=null){//手机用户关联表中已存在
					record.setFlag(true);
					//此处从手机用户关联表获取用户类型userType
					String userTypeCode =phoneUser.getUsertype();

					if (userTypeCode.equals("1")) {

						//通过手机号获取==省级登录用户
						ProvinceReportUser user = provinceUserMapper.selectProUserByPhone(phone);

						if (user != null) {
							String reportState = ""+user.getReportstate();
							if("null".equals(reportState)||"".equals(reportState)){
								reportState = provinceUserMapper.selectReportByAreaCode(user.getAreacode().substring(0, 2)+"000000");
							}

							record.setId(user.getId());
							record.setLoginid(user.getLoginname());
							record.setIsreg(Const.YES);
							record.setAreaCode(user.getAreacode());
							record.setUserName(user.getName());
							record.setArealevel(user.getAreatype());
							if(reportState!=null){
								record.setReportState(reportState);
							}

							record.setUserType("填报用户");
							record.setUserTypeCode(userTypeCode);
						}
					} else if (userTypeCode.equals("2")) {
						//通过手机号获取==专家评审用户
						ExpertUser user = expertUserMapper.selectExpertUserByPhone(phone);
						if (user != null) {
							record.setId(user.getId());
							record.setLoginid(user.getLoginname());
							record.setIsreg(Const.YES);
							record.setUserName(user.getName());
							record.setFlag(true);
							record.setUserType("专家评审用户");
							record.setUserTypeCode(userTypeCode);
							record.setExpertType(user.getType());
							record.setLevel(user.getLevel());
						}
					} else if (userTypeCode.equals("3")) {
						//通过手机号获取==交叉评审用户
						CrossUser user = crossUserMapper.selectCrossUserByPhone(phone);
						if (user != null) {
							record.setId(user.getId());
							record.setLoginid(user.getLoginname());
							record.setAreaCode(user.getAreacode());
							record.setUserName(user.getName());
							record.setUserType("交叉评审用户");
							record.setUserTypeCode(userTypeCode);
							record.setCrossType(user.getCrossType());
						}
					} /*else if(userTypeCode.equals("5")){
						//通过手机号获取==个人用户
						PersonalReport user = personalReportMapper.selectPersonUserByPhone(phone);
						if(user != null){
							record.setId(user.getId());
							record.setLoginid(user.getLoginname());
							record.setAreaCode(user.getAreacode());
							record.setUserName(user.getName());
							record.setUserType("个人用户");
							record.setCardID(user.getCardid());
							record.setAreaType(user.getAreatype());
							record.setProvincecode(user.getProvincecode());
						}
					}*/else{
						//通过手机号获取==系统管理
						SysUser user = sysUserMapper.selectSystemUserByPhone(phone);
						if (user != null) {
							record.setId(user.getId());
							record.setLoginid(user.getLoginname());
							record.setIsreg(Const.YES);
							record.setUserName(user.getName());
							record.setLoginid(user.getLoginname());
							record.setUserType("系统管理员");
							record.setUserTypeCode(userTypeCode);
						}
					}
				}

			}




		}

	}

	@Override
	public String initDistributeFileOfEpertForJiCha() throws Exception {
		try {
			//查询所有的稽查案卷
			List<Files> fileList = filesMapper.selectJiChaFiles();

			ExpertUser expertAseach = new ExpertUser();
			expertAseach.setType("5");//稽查类型
			expertAseach.setExpertclass("0");//地方专家
			// 当前类型的专家
			List<ExpertUser> expertList= expertUserMapper.selectGeneralExpertUserByPara2019(expertAseach);

			List<ExpertHandlFileList> handleList = new ArrayList<ExpertHandlFileList>();
			List<Files> updateFileList = new ArrayList<Files>();
			int fileNum = 0;//每个人大概的平均案卷数量
			if (fileList.size() > 0 && expertList.size() > 0) {
				fileNum = (fileList.size() * 2) % expertList.size() > 0 ? (fileList.size() * 2) / expertList.size() + 1 : (fileList.size() * 2) / expertList.size();

				for (Files file : fileList) {
					ExpertHandlFileList handel1 = new ExpertHandlFileList();
					ExpertHandlFileList handel2 = new ExpertHandlFileList();

					//存放该案卷分配的人员
					List<ExpertUser> f_users = new ArrayList<ExpertUser>();

					List<ExpertUser> copy = new ArrayList<ExpertUser>();
					copy.addAll(expertList);
					while (f_users.size() < 2) {//每1个案卷至到分配到两个合适的用户为止

						ExpertUser expert = copy.remove(new Random().nextInt(copy.size()));
						if (!file.getAreacode().equals(expert.getAreacode()) && expert.getFilenum() < fileNum-1) {//优先走分配数量小于正常数量2个的用户
							f_users.add(expert);
						}

						if(copy.size()==0 &&f_users.size()<2){//前一轮用户全部被分配完毕，再重新复制一份所有用户
							copy.addAll(expertList);
							ExpertUser expert1 = copy.remove(new Random().nextInt(copy.size()));

							if (!file.getAreacode().equals(expert1.getAreacode()) && expert1.getFilenum() < fileNum) {//然后再分配小于平均数量的用户
								f_users.add(expert1);
							}

						}
					}

					if(f_users.get(0).getId()==f_users.get(1).getId()){//如果存在1个案卷分给1个人分了两次的情况，做特殊处理
						f_users.remove(1);
						List<ExpertUser> copy2 = new ArrayList<ExpertUser>();
						copy2.addAll(expertList);
						while(f_users.size()<2){
							ExpertUser u = copy2.remove(new Random().nextInt(copy2.size()));
							if(!file.getAreacode().equals(u.getAreacode())){
								if(u.getFilenum()<fileNum&&f_users.get(0).getId()!=u.getId()){
									f_users.add(u);
								}
							}
						}
					}

					for (ExpertUser user : f_users) {
						for (ExpertUser expUser : expertList) {
							if (expUser.getId() == user.getId()) {
								expUser.setFilenum(expUser.getFilenum() + 1);//被分配的用户，案件数量会加1
							}
						}
					}

					handel1.setFileid(file.getId());
					handel1.setFilecode(file.getFilecode());
					handel1.setExpertid(f_users.get(0).getId());
					handel1.setExpertname(f_users.get(0).getName());
					handel1.setScoredstate("0");
					handel1.setHandltype(0);
					handleList.add(handel1);

					handel2.setFileid(file.getId());
					handel2.setFilecode(file.getFilecode());
					handel2.setExpertid(f_users.get(1).getId());
					handel1.setExpertname(f_users.get(1).getName());
					handel2.setScoredstate("0");
					handel2.setHandltype(0);
					handleList.add(handel2);


					//回写案卷信息表中：2位交叉评审人员信息
					file.setExpertaid(f_users.get(0).getId());
					file.setExpertaname(f_users.get(0).getName());
					file.setExpertbid(f_users.get(1).getId());
					file.setExpertbname(f_users.get(1).getName());
					updateFileList.add(file);
				}
			}else{
				return "案卷或人员信息异常：案卷"+fileList.size()+"份,交叉人员"+expertList.size()+"个";
			}
			// 批量新增交叉评审
			expertHandlFileMapper.insertBatchList(handleList);
			//批量更新案卷信息（回写专家评审、交叉评审人员信息）
			filesMapper.updateFilesOfExpertByBatchList(updateFileList);

			return "交叉人员案卷分配完毕,案卷"+fileList.size()+"*2份,交叉人员"+expertList.size()+"个,每个交叉专家分配约"+fileNum+"(+_1)份案卷";
		}catch(Exception e){
			return "后台出现未知异常，执行失败";
		}
	}

	@Override
	public String initFileListToExpertCommitForJiCha() {
		//2019年需要合议的稽查案卷
		List<Files> fileList = filesMapper.selectFileListNeedHeYiForJiCha();

		ExpertUser expertAseach = new ExpertUser();
		expertAseach.setExpertclass("0");
		expertAseach.setLevel(1);
		//当前类型的专家
		List<ExpertUser> expertList = expertUserMapper.selectGeneralExpertUserByPara(expertAseach);;//地方专家,有区划之分，尽量避免本地区的


		List<ExpertHandlFileList> handleList = new ArrayList<ExpertHandlFileList>();//定义存放更新【案卷与专家用户的关联信息】的集合
		List<Files> updateFileList = new ArrayList<Files>();//定义存放更新【案卷信息】的集合

		for(Files file : fileList){//循环所有的待合议的稽查案卷

			/**
			 * 从地方专家委员中筛选出非案卷本省的专家委员
			 */
			List<ExpertUser> tempExpertList1 = new ArrayList<ExpertUser>();//地方专家临时容器
			for(ExpertUser exp:expertList){
				if(file.getBelongareacode().startsWith(exp.getAreacode().substring(0,2))){//回避本省案卷

				}else{
					tempExpertList1.add(exp);
				}
			}

			/**
			 * 地方专家临时容器升序排列后,取案卷最少的1位专家
			 */
			Collections.sort(tempExpertList1, new Comparator<ExpertUser>(){//集合排序
		            /*
		             * int compare(Student o1, Student o2) 返回一个基本类型的整型，
		             * 返回负数表示：o1 小于o2，
		             * 返回0 表示：o1和o2相等，
		             * 返回正数表示：o1大于o2。
		             */
		            public int compare(ExpertUser o1, ExpertUser o2) {
		                //按照数量大小进行升序排列
		                if(o1.getFilenum() > o2.getFilenum()){
		                    return 1;
		                }
		                if(o1.getFilenum() == o2.getFilenum()){
		                    return 0;
		                }
		                return -1;
		            }
		        });
			ExpertUser commitB1= tempExpertList1.get(0);//人员1

			/**
			 * 被分配过案卷的专家，其案件数量都加1
			 */
			for(ExpertUser exp:expertList){
				if(exp.getId()==commitB1.getId()){
					exp.setFilenum(exp.getFilenum()+1);
				}
			}

			/**
			 * 更新专家委员与案卷的关联信息
			 */
			ExpertHandlFileList handel1 = new ExpertHandlFileList();
			handel1.setFileid(file.getId());
			handel1.setFilecode(file.getFilecode());
			handel1.setExpertid(commitB1.getId());
			handel1.setExpertname(commitB1.getName());
			handel1.setScoredstate("0");//0代表未评审状态
			handel1.setHandltype(1);//1代表B轮专家委员打分
			handleList.add(handel1);

			/**
			 * 更新案卷表中的案卷信息
			 */
			file.setExpertcommitaid(commitB1.getId());
			file.setExpertcommitaname(commitB1.getName());
			file.setIsconsiderexpcommit("0");//默认专家委员不需要合议
            updateFileList.add(file);

		}

		/**
		 * 批量更新专家用户的分配案卷数量
		 */
		expertUserMapper.setCommitExpertFileNum(expertList);

		int res = expertHandlFileMapper.insertBatchListCommit(handleList);//批量新增专家委员分配评审

		StringBuffer msg = new StringBuffer("0开始初始化;");
		if(res>-1){
			Date now =  new Date();
			DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm");
			msg.append(df.format(now)+">>>1案卷初始化分配成功;");
			//批量更新案卷信息（回写专家评审、交叉评审人员信息）
			int res2 = filesMapper.updateFilesOfCommitByBatchList(updateFileList);
			if(res2>-1){
				msg.append("2案卷表初始专家委员信息成功;");
			}else{
				msg.append("2案卷表初始专家委员信息失败;");
			}
		}else{
			msg.append("1案卷初始化分配失败;");
		}
		return msg.toString();
	}


	/**
	 * 第一轮卷面小于15分同步到第二轮
	 * @return
	 */
	@Transactional
	@Override
	public String initSyncJmSocre() {
		StringBuffer msg = new StringBuffer("0：开始同步;");
		try {
			//查询第一轮相差15分的案卷
			List<Files> filesList = filesMapper.selectBetweenPaperScore();

			msg.append("1：需要同步的案卷数据数量为:"+filesList.size()+";");
			if(CollectionUtils.isNotEmpty(filesList)){
				int count = 0;
				for (Files files:filesList){
					Integer expertcommitaid = files.getExpertcommitaid();
					Integer expertcommitbid = files.getExpertcommitbid();
					if (expertcommitaid!=null && expertcommitbid!=null){
						//第一轮评分数据
						ExpertHandlFileList ehfListA = expertHandlFileMapper.getExpertHandlFileByFileTypeAndId(files.getId(), files.getExpertaid());
						ExpertHandlFileList ehfListB = expertHandlFileMapper.getExpertHandlFileByFileTypeAndId(files.getId(), files.getExpertbid());

						//第二轮评分数据
						ExpertHandlFileList ehfComListA = expertHandlFileMapper.getExpertHandlFileByFileTypeAndId(files.getId(), files.getExpertcommitaid());
						ExpertHandlFileList ehfComListB = expertHandlFileMapper.getExpertHandlFileByFileTypeAndId(files.getId(), files.getExpertcommitbid());

						//查询一级指标专家A数据
						List<ExpertHandlIndexScore> ehiListA = expertHandlIndexScoreMapper.getIndexListByExpertId(String.valueOf(ehfListA.getId()), 0);
						if (!ChangnengUtil.isNull(ehiListA)){
							for (ExpertHandlIndexScore expertHandlIndexScoreA:ehiListA){
								Integer aId = expertHandlIndexScoreA.getId();
								expertHandlIndexScoreA.setExperthandlid(ehfComListA.getId());
								ExpertHandlIndexScore indexList = expertHandlIndexScoreMapper.getIndexListByExpertIdAndIndexId(ehfComListA.getId(), expertHandlIndexScoreA.getIndexid(), 0);
								//如果委员A没有这条指标项数据 则新增
								if (ChangnengUtil.isNull(indexList)){
									expertHandlIndexScoreA.setId(null);
									expertHandlIndexScoreMapper.insertSelective(expertHandlIndexScoreA);
									//查询二级指标项并赋值给委员A
									List<ExpertHandlItemScore> itemList = expertHandlItemScoreMapper.getItemListByIndexId(aId, ehfListA.getId());
									if (!ChangnengUtil.isNull(itemList)){
										for (ExpertHandlItemScore itemScore:itemList){
											itemScore.setExpertindexid(expertHandlIndexScoreA.getId());
											itemScore.setId(null);
											expertHandlItemScoreMapper.insertSelective(itemScore);
										}
									}
								}
							}
						}

						ehfComListA.setPaperscore(ehfListA.getPaperscore());
						ehfComListA.setScoredstate("7");
						/**
						 * 假如只进实体页面放开注释
						 */
					//	ehfComListA.setEntityState("1");
						count += expertHandlFileMapper.updateByPrimaryKeySelective(ehfComListA);

						//查询一级指标专家B数据
						List<ExpertHandlIndexScore> ehiListB = expertHandlIndexScoreMapper.getIndexListByExpertId(String.valueOf(ehfListB.getId()), 0);

						if (!ChangnengUtil.isNull(ehiListB)){
							for (ExpertHandlIndexScore expertHandlIndexScoreB:ehiListB){
								Integer bId = expertHandlIndexScoreB.getId();
								expertHandlIndexScoreB.setExperthandlid(ehfComListB.getId());
								ExpertHandlIndexScore indexList = expertHandlIndexScoreMapper.getIndexListByExpertIdAndIndexId(ehfComListB.getId(), expertHandlIndexScoreB.getIndexid(), 0);
								//如果委员A没有这条指标项数据 则新增
								if (ChangnengUtil.isNull(indexList)){
									expertHandlIndexScoreB.setId(null);
									expertHandlIndexScoreMapper.insertSelective(expertHandlIndexScoreB);

									//查询二级指标项并赋值给委员A
									List<ExpertHandlItemScore> itemList = expertHandlItemScoreMapper.getItemListByIndexId(bId, ehfListB.getId());
									if (!ChangnengUtil.isNull(itemList)){
										for (ExpertHandlItemScore itemScore:itemList){
											itemScore.setExpertindexid(expertHandlIndexScoreB.getId());
											itemScore.setId(null);
											expertHandlItemScoreMapper.insertSelective(itemScore);
										}
									}
								}
							}
						}
						ehfComListB.setPaperscore(ehfListB.getPaperscore());
						ehfComListB.setScoredstate("7");
						/**
						 * 假如只进实体页面放开注释
						 */
					//	ehfComListB.setEntityState("1");
						count += expertHandlFileMapper.updateByPrimaryKeySelective(ehfComListB);
					}
				}
				msg.append(" ,2：成功同步的委员专家数量:"+count+";");
			}
			msg.append(" 3：同步成功;");
		}catch (Exception e){
			msg.append(" 出现异常请查看 error:"+e.getMessage());
		}
		return msg.toString();
	}

}
