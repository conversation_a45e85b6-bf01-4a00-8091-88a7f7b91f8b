package com.changneng.sa.service;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.changneng.sa.bean.ElectionPersonal;
import com.changneng.sa.bean.ElectionUnit;
import com.changneng.sa.bean.FilesWithBLOBs;
import com.changneng.sa.bean.PenalizeSyncFile;
import com.changneng.sa.bean.ProvinceElectionPersonal;
import com.changneng.sa.bean.ProvinceFilesWithBLOBs;
import com.changneng.sa.bean.Tarea;
import com.changneng.sa.exception.BusinessException;


public interface ICommonService {
    /**
     * @Description 查询数据字典
     * @return
     */
    Map<String,Object> queryTCDictionaryList();
    
    String queryTCDictionaryByCodeAndType(String code,String type);
    /**
     * @Description 查询当前用户行政区划
     * @param t
     * @return
     * @throws BusinessException
     */
    Map<String,Object> queryTareaByLevelAndCode(Tarea  t) throws BusinessException;
    /**
     * @Description 查询行政区划下一级
     * @param code
     * @return
     * @throws BusinessException
     */
    List<Tarea> queryCascaded(String code) throws BusinessException;
    
    /**
     * 根据父级编码（parentCode）获取子级区划信息
     * @param parentCode
     * @return
     * @throws Exception
     */
    List<Tarea> getChildrenAreaByParentCode(String parentCode)throws Exception;
    /**
     * 重置下级密码模块根据父级编码（parentCode）获取子级区划信息：
     * @param parentCode
     * @return
     * @throws Exception
     */
    List<Tarea> queryTareaListByParentCodeForReset(String parentCode)throws Exception;
    /**
     * 根据父级code获取省级区划信息
     * @param parentCode
     * @return
     * @throws Exception
     */
    List<Tarea> getProvinceAreaByParentCode(String parentCode)throws Exception;
    /**
     * 根据去0后的code模糊查询本级及下级行政区划集合
     * @param codeStr
     * @return
     * @throws Exception
     */
    List<Tarea> getAreaListByCodeLike(String codeStr)throws Exception;
    
    /*List<Penalize SyncFile> selectPenalizeFileByAreacodePerson(String areacode,String type,String qtype,String concode);*/
    
    /**
     * @Description 根据区划码和案卷类型查询案卷
     * @param areacode
     * @param type
     * @return
     */
    /*List<Penalize SyncFile> selectPenalizeFileByAreacode(String areacode,String type,String qtype,String concode);*/
    
    /**
     * @Description 上报选择案卷时，后台案卷检索方法。
     * 根据区划码和案卷类型，以及案卷使用情况等查询案卷
     * @param areacode
     * @param fileType
     * @param useType 案卷使用情况：0代表集体
     * @param conFileCode
     * @param isClosed 是否结案0代表所有1仅代表结案
     * @return
     */
    List<PenalizeSyncFile> selectPenalizeFileByAreacodeForReport(String areacode,String fileType,String useType,String conFileCode,String isClosed);
    
    /*List<Penalize SyncFile> selectPenalizeFileByAreacodePrecise(String areacode,String type,String qtype,String concode);*/
    
    List<PenalizeSyncFile> selectPenalizeFileByAreacodeAll(String areacode,String concode);
    
    FilesWithBLOBs getSelected(String areacode,String filetype,String filecode);
    
    FilesWithBLOBs getSelected(String id);
    
    int selectPersonAnjuanCount(String areacode);
    
    int selectGroupAnjuanCount(String areacode,String type);
    
    ElectionUnit checkUnitExist(String areacode,String id);
    
    ElectionPersonal checkIdCard(String idcard,String id);
    
    ProvinceElectionPersonal checkIdCard2018(String idcard,String id);
    
    /*
     * 检查去年是否参加
     */
    Integer checkIDWithLast(String idcard);
    
    FilesWithBLOBs checkJiChaAnJuan(String id,String filecode,String areacode);
    
    ProvinceFilesWithBLOBs checkJiChaAnJuan2018(String id,String filecode,String areacode);
    
    ElectionUnit selectElectionUnitByAreacodeAndAreatype(String areacode,String areatype);
    
    Tarea queryTareaByCode(String code);
    
    FilesWithBLOBs queryFileinfoByTypeAndAreacode(Integer type,String areacode);
    
    Boolean selectReportState(String provinceCode);
    
    void downloadFile(String url,String fileName,HttpServletRequest request,HttpServletResponse response);
    
    /**
     * @Description 根据区划码和区划类型查询参选集体
     * @param areacode
     * @param type
     * @return
     */
    List<ElectionUnit> getUnitsByAreaLike(String areatype,String areacode);
    
    /**
     * 根据区划级别获取行政区划信息
     * @param areaLevel
     * @return
     * @throws Exception
     */
    List<Tarea> getArearListByAreaLevel(String areaLevel)throws Exception;
    
    /**
     * 先进集体单位集合(下拉)
     * @param areaType 行政区级别
     * @param areaCode 行政区代码-模糊匹配
     * <AUTHOR>
     * @return
     */
    List<ElectionUnit> getElectionUnits(String areaType,String areaCode)throws Exception;
    
    /**
     * 获取先进集体单位省级名称集合(下拉)
     * @param areaType 行政区级别
     * @param areaCode 行政区代码-模糊匹配
     * <AUTHOR>
     * @return
     */
    List<ElectionUnit> getProvinceByAreaType(String areaType,String areaCode)throws Exception;
    
    /**
     * 先进个人行政区集合(下拉)
     * @param areaType 行政区级别
     * @param areaCode 行政区代码-模糊匹配
     * <AUTHOR>
     * @return
     */
    List<ElectionPersonal> getElectionPersons(String areaType,String areaCode)throws Exception;
    
    /**
     * 获取先进集体个人省级名称集合(下拉)
     * @param areaType 行政区级别
     * @param areaCode 行政区代码-模糊匹配
     * <AUTHOR>
     * @return
     */
    List<ElectionPersonal> getProvinceByAreaTypePersons(String areaType,String areaCode)throws Exception;

    /**
     * 获取专家分配的案卷总数
     * @param id
     * @return
     */
	int getExpertFileNum(int expertId);
	/**
	 * 获取当前登录的专家的已评总数
	 * @param expertId
	 * @return
	 */
	int getExperYiPingNum(int expertId);
	/**
	 * 获得总案卷数
	 * @return
	 */
	int getfilesNum();
	/**
	 * 获得所有未评专家的案卷fileId
	 * @return
	 */
	int getExpertWeiPingAllNum();
	/**
	 * 获得所有未评交叉案卷fileId
	 * @return
	 */
	int getCrossWeiPingAllNum();
	
	/**
	 * 获得总的个人事迹案卷数
	 * @return
	 */
	int getShiJiNum();
	/**
	 * 获得交叉人事迹未评案卷数
	 * @return
	 */
	int getCrossShiJiZongNum();
	
	/**
	 * 获取当前交叉人员的案卷数
	 * @param loginId
	 * @return
	 */
	int getCrossFileNum(int loginId);
	/**
	 * 获得当前交叉人员得个人事迹案卷总数
	 * @param loginId
	 * @return
	 */

	int getCrossShiNum(int loginId);
	/**
	 * 获得当前登录交叉人员未评案卷总数
	 * @param loginId
	 * @return
	 */
	int getCrossFileWeiPingNum(int loginId);
	/**
	 * 获得当前登录交叉人员未评案卷总数
	 * @param loginId
	 * @return
	 */
	int getCrossShiJiWeiPingNum(int loginId);
	/**
	 * 合议总数
	 * @return
	 */
	int getHeYiNum();
	/**
	 * 不需要合议总数
	 * @return
	 */
	int getNoHeYiNum();
	/**
	 * 交叉阶段合议案卷数
	 * @return
	 */
	int getCrossHeYiNum();
	/**
	 * 交叉阶段案卷不合议数
	 * @return
	 */
	int getCrossNoHeYiNum();
	/**
	 * 交叉阶段合议个人事迹总数
	 * @return
	 */
	int getShiJiHeNum();
	/**
	 * 交叉阶段不合议个人数据数
	 * @return
	 */
	int getShiJiNoHeYiNum();
	/**
	 * 获取参选单位总数
	 * @return
	 */
	int getcanXuanDanWeiZongShu();
	/**
	 * 参选单位获得省市县总数
	 * @param areaType
	 * @return
	 */
	int getCanXuanDanWeiNum(String areaType);
	/**
	 * 参选个人省市县总数
	 * @param string
	 * @return
	 */
	int getCanXuanGeRenNum(String string);
	/**
	 * 专家案卷总数
	 * @return
	 */
	int getExpFilesNum();

	 /**
     * 查询省份列表
     * @param areaLevel
     * @return
     */
	List<Tarea> getProvinceList();
	
	/**
	 * 插入fastdfs上传日志
	 * @param file_name
	 * @param fastdfs_url
	 * @return
	 */
	int insertFastDFS_Log(String file_name,String fastdfs_url);
	/**
	 * 委员案卷已评数量
	 * @param file_name
	 * @param fastdfs_url
	 * @return
	 */
	int getWeiyuanNum();
	/**
	 * 委员案卷总数
	 * @return
	 */
	int getWeiyuanYiPingNum();

	int getTuCHuNum(int loginId,int i);
	
	boolean sendCheckCode(String phone) throws Exception;
	
	/**
	 * 根据手机号登录时，验证手机号是否存在，若存在，则返回表名称（为了区分用户类型）
	 * @param phone
	 * @return
	 */
	String checkPhoneIsHave(String phone);
	
	/**
	 * 首页统计-信息采集-概要统计
	 * @param subAreacode
	 * @return
	 */
	Map<String,Object> totalNumByArea(String areacode,String subAreacode);
	
	List<Integer> totalNumByFileType(String subAreacode);
	
	List<Integer> totalNumByUseType(String subAreacode);
	
	Map<String, Object> totalFileNumByArea(String areaCode,String subAreacode);
	
	Map<String, Object> totalJtNumByArea(String areaCode,String subAreacode);
	
	Map<String, Object> totalGrNumByArea(String areaCode,String subAreacode);
	
	/**
	 * 首页统计-省内评比-概要统计
	 * @param subAreacode
	 * @return
	 */
	Map<String,Object> totalBaseNumForTJ(String subAreacode);
	
	List<Map<String, Object>> totalUnitByAreaForTJ(String areaType,String subAreacode);
	
	List<Map<String, Object>> totalPersonByAreaForTJ(String areaType,String subAreacode);
	
	Map<String, Object> totalPersonInCaseNumForTJ(String subAreacode);
	
	List<Integer> totalUseTypeNumForTJ(String subAreacode);
	
	List<Integer> totalReportTypeNumForTJ(String subAreacode);
	
	List<Integer> totalKzDlbNumForTJ(String subAreacode);
	
	List<Integer> totalFileTypeNumForTJ(String subAreacode);
	
	Map<String, Object> totalFileNumByAreaForTJ(String areaCode,String subAreacode);
	
	Map<String, Object> totalUnitNumByAreaForTJ(String areaCode,String subAreacode);
	
	Map<String, Object> totalPersonNumByAreaForTJ(String areaCode,String subAreacode);

	/**
	 * 首页统计--案卷评分-参评案卷
	 * 
	 * @return
	 */
	List<Object> participateFilesStatistics();
	
	/**
	 * 首页统计--案卷评分-已评案卷
	 * 
	 * @return
	 */
	List<Object> evaluatedFilesStatistics();

	/**
	 * 首页统计--案卷评分-推送合议案卷
	 * 
	 * @return
	 */
	List<Object> pushCollegialFilesStatistics();

	/**
	 * 首页统计--案卷评分-本账号案件总数
	 * 
	 * @param id
	 * @return
	 */
	List<Object> thisAccountFilesStatistics(int id);

	/**
	 * 首页统计--案卷评分-本账号已评案卷
	 * 
	 * @param id
	 * @return
	 */
	List<Object> thisAccountEvaluatedFilesStatistics(int id);
	
}
