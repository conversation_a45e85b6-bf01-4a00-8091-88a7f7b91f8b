package com.changneng.sa.service;

import java.util.List;

import com.changneng.sa.bean.ProvinceElectionPersonal;
import com.changneng.sa.bean.ProvinceFilesWithBLOBs;
import com.changneng.sa.bean.Tarea;
import com.changneng.sa.exception.BusinessException;
import com.changneng.sa.util.PageBean;

public interface IXxcjGeRenShangBaoService {

	
	PageBean<ProvinceElectionPersonal> selectProElectionPersonalList(ProvinceElectionPersonal personalPro, int pageNum) throws BusinessException;
	
	//先进个人信息列表导出功能
	List<ProvinceElectionPersonal> selectExcel(ProvinceElectionPersonal personalPro) throws BusinessException;
	
	//根据id删除
	void deletePersonPro(String id) throws BusinessException;
	
	//根据id查询
	ProvinceElectionPersonal selectPersonalById(String id) throws BusinessException;
	

     // @Description 保存个人信息
    public void saveXxcjPersonAnjuan(List<ProvinceFilesWithBLOBs> fileList,ProvinceElectionPersonal ep) throws BusinessException;
	
	//此方法用于个人上报保存中，根据登录人code查询出所属的省市县
    Tarea selectAreaByCode(String areacode) throws BusinessException;

    public void updateXxcjPersonPro(List<ProvinceFilesWithBLOBs> fileList,ProvinceElectionPersonal ep) throws BusinessException;
    
    PageBean<Tarea> getPersonalAreaList(int pNum) throws BusinessException;
    
  //先进个人信息列表全国参选数量导出功能
    List<Tarea> getAllPersonalAreaList() throws BusinessException;

	List<ProvinceElectionPersonal> selectPersonalByCode(String areaCode);
}
