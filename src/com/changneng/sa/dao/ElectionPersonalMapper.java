package com.changneng.sa.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.changneng.sa.bean.ElectionPersonal;
import com.changneng.sa.bean.ElectionPersonalExcel;
import com.changneng.sa.bean.ElectionPersonal_PXJG;
import com.changneng.sa.bean.ElectionPersonal_SLPF;
import com.changneng.sa.bean.GongZhongTouPiao;

public interface ElectionPersonalMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(ElectionPersonal record);

    int insertSelective(ElectionPersonal record);

    ElectionPersonal selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(ElectionPersonal record);

    int updateByPrimaryKeyWithBLOBs(ElectionPersonal record);

    int updateByPrimaryKey(ElectionPersonal record);
    
    List<ElectionPersonal> selectElectionPersonalListBycondition(@Param("areacode")String areacode,@Param("type")String type);
    
    List<ElectionPersonal> selectElectionPersonalList();
    
    int selectPersonAnjuanCount(@Param("areacode")String areacode);
    
    ElectionPersonal checkIdCard(@Param("idcard")String idcard,@Param("id")String id);  
    
    /**
     * 数量评分-先进个人-列表集合查询
     * @param areaType 行政区级别
     * @param areaCode 行政区代码
     * <AUTHOR>
     * @return
     */
    List<ElectionPersonal_SLPF> getElectionPersonal_SLPF(@Param("areaType")String areaType,@Param("areaCode")String areaCode);
    
    
    
    /**
     * 质量评分 -- 先进个人
     * 根据不同的级别查询列表 （省级，地市，县）
     * lhl
     * @return
     */
    List<ElectionPersonal> selectPersonalList(@Param("areaType")String areaType);
    // 交叉排名
    List<ElectionPersonal> selectJcpmPersonalList(@Param("areaType")String areaType);
    // 公众投票
    List<ElectionPersonal> selectPersonalGztpList(@Param("areaType")String areaType);
    
    /**
     * 评选结果-先进个人-列表集合查询
     * @param areaType 行政区级别
     * @param areaCode 行政区代码
     * <AUTHOR>
     * @return
     */
    List<ElectionPersonal_PXJG> getElectionPersonal_PXJG(@Param("areaType")String areaType,@Param("areaCode")String areaCode);
    
    /**
     * 个人 评选出的前几名
     * 省：20
     * 市：40
     * 县：40
     * @param areaType
     * @param topNum
     * @return
     * @throws Exception
     */
    List<ElectionPersonal_PXJG> getElectionPersonal_PXJGTopNum(@Param("areaType")String areaType,@Param("topNum")Integer topNum);
    
    /**
     * 案卷评分--维护参选个人信息表
     * 查询仅查询 交叉人员id 和 id
     * <AUTHOR>
     * @return
     */
    List<ElectionPersonal> getElectionPersonalList();
    
    /**
     * 执行更新个人信息表
     * 维护fileScore 案卷得分
     * <AUTHOR> 
     * @return 影响条数
     */
    int updateElectionPersonalList();
    // 更新个人质量分含有权重
    int updateElectionPersonaQualityFileTotalScore();
    
    
    /**
     * 交叉排名分数汇总
     * lhl 2016-11-16
     * @return
     */
    int updateJcpmPersonalList();
    /**
     * 交叉评分汇总排名
     * lhl2016-11-16
     * @return
     */
    int setJcpmPersonRanking();
    
    /**
     * 先进个人行政区下拉(集合)
     * @param areaType
     * @param areaCode
     * @return
     */
    public List<ElectionPersonal> getPersonsByAreaLike(@Param("areaType")String areaType,@Param("areaCode")String areaCode);
    
    /**
     * 获取先进集体个人省级名称集合(集合)
     * @param areaType
     * @param areaCode
     * @return
     */
    public List<ElectionPersonal> getProvinceByAreaTypePersons(@Param("areaType")String areaType,@Param("areaCode")String areaCode);
    
    /**
     * 一票否决-先进个人-根据areacode查询人员
     * @param areaType
     * @param areaCode
     * @param name
     * @param areaCodeMate
     * <AUTHOR>
     * @return
     */
    List<ElectionPersonal> getPersonsByArea(@Param("areaType")String areaType,@Param("areaCode")String areaCode,@Param("name")String name,@Param("cardid")String cardid,@Param("areaCodeMate")String areaCodeMate);
    /**
     * 根据id设置人员表一票否决
     * @param personId
     * <AUTHOR>
     * @return
     */
    int setIsYiPiaoFouJue(@Param("personId")int personId,@Param("flag")String flag);
    
    /**
     * 根据cardid获取人员id
     * @param cardid
     * <AUTHOR>
     * @return
     */
    int getIDByCardID(@Param("cardid")String cardid);
    
    /**
     * 获取最大案件数量
     * <AUTHOR>
     * @return
     */
    int getMaxCaseNum();
    
    /**
     * 计算个人数量得分
     * @param maxCaseNum
     * <AUTHOR>
     * @return
     */
    int setPersonNumScore();
    
    /**
     * 计算个人评选结果总分
     * <AUTHOR>
     * @return
     */
    int setPersonTotalScore();
    
    /**
     * 根据个人评选结果总分计算排名
     * <AUTHOR>
     * @return
     */
    int setPersonRanking();
    /**
     * 下载excel表单数据列表 先进个人信息列表
     * @return ww
     */
	List<ElectionPersonalExcel> getXxhzList(@Param(value="areaType") String areaType);
	
	/**
	 * 根据区划code 前两位模糊查询个人事迹信息
	 * @param list
	 * @return
	 */
	List<ElectionPersonal> selectListByAreaListPara(List<String> list);
	
	/**
	 * 批量更新交叉评审人员信息
	 * @param list
	 * @return
	 */
	int updatePersonalFilesOfCrossByBatchList(List<ElectionPersonal> list);
	/**
	 * 根据区域的id和省份证导入公众投票的分值
	 * @param gongZhongTouPiao
	 */
	int updateByAreaCodeAndCardID(@Param("gongZhongTouPiao")GongZhongTouPiao gongZhongTouPiao);

	/**
	 * 获得所有个人事迹总数
	 * @return
	 */
	int getShiJiNum();
	/**
	 * 交叉阶段个人事迹合议数
	 * @return
	 */
	int getShiJiHeNum();
	/**
	 * 交叉阶段个人事迹不合议数
	 * @return
	 */
	int getShiJiNoHeYiNum();
	
	/**
	 * 参选个人省市县分布总数
	 * @param areaType
	 * @return
	 */
	int getCanXuanGeRenNum(@Param("areaType")String areaType);
	
	/**
	 * 公众投票排名
	 * lhl    2016-11-23
	 * @return
	 */
	int setGztpPersonRanking();
	
	/**
	 * 获得参选个人表中所有fileid不为null的数据
	 * 为专家案卷做准备
	 * @return
	 */
	List<ElectionPersonal> getElectionPersonalFileIdNotNull();
	
	
	/**
	 * 参选个人事迹全部取出，不区分不同的code地区
	 * lhl  
	 * 2016-12-19
	 * @return
	 */
	List<ElectionPersonal>  selectListByAreaListParaAll();

	ElectionPersonal selectElectionPersonalByCardId(@Param("cardid")String cardid);

	List<ElectionPersonal> selectPersonalQhdcList(@Param("areaType")String areaType);
	
	List<ElectionPersonal> selectPersonalQhdcExcel();
	
	/**
	 * 计算个人所有案卷的分数和
	 * @return
	 */
	int updatePersonalFileScorePlus();
	
	/**
	 * 计算个人案卷质量分
	 * @return
	 */
	int setPersonalFileScore();
	
	/**
     * 计算个人评选结果总分
     * <AUTHOR>
     * @return
     */
    int setPersonTotalScore2017();
    
    /**
     * 下载个人先进事迹专用
     * @return
     */
    List<ElectionPersonal> getPersonalList();

    /**
     * 计算个人质量分2019
     */
	int setQualityScore();
}