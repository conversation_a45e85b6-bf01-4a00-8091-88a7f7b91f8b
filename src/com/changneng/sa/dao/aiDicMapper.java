package com.changneng.sa.dao;

import com.changneng.sa.bean.aiDic;

import java.util.List;

public interface aiDicMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(aiDic record);

    int insertSelective(aiDic record);

    aiDic selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(aiDic record);

    int updateByPrimaryKey(aiDic record);

    List<aiDic> selectAll();

    List<aiDic> selectBytype(String answerType);
    List<aiDic> selectEntityByType(String answerType);
}
