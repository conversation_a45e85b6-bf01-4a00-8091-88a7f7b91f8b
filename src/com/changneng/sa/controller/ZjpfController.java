package com.changneng.sa.controller;


import java.io.BufferedOutputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSONObject;
import com.changneng.sa.bean.*;
import com.changneng.sa.dao.*;
import com.changneng.sa.util.*;
import org.apache.commons.collections.CollectionUtils;
import com.changneng.sa.bean.*;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.changneng.sa.aop.SysLogPoint;
import com.changneng.sa.aop.SysLogPoint.dbType;
import com.changneng.sa.aop.SysLogPoint.sysType;
import com.changneng.sa.gloableCtrl.CheckLogin;
import com.changneng.sa.gloableCtrl.ResultTypeEnum;
import com.changneng.sa.repeatCommit.CheckRepeatCommit;
import com.changneng.sa.service.ZjpfService;

/**
 * 专家平分模块
 * <AUTHOR> 2016-10-08
 *
 */
@Controller
@RequestMapping("/")
public class ZjpfController {

	@Autowired
	private ZjpfService zjpfService;

	@Autowired
	private TCDictionaryMapper dictionaryMapper;

	@Autowired
	private ExpertHandlFileListMapper expertHandlFileListMapper;

	@Autowired
	private ExpertHandlIndexScoreMapper expertHandlIndexScoreMapper;

	public static final Logger logger = Logger.getLogger(ZjpfController.class);
	@Autowired
	public  ZjpfAjaxMapper zjpfAjaxMapper;


    @Autowired
    private ExpertUserMapper expertUserMapper;

	 /**
     * 2019年专家评分--列表
     * @param request
     * @return
     */
    @CheckLogin(ResultTypeEnum.page)
    @SysLogPoint(dbOptType=dbType.SEARCH,sysOptType=sysType.EXPERT_SCORED)
    @RequestMapping(value = "/zjpf/zjpfList")
    public ModelAndView goZjpfList(HttpServletRequest request,
    		@RequestParam(value = "scoredState", required = false) String scoredState,
    		@RequestParam(value = "fileCode", required = false) String fileCode
    		){
    	ModelAndView mav = new ModelAndView("zjpf/zjpf_list");
    	PageBean<ExpertHandUnit_ZJPF_zjpfList>  pageBean = null;
    	try {
    		//获取当前session中的用户信息
    		LoginUser curLoginUser = ControllerUtil.getCurLoginUser(request);
    		Integer expertId = curLoginUser.getId();
    		String expertType = curLoginUser.getExpertType();
			String userTypeCode = curLoginUser.getUserTypeCode();


			ExpertUser user = expertUserMapper.selectByloginNameAndPassword(curLoginUser);
            String errorState = user.getErrorState();
            if(errorState!=null&&errorState.equals("1")){
                String errorRate = curLoginUser.getErrorRate();
                errorRate=curLoginUser.getUserName()+"专家，您好！在2024年案卷评查中,您的评查错误率为"+errorRate+"，为保证案卷评查质量,请您认真评查！";
                request.setAttribute("errorRate", errorRate);
                user.setErrorState("2");
                expertUserMapper.updateByPrimaryKeySelective(user);
            }
            ExpertHandUnit_ZJPF_zjpfList expertHandListVo = new ExpertHandUnit_ZJPF_zjpfList();
    		//页数
    		Integer pageNum = 1;
    		if(request.getParameter("pageNum")!=null){
    			if(request.getParameter("pageNum")!=""){
    				pageNum = Integer.parseInt(request.getParameter("pageNum"));
    			}
    		};
    		if(StringUtils.isNotBlank(scoredState)){
    			expertHandListVo.setScoredState(scoredState);
    		}
    		if(StringUtils.isNotBlank(fileCode)){
    			expertHandListVo.setFileCode(fileCode.trim());
    		}
    		if(expertId != null){
    			expertHandListVo.setExpertId(expertId);
    			pageBean = zjpfService.getExpertHandList(expertHandListVo, pageNum);
    		}
    		request.setAttribute("pageBean", pageBean);
			request.setAttribute("scoredState", scoredState);
			request.setAttribute("fileCode", fileCode);
			String expertTypeName = "";
    		if(expertType != null){
    			if(expertType.equals("0")){//行政处罚0
    				expertTypeName = "行政处罚案卷";
    			}else if(expertType.equals("1")){//按日计罚案卷1
    				expertTypeName = "按日计罚案卷";
    			}else if(expertType.equals("5")){//查封扣押6
    				expertTypeName = "现场稽查案卷";
    			}else if(expertType.equals("6")){//查封扣押6
    				expertTypeName = "查封扣押案卷";
    			}else if(expertType.equals("7")){//限产停产7
    				expertTypeName = "限产停产案卷";
    			}else if(expertType.equals("2")){//移送行政拘留案卷2
    				expertTypeName = "移送行政拘留案卷";
    			}else if(expertType.equals("3")){//涉嫌犯罪移送案卷3
					expertTypeName = "涉嫌犯罪移送案卷";
				}else if(expertType.equals("9")){//9 不予处罚
					expertTypeName = "不予处罚案卷";
				}
			}
    		request.setAttribute("expertTypeName", expertTypeName);
    		request.setAttribute("pageNum", pageNum);
    		request.setAttribute("userTypeCode", userTypeCode);
    		request.setAttribute("UserId", "");
			return mav;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
    }



	/**
	 * 2019年专家评分--列表
	 * @param request
	 * @return
	 */
	@CheckLogin(ResultTypeEnum.page)
	@SysLogPoint(dbOptType=dbType.SEARCH,sysOptType=sysType.EXPERT_SCORED)
	@RequestMapping(value = "/zjpf/zjpfLists")
	public ModelAndView goZjpfLists(HttpServletRequest request,Integer UserId){

		ModelAndView mav = new ModelAndView("zjpf/zjpf_list");
		PageBean<ExpertHandUnit_ZJPF_zjpfList>  pageBean = null;
		try {
			//获取当前session中的用户信息
			LoginUser curLoginUser = ControllerUtil.getCurLoginUser(request);
			Integer expertId = curLoginUser.getId();
			//userTypeCode=4 是管理员
			String userTypeCode = curLoginUser.getUserTypeCode();
			String expertType = curLoginUser.getExpertType();
			ExpertHandUnit_ZJPF_zjpfList expertHandListVo = new ExpertHandUnit_ZJPF_zjpfList();
			//页数
			Integer pageNum = 1;
			if(request.getParameter("pageNum")!=null){
				if(request.getParameter("pageNum")!=""){
					pageNum = Integer.parseInt(request.getParameter("pageNum"));
				}
			}
			if(expertId != null || UserId!= null){
				expertHandListVo.setExpertId(UserId);
				pageBean = zjpfService.getExpertHandList(expertHandListVo, pageNum);
			}
			request.setAttribute("pageBean", pageBean);
			String expertTypeName = "";
			if(expertType != null){
				if(expertType.equals("0")){//行政处罚0
					expertTypeName = "行政处罚案卷";
				}else if(expertType.equals("1")){//按日计罚案卷1
					expertTypeName = "按日计罚案卷";
				}else if(expertType.equals("5")){//查封扣押6
					expertTypeName = "现场稽查案卷";
				}else if(expertType.equals("6")){//查封扣押6
					expertTypeName = "查封扣押案卷";
				}else if(expertType.equals("7")){//限产停产7
					expertTypeName = "限产停产案卷";
				}else if(expertType.equals("2")){//移送行政拘留案卷2
					expertTypeName = "移送行政拘留案卷";
				}else if(expertType.equals("3")){//涉嫌犯罪移送案卷3
					expertTypeName = "涉嫌犯罪移送案卷";
				}else if(expertType.equals("9")){//不予处罚案卷
					expertTypeName = "不予处罚案卷";
				}
			}
			request.setAttribute("expertTypeName", expertTypeName);
			request.setAttribute("pageNum", pageNum);
			request.setAttribute("userTypeCode", userTypeCode);
			request.setAttribute("UserId", UserId);
			return mav;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}





    /**
     * 2019专家评分---卷面评分---填报、编辑页面
     * @param request
     * @param id 主键的id
     * @return
     */
    @CheckLogin(ResultTypeEnum.page)
    @RequestMapping(value = "/zjpf/zjpfScore")
    public ModelAndView gozjpfScore(HttpServletRequest request,
    		@RequestParam(value = "id", required = false) Integer id,
    		@RequestParam(value = "status", required = false) String status,
    		@RequestParam(value = "pageNum", required = false) String pageNum){
    	 ModelAndView mav = new ModelAndView("zjpf/zjpf_score");
   	 	 try {
	   		   if(id!=null && !"".equals(id)){
	   			   ExpertHandlFileListWithBLOBs expertHandlFileList =  zjpfService.findJuanAnLeiBiaoById(id);
	   			   Files file = zjpfService.findFileFileNameaAndUrl(expertHandlFileList.getFileid());
				   List<TCDictionary> tcDictionaryList = dictionaryMapper.getDataTemp("excellentFiles");
				   List<TCDictionary> jcajDictionaryList = dictionaryMapper.getDataTemp("jcajlentFiles");


	   			   String filetype = file.getFiletype();
	   			   String filetypeName = "";
	   			   if("0".equals(filetype)){
	   				   filetypeName = "行政处罚案卷";
	   			   }else if("1".equals(filetype)){
	   				   filetypeName = "按日计罚案卷";
	   			   }else if("6".equals(filetype)){
	   				   filetypeName = "查封扣押案卷";
	   			   }else if("7".equals(filetype)){
	   				   filetypeName = "限产停产案卷";
	   			   }else if("2".equals(filetype)){
	   				   filetypeName = "移送行政拘留案卷";
	   			   }else if("3".equals(filetype)){
	   				   filetypeName = "涉嫌犯罪移送案卷";
	   			   }else if("9".equals(filetype)){
					   filetypeName = "不予处罚案卷";
				   }else if("5".equals(filetype)){// 发现问题的污染源现场监督检查稽查案卷5
	   				   filetypeName = "发现问题的污染源现场监督检查稽查案卷";
	   				   mav = new ModelAndView("zjpf/zjpf_jc_score");
	   			   }

	   			   int index = file.getFilename().indexOf(".");

	   			   String suffix = file.getFilename().substring(index+1);
	   			   expertHandlFileList.setFilename(file.getFilename());
	   			   expertHandlFileList.setFiletype(file.getFiletype());
	   			   expertHandlFileList.setFileurl(file.getFileurl());
	   			   expertHandlFileList.setClosed(file.getClosed());
	   			   expertHandlFileList.setSuffix(suffix);



		   		   request.setAttribute("expertHandlFileList", expertHandlFileList);
		   		   request.setAttribute("pageNum", pageNum);
	     		   request.setAttribute("expertFileId", id);
	     		   request.setAttribute("filetypeName", filetypeName);
	     		   request.setAttribute("status", status);
				   request.setAttribute("tcDictionaryList",tcDictionaryList);
				   request.setAttribute("jcajDictionaryList",jcajDictionaryList);

				   // 添加fileId和expertId，用于加分项数据获取
				   request.setAttribute("fileId", expertHandlFileList.getFileid());
				   request.setAttribute("expertId", expertHandlFileList.getExpertid());

	     		   //获取fastDFS路径
	     		   String fastDFSAddress = PropertiesHandlerUtil.getValue("fastdfs.ip", "config.fastdfs");
	     		   String downUrl = fastDFSAddress+file.getFileurl()+"?attname="+file.getFilecode()+file.getFilename().substring(index);
				   String fastDFS = fastDFSAddress+expertHandlFileList.getFileurl();
				   request.setAttribute("fastDFS", fastDFS);
				   request.setAttribute("downUrl", downUrl);
	   		   }
	   		   return mav;
	     } catch (Exception e) {
	           e.printStackTrace();
	           return null;
	     }
	}
    /**
     * 异常信息详情查询
     * @param request
     * @return
     */
    @CheckLogin(ResultTypeEnum.page)
    @RequestMapping(value = "/zjpf/errorState")
    public ModelAndView errorState(HttpServletRequest request,
    		@RequestParam(value = "fileId", required = false) Integer fileId){
    	 ModelAndView mav = new ModelAndView("zjpf/zjpf_error");
		 List<ExpertScoreError>  pageBean = null;
		 List<ExpertScoreError>  pageBeanSt = null;
   	 	 try {
	   		   if(fileId!=null && !"".equals(fileId)){
				   Integer handlType=ControllerUtil.getCurLoginUser(request).getLevel();
	   		    	// 获取卷面分评审列表
				   pageBean = zjpfService.selectScoreErrorList(fileId,"0",handlType);
		   		   request.setAttribute("pageBean", pageBean);
	     		   request.setAttribute("fileId", fileId);

				   // 获取实体分评审列表
				   pageBeanSt = zjpfService.selectScoreErrorList(fileId,"1",handlType);
				   request.setAttribute("pageBeanSt", pageBeanSt);
	   		   }
	   		   return mav;
	     } catch (Exception e) {
	           e.printStackTrace();
	           return null;
	     }
	}


	/**
	 * 异常信息列表导出
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@CheckLogin(ResultTypeEnum.json)
	@RequestMapping(value = "/zjpf/downExpertScoreErrorExecl")
	public ModelAndView downExpertScoreErrorExecl(@RequestParam(value = "type", required = false) String type,
												  @RequestParam(value = "fileId", required = false) String fileId,HSSFWorkbook workbook,HttpServletRequest request,
											HttpServletResponse response) {
		try {
			ViewExcel viewExcel = new ViewExcel();
			Map<String, Object> model = new HashMap<String, Object>();


			//获取当前session中的用户信息
			LoginUser curLoginUser = ControllerUtil.getCurLoginUser(request);
			String userName = curLoginUser.getUserName();

			if(fileId!=null && !"".equals(fileId)){
				// 获取卷面分评审列表
				Integer handlType=ControllerUtil.getCurLoginUser(request).getLevel();
				List<ExpertScoreError> expertScoreErrors = zjpfService.selectScoreErrorList(Integer.valueOf(fileId), null,handlType);

				if (!CollectionUtils.isEmpty(expertScoreErrors)){
					// 表头
					String[] columnNames;
					String[] dbColumnNames;

					String expertNameA;
					String expertNameB;
					if (expertScoreErrors.get(0).getExpertNameA().equals(userName)){
						expertNameA=userName;
					}else {
						expertNameA="专家A";
					}
					if (expertScoreErrors.get(0).getExpertNameB().equals(userName)){
						expertNameB=userName;
					}else {
						expertNameB="专家B";
					}

					columnNames = new String[]{"存在争议项","具体指标（指实体部分）", expertNameA, expertNameB, "备注"};
					dbColumnNames= new String[]{"indexName","itemName", "expertA", "expertB", "remark"};


					model.put("columnNames", columnNames);
					// list集合对应的值
					model.put("dbColumnNames", dbColumnNames);



					model.put("list", expertScoreErrors);

					// excel文件的名称
					model.put("excelName", expertScoreErrors.get(0).getFileCode()+"案卷争议项.xls");
					// excel 文件的sheet
					model.put("sheetName", "sheet1");
					// 标记序号
					model.put("flag", false);
					viewExcel.buildExcelDocument(model, workbook, request, response);
				}
			}

			return new ModelAndView(new ViewExcel(), model);

		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

    /**
     * 2019专家评分---实体评分---填报、编辑页面
     * @param request
     * @param id 主键的id
     * @return
     */
    @CheckLogin(ResultTypeEnum.page)
    @RequestMapping(value = "/zjpf/entityAScore")
    public ModelAndView gozjpfAEntityScore(HttpServletRequest request,
    		@RequestParam(value = "id", required = false) Integer id,
										   @RequestParam(value = "status", required = false) String status,
    		@RequestParam(value = "pageNum", required = false) String pageNum,
										   @RequestParam(value = "pageIndex", required = false) String pageIndex,
										   @RequestParam(value = "areaType", required = false) String areaType,
										   @RequestParam(value = "isConsider", required = false) String isConsider,
										   @RequestParam(value = "isInCheck", required = false) String isInCheck,
										   @RequestParam(value = "fileCode", required = false) String fileCode,
										   @RequestParam(value = "gopath", required = false) String gopath
										   ){
    	 ModelAndView mav = new ModelAndView("zjpf/zjpf_entity_score");
   	 	 try {
			 //获取登录用户类型,是管理员还是专家,如果是4说明是管理员
			 LoginUser areaUser = ControllerUtil.getCurLoginUser(request);
			 String userTypeCode = areaUser.getUserTypeCode();
			 request.setAttribute("userTypeCode", userTypeCode);
	   		   if(id!=null && !"".equals(id)){
	   			   ExpertHandlFileListWithBLOBs expertHandlFileList =  zjpfService.findJuanAnLeiBiaoById(id);
				   String indexOneList = zjpfService.getIndexOneList(id);
				   Files file = zjpfService.findFileFileNameaAndUrl(expertHandlFileList.getFileid());
	   			   int index = file.getFilename().indexOf(".");
				   List<TCDictionary> tcDictionaryList = dictionaryMapper.getDataTemp("excellentFiles");
				   List<TCDictionary> jcajDictionaryList = dictionaryMapper.getDataTemp("jcajlentFiles");

	   			   String suffix = file.getFilename().substring(index+1);
	   			   expertHandlFileList.setFilename(file.getFilename());
	   			   expertHandlFileList.setFiletype(file.getFiletype());
	   			   expertHandlFileList.setFileurl(file.getFileurl());
	   			   expertHandlFileList.setClosed(file.getClosed());
	   			   expertHandlFileList.setSuffix(suffix);

	   			   String filetype = file.getFiletype();
	   			   String filetypeName = "";
	   			   if("0".equals(filetype)){
	   				   filetypeName = "行政处罚案卷";
	   			   }else if("1".equals(filetype)){
	   				   filetypeName = "按日计罚案卷";
	   			   }else if("6".equals(filetype)){
	   				   filetypeName = "查封扣押案卷";
	   			   }else if("7".equals(filetype)){
	   				   filetypeName = "限产停产案卷";
	   			   }else if("2".equals(filetype)){
	   				   filetypeName = "移送行政拘留案卷";
	   			   }else if("3".equals(filetype)){
	   				   filetypeName = "涉嫌犯罪移送案卷";
	   			   }else if("9".equals(filetype)){
					   filetypeName = "不予处罚案卷";
				   }

		   		   request.setAttribute("expertHandlFileList", expertHandlFileList);
		   		   request.setAttribute("indexOneList", indexOneList);
		   		   request.setAttribute("pageNum", pageNum);
	     		   request.setAttribute("expertFileId", id);
	     		   request.setAttribute("filetypeName", filetypeName);
				   request.setAttribute("tcDictionaryList",tcDictionaryList);
				   request.setAttribute("jcajDictionaryList",jcajDictionaryList);
				   request.setAttribute("status", status);


	     		   //获取fastDFS路径
	     		   String fastDFSAddress = PropertiesHandlerUtil.getValue("fastdfs.ip", "config.fastdfs");
	     		   String downUrl = fastDFSAddress+file.getFileurl()+"?attname="+file.getFilecode()+file.getFilename().substring(index);;
				   String fastDFS = fastDFSAddress+expertHandlFileList.getFileurl();
				   request.setAttribute("fastDFS", fastDFS);
				   request.setAttribute("downUrl", downUrl);

				   request.setAttribute("pageIndex", pageIndex);
				   request.setAttribute("areaType", areaType);
				   request.setAttribute("isConsider", isConsider);
				   request.setAttribute("isInCheck", isInCheck);
				   request.setAttribute("fileCode", fileCode);
				   request.setAttribute("gopath", gopath);
	   		   }
	   		   return mav;
	     } catch (Exception e) {
	           e.printStackTrace();
	           return null;
	     }
	}

    /**
     * 实体评分--查看页面
     * @param request
     * @param id 主键的id
     * @return
     */
    @CheckLogin(ResultTypeEnum.page)
    @RequestMapping(value = "/zjpf/entityAView")
    public ModelAndView gozjpfAEntityScoreView(HttpServletRequest request,
    		@RequestParam(value = "id", required = false) Integer id,
    		@RequestParam(value = "pageNum", required = false) String pageNum){
    	 ModelAndView mav = new ModelAndView("zjpf/zjpf_entity_view");
   	 	 try {
	   		   if(id!=null && !"".equals(id)){
	   			   ExpertHandlFileListWithBLOBs expertHandlFileList =  zjpfService.findJuanAnLeiBiaoById(id);
	   			   Files file = zjpfService.findFileFileNameaAndUrl(expertHandlFileList.getFileid());
	   			   int index = file.getFilename().indexOf(".");

	   			   String suffix = file.getFilename().substring(index+1);
	   			   expertHandlFileList.setFilename(file.getFilename());
	   			   expertHandlFileList.setFiletype(file.getFiletype());
	   			   expertHandlFileList.setFileurl(file.getFileurl());
	   			   expertHandlFileList.setClosed(file.getClosed());
	   			   expertHandlFileList.setSuffix(suffix);

	   			   String filetype = file.getFiletype();
	   			   String filetypeName = "";
	   			   if("0".equals(filetype)){
	   				   filetypeName = "行政处罚案卷";
	   			   }else if("1".equals(filetype)){
	   				   filetypeName = "按日计罚案卷";
	   			   }else if("6".equals(filetype)){
	   				   filetypeName = "查封扣押案卷";
	   			   }else if("7".equals(filetype)){
	   				   filetypeName = "限产停产案卷";
	   			   }else if("2".equals(filetype)){
	   				   filetypeName = "移送行政拘留案卷";
	   			   }else if("3".equals(filetype)){
	   				   filetypeName = "涉嫌犯罪移送案卷";
	   			   }else if("9".equals(filetype)){
					   filetypeName = "不予处罚案卷";
				   }

		   		   request.setAttribute("expertHandlFileList", expertHandlFileList);
		   		   request.setAttribute("pageNum", pageNum);
	     		   request.setAttribute("expertFileId", id);
	     		   request.setAttribute("filetypeName", filetypeName);

	     		   //获取fastDFS路径
	     		   String fastDFSAddress = PropertiesHandlerUtil.getValue("fastdfs.ip", "config.fastdfs");
	     		   String downUrl = fastDFSAddress+file.getFileurl()+"?attname="+file.getFilecode()+file.getFilename().substring(index);;
				   String fastDFS = fastDFSAddress+expertHandlFileList.getFileurl();
				   request.setAttribute("fastDFS", fastDFS);
				   request.setAttribute("downUrl", downUrl);
	   		   }
	   		   return mav;
	     } catch (Exception e) {
	           e.printStackTrace();
	           return null;
	     }
	}

    /**
     * 加分项--查看页面
     * @param request
     * @param id 主键的id
     * @return
     */
    @CheckLogin(ResultTypeEnum.page)
    @RequestMapping(value = "/zjpf/plusesView")
    public ModelAndView gozjpfPlusesView(HttpServletRequest request,
    		@RequestParam(value = "id", required = false) Integer id,
    		@RequestParam(value = "pageNum", required = false) String pageNum){
    	 ModelAndView mav = new ModelAndView("zjpf/zjpf_pluses_view");
   	 	 try {
	   		   if(id!=null && !"".equals(id)){
	   			   ExpertHandlFileListWithBLOBs expertHandlFileList =  zjpfService.findJuanAnLeiBiaoById(id);
	   			   Files file = zjpfService.findFileFileNameaAndUrl(expertHandlFileList.getFileid());
	   			   int index = file.getFilename().indexOf(".");

	   			   String suffix = file.getFilename().substring(index+1);
	   			   expertHandlFileList.setFilename(file.getFilename());
	   			   expertHandlFileList.setFiletype(file.getFiletype());
	   			   expertHandlFileList.setFileurl(file.getFileurl());
	   			   expertHandlFileList.setClosed(file.getClosed());
	   			   expertHandlFileList.setSuffix(suffix);

	   			   String filetype = file.getFiletype();
	   			   String filetypeName = "";
	   			   if("0".equals(filetype)){
	   				   filetypeName = "行政处罚案卷";
	   			   }else if("1".equals(filetype)){
	   				   filetypeName = "按日计罚案卷";
	   			   }else if("6".equals(filetype)){
	   				   filetypeName = "查封扣押案卷";
	   			   }else if("7".equals(filetype)){
	   				   filetypeName = "限产停产案卷";
	   			   }else if("2".equals(filetype)){
	   				   filetypeName = "移送行政拘留案卷";
	   			   }else if("3".equals(filetype)){
	   				   filetypeName = "涉嫌犯罪移送案卷";
	   			   }else if("9".equals(filetype)){
					   filetypeName = "不予处罚案卷";
				   }

		   		   request.setAttribute("expertHandlFileList", expertHandlFileList);
		   		   request.setAttribute("pageNum", pageNum);
	     		   request.setAttribute("expertFileId", id);
	     		   request.setAttribute("filetypeName", filetypeName);

	     		   //获取fastDFS路径
	     		   String fastDFSAddress = PropertiesHandlerUtil.getValue("fastdfs.ip", "config.fastdfs");
	     		   String downUrl = fastDFSAddress+file.getFileurl()+"?attname="+file.getFilecode()+file.getFilename().substring(index);;
				   String fastDFS = fastDFSAddress+expertHandlFileList.getFileurl();
				   request.setAttribute("fastDFS", fastDFS);
				   request.setAttribute("downUrl", downUrl);
	   		   }
	   		   return mav;
     } catch (Exception e) {
           e.printStackTrace();
           return null;
     }
	}


    /**
     * 2019专家评分---卷面评分---查看页面
     * @param request
     * @param id 主键的id
     * @return
     */
    @CheckLogin(ResultTypeEnum.page)
    @RequestMapping(value = "/zjpf/zjpfScoreView")
    public ModelAndView gozjpfScoreView(HttpServletRequest request,
    		@RequestParam(value = "id", required = false) Integer id,
    		@RequestParam(value = "status", required = false) String status,
    		@RequestParam(value = "pageNum", required = false) String pageNum,
    		@RequestParam(value = "pageIndex", required = false) String pageIndex,
    		@RequestParam(value = "areaType", required = false) String areaType,
    		@RequestParam(value = "isConsider", required = false) String isConsider,
    		@RequestParam(value = "isInCheck", required = false) String isInCheck,
    		@RequestParam(value = "fileCode", required = false) String fileCode,
    		@RequestParam(value = "gopath", required = false) String gopath
										){
    	 ModelAndView mav = new ModelAndView("zjpf/zjpf_view");
   	 	 try {
				 //获取登录用户类型,是管理员还是专家,如果是4说明是管理员
			 LoginUser areaUser = ControllerUtil.getCurLoginUser(request);
			 String userTypeCode = areaUser.getUserTypeCode();

			 if(id!=null && !"".equals(id)){
	   			   ExpertHandlFileListWithBLOBs expertHandlFileList =  zjpfService.findJuanAnLeiBiaoById(id);
	   			   Files file = zjpfService.findFileFileNameaAndUrl(expertHandlFileList.getFileid());
	   			   int index = file.getFilename().indexOf(".");
				   List<TCDictionary> tcDictionaryList = dictionaryMapper.getDataTemp("excellentFiles");
				   List<TCDictionary> jcajDictionaryList = dictionaryMapper.getDataTemp("jcajlentFiles");
	   			   String suffix = file.getFilename().substring(index+1);
	   			   expertHandlFileList.setFilename(file.getFilename());
	   			   expertHandlFileList.setFiletype(file.getFiletype());
	   			   expertHandlFileList.setFileurl(file.getFileurl());
	   			   expertHandlFileList.setClosed(file.getClosed());
	   			   expertHandlFileList.setSuffix(suffix);
	   			   String filetype = file.getFiletype();
	   			   String filetypeName = "";
	   			   if("0".equals(filetype)){
	   				   filetypeName = "行政处罚案卷";
	   			   }else if("1".equals(filetype)){
	   				   filetypeName = "按日计罚案卷";
	   			   }else if("6".equals(filetype)){
	   				   filetypeName = "查封扣押案卷";
	   			   }else if("7".equals(filetype)){
	   				   filetypeName = "限产停产案卷";
	   			   }else if("2".equals(filetype)){
	   				   filetypeName = "移送行政拘留案卷";
	   			   }else if("3".equals(filetype)){
	   				   filetypeName = "涉嫌犯罪移送案卷";
	   			   }else if("9".equals(filetype)){
					   filetypeName = "不予处罚案卷";
				   }else if("5".equals(filetype)){//查封扣押6
	   				   filetypeName = "发现问题的污染源现场监督检查稽查案卷";
	   				   mav = new ModelAndView("zjpf/zjpf_jc_view");
	    		   }

		   		   request.setAttribute("expertHandlFileList", expertHandlFileList);
		   		   request.setAttribute("pageNum", pageNum);
	     		   request.setAttribute("expertFileId", id);
	     		   request.setAttribute("filetypeName", filetypeName);
	     		   request.setAttribute("status", status);
	     		   request.setAttribute("tcDictionaryList", tcDictionaryList);
	     		   request.setAttribute("jcajDictionaryList", jcajDictionaryList);

	     		   //获取fastDFS路径
	     		   String fastDFSAddress = PropertiesHandlerUtil.getValue("fastdfs.ip", "config.fastdfs");
	     		   String downUrl = fastDFSAddress+file.getFileurl()+"?attname="+file.getFilecode()+file.getFilename().substring(index);;
				   String fastDFS = fastDFSAddress+expertHandlFileList.getFileurl();
				   request.setAttribute("fastDFS", fastDFS);
				   request.setAttribute("downUrl", downUrl);
				   request.setAttribute("userTypeCode", userTypeCode);
				   request.setAttribute("pageIndex", pageIndex);
				   request.setAttribute("areaType", areaType);
				   request.setAttribute("isConsider", isConsider);
				   request.setAttribute("isInCheck", isInCheck);
				   request.setAttribute("fileCode", fileCode);
				   request.setAttribute("gopath", gopath);
	   		   }
	   		   return mav;
	     } catch (Exception e) {
	           e.printStackTrace();
	           return null;
	     }
	}

    /**
     * 2019专家委员打分---列表（专家第二圈打分）
     * @param request
     * @param pageNum
     * @param expertID
     * @param fileCode
     * @param scoredState
     * @return
     */
    @CheckLogin(ResultTypeEnum.page)
	@RequestMapping("/zjpf/zjpf_list_commit")
	public ModelAndView goZjpfListCommit(HttpServletRequest request, String pageNum, String expertID, String fileCode,String scoredState,String entityState) {
		ModelAndView mav = new ModelAndView("/zjpf/zjpf_list_commit");
		try {
			LoginUser areaUser = ControllerUtil.getCurLoginUser(request);
			HashMap<String, String> map = new HashMap<>();
			if (pageNum == null || "".equals(pageNum)) {
				pageNum = "1";
			}
			int pagenum = Integer.valueOf(pageNum);
			map.put("expertID", String.valueOf(areaUser.getId()));
			map.put("fileCode", fileCode);
			map.put("scoredState", scoredState);
			map.put("entityState", entityState);
			PageBean<CommitteeBean> pageBean = zjpfService.getCommittenBean(map, pagenum);
			mav.addObject("pageBean", pageBean);
			mav.addObject("fileCode", fileCode);
			mav.addObject("entityState", entityState);
			mav.addObject("scoredState", scoredState);
			return mav;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}


    /**
     * 2019专家委员评分---卷面评分--填报、编辑页面
     * @param request
     * @param id 主键的id
     * @return
     */
    @CheckLogin(ResultTypeEnum.page)
    @RequestMapping(value = "/zjpf/zjpfCommiteeScore")
    public ModelAndView gozjpfCommiteeScore(HttpServletRequest request,
    		@RequestParam(value = "id", required = false) Integer id,
    		@RequestParam(value = "status", required = false) String status,
    		@RequestParam(value = "pageNum", required = false) String pageNum){
    	 ModelAndView mav = new ModelAndView("zjpf/zjpf_score_commit");
   	 	 try {
	   		   if(id!=null && !"".equals(id)){
	   			   ExpertHandlFileListWithBLOBs expertHandlFileList =  zjpfService.findJuanAnLeiBiaoById(id);
	   			   Files file = zjpfService.findFileFileNameaAndUrl(expertHandlFileList.getFileid());
	   			   int index = file.getFilename().indexOf(".");
				   List<TCDictionary> tcDictionaryList = dictionaryMapper.getDataTemp("excellentFiles");
				   List<TCDictionary> jcajDictionaryList = dictionaryMapper.getDataTemp("jcajlentFiles");

	   			   String filetype = file.getFiletype();
	   			   String filetypeName = "";
	   			   if("0".equals(filetype)){
	   				   filetypeName = "行政处罚案卷";
	   			   }else if("1".equals(filetype)){
	   				   filetypeName = "按日计罚案卷";
	   			   }else if("6".equals(filetype)){
	   				   filetypeName = "查封扣押案卷";
	   			   }else if("7".equals(filetype)){
	   				   filetypeName = "限产停产案卷";
	   			   }else if("2".equals(filetype)){
	   				   filetypeName = "移送行政拘留案卷";
	   			   }else if("3".equals(filetype)){
	   				   filetypeName = "涉嫌犯罪移送案卷";
	   			   }else if("9".equals(filetype)){
					   filetypeName = "不予处罚案卷";
				   }else if("5".equals(filetype)){// 发现问题的污染源现场监督检查稽查案卷5
	   				   filetypeName = "发现问题的污染源现场监督检查稽查案卷";
	   				   mav = new ModelAndView("zjpf/zjpf_jc_score_commit");
	   			   }


	   			   String suffix = file.getFilename().substring(index+1);
	   			   expertHandlFileList.setFilename(file.getFilename());
	   			   expertHandlFileList.setFiletype(file.getFiletype());
	   			   expertHandlFileList.setFileurl(file.getFileurl());
	   			   expertHandlFileList.setClosed(file.getClosed());
	   			   expertHandlFileList.setSuffix(suffix);


		   		   request.setAttribute("expertHandlFileList", expertHandlFileList);
		   		   request.setAttribute("pageNum", pageNum);
	     		   request.setAttribute("expertFileId", id);
	     		   request.setAttribute("filetypeName", filetypeName);
	     		   request.setAttribute("status", status);
	     		   request.setAttribute("tcDictionaryList", tcDictionaryList);
	     		   request.setAttribute("jcajDictionaryList", jcajDictionaryList);
					mav.addObject("filesId", id);

	     		   //获取fastDFS路径
	     		   String fastDFSAddress = PropertiesHandlerUtil.getValue("fastdfs.ip", "config.fastdfs");
	     		   String downUrl = fastDFSAddress+file.getFileurl()+"?attname="+file.getFilecode()+file.getFilename().substring(index);;
				   String fastDFS = fastDFSAddress+expertHandlFileList.getFileurl();
				   request.setAttribute("fastDFS", fastDFS);
				   request.setAttribute("downUrl", downUrl);

				   if("1".equals(expertHandlFileList.getEntityState())){
					   mav = new ModelAndView("zjpf/zjpf_entity_score_commit");
				   }
	   		   }
	   		   return mav;
	     } catch (Exception e) {
	           e.printStackTrace();
	           return null;
	     }
	}


    /**
     * 2019专家委员评分---实体评分---填报、编辑页面
     * @param request
     * @param id 主键的id
     * @return
     */
    @CheckLogin(ResultTypeEnum.page)
    @RequestMapping(value = "/zjpf/entityCommitScore")
    public ModelAndView gozjpfCommitEntityScore(HttpServletRequest request,
    		@RequestParam(value = "id", required = false) Integer id,
    		@RequestParam(value = "pageNum", required = false) String pageNum){

    	 ModelAndView mav = new ModelAndView("zjpf/zjpf_entity_score_commit");
   	 	 try {
	   		   if(id!=null && !"".equals(id)){
				   System.out.println("开始执行");
	   			   ExpertHandlFileListWithBLOBs expertHandlFileList =  zjpfService.findJuanAnLeiBiaoById(id);
				   String indexOneList = zjpfService.getIndexOneList(id);

	   			   Files file = zjpfService.findFileFileNameaAndUrl(expertHandlFileList.getFileid());
	   			   int index = file.getFilename().indexOf(".");
				   List<TCDictionary> tcDictionaryList = dictionaryMapper.getDataTemp("excellentFiles");
				   List<TCDictionary> jcajDictionaryList = dictionaryMapper.getDataTemp("jcajlentFiles");

	   			   String suffix = file.getFilename().substring(index+1);
	   			   expertHandlFileList.setFilename(file.getFilename());
	   			   expertHandlFileList.setFiletype(file.getFiletype());
	   			   expertHandlFileList.setFileurl(file.getFileurl());
	   			   expertHandlFileList.setClosed(file.getClosed());
	   			   expertHandlFileList.setSuffix(suffix);

	   			   String filetype = file.getFiletype();
	   			   String filetypeName = "";
	   			   if("0".equals(filetype)){
	   				   filetypeName = "行政处罚案卷";
	   			   }else if("1".equals(filetype)){
	   				   filetypeName = "按日计罚案卷";
	   			   }else if("6".equals(filetype)){
	   				   filetypeName = "查封扣押案卷";
	   			   }else if("7".equals(filetype)){
	   				   filetypeName = "限产停产案卷";
	   			   }else if("2".equals(filetype)){
	   				   filetypeName = "移送行政拘留案卷";
	   			   }else if("9".equals(filetype)){
					   filetypeName = "不予处罚案卷";
				   }else if("3".equals(filetype)){
	   				   filetypeName = "涉嫌犯罪移送案卷";
	   			   }
		   		   request.setAttribute("expertHandlFileList", expertHandlFileList);
		   		   request.setAttribute("pageNum", pageNum);
	     		   request.setAttribute("expertFileId", id);
	     		   request.setAttribute("filetypeName", filetypeName);
	     		   request.setAttribute("indexOneList", indexOneList);
				   request.setAttribute("tcDictionaryList",tcDictionaryList);
				   request.setAttribute("jcajDictionaryList",jcajDictionaryList);
	     		   //获取fastDFS路径
	     		   String fastDFSAddress = PropertiesHandlerUtil.getValue("fastdfs.ip", "config.fastdfs");
				   System.out.println("开始执行1");
	     		   String downUrl = fastDFSAddress+file.getFileurl()+"?attname="+file.getFilecode()+file.getFilename().substring(index);
				   System.out.println("开始执行2");
				   String fastDFS = fastDFSAddress+expertHandlFileList.getFileurl();
				   System.out.println("开始执行3");
				   request.setAttribute("fastDFS", fastDFS);
				   request.setAttribute("downUrl", downUrl);
	   		   }
			 System.out.println("开始返回");
	   		   return mav;

	     } catch (Exception e) {
	           e.printStackTrace();
	           return null;
	     }
	}

    /**
     * 2019专家委员评分---卷面评分---查看页面
     * @param request
     * @param id 主键的id
     * @return
     */
    @CheckLogin(ResultTypeEnum.page)
    @RequestMapping(value = "/zjpf/zjpfCommitView")
    public ModelAndView gozjpfCommiteeView(HttpServletRequest request,
    		@RequestParam(value = "id", required = false) Integer id,
    		@RequestParam(value = "status", required = false) String status,
    		@RequestParam(value = "pageNum", required = false) String pageNum){
    	 ModelAndView mav = new ModelAndView("zjpf/zjpf_view_commit");
   	 	 try {
	   		   if(id!=null && !"".equals(id)){
	   			   ExpertHandlFileListWithBLOBs expertHandlFileList =  zjpfService.findJuanAnLeiBiaoById(id);
	   			   Files file = zjpfService.findFileFileNameaAndUrl(expertHandlFileList.getFileid());
	   			   int index = file.getFilename().indexOf(".");
				   List<TCDictionary> tcDictionaryList = dictionaryMapper.getDataTemp("excellentFiles");
				   List<TCDictionary> jcajDictionaryList = dictionaryMapper.getDataTemp("jcajlentFiles");


				   String suffix = file.getFilename().substring(index+1);
	   			   expertHandlFileList.setFilename(file.getFilename());
	   			   expertHandlFileList.setFiletype(file.getFiletype());
	   			   expertHandlFileList.setFileurl(file.getFileurl());
	   			   expertHandlFileList.setClosed(file.getClosed());
	   			   expertHandlFileList.setSuffix(suffix);

	   			   String filetype = file.getFiletype();
	   			   String filetypeName = "";
	   			   if("0".equals(filetype)){
	   				   filetypeName = "行政处罚案卷";
	   			   }else if("1".equals(filetype)){
	   				   filetypeName = "按日计罚案卷";
	   			   }else if("6".equals(filetype)){
	   				   filetypeName = "查封扣押案卷";
	   			   }else if("7".equals(filetype)){
	   				   filetypeName = "限产停产案卷";
	   			   }else if("2".equals(filetype)){
	   				   filetypeName = "移送行政拘留案卷";
	   			   }else if("3".equals(filetype)){
	   				   filetypeName = "涉嫌犯罪移送案卷";
	   			   }else if("9".equals(filetype)){
					   filetypeName = "不予处罚案卷";
				   }else if("5".equals(filetype)){//查封扣押6
	   				   filetypeName = "发现问题的污染源现场监督检查稽查案卷";
	   				   mav = new ModelAndView("zjpf/zjpf_jc_view_commit");
	    		   }


		   		   request.setAttribute("expertHandlFileList", expertHandlFileList);
		   		   request.setAttribute("pageNum", pageNum);
	     		   request.setAttribute("expertFileId", id);
	     		   request.setAttribute("filetypeName", filetypeName);
	     		   request.setAttribute("status", status);
				   request.setAttribute("tcDictionaryList", tcDictionaryList);
				   request.setAttribute("jcajDictionaryList", jcajDictionaryList);
	     		   //获取fastDFS路径
	     		   String fastDFSAddress = PropertiesHandlerUtil.getValue("fastdfs.ip", "config.fastdfs");
	     		   String downUrl = fastDFSAddress+file.getFileurl()+"?attname="+file.getFilecode()+file.getFilename().substring(index);;
				   String fastDFS = fastDFSAddress+expertHandlFileList.getFileurl();
				   request.setAttribute("fastDFS", fastDFS);
				   request.setAttribute("downUrl", downUrl);
	   		   }
	   		   return mav;
	     } catch (Exception e) {
	           e.printStackTrace();
	           return null;
	     }
	}

    /**
     * 2019专家委员评分---实体评分--查看页面
     * @param request
     * @param id 主键的id
     * @return
     */
    @CheckLogin(ResultTypeEnum.page)
    @RequestMapping(value = "/zjpf/entityCommitView")
    public ModelAndView gozjpfCommitEntityView(HttpServletRequest request,
    		@RequestParam(value = "id", required = false) Integer id,
    		@RequestParam(value = "pageNum", required = false) String pageNum){
    	 ModelAndView mav = new ModelAndView("zjpf/zjpf_entity_view_commit");
   	 	 try {
	   		   if(id!=null && !"".equals(id)){
	   			   ExpertHandlFileListWithBLOBs expertHandlFileList =  zjpfService.findJuanAnLeiBiaoById(id);
	   			   Files file = zjpfService.findFileFileNameaAndUrl(expertHandlFileList.getFileid());
	   			   int index = file.getFilename().indexOf(".");

	   			   String suffix = file.getFilename().substring(index+1);
	   			   expertHandlFileList.setFilename(file.getFilename());
	   			   expertHandlFileList.setFiletype(file.getFiletype());
	   			   expertHandlFileList.setFileurl(file.getFileurl());
	   			   expertHandlFileList.setClosed(file.getClosed());
	   			   expertHandlFileList.setSuffix(suffix);

	   			   String filetype = file.getFiletype();
	   			   String filetypeName = "";
	   			   if("0".equals(filetype)){
	   				   filetypeName = "行政处罚案卷";
	   			   }else if("1".equals(filetype)){
	   				   filetypeName = "按日计罚案卷";
	   			   }else if("6".equals(filetype)){
	   				   filetypeName = "查封扣押案卷";
	   			   }else if("7".equals(filetype)){
	   				   filetypeName = "限产停产案卷";
	   			   }else if("2".equals(filetype)){
	   				   filetypeName = "移送行政拘留案卷";
	   			   }else if("9".equals(filetype)){
					   filetypeName = "不予处罚案卷";
				   }else if("3".equals(filetype)){
	   				   filetypeName = "涉嫌犯罪移送案卷";
	   			   }

		   		   request.setAttribute("expertHandlFileList", expertHandlFileList);
		   		   request.setAttribute("pageNum", pageNum);
	     		   request.setAttribute("expertFileId", id);
	     		   request.setAttribute("filetypeName", filetypeName);

	     		   //获取fastDFS路径
	     		   String fastDFSAddress = PropertiesHandlerUtil.getValue("fastdfs.ip", "config.fastdfs");
	     		   String downUrl = fastDFSAddress+file.getFileurl()+"?attname="+file.getFilecode()+file.getFilename().substring(index);
				   String fastDFS = fastDFSAddress+expertHandlFileList.getFileurl();
				   request.setAttribute("fastDFS", fastDFS);
				   request.setAttribute("downUrl", downUrl);
	   		   }
	   		   return mav;
	     } catch (Exception e) {
	           e.printStackTrace();
	           return null;
	     }
	}


    /**
     * 专家推荐案卷列表查看评分页面
     * @param request
     * @param id 主键的id0
     * @return
     */
    @CheckLogin(ResultTypeEnum.page)
    @RequestMapping(value = "/zjpf/zjpfScoreLook")
    public ModelAndView gozjpfScoreLook(HttpServletRequest request,
    		@RequestParam(value = "expertId", required = false) Integer expertId,
    		@RequestParam(value = "fileId", required = false) Integer fileId){
    	 ModelAndView mav = new ModelAndView("zjpf/zjpf_score");
   	 	 try {
	   		   if(expertId!=null && !"".equals(expertId) && fileId!=null && !"".equals(fileId)){
	   			   ExpertHandlFileListWithBLOBs expertHandlFileList =  zjpfService.getByExpIdAndFilecode(fileId,expertId);
	   			   Files file = zjpfService.findFileFileNameaAndUrl(fileId);
				   List<TCDictionary> tcDictionaryList = dictionaryMapper.getDataTemp("excellentFiles");
				   List<TCDictionary> jcajDictionaryList = dictionaryMapper.getDataTemp("jcajlentFiles");
	   			   int index = file.getFilename().indexOf(".");

	   			   String suffix = file.getFilename().substring(index+1);
	   			   expertHandlFileList.setFilename(file.getFilename());
	   			   expertHandlFileList.setFiletype(file.getFiletype());
	   			   expertHandlFileList.setFileurl(file.getFileurl());
	   			   expertHandlFileList.setSuffix(suffix);

	   			   String filetype = file.getFiletype();
	   			   String filetypeName = "";
	   			   if("0".equals(filetype)){
	   				   filetypeName = "行政处罚案卷";
	   			   }else if("1".equals(filetype)){
	   				   filetypeName = "按日计罚案卷";
	   			   }else if("6".equals(filetype)){
	   				   filetypeName = "查封扣押案卷";
	   			   }else if("7".equals(filetype)){
	   				   filetypeName = "限产停产案卷";
	   			   }else if("2".equals(filetype)){
	   				   filetypeName = "移送行政拘留案卷";
	   			   }else if("3".equals(filetype)){
	   				   filetypeName = "涉嫌犯罪移送案卷";
	   			   }else if("9".equals(filetype)){
					   filetypeName = "不予处罚案卷";
				   }

				   request.setAttribute("tcDictionaryList",tcDictionaryList);
				   request.setAttribute("jcajDictionaryList",jcajDictionaryList);
		   		   request.setAttribute("expertHandlFileList", expertHandlFileList);
	     		   request.setAttribute("expertFileId", expertHandlFileList.getId());
	     		   request.setAttribute("filetypeName", filetypeName);
	     		   request.setAttribute("status", 1);

	     		   //获取fastDFS路径
	     		   String fastDFSUrl = PropertiesHandlerUtil.getValue("fastdfs.ip", "config.fastdfs");
	     		   String url = file.getFileurl();
	     		   request.setAttribute("fastDFS", fastDFSUrl+url);

	   		   }
	   		   return mav;
	     } catch (Exception e) {
	           e.printStackTrace();
	           return null;
	     }
	}

    /**
     *功能： 案卷url判断是否为空，返回json
     * @param request
     * @param fileId
     * @return
     */
    @CheckLogin(ResultTypeEnum.json)
    @RequestMapping(value ="/zjpf/xzcfExistFileUrl")
    @ResponseBody
    public String  xzcfExistFileUrl(HttpServletRequest request,
    		@RequestParam(value = "fileId", required = false) Integer fileId){
    	try {
	    		Files files = null;
	    		if(fileId != null ){
	    			files = zjpfService.findFileFileNameaAndUrl(fileId);
	    			String filename = files.getFilename();
	    			if(files.getFileurl() != null && files.getFileurl() != ""){
	    				if(filename.lastIndexOf(".") != -1){
	    					return "yes";
	    				}else if(filename.lastIndexOf(".") == -1){
		    				return "suffixerror";
		    			}else {
		    				return "no";
		    			}
	    			}
	    		}
	    		return "no";
	    	}
	    	catch (Exception e) {
	    		e.printStackTrace();
	    		return "no";
	    }

	}

    /**
     * 专家评分文件的下载
     * @param request
     * @param response
     * @param fileId
     */
    @CheckLogin(ResultTypeEnum.json)
    @RequestMapping(value = "/zjpf/zjpfAnJuandown")
    @SysLogPoint(dbOptType=dbType.DOWNLOAD,sysOptType=sysType.COMMON_OPT)
    public void zjpfAnJuandown(HttpServletRequest request,HttpServletResponse response,
    		@RequestParam(value = "fileId", required = false) Integer fileId ){
    	try {
    		BufferedOutputStream output = null;
    		Files files = null;
    		if(fileId != null ){
    			files = zjpfService.findFileFileNameaAndUrl(fileId);
    		}
    		String filename = files.getFilename();
			int lastIndexOf = filename.lastIndexOf('.');
			String type = filename.substring(lastIndexOf);
    		FastDFSClient fastDFSClient = new FastDFSClient("classpath:config/fdfs_client.conf");
    		FileUtil.setFileDownloadHeader(request, response,files.getFilecode()+type);
    		if(files.getFileurl() != null ){
    			output =  new BufferedOutputStream(response.getOutputStream());
    			if(files.getFileurl() != null && files.getFileurl() !=""){
					fastDFSClient.download_file(files.getFileurl(),output);
				}
    		}
    	} catch (Exception e) {
			e.printStackTrace();
		}
    }

    /**
     * 专家评分保存打分——OLD
     */
    /*@CheckLogin(ResultTypeEnum.json)
    @RequestMapping(value = "/zjpf/savezjpf")
    @SysLogPoint(dbOptType=dbType.ADD,sysOptType=sysType.EXPERT_SCORED)
    @ResponseBody
    public JsonResult  saveZjpf(HttpServletRequest request, ExpertHandlFileListWithBLOBs expertHandlFileList){
    	JsonResult jsonResult=new JsonResult();
    	try {
    			Float expertfinalscore = expertHandlFileList.getExpertfinalscore();
    			 boolean naN = expertfinalscore.isNaN();
    			 String regex = "(?!^0\\.0?0$)^([0-9]|[0-9][0-9]|[1][0-1][0-9])?(\\.[0-9]{1,2})?$|^(120|120\\.0|120\\.00|0\\.0)";
				 //Pattern p = Pattern.compile(regex);
				 boolean matches = false;
				 if(expertfinalscore != null){
					 matches = Pattern.matches(regex, expertfinalscore.toString());
				 }
				 //判断是否为推荐案件，如果推荐案件取消，清除数据库中的评语
				 String yxdxanlituijian = expertHandlFileList.getYxdxanlituijian();
				 if(yxdxanlituijian != null &&  yxdxanlituijian != ""){
					 if(yxdxanlituijian.contains(",")){
						 String[] split = yxdxanlituijian.split(",");
						 yxdxanlituijian =  split[1];
						 expertHandlFileList.setYxdxanlituijian(yxdxanlituijian);
					 }
				 }
				 if("0".equals(yxdxanlituijian)){
					 expertHandlFileList.setYxdxanlituijianreviews("");
				 }
				 if(!naN && matches && expertfinalscore != null){
						 zjpfService.saveZjpf(expertHandlFileList);
						 jsonResult.setResult(Const.RESULT_SUCCESS);
						 jsonResult.setMessage("保存成功");
						 jsonResult.setData("1");
						 return jsonResult;
				 }else{
					 jsonResult.setResult(Const.RESULT_ERROR);
					 jsonResult.setMessage("保存失败");
					 jsonResult.setData("1");//数据格式输入不正确！
					 return jsonResult;
				 }
    	} catch (Exception e) {
    		e.printStackTrace();
    		jsonResult.setResult(Const.RESULT_ERROR);
    		jsonResult.setMessage("保存失败");
    		return jsonResult;
    	}
    }*/

    /**
     * 2019普通专家--卷面分数--评分页加载项集合
     * @param request
     * @param indexType 3交叉还是2专家
     * @param fileType 案件类型  0行政处罚案卷;1按日计罚案卷;2移送行政拘留案卷;3环境污染犯罪移送公安机关案卷;4申请法院强制执行案卷;5发现问题的污染源现场监督检查稽查案卷
     * @param id 主键id
     * @return
     */
    @CheckLogin(ResultTypeEnum.json)
    @RequestMapping(value = "/zjpf/getIndexList")
    @ResponseBody
    public JsonResult  getIndexList(HttpServletRequest request, String indexType,String fileType,String id){
    	 JsonResult json=new JsonResult();
    	 json= zjpfService.getIndexList(indexType,fileType,id,0);
    	 return json;
    }

	/**
	 *获取异常评分另外一个专家评分明细
	 * @param request
	 * @param indexType
	 * @param fileType
	 * @param id
	 * @return
	 */
    @CheckLogin(ResultTypeEnum.json)
    @RequestMapping(value = "/zjpf/getOtherExpertScore")
    @ResponseBody
    public JsonResult  getOtherExpert(Integer handlType,Integer expertId,Integer indexId,Integer fileId,Integer itemid){
    	 JsonResult json=new JsonResult();
    	 json= zjpfService.getOtherExpertScore(handlType,expertId,indexId,fileId,itemid);
    	 return json;
    }

	 /**
	 　* 保存是否认同异常
	 　* <AUTHOR>
	 　* @date 2024-06-09 10:06:47
	 　*/
	@CheckLogin(ResultTypeEnum.json)
	@RequestMapping(value = "/zjpf/saveErrorIsAgree")
	@ResponseBody
	public JsonResult  saveErrorIsAgree(ExpertScoreError errorInfo){
		JsonResult json=new JsonResult();
		json = zjpfService.saveErrorIsAgree(errorInfo);
		return json;
	}

    /**
     * 2019普通专家--实体分数--评分页加载项集合
     * @param request
     * @param indexType 3交叉还是2专家
     * @param fileType 案件类型  0行政处罚案卷;1按日计罚案卷;2移送行政拘留案卷;3环境污染犯罪移送公安机关案卷;4申请法院强制执行案卷;5发现问题的污染源现场监督检查稽查案卷
     * @param id 主键id
     * @return
     */
    @CheckLogin(ResultTypeEnum.json)
    @RequestMapping(value = "/zjpf/getEntityIndexs")
    @ResponseBody
    public JsonResult  getEntityIndexList(HttpServletRequest request, String indexType,String id){
    	 JsonResult json=new JsonResult();
    	 json= zjpfService.getEntityIndexs(indexType,id);
    	 return json;
    }

    /**
     * 获取加分项评分数据
     * @return 加分项评分数据
     */
    @CheckLogin(ResultTypeEnum.json)
    @RequestMapping(value = "/zjpf/getPlusesData")
    @ResponseBody
    public JsonResult getPlusesData(HttpServletRequest request, String id) {
		JsonResult json=new JsonResult();
		json = zjpfService.getPlusesData(id);
		return json;
    }

	@CheckLogin(ResultTypeEnum.json)
	@RequestMapping(value = "/zjpf/initAipfSeverice")
	@ResponseBody
	public JsonResult  initAipfSeverice(HttpServletRequest request){
		JsonResult json=new JsonResult();
//        zjpfService.initAipfSeverice();
        zjpfService.initAipfSeverice2025();
		return json;
	}


	@Autowired
	private TareaMapper tareaMapper;


	@RequestMapping(value = "/zjpf/getProvinces")
	@ResponseBody
	public List<FileIsDrawProvince> getProvinces() {
		List<FileIsDrawProvince> fileIsDrawProvinces = tareaMapper.selectProvince();
		return fileIsDrawProvinces;
	}

	@RequestMapping(value ="/zjpf/getCities")
	@ResponseBody
	public List<Tarea> getCities(@RequestParam("province") String provinceCode) {
		List<Tarea> tareas = tareaMapper.selectCountryByCityCode(provinceCode.substring(0,2));
		return tareas;
	}

	@RequestMapping(value ="/zjpf/getCounties")
	@ResponseBody
	public List<Tarea> getCounties(@RequestParam("city") String cityCode) {
		return tareaMapper.newselectCountryByCityCode(cityCode);
	}

	@RequestMapping(value ="/zjpf/saveAiFiles")
	@ResponseBody
	public void saveAiFiles(FilesWithBLOBs files) {
		zjpfService.saveAiFiles(files);

	}

	/** 初始化无需评查 */
	@CheckLogin(ResultTypeEnum.json)
	@RequestMapping(value = "/zjpf/initNotAipfSeverice")
	@ResponseBody
	public JsonResult  initNotAipfSeverice(HttpServletRequest request){
		JsonResult json=new JsonResult();
		zjpfService.initNotAipfSeverice2024();
		return json;
	}

	/** 初始化实体部分的ai识别内容 */
	@CheckLogin(ResultTypeEnum.json)
	@RequestMapping(value = "/zjpf/initEntityAipfSeverice")
	@ResponseBody
	public JsonResult  initEntityAipfSeverice(HttpServletRequest request){
		JsonResult json=new JsonResult();
		zjpfService.initEntityAipfSeverice2024();
		return json;
	}

	@CheckLogin(ResultTypeEnum.json)
	@RequestMapping(value = "/zjpf/initEntityAipfFoujue")
	@ResponseBody
	public JsonResult  initEntityAipfFoujue(HttpServletRequest request){
		JsonResult json=new JsonResult();
		zjpfService.initEntityAipfSevericeFouJue();
		return json;
	}

	@RequestMapping(value = "/zjpf/getCaseAssistant2024")
	@ResponseBody
	public JsonResult  getCaseAssistant2024(HttpServletRequest request,@RequestParam(value = "Reqcity", required = false)String Reqcity){
		JsonResult json=new JsonResult();
		List<CaseAssistant> list = zjpfService.getCaseAssistant2024(Reqcity);

		json.setCode("200");
		json.setResult(Const.RESULT_SUCCESS);
		json.setData(list);
		return json;
	}

	@CheckLogin(ResultTypeEnum.json)
	@RequestMapping(value = "/zjpf/initAiZjpfScore")
	@ResponseBody
	public JsonResult  initAiZjpfScore(HttpServletRequest request){
		JsonResult json=new JsonResult();
		zjpfService.initZjpfScore();
		json.setCode("200");
		json.setResult(Const.RESULT_SUCCESS);
		json.setData("已初始化成功");
		System.out.println("卷面初始化成功--------------------------");
		return json;
	}

	/** 初始化实体评分 */
	@CheckLogin(ResultTypeEnum.json)
	@RequestMapping(value = "/zjpf/initAiZjpfEntityScore")
	@ResponseBody
	public JsonResult  initAiZjpfEntityScore(HttpServletRequest request){
		JsonResult json=new JsonResult();
		String result = zjpfService.initZjpfEntityScore();
		json.setMessage(result);
		System.out.println("实体初始化成功");
		return json;
	}


	@CheckLogin(ResultTypeEnum.json)
	@RequestMapping(value = "/zjpf/initAiScoreById")
	@ResponseBody
	public JsonResult  initAiScoreById(HttpServletRequest request,String id){
		JsonResult json=new JsonResult();
		zjpfService.initAiScoreById(id);
		return json;
	}


    /**
     * 2019专家委员--卷面分数--评分页加载项集合
     * @param request
     * @param indexType 3交叉还是2专家
     * @param fileType 案件类型  0行政处罚案卷;1按日计罚案卷;2移送行政拘留案卷;3环境污染犯罪移送公安机关案卷;4申请法院强制执行案卷;5发现问题的污染源现场监督检查稽查案卷
     * @param id 主键id
     * @return
     */
    @CheckLogin(ResultTypeEnum.json)
    @RequestMapping(value = "/zjpf/getCommitIndexList")
    @ResponseBody
    public JsonResult  getCommitIndexList(HttpServletRequest request,String id,Integer handType){
    	 JsonResult json=new JsonResult();
    	 if(handType==null){
    		 handType=0;
    	 }
    	 json= zjpfService.getCommitIndexList(id,handType);
    	 return json;
    }


    /**
     * 2019专家委员--实体分数--评分页加载项集合
     * @param request
     * @param indexType 3交叉还是2专家
     * @param fileType 案件类型  0行政处罚案卷;1按日计罚案卷;2移送行政拘留案卷;3环境污染犯罪移送公安机关案卷;4申请法院强制执行案卷;5发现问题的污染源现场监督检查稽查案卷
     * @param id 主键id
     * @return
     */
    @CheckLogin(ResultTypeEnum.json)
    @RequestMapping(value = "/zjpf/getCommitEntityIndexs")
    @ResponseBody
    public JsonResult  getCommitEntityIndexList(HttpServletRequest request,String id,Integer handType){
    	 JsonResult json=new JsonResult();
    	 if(handType==null){
    		 handType=0;
    	 }
		System.out.println("开始执行页面已跳转");
    	 json= zjpfService.getCommitEntityIndexs(id,handType);
    	 return json;
    }


    /**
     * 保存专家评分（2017）
     */
    @CheckLogin(ResultTypeEnum.json)
    @CheckRepeatCommit
    @RequestMapping(value = "zjpf/saveExpertScore")
    @SysLogPoint(dbOptType=dbType.ADD,sysOptType=sysType.EXPERT_SCORED)
    @ResponseBody
    public JsonResult saveExpertScore(HttpServletRequest request, String scoringIndex, String status, Integer id){
		JsonResult json=new JsonResult();
		json= zjpfService.saveExpertScore(scoringIndex, status, id);
		try {
			//获取当前session中的用户信息
			LoginUser curLoginUser = ControllerUtil.getCurLoginUser(request);
			JSONObject jsonObject = JSONObject.parseObject(scoringIndex);
			String time="yyyy-MM-dd HH:mm:ss";
			System.out.print(curLoginUser.getUserName()+"在"+ DateUtil.getDateTime(time)+"评分,卷面评分详细为:"+jsonObject);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return json;
    }

	/**
	 * 修改"开始智能评查"的状态, 使页面固定显示ai识别的内容
	 */
	@CheckLogin(ResultTypeEnum.json)
	@CheckRepeatCommit
	@RequestMapping(value = "zjpf/updateAiView")
	@SysLogPoint(dbOptType=dbType.ADD,sysOptType=sysType.EXPERT_SCORED)
	@ResponseBody
	public int updateAiView(HttpServletRequest request, Integer id){
		zjpfAjaxMapper.updateAiView(id);
		return 1;
	}

    @CheckLogin(ResultTypeEnum.json)
    @CheckRepeatCommit
    @RequestMapping(value = "zjpf/updateEntityAiView")
    @SysLogPoint(dbOptType=dbType.ADD,sysOptType=sysType.EXPERT_SCORED)
    @ResponseBody
    public int updateEntityAiView(HttpServletRequest request, Integer id){
        zjpfAjaxMapper.updateEntityAiView(id);
        return 1;
    }


    /**
     * 保存专家分中的==实体评分（2019ADD）
     * /zjpf/saveExpertScore
     */
    @CheckLogin(ResultTypeEnum.json)
    @CheckRepeatCommit
    @RequestMapping(value = "zjpf/saveEntityScore")
    @SysLogPoint(dbOptType=dbType.ADD,sysOptType=sysType.EXPERT_SCORED)
    @ResponseBody
    public JsonResult saveExpertEntityScore(HttpServletRequest request,String scoringIndex,Integer id,String status){
    	 JsonResult json=new JsonResult();
    	 json= zjpfService.saveExperEntitytScore(scoringIndex,id,status);
    	 return json;
    }
	 /**
	 　* 保存评分项
	 　* <AUTHOR>
	 　* @date 2025-07-09 21:07:33
	 　*/
	@CheckLogin(ResultTypeEnum.json)
	@CheckRepeatCommit
	@RequestMapping(value = "zjpf/savePlusesScore")
	@SysLogPoint(dbOptType=dbType.ADD,sysOptType=sysType.EXPERT_SCORED)
	@ResponseBody
	public JsonResult savePlusesScore(HttpServletRequest request,String scoringIndex,Integer id,String status,String plusesScore){
		JsonResult json=new JsonResult();
		json= zjpfService.saveExperPlusesScore(scoringIndex,id,status,plusesScore);
		return json;
	}



	/**
	 * 专家委员打分时根据专家ID获取第一轮打分时该专家的打分详情
	 * @param request
	 * @param expertID
	 * @param fileID
	 * @return
	 */
    @CheckLogin(ResultTypeEnum.json)
	@RequestMapping("/zjpf/getExpertHanderID")
	@ResponseBody
	public JsonModel getExpertHanderID(HttpServletRequest request,String expertID,String fileID){
		JsonModel jsonModel = new JsonModel();
		try {
			HashMap<String, String> map = new HashMap<>();
			map.put("expertID", expertID);
			map.put("fileID", fileID);
			int expertHandlFileListID = zjpfService.getExpertHanderID(map);
			jsonModel.setId(Integer.valueOf(expertHandlFileListID));
			return jsonModel;
		} catch (Exception e) {
			jsonModel.setText("发生错误");
			return jsonModel;
		}

	}

    /**
     * 保存第一轮评分零分案件
     *
     * @param request
     * @param id
     * @param zeroScoreFileReviews
     * @return
     */
    @CheckLogin(ResultTypeEnum.json)
    @RequestMapping("/zjpf/saveZeroScoreFile")
	@ResponseBody
    public JsonResult saveZeroScoreFile(HttpServletRequest request,Integer id,String zeroScoreFileReviews) {
    	JsonResult json=new JsonResult();
   	 	json = zjpfService.saveZeroScoreFile(id, zeroScoreFileReviews);
   	 	return json;
    }
    /**
     *
     * @param request
     * @return
     */
    @ResponseBody
    @CheckLogin(ResultTypeEnum.json)
    @RequestMapping(value = "/zjpf/checkExpert")
    public JsonResult checkFile(HttpServletRequest request,Integer experHandID){
    	JsonResult jsonResult=new JsonResult();
    	try {
    		LoginUser areaUser= ControllerUtil.getCurLoginUser(request);

    		int userId= areaUser.getId();
    		if(userId!=0&&experHandID!=null){
    			Integer scoringNum = zjpfService.getScoringFiles(userId,experHandID);//获取正在评审中的案卷

				Integer scoringError = zjpfService.getScoringErrorFiles(userId,experHandID);//获取异常的案卷


        		if(scoringNum==null||scoringNum==0){ //无评审中案卷
					if(scoringError==null||scoringError==0){   //无异常案卷
						jsonResult.setData("1");//成功，正常评分
					}else {
						Integer[] state=new Integer[]{5};
						Integer zjpf = zjpfService.getSocringNo(userId,experHandID,state);
						if (zjpf!=null&&zjpf>0){   //大于0说明是异常案卷
							jsonResult.setData("1");
						}else {
							jsonResult.setData("3");  //异常情况
						}
						//异常情况也允许评其他案卷
//						jsonResult.setData("1");
					}

        		}else{
					Integer[] state=new Integer[]{2,3,4,6};
        			Integer zjpf = zjpfService.getSocringNo(userId,experHandID,state);
        			if (zjpf!=null&&zjpf>0){   //大于0说明是评审中
						jsonResult.setData("1");//成功，正常评分
					}else {
						jsonResult.setData("0");
					}
        		}

    		}else{
    			jsonResult.setData("2");
    		}

    		jsonResult.setResult(Const.RESULT_SUCCESS);
    		return jsonResult;
    	} catch (Exception e) {
    		e.printStackTrace();
    		jsonResult.setResult(Const.RESULT_ERROR);
    		return jsonResult;
    	}
    }

    /**
	 * 首席专家案卷合议列表（专家最终打分）==2019
	 *
	 * @param request
	 * @param areaType  案卷地区类型
	 * @param isConsider  是否合议
	 * @param fileCode  文件code
	 * @param pageNum  页数
	 * @param pageIndex  pageIndex=1 跳转专家合议列表  pageIndex=2 跳转案卷评分列表页面
	 * @param model
	 * @return
	 */
	@CheckLogin(ResultTypeEnum.page)
	@RequestMapping(value = "/zjpf/chief_list")
	public ModelAndView toChiefExpertList(HttpServletRequest request, String pageNum, String expertID, String fileCode,String scoredState) {
		ModelAndView mav = new ModelAndView("zjpf/zjpf_list_chief");
		try {
			LoginUser areaUser = ControllerUtil.getCurLoginUser(request);


			//所有首席专家账号看到的都和Chiefexp一样;所以默认获取Chiefexp的id即可
			ExpertUser expertUser= zjpfService.getChiefExpertUser();
			String expertUserID = String.valueOf(expertUser.getId());

			if("Chiefexp1".equals(areaUser.getLoginid())||"Chiefexp2".equals(areaUser.getLoginid())||"Chiefexp3".equals(areaUser.getLoginid())
				||"Chiefexp4".equals(areaUser.getLoginid())||"Chiefexp5".equals(areaUser.getLoginid())||"Chiefexp6".equals(areaUser.getLoginid())
					||"Chiefexp7".equals(areaUser.getLoginid())||"Chiefexp8".equals(areaUser.getLoginid())||"Chiefexp9".equals(areaUser.getLoginid())
					||"Chiefexp10".equals(areaUser.getLoginid())){
				expertUserID = String.valueOf(areaUser.getId());
			}


			HashMap<String, String> map = new HashMap<>();
			if (pageNum == null || "".equals(pageNum)) {
				pageNum = "1";
			}
			int pagenum = Integer.valueOf(pageNum);
			map.put("expertID", expertUserID);
			map.put("fileCode", fileCode);
			map.put("scoredState", scoredState);
			PageBean<CommitteeBean> pageBean = zjpfService.getChiefList(map, pagenum);;
			mav.addObject("pageBean", pageBean);
			mav.addObject("fileCode", fileCode);
			mav.addObject("scoredState", scoredState);
			return mav;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	/**
     * 2019首席专家评分---卷面评分--填报、编辑页面
     * @param request
     * @param id 主键的id
     * @return
     */
    @CheckLogin(ResultTypeEnum.page)
    @RequestMapping(value = "/zjpf/zjpfChiefScore")
    public ModelAndView gozjpfChiefScore(HttpServletRequest request,
    		@RequestParam(value = "id", required = false) Integer id,
    		@RequestParam(value = "status", required = false) String status,
    		@RequestParam(value = "pageNum", required = false) String pageNum){
    	 ModelAndView mav = new ModelAndView("zjpf/zjpf_score_chief");
   	 	 try {
	   		   if(id!=null && !"".equals(id)){
	   			   ExpertHandlFileListWithBLOBs expertHandlFileList =  zjpfService.findJuanAnLeiBiaoById(id);
				   List<TCDictionary> tcDictionaryList = dictionaryMapper.getDataTemp("excellentFiles");
				   List<TCDictionary> jcajDictionaryList = dictionaryMapper.getDataTemp("jcajlentFiles");
	   			   Files file = zjpfService.findFileFileNameaAndUrl(expertHandlFileList.getFileid());
	   			   int index = file.getFilename().indexOf(".");

	   			   String suffix = file.getFilename().substring(index+1);
	   			   expertHandlFileList.setFilename(file.getFilename());
	   			   expertHandlFileList.setFiletype(file.getFiletype());
	   			   expertHandlFileList.setFileurl(file.getFileurl());
	   			   expertHandlFileList.setClosed(file.getClosed());
	   			   expertHandlFileList.setSuffix(suffix);

	   			   String filetype = file.getFiletype();
	   			   String filetypeName = "";
	   			   if("0".equals(filetype)){
	   				   filetypeName = "行政处罚案卷";
	   			   }else if("1".equals(filetype)){
	   				   filetypeName = "按日计罚案卷";
	   			   }else if("6".equals(filetype)){
	   				   filetypeName = "查封扣押案卷";
	   			   }else if("7".equals(filetype)){
	   				   filetypeName = "限产停产案卷";
	   			   }else if("2".equals(filetype)){
	   				   filetypeName = "移送行政拘留案卷";
	   			   }else if("3".equals(filetype)){
	   				   filetypeName = "涉嫌犯罪移送案卷";
	   			   }else if("9".equals(filetype)){
					   filetypeName = "不予处罚案卷";
				   }

		   		   request.setAttribute("expertHandlFileList", expertHandlFileList);
		   		   request.setAttribute("pageNum", pageNum);
	     		   request.setAttribute("expertFileId", id);
	     		   request.setAttribute("filetypeName", filetypeName);
	     		   request.setAttribute("status", status);
				   request.setAttribute("tcDictionaryList",tcDictionaryList);
				   request.setAttribute("jcajDictionaryList",jcajDictionaryList);
	     		   //获取fastDFS路径
	     		   String fastDFSAddress = PropertiesHandlerUtil.getValue("fastdfs.ip", "config.fastdfs");
	     		   String downUrl = fastDFSAddress+file.getFileurl()+"?attname="+file.getFilecode()+file.getFilename().substring(index);;
				   String fastDFS = fastDFSAddress+expertHandlFileList.getFileurl();
				   request.setAttribute("fastDFS", fastDFS);
				   request.setAttribute("downUrl", downUrl);
	   		   }
	   		   return mav;
	     } catch (Exception e) {
	           e.printStackTrace();
	           return null;
	     }
	}


    /**
     * 2019首席专家评分---实体评分---填报、编辑页面
     * @param request
     * @param id 主键的id
     * @return
     */
    @CheckLogin(ResultTypeEnum.page)
    @RequestMapping(value = "/zjpf/entityChiefScore")
    public ModelAndView gozjpfChiefEntityScore(HttpServletRequest request,
    		@RequestParam(value = "id", required = false) Integer id,
    		@RequestParam(value = "pageNum", required = false) String pageNum){
    	 ModelAndView mav = new ModelAndView("zjpf/zjpf_entity_score_chief");
   	 	 try {
	   		   if(id!=null && !"".equals(id)){
	   			   ExpertHandlFileListWithBLOBs expertHandlFileList =  zjpfService.findJuanAnLeiBiaoById(id);
	   			   Files file = zjpfService.findFileFileNameaAndUrl(expertHandlFileList.getFileid());
	   			   int index = file.getFilename().indexOf(".");

	   			   String suffix = file.getFilename().substring(index+1);
	   			   expertHandlFileList.setFilename(file.getFilename());
	   			   expertHandlFileList.setFiletype(file.getFiletype());
	   			   expertHandlFileList.setFileurl(file.getFileurl());
	   			   expertHandlFileList.setClosed(file.getClosed());
	   			   expertHandlFileList.setSuffix(suffix);

	   			   String filetype = file.getFiletype();
	   			   String filetypeName = "";
	   			   if("0".equals(filetype)){
	   				   filetypeName = "行政处罚案卷";
	   			   }else if("1".equals(filetype)){
	   				   filetypeName = "按日计罚案卷";
	   			   }else if("6".equals(filetype)){
	   				   filetypeName = "查封扣押案卷";
	   			   }else if("7".equals(filetype)){
	   				   filetypeName = "限产停产案卷";
	   			   }else if("2".equals(filetype)){
	   				   filetypeName = "移送行政拘留案卷";
	   			   }else if("3".equals(filetype)){
	   				   filetypeName = "涉嫌犯罪移送案卷";
	   			   }else if("9".equals(filetype)){
					   filetypeName = "不予处罚案卷";
				   }

		   		   request.setAttribute("expertHandlFileList", expertHandlFileList);
		   		   request.setAttribute("pageNum", pageNum);
	     		   request.setAttribute("expertFileId", id);
	     		   request.setAttribute("filetypeName", filetypeName);

	     		   //获取fastDFS路径
	     		   String fastDFSAddress = PropertiesHandlerUtil.getValue("fastdfs.ip", "config.fastdfs");
	     		   String downUrl = fastDFSAddress+file.getFileurl()+"?attname="+file.getFilecode()+file.getFilename().substring(index);;
				   String fastDFS = fastDFSAddress+expertHandlFileList.getFileurl();
				   request.setAttribute("fastDFS", fastDFS);
				   request.setAttribute("downUrl", downUrl);
	   		   }
	   		   return mav;
	     } catch (Exception e) {
	           e.printStackTrace();
	           return null;
	     }
	}

    /**
     * 2019首席专家评分---卷面评分---查看页面
     * @param request
     * @param id 主键的id
     * @return
     */
    @CheckLogin(ResultTypeEnum.page)
    @RequestMapping(value = "/zjpf/zjpfChiefView")
    public ModelAndView gozjpfChiefView(HttpServletRequest request,
    		@RequestParam(value = "id", required = false) Integer id,
    		@RequestParam(value = "status", required = false) String status,
    		@RequestParam(value = "pageNum", required = false) String pageNum){
    	 ModelAndView mav = new ModelAndView("zjpf/zjpf_view_chief");
   	 	 try {
	   		   if(id!=null && !"".equals(id)){
	   			   ExpertHandlFileListWithBLOBs expertHandlFileList =  zjpfService.findJuanAnLeiBiaoById(id);
	   			   Files file = zjpfService.findFileFileNameaAndUrl(expertHandlFileList.getFileid());
	   			   int index = file.getFilename().indexOf(".");

	   			   String suffix = file.getFilename().substring(index+1);
	   			   expertHandlFileList.setFilename(file.getFilename());
	   			   expertHandlFileList.setFiletype(file.getFiletype());
	   			   expertHandlFileList.setFileurl(file.getFileurl());
	   			   expertHandlFileList.setClosed(file.getClosed());
	   			   expertHandlFileList.setSuffix(suffix);

	   			   String filetype = file.getFiletype();
	   			   String filetypeName = "";
	   			   if("0".equals(filetype)){
	   				   filetypeName = "行政处罚案卷";
	   			   }else if("1".equals(filetype)){
	   				   filetypeName = "按日计罚案卷";
	   			   }else if("6".equals(filetype)){
	   				   filetypeName = "查封扣押案卷";
	   			   }else if("7".equals(filetype)){
	   				   filetypeName = "限产停产案卷";
	   			   }else if("2".equals(filetype)){
	   				   filetypeName = "移送行政拘留案卷";
	   			   }else if("3".equals(filetype)){
	   				   filetypeName = "涉嫌犯罪移送案卷";
	   			   }else if("9".equals(filetype)){
					   filetypeName = "不予处罚案卷";
				   }


		   		   request.setAttribute("expertHandlFileList", expertHandlFileList);
		   		   request.setAttribute("pageNum", pageNum);
	     		   request.setAttribute("expertFileId", id);
	     		   request.setAttribute("filetypeName", filetypeName);
	     		   request.setAttribute("status", status);

	     		   //获取fastDFS路径
	     		   String fastDFSAddress = PropertiesHandlerUtil.getValue("fastdfs.ip", "config.fastdfs");
	     		   String downUrl = fastDFSAddress+file.getFileurl()+"?attname="+file.getFilecode()+file.getFilename().substring(index);;
				   String fastDFS = fastDFSAddress+expertHandlFileList.getFileurl();
				   request.setAttribute("fastDFS", fastDFS);
				   request.setAttribute("downUrl", downUrl);
	   		   }
	   		   return mav;
	     } catch (Exception e) {
	           e.printStackTrace();
	           return null;
	     }
	}

    /**
     * 2019首席专家评分---实体评分--查看页面
     * @param request
     * @param id 主键的id
     * @return
     */
    @CheckLogin(ResultTypeEnum.page)
    @RequestMapping(value = "/zjpf/entityChiefView")
    public ModelAndView gozjpfChiefEntityView(HttpServletRequest request,
    		@RequestParam(value = "id", required = false) Integer id,
    		@RequestParam(value = "pageNum", required = false) String pageNum){
    	 ModelAndView mav = new ModelAndView("zjpf/zjpf_entity_view_chief");
   	 	 try {
	   		   if(id!=null && !"".equals(id)){
	   			   ExpertHandlFileListWithBLOBs expertHandlFileList =  zjpfService.findJuanAnLeiBiaoById(id);
	   			   Files file = zjpfService.findFileFileNameaAndUrl(expertHandlFileList.getFileid());
	   			   int index = file.getFilename().indexOf(".");

	   			   String suffix = file.getFilename().substring(index+1);
	   			   expertHandlFileList.setFilename(file.getFilename());
	   			   expertHandlFileList.setFiletype(file.getFiletype());
	   			   expertHandlFileList.setFileurl(file.getFileurl());
	   			   expertHandlFileList.setClosed(file.getClosed());
	   			   expertHandlFileList.setSuffix(suffix);

	   			   String filetype = file.getFiletype();
	   			   String filetypeName = "";
	   			   if("0".equals(filetype)){
	   				   filetypeName = "行政处罚案卷";
	   			   }else if("1".equals(filetype)){
	   				   filetypeName = "按日计罚案卷";
	   			   }else if("6".equals(filetype)){
	   				   filetypeName = "查封扣押案卷";
	   			   }else if("7".equals(filetype)){
	   				   filetypeName = "限产停产案卷";
	   			   }else if("2".equals(filetype)){
	   				   filetypeName = "移送行政拘留案卷";
	   			   }else if("3".equals(filetype)){
	   				   filetypeName = "涉嫌犯罪移送案卷";
	   			   }else if("9".equals(filetype)){
					   filetypeName = "不予处罚案卷";
				   }

		   		   request.setAttribute("expertHandlFileList", expertHandlFileList);
		   		   request.setAttribute("pageNum", pageNum);
	     		   request.setAttribute("expertFileId", id);
	     		   request.setAttribute("filetypeName", filetypeName);

	     		   //获取fastDFS路径
	     		   String fastDFSAddress = PropertiesHandlerUtil.getValue("fastdfs.ip", "config.fastdfs");
	     		   String downUrl = fastDFSAddress+file.getFileurl()+"?attname="+file.getFilecode()+file.getFilename().substring(index);
				   String fastDFS = fastDFSAddress+expertHandlFileList.getFileurl();
				   request.setAttribute("fastDFS", fastDFS);
				   request.setAttribute("downUrl", downUrl);
	   		   }
	   		   return mav;
	     } catch (Exception e) {
	           e.printStackTrace();
	           return null;
	     }
	}

    /**
     * 2019---保存-稽查专家评分
     */
    @CheckLogin(ResultTypeEnum.json)
    @CheckRepeatCommit
    @RequestMapping(value = "zjpf/saveJCExpertScore")
    @SysLogPoint(dbOptType=dbType.ADD,sysOptType=sysType.EXPERT_SCORED)
    @ResponseBody
    public JsonResult saveJCExpertScore(HttpServletRequest request, String scoringIndex, String status, Integer id){
    	 JsonResult json=new JsonResult();
    	 json= zjpfService.saveJcExpertScore(scoringIndex, status, id);
    	 return json;
    }

	/**
	 * 查询实体项 以及小项
	 * @param request
	 * @param id
	 * @return
	 */
	@CheckLogin(ResultTypeEnum.json)
	@RequestMapping(value = "zjpf/selectEntity")
	@ResponseBody
	public JsonResult selectEntity(HttpServletRequest request,String id,Integer index){
		logger.info("查询方法进来了  id"+id);
		JsonResult json = new JsonResult();
		try {

			if(!"".equals(id)){
				List<ExpertHandlIndexScore> list= zjpfService.selectEntity(id);

				if(!list.isEmpty()){
					ExpertHandlItemScore expertHandlItemScore = new ExpertHandlItemScore();
					List<ExpertHandlItemScore> itemList = new ArrayList<>();
					//大项
					for (ExpertHandlIndexScore expertHandlIndex :list ) {
						List<ExpertHandlItemScore> expertHandlItemScoreList = expertHandlIndex.getExpertHandlItemScoreList();

						String indexname = expertHandlIndex.getIndexname();
						Integer indexId = expertHandlIndex.getId();
						Integer sort = expertHandlIndex.getSort();
						//小项
						for (ExpertHandlItemScore expertHandlItem : expertHandlItemScoreList ) {

							expertHandlItemScore=expertHandlItem;
							//指标id
							expertHandlItemScore.setNormId(expertHandlIndex.getIndexid());
							expertHandlItemScore.setIndexName(indexname);
							expertHandlItemScore.setIndexId(indexId);
							expertHandlItemScore.setSort(sort);
							itemList.add(expertHandlItemScore);
						}
					}
					int size = itemList.size();
					if(size > index){
						json.setCode("200");
						json.setData(itemList.get(index));
						json.setResult(Const.RESULT_SUCCESS);
						json.setMessage("查询成功!");
					}else{
						//保存实体分数 页面的提交按钮也有这个逻辑
						ExpertHandlIndexScore expertHandlIndexScore = new ExpertHandlIndexScore();
						aa:for (ExpertHandlIndexScore expertHandlIndex :list ) {
							for (ExpertHandlItemScore item : expertHandlIndex.getExpertHandlItemScoreList()) {
								//String s = item.getScore().toString();
								long floatScore = item.getScore().longValue();
								if (item.getScore()!=null && floatScore == 1) {
									expertHandlIndexScore.setResultScore(1F);
									expertHandlIndexScore.setId(expertHandlIndex.getId());
									expertHandlIndexScoreMapper.updateByPrimaryKeySelective(expertHandlIndexScore);
									continue aa;
								} else {
									expertHandlIndexScore.setResultScore(0F);
									expertHandlIndexScore.setId(expertHandlIndex.getId());
									expertHandlIndexScoreMapper.updateByPrimaryKeySelective(expertHandlIndexScore);
								}

							}
						}
						ExpertHandlFileList expertHandlFileList = new ExpertHandlFileList();
						for(ExpertHandlItemScore item:itemList){
							long floatScores = item.getScore().longValue();
							if(floatScores == 1){
								expertHandlFileList.setEntityscore(0F);
								expertHandlFileList.setId(Integer.parseInt(id));
								expertHandlFileListMapper.updateByPrimaryKeySelective(expertHandlFileList);
							}
						}

						json.setCode("null");
						json.setData(null);
						json.setResult(Const.RESULT_NULL);
						json.setMessage(null);
					}
				}else{
					logger.info("查询出错 结果为空！");
				}

			}else{
				logger.info("传参出错id为空  id"+id);
			}


		}catch(Exception e){
			//ddd
			logger.error(  "查询实体出错",e);

		}
		return json;
	}

	/**
	 * 保存 大项 小项
	 * @param request
	 * @param
	 * @param id
	 * @param
	 * @return
	 */
	@CheckLogin(ResultTypeEnum.json)
	@CheckRepeatCommit
	@RequestMapping(value = "zjpf/svaeIndexEntity")
	@SysLogPoint(dbOptType=dbType.ADD,sysOptType=sysType.EXPERT_SCORED)
	@ResponseBody
	public JsonResult svaeIndexEntity(HttpServletRequest request,Integer id,Integer normId,Integer itemId,Integer score,String commeStr,String commeStrOne,String commeStrTwo, String commeStrThree,String commeStrFour,
									  String commeStrFive,String commeStrSix, String commeStrSeven,String commeStrEight){
		JsonResult json=new JsonResult();
		json= zjpfService.svaeIndexEntity(id,normId,itemId,score,commeStr,commeStrOne,commeStrTwo,commeStrThree,commeStrFour,commeStrFive,commeStrSix,commeStrSeven,commeStrEight);
		return json;
	}



	/**
	 * 2019年专家评分--列表
	 * @param request
	 * @return
	 */
	@CheckLogin(ResultTypeEnum.page)
	@SysLogPoint(dbOptType=dbType.SEARCH,sysOptType=sysType.EXPERT_SCORED)
	@RequestMapping(value = "/zjpf/notGood")
	@ResponseBody
	public JsonResult noGood(HttpServletRequest request,
							 @RequestParam(value = "id", required = false) Integer id,
							 @RequestParam(value = "fileMaterialsNums") String fileMaterialsNums,
							 @RequestParam(value = "fileMaterialsDocs") String fileMaterialsDocs,
							 @RequestParam(value = "fileMaterialsSups") String fileMaterialsSups){
		try {
			JsonResult json=new JsonResult();
			json= zjpfService.saveNotGood(id,fileMaterialsNums,fileMaterialsDocs,fileMaterialsSups);
			return json;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}

	}


}


