package com.changneng.sa.controller;

import com.changneng.sa.aop.SysLogPoint;
import com.changneng.sa.bean.*;
import com.changneng.sa.gloableCtrl.CheckLogin;
import com.changneng.sa.gloableCtrl.ResultTypeEnum;
import com.changneng.sa.service.FileExtraction;
import com.changneng.sa.service.FilesDrawActivityService;
import com.changneng.sa.service.RandomCaseFile2024DownService;
import com.changneng.sa.service.RandomCaseFileService;
import com.changneng.sa.util.*;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
　* 抽取案件活动
　* <AUTHOR>
　* @date 2023-08-07 16:08:39
　*/
@Controller
@RequestMapping("/filesDrawActivity")
public class FilesDrawActivityController {

    @Autowired
    private FilesDrawActivityService filesDrawActivityService;
    @Autowired
    private FileExtraction fileExtraction;
    @Autowired
    private RandomCaseFileService randomCaseFileService;
    @Autowired
    private RandomCaseFile2024DownService randomCaseFile2024DownService;

     /**
     　* 2024 案卷抽取,并跳转抽取后的统计页面
     　* <AUTHOR>
     　* @date 2024-05-11 15:05:58
     　*/
    @CheckLogin(ResultTypeEnum.page)
    @RequestMapping("/goCaseFile2024")
    public ModelAndView goCaseFile2024(HttpServletRequest request,
                                       @RequestParam(value = "activityId", required = false) Integer activityId,
                                       @RequestParam(value = "provinceName", required = false) String provinceName ,
                                       @RequestParam(value = "cityName", required = false) String cityName ){
        ModelAndView mv = new ModelAndView("ajcq/case_file_2024_down");
        List<FilesVo> dataList = randomCaseFileService.selectResultCount(activityId,provinceName);
        mv.addObject("dataList",dataList);
        mv.addObject("activityId",activityId);
        mv.addObject("provinceName",provinceName);
        mv.addObject("cityName",cityName);
        return mv;
    }
     /**
     　* 抽取合格情况导出
     　* <AUTHOR>
     　* @date 2024-05-16 21:05:19
     　*/
    @CheckLogin(ResultTypeEnum.json)
    @RequestMapping(value = "/caseFileExport")
    public void caseFileExport(HttpServletRequest request,HttpServletResponse response,
                                     @RequestParam(value = "provinceName", required = false) String provinceName ,
                                     @RequestParam(value = "cityName", required = false) String cityName,
                                     @RequestParam(value = "activityId", required = false) Integer activityId
    ) throws Exception {
        String excelName = "抽取合格情况";
        //组装查询信息
        FilesVo filesVo = new FilesVo();
        filesVo.setProvinceName(provinceName);
        filesVo.setCityName(cityName);
        filesVo.setActivityId(activityId);

        //查询案卷抽查统计情况
        List<FilesVo> dataList = randomCaseFileService.selectResultCountExport(activityId);
        //表头
        String[] filesHead = ExeclBeanUtil.caseFile2024Down;
        //需要显示在excel中的参数对应的值
        String[] dbEntityVeto = ExeclBeanUtil.caseFileColumn2024Down;
        List<Map<String, Object>> maps = ExcelUtil.listConvert(dataList);
        //导出
        ExcelUtil.exportFilesExcel(request, response, maps, excelName,filesHead,dbEntityVeto);

    }



     /**
     　* 2024年上半年案卷抽取
     　* <AUTHOR>
     　* @date 2024-05-15 20:05:34
     　*/
    @CheckLogin(ResultTypeEnum.page)
    @RequestMapping("/getCaseFile2024")
    @ResponseBody
    public JsonResult getCaseFile2024(HttpServletRequest request,
                                       @RequestParam(value = "activityId", required = false) Integer activityId){

        JsonResult jsonResult = randomCaseFileService.get2024UpRandomFiles(request, activityId);

        return jsonResult;
    }

    @CheckLogin(ResultTypeEnum.page)
    @RequestMapping("/getCaseFile2024Down")
    @ResponseBody
    public JsonResult getCaseFile2024Down(HttpServletRequest request,
                                      @RequestParam(value = "activityId", required = false) Integer activityId){

        JsonResult jsonResult = randomCaseFile2024DownService.get2024DownRandomFiles(request, activityId);

        return jsonResult;
    }


     /**
      *
     　* 跳转案卷抽取页面(活动页面)
     　* <AUTHOR>
     　* @date 2023-08-07 17:08:41
     　*/
    @CheckLogin(ResultTypeEnum.page)
    @RequestMapping("/goActivityList")
    public ModelAndView goActivityList(HttpServletRequest request,
                                       @RequestParam(value = "pageNum", required = false,defaultValue = "1") Integer pageNum){
        ModelAndView mv = new ModelAndView("ajcq/ajcq_list");
        PageBean<FilesDrawActivity> dataList = filesDrawActivityService.getActivityList(pageNum);
        mv.addObject("dataList",dataList);
        return mv;
    }
    @CheckLogin(ResultTypeEnum.page)
    @RequestMapping("/extractMethod")
    public ModelAndView extractMethod(){
        ModelAndView mv = new ModelAndView("ajcq/ajcq_list");
        fileExtraction.getSecondFileExtraction(0);
        return mv;
    }
    @CheckLogin(ResultTypeEnum.page)
    @RequestMapping(value = "/extractDetail")
    @ResponseBody
    public JsonResult extractDetail(HttpServletRequest request, Integer type ){
        JsonResult jsonResult = new JsonResult();
        try{
            fileExtraction.getSecondFileExtraction(type);
            jsonResult.setResult(Const.RESULT_SUCCESS);
            jsonResult.setMessage("抽取成功");
        }catch (Exception e){
            jsonResult.setResult(Const.RESULT_ERROR);
            jsonResult.setMessage("抽取失败："+e.getMessage());
        }
        return jsonResult;
    }

     /**
     　* 新增案卷抽查活动
      * @param activityName 活动名称
     　* <AUTHOR>
     　* @date 2023-08-08 18:08:27
     　*/
    @CheckLogin(ResultTypeEnum.page)
    @RequestMapping(value = "/addActivity")
    @ResponseBody
    public JsonResult addActivity(HttpServletRequest request,
                                  @RequestParam(value = "activityName", required = false) String activityName ,
                                  @RequestParam(value = "endTime", required = false)@DateTimeFormat(pattern = "yyyy-MM-dd") Date endTime ,
                                  @RequestParam(value = "startTime", required = false)@DateTimeFormat(pattern = "yyyy-MM-dd") Date startTime
    ){
        //新增抽查活动
        JsonResult jsonResult = filesDrawActivityService.insertActivity(request,activityName,startTime,endTime);
        return jsonResult;
    }
     /**
     　* 编辑活动名称
     　* <AUTHOR>
     　* @date 2023-08-10 16:08:35
     　*/
    @CheckLogin(ResultTypeEnum.page)
    @RequestMapping(value = "/editActivity")
    @ResponseBody
    public JsonResult editActivity(HttpServletRequest request,
                                   @RequestParam(value = "id", required = false) Integer id,
                                  @RequestParam(value = "activityName", required = false) String activityName ,
                                   @RequestParam(value = "endTime", required = false)@DateTimeFormat(pattern = "yyyy-MM-dd") Date endTime ,
                                   @RequestParam(value = "startTime", required = false)@DateTimeFormat(pattern = "yyyy-MM-dd") Date startTime){
        //修改抽查活动
        JsonResult jsonResult = filesDrawActivityService.updateActivity(request,id,activityName,startTime,endTime);
        return jsonResult;
    }

     /**
     　* 删除活动
      @param activityId 活动ID
     　* <AUTHOR>
     　* @date 2023-08-10 16:08:11
     　*/
    @CheckLogin(ResultTypeEnum.page)
    @RequestMapping(value = "/removeActivity")
    @ResponseBody
    public JsonResult removeActivity(HttpServletRequest request,
                                   @RequestParam(value = "activityId", required = false) Integer activityId ){
        //删除抽查活动
        JsonResult jsonResult = filesDrawActivityService.removeActivityById(request,activityId);
        return jsonResult;
    }
     /**
     　* 跳转抽取案卷页面,并查询案卷抽取统计情况
      * 案卷抽取明细
      @param activityId 活动ID
     　* <AUTHOR>
     　* @date 2023-08-11 11:08:08
     　*/
    @CheckLogin(ResultTypeEnum.page)
    @RequestMapping("/goCqList")
    public ModelAndView goRandomFiles(HttpServletRequest request,
                                           @RequestParam(value = "activityId", required = false) Integer activityId
    ){
        ModelAndView mv = new ModelAndView("ajcq/cq_list");
        //查询案卷抽查统计情况
        List<FilesDrawVo> dataList = filesDrawActivityService.getCountByActivityId(activityId);
        FilesDrawVo total = new FilesDrawVo();
        if (CollectionUtils.isNotEmpty(dataList)){
            // 使用 Optional.ofNullable 判断 p.getPenalty() 是否为 null
            // 如果 getPenalty() 方法返回 null，则使用 orElse(0) 指定默认值为 0，如果不为 null，则使用 getPenalty() 方法返回的值。
            total.setPenalty(dataList.stream() .mapToInt(p -> Optional.ofNullable(p.getPenalty()).orElse(0)).sum());
            total.setDailyPenalty(dataList.stream() .mapToInt(p -> Optional.ofNullable(p.getDailyPenalty()).orElse(0)).sum());
            total.setDetention(dataList.stream() .mapToInt(p -> Optional.ofNullable(p.getDetention()).orElse(0)).sum());
            total.setCrimes(dataList.stream() .mapToInt(p -> Optional.ofNullable(p.getCrimes()).orElse(0)).sum());
            total.setSeizureDetain(dataList.stream() .mapToInt(p -> Optional.ofNullable(p.getSeizureDetain()).orElse(0)).sum());
            total.setStopProduction(dataList.stream() .mapToInt(p -> Optional.ofNullable(p.getStopProduction()).orElse(0)).sum());
            total.setNotPunishable(dataList.stream() .mapToInt(p -> Optional.ofNullable(p.getNotPunishable()).orElse(0)).sum());
            total.setTotal(dataList.stream() .mapToInt(p -> Optional.ofNullable(p.getTotal()).orElse(0)).sum());
        }
        mv.addObject("dataList",dataList);
        mv.addObject("total",total);
        mv.addObject("activityId",activityId);
        return mv;
    }
     /**
     　* 2024案卷抽取明细导出
     　* <AUTHOR>
     　* @date 2024-05-16 21:05:20
     　*/
    @CheckLogin(ResultTypeEnum.json)
    @RequestMapping(value = "/extractDetailsExport")
    public void extractDetailsExport(HttpServletRequest request,HttpServletResponse response,
                                     @RequestParam(value = "provinceName", required = false) String provinceName ,
                                     @RequestParam(value = "cityName", required = false) String cityName,
                                     @RequestParam(value = "activityId", required = false) Integer activityId
    ) throws Exception {
        String excelName = "案卷抽取明细";
        //组装查询信息
        FilesVo filesVo = new FilesVo();
        filesVo.setProvinceName(provinceName);
        filesVo.setCityName(cityName);
        filesVo.setActivityId(activityId);

        //查询案卷抽查统计情况
        List<FilesDrawVo> dataList = filesDrawActivityService.getCountByActivityId(activityId);
        //表头
        String[] filesHead = ExeclBeanUtil.extractDetails2024;
        //需要显示在excel中的参数对应的值
        String[] dbEntityVeto = ExeclBeanUtil.extractDetailsColumn2024;
        List<Map<String, Object>> maps = ExcelUtil.listConvert(dataList);


        //导出
        ExcelUtil.exportFilesExcel(request, response, maps, excelName,filesHead,dbEntityVeto);


    }


     /**
     　* 抽取案卷 2023年年底大练兵抽取
      @param activityId 活动ID
     　* <AUTHOR>
     　* @date 2023-08-10 16:08:25
     　*/
    @CheckLogin(ResultTypeEnum.page)
    @RequestMapping(value = "/getRandomFiles")
    @ResponseBody
    public JsonResult getRandomFiles(HttpServletRequest request,
                                   @RequestParam(value = "activityId", required = false) Integer activityId ){
        //修改抽查活动
        JsonResult jsonResult = filesDrawActivityService.getRandomFiles(request,activityId);
        return jsonResult;
    }

     /**
     　* 抽取案卷 --- 暂时弃用
      @param activityId 活动ID
      @param type 按第几种情况抽取 1完整版,2可抽取本市未结案,3可抽取省内未结案
     　* <AUTHOR>
     　* @date 2023-08-10 16:08:25
     　*/
     /*
    @CheckLogin(ResultTypeEnum.page)
    @RequestMapping(value = "/getRandomFiles2")
    @ResponseBody
    public JsonResult getRandomFiles2(HttpServletRequest request,
                                     @RequestParam(value = "type", required = false) Integer type,
                                     @RequestParam(value = "activityId", required = false) Integer activityId ){

        System.err.println(type);
        System.err.println(activityId);
        JsonResult jsonResult = null;

        *//*jsonResult = filesDrawActivityService.getRandomFilesByType(request,activityId,type);*//*

        if (type==1){
            //完整抽查活动
            jsonResult = filesDrawActivityService.getRandomFiles(request,activityId);
        }else if (type==2){

        }else if (type==3){

        }else {
            jsonResult.setResult(Const.RESULT_ERROR);
            jsonResult.setMessage("抽取异常,传参失败,请检查程序");
        }


        return jsonResult;
    }*/
     /**
     　* 跳转案卷管理页面,查询当前活动下的已抽取的案卷
       *    @param activityId 活动ID
       *   @param pageNum 分页信息
     　* <AUTHOR>
     　* @date 2023-08-10 18:08:39
     　*/
    @CheckLogin(ResultTypeEnum.page)
    @RequestMapping("/goAjManage")
    public ModelAndView goAjManage(HttpServletRequest request,
                                       @RequestParam(value = "pageNum", required = false,defaultValue = "1") Integer pageNum,
                                           @RequestParam(value = "activityId", required = false) Integer activityId ,
                                           @RequestParam(value = "fileCode", required = false) String fileCode ,
                                            @RequestParam(value = "fileName", required = false) String fileName,
                                           @RequestParam(value = "fileType", required = false) Integer fileType ,
                                   @RequestParam(value = "caseState", required = false) Integer caseState
    ){
        ModelAndView mv = new ModelAndView("ajcq/aj_manage");
        //组装信息
        FilesVo filesVo = new FilesVo();
        filesVo.setActivityId(activityId);
        filesVo.setFileCode(fileCode);
        filesVo.setFileName(fileName);
        filesVo.setFileType(fileType);
        filesVo.setClosed(caseState);
        PageBean<FilesVo> dataList = filesDrawActivityService.getFilesByActivityId(pageNum,filesVo);
        mv.addObject("dataList",dataList);
        mv.addObject("activityId",activityId);
        //筛选条件回显
        mv.addObject("fileCode",fileCode);
        mv.addObject("fileName",fileName);
        mv.addObject("fileType",fileType);
        mv.addObject("caseState",caseState);

        return mv;
    }

     /**
     　* 获取并检测附件信息
      @param fileId 案卷主键ID
     　* <AUTHOR>
     　* @date 2023-08-10 19:08:20
     　*/
    @RequestMapping(value = "/checkFileUrl")
    @ResponseBody
    public JsonResult checkFileUrl(HttpServletRequest request,
                               @RequestParam(value = "fileId", required = false) Integer fileId){
        JsonResult jsonResult = new JsonResult();
        try {
            if (fileId != null){
                BufferedOutputStream output = null;
                Files fileInfo = filesDrawActivityService.getFilesByFileId(fileId);
                if (fileInfo != null ){
                    if (fileInfo.getFileurl()!=null && !fileInfo.getFileurl().equals("") ){
                        String filename = fileInfo.getFilename();
                        if (filename.equals("") || filename.lastIndexOf(".") == -1){
                            jsonResult.setResult(Const.RESULT_ERROR);
                            jsonResult.setMessage("案卷名称后缀异常");
                        }else {
                            jsonResult.setResult(Const.RESULT_SUCCESS);
                            jsonResult.setMessage("获取文件成功");
                        }
                    }else {
                        jsonResult.setResult(Const.RESULT_ERROR);
                        jsonResult.setMessage("案卷文件不存在,请重新上传");
                    }
                }else {
                    jsonResult.setResult(Const.RESULT_ERROR);
                    jsonResult.setMessage("案卷不存在");
                }
            }else {
                jsonResult.setResult(Const.RESULT_ERROR);
                jsonResult.setMessage("案卷ID不能为为空");
            }
        }catch (Exception e){
            jsonResult.setResult(Const.RESULT_ERROR);
            jsonResult.setMessage("保存失败");
            jsonResult.setData(e.getMessage());
        }

        return jsonResult;
    }

    /**
     　* 单个案卷下载
     @param fileId 案卷主键ID
     　* <AUTHOR>
     　* @date 2023-08-10 19:08:20
     　*/
    @SysLogPoint(dbOptType= SysLogPoint.dbType.DOWNLOAD,sysOptType= SysLogPoint.sysType.COMMON_OPT)
    @RequestMapping(value = "/fileDownload")
    public void fileDownload(HttpServletRequest request,HttpServletResponse response,
                               @RequestParam(value = "fileId", required = false) Integer fileId){
        try {
            BufferedOutputStream output = null;
            if(fileId != null ){
                Files files = filesDrawActivityService.getFilesByFileId(fileId);
                if(files != null && !files.getFileurl().equals("") && files.getFileurl()!=null){
                    String filename = files.getFilename();
                    String suffix = filename.substring(filename.lastIndexOf('.'));
                    FastDFSClient fastDFSClient = new FastDFSClient("classpath:config/fdfs_client.conf");
                    FileUtil.setFileDownloadHeader(request, response,files.getFilecode()+suffix);
                    output =  new BufferedOutputStream(response.getOutputStream());
                    fastDFSClient.download_file(files.getFileurl(),output);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

     /**
     　* 导出案卷列表Excel
     　* <AUTHOR>
     　* @date 2023-08-10 19:08:02
     　*/
    @CheckLogin(ResultTypeEnum.json)
    @RequestMapping(value = "/filesExportExcel")
    public void filesExportExcel(HttpServletRequest request,HttpServletResponse response,
                                      @RequestParam(value = "activityId", required = false) Integer activityId ,
                                 @RequestParam(value = "fileCode", required = false) String fileCode ,
                                 @RequestParam(value = "fileName", required = false) String fileName,
                                 @RequestParam(value = "caseState", required = false) Integer caseState,
                                      @RequestParam(value = "fileType", required = false) Integer fileType ) throws Exception {
        //组装信息
        FilesVo filesVo = new FilesVo();
        filesVo.setActivityId(activityId);
        filesVo.setFileCode(fileCode);
        filesVo.setFileName(fileName);
        filesVo.setFileType(fileType);
        filesVo.setClosed(caseState);
        String excelName = "抽取的案卷列表";
        //表头
        String[] filesHead = ExeclBeanUtil.fileList2024;
        //需要显示在excel中的参数对应的值
        String[] dbEntityVeto = ExeclBeanUtil.fileListColumn2024;
        //查询要导出的数据
        List<FilesVo> fileList = filesDrawActivityService.selectFileList(filesVo);
        List<Map<String, Object>> maps = ExcelUtil.listConvert(fileList);

        //导出
        ExcelUtil.exportFilesExcel(request, response, maps, excelName,filesHead,dbEntityVeto);


    }
    @CheckLogin(ResultTypeEnum.json)
    @RequestMapping(value = "/filesExportExcelBak")
    public void filesExportExcelBak(HttpServletRequest request,HttpServletResponse response,
                                      @RequestParam(value = "activityId", required = false) Integer activityId ,
                                 @RequestParam(value = "fileCode", required = false) String fileCode ,
                                 @RequestParam(value = "fileName", required = false) String fileName,
                                 @RequestParam(value = "caseState", required = false) Integer caseState,
                                      @RequestParam(value = "fileType", required = false) Integer fileType ) throws Exception {
        //组装信息
        FilesVo filesVo = new FilesVo();
        filesVo.setActivityId(activityId);
        filesVo.setFileCode(fileCode);
        filesVo.setFileName(fileName);
        filesVo.setFileType(fileType);
        filesVo.setClosed(caseState);
        String excelName = "抽取的案卷列表";
        //表头
        String[] filesHead = ExeclBeanUtil.fileList;
        //需要显示在excel中的参数对应的值
        String[] dbEntityVeto = ExeclBeanUtil.fileListColumn;
        //查询要导出的数据
        List<FilesVo> fileList = filesDrawActivityService.selectFileList(filesVo);
        List<Map<String, Object>> maps = ExcelUtil.listConvert(fileList);

        //导出
        ExcelUtil.exportFilesExcel(request, response, maps, excelName,filesHead,dbEntityVeto);


    }

     /**
     　* 跳转案卷汇总页面,获取所有案卷统计
     　* <AUTHOR>
     　* @date 2023-09-04 14:09:27
      *
      * 2024-05-09 活动查看按钮
     　*/
    @CheckLogin(ResultTypeEnum.page)
    @RequestMapping("/goAllFiles")
    public ModelAndView goAllFiles(HttpServletRequest request,
                                   @RequestParam(value = "pageNum", required = false,defaultValue = "1") Integer pageNum,
                                   @RequestParam(value = "activityId") Integer activityId,
                                   @RequestParam(value = "provinceName", required = false) String provinceName ,
                                   @RequestParam(value = "cityName", required = false) String cityName  ){
        ModelAndView mv = new ModelAndView("ajcq/ajhz_list");
        //活动信息
        FilesDrawActivity activityInfo = filesDrawActivityService.getActivityInfo(activityId);
        //组装信息
        FilesVo filesVo = new FilesVo();
        filesVo.setProvinceName(provinceName);
        filesVo.setCityName(cityName);
        filesVo.setStartTime(activityInfo.getStartTime());
        filesVo.setEndTime(activityInfo.getEndTime());

        filesVo.setCityName(cityName);

        PageBean<FilesVo> dataList =  filesDrawActivityService.getAllFiles(pageNum,filesVo);

        mv.addObject("dataList",dataList);
        mv.addObject("provinceName",provinceName);
        mv.addObject("cityName",cityName);
        mv.addObject("activityId",activityId);
        mv.addObject("activitySate",activityInfo.getActivityState());
        mv.addObject("startTime",activityInfo.getStartTime());
        mv.addObject("endTime",activityInfo.getEndTime());
        return mv;
    }
    /**
     　* 导出案卷列表Excel
     　* <AUTHOR>
     　* @date 2023-08-10 19:08:02
     　*/
    @CheckLogin(ResultTypeEnum.json)
    @RequestMapping(value = "/allFilesExport")
    public void allFilesExport(HttpServletRequest request,HttpServletResponse response,
                                 @RequestParam(value = "provinceName", required = false) String provinceName ,
                                 @RequestParam(value = "cityName", required = false) String cityName,
                                 @RequestParam(value = "activityId", required = false) Integer activityId
                               ) throws Exception {
        //活动信息
        FilesDrawActivity activityInfo = filesDrawActivityService.getActivityInfo(activityId);
        //组装信息
        FilesVo filesVo = new FilesVo();
        filesVo.setProvinceName(provinceName);
        filesVo.setCityName(cityName);
        filesVo.setStartTime(activityInfo.getStartTime());
        filesVo.setEndTime(activityInfo.getEndTime());

        String excelName = "待抽取案卷汇总统计";
        //表头
        String[] filesHead = ExeclBeanUtil.allFileList2024;
        //需要显示在excel中的参数对应的值
        String[] dbEntityVeto = ExeclBeanUtil.allFileListColumn2024;
        //查询要导出的数据
        List<FilesVo> fileList = filesDrawActivityService.getAllFilesExport(filesVo);
        List<Map<String, Object>> maps = ExcelUtil.listConvert(fileList);

        //导出
        ExcelUtil.exportFilesExcel(request, response, maps, excelName,filesHead,dbEntityVeto);


    }

    @CheckLogin(ResultTypeEnum.json)
    @RequestMapping(value = "/allFilesExportBak")
    public void allFilesExportBak(HttpServletRequest request,HttpServletResponse response,
                                 @RequestParam(value = "provinceName", required = false) String provinceName ,
                                 @RequestParam(value = "cityName", required = false) String cityName ) throws Exception {
        //组装信息
        FilesVo filesVo = new FilesVo();
        filesVo.setProvinceName(provinceName);
        filesVo.setCityName(cityName);

        String excelName = "案卷汇总统计";
        //表头
        String[] filesHead = ExeclBeanUtil.allFileList;
        //需要显示在excel中的参数对应的值
        String[] dbEntityVeto = ExeclBeanUtil.allFileListColumn;
        //查询要导出的数据
        List<FilesVo> fileList = filesDrawActivityService.getAllFilesExport(filesVo);
        List<Map<String, Object>> maps = ExcelUtil.listConvert(fileList);

        //导出
        ExcelUtil.exportFilesExcel(request, response, maps, excelName,filesHead,dbEntityVeto);


    }

    @CheckLogin(ResultTypeEnum.page)
    @RequestMapping(value = "/subExpert")
    @ResponseBody
    public JsonResult subExpert(HttpServletRequest request,
                                     @RequestParam(value = "activityId", required = false) Integer activityId ){
        //删除抽查活动
        JsonResult jsonResult = randomCaseFileService.distributeFileOfExpert(request,activityId);
        return jsonResult;
    }



}
