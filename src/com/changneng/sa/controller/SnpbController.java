package com.changneng.sa.controller;

import java.util.ArrayList;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.changneng.sa.bean.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.BindingResult;
import org.springframework.validation.ObjectError;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.changneng.sa.aop.SysLogPoint;
import com.changneng.sa.aop.SysLogPoint.dbType;
import com.changneng.sa.aop.SysLogPoint.sysType;
import com.changneng.sa.exception.BusinessException;
import com.changneng.sa.gloableCtrl.CheckLogin;
import com.changneng.sa.gloableCtrl.ResultTypeEnum;
import com.changneng.sa.repeatCommit.CheckRepeatCommit;
import com.changneng.sa.service.IXxcjGeRenAndFilesService;
import com.changneng.sa.service.IXxcjGeRenShangBaoService;
import com.changneng.sa.service.IXxcjService;
import com.changneng.sa.service.IXxcjShangBaoService;
import com.changneng.sa.service.ProvinceReportUserService;
import com.changneng.sa.service.SnpbService;
import com.changneng.sa.util.ChangnengUtil;
import com.changneng.sa.util.Const;
import com.changneng.sa.util.JsonResult;

/**
 * 
 * <AUTHOR> 2018-07-05
 * 报送国家
 */

@Controller
@RequestMapping("/snpb")
public class SnpbController {
	 @Autowired
	 private IXxcjService xxcjService;
	@Autowired
    private SnpbService snpbService;
    @Autowired
    private IXxcjGeRenAndFilesService xxcjGeRenAndFilesService;
    @Autowired
    private IXxcjShangBaoService iXxcjShangBaoService;
    @Autowired
    private IXxcjGeRenShangBaoService xxcjGeRenService;
    @Autowired
    private ProvinceReportUserService proRepUserService;
	/**
	 * 报送国家列表
	 */

	 @CheckLogin(ResultTypeEnum.page)
	 @RequestMapping(value = "/snpb_list")
	   public ModelAndView snpbList(HttpServletRequest request){		 
		 ModelAndView mav = new ModelAndView("snpb/snpb_list");
		 Integer specialTotalNum=5;
		 Integer totalNum=11;
		 Integer totalPerNum=10;
		 Integer finalTotalNum=0;
		 Integer finalTotalPer=0;
		 try {
			LoginUser areaUser= ControllerUtil.getCurLoginUser(request);
			ProvinceReportUser provinceReportUser = proRepUserService.selectUserByAreaCode(areaUser.getAreaCode());
			String areacodePro= areaUser.getAreaCode().substring(0,2);
			//先进集体 新增 展示 配套措施案卷  稽查案卷
			List<provinceSelectionUnit> selectionUnitList=snpbService.selectProvinceSelectionUnitList(areacodePro);

			List<provinceSelectionUnit> list=snpbService.selectProvinceUnitList(areacodePro,"1");
			//先进个人
			List<ProvinceElectionPersonal> selectionPersonalList=snpbService.selectionPersonalList(areacodePro);
			 for (ProvinceElectionPersonal electionPersonal : selectionPersonalList) {
				 electionPersonal.setWorkYearPro(Math.round(electionPersonal.getWorkyearnumPro()));
			 }
			mav.addObject("reportState",areaUser.getReportState());
			mav.addObject("areaUser",areaUser);
			mav.addObject("reportState",provinceReportUser.getReportstate());
			mav.addObject("provinceReportUser",provinceReportUser);
			mav.addObject("selectionUnitList",selectionUnitList);
			mav.addObject("unitListSize",list.size());
			mav.addObject("selectionPersonalList",selectionPersonalList);
			 if(areacodePro.equals("11")||areacodePro.equals("55")||areacodePro.equals("12")||areacodePro.equals("31")||areacodePro.equals("66")){
				 finalTotalNum=specialTotalNum-(selectionUnitList.size());
	            }else{
	             finalTotalNum=totalNum-(selectionUnitList.size());
	            }
			 finalTotalPer=totalPerNum-(selectionPersonalList.size());
			 
			SysInitConfig sysInitConfig = proRepUserService.selectListByTypeFlag();
			mav.addObject(sysInitConfig);
			 
				mav.addObject("finalTotalNum",finalTotalNum);
				mav.addObject("finalTotalPer",finalTotalPer);
			return mav;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
		 
	 }
	 
	 /**
		 * 案卷修改列表
		 */ 

		 @CheckLogin(ResultTypeEnum.page)
		 @RequestMapping(value = "/snpb_caseUpdate")
		   public ModelAndView snpbCaseUpdate(HttpServletRequest request,@RequestParam(value = "areanamePro", required = false)String areanamePro){
			 ModelAndView mav = null;
			 try {
				String areacodePro=request.getParameter("areacodePro");
				String areatypePro=request.getParameter("areatypePro");
				if("3".equals(areatypePro)){
					mav = new ModelAndView("snpb/bs_county_updateFiles");//县级
					List<ProvinceFiles> provinceFilesList=snpbService.selectprovinceFilesList(areacodePro);
					
					/*String[] emptyFiles = BusinessUtil.getEmptyFiles(provinceFilesList, "0,0,-1"); 
					if(emptyFiles!=null){
						for(int i=0;i<emptyFiles.length;i++ ){
							ProvinceFiles emptyFile = new ProvinceFiles();
							emptyFile.setFiletypePro(emptyFiles[i]);
							emptyFile.setFilecodePro("");
							provinceFilesList.add(emptyFile);
						}
						//System.out.println("鍘跨骇provinceFilesList澶у皬>>"+provinceFilesList.size());
					}*/
					
					List<ProvinceFiles> provinceFilesList2 = new ArrayList<>();
					if(provinceFilesList.size()==0){
						for (int i = 0; i < 1; i++) {
							String type = "-1";
							/*if(i==0|| i==1){
								type = "0";
							}else if(i==2){
								type = "-1";
							}*/

							ProvinceFiles emptyFile = new ProvinceFiles();
							emptyFile.setFiletypePro(type);
							emptyFile.setFilecodePro("");
							provinceFilesList.add(emptyFile);
						}
					}
					/*for (ProvinceFiles provinceFiles : provinceFilesList) {
						for (int i = 0; i < 1; i++) {
							*//*String type = "";
							if(i==0|| i==1){
								type = "0";
							}else if(i==2|| i==3){
								type = "-1";
							}else{
								type = "5";
							}*//*
							String type = "-1";
							
	 						if(provinceFilesList2.size()<i+1){//如果新列表里这个元素为空，新添加元素
	 							if(provinceFiles.getIsUse()!=1){
	 								*//*if("0".equals(provinceFiles.getFiletypePro()) && (i==0|| i==1)){
	 									provinceFiles.setIsUse(1);
	 									provinceFilesList2.add(provinceFiles);
	 								}else*//*
	 								if("123467".contains(provinceFiles.getFiletypePro())*//* && i==0*//*){
	 									provinceFiles.setIsUse(1);
	 									provinceFilesList2.add(provinceFiles);
	 								}else{
	 									ProvinceFiles emptyFile = new ProvinceFiles();
	 									emptyFile.setFiletypePro(type);
	 									emptyFile.setFilecodePro("");
	 									provinceFilesList2.add(emptyFile);
	 								}
	 							}else{
	 								ProvinceFiles emptyFile = new ProvinceFiles();
									emptyFile.setFiletypePro(type);
									emptyFile.setFilecodePro("");
									provinceFilesList2.add(emptyFile);
	 							}
							}else{//如果新列表里这个元素不为空，但是案卷号为空，替换
								if("".equals(provinceFilesList2.get(i).getFilecodePro())){
									if(provinceFiles.getIsUse()!=1){
										if("0".equals(provinceFiles.getFiletypePro())*//* && (i==0|| i==1)*//*){
											provinceFiles.setIsUse(1);
											provinceFilesList2.set(i, provinceFiles);
										}else
										if("123467".contains(provinceFiles.getFiletypePro())*//* && i==0*//*){
											provinceFiles.setIsUse(1);
											provinceFilesList2.set(i, provinceFiles);
										}
									}
								}
							}
						}
					}*/
					mav.addObject("provinceFilesList",provinceFilesList);
					mav.addObject("areanamePro",areanamePro);
					mav.addObject("areacodePro",areacodePro);
					mav.addObject("areatypePro",areatypePro);
				}else if("2".equals(areatypePro)){
					mav = new ModelAndView("snpb/bs_city_updateFiles");//市级
					List<ProvinceFiles> provinceFilesList=snpbService.selectprovinceFilesList(areacodePro);
					
					/*String[] emptyFiles = BusinessUtil.getEmptyFiles(provinceFilesList, "0,0,-1,-1,5"); 
					if(emptyFiles!=null){	
						for(int i=0;i<emptyFiles.length;i++ ){
							ProvinceFiles emptyFile = new ProvinceFiles();
							emptyFile.setFiletypePro(emptyFiles[i]);
							emptyFile.setFilecodePro("");
							provinceFilesList.add(emptyFile);
						}
						//System.out.println("甯傜骇provinceFilesList澶у皬>>"+provinceFilesList.size());
					}*/
					List<ProvinceFiles> provinceFilesList2 = new ArrayList<>();
					
					if(provinceFilesList.size()==0){
						for (int i = 0; i < 2; i++) {
//							String type = "";
//							if(i==0|| i==1){
//								type = "0";
//							}else if(i==2|| i==3){
//								type = "-1";
//							}else{
//								type = "5";
//							}
							String type = "";
							if(i==1){
								type = "-1";
							}else{
								type = "5";
							}
							ProvinceFiles emptyFile = new ProvinceFiles();
							emptyFile.setFiletypePro(type);
							emptyFile.setFilecodePro("");
							provinceFilesList.add(emptyFile);
						}
					} else if (provinceFilesList.size() == 1){
                        ProvinceFiles emptyFile = new ProvinceFiles();
                        if ("123467".contains(provinceFilesList.get(0).getFiletypePro())) {
                            emptyFile.setFiletypePro("5");
                        } else {
                            emptyFile.setFiletypePro("-1");
                        }
                        emptyFile.setFilecodePro("");
                        provinceFilesList.add(emptyFile);
					}
					
					for (ProvinceFiles provinceFiles : provinceFilesList) /*{
						for (int i = 0; i < 2; i++) {
							String type = "";
							*//*if(i==0|| i==1){
								type = "0";
							}else if(i==2|| i==3){
								type = "-1";
							}else{
								type = "5";
							}*//*
							if(i==1){
								type = "-1";
							}else{
								type = "5";
							}
							
	 						if(provinceFilesList2.size()<i+1){//如果新列表里这个元素为空，新添加元素
	 							if(provinceFiles.getIsUse()!=1){
	 								*//*if("0".equals(provinceFiles.getFiletypePro()) *//**//*&& *//**//**//**//*(i==0|| i==1)*//**//**//**//* i==1*//**//*){
	 									provinceFiles.setIsUse(1);
	 									provinceFilesList2.add(provinceFiles);
	 								}else*//*
									if("123467".contains(provinceFiles.getFiletypePro()) *//*&& i==1*//*){
	 									provinceFiles.setIsUse(1);
	 									provinceFilesList2.add(provinceFiles);
	 								}else if("5".equals(provinceFiles.getFiletypePro()) *//*&& i==4*//*){
	 									provinceFiles.setIsUse(1);
	 									provinceFilesList2.add(provinceFiles);
	 								}else{
	 									ProvinceFiles emptyFile = new ProvinceFiles();
	 									emptyFile.setFiletypePro(type);
	 									emptyFile.setFilecodePro("");
	 									provinceFilesList2.add(emptyFile);
	 								}
	 							}else{
	 								ProvinceFiles emptyFile = new ProvinceFiles();
									emptyFile.setFiletypePro(type);
									emptyFile.setFilecodePro("");
									provinceFilesList2.add(emptyFile);
	 							}
							}else{//如果新列表里这个元素不为空，但是案卷号为空，替换
								if("".equals(provinceFilesList2.get(i).getFilecodePro())){
									if(provinceFiles.getIsUse()!=1){
										*//*if("0".equals(provinceFiles.getFiletypePro()) && (i==0|| i==1)){
											provinceFiles.setIsUse(1);
											provinceFilesList2.set(i, provinceFiles);
										}else*//*
										if("123467".contains(provinceFiles.getFiletypePro())*//* && (i==2|| i==3) i==1*//*){
											provinceFiles.setIsUse(1);
											provinceFilesList2.set(i, provinceFiles);
										}else if("5".equals(provinceFiles.getFiletypePro()) *//*&& i==2*//*){
											provinceFiles.setIsUse(1);
											provinceFilesList2.set(i, provinceFiles);
										}
									}
								}
							}
						}
					}*/
					
					
					mav.addObject("provinceFilesList",provinceFilesList);
					mav.addObject("areanamePro",areanamePro);
					mav.addObject("areacodePro",areacodePro);
					mav.addObject("areatypePro",areatypePro);
				}
				return mav;
			} catch (Exception e) {
				e.printStackTrace();
				return null;
			}
		 }
	 
	 
	 /**
	  * <AUTHOR> 2018-07-16
	 * 国家报送先进集体案卷修改跳转
	 */ 
	 @CheckLogin(ResultTypeEnum.page)
	 @RequestMapping(value = "/caseUpdate")
	   public ModelAndView caseUpdate(HttpServletRequest request,
			   @RequestParam(value = "id", required = false) String id,
			   @RequestParam(value = "filetypePro", required = false) String filetypePro,
			   @RequestParam(value = "joinunitPro", required = false) String joinunitPro,
			   @RequestParam(value = "areacodePro", required = false) String areacodePro,
			   @RequestParam(value = "areatypePro", required = false) String areatypePro){
			 ModelAndView mav = null;
			    //编辑
				 if(!ChangnengUtil.isNull(id)){
					 
					/* if("0".equals(filetypePro)){//一般行政处罚案卷
							mav = new ModelAndView("snpb/bs_jiti_ybxzcf_input");
							mav.addObject("areatypePro",areatypePro);
							mav.addObject("areacodePro",areacodePro);
							mav.addObject("joinunitPro",joinunitPro);
						}else*/
					if("5".equals(filetypePro)){//稽查案卷
							mav = new ModelAndView("snpb/bs_jiti_jcaj_input");
							mav.addObject("areatypePro",areatypePro);
							mav.addObject("areacodePro",areacodePro);
							mav.addObject("joinunitPro",joinunitPro);
					}else {//配套案卷
						mav = new ModelAndView("snpb/bs_jiti_ptaj_input");
						mav.addObject("areatypePro",areatypePro);
						mav.addObject("areacodePro",areacodePro);
						mav.addObject("joinunitPro",joinunitPro);
					}
		           ProvinceFilesWithBLOBs xxcjProFilesList =  iXxcjShangBaoService.selectXZCFById(id);//此处用来调用数据
		 	       mav.addObject("xxcjProFilesList",xxcjProFilesList);
		 	    //新增
		        }else{
		        	/* if("0".equals(filetypePro)){//一般行政处罚案卷
							mav = new ModelAndView("snpb/bs_jiti_ybxzcf_input");
							 mav.addObject("joinunitPro",joinunitPro);
							 mav.addObject("areacodePro",areacodePro);
							 mav.addObject("areatypePro",areatypePro);
						}else */
		        	if("5".equals(filetypePro)){//稽查案卷
						 mav = new ModelAndView("snpb/bs_jiti_jcaj_input");
						 mav.addObject("joinunitPro",joinunitPro);
						 mav.addObject("areacodePro",areacodePro);
						 mav.addObject("areatypePro",areatypePro);
					}else {//配套案卷
						 mav = new ModelAndView("snpb/bs_jiti_ptaj_input");
						 mav.addObject("joinunitPro",joinunitPro);
						 mav.addObject("areacodePro",areacodePro);
						 mav.addObject("areatypePro",areatypePro);
					}
		        	
		        }
				 mav.addObject("subAreaCode",areacodePro.substring(0,2));
				 
			 return mav;
	 }
	 
	 /**
	     * 案卷就改保存
	     * wudi 2018-07-16
	     * @param request
	     * @return
	     */
	    @CheckLogin(ResultTypeEnum.json)
	    @RequestMapping(value = "/xxcjybxzcfAddOrUpdate")
	    @ResponseBody
	    public JsonResult goXxcjybxzcfAddOrUpdate(HttpServletRequest request,HttpServletResponse response,
	    		@Validated ProvinceFilesWithBLOBs proFiles,BindingResult res, @RequestParam(value = "id", required = false) String id
	    	,@RequestParam(value = "areacodePro", required = false) String areacodePro
	    	,@RequestParam(value = "joinunitPro", required = false) String joinunitPro){
	    	 ProvinceFilesWithBLOBs xxcjProFilesList =  iXxcjShangBaoService.selectXZCFById(id);//此处用来调用数据
	    	 	JsonResult jsonResult=new JsonResult();
	    	 	try {   
	    			if(res.hasErrors()){
	    	 			String errorMsg = "";
	    	 			
	    	 			for(ObjectError obj:res.getAllErrors()){
	    	 	            System.out.println(obj.getDefaultMessage());
	    	 	            errorMsg += obj.getDefaultMessage()+"；";
	    	 	        }
	            		jsonResult.setResult(Const.RESULT_ERROR);
	        			jsonResult.setMessage(errorMsg);
	        			return jsonResult;
	            	}else{
						 if(!ChangnengUtil.isNull(id)){
							 proFiles.setAreacodePro(xxcjProFilesList.getAreacodePro());
							 proFiles.setJoinunitPro(xxcjProFilesList.getJoinunitPro());
							 if("5".equals(proFiles.getFiletypePro())){
								 iXxcjShangBaoService.addOrUpdateJCAJ(proFiles);//此处稽查案卷新增修改
							 }else{
								 iXxcjShangBaoService.addOrUpdateXZCF(proFiles);//此处一般和配套新增修改
							 }
							 jsonResult.setResult(Const.RESULT_SUCCESS);
							 jsonResult.setMessage("保存成功");
							 System.out.println("保存成功");
						 } else {
							 if ("5".equals(proFiles.getFiletypePro())) {
								 List<ProvinceFiles> provinceFiles = iXxcjShangBaoService.selectJCAJByCode(areacodePro);
								 if (provinceFiles != null && !provinceFiles.isEmpty()) {
									 jsonResult.setResult(Const.RESULT_ERROR);
									 jsonResult.setMessage("每个账号可上报1个稽查案件！");
									 return jsonResult;
								 } else {
									 iXxcjShangBaoService.addOrUpdateJCAJ(proFiles);//此处稽查案卷新增修改
									 jsonResult.setResult(Const.RESULT_SUCCESS);
									 jsonResult.setMessage("保存成功");
								 }
							 } else {
								 List<ProvinceFiles> provinceFiles = iXxcjShangBaoService.selectPTAJByCode(areacodePro);
								 if (provinceFiles != null && !provinceFiles.isEmpty()) {
									 jsonResult.setResult(Const.RESULT_ERROR);
									 jsonResult.setMessage("每个账号可上报1个配套措施案件！");
									 return jsonResult;
								 }else {
									 iXxcjShangBaoService.addOrUpdateXZCF(proFiles);//此处一般和配套新增修改
									 jsonResult.setResult(Const.RESULT_SUCCESS);
									 jsonResult.setMessage("保存成功");
								 }
							 }
						 }
	    			}

	            return jsonResult;
	    	 	 } catch (Exception e) {
	    	            e.printStackTrace();
	    	            jsonResult.setResult(Const.RESULT_ERROR);
	    	            jsonResult.setMessage("信息操作失败");
	    	            System.out.println("信息操作失败");
	    	            return jsonResult;
	    	        }
	    }  
	    
	 
	 /**
		 * 先进 集体修改列表
		 */ 
	 @CheckLogin(ResultTypeEnum.page)
	 @RequestMapping(value = "/provinceUnitUpdate")
	   public ModelAndView snpbList1(HttpServletRequest request,
			   @RequestParam(value = "id", required = false) String id){
		
		 try {
			 ModelAndView mav = null;
			 LoginUser areaUser= new LoginUser();
			 provinceSelectionUnit provinceSelectionUnit=snpbService.selectProvinceSelectionUnit(id);
			 areaUser.setUserName(provinceSelectionUnit.getAreanamePro());
			 System.out.println(provinceSelectionUnit.getAreatypePro());
			 areaUser.setArealevel(provinceSelectionUnit.getAreatypePro());
			 provinceSelectionUnit.setId(Integer.parseInt(request.getParameter("id")));
			 mav = new ModelAndView("snpb/bs_updatePro_xzqjcsj");//省级
			 ProvinceReportUser provinceReportUser = proRepUserService.selectUserByAreaCode(areaUser.getAreaCode());
			 mav.addObject("provinceReportUser",provinceReportUser);
			 SysInitConfig sysInitConfig=proRepUserService.selectListByTypeFlag();//获取采集阶段状态
			 //System.out.println(sysInitConfig);
			 mav.addObject("sysInitConfig", sysInitConfig);
			 mav.addObject("provinceSelectionUnit",provinceSelectionUnit);
			 mav.addObject("areaUser",areaUser);
			 return mav;
		} catch (BusinessException e) {
			e.printStackTrace();
			 return null;
		}
		
	 }
	 
	 /**
	     * 先进 集体修改列表（市、县级）
	     * @param request
	     * @return
	     */
	    @CheckLogin(ResultTypeEnum.page)
	    @RequestMapping(value = "/cityUnitUpdate")
	    public ModelAndView goCityUnitUpdate(HttpServletRequest request,
				   @RequestParam(value = "id", required = false) String id){
	        ModelAndView mav=new ModelAndView("snpb/bs_updateCit_xzqjcsj");
	        try {
	        	LoginUser areaUser= new LoginUser();
	        	provinceSelectionUnit provinceSelectionUnit=snpbService.selectProvinceSelectionUnit(id);
	        	areaUser.setUserName(provinceSelectionUnit.getAreanamePro());
	        	areaUser.setArealevel(provinceSelectionUnit.getAreatypePro());
	        	provinceSelectionUnit.setId(Integer.parseInt(request.getParameter("id")));
	            ProvinceReportUser provinceReportUser = proRepUserService.selectUserByAreaCode(areaUser.getAreaCode());
	            mav.addObject("provinceReportUser",provinceReportUser);
	            SysInitConfig sysInitConfig=proRepUserService.selectListByTypeFlag();//获取采集阶段状态
	            mav.addObject("sysInitConfig", sysInitConfig);
				mav.addObject("provinceSelectionUnit",provinceSelectionUnit);
				mav.addObject("areaUser",areaUser);
	            return mav;
	        } catch (Exception e) {
	            e.printStackTrace();
	            return null;
	        }
	    }
	 
	 /**
	     * @Description 行政基础数据（省、市、县级）保存方法
	     * @param
	     * @param request
	     * @param response
	     * @return
	     */

	    @CheckRepeatCommit
	    @CheckLogin(ResultTypeEnum.json)
	    @RequestMapping(value = "/saveProvinceBase")
	    @ResponseBody
	    @SysLogPoint(dbOptType=dbType.UPDATE,sysOptType=sysType.ENTERRISE_NUM_REPORT)
	    public JsonResult saveProvinceBase(@Validated provinceSelectionUnit proUnit,BindingResult res, HttpServletRequest request,HttpServletResponse response){
	        JsonResult jsonResult=new JsonResult();
	        try { 
	        	if(res.hasErrors()){
	        		String errMsg = "";
	        		for (ObjectError obj : res.getAllErrors()) {
	        			errMsg = obj.getDefaultMessage();
					}
	        		jsonResult.setResult(Const.RESULT_ERROR);
	    			jsonResult.setMessage(errMsg);
	    			return jsonResult;
	        	}else{
	        	System.out.println(proUnit.getAreacodePro());
	        
	        	//flag为true则表明已有数据是修改，为false则表示保存
	        	xxcjService.updateProvinceUnit(proUnit,proUnit.getId());
	            jsonResult.setResult(Const.RESULT_SUCCESS);
	            jsonResult.setMessage("保存成功");
	            return jsonResult;
	        	}
	        } catch (Exception e) {
	            e.printStackTrace();
	            jsonResult.setResult(Const.RESULT_ERROR);
	            jsonResult.setMessage("保存失败");
	            return jsonResult;
	        }
	    }
	    /**
		 * 先进个人修改列表
		 */ 
	 @CheckLogin(ResultTypeEnum.page)
	 @RequestMapping(value = "/personalUpdate")
	   public ModelAndView snpbList2(HttpServletRequest request,
			   @RequestParam(value = "id", required = false) String id){
		 ModelAndView mav = null;
 		 LoginUser areaUser= new LoginUser();
 		  ProvinceFilesWithBLOBs xxcjProFiles1 =null;
 		 ProvinceFilesWithBLOBs xxcjProFiles2 =null;
		 try {
			  ProvinceElectionPersonal ProPerson=xxcjGeRenService.selectPersonalById(id);
             ProPerson.setWorkYearPro(Math.round(ProPerson.getWorkyearnumPro()));
			 areaUser.setUserName(ProPerson.getAreanamePro());
			String areatypePro=ProPerson.getAreatypePro();
	    if(!ChangnengUtil.isNull(id)){
				mav = new ModelAndView("snpb/bs_geren_update");
	              //根据id查询个人信息表
	              //根据个人id查询个人信息及案卷关联表
	              List<ProvincePersonalFilesWithBLOBs> ProPersonAndFiles=xxcjGeRenAndFilesService.selectPersonalById(id);
	              //根据id查询个人信息及案卷关联表
	                if(ProPersonAndFiles.size()>0){
	                	 //根据案卷id查询案卷表  个人案卷一查询
	                    String fileId1=(ProPersonAndFiles.get(0).getFileidPro()).toString();
	                    xxcjProFiles1 =  iXxcjShangBaoService.selectXZCFById(fileId1);
	                    //根据案卷id查询案卷表  个人案卷二查询
	                    if(ProPersonAndFiles.size()>1){
	                    	 String fileId2=ProPersonAndFiles.get(1).getFileidPro().toString();
	                         xxcjProFiles2 =  iXxcjShangBaoService.selectXZCFById(fileId2);	
	                    }
	                }
	                  mav.addObject("areaUser",areaUser);
	                  mav.addObject("ProPerson", ProPerson);
	                  mav.addObject("areatypePro", areatypePro);
	                  mav.addObject("ProPersonAndFiles", ProPersonAndFiles);
	                  mav.addObject("xxcjProFiles1",xxcjProFiles1);
	                  mav.addObject("xxcjProFiles2",xxcjProFiles2);
	      }
	    mav.addObject("subAreaCode",ProPerson.getAreacodePro().substring(0,2));
			 return mav;
		} catch (BusinessException e) {
			e.printStackTrace();
			 return null;
			 
		}
		
	 }
	 
	 
	  @CheckLogin(ResultTypeEnum.json)
	    @RequestMapping(value = "/savePersonal")
	    @ResponseBody
	    public JsonResult saveProvinceBase(@Validated FilesListAndElectionPersonalProModel model,BindingResult res,HttpServletRequest request,HttpServletResponse response){
	        JsonResult jsonResult=new JsonResult();
	        String areacodePro=request.getParameter("areacodePro");
	        try {  
	        	if(res.hasErrors()){
    	 			String errorMsg = "";
    	 			
    	 			for(ObjectError obj:res.getAllErrors()){
    	 	            System.out.println(obj.getDefaultMessage());
    	 	            errorMsg += obj.getDefaultMessage()+"；";
    	 	        }
            		jsonResult.setResult(Const.RESULT_ERROR);
        			jsonResult.setMessage(errorMsg);
        			return jsonResult;
            	}else{
	        		ProvinceReportUser provinceReportUser = proRepUserService.selectUserByAreaCode(areacodePro);
		        	//根据登录人code查询所属省市县
		        	Tarea tarea=xxcjGeRenService.selectAreaByCode(areacodePro);
		        	model.getProElectionPersonal().setAreacodePro(areacodePro);
		        	model.getProElectionPersonal().setAreanamePro(provinceReportUser.getName());
		        	model.getProElectionPersonal().setAreatypePro(tarea.getArealevel());
		        	model.getProElectionPersonal().setProvincePro(tarea.getProvince());
		        	model.getProElectionPersonal().setCityPro(tarea.getCity());
		        	model.getProElectionPersonal().setCountryPro(tarea.getCountry());
	        		xxcjGeRenService.saveXxcjPersonAnjuan(model.getFiles(), model.getProElectionPersonal());
	            jsonResult.setResult(Const.RESULT_SUCCESS);
	            jsonResult.setMessage("保存成功");
	            return jsonResult;
            	}
	        } catch (Exception e) {
	            e.printStackTrace();
	            jsonResult.setResult(Const.RESULT_ERROR);
	            jsonResult.setMessage("保存失败");
	            return jsonResult;
	        }
	    }
	  
	  
	 /**
	  * <AUTHOR> 2018-07-12
	  * 
	 * 报送国家
	 */ 
	 @ResponseBody
	 @CheckLogin(ResultTypeEnum.json)
	 @RequestMapping(value = "/reportCountry")
	 public JsonResult reportingCountry(HttpServletRequest request){
		 JsonResult jsonResult=new JsonResult();
		 try {
			LoginUser areaUser= ControllerUtil.getCurLoginUser(request);
			String areacode= areaUser.getAreaCode();
		    snpbService.updateProvinceReportUserList(areacode);
		    jsonResult.setResult(Const.RESULT_SUCCESS);
            jsonResult.setMessage("提交成功");
            System.out.println(jsonResult);
          return jsonResult;
		} catch (Exception e) {
			e.printStackTrace();
			jsonResult.setResult(Const.RESULT_ERROR);
            jsonResult.setMessage("提交失败");
            System.out.println(jsonResult);
            return jsonResult;
            
		}
	 }
	 
	 /**
	  * <AUTHOR> 2018-07-12
	  * 
	 * 报送国家
	 */ 
	 @ResponseBody
	 @CheckLogin(ResultTypeEnum.json)
	 @RequestMapping(value = "/checkFile")
	 public JsonResult checkFile(HttpServletRequest request){
		 JsonResult jsonResult=new JsonResult();
		 try {
//			LoginUser areaUser= ControllerUtil.getCurLoginUser(request);
//			String areacode= areaUser.getAreaCode();
//			provinceSelectionUnit provinceSelectionUnit = snpbService.getProvinceSelectionUnit(areacode);
//			if(provinceSelectionUnit!=null){
//				String activityreporturl = provinceSelectionUnit.getActivityreporturl();
////				String selfevaluationreporturl = provinceSelectionUnit.getSelfevaluationreporturl();
//				if(ChangnengUtil.isNull(activityreporturl) /*|| ChangnengUtil.isNull(selfevaluationreporturl)*/){
//					jsonResult.setData("0");
//				}else{
//					jsonResult.setData("1");
//				}
//			}else{
//				jsonResult.setData("0");
//			}
		    jsonResult.setResult(Const.RESULT_SUCCESS);
            jsonResult.setMessage("提交成功");
            System.out.println(jsonResult);
          return jsonResult;
		} catch (Exception e) {
			e.printStackTrace();
			jsonResult.setResult(Const.RESULT_ERROR);
            jsonResult.setMessage("提交失败");
            System.out.println(jsonResult);
            return jsonResult;
            
		}
	 }
	 
}
