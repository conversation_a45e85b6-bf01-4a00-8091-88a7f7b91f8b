# DaLianBing

## 2025年功能更新记录

### 2025-07-10 评分页面添加「加分项」功能

本次更新为专家评分系统新增了「加分项」评分功能，实现了完整的三阶段评分流程：规范性评查 → 合法性评查 → 加分项。

#### 🎯 功能特性

1. **三阶段评分流程**
   - 规范性评查（卷面评分）
   - 合法性评查（实体评分）
   - 加分项评分（新增）

2. **访问控制机制**
   - 必须按顺序完成：规范性评查 → 合法性评查 → 加分项
   - 每个阶段保存后才能进入下一阶段
   - 通过 `scoredState` 字段控制页面访问权限

3. **加分项评分规则**
   - 默认选择"不符合"选项
   - 使用 `scoreType=2` 标识加分项评分
   - 支持多项加分标准的独立评分

#### 📁 主要文件修改

**前端页面 (JSP)**
- `zjpf_score.jsp` - 主评分页面，新增加分项标签页
- `zjpf_pluses_view.jsp` - 加分项查看页面（新增）
- `zjpf_view.jsp` - 评分查看页面，增加加分项导航
- `zjpf_entity_view.jsp` - 实体评分查看页面优化
- `zjpf_list.jsp` - 评分列表页面更新

**后端服务层**
- `ZjpfController.java` - 新增加分项相关接口
  - `getPlusesData()` - 获取加分项数据
  - `gozjpfPlusesView()` - 加分项查看页面
- `ZjpfService.java` - 新增加分项服务接口
- `ZjpfServiceImpl.java` - 实现加分项业务逻辑

**数据模型层**
- `ExpertHandlFileList.java` - 新增 `plusesScore` 字段（BigDecimal类型）
- `ExpertHandlIndexScore.java` - 支持加分项评分类型
- `ExpertHandUnit_ZJPF_zjpfList.java` - 列表查询优化

**配置文件**
- `Const.java` - 新增加分项常量定义
- `ExpertHandlFileListMapper.xml` - 数据库映射更新
- `ZjpfAjaxMapper.xml` - Ajax查询接口更新

#### 🔧 技术实现要点

1. **数据库设计**
   ```sql
   -- 新增加分项得分字段
   ALTER TABLE experthandlfilelist ADD COLUMN plusesScore DECIMAL(10,2) COMMENT '加分项得分';
   ```

2. **评分类型定义**
   ```java
   // 评分类型常量
   public static String ENTITY_FILE_TYPE = "98";   // 合法性评查
   public static String PLUSES_FILE_TYPE = "100";  // 加分项
   ```

3. **状态控制逻辑**
   - `scoredState = 0` - 未评分
   - `scoredState = 3` - 规范性评查已保存
   - `scoredState = 10` - 合法性评查已保存
   - `scoredState = 1` - 加分项已保存（完成）

4. **前端交互优化**
   - 动态按钮状态控制
   - 自动默认选择"不符合"
   - 三个标签页的联动切换

#### 📊 评分计算公式

```javascript
// 最终得分计算
finalScore = (paperScore + entityScore + plusesScore) / standardScore * 50 - deductionScore
```

#### 🚀 部署说明

1. 数据库需要执行字段新增脚本
2. 重新部署应用程序
3. 清理浏览器缓存以加载新的前端资源

---

## 2024年记录





2024年新原型 https://b5e4ww.axshare.com

2024-05-07 这个仓库初始化


# ↓下面的是2023年开发时记录的东西

2023年的原型 https://hodxs8.axshare.com

###案卷抽取情况查询SQL备份
```
-- 所有待抽取案卷情况总计
select
t.province as 省,
COUNT(p.id) as 总数,
COUNT(CASE WHEN p.fileType='0' THEN 1 ELSE NULL END) as 处罚个数,
COUNT(CASE WHEN p.fileType<>'0' THEN 1 ELSE NULL END) as 配套个数
from
T_area t
LEFT join penalizeSyncFileSpe p on p.areaCode = t.code AND p.isClosed=1 AND p.isEnbaled = 0
group BY t.province
ORDER BY t.code

-- 案卷已抽取情况
select
t.province as 省,
t.city as 市,
t.code as 行政编码,
COUNT(CASE WHEN p.fileType='0' THEN 1 ELSE NULL END) as 处罚个数,
COUNT(CASE WHEN p.fileType<>'0' THEN 1 ELSE NULL END) as 配套个数,
IF (COUNT(CASE WHEN p.fileType='0' THEN 1 ELSE NULL END)+COUNT(CASE WHEN p.fileType<>'0' THEN 1 ELSE NULL END)=2,'够','不够') as 是否抽够,
IF (COUNT(CASE WHEN p.fileType='0' THEN 1 ELSE NULL END)+COUNT(CASE WHEN p.fileType<>'0' THEN 1 ELSE NULL END)=0,'没抽到案卷','') as 是否为空
from
T_area t
left join filesActivityRelation far on far.scoreAreaCode = t.code AND far.isDeleted =0 AND far.activityId = 24
LEFT join penalizeSyncFileSpe p on p.id = far.syncFileSpeId
WHERE
(t.areaLevel = 1 or t.areaLevel=2)
group BY t.code
ORDER BY t.code
```

## 新增表
````
CREATE TABLE `filesDrawActivity` (
`id` int NOT NULL AUTO_INCREMENT COMMENT '主键id',
`activityName` varchar(200) DEFAULT NULL COMMENT '活动名称',
`createTime` datetime DEFAULT NULL COMMENT '创建时间',
`updateTime` datetime DEFAULT NULL COMMENT '修改时间',
`createName` varchar(255) DEFAULT NULL COMMENT '创建人',
`updateName` varchar(0) DEFAULT NULL COMMENT '修改人',
`isDeleted` int NOT NULL DEFAULT '0' COMMENT '逻辑删除标识:0未删除，1已删除',
PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='抽案卷活动表';``

````
### 案卷附件字段
```
ALTER TABLE dlb_2023_dev.penalizeSyncFileSpeSpecial ADD fileUrl varchar(255) NULL COMMENT '附件地址';

ALTER TABLE dlb_2023_dev.penalizeSyncFileSpeSpecial ADD fileSize DOUBLE NULL COMMENT '附件大小';
```

### 案卷总表的id超长,修改抽取案卷中间表int改为bigint
``ALTER TABLE dlb_2023_dev.filesActivityRelation MODIFY COLUMN syncFileSpeId BIGINT NULL COMMENT '案件来源ID';``

### 地方账号表,省厅文件
```ALTER TABLE `provinceReportUser`
MODIFY COLUMN `areaType` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '地市level等级' AFTER `electCountyState`,
ADD COLUMN `fileName` varchar(255) NULL COMMENT '省盖章文件(申请复核案卷)' AFTER `bigArea`,
ADD COLUMN `fileUrl` varchar(500) NULL COMMENT '盖章文件下载地址' AFTER `fileName`;```

### 案卷复核: 政策文件(20231020新增)
```ALTER TABLE `filesRecheckInfo`
ADD COLUMN `lawFileName` varchar(255) NULL COMMENT '附件name' AFTER `isDeleted`,
ADD COLUMN `lawFileUrl` varchar(500) NULL COMMENT '附件下载地址url' AFTER `fileName`;```

### 需要抽取案卷的省
``CREATE TABLE `fileIsDrawProvince` (
`id` int NOT NULL AUTO_INCREMENT,
`activeId` int DEFAULT NULL COMMENT '活动ID',
`activeName` varchar(255) DEFAULT NULL COMMENT '活动名',
`province` varchar(20) DEFAULT NULL COMMENT '省Code',
`provinceName` varchar(255) DEFAULT NULL COMMENT '省name',
`idDraw` int DEFAULT '0' COMMENT '是否抽取：0不抽，1抽',
`round` int DEFAULT NULL COMMENT '抽取规则：1第一轮，2第二轮，3第三轮',
PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='抽取案卷的省'``

### 优选表加字段-20231027
```
ALTER TABLE penalizeSyncFileSpeSpecial ADD isPersonal INT DEFAULT 0 NULL COMMENT '是否是个人案卷：0不是，1是个人案卷';
ALTER TABLE penalizeSyncFileSpeSpecial ADD personName varchar(100) NULL COMMENT '执法人员姓名';
ALTER TABLE penalizeSyncFileSpeSpecial ADD lawNum varchar(100) NULL COMMENT '执法证号';
```

### 案卷表加字段 -20231027
```
ALTER TABLE files ADD personId varchar(100) NULL COMMENT '执法人员ID';
ALTER TABLE files ADD supplementInfo varchar(100) NULL COMMENT '补抽标识：1本类型未结案，2其他类型已结案，3其他类型未结案';
ALTER TABLE files ADD isPersonal int DEFAULT 0 NULL COMMENT '个人抽取的案卷：0不是个人，1是个人自己，2个人和市共用';
ALTER TABLE files ADD isCounty int DEFAULT 0 NULL COMMENT '县级抽取的案卷：0不是县级，1县级自己，2县级和个人共用，3县级和市级共用';
ALTER TABLE files ADD isCity int DEFAULT 0 NULL COMMENT '市级抽取的案卷：0不是市的，1市自己的，2市和个人';



```

