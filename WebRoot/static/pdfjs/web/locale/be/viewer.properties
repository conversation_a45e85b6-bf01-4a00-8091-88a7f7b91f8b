# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=ÐÐ°Ð¿ÑÑÑÐ´Ð½ÑÑ ÑÑÐ°ÑÐ¾Ð½ÐºÐ°
previous_label=ÐÐ°Ð¿ÑÑÑÐ´Ð½ÑÑ
next.title=ÐÐ°ÑÑÑÐ¿Ð½Ð°Ñ ÑÑÐ°ÑÐ¾Ð½ÐºÐ°
next_label=ÐÐ°ÑÑÑÐ¿Ð½Ð°Ñ

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=Ð¡ÑÐ°ÑÐ¾Ð½ÐºÐ°
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages=Ð· {{pagesCount}}
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.
page_of_pages=({{pageNumber}} Ð· {{pagesCount}})

zoom_out.title=ÐÐ°Ð¼ÐµÐ½ÑÑÑÑ
zoom_out_label=ÐÐ°Ð¼ÐµÐ½ÑÑÑÑ
zoom_in.title=ÐÐ°Ð²ÑÐ»ÑÑÑÑÑ
zoom_in_label=ÐÐ°Ð²ÑÐ»ÑÑÑÑÑ
zoom.title=ÐÐ°Ð²ÑÐ»ÑÑÑÐ½Ð½Ðµ ÑÑÐºÑÑÑ
presentation_mode.title=ÐÐµÑÐ°ÐºÐ»ÑÑÑÑÑÐ° Ñ ÑÑÐ¶ÑÐ¼ Ð¿Ð°ÐºÐ°Ð·Ñ
presentation_mode_label=Ð ÑÐ¶ÑÐ¼ Ð¿Ð°ÐºÐ°Ð·Ñ
open_file.title=ÐÐ´ÐºÑÑÑÑ ÑÐ°Ð¹Ð»
open_file_label=ÐÐ´ÐºÑÑÑÑ
print.title=ÐÑÑÐºÐ°Ð²Ð°ÑÑ
print_label=ÐÑÑÐºÐ°Ð²Ð°ÑÑ
download.title=Ð¡ÑÑÐ³Ð½ÑÑÑ
download_label=Ð¡ÑÑÐ³Ð½ÑÑÑ
save.title=ÐÐ°ÑÐ°Ð²Ð°ÑÑ
save_label=ÐÐ°ÑÐ°Ð²Ð°ÑÑ
bookmark.title=Ð¦ÑÐ¿ÐµÑÐ°ÑÐ½Ñ Ð²ÑÐ³Ð»ÑÐ´ (ÑÐºÐ°Ð¿ÑÑÐ²Ð°ÑÑ Ð°Ð±Ð¾ Ð°Ð´ÐºÑÑÑÑ Ñ Ð½Ð¾Ð²ÑÐ¼ Ð°ÐºÐ½Ðµ)
bookmark_label=Ð¦ÑÐ¿ÐµÑÐ°ÑÐ½ÑÑ Ð¿ÑÐ°ÑÐ²Ð°

bookmark1.title=ÐÐ·ÐµÐ¹Ð½Ð°Ñ ÑÑÐ°ÑÐ¾Ð½ÐºÐ° (Ð¿Ð°Ð³Ð»ÑÐ´Ð·ÐµÑÑ URL-Ð°Ð´ÑÐ°Ñ Ð· Ð´Ð·ÐµÐ¹Ð½Ð°Ð¹ ÑÑÐ°ÑÐ¾Ð½ÐºÑ)
bookmark1_label=Ð¦ÑÐ¿ÐµÑÐ°ÑÐ½ÑÑ ÑÑÐ°ÑÐ¾Ð½ÐºÐ°

# Secondary toolbar and context menu
tools.title=ÐÑÑÐ»Ð°Ð´Ñ
tools_label=ÐÑÑÐ»Ð°Ð´Ñ
first_page.title=ÐÐµÑÐ°Ð¹ÑÑÑ Ð½Ð° Ð¿ÐµÑÑÑÑ ÑÑÐ°ÑÐ¾Ð½ÐºÑ
first_page_label=ÐÐµÑÐ°Ð¹ÑÑÑ Ð½Ð° Ð¿ÐµÑÑÑÑ ÑÑÐ°ÑÐ¾Ð½ÐºÑ
last_page.title=ÐÐµÑÐ°Ð¹ÑÑÑ Ð½Ð° Ð°Ð¿Ð¾ÑÐ½ÑÑ ÑÑÐ°ÑÐ¾Ð½ÐºÑ
last_page_label=ÐÐµÑÐ°Ð¹ÑÑÑ Ð½Ð° Ð°Ð¿Ð¾ÑÐ½ÑÑ ÑÑÐ°ÑÐ¾Ð½ÐºÑ
page_rotate_cw.title=ÐÐ°Ð²ÑÑÐ½ÑÑÑ Ð¿Ð° ÑÐ¾Ð½ÑÑ
page_rotate_cw_label=ÐÐ°Ð²ÑÑÐ½ÑÑÑ Ð¿Ð° ÑÐ¾Ð½ÑÑ
page_rotate_ccw.title=ÐÐ°Ð²ÑÑÐ½ÑÑÑ ÑÑÐ¿ÑÐ°ÑÑ ÑÐ¾Ð½ÑÐ°
page_rotate_ccw_label=ÐÐ°Ð²ÑÑÐ½ÑÑÑ ÑÑÐ¿ÑÐ°ÑÑ ÑÐ¾Ð½ÑÐ°

cursor_text_select_tool.title=Ð£ÐºÐ»ÑÑÑÑÑ Ð¿ÑÑÐ»Ð°Ð´Ñ Ð²ÑÐ±Ð°ÑÑ ÑÑÐºÑÑÑ
cursor_text_select_tool_label=ÐÑÑÐ»Ð°Ð´Ð° Ð²ÑÐ±Ð°ÑÑ ÑÑÐºÑÑÑ
cursor_hand_tool.title=Ð£ÐºÐ»ÑÑÑÑÑ ÑÑÑÐ½ÑÑ Ð¿ÑÑÐ»Ð°Ð´Ñ
cursor_hand_tool_label=Ð ÑÑÐ½Ð°Ñ Ð¿ÑÑÐ»Ð°Ð´Ð°

scroll_page.title=ÐÑÐºÐ°ÑÑÑÑÐ¾ÑÐ²Ð°ÑÑ Ð¿ÑÐ°ÐºÑÑÑÐºÑ ÑÑÐ°ÑÐ¾Ð½Ðºi
scroll_page_label=ÐÑÐ°ÐºÑÑÑÐºÐ° ÑÑÐ°ÑÐ¾Ð½Ðºi
scroll_vertical.title=Ð£Ð¶ÑÐ²Ð°ÑÑ Ð²ÐµÑÑÑÐºÐ°Ð»ÑÐ½ÑÑ Ð¿ÑÐ°ÐºÑÑÑÐºÑ
scroll_vertical_label=ÐÐµÑÑÑÐºÐ°Ð»ÑÐ½Ð°Ñ Ð¿ÑÐ°ÐºÑÑÑÐºÐ°
scroll_horizontal.title=Ð£Ð¶ÑÐ²Ð°ÑÑ Ð³Ð°ÑÑÐ·Ð°Ð½ÑÐ°Ð»ÑÐ½ÑÑ Ð¿ÑÐ°ÐºÑÑÑÐºÑ
scroll_horizontal_label=ÐÐ°ÑÑÐ·Ð°Ð½ÑÐ°Ð»ÑÐ½Ð°Ñ Ð¿ÑÐ°ÐºÑÑÑÐºÐ°
scroll_wrapped.title=Ð£Ð¶ÑÐ²Ð°ÑÑ Ð¼Ð°ÑÑÐ°Ð±Ð°Ð²Ð°Ð»ÑÐ½ÑÑ Ð¿ÑÐ°ÐºÑÑÑÐºÑ
scroll_wrapped_label=ÐÐ°ÑÑÐ°Ð±Ð°Ð²Ð°Ð»ÑÐ½Ð°Ñ Ð¿ÑÐ°ÐºÑÑÑÐºÐ°

spread_none.title=ÐÐµ Ð²ÑÐºÐ°ÑÑÑÑÐ¾ÑÐ²Ð°ÑÑ ÑÐ°Ð·Ð³Ð¾ÑÐ½ÑÑÑÑ ÑÑÐ°ÑÐ¾Ð½ÐºÑ
spread_none_label=ÐÐµÐ· ÑÐ°Ð·Ð³Ð¾ÑÐ½ÑÑÑÑ ÑÑÐ°ÑÐ¾Ð½Ð°Ðº
spread_odd.title=Ð Ð°Ð·Ð³Ð¾ÑÐ½ÑÑÑÑ ÑÑÐ°ÑÐ¾Ð½ÐºÑ Ð¿Ð°ÑÑÐ½Ð°ÑÑÑ Ð· Ð½ÑÑÐ¾ÑÐ½ÑÑ Ð½ÑÐ¼Ð°ÑÐ¾Ñ
spread_odd_label=ÐÑÑÐ¾ÑÐ½ÑÑ ÑÑÐ°ÑÐ¾Ð½ÐºÑ Ð·Ð»ÐµÐ²Ð°
spread_even.title=Ð Ð°Ð·Ð³Ð¾ÑÐ½ÑÑÑÑ ÑÑÐ°ÑÐ¾Ð½ÐºÑ Ð¿Ð°ÑÑÐ½Ð°ÑÑÑ Ð· ÑÐ¾ÑÐ½ÑÑ Ð½ÑÐ¼Ð°ÑÐ¾Ñ
spread_even_label=Ð¦Ð¾ÑÐ½ÑÑ ÑÑÐ°ÑÐ¾Ð½ÐºÑ Ð·Ð»ÐµÐ²Ð°

# Document properties dialog box
document_properties.title=Ð£Ð»Ð°ÑÑÑÐ²Ð°ÑÑÑ Ð´Ð°ÐºÑÐ¼ÐµÐ½ÑÐ°â¦
document_properties_label=Ð£Ð»Ð°ÑÑÑÐ²Ð°ÑÑÑ Ð´Ð°ÐºÑÐ¼ÐµÐ½ÑÐ°â¦
document_properties_file_name=ÐÐ°Ð·Ð²Ð° ÑÐ°Ð¹Ð»Ð°:
document_properties_file_size=ÐÐ°Ð¼ÐµÑ ÑÐ°Ð¹Ð»Ð°:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} ÐÐ ({{size_b}} Ð±Ð°Ð¹Ñ)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} ÐÐ ({{size_b}} Ð±Ð°Ð¹Ñ)
document_properties_title=ÐÐ°Ð³Ð°Ð»Ð¾Ð²Ð°Ðº:
document_properties_author=ÐÑÑÐ°Ñ:
document_properties_subject=Ð¢ÑÐ¼Ð°:
document_properties_keywords=ÐÐ»ÑÑÐ°Ð²ÑÑ ÑÐ»Ð¾Ð²Ñ:
document_properties_creation_date=ÐÐ°ÑÐ° ÑÑÐ²Ð°ÑÑÐ½Ð½Ñ:
document_properties_modification_date=ÐÐ°ÑÐ° Ð·Ð¼ÑÐ½ÐµÐ½Ð½Ñ:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_creator=Ð¡ÑÐ²Ð°ÑÐ°Ð»ÑÐ½ÑÐº:
document_properties_producer=ÐÑÑÐ°Ð±Ð½ÑÐº PDF:
document_properties_version=ÐÐµÑÑÑÑ PDF:
document_properties_page_count=ÐÐ¾Ð»ÑÐºÐ°ÑÑÑ ÑÑÐ°ÑÐ¾Ð½Ð°Ðº:
document_properties_page_size=ÐÐ°Ð¼ÐµÑ ÑÑÐ°ÑÐ¾Ð½ÐºÑ:
document_properties_page_size_unit_inches=ÑÐ°Ð»ÑÑ
document_properties_page_size_unit_millimeters=Ð¼Ð¼
document_properties_page_size_orientation_portrait=ÐºÐ½ÑÐ¶Ð½Ð°Ñ
document_properties_page_size_orientation_landscape=Ð°Ð»ÑÐ±Ð¾Ð¼Ð½Ð°Ñ
document_properties_page_size_name_a3=A3
document_properties_page_size_name_a4=A4
document_properties_page_size_name_letter=Letter
document_properties_page_size_name_legal=Legal
# LOCALIZATION NOTE (document_properties_page_size_dimension_string):
# "{{width}}", "{{height}}", {{unit}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement and orientation, of the (current) page.
document_properties_page_size_dimension_string={{width}} Ã {{height}} {{unit}} ({{orientation}})
# LOCALIZATION NOTE (document_properties_page_size_dimension_name_string):
# "{{width}}", "{{height}}", {{unit}}, {{name}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement, name, and orientation, of the (current) page.
document_properties_page_size_dimension_name_string={{width}} Ã {{height}} {{unit}} ({{name}}, {{orientation}})
# LOCALIZATION NOTE (document_properties_linearized): The linearization status of
# the document; usually called "Fast Web View" in English locales of Adobe software.
document_properties_linearized=Ð¥ÑÑÐºÑ Ð¿ÑÐ°Ð³Ð»ÑÐ´ Ñ ÐÐ½ÑÑÑÐ½ÑÑÐµ:
document_properties_linearized_yes=Ð¢Ð°Ðº
document_properties_linearized_no=ÐÐµ
document_properties_close=ÐÐ°ÐºÑÑÑÑ

print_progress_message=ÐÐ°Ð´ÑÑÑÑÐ¾ÑÐºÐ° Ð´Ð°ÐºÑÐ¼ÐµÐ½ÑÐ° Ð´Ð° Ð´ÑÑÐºÑâ¦
# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent={{progress}}%
print_progress_close=Ð¡ÐºÐ°ÑÐ°Ð²Ð°ÑÑ

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=ÐÐ°ÐºÐ°Ð·Ð°ÑÑ/ÑÑÐ°Ð²Ð°ÑÑ Ð±Ð°ÐºÐ°Ð²ÑÑ Ð¿Ð°Ð½ÑÐ»Ñ
toggle_sidebar_notification2.title=ÐÐ°ÐºÐ°Ð·Ð°ÑÑ/ÑÑÐ°Ð²Ð°ÑÑ Ð±Ð°ÐºÐ°Ð²ÑÑ Ð¿Ð°Ð½ÑÐ»Ñ (Ð´Ð°ÐºÑÐ¼ÐµÐ½Ñ Ð¼Ð°Ðµ Ð·Ð¼ÐµÑÑ/ÑÐºÐ»Ð°Ð´Ð°Ð½Ð½Ñ/Ð¿Ð»Ð°ÑÑÑ)
toggle_sidebar_label=ÐÐ°ÐºÐ°Ð·Ð°ÑÑ/ÑÑÐ°Ð²Ð°ÑÑ Ð±Ð°ÐºÐ°Ð²ÑÑ Ð¿Ð°Ð½ÑÐ»Ñ
document_outline.title=ÐÐ°ÐºÐ°Ð·Ð°ÑÑ ÑÑÑÑÐºÑÑÑÑ Ð´Ð°ÐºÑÐ¼ÐµÐ½ÑÐ° (Ð´Ð²Ð°Ð¹Ð½Ð°Ñ Ð¿ÑÑÑÑÑÐºÐ°, ÐºÐ°Ð± ÑÐ°Ð·Ð³Ð°ÑÐ½ÑÑÑ /Ð·Ð³Ð°ÑÐ½ÑÑÑ ÑÑÐµ ÑÐ»ÐµÐ¼ÐµÐ½ÑÑ)
document_outline_label=Ð¡ÑÑÑÐºÑÑÑÐ° Ð´Ð°ÐºÑÐ¼ÐµÐ½ÑÐ°
attachments.title=ÐÐ°ÐºÐ°Ð·Ð°ÑÑ Ð´Ð°Ð»ÑÑÑÐ½Ð½Ñ
attachments_label=ÐÐ°Ð»ÑÑÑÐ½Ð½Ñ
layers.title=ÐÐ°ÐºÐ°Ð·Ð°ÑÑ Ð¿Ð»Ð°ÑÑÑ (Ð½Ð°ÑÑÑÐ½ÑÑÐµ Ð´Ð²Ð¾Ð¹ÑÑ, ÐºÐ°Ð± ÑÐºÑÐ½ÑÑÑ ÑÑÐµ Ð¿Ð»Ð°ÑÑÑ Ð´Ð° Ð¿ÑÐ°Ð´Ð²ÑÐ·Ð½Ð°ÑÐ°Ð½Ð°Ð³Ð° ÑÑÐ°Ð½Ñ)
layers_label=ÐÐ»Ð°ÑÑÑ
thumbs.title=ÐÐ°ÐºÐ°Ð· Ð¼ÑÐ½ÑÑÑÑÑ
thumbs_label=ÐÑÐ½ÑÑÑÑÑÑ
current_outline_item.title=ÐÐ½Ð°Ð¹ÑÑÑ Ð±ÑÐ³ÑÑÑ ÑÐ»ÐµÐ¼ÐµÐ½Ñ ÑÑÑÑÐºÑÑÑÑ
current_outline_item_label=ÐÑÐ³ÑÑÑ ÑÐ»ÐµÐ¼ÐµÐ½Ñ ÑÑÑÑÐºÑÑÑÑ
findbar.title=ÐÐ¾ÑÑÐº Ñ Ð´Ð°ÐºÑÐ¼ÐµÐ½ÑÐµ
findbar_label=ÐÐ½Ð°Ð¹ÑÑÑ

additional_layers=ÐÐ°Ð´Ð°ÑÐºÐ¾Ð²ÑÑ Ð¿Ð»Ð°ÑÑÑ
# LOCALIZATION NOTE (page_landmark): "{{page}}" will be replaced by the page number.
page_landmark=Ð¡ÑÐ°ÑÐ¾Ð½ÐºÐ° {{page}}
# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=Ð¡ÑÐ°ÑÐ¾Ð½ÐºÐ° {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=ÐÑÐ½ÑÑÑÑÑÐ° ÑÑÐ°ÑÐ¾Ð½ÐºÑ {{page}}

# Find panel button title and messages
find_input.title=Ð¨ÑÐºÐ°ÑÑ
find_input.placeholder=Ð¨ÑÐºÐ°ÑÑ Ñ Ð´Ð°ÐºÑÐ¼ÐµÐ½ÑÐµâ¦
find_previous.title=ÐÐ½Ð°Ð¹ÑÑÑ Ð¿Ð°Ð¿ÑÑÑÐ´Ð½Ñ Ð²ÑÐ¿Ð°Ð´Ð°Ðº Ð²ÑÑÐ°Ð·Ñ
find_previous_label=ÐÐ°Ð¿ÑÑÑÐ´Ð½Ñ
find_next.title=ÐÐ½Ð°Ð¹ÑÑÑ Ð½Ð°ÑÑÑÐ¿Ð½Ñ Ð²ÑÐ¿Ð°Ð´Ð°Ðº Ð²ÑÑÐ°Ð·Ñ
find_next_label=ÐÐ°ÑÑÑÐ¿Ð½Ñ
find_highlight=ÐÐ°Ð´ÑÐ°ÑÐ±Ð°Ð²Ð°ÑÑ ÑÑÐµ
find_match_case_label=ÐÐ´ÑÐ¾Ð·Ð½ÑÐ²Ð°ÑÑ Ð²ÑÐ»ÑÐºÑÑ/Ð¼Ð°Ð»ÑÑ Ð»ÑÑÐ°ÑÑ
find_match_diacritics_label=Ð ÑÐ»ÑÐºÐ°Ð¼ Ð´ÑÑÐºÑÑÑÑÐº
find_entire_word_label=Ð¡Ð»Ð¾Ð²Ñ ÑÐ°Ð»ÐºÐ°Ð¼
find_reached_top=ÐÐ°ÑÑÐ³Ð½ÑÑÑ Ð¿Ð°ÑÐ°ÑÐ°Ðº Ð´Ð°ÐºÑÐ¼ÐµÐ½ÑÐ°, Ð¿ÑÐ°ÑÑÐ³ Ð· ÐºÐ°Ð½ÑÐ°
find_reached_bottom=ÐÐ°ÑÑÐ³Ð½ÑÑÑ ÐºÐ°Ð½ÐµÑ Ð´Ð°ÐºÑÐ¼ÐµÐ½ÑÐ°, Ð¿ÑÐ°ÑÑÐ³ Ð· Ð¿Ð°ÑÐ°ÑÐºÑ
# LOCALIZATION NOTE (find_match_count): The supported plural forms are
# [one|two|few|many|other], with [other] as the default value.
# "{{current}}" and "{{total}}" will be replaced by a number representing the
# index of the currently active find result, respectively a number representing
# the total number of matches in the document.
find_match_count={[ plural(total) ]}
find_match_count[one]={{current}} Ð· {{total}} ÑÑÐ¿Ð°Ð´Ð·ÐµÐ½Ð½Ñ
find_match_count[two]={{current}} Ð· {{total}} ÑÑÐ¿Ð°Ð´Ð·ÐµÐ½Ð½ÑÑ
find_match_count[few]={{current}} Ð· {{total}} ÑÑÐ¿Ð°Ð´Ð·ÐµÐ½Ð½ÑÑ
find_match_count[many]={{current}} Ð· {{total}} ÑÑÐ¿Ð°Ð´Ð·ÐµÐ½Ð½ÑÑ
find_match_count[other]={{current}} Ð· {{total}} ÑÑÐ¿Ð°Ð´Ð·ÐµÐ½Ð½ÑÑ
# LOCALIZATION NOTE (find_match_count_limit): The supported plural forms are
# [zero|one|two|few|many|other], with [other] as the default value.
# "{{limit}}" will be replaced by a numerical value.
find_match_count_limit={[ plural(limit) ]}
find_match_count_limit[zero]=ÐÐ¾Ð»ÑÑ Ð·Ð° {{limit}} ÑÑÐ¿Ð°Ð´Ð·ÐµÐ½Ð½ÑÑ
find_match_count_limit[one]=ÐÐ¾Ð»ÑÑ Ð·Ð° {{limit}} ÑÑÐ¿Ð°Ð´Ð·ÐµÐ½Ð½Ðµ
find_match_count_limit[two]=ÐÐ¾Ð»ÑÑ Ð·Ð° {{limit}} ÑÑÐ¿Ð°Ð´Ð·ÐµÐ½Ð½ÑÑ
find_match_count_limit[few]=ÐÐ¾Ð»ÑÑ Ð·Ð° {{limit}} ÑÑÐ¿Ð°Ð´Ð·ÐµÐ½Ð½ÑÑ
find_match_count_limit[many]=ÐÐ¾Ð»ÑÑ Ð·Ð° {{limit}} ÑÑÐ¿Ð°Ð´Ð·ÐµÐ½Ð½ÑÑ
find_match_count_limit[other]=ÐÐ¾Ð»ÑÑ Ð·Ð° {{limit}} ÑÑÐ¿Ð°Ð´Ð·ÐµÐ½Ð½ÑÑ
find_not_found=ÐÑÑÐ°Ð· Ð½Ðµ Ð·Ð½Ð¾Ð¹Ð´Ð·ÐµÐ½Ñ

# Error panel labels
error_more_info=ÐÐ°Ð´ÑÐ°Ð±ÑÐ·Ð½ÐµÐ¹
error_less_info=Ð¡ÑÑÑÐ»Ð°
error_close=ÐÐ°ÐºÑÑÑÑ
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js Ð²{{version}} (Ð·Ð±Ð¾ÑÐºÐ°: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=ÐÐ°Ð²ÐµÐ´Ð°Ð¼Ð»ÐµÐ½Ð½Ðµ: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=Ð¡ÑÐ¾Ñ: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=Ð¤Ð°Ð¹Ð»: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=Ð Ð°Ð´Ð¾Ðº: {{line}}

# Predefined zoom values
page_scale_width=Ð¨ÑÑÑÐ½Ñ ÑÑÐ°ÑÐ¾Ð½ÐºÑ
page_scale_fit=Ð£ÑÑÑÐ½ÐµÐ½Ð½Ðµ ÑÑÐ°ÑÐ¾Ð½ÐºÑ
page_scale_auto=ÐÑÑÐ°Ð¼Ð°ÑÑÑÐ½Ð°Ðµ Ð¿Ð°Ð²ÐµÐ»ÑÑÑÐ½Ð½Ðµ
page_scale_actual=Ð¡Ð°Ð¿ÑÐ°ÑÐ´Ð½Ñ Ð¿Ð°Ð¼ÐµÑ
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

# Loading indicator messages
loading=Ð§ÑÑÐ°ÐµÑÑÐ°â¦
loading_error=ÐÐ´Ð°ÑÑÐ»Ð°ÑÑ Ð¿Ð°Ð¼ÑÐ»ÐºÐ° ÑÂ ÑÐ°ÑÐµ Ð·Ð°Ð³ÑÑÐ·ÐºÑ PDF.
invalid_file_error=ÐÑÑÐ¿ÑÐ°ÑÐ½Ñ Ð°Ð±Ð¾ Ð¿Ð°ÑÐºÐ¾Ð´Ð¶Ð°Ð½Ñ ÑÐ°Ð¹Ð» PDF.
missing_file_error=ÐÐ´ÑÑÑÐ½Ñ ÑÐ°Ð¹Ð» PDF.
unexpected_response_error=ÐÐµÑÐ°ÐºÐ°Ð½Ñ Ð°Ð´ÐºÐ°Ð· ÑÐµÑÐ²ÐµÑÐ°.

rendering_error=ÐÐ´Ð°ÑÑÐ»Ð°ÑÑ Ð¿Ð°Ð¼ÑÐ»ÐºÐ° Ð¿Ð°Ð´ÑÐ°Ñ Ð°Ð´Ð»ÑÑÑÑÐ°Ð²Ð°Ð½Ð½Ñ ÑÑÐ°ÑÐ¾Ð½ÐºÑ.

# LOCALIZATION NOTE (annotation_date_string): "{{date}}" and "{{time}}" will be
# replaced by the modification date, and time, of the annotation.
annotation_date_string={{date}}, {{time}}

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 â Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[{{type}} Annotation]
password_label=Ð£Ð²ÑÐ´Ð·ÑÑÐµ Ð¿Ð°ÑÐ¾Ð»Ñ, ÐºÐ°Ð± Ð°Ð´ÐºÑÑÑÑ Ð³ÑÑÑ ÑÐ°Ð¹Ð» PDF.
password_invalid=ÐÑÐ´Ð·ÐµÐ¹ÑÐ½Ñ Ð¿Ð°ÑÐ¾Ð»Ñ. ÐÐ°ÑÐ¿ÑÐ°Ð±ÑÐ¹ÑÐµ Ð·Ð½Ð¾Ñ.
password_ok=ÐÐ¾Ð±ÑÐ°
password_cancel=Ð¡ÐºÐ°ÑÐ°Ð²Ð°ÑÑ

printing_not_supported=ÐÐ°Ð¿ÑÑÑÐ´Ð¶Ð°Ð½Ð½Ðµ: Ð´ÑÑÐº Ð½Ðµ Ð¿Ð°Ð´ÑÑÑÐ¼Ð»ÑÐ²Ð°ÐµÑÑÐ° ÑÐ°Ð»ÐºÐ°Ð¼ Ð³ÑÑÑÐ¼ Ð±ÑÐ°ÑÐ·ÐµÑÐ°Ð¼.
printing_not_ready=Ð£Ð²Ð°Ð³Ð°: PDF Ð½Ðµ ÑÑÑÐ³Ð½ÑÑÑ ÑÐ°Ð»ÐºÐ°Ð¼ Ð´Ð»Ñ Ð´ÑÑÐºÐ°Ð²Ð°Ð½Ð½Ñ.
web_fonts_disabled=Ð¨ÑÑÑÑÑ Ð¡ÐµÑÑÐ²Ð° Ð·Ð°Ð±Ð°ÑÐ¾Ð½ÐµÐ½Ñ: Ð½ÐµÐ¼Ð°Ð³ÑÑÐ¼Ð° ÑÐ¶ÑÐ²Ð°ÑÑ ÑÐºÐ»Ð°Ð´Ð·ÐµÐ½ÑÑ ÑÑÑÑÑÑ PDF.

# Editor
editor_free_text2.title=Ð¢ÑÐºÑÑ
editor_free_text2_label=Ð¢ÑÐºÑÑ
editor_ink2.title=ÐÐ°Ð»ÑÐ²Ð°ÑÑ
editor_ink2_label=ÐÐ°Ð»ÑÐ²Ð°ÑÑ

free_text2_default_content=ÐÐ°ÑÐ½ÑÑÐµ Ð½Ð°Ð±Ð¾Ñ ÑÑÐºÑÑÑâ¦

# Editor Parameters
editor_free_text_color=ÐÐ¾Ð»ÐµÑ
editor_free_text_size=ÐÐ°Ð¼ÐµÑ
editor_ink_color=ÐÐ¾Ð»ÐµÑ
editor_ink_thickness=Ð¢Ð°ÑÑÑÑÐ½Ñ
editor_ink_opacity=ÐÐµÐ¿ÑÐ°Ð·ÑÑÑÑÐ°ÑÑÑ

# Editor aria
editor_free_text2_aria_label=Ð¢ÑÐºÑÑÐ°Ð²Ñ ÑÑÐ´Ð°ÐºÑÐ°Ñ
editor_ink2_aria_label=ÐÑÐ°ÑÑÑÐ½Ñ ÑÑÐ´Ð°ÐºÑÐ°Ñ
editor_ink_canvas_aria_label=ÐÑÑÐ²Ð°, ÑÑÐ²Ð¾ÑÐ°Ð½Ð°Ñ ÐºÐ°ÑÑÑÑÐ°Ð»ÑÐ½ÑÐºÐ°Ð¼
