<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
</head>
<body>
<div class="center_weizhi">
当前位置：公众投票 - 先进个人评选 -
		             <c:choose>
		             	<c:when test="${areaType ==1}">
		             		省级公众投票结果
		             	</c:when>
		             	<c:when test="${areaType ==2}">
		             		 市级公众投票结果
		             	</c:when>
		             	<c:when test="${areaType ==3}">
		             		 县级公众投票结果
		             	</c:when>
		             	<c:otherwise>
		             	</c:otherwise>
		             </c:choose>
</div>
<div class="center">
<div class="center_list">
    <div class="panel-group" id="accordion">
          <!---快速查询--->
  		<form id= "searchForm"  role="form">
              <input type="hidden" name="areaType" value="${areaType}" id="areaTypeSelect">
        </form>
              <button type="button" class="btn btn-primary" id ="viewExcel" >导出EXCEL</button> 
    </div>
        <table class="table table-bordered table-hover table-condensed">
           <c:if test="${areaType ==1 }">
            <thead>
           <tr>
             <td width="50" height="30" bgcolor="#efefef">序号</td>
             <td bgcolor="#efefef">省</td>
             <td width="100" bgcolor="#efefef">候选人姓名</td>
             <td width="150" bgcolor="#efefef">候选人身份证号码</td>
             <td width="100" bgcolor="#efefef">得票数</td>
             <td width="100" bgcolor="#efefef">分值</td>
             <td width="100" bgcolor="#efefef">排名</td>
           </tr>
         </thead>
         <tbody>
  			<c:forEach items="${electionPersonalList.list}" var="item" varStatus="status">
	           <tr>
	             <td height="30" align="center">${status.index+1}<input id="id${status.index+1}" type="hidden"  value="${item.id}"></td>
	             <td>${item.province}</td>
	             <td>${item.name}</td>
	             <td>${item.cardid}</td>
	             <td>${item.publicvotenum}</td>
	             <td>${item.publicvotenumscore}</td>
	             <td>
		             <c:choose>
		             	<c:when test="${item.publicvoteranking !='' && item.publicvoteranking !=null }">
		             		第${item.publicvoteranking }名
		             	</c:when>
		             	<c:otherwise>
		             	</c:otherwise>
		             </c:choose>
		         </td>
	           </tr>
           </c:forEach>
         </tbody>
           </c:if>
                 <c:if test="${areaType ==2 }">
            <thead>
           <tr>
             <td width="50" height="30" bgcolor="#efefef">序号</td>
             <td bgcolor="#efefef">省</td>
             <td bgcolor="#efefef">地市</td>
             <td width="100" bgcolor="#efefef">候选人姓名</td>
             <td width="150" bgcolor="#efefef">候选人身份证号码</td>
             <td width="100" bgcolor="#efefef">得票数</td>
             <td width="100" bgcolor="#efefef">分值</td>
             <td width="100" bgcolor="#efefef">排名</td>
           </tr>
         </thead>
         <tbody>
  			<c:forEach items="${electionPersonalList.list}" var="item" varStatus="status">
	           <tr>
	             <td height="30" align="center">${status.index+1}<input id="id${status.index+1}" type="hidden"  value="${item.id}"></td>
	             <td>${item.province}</td>
	             <td>${item.city}</td>
	             <td>${item.name}</td>
	             <td>${item.cardid}</td>
	             <td>${item.publicvotenum}</td>
	             <td>${item.publicvotenumscore}</td>
	             <td>
		             <c:choose>
		             	<c:when test="${item.publicvoteranking !='' && item.publicvoteranking !=null }">
		             		第${item.publicvoteranking }名
		             	</c:when>
		             	<c:otherwise>
		             	</c:otherwise>
		             </c:choose>
		         </td>
	           </tr>
           </c:forEach>
         </tbody>
           </c:if>
                 <c:if test="${areaType ==3 }">
            <thead>
           <tr>
             <td width="50" height="30" bgcolor="#efefef">序号</td>
             <td bgcolor="#efefef">省</td>
             <td bgcolor="#efefef">地市</td>
             <td bgcolor="#efefef">区县</td>
             <td width="100" bgcolor="#efefef">候选人姓名</td>
             <td width="150" bgcolor="#efefef">候选人身份证号码</td>
             <td width="100" bgcolor="#efefef">得票数</td>
             <td width="100" bgcolor="#efefef">分值</td>
             <td width="100" bgcolor="#efefef">排名</td>
           </tr>
         </thead>
         <tbody>
  			<c:forEach items="${electionPersonalList.list}" var="item" varStatus="status">
	           <tr>
	             <td height="30" align="center">${status.index+1}<input id="id${status.index+1}" type="hidden"  value="${item.id}"></td>
	             <td>${item.province}</td>
	             <td>${item.city}</td>
	             <td>${item.country}</td>
	             <td>${item.name}</td>
	             <td>${item.cardid}</td>
	             <td>${item.publicvotenum}</td>
	             <td>${item.publicvotenumscore}</td>
	             <td>
		             <c:choose>
		             	<c:when test="${item.publicvoteranking !='' && item.publicvoteranking !=null }">
		             		第${item.publicvoteranking }名
		             	</c:when>
		             	<c:otherwise>
		             	</c:otherwise>
		             </c:choose>
		         </td>
	           </tr>
           </c:forEach>
         </tbody>
           </c:if>
        
       </table>
             <input type="hidden" value="${areaType}" id ="areaType">
    </div>
    <!--列表翻页 开始-->
    <div class="page">
    	<span style="padding:12px; float:left; color:#0099cc;">共${electionPersonalList.total}条记录</span>
        <ul class="pagination" id="pageCon">
            
        </ul>
    </div>
    <!--列表翻页 结束-->
</div>
<script type="text/javascript">

// 搜索条件
$(document).ready(function(){
	$("#areaTypeSelect").change(function(){
		business.addMainContentParserHtml("gztpGerenList.do",$("#searchForm").serialize());
	});
	$("#viewExcel").click(function(){
	  var viewType =$("#areaType").val();
	  var path = WEBPATH+"/ajpfViewExcel.do?target=6&viewType="+viewType; 
      window.location.href=path;
	});
});
//分页
$(document).ready(function(){
	var curentPage = eval('${electionPersonalList.pageNum}');
	var totalPage = eval('${electionPersonalList.pages}');
	var areaType = eval('${areaType}');
	if(totalPage>0){
		var options = {
			bootstrapMajorVersion: 3,
		    currentPage: curentPage,
		    totalPages: totalPage,
		    numberOfPages: 5,
		    itemTexts: function (type, page, current) {
		            switch (type) {
		            case "first":
		                return "首页";
		            case "prev":
		                return "&laquo;";
		            case "next":
		                return "&raquo;";
		            case "last":
		                return "尾页";
		            case "page":
		                return page;
		            }
		    },
		    onPageClicked: function (event, originalEvent, type, page) {
            	business.addMainContentParserHtml(WEBPATH+'/gztpGerenList.do?pageNum='+page,$("#searchForm").serialize());
            }
    	};
    	$('#pageCon').bootstrapPaginator(options);
   	}
});
</script>
</body>
</html>
