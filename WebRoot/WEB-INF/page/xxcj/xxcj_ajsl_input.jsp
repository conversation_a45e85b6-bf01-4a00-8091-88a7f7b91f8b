<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
<script type="text/javascript">
$(document).ready(function(){
	 //文件上传1、
	 $("#Profile").fileinput({
        uploadUrl: WEBPATH+'/pdffileupload.do', // you must set a valid URL here else you will get an error
        allowedFileExtensions : ['pdf'],
        language:'zh',
        //overwriteInitial: true,
        minFileCount: 1,
        maxFileCount: 1,
        minFileSize:1,
        maxFileSize:51200,
        enctype: 'multipart/form-data',        
        dropZoneTitle:"可拖拽文件到此处...",
        initialPreviewShowDelete: false,
        msgFilesTooMany:'选择上传的文件数量({n}) 超过允许的最大数值{m}！',
        msgZoomModalHeading:'文件预览',
        msgInvalidFileExtension:'不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
        msgNoFilesSelected:'请选择文件',
        msgValidationError:'文件类型不正确或文件过大',
        initialPreviewFileType:'pdf',
        browseLabel:"选择文件",
        removeLabel:'删除',
        removeTitle:'删除文件',
        uploadLabel:'上传',
        uploadTitle:'上传文件',
        cancelLabel: '取消',
        cancelTitle: '取消上传',
        showPreview:false,
        autoReplace:true,
        slugCallback: function(filename) {
            return filename.replace('(', '_').replace(']', '_');
        }
	}).on('filepreupload', function(event, data, previewId, index) { //文件开始上传操作
		
	   $("#xxcj_AJSL_Butt").prop('disabled', true);
	
	}).on('fileuploaded', function(event, data, previewId, index) {//文件上传完成执行操作

		$("#province_Fileurl").val(data.response.url);
	  	$("#province_Filename").val(data.response.fileRealName);
	  	$("#wsc").text(data.response.fileRealName);
	  	$("#xxcj_AJSL_Butt").prop('disabled',false);
	  	
	})/* .on('fileclear', function(event) {  //文件上传删除文件执行的操作
		
    	$("#province_Fileurl").val("");
	  	$("#province_Filename").val("");
	  	$("#wsc").text("未上传");
    }); */
    
    $("#reButton").click(function(){
    	$("#uploadTr").attr("style","");
    });
    
    //文件上传2、
	 $("#Cityfile").fileinput({
        uploadUrl: WEBPATH+'/pdffileupload.do', // you must set a valid URL here else you will get an error
        allowedFileExtensions : ['pdf'],
        language:'zh',
        //overwriteInitial: true,
        minFileCount: 1,
        maxFileCount: 1,
        minFileSize:1,
        maxFileSize:51200,
        enctype: 'multipart/form-data',        
        dropZoneTitle:"可拖拽文件到此处...",
        initialPreviewShowDelete: false,
        msgFilesTooMany:'选择上传的文件数量({n}) 超过允许的最大数值{m}！',
        msgZoomModalHeading:'文件预览',
        msgInvalidFileExtension:'不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
        msgNoFilesSelected:'请选择文件',
        msgValidationError:'文件类型不正确或文件过大',
        initialPreviewFileType:'pdf',
        browseLabel:"选择文件",
        removeLabel:'删除',
        removeTitle:'删除文件',
        uploadLabel:'上传',
        uploadTitle:'上传文件',
        cancelLabel: '取消',
        cancelTitle: '取消上传',
        showPreview:false,
        autoReplace:true,
        slugCallback: function(filename) {
            return filename.replace('(', '_').replace(']', '_');
        }
	}).on('filepreupload', function(event, data, previewId, index) { //文件开始上传操作
		
	   $("#xxcj_AJSL_Butt").prop('disabled', true);
	
	}).on('fileuploaded', function(event, data, previewId, index) {//文件上传完成执行操作

		$("#city_Fileurl").val(data.response.url);
	  	$("#city_Filename").val(data.response.fileRealName);
	  	$("#wsc2").text(data.response.fileRealName);
	  	$("#xxcj_AJSL_Butt").prop('disabled',false);
	  	
	})/* .on('fileclear', function(event) {  //文件上传删除文件执行的操作
		
    	$("#city_Fileurl").val("");
	  	$("#city_Filename").val("");
	  	$("#wsc2").text("未上传");
    }); */
    
    $("#reButtonCity").click(function(){
    	$("#uploadTr2").attr("style","");
    });
	
});
</script>
</head>
<body>
<div class="center_weizhi">当前位置：信息采集 - 参选先进集体信息  - 重点监控企业数量上报</div>
<div class="center">
<form id="ajslForm">
<div class="center_list">
  <table align="center" class="table_input">
    <tbody>
    <c:if test="${xxcj_AJSL.id!=''&&xxcj_AJSL.id!=null}">
      
		      <tr>
		        <td width="250">&nbsp;</td>
		        <td><span style="color:#295A5F; font-size:18px; float:left; font-weight:bold;">${xxcj_AJSL.areaname }</span></td>
		      </tr>
		      <tr>
		        <td>行政区域内国控企业数量</td>
		        <td style="text-align:left;">${xxcj_AJSL.areaguokongqynum }</td>
		      </tr>
		      <tr>
		        <td>行政区域内省级重点监控企业数量</td>
		        <td>
		        	<div class="form-group">
		        		<input type="text" class="form-control" id="provincezhongdianqynum0" name="provincezhongdianqynum" value="${xxcj_AJSL.provincezhongdianqynum }" placeholder="请输入行政区域内省级重点监控企业数量">
		        	</div>
		        </td>
		      </tr>
		      <c:if test="${xxcj_AJSL.areatype=='1' }">
		      <tr>
		        <td>正式发文的名单扫描件</td>
		        <td style="text-align:left;">
		        <span id="wsc" style="font-size:16px;">
		        <c:choose>
		        	<c:when test="${xxcj_AJSL.provinceofficialdocument!='' && xxcj_AJSL.provinceofficialdocument!=null }">
		        		<div style="float:left;margin-right:8px;padding-top:4px;">
		        			${xxcj_AJSL.provinceofficialdocument}
		        		</div>
		        	</c:when>
		        	<c:otherwise>
		        	<font >未上传</font>
		        	</c:otherwise>
		        </c:choose>
		        </span> 
		        <c:if test="${xxcj_AJSL.provinceofficialdocument!='' && xxcj_AJSL.provinceofficialdocument!=null }">
		        		<input id="reButton" class="btn btn-primary btn-xs" type="button" value="重新上传"/>
		        </c:if>
		        </td>
		      </tr>
		      <tr id="uploadTr" <c:if test="${xxcj_AJSL.provinceofficialdocument!='' && xxcj_AJSL.provinceofficialdocument!=null }">style='display:none'</c:if>>
		        <td>&nbsp;</td>
		        <td style="text-align:left;">
		        <input id="Profile" type="file" name="Profile" class="file-loading">
		        <input type="hidden" class="form-control" name="provinceofficialdocument" id="province_Filename" value="${xxcj_AJSL.provinceofficialdocument}">
				<input type="hidden" class="form-control" name="provinceofficialdocumenturl" id="province_Fileurl" value="${xxcj_AJSL.provinceofficialdocumenturl}">
		        </td>
		      </tr>
		      </c:if>
		      
		  <c:if test="${user.areaCode!='55000000' && user.areaCode!='11000000' && user.areaCode!='12000000' && user.areaCode!='31000000' && user.areaCode!='66000000'}">
		      <tr>
		        <td>行政区域内市级重点监控企业数量</td>
		        <td>
		        <div class="form-group">
		        <input type="text" class="form-control" id="cityzhongdianqynum0" name="cityzhongdianqynum" value="${xxcj_AJSL.cityzhongdianqynum }" placeholder="请输入行政区域内市级重点监控企业数量">
		        </div>
		        </td>
		      </tr>
		      <c:if test="${xxcj_AJSL.areatype=='1' }">
			      <tr>
			        <td>正式发文的名单扫描件</td>
			        <td style="text-align:left;">
			        <span id="wsc2" style="font-size:16px;">
			        <c:choose>
			        	<c:when test="${xxcj_AJSL.cityofficialdocument!='' && xxcj_AJSL.cityofficialdocument!=null }">
			        		<div style="float:left;margin-right:8px;padding-top:4px;">
			        			${xxcj_AJSL.cityofficialdocument}
			        		</div>
			        	</c:when>
			        	<c:otherwise>
			        	<font>未上传</font>
			        	</c:otherwise>
			        </c:choose>
			        </span> 
			        <c:if test="${xxcj_AJSL.cityofficialdocument!='' && xxcj_AJSL.cityofficialdocument!=null }">
			        		<input id="reButtonCity" class="btn btn-primary btn-xs" type="button" value="重新上传"/>
			        </c:if>
			        </td>
			      </tr>
			      <tr id="uploadTr2" <c:if test="${xxcj_AJSL.cityofficialdocument!='' && xxcj_AJSL.cityofficialdocument!=null }">style='display:none'</c:if>>
			        <td>&nbsp;</td>
			        <td style="text-align:left;">
			        <input id="Cityfile" type="file" name="Cityfile" class="file-loading">
			        <input type="hidden" class="form-control" name="cityofficialdocument" id="city_Filename" value="${xxcj_AJSL.cityofficialdocument}">
					<input type="hidden" class="form-control" name="cityofficialdocumenturl" id="city_Fileurl" value="${xxcj_AJSL.cityofficialdocumenturl}">
			        </td>
			      </tr>
		      </c:if>
		   </c:if>
      </c:if>
      <c:if test="${1==1 }">
	      <tr>
	        <td align="center">&nbsp;
	        <input type="hidden" name="id" value="${xxcj_AJSL.id }"/>
	        </td>
	        <td style="text-align:left;"><a href="#">
	          <button type="button"  id="xxcj_AJSL_Butt" class="btn btn-primary" name="signup" value="Sign up" style="font-size:16px;width:150px; margin-top:5px;">信息保存</button>
	          </a></td>
	      </tr>
      </c:if>
    </tbody>
  </table>
    </div>
    </form>
</div>
<script language="JavaScript">


	//表单校验
	$(document).ready(function() {
	$('#ajslForm').formValidation({
		message: 'This value is not valid',
	    icon: {
	    	valid: 'glyphicon glyphicon-ok',
	        invalid: 'glyphicon glyphicon-remove',
	        validating: 'glyphicon glyphicon-refresh'
	    },
	    autoFocus: true,
	    fields: {
	        "provincezhongdianqynum": {
	        	validators: {
	            	notEmpty: {
	                	message: '请填写该行政区域内省级重点监控企业数量.'
	                },
	                stringLength: {
	                	max: 9,
	                	message: '该项不能超过9个字符.'
	                },
	                regexp: {
	                    regexp: /^[0-9]+$/,
	                    message: '该项只能填写整数'
	                }
	            }
	        },
	        "cityzhongdianqynum": {
	        	validators: {
	            	notEmpty: {
	                	message: '请填写该行政区域内市级重点监控企业数量.'
	                },
	                stringLength: {
	                	max: 9,
	                	message: '该项不能超过9个字符.'
	                },
	                regexp: {
	                    regexp: /^[0-9]+$/,
	                    message: '该项只能填写整数'
	                }
	            }
	        }
	        
	              
	        }
	    });
	});
	
	
	
	//表单提交
	$(document).ready(function(){
		$("#xxcj_AJSL_Butt").click(function(){
			var validate = false;
			$("#ajslForm").data('formValidation').validate();
			validate = $("#ajslForm").data('formValidation').isValid();
			if(validate){
				var fileurl=$("#province_Fileurl").val();
       			var filename=$("#province_Filename").val();
       			var fileurl1=$("#city_Fileurl").val();
       			var filename1=$("#city_Filename").val();
				if(fileurl!=""&&filename!=""&&fileurl1!=""&&filename1!=""){
					var options = {
						url:WEBPATH+'/xxcj/savXxcjAJSL.do',
						type:'post',
						success:function(data){
						if(data.type=="error"){
							swal("保存失败!", "", "error");
			                return false;
			             }else if(data.type=="success"){
			             	swal({
							    	title: "保存成功!",
							        type: "success",
							        closeOnConfirm: true,
							        confirmButtonText: "确定",
							        confirmButtonColor: "#A7D5EA"
						    	}, function() {
							    	business.addMainContentParserHtml('xxcj/xxcjAjsl.do','');
							});
			               return false;
			             }	
						},
						error:function(){
							swal("服务异常,保存失败!", "", "error");
						}
					};
					$("#ajslForm").ajaxSubmit(options);
				}else{
					swal("请上传正式发文的名单扫描件!", "", "error");
				}
			}else if(validate==null){
				//表单未填写
				$("#ajslForm").data('formValidation').validate();
	       
	        }
		});
	});
	
	
	
$(document).ready(function() {
	$(".topnav").accordion({
		accordion:false,
		speed: 500,
		closedSign: '[+]',
		openedSign: '[-]'
	});
});

</script>
</body>
</html>
