<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
<script type="text/javascript">
$(document).ready(function(){
	 //文件上传、
	 $("#file,#file2").fileinput({
        uploadUrl: WEBPATH+'/pdffileupload.do', // you must set a valid URL here else you will get an error
        allowedFileExtensions : ['pdf'],
        language:'zh',
        //overwriteInitial: true,
        minFileCount: 1,
        maxFileCount: 1,
        minFileSize:1,
        maxFileSize:51200,
        enctype: 'multipart/form-data',        
        dropZoneTitle:"可拖拽文件到此处...",
        initialPreviewShowDelete: false,
        msgFilesTooMany:'选择上传的文件数量({n}) 超过允许的最大数值{m}！',
        msgZoomModalHeading:'文件预览',
        msgInvalidFileExtension:'不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
        msgNoFilesSelected:'请选择文件',
        msgValidationError:'文件类型不正确或文件过大',
        initialPreviewFileType:'pdf',
        browseLabel:"选择文件",
        removeLabel:'删除',
        removeTitle:'删除文件',
        uploadLabel:'上传',
        uploadTitle:'上传文件',
        cancelLabel: '取消',
        cancelTitle: '取消上传',
        showPreview:false,
        autoReplace:true,
        slugCallback: function(filename) {
            return filename.replace('(', '_').replace(']', '_');
        }
	}).on('filepreupload', function(event, data, previewId, index) { //文件开始上传操作
		
	   $("#saveProvinceJichaBtn").prop('disabled', true);
	
	}).on('fileuploaded', function(event, data, previewId, index) {//文件上传完成执行操作
		if(event.currentTarget.id=="file"){
			$("#fileurl").val(data.response.url);
		  	$("#filename").val(data.response.fileRealName);
		  	$("#wsc").text(data.response.fileRealName);
		}else{
			$("#fileurl2").val(data.response.url);
		  	$("#filename2").val(data.response.fileRealName);
		  	$("#wsc2").text(data.response.fileRealName);
		}
		
	  	$("#saveProvinceJichaBtn").prop('disabled',false);
	  	
	})/* .on('fileclear', function(event) {  //文件上传删除文件执行的操作
		if(event.currentTarget.id=="file"){
			$("#fileurl").val("");
		  	$("#filename").val("");
		  	$("#wsc").text("未上传");
		}else{
			$("#fileurl2").val("");
		  	$("#filename2").val("");
		  	$("#wsc2").text("未上传");
		}
    	
    }) */
    
	//保存数据方法
	$('#saveProvinceJichaBtn').click(function() {
       	 var options = {
           url: WEBPATH+'/xxcj/saveXxcjJichaList.do',
           type: 'post',
           success:function(data){
	           if(data.result=="error"){
	        	   swal("操作失败", data.message, "error");
	               return false;
	           }else if(data.result=="success"){
	        	   	business.addMainContentParserHtml('xxcj/xxcjJichaProvinceInput.do',null);
	        	  	swal({title: "保存成功",text: "",type:"success"});
	             	return false;
	           }else if(data.code=="007"){
	           		swal(data.message, "", "info");
	            	return false;
	           }
         	}
       	 };
       	$("#saveProvinceJichaForm").data('formValidation').validate();
       	var validate = $("#saveProvinceJichaForm").data('formValidation').isValid();
       	if(validate){
       		var fileurl=$("#fileurl").val();
       		var filename=$("#filename").val();
       		var code1=$("[name='list[0].files.filecode']").val();
       		var code2=$("[name='list[1].files.filecode']").val();
       		var status=$("#showJichatwo").text();
       		if(status=="取消添加"){
       		   var fileurl2=$("#fileurl2").val();
        	   var filename2=$("#filename2").val();
       		   if((fileurl==""||filename=="")||(fileurl2==""||filename2=="")){
       			 swal("提示", "请选择上传文件", "error");
       			 return;
       		   }else{
       			   var id1='${fd1.checkFileDesc.id}';
       			   var id2='${fd2.checkFileDesc.id}'
       			   if((id1==null&&id2==null)||(id1==""&&id2=="")){
       				if(code1==code2){
         				 swal("提示", "两份稽查案卷不能有相同的稽查意见书文号", "error");
         				 return;
         			 }
       			   }
    				 $("#isdel").val(0);
        			 $('#saveProvinceJichaForm').ajaxSubmit(options);
       		   }
       		}else{
       			if(fileurl==""||filename==""){
           		 swal("提示", "请选择上传文件", "error");
           		}else{
           		 	var id2='${fd2.checkFileDesc.id}';
           			if(id2!=""&&id2!=null){
           				$("#isdel").val(1);
           			}else{
           				$("#isdel").val(0);
           			}
           			$('#saveProvinceJichaForm').ajaxSubmit(options);
           		}
       		}
       	}
   	});
            
     var txt='${fd2.checkFileDesc.id}';  
	 if(txt!=null&&txt!=""){
		 $("#showJichatwo").text("取消添加");
	 }else{
		 $("#showJichatwo").text("添加稽查案卷二");
	 }
      
	//表单验证
	$("#saveProvinceJichaForm").formValidation({
        framework: 'bootstrap',
        message: 'This value is not valid',
        icon:{
	            valid: 'glyphicon glyphicon-ok',
	            invalid: 'glyphicon glyphicon-remove',
	            validating: 'glyphicon glyphicon-refresh'
        },
        fields: {
        	"list[0].checkFileDesc.checkunit": {
                validators: {
                    notEmpty: {
                        message: '请输入稽查单位'
                    },
                    stringLength: {
                        min: 1,
                        max: 30,
                        message: '1-30个字符'
                    }
                }
            },
            "list[0].checkFileDesc.checkdate": {
                validators: {
                    notEmpty: {
                        message: '请输入稽查时间'
                    },
                    stringLength: {
                        min: 1,
                        max: 20,
                        message: '1-20个字符'
                    }
                }
            },
            "list[0].checkFileDesc.checkcontent": {
                validators: {
                    notEmpty: {
                        message: '请输入稽查内容'
                    },
                    stringLength: {
                        min: 1,
                        max: 200,
                        message: '1-200个字符'
                    }
                }
            },
            "list[0].files.filecode": {
                validators: {
                    notEmpty: {
                        message: '请输入稽查意见书文号'
                    },
                    stringLength: {
                        min: 1,
                        max: 50,
                        message: '1-50个字符'
                    },
                    remote: {
                 	   dataType:'json',
                 	   type:'POST',
                 	   delay:500,
                        url: '${webpath}/xxcj/checkJiChaAnJuan.do',
                        data: function(validator, $field, value) {
                            return {
                                filecode:validator.getFieldElements('list[0].files.filecode').val(),
                                id:'${fd1.files.id}'
                            };
                        },
                 	   message:'稽查意见书文号已经存在，不允许重复录入'
                    }
                }
            },
            "list[0].checkFileDesc.checkedunit": {
                validators: {
                    notEmpty: {
                        message: '请输入被稽查单位'
                    },
                    stringLength: {
                        min: 1,
                        max: 50,
                        message: '1-50个字符'
                    }
                }
            },
            
            
            "list[1].checkFileDesc.checkunit": {
                validators: {
                    notEmpty: {
                        message: '请输入稽查单位'
                    },
                    stringLength: {
                        min: 1,
                        max: 30,
                        message: '1-30个字符'
                    }
                }
            },
            "list[1].checkFileDesc.checkdate": {
                validators: {
                    notEmpty: {
                        message: '请输入稽查时间'
                    },
                    stringLength: {
                        min: 1,
                        max: 20,
                        message: '1-20个字符'
                    }
                }
            },
            "list[1].checkFileDesc.checkcontent": {
                validators: {
                    notEmpty: {
                        message: '请输入稽查内容'
                    },
                    stringLength: {
                        min: 1,
                        max: 200,
                        message: '1-200个字符'
                    }
                }
            },
           "list[1].files.filecode": {
                validators: {
                    notEmpty: {
                        message: '请输入稽查意见书文号'
                    },
                    stringLength: {
                        min: 1,
                        max: 50,
                        message: '1-50个字符'
                    },
                    remote: {
                  	   dataType:'json',
                  	   type:'POST',
                  	   delay:500,
                         url: '${webpath}/xxcj/checkJiChaAnJuan.do',
                         data: function(validator, $field, value) {
                             return {
                            	 filecode:validator.getFieldElements('list[1].files.filecode').val(),
                                 id:'${fd2.files.id}'
                             };
                         },
                  	   message:'稽查意见书文号已经存在，不允许重复录入'
                     }
                }
            },
            "list[1].checkFileDesc.checkedunit": {
                validators: {
                    notEmpty: {
                        message: '请输入被稽查单位'
                    },
                    stringLength: {
                        min: 1,
                        max: 50,
                        message: '1-50个字符'
                    }
                }
            }
	   }
	});
	
	$('#saveProvinceJichaForm').data('formValidation').validateField("list[0].files.filecode");
	 $('#saveProvinceJichaForm').data('formValidation').validateField("list[1].files.filecode");
	
	$("#showUpload1").click(function(){
    	$("#uploadTr1").attr("style","");
 	});
	 $("#showUpload2").click(function(){
	    	$("#uploadTr2").attr("style","");
	 });
	 
	 $("#showJichatwo").click(function(){
	    	$("#divShow").toggle();
	    	var text=$("#showJichatwo").text() ;
	    	if(text=="添加稽查案卷二"){
	    		$("#divShow input").prop("disabled",false);
	    		$("#divShow textarea").prop("disabled",false);
	    		$("#showJichatwo").text("取消添加") ;
	    	}else{
	    		$("#divShow input").prop("disabled",true);
	    		$("#divShow textarea").prop("disabled",true);
	    		$("#showJichatwo").text("添加稽查案卷二") ;
	    	}
	 });
});
</script>
</head>
<body>
<div class="center_weizhi">当前位置：信息采集 - 参选先进集体信息- 稽查案卷上报填写</div>
<div class="center">
<div class="center_list">
		<form id="saveProvinceJichaForm" method="post" class="form-horizontal">
		<!-- 案卷一-->
		<input type="hidden" name="list[0].checkFileDesc.id" value="${fd1.checkFileDesc.id}">
		<input type="hidden" name="list[0].checkFileDesc.areatype" value="${areaUser.arealevel}">
		<input type="hidden" name="list[0].checkFileDesc.areacode" value="${areaUser.areaCode}">
			<input type="hidden" name="list[0].checkFileDesc.jichatype" value="1">
		<!-- 案卷二-->
		<!-- 文件信息隐藏域 -->
		<input type="hidden" name="list[0].files.areacode" value="${areaUser.areaCode}">
		<input type="hidden" name="list[0].files.id" value="${fd1.files.id}" >
		<input type="hidden" name="list[0].files.filetype" value="5">
		<input type="hidden" name="list[0].files.filename" id="filename" value="${fd1.files.filename}">
		<input type="hidden" name="list[0].files.fileurl" id="fileurl" value="${fd1.files.fileurl}">
		<input type="hidden" name="list[0].files.belongareacode" value="${areaUser.areaCode}">
		
        <table class="table_input">
         <tbody>
           <tr>
             <td width="200">&nbsp;</td>
             <td><span style="color:#295A5F; font-size:18px; float:left; font-weight:bold;">稽查案卷一</span></td>
           </tr>
           <tr>
             <td>稽查单位</td>
             <td>
             <div class="form-group">
                 <div class="col-sm-12">
             <input type="text" class="form-control input-sm" <c:if test="${areaUser.sysStatus!='1' }">disabled="disabled"</c:if>  name="list[0].checkFileDesc.checkunit" placeholder="请输入稽查单位" value="${fd1.checkFileDesc.checkunit}">
             </div>
		       </div> 
             </td>
           </tr>
           <tr>
             <td>稽查时间</td>
             <td>
             <div class="form-group">
                 <div class="col-sm-12">
             <input type="text" class="form-control input-sm" <c:if test="${areaUser.sysStatus!='1' }">disabled="disabled"</c:if> readonly="readonly" name="list[0].checkFileDesc.checkdate" id="datepicker" value="<fmt:formatDate value="${fd1.checkFileDesc.checkdate}" pattern="yyyy-MM-dd"/>" placeholder="请输入稽查时间">
             </div>
		       </div> 
             </td>
           </tr>
           <tr>
             <td>稽查内容</td>
             <td>
             <div class="form-group">
                 <div class="col-sm-12">
                 <textarea rows="5" class="form-control" <c:if test="${areaUser.sysStatus!='1' }">disabled="disabled"</c:if>  name="list[0].checkFileDesc.checkcontent" placeholder="请输入稽查内容">${fd1.checkFileDesc.checkcontent}</textarea>
             </div>
		       </div> 
             </td>
           </tr>
            <tr>
             <td>被稽查单位</td>
             <td>
             <div class="form-group">
                 <div class="col-sm-12">
             <input type="text" class="form-control input-sm" <c:if test="${areaUser.sysStatus!='1' }">disabled="disabled"</c:if> name="list[0].checkFileDesc.checkedunit" placeholder="请输入被稽查单位" value="${fd1.checkFileDesc.checkedunit}">
             </div>
		       </div> 
             </td>
           </tr>
           <tr>
             <td>稽查意见书文号</td>
             <td>
             <div class="form-group">
                 <div class="col-sm-12">
                              <input type="text" <c:if test="${areaUser.sysStatus!='1' }">disabled="disabled"</c:if> class="form-control input-sm" name="list[0].files.filecode" placeholder="请输入稽查意见书文号" value="${fd1.files.filecode}">
             </div>
		       </div> 
             </td>
           </tr>
           <tr>
             <td>稽查案卷文本</td>
             <td style="text-align:left;">
             		<span id="wsc" style="font-size: 16px;">
             		<c:choose>
		                 		<c:when test="${fd1.checkFileDesc.id!=''&&fd1.checkFileDesc.id!=null}">
		                 			${fd1.files.filename} 
		                 			<c:if test="${areaUser.sysStatus=='1' }">
		                 				<button type="button" id="showUpload1" class="btn btn-primary btn-xs">重新上传</button>
		                 			</c:if>
		                 		</c:when>
		                 		<c:otherwise>
		                 			<span style="font-size: 14px;">未上传</span>
		                 		</c:otherwise>
		            </c:choose>
		            </span> 
 		    </td>
           </tr>
          <tr id="uploadTr1" <c:if test="${fd1.checkFileDesc.id!=''&&fd1.checkFileDesc.id!=null}">style='display:none'</c:if>>
               <td>上传附件</td>
               <td style="text-align:left;">
	               		<input id="file" type="file" name="file" class="file-loading" > 
	               		<span style="color: red;">仅允许上传PDF格式文件，大小不超过50M</span>
               </td>
           </tr>
            </tbody>
      	 </table>
      	  <input type="hidden" name="isdel" id="isdel">
      	   <input type="hidden" name="id" value="${fd2.checkFileDesc.id}">
            <div id="divShow"  <c:if test="${fd2.checkFileDesc.id==null}">style='display:none'</c:if>>
            <input type="hidden" name="list[1].checkFileDesc.id" <c:if test="${fd2.checkFileDesc.id==null}">disabled='disabled'</c:if>  value="${fd2.checkFileDesc.id}">
		<input type="hidden" name="list[1].checkFileDesc.areatype" <c:if test="${fd2.checkFileDesc.id==null}">disabled='disabled'</c:if>  value="${areaUser.arealevel}">
		<input type="hidden" name="list[1].checkFileDesc.areacode" <c:if test="${fd2.checkFileDesc.id==null}">disabled='disabled'</c:if>  value="${areaUser.areaCode}">
		<input type="hidden" name="list[1].checkFileDesc.jichatype" <c:if test="${fd2.checkFileDesc.id==null}">disabled='disabled'</c:if>  value="2">
		
		<input type="hidden" name="list[1].files.areacode" <c:if test="${fd2.checkFileDesc.id==null}">disabled='disabled'</c:if>  value="${areaUser.areaCode}">
		<input type="hidden" name="list[1].files.id" <c:if test="${fd2.checkFileDesc.id==null}">disabled='disabled'</c:if>  value="${fd2.files.id}" >
		<input type="hidden" name="list[1].files.filetype" <c:if test="${fd2.checkFileDesc.id==null}">disabled='disabled'</c:if>  value="5">
		<input type="hidden" name="list[1].files.filename" id="filename2" <c:if test="${fd2.checkFileDesc.id==null}">disabled='disabled'</c:if>  value="${fd2.files.filename}">
		<input type="hidden" name="list[1].files.fileurl" id="fileurl2" <c:if test="${fd2.checkFileDesc.id==null}">disabled='disabled'</c:if>  value="${fd2.files.fileurl}">
		<input type="hidden" name="list[1].files.belongareacode" <c:if test="${fd2.checkFileDesc.id==null}">disabled='disabled'</c:if>  value="${areaUser.areaCode}">
	       
	       <table class="table_input">
	       <tbody>
           <tr>
             <td width="200">&nbsp;</td>
             <td><span style="color:#295A5F; font-size:18px; float:left; font-weight:bold;">稽查案卷二</span></td>
           </tr>
           <tr>
             <td>稽查单位</td>
             <td>
             <div class="form-group">
                 <div class="col-sm-12">
             <input type="text" <c:if test="${areaUser.sysStatus!='1' }">disabled="disabled"</c:if> class="form-control input-sm"  <c:if test="${fd2.checkFileDesc.id==null}">disabled='disabled'</c:if>  name="list[1].checkFileDesc.checkunit" placeholder="请输入稽查单位" value="${fd2.checkFileDesc.checkunit}">
             </div>
		       </div> 
             </td>
           </tr>
           <tr>
             <td>稽查时间</td>
             <td>
             <div class="form-group">
                 <div class="col-sm-12">
             <input type="text" <c:if test="${areaUser.sysStatus!='1' }">disabled="disabled"</c:if> class="form-control input-sm" <c:if test="${fd2.checkFileDesc.id==null}">disabled='disabled'</c:if>  readonly="readonly" name="list[1].checkFileDesc.checkdate" id="datepicker2" value="<fmt:formatDate value="${fd2.checkFileDesc.checkdate}" pattern="yyyy-MM-dd"/>" placeholder="请输入稽查时间">
             </div>
		       </div> 
             </td>
           </tr>
           <tr>
             <td>稽查内容</td>
             <td>
             <div class="form-group">
                 <div class="col-sm-12">
                <textarea rows="5" <c:if test="${areaUser.sysStatus!='1' }">disabled="disabled"</c:if> class="form-control" <c:if test="${fd2.checkFileDesc.id==null}">disabled='disabled'</c:if>  name="list[1].checkFileDesc.checkcontent" placeholder="请输入稽查内容">${fd2.checkFileDesc.checkcontent}</textarea>
             </div>
		       </div> 
             </td>
           </tr>
            <tr>
             <td>被稽查单位</td>
             <td>
             <div class="form-group">
                 <div class="col-sm-12">
             <input type="text" <c:if test="${areaUser.sysStatus!='1' }">disabled="disabled"</c:if> class="form-control input-sm" <c:if test="${fd2.checkFileDesc.id==null}">disabled='disabled'</c:if>  name="list[1].checkFileDesc.checkedunit" placeholder="请输入被稽查单位" value="${fd2.checkFileDesc.checkedunit}">
             </div>
		       </div> 
             </td>
           </tr>
           <tr>
             <td>稽查意见书文号</td>
             <td>
             <div class="form-group">
                 <div class="col-sm-12">
                              <input type="text" <c:if test="${areaUser.sysStatus!='1' }">disabled="disabled"</c:if> class="form-control input-sm" <c:if test="${fd2.checkFileDesc.id==null}">disabled='disabled'</c:if>  name="list[1].files.filecode" placeholder="请输入稽查意见书文号" value="${fd2.files.filecode}">
             </div>
		       </div> 
             </td>
           </tr>
           <tr>
             <td>稽查案卷文本</td>
             <td style="text-align:left;">
             		<span id="wsc2" style="font-size: 16px;">
             		<c:choose>
		                 		<c:when test="${fd2.checkFileDesc.id!=''&&fd2.checkFileDesc.id!=null}">
		                 			${fd2.files.filename}    
		                 			<c:if test="${areaUser.sysStatus=='1' }">
		                 				<button type="button" id="showUpload2" class="btn btn-primary btn-xs">重新上传</button>
		                 			</c:if>
		                 		</c:when>
		                 		<c:otherwise>
		                 			<span style="font-size: 14px;">未上传</span>
		                 		</c:otherwise>
		            </c:choose>
		            </span> 
 		    </td>
           </tr>
           <tr id="uploadTr2" <c:if test="${fd2.checkFileDesc.id!=''&&fd2.checkFileDesc.id!=null}">style='display:none'</c:if>>
               <td>上传附件</td>
               <td style="text-align:left;">
	               		<input id="file2" type="file" name="file2" class="file-loading" > 
	               		<span style="color: red;">仅允许上传PDF格式文件，大小不超过50M</span>
               </td>
           </tr>
            </tbody>
	       </table>
	       </div>
           <table class="table_input">
           <tbody>
	           <tr>
	             <td style="text-align:center;">
	             </td>
	             <td style="text-align:center;">
	            	 <c:if test="${areaUser.sysStatus=='1'&& areaUser.reportState=='0'}">
	             		<a href="#"><button type="button" class="btn btn-primary" id="saveProvinceJichaBtn" style="font-size:16px;width:150px; margin-top:5px;">信息保存</button></a>
	             
	             		<a href="#"><button type="button" class="btn btn-primary" id="showJichatwo" style="font-size:16px;width:150px; margin-top:5px;"></button></a>
	             	 </c:if>
	             </td>
	           </tr>
	         </tbody>
	       </table>
       </form>
    </div>
</div>
</body>
<script>
  $(function() {
    $( "#datepicker" ).datepicker({
    	monthNames: [ "1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月" ],
		dayNamesMin: [ "日", "一", "二", "三", "四", "五", "六" ],
        dateFormat: 'yy-mm-dd',
        maxDate: 0,
        changeYear: true,
        onSelect: function(selectedDate) {//选择日期后执行的操作  
        	$('#saveProvinceJichaForm').data('formValidation').revalidateField("list[0].checkFileDesc.checkdate");
        }  
    });
    $( "#datepicker2" ).datepicker({
    	monthNames: [ "1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月" ],
		dayNamesMin: [ "日", "一", "二", "三", "四", "五", "六" ],
        dateFormat: 'yy-mm-dd',
        maxDate: 0,
        changeYear: true,
        onSelect: function(selectedDate) {//选择日期后执行的操作  
        	$('#saveProvinceJichaForm').data('formValidation').revalidateField("list[1].checkFileDesc.checkdate");
        }  
    });
  });
</script>
</html>
