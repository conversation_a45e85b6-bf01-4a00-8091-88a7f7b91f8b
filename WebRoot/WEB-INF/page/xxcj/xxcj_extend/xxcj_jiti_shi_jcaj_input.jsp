<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
<script type="text/javascript">
function butChange(index){

	var butVal = $("#reButton"+index).attr("value");
	var butStyle = $("#reButton"+index).attr("style");
	if(butStyle!=""&&typeof(butStyle)!="undefined"){
		$("#reButton"+index).attr("style","");
    	$("#uploadTr"+index).attr("style","display:none");
    	$("#wsc"+index).attr("style","");
	}else{
		if(butVal=="重新上传"){
			$("#reButton"+index).attr("value","返回");
			$("#uploadTr"+index).attr("style","");
	    	$("#wsc"+index).attr("style","display:none");
		}else if(butVal=="返回"){
			$("#reButton"+index).attr("value","重新上传");
			$("#uploadTr"+index).attr("style","display:none");
	    	$("#wsc"+index).attr("style","");
		}
	}
}
$(document).ready(function(){
	//稽查案卷文本 上传附件
	 $("#JCAJProfile").fileinput({
       uploadUrl: WEBPATH+'/pdffileupload.do', // you must set a valid URL here else you will get an error
       allowedFileExtensions : ['pdf'],
       language:'zh',
       browseClass:'btn btn-danger',//按钮样式
       //overwriteInitial: true,
       minFileCount: 1,
       maxFileCount: 1,
       minFileSize:1,
       maxFileSize:53500,
       enctype: 'multipart/form-data',        
       dropZoneTitle:"可拖拽文件到此处...",
       initialPreviewShowDelete: false,
       msgFilesTooMany:'选择上传的文件数量({n}) 超过允许的最大数值{m}！',
       msgZoomModalHeading:'文件预览',
       msgInvalidFileExtension:'不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
       msgNoFilesSelected:'请选择文件',
       msgValidationError:'文件类型不正确或文件过大',
       initialPreviewFileType:'pdf',
       browseLabel:"选择文件",
       removeLabel:'删除',
       removeTitle:'删除文件',
       uploadLabel:'上传',
       uploadTitle:'上传文件',
       cancelLabel: '取消',
       cancelTitle: '取消上传',
       showPreview:false,
       autoReplace:true,
       slugCallback: function(filename) {
           return filename.replace('(', '_').replace(']', '_');
       }
	}).on('filepreupload', function(event, data, previewId, index) {
	   $("#savejcajBtn").prop('disabled', true);
	}).on('fileuploaded', function(event, data, previewId, index) {//文件上传完成执行操作
		
		$("#check_filetexturlPro").val(data.response.url);
	  	$("#check_filetextPro").val(data.response.fileRealName);
	  	$("#wsc").text(data.response.fileRealName);
	  	butChange('');
	  	$("#savejcajBtn").prop('disabled',false);
	  	
	})/* .on('fileclear', function(event) {  //文件上传删除文件执行的操作
		
   	$("#fileurl").val("");
	  	$("#filename").val("");
	  	$("#wsc").text("未上传");
   }) */
   $("#reButton").click(function(){
	   butChange('');
   });
	 
	//表单验证
	//表单校验
	$("#jcajForm").formValidation({
	    message: 'This value is not valid',
	    icon:{
	            valid: 'glyphicon glyphicon-ok',
	            invalid: 'glyphicon glyphicon-remove',
	            validating: 'glyphicon glyphicon-refresh'
	    },
	    autoFocus: true,
	    fields: {
	    	"checkunitPro": {
	    		 validators: {
	                    notEmpty: {
	                        message: '请输入稽查单位'
	                    },
	                    stringLength: {
	                        min: 1,
	                        max: 30,
	                        message: '1-30个字符'
	                    },
		                  regexp:{
		                         message:'不允许输入非法的字符<、>',
		                         regexp:/^(?!.*(\<|\>))/
		                    }
	                }
	            },
	    	"checkdatePro": {
	    		 validators: {
	                    notEmpty: {
	                        message: '请输入稽查时间'
	                    },
	                    stringLength: {
	                        min: 1,
	                        max: 20,
	                        message: '1-20个字符'
	                    }
	                }
	            },
	    	"checkcontentPro": {
	    		 validators: {
	                    notEmpty: {
	                        message: '请输入稽查内容'
	                    },
	                    stringLength: {
	                        min: 1,
	                        max: 200,
	                        message: '1-200个字符'
	                    },
		                  regexp:{
		                         message:'不允许输入非法的字符<、>',
		                         regexp:/^(?!.*(\<|\>))/
		                    }
	                }
	            },
	        "checkedunitPro": {
	        	  validators: {
	                    notEmpty: {
	                        message: '请输入被稽查单位'
	                    },
	                    stringLength: {
	                        min: 1,
	                        max: 50,
	                        message: '1-50个字符'
	                    },
		                  regexp:{
		                         message:'不允许输入非法的字符<、>',
		                         regexp:/^(?!.*(\<|\>))/
		                    }
	                }
	            },
	        "filecodePro": {
	        	 validators: {
	                    notEmpty: {
	                        message: '请输入稽查意见书文号'
	                    },
	                    stringLength: {
	                        min: 1,
	                        max: 50,
	                        message: '1-50个字符'
	                    },
		                regexp:{
		                    message:'不允许输入非法的字符<、>',
		                    regexp:/^(?!.*(\<|\>))/
		                },
	                    remote: {
	                    	   dataType:'json',
	                    	   type:'POST',
	                    	   delay:500,
	                           url: '${webpath}/xxcj/checkJiChaAnJuan2018.do',
	                           data: function(validator, $field, value) {
	                               return {
	                              	 filecode:validator.getFieldElements('filecodePro').val(),
	                                 id:'${xxcjProFilesList.id}'
	                               };
	                           },
	                    	   message:'稽查意见书文号已经存在，不允许重复录入'
	                       }
	                }
	            },
	        
	    }
	});
	if($("#jcajId").val()!=""){
		$("#jcajForm").data('formValidation').validate();
	}
});

//保存数据方法
$('#savejcajBtn').click(function(){
	if($("#jcajId").val()==""){
		$("#jcajForm").data('formValidation').validate();
	}
	var validate = $("#jcajForm").data('formValidation').isValid();
	var time=$("#datepicker").val();
	var id=$("#jcajId").val();
	if(id!=null && id!=""){
		url=WEBPATH+"/xxcj/xxcjybxzcfAddOrUpdate.do?id="+id	
	}else{
		url=WEBPATH+"/xxcj/xxcjybxzcfAddOrUpdate.do"
	}
	if(validate){
		var options = {
			       url: url,
			       type: 'post',
			       success:function(data){
			           if(data.result=="error"){
			        	   swal({title: "操作失败",text:data.message,type:"error",confirmButtonColor: "#d9534f"});
			               return false;
			           }else if(data.result=="success"){
			        	  swal({title: "操作成功",text:"",type:"success",confirmButtonColor: "#d9534f"});
			        	  business.addMainContentParserHtml('xxcj/xxcjjcajList.do',null);
			        	  return false;
			           }else{
			        	   swal({title: "系统异常",text:"请刷新菜单或重新登录！",type:"warning",confirmButtonColor: "#d9534f"});
			           }
			     	},
					error:function(){
						swal({title: "服务异常,保存失败!",text:"",type:"error",confirmButtonColor: "#d9534f"});
					}
		};
			var filename=$("#check_filetextPro").val();
			var fileurl=$("#check_filetexturlPro").val();
			if(fileurl!=""&&filename!=""){
		$("#jcajForm").ajaxSubmit(options);
		}else{
			swal({title: "请上传稽查案卷文本附件!",text:"",type:"error",confirmButtonColor: "#d9534f"});
		}
	}
});	

</script>
</head>
<body>
<div class="center_weizhi"><span style="float: left;">当前位置：信息采集 - 市级评比信息 - 稽查案卷上报</span><span style="float: right;padding-right: 10px;"><i class="fa fa-chevron-left"></i> <a  href="javascript:void(0);" onclick="macroMgr.onLevelTwoMenuClick(null, 'xxcj/xxcjjcajList.do')">返回</a></span></div>
<div class="frame_center">
<div class="center">
<div class="frame_center">
<div class="center_list">
     <form id="jcajForm" method="post" class="form-horizontal">
     <!-- 文件信息隐藏域 -->
     <input type="hidden" class="form-control" id="jcajId" name="jcajId" value="${xxcjProFilesList.id }">
     <input type="hidden" id="areacode" name="" value="${areaUser.areaCode}">
     <input type="hidden" id="filetypePro" name="filetypePro" value="5">
        <table align="center" class="table_input">
         <tbody>
           <tr>
                <td colspan="2">
                <div class="shangbao_panel">
                    <div class="shangbao_titlt">稽查案卷</div>
                    <table style="width:100%;" class="table_input">
                        <tr>
                             <td style="width:224px;">稽查单位<i class="ace-icon fa fa-asterisk" style="color:red"></i></td>
                             <td width="550px"><div class="form-group"> <div class="col-sm-12"><input type="text" class="form-control" id="checkunitPro_Id" name="checkunitPro" placeholder="请输入稽查单位" value="${xxcjProFilesList.checkunitPro }"></div> </div></td>
                           </tr>
                           <tr>
                           </tr>
                            <tr>
				             <td>稽查时间<i class="ace-icon fa fa-asterisk" style="color:red"></i></td>
				             <td>
				             <div class="form-group">
				                 <div class="col-sm-12">
				             		<input type="text" class="form-control"  readonly value="<fmt:formatDate value="${xxcjProFilesList.checkdatePro}" pattern="yyyy-MM-dd"/>" name="checkdatePro" id="datepicker"  placeholder="请输入稽查时间">
				             		
				             </div>
						       </div> 
				             </td>
				           </tr>
                           <tr>
                             <td>稽查内容<i class="ace-icon fa fa-asterisk" style="color:red"></i></td>
                             <td><div class="form-group"> <div class="col-sm-12"><textarea rows="6" class="form-control" id="checkcontentPro_Id" name="checkcontentPro" placeholder="请输入稽查内容">${xxcjProFilesList.checkcontentPro }</textarea></div> </div></td>
                           </tr>
                           <tr>
                             <td>被稽查单位<i class="ace-icon fa fa-asterisk" style="color:red"></i></td>
                             <td><div class="form-group"> <div class="col-sm-12"><input type="text" class="form-control" id="checkedunitPro_Id" name="checkedunitPro" placeholder="请输入被稽查单位" value="${xxcjProFilesList.checkedunitPro }"></div> </div></td>
                           </tr>
                            <tr>
                             <td>稽查意见书文号<i class="ace-icon fa fa-asterisk" style="color:red"></i></td>
                             <td><div class="form-group"> <div class="col-sm-12"><input type="text" class="form-control" id="filecodePro_Id" name="filecodePro" placeholder="请输入稽查意见书文号" value="${xxcjProFilesList.filecodePro }"></div> </div></td>
                           </tr>
                           <tr>
                             <td>稽查案卷文本<i class="ace-icon fa fa-asterisk" style="color:red"></i></td>
                                 <td style="text-align:left;">
                             <span id="wsc" style="font-size:16px;">
			       			  
			       			   <c:choose>
						        	<c:when test="${xxcjProFilesList.checkfiletextPro!='' && xxcjProFilesList.checkfiletextPro!=null }">
						        		<div style="float:left;margin-right:8px;padding-top:4px;">
						        			${xxcjProFilesList.checkfiletextPro}
						        		</div>
						        	</c:when>
						        </c:choose>
						        </span>
						        <span id="uploadTr" <c:if test="${xxcjProFilesList.checkfiletextPro!='' && xxcjProFilesList.checkfiletextPro!=null }">style='display:none'</c:if>>
							        <input id="JCAJProfile" type="file" name="JCAJProfile" class="file-loading">
							        <span style="color: red;">可上传PDF格式的附件，大小不超过50M； </span>
							        <input type="hidden" class="form-control" name="checkfiletextPro" id="check_filetextPro" value="${xxcjProFilesList.checkfiletextPro}">
									<input type="hidden" class="form-control" name="checkfiletexturlPro" id="check_filetexturlPro" value="${xxcjProFilesList.checkfiletexturlPro}">
	                           </span>
						        </td>
						        <td>
						        	<input id="reButton" <c:if test="${xxcjProFilesList.checkfiletextPro=='' || xxcjProFilesList.checkfiletextPro==null }">style='display:none'</c:if> class="btn btn-danger btn-xs" type="button" value="重新上传"/>
						        </td>
						      </tr>
						      <%-- <tr id="uploadTr" <c:if test="${xxcjProFilesList.checkfiletextPro!='' && xxcjProFilesList.checkfiletextPro!=null }">style='display:none'</c:if>>
						        <td>&nbsp;</td>
						        <td style="text-align:left;">
						        <input id="JCAJProfile" type="file" name="JCAJProfile" class="file-loading">
						        <span style="color: red;">可上传PDF格式的附件，大小不超过50M； </span>
						        <input type="hidden" class="form-control" name="checkfiletextPro" id="check_filetextPro" value="${xxcjProFilesList.checkfiletextPro}">
								<input type="hidden" class="form-control" name="checkfiletexturlPro" id="check_filetexturlPro" value="${xxcjProFilesList.checkfiletexturlPro}">
                             </td>
                           </tr> --%>
                    </table>
                </div>
                </td>
           </tr>
           
           <tr>
             <td align="center" width="300">&nbsp;</td>
             <td style="text-align:left;"><a href="#"><button id="savejcajBtn" type="button" class="btn btn-danger" name="signup" value="Sign up" style="font-size:16px;width:150px; margin-top:5px;">信息保存</button></a></td>
           </tr>
         </tbody>
       </table>
       </form>
       </div>
       </div>
    </div>
</div>
<script language="JavaScript">

$(document).ready(function() {
	$(".topnav").accordion({
		accordion:false,
		speed: 500,
		closedSign: '[+]',
		openedSign: '[-]'
	});
});
$(function() {
    $( "#datepicker" ).datepicker({
    	monthNames: [ "1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月" ],
		dayNamesMin: [ "日", "一", "二", "三", "四", "五", "六" ],
        dateFormat: 'yy-mm-dd',
        maxDate: 0,
        changeYear: true,
        onSelect: function(selectedDate) {//选择日期后执行的操作  
        	$('#jcajForm').data('formValidation').revalidateField("checkdatePro");
        }  
    });
  });
</script>
</body>
</html>
