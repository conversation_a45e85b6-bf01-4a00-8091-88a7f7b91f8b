<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<script type="text/javascript">
/*$(document).ready(function(){

	var objValue=eval("${ProPerson.handlcasecodesPro}");
	var str=[];
	$.each(objValue,function(index,obj){
		str.push(obj.text);
	});
	$("#handlcasecodesPro").text(str.toString());
});*/
function downloadFile(url,fileName){
	if(url!=null&&url!=""){
		window.location.href="${webpath}/filedownload.do?url="+url+"&fileName="+fileName;
	}else{
		swal({title: "提示",text: "您下载的附件不存在！",type:"info"});
	}
}
</script>
<html>
<head>
<body>
<c:if test="${areaUser.arealevel==1}">
<div class="center_weizhi"><span style="float: left;">当前位置：推荐报送 - 省级评比信息 - 省级表现突出候选个人</span><span style="float: right;padding-right: 10px;"><i class="fa fa-chevron-left"></i> <a  href="javascript:void(0);" onclick="macroMgr.onLevelTwoMenuClick(null, 'xxcj/cityPersonPro.do')">返回</a></span></div>
</c:if>
<c:if test="${areaUser.arealevel==2}">
<div class="center_weizhi"><span style="float: left;">当前位置：推荐报送 - 市级评比信息 - 市级表现突出候选个人</span><span style="float: right;padding-right: 10px;"><i class="fa fa-chevron-left"></i> <a  href="javascript:void(0);" onclick="macroMgr.onLevelTwoMenuClick(null, 'xxcj/cityPersonPro.do')">返回</a></span></div>
</c:if>
<c:if test="${areaUser.arealevel==3}">
<div class="center_weizhi"><span style="float: left;">当前位置：推荐报送 - 县级评比信息 - 县级表现突出候选个人</span><span style="float: right;padding-right: 10px;"><i class="fa fa-chevron-left"></i> <a  href="javascript:void(0);" onclick="macroMgr.onLevelTwoMenuClick(null, 'xxcj/cityPersonPro.do')">返回</a></span></div>
</c:if>
<div class="center">
<div class="center_list">
  <table align="center" class="table_input">
    <tbody>
      <tr>
        <td style="font-weight:bold">所属行政区：</td>
        <td style="text-align:left;">${ProPerson.provincePro}${ProPerson.cityPro}${ProPerson.countryPro}</td>
      </tr>
      <tr>
        <td width="400" style="font-weight:bold">案卷类型</td>
        <td style="text-align:left;">
        <c:if test="${xxcjProFiles1.filetypePro==0}">一般行政处罚</c:if>
         <c:if test="${xxcjProFiles1.filetypePro==1}">按日计罚</c:if>
          <c:if test="${xxcjProFiles1.filetypePro==6}">查封扣押</c:if>
           <c:if test="${xxcjProFiles1.filetypePro==7}">限产停产</c:if>
            <c:if test="${xxcjProFiles1.filetypePro==2}">移送行政拘留</c:if>
             <c:if test="${xxcjProFiles1.filetypePro==3}">涉嫌犯罪移送</c:if>
        </td>
      </tr>
      <tr>
        <td style="font-weight:bold">案卷名称：</td>
        <td style="text-align:left;">${xxcjProFiles1.filecodePro}</td>
      </tr>
       <%--<tr>
        <td style="font-weight:bold">案件调查情况材料简版说明：</td>
        <td style="text-align:left;">${ProPersonAndFiles[0].filesimpledescPro}</td>
      </tr>
      <tr>
        <td style="font-weight:bold">案件调查情况材料详版说明：</td>
        <td style="text-align:left;">${ProPersonAndFiles[0].filedetiaildescPro}</td>
      </tr>--%>
      
      <%--<tr>
        <td width="200" style="font-weight:bold">案卷类型：</td>
        <td style="text-align:left;">
        <c:if test="${xxcjProFiles2.filetypePro==0}">一般行政处罚</c:if>
         <c:if test="${xxcjProFiles2.filetypePro==1}">按日计罚</c:if>
          <c:if test="${xxcjProFiles2.filetypePro==6}">查封扣押</c:if>
           <c:if test="${xxcjProFiles2.filetypePro==7}">限产停产</c:if>
            <c:if test="${xxcjProFiles2.filetypePro==2}">移送行政拘留</c:if>
             <c:if test="${xxcjProFiles2.filetypePro==3}">涉嫌犯罪移送</c:if>
        </td>
      </tr>
      <tr>
        <td style="font-weight:bold">案卷名称：</td>
        <td style="text-align:left;">${xxcjProFiles2.filecodePro}</td>
      </tr>
       <tr>
        <td style="font-weight:bold">案件调查情况材料简版说明：</td>
        <td style="text-align:left;">${ProPersonAndFiles[1].filesimpledescPro}</td>
      </tr>
      <tr>
        <td style="font-weight:bold">案件调查情况材料详版说明：</td>
        <td style="text-align:left;">${ProPersonAndFiles[1].filedetiaildescPro}</td>
      </tr>--%>
      <tr>
        <td style="font-weight:bold">姓名：</td>
        <td style="text-align:left;">${ProPerson.namePro}</td>
      </tr>
      <tr>
        <td style="font-weight:bold">性别：</td>
        <td style="text-align:left;">${ProPerson.sexPro}</td>
      </tr>
      <tr>
        <td style="font-weight:bold">职务：</td>
        <td style="text-align:left;">${ProPerson.jobPro}</td>
      </tr>
      
      <tr>
        <td style="font-weight:bold">所在单位名称：</td>
        <td style="text-align:left;">${ProPerson.unitnamePro}</td>
      </tr>
      <tr>
        <td style="font-weight:bold">环保工作年限：</td>
        <td style="text-align:left;">${ProPerson.workYearPro}</td>
      </tr>
      
      <tr>
        <td style="font-weight:bold">身份证号码：</td>
        <td style="text-align:left;">${ProPerson.cardidPro}</td>
      </tr>
      
      <tr>
        <td style="font-weight:bold">学历：</td>
        <td style="text-align:left;">${ProPerson.educationPro}</td>
      </tr>
      <tr>
        <td style="font-weight:bold">编制性质：</td>
        <td style="text-align:left;">${ProPerson.orgpropPro}</td>
      </tr>
      
      <tr>
        <td style="font-weight:bold">联系电话：</td>
        <td style="text-align:left;">${ProPerson.phonePro}</td>
      </tr>
       <%--<tr>
        <td style="font-weight:bold">参与调查处理案件案卷号：</td>
        <td style="text-align:left;"><span id="handlcasecodesPro"></span></td>
      </tr>
       <tr>
         <td style="font-weight:bold">参与调查处理案件数量：</td>
         <td style="text-align:left;">${ProPerson.handlcasenumPro}</td>
       </tr>--%>
      <tr>
        <td style="font-weight:bold">个人事迹材料：</td>
        <td style="text-align:left;">
        <a style="color: blue;cursor:pointer;text-decoration:none;"    onclick="downloadFile('${ProPerson.personalmaterialurlPro}',' ${ProPerson.personalmaterialPro}')"> ${ProPerson.personalmaterialPro}</a> 
        </td>
      </tr>
      <tr>
        <td style="font-weight:bold">个人照片上传：</td>
        <td style="text-align:left;">
        <a style="color: blue;cursor:pointer;text-decoration:none;"    onclick="downloadFile('${ProPerson.personalphotouploadurlPro}',' ${ProPerson.personalphotouploadPro}')"> ${ProPerson.personalphotouploadPro}</a> 
        </td>
      </tr>
      <tr>
        <td colspan="2"><span style="font-weight: bold;float: left;font-size: 20px;margin-left: 145px;">典型案例采纳情况（日常监督执法工作）</span></td>
      </tr>
      <tr>
        <td style="font-weight:bold">被市级采纳作为典型案例公开数量/件：</td>
        <td style="text-align:left;">${ProPerson.cityAcceptCasePublicNum}</td>
      </tr>
      <tr>
        <td style="font-weight:bold">被省级采纳作为典型案例公开数量：</td>
        <td style="text-align:left;">${ProPerson.provinceAcceptCasePublicNum}</td>
      </tr>
      <%--<tr>
        <td style="font-weight:bold">个人廉洁执法相关证明材料：</td>
        <td style="text-align:left;">
        <a style="color: blue;cursor:pointer;text-decoration:none;"    onclick="downloadFile('${ProPerson.perhonestfileurlPro}','${ProPerson.perhonestfilenamePro}')"> ${ProPerson.perhonestfilenamePro}</a>
      </tr>--%>
      <tr>
        <td style="font-weight:bold">日常监督执法工作：</td>
        <td style="text-align:left;">
        <a style="color: blue;cursor:pointer;text-decoration:none;"    onclick="downloadFile('${ProPerson.dailySupervisionWorkUrlPro}','${ProPerson.dailySupervisionWorkPro}')"> ${ProPerson.dailySupervisionWorkPro}</a>
      </tr>
    </tbody>
  </table>
</div>
</div>
</body>
</html>
