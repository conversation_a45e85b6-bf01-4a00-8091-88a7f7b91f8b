<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<html>
<head>


</head>
<body>
<div class="center_weizhi"><span style="float: left;">当前位置：信息采集 - 市级评比信息 - 稽查案卷上报</span><span style="float: right;padding-right: 10px;"><i class="fa fa-chevron-left"></i> <a  href="javascript:void(0);" onclick="macroMgr.onLevelTwoMenuClick(null, 'xxcj/xxcjjcajList.do')">返回</a></span></div>
<div class="center">
<div class="frame_center">
<div class="center_list">
    
        <table align="center" class="table_input">
         <tbody>
           <tr>
           	<td colspan="2">
           	<div class="shangbao_panel">
                <div class="shangbao_titlt">稽查案卷</div>
                <table style="width:100%;" class="table_input">
                    <tr>
                     <td>稽查单位</td>
                     <td style="text-align: left;">${xxcjProFilesList.checkunitPro }</td>
                   </tr>
                   <tr>
                     <td>稽查时间</td>
                     <td style="text-align: left;"><fmt:formatDate value="${xxcjProFilesList.checkdatePro}" pattern="yyyy-MM-dd"/></td>
                   </tr>
                   <tr>
                     <td>稽查内容</td>
                     <td style="text-align: left;">${xxcjProFilesList.checkcontentPro }</td>
                   </tr>
                   <tr>
                     <td>被稽查单位</td>
                     <td style="text-align: left;">${xxcjProFilesList.checkedunitPro }</td>
                   </tr>
                   <tr>
                     <td width="224">稽查意见书文号</td>
                     <td style="text-align: left;">${xxcjProFilesList.filecodePro }</td>
                   </tr>
                   <tr>
                     <td>稽查案卷文本</td>
                     <td style="text-align: left;">${xxcjProFilesList.checkfiletextPro }</td>
                   </tr>
                </table>
            </div>
            </td>
           </tr>           
         </tbody>
       </table>
    </div>
</div>
</div>
<script language="JavaScript">

$(document).ready(function() {
	$(".topnav").accordion({
		accordion:false,
		speed: 500,
		closedSign: '[+]',
		openedSign: '[-]'
	});
});
</script>
</body>
</html>
