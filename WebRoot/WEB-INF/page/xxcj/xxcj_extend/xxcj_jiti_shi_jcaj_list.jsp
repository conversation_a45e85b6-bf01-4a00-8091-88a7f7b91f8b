<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %> 
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<html>
<head>
<script type="text/javascript">
$(document).ready(function(){
	//快速搜索
	business.listenEnter("searchButt");
	$("#searchButt").click(function(){
		business.addMainContentParserHtml("xxcj/xxcjjcajList.do",$("#searchForm").serialize());
	});
//分页
	var curentPage = eval('${xxcjProFilesList.pageNum}');
	var totalPage = eval('${xxcjProFilesList.pages}');
	var areaType = eval('${areaType}');
	if(totalPage>0){
		var options = {
			bootstrapMajorVersion: 3,
		    currentPage: curentPage,
		    totalPages: totalPage,
		    numberOfPages: 5,
		    itemTexts: function (type, page, current) {
		            switch (type) {
		            case "first":
		                return "首页";
		            case "prev":
		                return "&laquo;";
		            case "next":
		                return "&raquo;";
		            case "last":
		                return "尾页";
		            case "page":
		                return page;
		            }
		    },
		    onPageClicked: function (event, originalEvent, type, page) {
            	business.addMainContentParserHtml(WEBPATH+'/xxcj/xxcjjcajList.do?pageNum='+page,$("#searchForm").serialize());//需要修改
            }
    	};
    	$('#pageCon').bootstrapPaginator(options);
   	}
//分页完毕	
});

//稽查案卷上报
$("#jcajsb").click(function(){
	if($("#Listlength").val()<1){
		business.addMainContentParserHtml('xxcj/xxcjjcajInput.do',null);
	}else{
		swal({title: "稽查案卷上报已经达到上限",text: "",type:"error",confirmButtonColor: "#d9534f"});
	}
});

//删除操作
function delObjectBtn(id){
		 swal({
			    title: "您确定执行删除操作吗？",
	              type: "warning",
	              showCancelButton: true,
	              closeOnConfirm: false,
	              confirmButtonText: "是的，我要删除",
	              confirmButtonColor: "#d9534f"
			}, function(isConfirm) {
					if (isConfirm) {
		$.ajax({
			type:"post",
			url:WEBPATH+"/xxcj/deleteFiles.do",
			data:{
				id:id,
			},
			 async:false,
			 dataType: "json",
			success:function(data){
				 if(data.result=="error"){
					  swal({title: "删除失败",text: "",type:"error",confirmButtonColor: "#d9534f"});
					  swal.close();
		               return false;
			       }else if(data.result=="success"){
			    	  swal({title: "删除成功",text: "",type:"success",confirmButtonColor: "#d9534f"});
			    	business.addMainContentParserHtml('xxcj/xxcjjcajList.do',null);
			       }
			},error:function(data){
				swal.close();
				business.addMainContentParserHtml('xxcj/xxcjjcajList.do',null);
			}
		});
		}else {
			swal({
				title : "已取消",
				text : "您取消了删除操作！",
				type : "info",
				confirmButtonColor: "#d9534f"
			})
		}
})
}



</script>
</head>
<body>
<div class="center_weizhi">当前位置：信息采集 - 市级评比信息 - 稽查案卷上报</div>
<div class="center">
<div class="frame_center">
	<div class="center_list">
    <div class="panel-group" id="accordion">
    <form  id= "searchForm" role="form"  >
          <h4 class="panel-title">          
            <div class="btn-group" style="margin-right:20px;float:left;">
            	<c:if test="${sysInitConfig.code == 1}">
	             	 <c:if test="${provinceReportUser.isDevInternalEva != 1 && provinceReportUser.electcityState != 1}">
		             	  <button id="jcajsb" type="button" class="btn btn-danger" style="font-size:16px;">稽查案卷上报</button>
	             	 </c:if>
	             	</c:if>
            </div>       
            
            <div style="width:260px;" class="btn-group">
               <form class="bs-example bs-example-form" role="form">
                  <div class="row">                     
                     <div class="col-lg-6">
                        <div class="input-group">
                           <input id="filecodePro" name="filecodePro" value="${filecodePro}" type="text" class="form-control" style="width:200px;">
                           <span class="input-group-btn">
                              <button id="searchButt" class="btn btn-danger" type="button">
                                 快速搜索
                              </button>
                           </span>
                        </div>
                     </div>
                  </div>
               </form>
            </div>  
            <div class="btn-group" style="margin-left:50px;color:#F00;">提示：每个账号可上报1个稽查案件 </div>            
          </h4>
          </form>
    </div>
    </br>
        <table class="table table-bordered table-hover table-condensed">
         <thead>
           <tr>
             <td width="50" height="30" bgcolor="#efefef">序号</td>
             <td bgcolor="#efefef">参选单位</td>
             <td bgcolor="#efefef">稽查单位</td>
             <td bgcolor="#efefef">稽查时间</td>
             <td bgcolor="#efefef">被稽查单位</td>
             <td bgcolor="#efefef">稽查意见书文号</td>
             <td bgcolor="#efefef">稽查案卷文本</td>
             <td width="140" bgcolor="#efefef">操作</td>
           </tr>
         </thead>
         <tbody>
         <c:forEach  var="xxcjProFilesList" items="${xxcjProFilesList.list}" varStatus="status">
         		 <tr>
	             <td height="30" align="center">${status.index+1}</td>
	             <td>${xxcjProFilesList.joinunitPro }</td>
	             <td>${xxcjProFilesList.checkunitPro }</td>
	             <td><fmt:formatDate value="${xxcjProFilesList.checkdatePro}" pattern="yyyy-MM-dd"/></td>
	             <td>${xxcjProFilesList.checkedunitPro }</td>
	             <td>${xxcjProFilesList.filecodePro }</td>
	             <td>${xxcjProFilesList.checkfiletextPro }</td>
	             <td><button onclick="macroMgr.onLevelTwoMenuClick(null, 'xxcj/jcajcheckFiles.do?id=${xxcjProFilesList.id}')" class="btn btn-warning btn-xs">查看</button> 
	             	 <c:if test="${sysInitConfig.code == 1}">
	             	 <c:if test="${provinceReportUser.isDevInternalEva != 1 && provinceReportUser.electcityState != 1}">
		             	 <button onclick="macroMgr.onLevelTwoMenuClick(null, 'xxcj/xxcjjcajInput.do?id=${xxcjProFilesList.id}')" class="btn btn-success btn-xs">编辑</button>
	             	 	 <button onclick="delObjectBtn(${xxcjProFilesList.id})" class="btn btn-danger btn-xs">删除</button>
	             	 </c:if>
	             	</c:if>
	             </td>
	           </tr>
          	</c:forEach>
          	<input type="hidden" id="Listlength" name="Listlength" value="${fn:length(xxcjProFilesList.list)}">
         </tbody>
       </table>
    </div>
     <!--列表翻页 开始-->
    <div class="page">
    	<span style="padding:12px; float:left; color:#0099cc;">共${xxcjProFilesList.total}条记录</span>
        <ul class="pagination" id="pageCon">
            
        </ul>
    </div>
    <!--列表翻页 结束-->
</div>
</div>
<script language="JavaScript">

$(document).ready(function() {
	$(".topnav").accordion({
		accordion:false,
		speed: 500,
		closedSign: '[+]',
		openedSign: '[-]'
	});
});

</script>
</body>
</html>
