<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %> 
<html>
<head>
<script type="text/javascript">
$(document).ready(function(){
	//分页
		var curentPage = eval('${proPersonalList.pageNum}');
		var totalPage = eval('${proPersonalList.pages}');
		var areaType = eval('${areaType}');
		if(totalPage>0){
			var options = {
				bootstrapMajorVersion: 3,
			    currentPage: curentPage,
			    totalPages: totalPage,
			    numberOfPages: 5,
			    itemTexts: function (type, page, current) {
			            switch (type) {
			            case "first":
			                return "首页";
			            case "prev":
			                return "&laquo;";
			            case "next":
			                return "&raquo;";
			            case "last":
			                return "尾页";
			            case "page":
			                return page;
			            }
			    },
			    onPageClicked: function (event, originalEvent, type, page) {
	            	business.addMainContentParserHtml(WEBPATH+'/xxcj/cityPersonPro.do?pageNum='+page);//需要修改
	            }
	    	};
	    	$('#pageCon').bootstrapPaginator(options);
	   	}
	//分页完毕	
	});

	//表现突出候选个人上限
	$("#shiGerenSB").click(function(){
		console.log($("#areaCode").val())

		if ($("#areaCode").val()=="12000000"){
			if($("#Listlength").val()<4){
				business.addMainContentParserHtml('xxcj/cityPersonInputPro.do',null);
			}else {
				if($("#arealevel").val()==1){
					swal({title: "省级表现突出候选个人已经达到上限",text: "",type:"error",confirmButtonColor: "#d9534f"});
				}else if($("#arealevel").val()==2){
					swal({title: "市级表现突出候选个人已经达到上限",text: "",type:"error",confirmButtonColor: "#d9534f"});
				}else if($("#arealevel").val()==3){
					swal({title: "县级表现突出候选个人已经达到上限",text: "",type:"error",confirmButtonColor: "#d9534f"});
				}
			}
		}else if($("#Listlength").val()<3){
			business.addMainContentParserHtml('xxcj/cityPersonInputPro.do',null);
		}else{
			if($("#arealevel").val()==1){
				swal({title: "省级表现突出候选个人已经达到上限",text: "",type:"error",confirmButtonColor: "#d9534f"});
			}else if($("#arealevel").val()==2){
				swal({title: "市级表现突出候选个人已经达到上限",text: "",type:"error",confirmButtonColor: "#d9534f"});
			}else if($("#arealevel").val()==3){
				swal({title: "县级表现突出候选个人已经达到上限",text: "",type:"error",confirmButtonColor: "#d9534f"});
			}
		}
	});

	//删除方法
	function deletePersonById(id){

		swal({
			title: "您确定执行删除操作吗？",
	        type: "warning",
			showCancelButton: true,
			closeOnConfirm: false,
			confirmButtonText: "是的，我要删除",
			confirmButtonColor: "#d9534f"
		   	}, function() {
		     	$.ajax({
		  			type: "POST",
		  			url: "${webpath}/xxcj/deleteXxcjPersonPro.do",
		  			data:{id:id},
		  			async:false,
		  			success: function(data){
		  				if(data.result=="error"){
		  					swal({title: "删除失败",text: "",type:"error",confirmButtonColor: "#d9534f"});
		  		     		return false;
		  				}else if(data.result=="success"){
		  			    	  swal({title: "删除成功",text: "",type:"success",confirmButtonColor: "#d9534f"});
		  			    	business.addMainContentParserHtml('xxcj/cityPersonPro.do',null);
		  				}
		  			}
		  		});
	    });
	}
</script>
	<style>
		#xiancenter{
			background: #fff;
		}
	</style>
</head>

<body>
<c:if test="${areaUser.arealevel==1}">
<div class="center_weizhi"><span style="float: left;">当前位置：信息采集 - 省级评比信息 - 省级表现突出候选个人</span></div>
</c:if>
<c:if test="${areaUser.arealevel==2}">
<div class="center_weizhi"><span style="float: left;">当前位置：信息采集 - 市级评比信息 - 市级表现突出候选个人</span></div>
</c:if>
<c:if test="${areaUser.arealevel==3}">
<div class="center_weizhi"><span style="float: left;">当前位置：信息采集 - 县级评比信息 - 县级表现突出候选个人</span></div>
</c:if>
<div class="center">
<div class="center_list" id="xiancenter">
    <div class="panel-group" id="accordion">
          <h4 class="panel-title">
            <div class="btn-group" style="margin-right:20px;float:left;">
            	<c:if test="${sysInitConfig.code == 1}">
	             	 <c:if test="${provinceReportUser.isDevInternalEva != 1 && provinceReportUser.electPerState != 1}">
		             	  <button id="shiGerenSB" type="button" class="btn btn-danger" style="font-size:16px;margin-top: 15px">候选个人信息录入</button>
	             	 </c:if>
	             </c:if>
            </div>
            <div class="btn-group" style="padding-top:30px;color:#F00;">提示：每个账号最多可上报3名个人 </div>
          </h4>
          
    </div>
    </br>
    <table class="table table-bordered table-hover table-condensed">
         <thead>
           <tr>
           	 <th width="30" height="30" bgcolor="#efefef"></th>
             <th width="50" height="30" bgcolor="#efefef">序号</th>
             <th bgcolor="#efefef">所属行政区</th>
             <th bgcolor="#efefef">姓名</th>
             <th bgcolor="#efefef">性别</th>
             <th bgcolor="#efefef">职务</th>
             <th bgcolor="#efefef">学历</th>
             <th bgcolor="#efefef">编制性质</th>
             <th bgcolor="#efefef">身份证号码</th>
             <th bgcolor="#efefef">联系电话</th>
             <%--<th width="160" bgcolor="#efefef">参与调查处理案件数量</th>--%>
             <th width="140" bgcolor="#efefef">操作</th>
           </tr>
         </thead>
         <tbody>
           <tr>
           <c:forEach  var="proPersonalList" items="${proPersonalList.list}" varStatus="status">
	          <tr>
	          	 <td width="30" height="30">
	          	 <c:choose>
	          	 <c:when test="${proPersonalList.isjoinlast=='1'}">
	          	 	<img src="${webpath }/static/img/not.png" data-toggle="tooltip" data-placement="right" title="参与过未入选">
	          	 </c:when>
	          	 <%-- <c:otherwise>
	          	 	<img src="${webpath }/static/img/new.png" data-toggle="tooltip" data-placement="right" title="新参选人员">
	          	 </c:otherwise> --%>
	          	 </c:choose>
	          	 </td>	
	             <td height="30" align="center">${status.index+1}</td>
	             <td>${proPersonalList.areanamePro}</td>
	             <td>${proPersonalList.namePro}</td>
	             <td>${proPersonalList.sexPro}</td>
	             <td>${proPersonalList.jobPro}</td>
	              <td>
	             <c:if test="${proPersonalList.educationcodePro=='01'}">初中及以下</c:if>
	             <c:if test="${proPersonalList.educationcodePro=='02'}">中专</c:if>
	             <c:if test="${proPersonalList.educationcodePro=='03'}">高中</c:if>
	             <c:if test="${proPersonalList.educationcodePro=='04'}">大专</c:if>
	             <c:if test="${proPersonalList.educationcodePro=='05'}">本科</c:if>
	             <c:if test="${proPersonalList.educationcodePro=='06'}">硕士</c:if>
	             <c:if test="${proPersonalList.educationcodePro=='07'}">博士</c:if>
	             <c:if test="${proPersonalList.educationcodePro=='08'}">其他</c:if>
	             </td>
	             <td>
	             <c:if test="${proPersonalList.orgpropcodePro=='01'}">事业编制</c:if>
	             <c:if test="${proPersonalList.orgpropcodePro=='02'}">参照公务员管理事业编制</c:if>
	             <c:if test="${proPersonalList.orgpropcodePro=='03'}">行政编制</c:if>
	             <c:if test="${proPersonalList.orgpropcodePro=='04'}">企业编制</c:if>
	             <c:if test="${proPersonalList.orgpropcodePro=='05'}">其他</c:if>
	             </td>
	             <td>${proPersonalList.cardidPro}</td>
	              <td>${proPersonalList.phonePro}</td>
	             <%--<td>${proPersonalList.handlcasenumPro}</td>--%>
	             <td>
	             <button class="btn btn-warning btn-xs" onclick="macroMgr.onLevelTwoMenuClick(null, 'xxcj/cityPersonDetailPro.do?id=${proPersonalList.id}')">查看</button> 
	             	<c:if test="${sysInitConfig.code == 1}">
	             	 <c:if test="${provinceReportUser.isDevInternalEva != 1 && provinceReportUser.electPerState != 1}">
		             	 <a href="#"><button class="btn btn-success btn-xs" onclick="macroMgr.onLevelTwoMenuClick(null, 'xxcj/cityPersonUpdatePro.do?id=${proPersonalList.id}')">编辑</button></a>
             			 <button class="btn btn-danger btn-xs" onclick="deletePersonById(${proPersonalList.id})">删除</button>
	             	 </c:if>
	             	</c:if>
	             
	             </td>
	           </tr>
	         </c:forEach>
	         <input type="hidden" id="Listlength" name="Listlength" value="${fn:length(proPersonalList.list)}" />
           </tr>
         </tbody>
       </table>
       <input type="hidden" id="arealevel" name="arealevel" value="${areaUser.arealevel}" />
	<input type="hidden" id="areaCode" name="areaCode" value="${areaUser.areaCode}" />
    </div>
     <!--列表翻页 开始-->
    <div class="page">
    	<span style="padding:12px; float:left; color:#0099cc;">共${proPersonalList.total}条记录</span>
        <ul class="pagination" id="pageCon">
            
        </ul>
    </div>
    <!--列表翻页 结束-->
</div>
</body>
</html>
