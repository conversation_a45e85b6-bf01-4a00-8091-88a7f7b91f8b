<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
<script type="text/javascript">
$(document).ready(function(){
	//文件上传、
	 $("#file1").fileinput({
      uploadUrl: WEBPATH+'/pdffileupload.do', // you must set a valid URL here else you will get an error
      allowedFileExtensions : ['doc','docx'],
      language:'zh',
      minFileCount: 1,
      maxFileCount: 1,
      minFileSize:1,
      maxFileSize:51200,
      enctype: 'multipart/form-data',        
      showPreview:false,
      autoReplace:true,
      slugCallback: function(filename) {
          return filename.replace('(', '_').replace(']', '_');
      }
	}).on('filepreupload', function(event, data, previewId, index) { //文件开始上传操作
	   $("#saveXxcjPersonBtn").prop('disabled', true);
	}).on('fileuploaded', function(event, data, previewId, index) {//文件上传完成执行操作
		$("#teamMaterialName").val(data.response.fileRealName);
	  	$("#teamMaterialURL").val(data.response.url);
		$("#jtcl").text(data.response.fileRealName);
		$("#saveXxcjPersonBtn").prop('disabled',false);
	})
  
	//保存数据方法
	$('#saveProvinceGroupBtn').click(function() {
	
       	 var options = {
           url: WEBPATH+'/xxcj/saveOrUpdateXxcjGroup.do',
           type: 'post',
           success:function(data){
	           if(data.result=="error"){
	        	   swal("操作失败",data.message, "error");
	               return false;
	           }else if(data.result=="success"){
	        	   business.addMainContentParserHtml('xxcj/provinceGroup.do',null);
	        	  swal({title: "操作成功",text: "",type:"success"});
	             return false;
	           }else if(data.code == '007'){
          		 swal({ title : data.message, text : "", type : "info" });
          		 return false;
          	   }
         	}
       	 };
       	$("#saveProvinceGroupForm").data('formValidation').validate();
       	var validate = $("#saveProvinceGroupForm").data('formValidation').isValid();
       	if(validate){
       		var res = $("#anjuan").val();
			if(res!=""&&res!=null){
				var tn=$("#teamMaterialName").val();
				var tu=$("#teamMaterialURL").val();
				if(tn!=""&&tn!=null&&tu!=""&&tu!=null){
					$('#saveProvinceGroupForm').ajaxSubmit(options);
				}else{
					swal("提示", "请上传集体事迹材料", "error");
				}
			}else{
				swal("提示", "请选择案卷", "error");
			}
       	}else{
	       	 $("#saveProvinceGroupForm").data('formValidation').validate();
	    	 return false;
	     } 
   	});
	
	//表单验证
	$("#saveProvinceGroupForm").formValidation({
        framework: 'bootstrap',
        message: 'This value is not valid',
        icon:{
	            valid: 'glyphicon glyphicon-ok',
	            invalid: 'glyphicon glyphicon-remove',
	            validating: 'glyphicon glyphicon-refresh'
        },
        fields: {
        	"anjuanwenhao1": {
                validators: {
                	notEmpty: {
                        message: '请选择案卷文号'
                    }
                }
            },
        	"filesList[0].filetype": {
                validators: {
                	notEmpty: {
                        message: '请选择案卷类型'
                    }
                }
            },
            "filesList[0].filesimpledesc": {
                validators: {
                	stringLength: {
                        min: 1,
                        max: 150,
                        message: '请输入1-150个字符'
                    }
                }
            },
            "filesList[0].filedetiaildesc": {
                validators: {
                	stringLength: {
                        min: 1,
                        max: 2000,
                        message: '请输入1-2000个字符'
                    }
                }
            },
            "filesList[0].isPublic": {
                validators: {
                    notEmpty: {
                        message: '请选择处罚情况是否在门户网站公开'
                    },
                    callback: {
                       	message: '',
                           callback: function(value, validator, $field) {
                           	if(value=='1'){
                           		$("#saveProvinceGroupForm").formValidation('enableFieldValidators', 'filesList[0].publicAddress', true);
                           	}else{
                           		$("#saveProvinceGroupForm").formValidation('enableFieldValidators', 'filesList[0].publicAddress', false);
                           	}
           					return true;
                          }
                     }
                }
            },
            "filesList[0].publicAddress": {
                validators: {
                    notEmpty: {
                        message: '请输入信息公开网址'
                    }
                }
            }
	   }
	});

	//初始化加载select2
    $(".multiselect-data-ajax").select2({
		  language:'zh-CN',
		  data:[{id:'${xzcfaj.oldid}'+'${xzcfaj.filetype}',text:'${xzcfaj.filecode}'}],
		  allowClear:true
		}).val(['${xzcfaj.oldid}'+'${xzcfaj.filetype}']).trigger("change").select2({
		  language:'zh-CN',
		  ajax: {
		    url: "${webpath}/xxcj/selectAnjuanByAreacode.do",
		    dataType: 'json',
		    delay: 500,
		    type:'POST',
		    data: function (params) {
		      return {
		        query: params.term,
		        type:$("#fileType").val(),
		        qtype:1,
		        areacode:'${areaUser.areaCode}'.substring(0,2)
		        
		      };
		    },
		    processResults: function (data, params) {
		      return {
		        results: data
		      };
		    },
		    cache: true
		  },
		  escapeMarkup: function (markup) 
		  { 
			  return markup; 
		  },
		  templateResult:function (result) {
		        return result.text;
	      },
		  templateSelection:function (result) {
		        return result.text;
	      },
		  minimumInputLength:1,
		  theme:'bootstrap',
		  placeholder:'请选择案件决定书文号',
		  allowClear:true
		});
    	//绑定选择select事件
		$('.multiselect-data-ajax').on('select2:select', function (evt) {

			var res = $(".multiselect-data-ajax").select2("data");
			$("#filecode").val(res[0].text);
			$("#belongareacode").val(res[0].areacode);
			$("#oldid").val(res[0].oldid);
		});
		//绑定取消选择select事件
		$('.multiselect-data-ajax').on('select2:unselecting', function (evt) {
			$("#filecode").val("");
			$("#belongareacode").val("");
			$("#oldid").val("");
		});
});

function clearAnJuanWenHao(){
	$("#filecode").val("");
	$("#belongareacode").val("");
	$("#oldid").val("");
	$("#anjuan").val(null).trigger("change");
}
$("#showUpload1").click(function(){
	$("#uploadTr").attr("style","");
});
</script>
</head>
<body>
<div class="center_weizhi">当前位置：信息采集 - 参选先进集体信息 - 省级案件上报</div>
<div class="center">
<div class="center_list">
   	<form id="saveProvinceGroupForm" method="post" class="form-horizontal">
        <table class="table_input">
         <tbody>
           <tr>
             <td width="300">省级参选单位</td>
             <td style="text-align: left;">
              ${areaUser.userName}
             </td>
           </tr>
           <tr>
           		<td>
           		集体事迹材料
           		</td>
           		<td style="text-align: left;">
           		 <span id="jtcl" style="font-size: 16px;">
           		 		<c:choose>
		                 		<c:when test="${electionUnit.teamMaterialName!=''&&electionUnit.teamMaterialName!=null}">
		                 			 ${electionUnit.teamMaterialName}
		                 			 <c:if test="${areaUser.sysStatus=='1'&&areaUser.reportState=='0'}">
		                 			 <button type="button" id="showUpload1" class="btn btn-primary btn-xs">重新上传</button>
		                 			 </c:if>
		                 		</c:when>
		                 		<c:otherwise>
		                 			<span style="font-size: 14px;">未上传</span>
		                 		</c:otherwise>
		            	</c:choose>
           		 </span>
           		</td>
           </tr>
           <c:if test="${areaUser.sysStatus=='1'&&areaUser.reportState=='0'}">
           <tr id="uploadTr" <c:if test="${electionUnit.teamMaterialName!=''&&electionUnit.teamMaterialName!=null}">style='display:none'</c:if>>
             <td>集体事迹材料上传</td>
             <td style="text-align: left;">
	        	<input id="file1" type="file" name="file" class="file-loading"/> 
	        	<span style="color: red;">仅允许上传doc和docx格式文件，大小不超过50M</span>
	   			<input type="hidden" name="electionUnit.id" value="${electionUnit.id}"/>
	   			<input type="hidden" name="electionUnit.areacode" value="${electionUnit.areacode}"/>
	            <input type="hidden" id="teamMaterialName" name="electionUnit.teamMaterialName" value="${electionUnit.teamMaterialName}"/>
	            <input type="hidden" id="teamMaterialURL" name="electionUnit.teamMaterialURL" value="${electionUnit.teamMaterialURL}"/>
             </td>
           </tr>
           </c:if>
           <tr>
           	<td colspan="2">
           	<div class="shangbao_panel">
                <div class="shangbao_titlt">行政处罚案卷一</div>
                <table style="width:100%;" class="table_input">
                	<tr>
             	 		<td>案卷类型</td>
			             <td>
			              <div class="form-group">
			                 <div class="col-sm-12">
			           		 		<select class="form-control" <c:if test="${areaUser.sysStatus!='1'||areaUser.reportState=='1'}">disabled="disabled"</c:if> id="fileType" name="filesList[0].filetype" onchange="clearAnJuanWenHao()">
			           		 		   <option value="">请选择</option>
						               <option value="0" <c:if test="${xzcfaj.filetype==0}">selected</c:if> >行政处罚案卷</option>
						               <option value="1" <c:if test="${xzcfaj.filetype==1}">selected</c:if> >按日计罚案卷</option>
						               <option value="2" <c:if test="${xzcfaj.filetype==2}">selected</c:if> >移送行政拘留案卷</option>
						               <option value="3" <c:if test="${xzcfaj.filetype==3}">selected</c:if> >涉嫌犯罪移送案卷</option>
						               <option value="6" <c:if test="${xzcfaj.filetype==6}">selected</c:if> >查封扣押案卷</option>
						               <option value="7" <c:if test="${xzcfaj.filetype==7}">selected</c:if> >限产停产案卷</option>
					             	</select>
			              	 </div>
					     </div> 
			             </td>
             	 		</tr>
                    <tr>
                     <td>案卷文号</td>
                     <td style="text-align:left;">
                     	<div class="form-group">
	                 <div class="col-sm-12">
                    	<select class="multiselect-data-ajax form-control" name="anjuanwenhao1" <c:if test="${areaUser.sysStatus!='1'||areaUser.reportState=='1' }">disabled="disabled"</c:if> id="anjuan">
						</select>
	                 	 <input type="hidden" name="filesList[0].filecode"  id="filecode"  value="${xzcfaj.filecode}"/>
	                 	 <input type="hidden" name="filesList[0].belongareacode" id="belongareacode" value="${xzcfaj.belongareacode}" />
	                 	 <input type="hidden" name="filesList[0].id" value="${xzcfaj.id}"/>
	                 	 <input type="hidden" name="filesList[0].oldid" id="oldid" value="${xzcfaj.oldid}"/>

			             </div>
			            </div>
                     </td>
                   </tr>
                   <tr>
                     <td>案卷材料简版说明</td>
                     <td>
                     <div class="form-group">
			                 <div class="col-sm-12">
                     <textarea  rows="5" class="form-control" <c:if test="${areaUser.sysStatus!='1' ||areaUser.reportState=='1'}">disabled="disabled"</c:if> name="filesList[0].filesimpledesc" placeholder="请输入案卷材料简版说明">${xzcfaj.filesimpledesc}</textarea>
                     </div>
                     </div>
                     </td>
                   </tr>
                   <tr>
                     <td>案卷材料详版说明</td>
                     <td>
                     <div class="form-group">
			                 <div class="col-sm-12">
                     <textarea  rows="5" class="form-control" <c:if test="${areaUser.sysStatus!='1'|| areaUser.reportState=='1' }">disabled="disabled"</c:if> name="filesList[0].filedetiaildesc"  placeholder="请输入案卷材料详版说明">${xzcfaj.filedetiaildesc}</textarea>
                      </div>
                     </div>
                     </td>
                   </tr>
                   <tr>
                     <td width="224">处罚情况是否在门户网站公开</td>
                     <td>
                      	<div class="form-group">
			                 <div class="col-sm-12">
		                     <select name="filesList[0].isPublic" <c:if test="${areaUser.sysStatus!='1'|| areaUser.reportState=='1'}">disabled="disabled"</c:if> class="form-control">
		                       <option value="">请选择</option>
		                       <option value="1" <c:if test="${xzcfaj.isPublic==1}">selected</c:if> >是</option>
		                       <option value="0" <c:if test="${xzcfaj.isPublic==0}">selected</c:if> >否</option>
		                     </select>
		                     </div>
		                </div>
                     </td>
                   </tr>
                   <tr>
                     <td>信息公开网址</td>
                     <td>
                     	<div class="form-group">
			                 <div class="col-sm-12">
                     	<input type="text" class="form-control" <c:if test="${areaUser.sysStatus!='1'||areaUser.reportState=='1' }">disabled="disabled"</c:if> name="filesList[0].publicAddress" placeholder="请输入信息公开网址" value="${xzcfaj.publicAddress}">
                         </div>
		                </div>
                     </td>
                   </tr>
                </table>
            </div>
            </td>
           </tr>           
            <tr>
             <td>&nbsp;</td>
             <td style="text-align:left;">
               <c:if test="${areaUser.sysStatus=='1'&& areaUser.reportState=='0'}">
             	<a href="#"><button type="button" class="btn btn-primary" id="saveProvinceGroupBtn"  style="font-size:16px;width:150px; margin-top:5px;">信息保存</button></a>
             	</c:if>
             </td>
           </tr>
         </tbody>
       </table>
       </form>
    </div>
</div>

</body>
</html>
