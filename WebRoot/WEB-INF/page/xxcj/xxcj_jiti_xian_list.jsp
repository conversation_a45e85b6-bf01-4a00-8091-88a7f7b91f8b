<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<body>
<div class="center_weizhi">当前位置：信息采集 - 参选先进集体信息 - 县级案件上报</div>
<div class="center">
<div class="center_list">
        <table class="table table-bordered table-hover table-condensed">
         <thead>
           <tr>
             <td width="50" height="30" bgcolor="#efefef">序号</td>
             <td width="200" bgcolor="#efefef">所属行政区</td>
             <td width="110" bgcolor="#efefef">行政处罚案卷1</td>
             <td width="110" bgcolor="#efefef">行政处罚案卷2</td>
             <td width="150" bgcolor="#efefef">移送行政拘留案卷1</td>
             <td width="150" bgcolor="#efefef">涉嫌犯罪移送案卷1</td>
             <td width="130" bgcolor="#efefef">操作</td>
           </tr>
         </thead>
         <tbody>
           <c:forEach  var="electionunit" items="${countyGrouplList}" varStatus="status">
         		 <tr>
	             <td height="30" align="center">${status.index+1}</td>
	             <td>${electionunit.province }${electionunit.city }${electionunit.county }</td>
	             <td>${electionunit.xzcf1}</td>
	             <td>${electionunit.xzcf2 }</td>
	             <td>${electionunit.xzjl }</td>
	             <td>${electionunit.sxfz }</td>
	             <td style="text-align: center;"><a href="#"><button class="btn btn-info btn-xs" onclick="macroMgr.onLevelTwoMenuClick(null, 'xxcj/countyGroupDetail.do?id=${electionunit.id}')">查看</button></a>
	             	<c:if test="${areaUser.sysStatus=='1' && areaUser.reportState=='0'}">
	             		 <c:if test="${sessionScope.sa_session.arealevel=='3' }">
	              		<a href="#"><button class="btn btn-success btn-xs" onclick="macroMgr.onLevelTwoMenuClick(null, 'xxcj/countyGroupUpdate.do?id=${electionunit.id}')">填报</button></a>
	              		</c:if>
	              	</c:if>
	              </td>
	           </tr>
          	</c:forEach>
         </tbody>
       </table>
    </div>
 </div>
</body>
</html>
