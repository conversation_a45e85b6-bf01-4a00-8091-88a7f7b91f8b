<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
<script type="text/javascript">
var areaChange=function(){
	 $("#filecodexxcf1").val("");
	 $("#belongareacodexxcf1").val("");
	 $("#xxcf1oldid").val("");
	 $("#typeaheadxxcf1").val(null).trigger("change");
	 
	 $("#filecodexxcf2").val("");
	 $("#belongareacodexxcf2").val("");
	 $("#xxcf2oldid").val("");
	 $("#typeaheadxxcf2").val(null).trigger("change");
	 
	 $("#handlcasecodesselectvalue").val("");
	 $("#handlcasenumtext").text("");
     $("#handlcasenum").val("");
	 $(".multiselect-data-ajax").val(null).trigger("change");
}

var resetSelect=function(){
	 $("#filecodexxcf1").val("");
	 $("#belongareacodexxcf1").val("");
	 $("#xxcf1oldid").val("");
	 $("#typeaheadxxcf1").val(null).trigger("change");
}

var resetSelect1=function(){
	 $("#filecodexxcf2").val("");
	 $("#belongareacodexxcf2").val("");
	 $("#xxcf2oldid").val("");
	 $("#typeaheadxxcf2").val(null).trigger("change");
}

$(document).ready(function(){
	 var v= $("#povinceSelect").val().substring(0,2);
	 if(v=='11'||v=='12'||v=='55'||v=='31'||v=='66'){
		 $("#countryoption").css('display','none'); 
	 }
	
	 //文件上传、
	 $("#file1").fileinput({
       uploadUrl: WEBPATH+'/pdffileupload.do', // you must set a valid URL here else you will get an error
       allowedFileExtensions : ['doc','docx'],
       language:'zh',
       //overwriteInitial: true,
       minFileCount: 1,
       maxFileCount: 1,
       minFileSize:1,
       maxFileSize:51200,
       enctype: 'multipart/form-data',        
       dropZoneTitle:"可拖拽文件到此处...",
       initialPreviewShowDelete: false,
       msgFilesTooMany:'选择上传的文件数量({n}) 超过允许的最大数值{m}！',
       msgZoomModalHeading:'文件预览',
       msgInvalidFileExtension:'不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
       msgNoFilesSelected:'请选择文件',
       msgValidationError:'文件类型不正确或文件过大',
       initialPreviewFileType:'pdf',
       browseLabel:"选择文件",
       removeLabel:'删除',
       removeTitle:'删除文件',
       uploadLabel:'上传',
       uploadTitle:'上传文件',
       cancelLabel: '取消',
       cancelTitle: '取消上传',
       showPreview:false,
       autoReplace:true,
       slugCallback: function(filename) {
           return filename.replace('(', '_').replace(']', '_');
       }
	}).on('filepreupload', function(event, data, previewId, index) { //文件开始上传操作
		
	   $("#saveXxcjPersonBtn").prop('disabled', true);
	
	}).on('fileuploaded', function(event, data, previewId, index) {//文件上传完成执行操作
		
		if(event.currentTarget.id=="file1"){
			$("#personalmaterialname").val(data.response.fileRealName);
		  	$("#personalmaterialurl").val(data.response.url);
		  	$("#filetext1").text(data.response.fileRealName);
		}else{
			$("#perhonestfilename").val(data.response.fileRealName);
		  	$("#perhonestfileurl").val(data.response.url);
		  	$("#filetext2").text(data.response.fileRealName);
		}
		$("#saveXxcjPersonBtn").prop('disabled',false);
	})/* .on('fileclear', function(event) {  //文件上传删除文件执行的操作
		if(event.currentTarget.id=="file1"){
			$("#personalmaterialname").val("");
		  	$("#personalmaterialurl").val("");
		  	$("#filetext1").text("未上传");
		}else{
			$("#perhonestfilename").val("");
		  	$("#perhonestfileurl").val("");
		  	$("#filetext2").text("未上传");
		}
   		
   	}) */
  //文件上传、
	 $("#file2").fileinput({
      uploadUrl: WEBPATH+'/pdffileupload.do', // you must set a valid URL here else you will get an error
      allowedFileExtensions : ['pdf'],
      language:'zh',
      //overwriteInitial: true,
      minFileCount: 1,
      maxFileCount: 1,
      minFileSize:1,
      maxFileSize:51200,
      enctype: 'multipart/form-data',        
      dropZoneTitle:"可拖拽文件到此处...",
      initialPreviewShowDelete: false,
      msgFilesTooMany:'选择上传的文件数量({n}) 超过允许的最大数值{m}！',
      msgZoomModalHeading:'文件预览',
      msgInvalidFileExtension:'不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
      msgNoFilesSelected:'请选择文件',
      msgValidationError:'文件类型不正确或文件过大',
      initialPreviewFileType:'pdf',
      browseLabel:"选择文件",
      removeLabel:'删除',
      removeTitle:'删除文件',
      uploadLabel:'上传',
      uploadTitle:'上传文件',
      cancelLabel: '取消',
      cancelTitle: '取消上传',
      showPreview:false,
      autoReplace:true,
      slugCallback: function(filename) {
          return filename.replace('(', '_').replace(']', '_');
      }
	}).on('filepreupload', function(event, data, previewId, index) { //文件开始上传操作
		
	   $("#saveXxcjPersonBtn").prop('disabled', true);
	
	}).on('fileuploaded', function(event, data, previewId, index) {//文件上传完成执行操作
		
		if(event.currentTarget.id=="file1"){
			$("#personalmaterialname").val(data.response.fileRealName);
		  	$("#personalmaterialurl").val(data.response.url);
		  	$("#filetext1").text(data.response.fileRealName);
		}else{
			$("#perhonestfilename").val(data.response.fileRealName);
		  	$("#perhonestfileurl").val(data.response.url);
		  	$("#filetext2").text(data.response.fileRealName);
		}
		$("#saveXxcjPersonBtn").prop('disabled',false);
	})
   	
   	
	//保存数据方法
	$('#saveXxcjPersonBtn').click(function() {
		//checkArea();
      	 var options = {
          url: WEBPATH+'/xxcj/saveXxcjPerson.do',
          type: 'post',
          success:function(data){
	           if(data.result=="error"){
	        	   swal("操作失败",data.message, "error");
	               return false;
	           }else if(data.result=="success"){
	        	  business.addMainContentParserHtml('xxcj/personInput.do',null);
	        	  swal({title: "保存成功",text: "",type:"success"});
	             return false;
	           }else if(data.code == '007'){
	          		 swal({ title : data.message, text : "", type : "info" });
	          		 return false;
	           }
        	}
      	 };
      	$("#saveXxcjPersonForm").data('formValidation').validate();
      	var validate = $("#saveXxcjPersonForm").data('formValidation').isValid();
      	if(validate){
      		var materialname=$("#personalmaterialname").val();
       		var materialurl=$("#personalmaterialurl").val();
       		
       		var honestfilename=$("#perhonestfilename").val();
       		var honestfileurl=$("#perhonestfileurl").val();
       		
      		if((materialname==""||materialurl=="")||(honestfilename==""||honestfileurl=="")){
      		 swal("提示", "请选择上传文件", "error");
      		}else{
      			var anjuan=$("#typeaheadxxcf1").val();
      			if(anjuan!=""&&anjuan!=null){
      				var res = $(".multiselect-data-ajax").select2("data");
      				if(res.length>0){
      					$.ajax({
                			   type: "POST",
                			   url: "${webpath}/xxcj/selectPersonAnjuanCountNew.do",
                			   data:{subAreaCode:$("#areacode").val().substring(0,2),type:$("#filetype").val()},
                			   async:false,
                			   success: function(data){
                				   if(data.result=="error"){
                					   swal({title: "查询失败",text: "",type:"error"});
                		               return false;
                			       }else if(data.result=="success"){
                			          if(data.data>=10){
                			        	swal({title: "操作失败",text: "个人信息上报数量已经达到上限",type:"error"});
                			          }else{
                			        	  var filecode1 =  $("#filecodexxcf1").val();
                			        	  var filecode2 =  $("#filecodexxcf2").val();              			        	  
                			        	  if(filecode1==filecode2){
                			        	  	swal({title: "操作失败",text: "两份案卷不能选择同一份案卷",type:"error"});
                			        	  }else{
                			        		//验证省市县是否正确选择,并给省市县隐藏域赋值
                			        		var b = checkArea();
                			        		if(b){
                			        			$('#saveXxcjPersonForm').ajaxSubmit(options);
                			        		}else{
                			        			//给省市县赋值
                			        			swal({title: "操作失败",text: "请正确选择省市县",type:"error"});
                			        		}
                			        	  }
                			          }
                			       }
                			   }
                			});
      				}else{
      					swal("提示", "请选择参与调查处理案件的决定书文号或移送编号", "error");
      				}
      			}else{
      				swal("提示", "请选择案卷", "error");
      			}
      		}
      	}
  	});
           
          
           
         
	//表单验证
	$("#saveXxcjPersonForm").formValidation({
       framework: 'bootstrap',
       message: 'This value is not valid',
       icon:{
	            valid: 'glyphicon glyphicon-ok',
	            invalid: 'glyphicon glyphicon-remove',
	            validating: 'glyphicon glyphicon-refresh'
       },
       fields: {
       		"files[0].filetype": {
               validators: {
                   notEmpty: {
                       message: '请选择案卷一类型'
                   }
               }
           },
           /* "files[1].filetype": {
               validators: {
                   notEmpty: {
                       message: '请选择案卷二类型'
                   }
               }
           }, */
           "electionPersonal.name": {
               validators: {
                   notEmpty: {
                       message: '请输入姓名'
                   },
                   stringLength: {
                       min: 1,
                       max: 15,
                       message: '1-15个字符'
                   }
               }
           },
           "electionPersonal.sex": {
               validators: {
                   notEmpty: {
                       message: '请选择性别'
                   }
               }
           },
           "electionPersonal.job": {
               validators: {
                   notEmpty: {
                       message: '请输入职务'
                   },
                   stringLength: {
                       min: 1,
                       max: 30,
                       message: '1-30个字符'
                   }
               }
           },
           "electionPersonal.cardid": {
               validators: {
            	   verbose: false,
                   notEmpty: {
                       message: '请输入身份证号码'
                   },
                   regexp:{
                     	message:'请输入正确的身份证号码',
                     	regexp:/^\d{17}(\d|x)$/i
                  },
                  remote: {
                	   dataType:'json',
                	   type:'POST',
                	   delay:500,
                       url: '${webpath}/xxcj/checkIdCardInput.do',
                       data: function(validator, $field, value) {
                           return {
                               idcard:validator.getFieldElements('electionPersonal.cardid').val()
                           };
                       },
                	   message:'身份证号码已经存在，不允许重复录入'
                   }
               }
           },
           "electionPersonal.phone": {
               validators: {
                   notEmpty: {
                       message: '请输入联系电话'
                   },
                   phone:{
                   	message:'请输入正确的手机号码',
                   	country:'CN'
                   }
               }
           },
           "electionPersonal.unitname": {
               validators: {
                   notEmpty: {
                       message: '请输入单位名称'
                   },
		           stringLength: {
		               min: 1,
		               max: 15,
		               message: '1-15个字符'
		           }
               }
           },
           "electionPersonal.workyearnum": {
        	   validators: {
            	   notEmpty: {
                       message: '请输入工作年限'
                   },
                   regexp:{
                      	message:'请输入最多一位小数的有效数字',
                      	regexp:/^[0-9]+(.[0-9]{1,1})?$/i
                   }
               }
           },
           "electionPersonal.educationcode": {
               validators: {
                   notEmpty: {
                       message: '请选择学历'
                   }
               }
           },
           "electionPersonal.orgpropcode": {
               validators: {
                   notEmpty: {
                       message: '请选择编制性质'
                   }
               }
           },
           "electionPersonal.areatype": {
               validators: {
                   notEmpty: {
                       message: '请选择区划类别'
                   }
               }
           },
           "electionPersonal.filesimpledesc": {
               validators: {
            	   stringLength: {
		               min: 1,
		               max: 150,
		               message: '案件一调查情况材料简版说明不能超过150字符'
		           }
               }
           },
           "electionPersonal.filedetiaildesc": {
               validators: {
            	   stringLength: {
		               min: 1,
		               max: 2000,
		               message: '案件一调查情况材料详版说明不能超过2000字符'
		           }
               }
           },
           "electionPersonal.filesimpledesc2": {
               validators: {
            	   stringLength: {
		               min: 1,
		               max: 150,
		               message: '案件二调查情况材料简版说明不能超过150字符'
		           }
               }
           },
           "electionPersonal.filedetiaildesc2": {
               validators: {
            	   stringLength: {
		               min: 1,
		               max: 2000,
		               message: '案件二调查情况材料详版说明不能超过2000字符'
		           }
               }
           }
	   }
	});
	//初始化加载select2
	$(".multiselect-data-ajax").select2({
		  language:'zh-CN',
		  ajax: {
		    url: "${webpath}/xxcj/selectAnjuanByAreacodeAll.do",
		    dataType: 'json',
		    delay: 500,
		    type:'POST',
		    data: function (params) {
		      return {
		        query: params.term,
		        areacode:$("#areacode").val()
		      };
		    },
		    processResults: function (data, params) {
		      return {
		        results: data,
		      };
		    },
		    cache: true
		  },
		  escapeMarkup: function (markup) 
		  { 
			  return markup; 
		  }, 
		  templateResult:function (result) {
		        return result.text;
	      },
		  templateSelection:function (result) {
		        return result.text;
	      },
		  minimumInputLength:1,
		  placeholder:'请选择参与调查处理案件的决定书文号或移送编号',
		  allowClear:true
		});
	
	
		//绑定选择select事件
		$('.multiselect-data-ajax').on('select2:select', function (evt) {
			var res = $(".multiselect-data-ajax").select2("data");
			var value= [];
			$.each(res,function(index,obj){
				var objValue={id:obj.id,text:obj.text}
				value.push(objValue);
			});
			$("#handlcasecodesselectvalue").val(JSON.stringify(value).replace(/\"/g, "'"));
			$("#handlcasenumtext").text(res.length);
	     	$("#handlcasenum").val(res.length);
		});
		//绑定取消选择select事件
		$('.multiselect-data-ajax').on('select2:unselect', function (evt) {
			var res = $(".multiselect-data-ajax").select2("data");
			var value= [];
			$.each(res,function(index,obj){
				var objValue={id:obj.id,text:obj.text}
				value.push(objValue);
			});
			$("#handlcasecodesselectvalue").val(JSON.stringify(value).replace(/\"/g, "'"));
			$("#handlcasenumtext").text(res.length);
	     	$("#handlcasenum").val(res.length);
		});
		
		//初始化加载案件一select2
		$("#typeaheadxxcf1").select2({
			  language:'zh-CN',
			  ajax: {
			    url: "${webpath}/xxcj/selectAnjuanByAreacodePerson.do",
			    dataType: 'json',
			    delay: 500,
			    type:'POST',
			    data: function (params) {
			      return {
			        query: params.term,
			        type:$("#filetype").val(),
			        qtype:0,
			        areacode:$("#areacode").val()
			        
			      };
			    },
			    processResults: function (data, params) {
			      return {
			        results: data
			      };
			    },
			    cache: true
			  },
			  escapeMarkup: function (markup) 
			  { 
				  return markup; 
			  },
			  templateResult:function (result) {
			        return result.text;
		      },
			  templateSelection:function (result) {
			        return result.text;
		      },
			  minimumInputLength:1,
			  theme:'bootstrap',
			  placeholder:'请选决定文书号/移送编号',
			  allowClear:true
			});
	    	//绑定选择select事件
			$('#typeaheadxxcf1').on('select2:select', function (evt) {
				var res = $("#typeaheadxxcf1").select2("data");
				$("#filecodexxcf1").val(res[0].text);
				$("#belongareacodexxcf1").val(res[0].areacode);
				$("#xxcf1oldid").val(res[0].oldid);
			});
			//绑定取消选择select事件
			$('#typeaheadxxcf1').on('select2:unselecting', function (evt) {
				$("#filecodexxcf1").val("");
				$("#belongareacodexxcf1").val("");
				$("#xxcf1oldid").val("");
			});
			
			//初始化加载案件二select2
			$("#typeaheadxxcf2").select2({
				  language:'zh-CN',
				  ajax: {
				    url: "${webpath}/xxcj/selectAnjuanByAreacodePerson.do",
				    dataType: 'json',
				    delay: 500,
				    type:'POST',
				    data: function (params) {
				      return {
				        query: params.term,
				        type:$("#filetype2").val(),
				        qtype:0,
				        areacode:$("#areacode").val()
				        
				      };
				    },
				    processResults: function (data, params) {
				      return {
				        results: data
				      };
				    },
				    cache: true
				  },
				  escapeMarkup: function (markup) 
				  { 
					  return markup; 
				  },
				  templateResult:function (result) {
				        return result.text;
			      },
				  templateSelection:function (result) {
				        return result.text;
			      },
				  minimumInputLength:1,
				  theme:'bootstrap',
				  placeholder:'请选决定文书号/移送编号',
				  allowClear:true
				});
			//绑定选择select事件
			$('#typeaheadxxcf2').on('select2:select', function (evt) {
				var res = $("#typeaheadxxcf2").select2("data");
				console.log(res);
				$("#filecodexxcf2").val(res[0].text);
				$("#belongareacodexxcf2").val(res[0].areacode);
				$("#xxcf2oldid").val(res[0].oldid);
			});
			//绑定取消选择select事件
			$('#typeaheadxxcf2').on('select2:unselecting', function (evt) {
				$("#filecodexxcf2").val("");
				$("#belongareacodexxcf2").val("");
				$("#xxcf2oldid").val("");
			});
	 })
	 
	 //根据级别级联省市县
	 function typeChange(){
	    //获取被选中的option标签  
     	var vs = $('#typeSelect').val(); 
     	if(vs==1 || vs==''){
     		var v= $("#areacode").val().substring(0,2);
   			$("#areacode").val(v);
   			
   			//
			var v= $("#citySelect").val();
			if(v!=null){
				$("#areacode1").val($("#povinceSelect").val());
			}
   			//选省后清空市县
			$("#citySelect").val("");
			$("#countySelect").val("");
			
 	    	$("#citySelect").css('display','none'); 
 	    	$("#countySelect").css('display','none'); 
 	    } 
	    if(vs==2){
	    	var v= $("#areacode").val().substring(0,4);
			$("#areacode").val(v);
			
			//
			var v= $("#countySelect").val();
			if(v!=null){
				$("#areacode1").val($("#citySelect").val());
			}
			
			//选市后清空县
			$("#countySelect").val("");
			
	    	$("#citySelect").css('display','block'); 
	    	$("#countySelect").css('display','none'); 
	    }
	    if(vs==3){
	    	$("#citySelect").css('display','block'); 
	    	$("#countySelect").css('display','block'); 
	    }
	    
	    /* $("#a1").val($('#povinceSelect').val());
		$("#a2").val($("#citySelect").val());
		$("#a3").val($("#countySelect").val()); */
    }  
	 
	 /* function povinceChange(){
		 //给行政区划控件赋值
		 var v= $("#povinceSelect").val().substring(0,2);
		 $("#areacode").val(v);
		 
		 if(v=='11'||v=='12'||v=='55'||v=='31'||v=='66'){
			 $("#countySelect").css('display','none'); 
		 }else{
			 var type = $('#typeSelect').val();
			 if(type=='3'){
				 $("#countySelect").css('display','block');
			 }
		 }
		 
		 $("#citySelect").empty();
		 var code = $('#povinceSelect').val();
		 $.ajax({
			 type: "POST",
			   url: WEBPATH+'/cascaded.do',
			   data:{code:code},
			   async:false,
			   success: function(data){
				   var option = "<option value='' selected>请选择城市</option>";
				   $("#citySelect").append(option);
				   var array = data.data;
				   for(var i = 0;i<array.length;i++){
					   var option="<option value="+data.data[i].code+">"+data.data[i].name+"</option>";
					   console.log(option);
					   $("#citySelect").append(option);
				   }
			   }
		 });
	 } */
	 
	 function cityChange(){
		//给行政区划控件赋值
		 var v= $("#citySelect").val();
		 if(v==null || v==''){
			 $("#areacode1").val($("#povinceSelect").val());
			 $("#areacode").val($("#povinceSelect").val().substring(0,2));
		 }else{
			 $("#areacode1").val(v);
			 v1 = v.substring(0,2)
			 if(v1=='11'||v1=='12'||v1=='55'||v1=='31'||v1=='66'){
				v = v.substring(0,6);
			 }else{
				v = v.substring(0,4);
			 }
			 $("#areacode").val(v);
		 }
		 
		 $("#countySelect").empty();
		 var code = $('#citySelect').val();
		 $.ajax({
			 type: "POST",
			   url: WEBPATH+'/cascaded.do',
			   data:{code:code},
			   async:false,
			   success: function(data){
				   var option = "<option value='' selected>请选择区县</option>";
				   $("#countySelect").append(option);
				   var array = data.data;
				   for(var i = 0;i<array.length;i++){
					   var option="<option value="+data.data[i].code+">"+data.data[i].name+"</option>";
					   console.log(option);
					   $("#countySelect").append(option);
				   }
			   }
		 });
	 }
	 
	 function countyChange(){
		 //给行政区划控件赋值
		 var v= $("#countySelect").val();
		 if(v==null || v==''){//如果选择：请选择区县
			 $("#areacode1").val($("#citySelect").val());
			 $("#areacode").val($("#citySelect").val().substring(0,6));	
		 }else{
			 $("#areacode1").val(v);
			 $("#areacode").val(v.substring(0,6));
		 }
	 }
	 
	 function educationChange(){
		 var v = $("#educationcode").find("option:selected").text();
		 $("#educationname").val(v);
	 }
	 
	 function orgpropChange(){
		 var v = $("#orgpropcode").find("option:selected").text();
		 $("#orgpropname").val(v);
	 }
	 
	 //验证省市县
	 function checkArea(){

		 var b = true;
		 var type = $('#typeSelect').val();
		 var povince = $("#povinceSelect").val()
		 var city = $("#citySelect").val();
		 var county = $("#countySelect").val();
		 
		 var provinceName=$("#povinceSelect").find("option:selected").text();
		 var cityName=$("#citySelect").find("option:selected").text();
		 var countryName=$("#countySelect").find("option:selected").text();
		 
		 
		 if(type==1){
			 $("#provinceName").val(provinceName);
	       	 $("#cityName").val("");
	       	 $("#countryName").val("");
		 }	 
		 if(type==2){
			 if(city==null || city==''){
				 b = false;
			 }
			 $("#provinceName").val(provinceName);
	       	 $("#cityName").val(cityName);
	       	 $("#countryName").val("");
		 }
		 if(type==3){
			 if(city==null || city=='' || county==null || county==''){
				 b = false;
			 }
			 $("#provinceName").val(provinceName);
	       	 $("#cityName").val(cityName);
	       	 $("#countryName").val(countryName);
		 }
		 return b;
		 
	 }
	 
	
</script>
</head>
<body>
<div class="center_weizhi">当前位置：信息采集 - 参选先进个人信息 - 先进个人上报</div>
<div class="center">
<div class="center_list" id="scrollable-dropdown-menu">
    <form id="saveXxcjPersonForm" method="post">
    	<input type="hidden" name="electionPersonal.loginid" value="${sessionScope.sa_session.loginid}">
    	<input type="hidden" id="areacode" name="" value="${provincecode.substring(0,2)}">
    	<input type="hidden" id="areacode1" name="electionPersonal.areacode" value="${provincecode}">
    	<input type="hidden" id="educationname" name="electionPersonal.educationname">
        <input type="hidden" id="orgpropname" name="electionPersonal.orgpropname">
        
	        <!-- 省<input type="text" id="a1" name="">
	        市<input type="text" id="a2" name="">
	        县<input type="text" id="a3" name=""> -->
        
        <table class="table_input">
         <tbody>
           <tr>
           	<td width="300"></td>
           	<td style="text-align:center;color: red">本页面信息保存后，此处行政区级别、所属行政区将不能更改，请确认填写正确！！</td>
           </tr>
           <tr>
	        <td width="300">行政区划级别</td>
	        <td>
	        <div class="form-group">
               <div class="col-sm-12">
             		<select <c:if test="${areaUser.sysStatus!='1'||reportState=='1'}">disabled="disabled"</c:if> id="typeSelect" name="electionPersonal.areatype" class="form-control" onchange="typeChange();areaChange()">
			          <option value="">请选择所属行政区</option>
		           	  <option value="1">省</option>
		         	  <option value="2">市</option>
		         	  <option id="countryoption" value="3">县</option>
			        </select>
             	</div>
             </div>
	        </td>
	      </tr>
           <tr>
		        <td>所属行政区</td>
		        <td>
		        <div class="form-group">
	               <div class="col-sm-4">
	               		<input type="hidden" id="provinceName" name="electionPersonal.province">
	             		<select id="povinceSelect" <c:if test="${areaUser.sysStatus!='1'||reportState=='1'}">disabled="disabled"</c:if> class="form-control" onchange="areaChange()">
				          	<option value="${provincecode}" >${provinceName}</option>
					          <%-- <c:forEach var="list" items="${provinceList}">
							      <option value="${list.code}">${list.name}</option>
							  </c:forEach> --%>
				          </select>
	             	</div>
	             	<div class="col-sm-4">
	             		<input type="hidden" id="cityName" name="electionPersonal.city">
	             		<select id="citySelect" <c:if test="${areaUser.sysStatus!='1'||reportState=='1'}">disabled="disabled"</c:if> onchange="cityChange();areaChange()" class="form-control" style="display: none">
		              	<option value="" selected>请选择城市</option>
					          <c:forEach var="list" items="${cityList}">
							      <option value="${list.code}" >${list.name}</option>
							  </c:forEach>
		            </select>
		            </div>
		            <div class="col-sm-4">
		            	<input type="hidden" id="countryName" name="electionPersonal.country">
	             		<select id="countySelect" <c:if test="${areaUser.sysStatus!='1'||reportState=='1'}">disabled="disabled"</c:if> onchange="countyChange();areaChange()" class="form-control" style="display: none">
			               	<option value=''>请选择区县</option>
			            </select>
	             	</div>
	              </div>
		        </td>
	      </tr>	
	      <tr>
            <td width="224">案卷类型</td>
            <td>
            <div class="form-group">
            	<div class="col-sm-12">
            		<select name="files[0].filetype" <c:if test="${areaUser.sysStatus!='1'||reportState=='1'}">disabled="disabled"</c:if> class="form-control" id="filetype" onchange="resetSelect();">
		               <option value="">请选择案卷类型</option>
                       <option value="0">行政处罚案卷</option>
                       <option value="1">按日计罚案卷</option>
                       <option value="6">查封扣押案卷</option>
                       <option value="7">限产停产案卷</option>
                       <option value="2">移送行政拘留案卷</option>
                       <option value="3">涉嫌犯罪移送案卷</option>
		             </select>
            	</div>
            </div>
            </td>
          </tr>
           <tr>
             <td>决定文书号/移送编号</td>
             <td style="text-align: left;">
             	<div class="form-group">
                 <div class="col-sm-12">
                 <select id="typeaheadxxcf1" <c:if test="${areaUser.sysStatus!='1'||reportState=='1'}">disabled="disabled"</c:if> class="form-control" >
				 </select>
                 <input type="hidden"name="files[0].belongareacode" id="belongareacodexxcf1" />
                 <input type="hidden" name="files[0].filecode" id="filecodexxcf1"/>
                  <input type="hidden" name="files[0].oldid" id="xxcf1oldid"/>
               </div>
             </div>
              </td>
           </tr>
            <%--<tr>
             <td>案件调查情况材料简版说明</td>
             <td>
             <div class="form-group">
                 <div class="col-sm-12">
             <textarea rows="5" class="form-control" <c:if test="${areaUser.sysStatus!='1'||reportState=='1'}">disabled="disabled"</c:if> name="electionPersonal.filesimpledesc" placeholder="请输入案卷材料简版说明"></textarea>
             </div>
             </div>
             </td>
           </tr>
           <tr>
             <td>案件调查情况材料详版说明</td>
             <td>
             <div class="form-group">
                 <div class="col-sm-12">
             	<textarea rows="5" class="form-control" <c:if test="${areaUser.sysStatus!='1'||reportState=='1'}">disabled="disabled"</c:if> name="electionPersonal.filedetiaildesc" placeholder="请输入案卷材料详版说明"></textarea>
             </div>
             </div>
             </td>
           </tr>--%>
           
           <tr>
            <td width="224">案卷类型</td>
            <td>
            <div class="form-group">
            	<div class="col-sm-12">
            		<select name="files[1].filetype" <c:if test="${areaUser.sysStatus!='1'||reportState=='1'}">disabled="disabled"</c:if> class="form-control" id="filetype2" onchange="resetSelect1();">
		               <option value="">请选择案卷类型</option>
                       <option value="0">行政处罚案卷</option>
                       <option value="1">按日计罚案卷</option>
                       <option value="6">查封扣押案卷</option>
                       <option value="7">限产停产案卷</option>
                       <option value="2">移送行政拘留案卷</option>
                       <option value="3">涉嫌犯罪移送案卷</option>
		             </select>
            	</div>
            </div>
            </td>
          </tr>
           <tr>
             <td>决定文书号/移送编号</td>
             <td style="text-align: left;">
             	<div class="form-group">
	                 <div class="col-sm-12">
	                 <select id="typeaheadxxcf2" <c:if test="${areaUser.sysStatus!='1'||reportState=='1'}">disabled="disabled"</c:if> class="form-control" >
					 </select>
	                 <input type="hidden"name="files[1].belongareacode" id="belongareacodexxcf2" />
	                 <input type="hidden" name="files[1].filecode" id="filecodexxcf2"/>
	                  <input type="hidden" name="files[1].oldid" id="xxcf2oldid"/>
	               </div>
	             </div>
              </td>
           </tr>
            <tr>
             <td>案件调查情况材料简版说明</td>
             <td>
             <div class="form-group">
                 <div class="col-sm-12">
             <textarea rows="5" class="form-control" <c:if test="${areaUser.sysStatus!='1'||reportState=='1'}">disabled="disabled"</c:if> name="electionPersonal.filesimpledesc2" placeholder="请输入案卷材料简版说明"></textarea>
             </div>
             </div>
             </td>
           </tr>
           <tr>
             <td>案件调查情况材料详版说明</td>
             <td>
             <div class="form-group">
                 <div class="col-sm-12">
             	<textarea rows="5" class="form-control" <c:if test="${areaUser.sysStatus!='1'||reportState=='1'}">disabled="disabled"</c:if> name="electionPersonal.filedetiaildesc2" placeholder="请输入案卷材料详版说明"></textarea>
             </div>
             </div>
             </td>
           </tr>
           
           <tr>
             <td>姓名</td>
             <td>
             <div class="form-group">
                 <div class="col-sm-12">
             <input type="text" class="form-control" <c:if test="${areaUser.sysStatus!='1'||reportState=='1'}">disabled="disabled"</c:if> name="electionPersonal.name" placeholder="请输入姓名">
             </div>
             </div>
             </td>
           </tr>
           <tr>
             <td>性别</td>
             <td>
             <div class="form-group">
                 <div class="col-sm-12">
             <select name="electionPersonal.sex" <c:if test="${areaUser.sysStatus!='1'||reportState=='1'}">disabled="disabled"</c:if> class="form-control">
               <option value="">请选择性别</option>
               <option>男</option>
               <option>女</option>
             </select>
             </div>
             </div>
             </td>
           </tr>
           <tr>
             <td>职务</td>
             <td>
             <div class="form-group">
                 <div class="col-sm-12">
             <input type="text" class="form-control" <c:if test="${areaUser.sysStatus!='1'||reportState=='1'}">disabled="disabled"</c:if> name="electionPersonal.job" placeholder="请输入职务">
             </div>
             </div>
             </td>
           </tr>
           
           <tr>
             <td>所在单位名称</td>
             <td>
             <div class="form-group">
                 <div class="col-sm-12">
             <input type="text" class="form-control" <c:if test="${areaUser.sysStatus!='1'||reportState=='1'}">disabled="disabled"</c:if> name="electionPersonal.unitname" placeholder="请输入所在单位名称">
             </div>
             </div>
             </td>
           </tr>
           
           <tr>
             <td>环保工作年限</td>
             <td>
             <div class="form-group">
                 <div class="col-sm-12">
             <input type="text" class="form-control" <c:if test="${areaUser.sysStatus!='1'||reportState=='1'}">disabled="disabled"</c:if> name="electionPersonal.workyearnum" placeholder="请输入环保工作年限">
             </div>
             </div>
             </td>
           </tr>
           
           <tr>
             <td>身份证号码</td>
             <td>
             <div class="form-group">
                 <div class="col-sm-12">
             <input type="text" class="form-control" <c:if test="${areaUser.sysStatus!='1'||reportState=='1'}">disabled="disabled"</c:if> name="electionPersonal.cardid" placeholder="请输入身份证号码">
             </div>
             </div>
             </td>
           </tr>
           
           <tr>
             <td>学历</td>
             <td>
             <div class="form-group">
                 <div class="col-sm-12">
             <select id="educationcode" <c:if test="${areaUser.sysStatus!='1'||reportState=='1'}">disabled="disabled"</c:if> name="electionPersonal.educationcode" class="form-control" onchange="educationChange()">
             	<option value="" >请选择学历</option>
                <option value="01" >初中及以下</option>
		        <option value="02" >中专</option>
		        <option value="03" >高中</option>
		        <option value="04" >大专</option>
		        <option value="05" >本科</option>
		        <option value="06" >硕士</option>
		        <option value="07" >博士</option>
		        <option value="08" >其他</option>
             </select>
             </div>
             </div>
             </td>
           </tr>
           
           <tr>
             <td>编制性质</td>
             <td>
             <div class="form-group">
                 <div class="col-sm-12">
             <select id="orgpropcode" <c:if test="${areaUser.sysStatus!='1'||reportState=='1'}">disabled="disabled"</c:if> name="electionPersonal.orgpropcode" class="form-control" onchange="orgpropChange()">
             	<option value="">请选择</option>
		        <option value="01" >事业编制</option>
		        <option value="02" >参照公务员管理事业编制</option>
		        <option value="03" >行政编制</option>
		        <option value="04" >企业编制</option>
		        <option value="05" >其他</option>
             </select>
             </div>
             </div>
             </td>
           </tr>
           
           <tr>
             <td>联系电话</td>
             <td>
             <div class="form-group">
                 <div class="col-sm-12">
             <input type="text" class="form-control" <c:if test="${areaUser.sysStatus!='1'||reportState=='1'}">disabled="disabled"</c:if> name="electionPersonal.phone" placeholder="请输入联系电话">
             </div>
             </div>
             </td>
           </tr>
           <tr>
             <td>参与调查处理案件</td>
             <td>
             <div class="form-group">
                 <div class="col-sm-12">
                 	<select <c:if test="${areaUser.sysStatus!='1'||reportState=='1'}">disabled="disabled"</c:if> class="multiselect-data-ajax form-control" multiple="multiple">
					</select>
					<input type="hidden" name="electionPersonal.handlcasecodes" id="handlcasecodesselectvalue" />
             </div>
             </div>
             </td>
           </tr>
          <!--  <tr>
             <td>参与调查处理案件案卷号</td>
             <td style="text-align:left;">
             	<div class="form-group">
                 <div class="col-sm-12">
                            <select id="handlcasecodesselect" multiple="multiple">
                            
                            </select>
                 </div>
                 </div>     
             </td>
           </tr> -->
           <tr>
             <td>参与调查处理案件数量</td>
             <td style="text-align:left;">
             <div class="form-group">
                 <div class="col-sm-12">
             <span id="handlcasenumtext" >根据选择的参与调查处理案件案卷号数量自动获取</span>
             <input type="hidden" class="form-control" name="electionPersonal.handlcasenum" id="handlcasenum"/>
             </div>
                 </div>    
             </td>
           </tr>
          <!--  <tr>
             <td>参与调查处理案件数量</td>
             <td>
             <div class="form-group">
                 <div class="col-sm-12">
             <input type="text" class="form-control" name="electionPersonal.handlcasenum" placeholder="请输入参与调查处理案件数量">
             </div>
             </div>
             </td>
           </tr>
           <tr>
             <td>参与调查处理案件案卷号</td>
             <td style="text-align:left;">
             <div class="form-group">
                 <div class="col-sm-12">
             <textarea name="electionPersonal.handlcasecodes" rows="5" class="form-control" id="name8" placeholder="请输入参与调查处理案件案卷号"></textarea>
             </div>
             </div>
             </td>
           </tr> -->
           <tr>
             <td>个人事迹材料</td>
             <td style="text-align:left;">
             		<div class="form-group">
                 <div class="col-sm-12">
                 <span id="filetext1" style="font-size: 14px;">
              			  未上传
		        </span> 
                 </div>
                 </div>
                 <input type="hidden" name="electionPersonal.personalmaterialname" id="personalmaterialname"/>
                 <input type="hidden" name="electionPersonal.personalmaterialurl" id="personalmaterialurl"/>
             </td>
           </tr>
           <c:if test="${areaUser.sysStatus=='1'&&reportState=='0'}">
           <tr>
               <td>上传附件</td>
               <td style="text-align:left;">
               		<div class="form-group">
                 	<div class="col-sm-12">
	               		<input id="file1" type="file" name="file1" class="file-loading"  value="1">
	               		<span style="color: red;">仅允许上传doc、docx格式文件，大小不超过50M</span>
               		</div>
               		</div>
               </td>
           </tr>
           </c:if>
           <tr>
             <td>个人廉洁执法相关证明材料</td>
             <td style="text-align:left;">
             <div class="form-group">
                 <div class="col-sm-12">
                <span id="filetext2" style="font-size: 14px;">
              			  未上传
		        </span> 
		        </div>
		        </div>
		        <input type="hidden" name="electionPersonal.perhonestfilename" id="perhonestfilename"/>
                 <input type="hidden" name="electionPersonal.perhonestfileurl" id="perhonestfileurl"/>
             </td>
           </tr>
             <c:if test="${areaUser.sysStatus=='1'&&reportState=='0'}">
            <tr>
               <td>上传附件</td>
               <td style="text-align:left;">
               	<div class="form-group">
                 <div class="col-sm-12">
	               		<input id="file2" type="file" name="file2" class="file-loading" value="2"> 
	               		<span style="color: red;">仅允许上传pdf格式文件，大小不超过50M</span>
	               </div>
	               </div>		
               </td>
           </tr>
            </c:if>
           <tr>
             <td align="center">&nbsp;</td>
             <td style="text-align:left;">
             <c:if test="${reportState=='0'&&areaUser.sysStatus=='1'}">
             <a href="#"><button type="button" class="btn btn-primary" id="saveXxcjPersonBtn" style="font-size:16px;width:150px; margin-top:5px;">信息保存</button></a></td>
           	</c:if>
           </tr>
         </tbody>
       </table>
       </form>
    </div>
</div>
</body>
</html>
