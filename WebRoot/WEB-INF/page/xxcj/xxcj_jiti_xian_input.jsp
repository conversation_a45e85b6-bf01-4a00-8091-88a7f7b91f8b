<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
<style type="text/css">
/*自定义宽度*/  
.myOwnDdl{  
    display:inline-block;  
    width:40%;
}  
  
/* 实现宽度自定义 */  
.myOwnDdl .btn-group{  
    width:100%;  
}  
.myOwnDdl .multiselect {  
    width:100%;  
    text-align:right;  
    margin-top:-5px;  
}  
.myOwnDdl ul {  
    width:100%;  
}  
.myOwnDdl .multiselect-selected-text {  
    left:0;  
    right:25px;  
    height:500px;
    text-align:center;  
    padding-left:20px;  
}  
  
/*控制隔行换色*/  
.myOwnDll .multiselect-container li.odd {  
    background: #eeeeee;  
}  


</style>
<script type="text/javascript">
	$(document).ready(function(){
		//保存数据方法
		$('#saveCountyGroupBtn').click(function() {
	       	 var options = {
	           url: WEBPATH+'/xxcj/saveXxcjGroup.do',
	           type: 'post',
	           success:function(data){
		           if(data.result=="error"){
		        	   swal("保存失败", "信息保存操作失败了!", "error");
		               return false;
		           }else if(data.result=="success"){
		        	   business.addMainContentParserHtml('xxcj/xxcjcountyGroupList.do',null);
		        	  swal({title: "保存成功",text: "",type:"success"});
		             return false;
		           }
	         	}
	       	 };
	       	$("#saveCountyGroupForm").data('formValidation').validate();
	       	var validate = $("#saveCountyGroupForm").data('formValidation').isValid();
	       	if(validate){
	       	  	var areaname=$("[name='electionUnit.areacode']").find("option:selected").text();
	       		var cityname=$("#city").find("option:selected").text();
	           	$("#areaname").val(areaname);
	           	$("#cityname").val(cityname);
	           	$("#countryname").val(areaname);
	           	var xzcf1=$("#typeaheadxxcf1").val();
	        	if(xzcf1!=""&&xzcf1!=null){
	        		var count=0
	        		$.ajax({
	        			   type: "POST",
	        			   url: "${webpath}/xxcj/selectGroupAnjuanCount.do",
	        			   data:{type:3},
	        			   async:false,
	        			   success: function(data){
	        				   if(data.result=="error"){
	        					   swal({title: "查询失败",text: "",type:"error"});
	        		               return false;
	        			       }else if(data.result=="success"){
	        			    	   count=data.data;
	        			       }
	        			   }
	        		});
	        		if(count<4){
	        			$.ajax({
		        			   type: "POST",
		        			   url: "${webpath}/xxcj/checkUnitExist.do",
		        			   data:{areacode:$("[name='electionUnit.areacode']").val()},
		        			   async:false,
		        			   success: function(data){
		        				   if(data.result=="error"){
		        					   swal({title: "查询失败",text: "",type:"error"});
		        		               return false;
		        			       }else if(data.result=="success"){
		        			          if(data.data==1){
		        			        	swal({title: "操作失败",text: "该市级信息已经上报",type:"error"});
		        			          }else{
		        			 			var xzcf2=$("#typeaheadxxcf2").val();
		        			 			var xzcf3=$("#typeaheadxxcf3").val();
			 	       			 		if(xzcf2!=""&&xzcf2!=null){
			 	   			 				if(xzcf1==xzcf2){
			 		   			 				swal({title: "提示",text: "不能选择相同的行政处罚案卷",type:"error"});
			 	       			           		return;
			 	   			 				}
			 	   			 				
			 	   			 			}
		        			 			if(xzcf3!=""&&xzcf3!=null){
		        			 				if(xzcf1==xzcf3){
		 	       			 				swal({title: "提示",text: "不能选择相同的行政处罚案卷",type:"error"});
		 	       			           		return;
		        			 				}
		        			 			}
		        			 			if(xzcf2!=""&&xzcf2!=null&&xzcf3!=""&&xzcf3!=null){
		 	       			 			if((xzcf1==xzcf2)||(xzcf1==xzcf3)||(xzcf2==xzcf3)){
		 	       			           		swal({title: "提示",text: "不能选择相同的行政处罚案卷",type:"error"});
		 	       			           		return;
		 	       			           	}
		        			 			}
		        			        	$('#saveCountyGroupForm').ajaxSubmit(options);
		        			          }
		        			       }
		        			   }
		        			});
	        		}else{
	        			swal({title: "提示",text: "最多可上报4个县级单位",type:"error"});
	        		}
	        	}else{
	        		swal({title: "提示",text: "请选择行政处罚案卷1",type:"error"});
	        	}
	       	}
	   	});
		//表单验证
		//表单校验
		$("#saveCountyGroupForm").formValidation({
	        framework: 'bootstrap',
	        message: 'This value is not valid',
	        icon:{
		            valid: 'glyphicon glyphicon-ok',
		            invalid: 'glyphicon glyphicon-remove',
		            validating: 'glyphicon glyphicon-refresh'
	        },
	        fields: {
	        	"electionUnit.areacode": {
	                validators: {
	                    notEmpty: {
	                        message: '请选择区县'
	                    }
	                }
	            },
	        	"city": {
	                validators: {
	                    notEmpty: {
	                        message: '请选择市'
	                    }
	                }
	            },
	            "filesList[0].filesimpledesc": {
	                validators: {
	                    notEmpty: {
	                        message: '请输入案卷材料简版说明'
	                    },
	                    stringLength: {
	                        min: 1,
	                        max: 150,
	                        message: '1-150个字符'
	                    }
	                }
	            },
	            "filesList[0].filedetiaildesc": {
	                validators: {
	                    notEmpty: {
	                        message: '请输入案卷材料详版说明'
	                    },
	                    stringLength: {
	                        min: 1,
	                        max: 2000,
	                        message: '1-2000个字符'
	                    }
	                }
	            },
	            "filesList[1].filesimpledesc": {
	                validators: {
	                    stringLength: {
	                        min: 1,
	                        max: 150,
	                        message: '1-150个字符'
	                    }
	                }
	            },
	            "filesList[1].filedetiaildesc": {
	                validators: {
	                    stringLength: {
	                        min: 1,
	                        max: 2000,
	                        message: '1-2000个字符'
	                    }
	                }
	            },
	            "filesList[2].filesimpledesc": {
	                validators: {
	                    stringLength: {
	                        min: 1,
	                        max: 150,
	                        message: '1-150个字符'
	                    }
	                }
	            },
	            "filesList[2].filedetiaildesc": {
	                validators: {
	                    stringLength: {
	                        min: 1,
	                        max: 2000,
	                        message: '1-2000个字符'
	                    }
	                }
	            },
	            "filesList[3].filesimpledesc": {
	                validators: {
	                    stringLength: {
	                        min: 1,
	                        max: 150,
	                        message: '1-150个字符'
	                    }
	                }
	            },
	            "filesList[3].filedetiaildesc": {
	                validators: {
	                    stringLength: {
	                        min: 1,
	                        max: 2000,
	                        message: '1-2000个字符'
	                    }
	                }
	            },
	            "filesList[4].filesimpledesc": {
	                validators: {
	                    stringLength: {
	                        min: 1,
	                        max: 150,
	                        message: '1-150个字符'
	                    }
	                }
	            },
	            "filesList[4].filedetiaildesc": {
	                validators: {
	                    stringLength: {
	                        min: 1,
	                        max: 2000,
	                        message: '1-2000个字符'
	                    }
	                }
	            },
	            "filesList[5].filesimpledesc": {
	                validators: {
	                    stringLength: {
	                        min: 1,
	                        max: 150,
	                        message: '1-150个字符'
	                    }
	                }
	            },
	            "filesList[5].filedetiaildesc": {
	                validators: {
	                    stringLength: {
	                        min: 1,
	                        max:2000,
	                        message: '1-2000个字符'
	                    }
	                }
	            },
		   }
		});
		ajaxAutoCompleteInit();
	});
	
	function ajaxAutoCompletToXxcf1(){
		$("#typeaheadxxcf1").select2({
			  language:'zh-CN',
			  ajax: {
			    url: "${webpath}/xxcj/selectAnjuanByAreacode.do",
			    dataType: 'json',
			    delay: 500,
			    type:'POST',
			    data: function (params) {
			      return {
			        query: params.term,
			        type:0,
			        qtype:1,
			        areacode:$("#county").val().substring(0,6)
			        
			      };
			    },
			    processResults: function (data, params) {
			      return {
			        results: data
			      };
			    },
			    cache: true
			  },
			  escapeMarkup: function (markup) 
			  { 
				  return markup; 
			  },
			  templateResult:function (result) {
			        return result.text;
		      },
			  templateSelection:function (result) {
			        return result.text;
		      },
		      minimumInputLength:3,
			  theme:'bootstrap',
			  placeholder:'请选择案件决定书文号',
			  allowClear:true
			});
	    	//绑定选择select事件
			$('#typeaheadxxcf1').on('select2:select', function (evt) {
				var res = $("#typeaheadxxcf1").select2("data");
				$("#filecodexxcf1").val(res[0].text);
				$("#belongareacodexxcf1").val(res[0].areacode);
				$("#xxcf1oldid").val(res[0].oldid);
			});
			//绑定取消选择select事件
			$('#typeaheadxxcf1').on('select2:unselecting', function (evt) {
				$("#filecodexxcf1").val("");
				$("#belongareacodexxcf1").val("");
				$("#xxcf1oldid").val("");
			});
	}
	
	function ajaxAutoCompletToXxcf2(){
		$("#typeaheadxxcf2").select2({
			  language:'zh-CN',
			  ajax: {
			    url: "${webpath}/xxcj/selectAnjuanByAreacode.do",
			    dataType: 'json',
			    delay: 500,
			    type:'POST',
			    data: function (params) {
			      return {
			        query: params.term,
			        type:0,
			        qtype:1,
			        areacode:$("#county").val().substring(0,6)
			        
			      };
			    },
			    processResults: function (data, params) {
			      return {
			        results: data
			      };
			    },
			    cache: true
			  },
			  escapeMarkup: function (markup) 
			  { 
				  return markup; 
			  },
			  templateResult:function (result) {
			        return result.text;
		      },
			  templateSelection:function (result) {
			        return result.text;
		      },
			  minimumInputLength:3,
			  theme:'bootstrap',
			  placeholder:'请选择案件决定书文号',
			  allowClear:true
			});
	    	//绑定选择select事件
			$('#typeaheadxxcf2').on('select2:select', function (evt) {
				var res = $("#typeaheadxxcf2").select2("data");
				$("#filecodexxcf2").val(res[0].text);
				$("#belongareacodexxcf2").val(res[0].areacode);
				$("#xxcf2oldid").val(res[0].oldid);
			});
			//绑定取消选择select事件
			$('#typeaheadxxcf2').on('select2:unselecting', function (evt) {
				$("#filecodexxcf2").val("");
				$("#belongareacodexxcf2").val("");
				$("#xxcf2oldid").val("");
			});
	}
	
	function ajaxAutoCompletToXxcf3(){
		$("#typeaheadxxcf3").select2({
			  language:'zh-CN',
			  ajax: {
			    url: "${webpath}/xxcj/selectAnjuanByAreacode.do",
			    dataType: 'json',
			    delay: 500,
			    type:'POST',
			    data: function (params) {
			      return {
			        query: params.term,
			        type:0,
			        qtype:1,
			        areacode:$("#county").val().substring(0,6)
			        
			      };
			    },
			    processResults: function (data, params) {
			      return {
			        results: data
			      };
			    },
			    cache: true
			  },
			  escapeMarkup: function (markup) 
			  { 
				  return markup; 
			  },
			  templateResult:function (result) {
			        return result.text;
		      },
			  templateSelection:function (result) {
			        return result.text;
		      },
			  minimumInputLength:3,
			  theme:'bootstrap',
			  placeholder:'请选择案件决定书文号',
			  allowClear:true
			});
	    	//绑定选择select事件
			$('#typeaheadxxcf3').on('select2:select', function (evt) {
				var res = $("#typeaheadxxcf3").select2("data");
				$("#filecodexxcf3").val(res[0].text);
				$("#belongareacodexxcf3").val(res[0].areacode);
				$("#xxcf3oldid").val(res[0].oldid);
			});
			//绑定取消选择select事件
			$('#typeaheadxxcf3').on('select2:unselecting', function (evt) {
				$("#filecodexxcf3").val("");
				$("#belongareacodexxcf3").val("");
				$("#xxcf3oldid").val("");
			});
	}
	
 	function ajaxAutoCompletToArjf(){
		$("#typeaheadarjf").select2({
			  language:'zh-CN',
			  ajax: {
			    url: "${webpath}/xxcj/selectAnjuanByAreacode.do",
			    dataType: 'json',
			    delay: 500,
			    type:'POST',
			    data: function (params) {
			      return {
			        query: params.term,
			        type:1,
			        qtype:1,
			        areacode:$("#county").val().substring(0,6)
			        
			      };
			    },
			    processResults: function (data, params) {
			      return {
			        results: data
			      };
			    },
			    cache: true
			  },
			  escapeMarkup: function (markup) 
			  { 
				  return markup; 
			  },
			  templateResult:function (result) {
			        return result.text;
		      },
			  templateSelection:function (result) {
			        return result.text;
		      },
			  minimumInputLength:3,
			  theme:'bootstrap',
			  placeholder:'请选择案件决定书文号',
			  allowClear:true
			});
	    	//绑定选择select事件
			$('#typeaheadarjf').on('select2:select', function (evt) {
				var res = $("#typeaheadarjf").select2("data");
				$("#filecodearjf").val(res[0].text);
				$("#belongareacodearjf").val(res[0].areacode);
				$("#arjfoldid").val(res[0].oldid);
			});
			//绑定取消选择select事件
			$('#typeaheadarjf').on('select2:unselecting', function (evt) {
				$("#filecodearjf").val("");
				$("#belongareacodearjf").val("");
				$("#arjfoldid").val("");
			});
	}
	function ajaxAutoCompletToXzjl(){
		//首先进行销毁
		$("#typeaheadxzjl").select2({
			  language:'zh-CN',
			  ajax: {
			    url: "${webpath}/xxcj/selectAnjuanByAreacode.do",
			    dataType: 'json',
			    delay: 500,
			    type:'POST',
			    data: function (params) {
			      return {
			        query: params.term,
			        type:2,
			        qtype:1,
			        areacode:$("#county").val().substring(0,6)
			        
			      };
			    },
			    processResults: function (data, params) {
			      return {
			        results: data
			      };
			    },
			    cache: true
			  },
			  escapeMarkup: function (markup) 
			  { 
				  return markup; 
			  },
			  templateResult:function (result) {
			        return result.text;
		      },
			  templateSelection:function (result) {
			        return result.text;
		      },
			  minimumInputLength:3,
			  theme:'bootstrap',
			  placeholder:'请选择案件移送编号',
			  allowClear:true
			});
	    	//绑定选择select事件
			$('#typeaheadxzjl').on('select2:select', function (evt) {
				var res = $("#typeaheadxzjl").select2("data");
				$("#filecodexzjl").val(res[0].text);
				$("#belongareacodexzjl").val(res[0].areacode);
				$("#xzjloldid").val(res[0].oldid);
			});
			//绑定取消选择select事件
			$('#typeaheadxzjl').on('select2:unselecting', function (evt) {
				$("#filecodexzjl").val("");
				$("#belongareacodexzjl").val("");
				$("#xzjloldid").val("");
			});
	}
	function ajaxAutoCompletToSxfz(){
		$("#typeaheadsxfz").select2({
			  language:'zh-CN',
			  ajax: {
			    url: "${webpath}/xxcj/selectAnjuanByAreacode.do",
			    dataType: 'json',
			    delay: 500,
			    type:'POST',
			    data: function (params) {
			      return {
			        query: params.term,
			        type:3,
			        qtype:1,
			        areacode:$("#county").val().substring(0,6)
			        
			      };
			    },
			    processResults: function (data, params) {
			      return {
			        results: data
			      };
			    },
			    cache: true
			  },
			  escapeMarkup: function (markup) 
			  { 
				  return markup; 
			  },
			  templateResult:function (result) {
			        return result.text;
		      },
			  templateSelection:function (result) {
			        return result.text;
		      },
			  minimumInputLength:3,
			  theme:'bootstrap',
			  placeholder:'请选择案件移送编号',
			  allowClear:true
			});
	    	//绑定选择select事件
			$('#typeaheadsxfz').on('select2:select', function (evt) {
				var res = $("#typeaheadsxfz").select2("data");
				$("#filecodesxfz").val(res[0].text);
				$("#belongareacodesxfz").val(res[0].areacode);
				$("#sxfzoldid").val(res[0].oldid);
			});
			//绑定取消选择select事件
			$('#typeaheadsxfz').on('select2:unselecting', function (evt) {
				$("#filecodesxfz").val("");
				$("#belongareacodesxfz").val("");
				$("#sxfzoldid").val("");
			});
	}
	
	function ajaxAutoCompletToFyzx(){
		//首先进行销毁
		$("#typeaheadfyzx").select2({
			  language:'zh-CN',
			  ajax: {
			    url: "${webpath}/xxcj/selectAnjuanByAreacode.do",
			    dataType: 'json',
			    delay: 500,
			    type:'POST',
			    data: function (params) {
			      return {
			        query: params.term,
			        type:4,
			        qtype:1,
			        areacode:$("#county").val().substring(0,6)
			        
			      };
			    },
			    processResults: function (data, params) {
			      return {
			        results: data
			      };
			    },
			    cache: true
			  },
			  escapeMarkup: function (markup) 
			  { 
				  return markup; 
			  },
			  templateResult:function (result) {
			        return result.text;
		      },
			  templateSelection:function (result) {
			        return result.text;
		      },
			  minimumInputLength:3,
			  theme:'bootstrap',
			  placeholder:'请选择案件移送编号',
			  allowClear:true
			});
	    	//绑定选择select事件
			$('#typeaheadfyzx').on('select2:select', function (evt) {
				var res = $("#typeaheadfyzx").select2("data");
				$("#filecodefyzx").val(res[0].text);
				$("#belongareacodefyzx").val(res[0].areacode);
				$("#fyzxoldid").val(res[0].oldid);
			});
			//绑定取消选择select事件
			$('#typeaheadfyzx').on('select2:unselecting', function (evt) {
				$("#filecodefyzx").val("");
				$("#belongareacodefyzx").val("");
				$("#fyzxoldid").val("");
			});
	}
	function ajaxAutoCompleteInit(){
		ajaxAutoCompletToXxcf1();
		ajaxAutoCompletToXxcf2();
		ajaxAutoCompletToXxcf3();
		ajaxAutoCompletToArjf();
		ajaxAutoCompletToXzjl();
		ajaxAutoCompletToSxfz();			
		ajaxAutoCompletToFyzx();
	};
	function resetSelect(){
		$("#filecodexxcf1").val("");
		$("#belongareacodexxcf1").val("");
		$("#xxcf1oldid").val("");
		$("#typeaheadxxcf1").val(null).trigger("change");
		
		$("#filecodexxcf2").val("");
		$("#belongareacodexxcf2").val("");
		$("#xxcf2oldid").val("");
		$("#typeaheadxxcf2").val(null).trigger("change");
		
		$("#filecodexxcf3").val("");
		$("#belongareacodexxcf3").val("");
		$("#xxcf3oldid").val("");
		$("#typeaheadxxcf3").val(null).trigger("change");
		
		$("#filecodearjf").val("");
		$("#belongareacodearjf").val("");
		$("#arjfoldid").val("");
		$("#typeaheadarjf").val(null).trigger("change");
		
		$("#filecodexzjl").val("");
		$("#belongareacodexzjl").val("");
		$("#xzjloldid").val("");
		$("#typeaheadxzjl").val(null).trigger("change");
		
		$("#filecodesxfz").val("");
		$("#belongareacodesxfz").val("");
		$("#sxfzoldid").val("");
		$("#typeaheadsxfz").val(null).trigger("change");
		
		$("#filecodefyzx").val("");
		$("#belongareacodefyzx").val("");
		$("#fyzxoldid").val("");
		$("#typeaheadfyzx").val(null).trigger("change");
	}
</script>
</head>

<body>
<div class="center_weizhi">当前位置：信息采集 - 参选先进集体信息 - 县级案件及国控断面上报</div>
<div class="center">
<div class="center_list">
    	<form id="saveCountyGroupForm" method="post" class="form-horizontal">
        <table  class="table_input">
         <tbody>
           <tr>
             <td width="200">请选择市</td>
             <td>
              <div class="form-group">
                 <div class="col-sm-12">
			             <select id="city" name="city" class="form-control" onchange="business.cascaded($('#city').val(),'county');resetSelect();selectDuanmianList();" >
			               <option value="">请选择地市</option>
			               <c:forEach var="city" items="${cityList}">
				               	<option value="${city.code}">${city.name}</option>
				           </c:forEach>
			             </select>
			            <input type="hidden" name="electionUnit.city" id="cityname" value="${electionUnit.city}"/>
			             <input type="hidden" name="electionUnit.id" value="${electionUnit.id}"/>
			             <input type="hidden" name="electionUnit.areatype" value="3"/>
			             <input type="hidden" name="electionUnit.areaname" id="areaname" value="${electionUnit.areaname}"/>
			             <input type="hidden" name="electionUnit.country" id="countryname" value="${electionUnit.country}"/>
			             <input type="hidden" name="electionUnit.province" value="${areaUser.userName}"/>
             </div>
              </div>
             </td>
           </tr>
            <tr>
             <td>请选择区县</td>
             <td>
              <div class="form-group">
                 <div class="col-sm-12">
			             <select name="electionUnit.areacode" id="county" onchange="resetSelect();" class="form-control">
			               <option value="">请选择区县</option>
			             </select>
             </div>
              </div>
             </td>
           </tr>
     		 <tr>
             <td>&nbsp;</td>
             <td><span style="color:#295A5F; font-size:18px; float:left; font-weight:bold;">行政处罚</span></td>
           </tr>
           <tr>
             <td>行政处罚案卷1</td>
             <td style="text-align: left">
	              <div class="form-group">
	                 <div class="col-sm-12">
	               			<select id="typeaheadxxcf1" class="form-control">
							</select>
		                   <input type="hidden" name="filesList[0].filecode" id="filecodexxcf1"/>
		                   <input type="hidden"name="filesList[0].belongareacode" id="belongareacodexxcf1"/>
			               <input type="hidden" name="filesList[0].areacode" />
			               <input type="hidden" name="filesList[0].filetype" value="0"/>
			               <input type="hidden" name="filesList[0].oldid" id="xxcf1oldid"/>
			                <input type="hidden" name="filesList[0].penalizetype" value="0"/>
			     	</div> 
	             </div>
              </td>
           </tr>
           <tr>
             <td>案卷材料简版说明</td>
             <td>
              <div class="form-group">
                 <div class="col-sm-12">
             		<textarea name="filesList[0].filesimpledesc" rows="5" class="form-control"  placeholder="请输入案卷材料简版说明"></textarea>
              </div>
		     </div> 
             </td>
           </tr>
           <tr>
             <td>案卷材料详版说明</td>
             <td>
              <div class="form-group">
                 <div class="col-sm-12">
             <textarea name="filesList[0].filedetiaildesc" rows="5" class="form-control"  placeholder="请输入案卷材料详版说明"></textarea>
              </div>
		     </div> 
             </td>
           </tr>
           <tr>
             <td>行政处罚案卷2</td>
             <td style="text-align: left">
              <div class="form-group">
                 <div class="col-sm-12">
                 <select id="typeaheadxxcf2" class="multiselect-data-ajax form-control"  >
						</select>
                  <input type="hidden"name="filesList[1].belongareacode" id="belongareacodexxcf2"/>
              <input type="hidden" name="filesList[1].filecode" id="filecodexxcf2" />
               <input type="hidden" name="filesList[1].areacode" />
               <input type="hidden" name="filesList[1].filetype" value="0"/>
               <input type="hidden" name="filesList[1].oldid" id="xxcf2oldid"/>
                <input type="hidden" name="filesList[1].penalizetype" value="1"/>
              </div>
             </div>
               </td>
           </tr>
           <tr>
             <td>案卷材料简版说明</td>
             <td>
             
              <div class="form-group">
                 <div class="col-sm-12">
             <textarea name="filesList[1].filesimpledesc" rows="5" class="form-control"  placeholder="请输入案卷材料简版说明"></textarea>
             
              </div>
		     </div> 
             </td>
           </tr>
           <tr>
             <td>案卷材料详版说明</td>
             <td>
              <div class="form-group">
                 <div class="col-sm-12">
             <textarea name="filesList[1].filedetiaildesc" rows="5" class="form-control"  placeholder="请输入案卷材料详版说明"></textarea>
              </div>
		     </div> 
             </td>
           </tr>
           <tr>
             <td>行政处罚案卷3</td>
             <td style="text-align: left">
              <div class="form-group">
                 <div class="col-sm-12">
                 <select id="typeaheadxxcf3" class="multiselect-data-ajax form-control"  >
						</select>
                  <input type="hidden"name="filesList[2].belongareacode" id="belongareacodexxcf3"/>
              <input type="hidden" readonly="readonly" name="filesList[2].filecode" id="filecodexxcf3" />
               <input type="hidden" name="filesList[2].areacode" />
               <input type="hidden" name="filesList[2].filetype" value="0"/>
               <input type="hidden" name="filesList[2].oldid" id="xxcf3oldid"/>
               <input type="hidden" name="filesList[2].penalizetype" value="2"/>
                </div>
		     </div> 
               </td>
           </tr>
           <tr>
             <td>案卷材料简版说明</td>
             <td>
              <div class="form-group">
                 <div class="col-sm-12">
             <textarea name="filesList[2].filesimpledesc" rows="5" class="form-control"  placeholder="请输入案卷材料简版说明"></textarea>
              </div>
		     </div> 
             
             </td>
           </tr>
           <tr>
             <td>案卷材料详版说明</td>
             <td>
              <div class="form-group">
                 <div class="col-sm-12">
             <textarea name="filesList[2].filedetiaildesc" rows="5" class="form-control" placeholder="请输入案卷材料详版说明"></textarea>
              </div>
		     </div> 
             </td>
           </tr>
           <tr>
             <td>&nbsp;</td>
             <td><span style="color:#295A5F; font-size:18px; float:left; font-weight:bold;">按日计罚</span></td>
           </tr>
           <tr>
             <td>按日计罚案卷1</td>
             <td style="text-align: left">
              <div class="form-group">
                 <div class="col-sm-12">
                <select id="typeaheadarjf" class="multiselect-data-ajax form-control"  >
						</select>
                  <input type="hidden"name="filesList[3].belongareacode" id="belongareacodearjf"/>
                <input type="hidden" name="filesList[3].filecode" id="filecodearjf" />
               <input type="hidden" name="filesList[3].areacode" />
               <input type="hidden" name="filesList[3].filetype" value="1"/>
               <input type="hidden" name="filesList[3].oldid" id="arjfoldid"/>
                <input type="hidden" name="filesList[3].penalizetype" value="3"/>
                </div>
		     </div> 
               </td>
           </tr>
           <tr>
             <td>案卷材料简版说明</td>
             <td>
              <div class="form-group">
                 <div class="col-sm-12">
             <textarea name="filesList[3].filesimpledesc" rows="5" class="form-control"  placeholder="请输入案卷材料简版说明"></textarea>
              </div>
		     </div> 
             
             </td>
           </tr>
           <tr>
             <td>案卷材料详版说明</td>
             <td>
              <div class="form-group">
                 <div class="col-sm-12">
             <textarea name="filesList[3].filedetiaildesc" rows="5" class="form-control" placeholder="请输入案卷材料详版说明"></textarea>
              </div>
		     </div> 
             </td>
           </tr>
           <tr>
             <td>&nbsp;</td>
             <td><span style="color:#295A5F; font-size:18px; float:left; font-weight:bold;">移送行政拘留</span></td>
           </tr>
           <tr>
             <td>移送行政拘留案卷1</td>
             <td style="text-align: left">
              <div class="form-group">
                 <div class="col-sm-12">
                 <select id="typeaheadxzjl" class="multiselect-data-ajax form-control"  >
						</select>
                <input type="hidden"name="filesList[4].belongareacode" id="belongareacodexzjl"/>
                <input type="hidden" name="filesList[4].filecode" id="filecodexzjl"/>
               <input type="hidden" name="filesList[4].areacode" />
               <input type="hidden" name="filesList[4].filetype" value="2"/>
               <input type="hidden" name="filesList[4].oldid" id="xzjloldid"/>
                <input type="hidden" name="filesList[4].penalizetype" value="4"/>
                </div>
             </div>
             </td>
           </tr>
           <tr>
             <td>案卷材料简版说明</td>
             <td>
             <div class="form-group">
                 <div class="col-sm-12">
             <textarea name="filesList[4].filesimpledesc" rows="5" class="form-control"  placeholder="请输入案卷材料简版说明"></textarea>
               </div>
		     </div> 
             </td>
           </tr>
           <tr>
             <td>案卷材料详版说明</td>
             <td>
              <div class="form-group">
                 <div class="col-sm-12">
             <textarea name="filesList[4].filedetiaildesc" rows="5" class="form-control"  placeholder="请输入案卷材料详版说明"></textarea>
              </div>
		     </div> 
             </td>
           </tr>
           <tr>
             <td>&nbsp;</td>
             <td style="text-align:left;"><span style="color:#295A5F; font-size:18px; float:left; font-weight:bold;">涉嫌犯罪移送</span></td>
           </tr>
           <tr>
             <td>涉嫌犯罪移送案卷1</td>
             <td style="text-align: left">
              <div class="form-group">
                 <div class="col-sm-12">
                <select id="typeaheadsxfz" class="multiselect-data-ajax form-control"  >
						</select>
                <input type="hidden"name="filesList[5].belongareacode" id="belongareacodesxfz"/>
               <input type="hidden"  name="filesList[5].filecode" id="filecodesxfz" />
               <input type="hidden" name="filesList[5].areacode" />
                <input type="hidden" name="filesList[5].filetype" value="3"/>
                <input type="hidden" name="filesList[5].oldid" id="sxfzoldid"/>
                 <input type="hidden" name="filesList[5].penalizetype" value="5"/>
                </div>
             </div>
              </td>
           </tr>
           <tr>
             <td>案卷材料简版说明</td>
             <td>
              <div class="form-group">
                 <div class="col-sm-12">
             <textarea name="filesList[5].filesimpledesc" rows="5" class="form-control"  placeholder="请输入案卷材料简版说明"></textarea>
              </div>
		     </div> 
             </td>
           </tr>
           <tr>
             <td>案卷材料详版说明</td>
             <td>
              <div class="form-group">
                 <div class="col-sm-12">
             <textarea name="filesList[5].filedetiaildesc" rows="5" class="form-control" placeholder="请输入案卷材料详版说明"></textarea>
              </div>
		     </div> 
             </td>
           </tr>
           <tr>
             <td>&nbsp;</td>
             <td><span style="color:#295A5F; font-size:18px; float:left; font-weight:bold;">申请法院强制执行</span></td>
           </tr>
           <tr>
             <td>申请法院强制执行案卷1</td>
             <td style="text-align: left">
             	 <div class="form-group">
                 <div class="col-sm-12">
                <select id="typeaheadfyzx" class="multiselect-data-ajax form-control"  >
						</select>
                  <input type="hidden"name="filesList[6].belongareacode" id="belongareacodefyzx"/>
               <input type="hidden"  name="filesList[6].filecode" id="filecodefyzx" />
               <input type="hidden" name="filesList[6].areacode" />
                <input type="hidden" name="filesList[6].filetype" value="4"/>
                 <input type="hidden" name="filesList[6].oldid" id="fyzxoldid"/>
                  <input type="hidden" name="filesList[6].penalizetype" value="6"/>
                </div>
		     </div> 
               </td>
           </tr>
            <tr>
             <td align="center">&nbsp;</td>
             <td style="text-align:left;"><span style="color:#295A5F; font-size:18px; float:left; font-weight:bold;">国控监测断面名称</span></td>
           </tr>
            <tr>
             <td>国控监测断面名称</td>
             <td style="text-align:left;">
             		<div class="">  
                            <select id="duanmianlist" multiple="multiple">
                            
                            </select>
                     </div>
                 	<input type="hidden" name="electionUnit.guokongduanmian" id="duanmianvalue" />
             </td>
           </tr>
           <tr>
             <td align="center">&nbsp;</td>
             <td style="text-align:left;"><a href="#"><button type="button" class="btn btn-primary" id="saveCountyGroupBtn" style="font-size:16px;width:150px; margin-top:5px;">信息保存</button></a></td>
           </tr>
         </tbody>
       </table>
       </form>
    </div>
</div>

</body>
<script type="text/javascript">
$(document).ready(function(){
	$('#duanmianlist').multiselect({
        buttonWidth:"600px",
        maxHeight:600,
        nonSelectedText:'请选择国控监测断面名称',
        numberDisplayed:15,
        includeSelectAllOption:true,
        selectAllText:'全选',
        allSelectedText:"已选全部",
        selectAllJustVisible:false,
        selectAllName:true,
        onDropdownHide: function(event) {
        	var options =$("#duanmianlist").multiselect('getSelected');
            options.each(function () {
         	   var label = $(this).val();
         	   if(label!=null){
         		  $("#duanmianvalue").val(JSON.stringify(label));
         	   }else{
         		  $("#duanmianvalue").val("");
         	   }
            }); 
        }
    });

	//加载国控断面下拉列表
	selectDuanmianList();
	//默认选中
	var value='${electionUnit.guokongduanmian}';
	$("#duanmianvalue").val(value);
	var delSelect=eval(value);
	if($.isArray(delSelect)){
		$('#duanmianlist').multiselect('select',delSelect,true);
		
	}
});
//加载国控断面下拉框的值
function selectDuanmianList(){
	$.ajax({
		   type: "POST",
		   url: "${webpath}/xxcj/selectDuanMianListByAreacode.do",
		   data:{areacode:$("#city").val()},
		   async:false,
		   success: function(data){
			   if(data.result=="error"){
				   swal({title: "查询失败",text: "",type:"success"});
	               return false;
		       }else if(data.result=="success"){
	    		   $("#duanmianlist").multiselect('dataprovider',data.data);
	    		   $("#duanmianlist").multiselect('refresh');
		           return true;
		       }
		   }
	});
}

</script>
</html>
