<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<script type="text/javascript">
$(document).ready(function(){
	var objValue=eval("${electionPersonalDetail.handlcasecodes}");
	var str=[];
	$.each(objValue,function(index,obj){
		str.push(obj.text);
	});
	$("#handlcasecodes").text(str.toString());
});
function downloadFile(url,fileName){
	if(url!=null&&url!=""){
		window.location.href="${webpath}/filedownload.do?url="+url+"&fileName="+fileName;
	}else{
		swal({title: "提示",text: "您下载的附件不存在！",type:"info"});
	}
}
</script>
<html>
<head>
<body>
<div class="center_weizhi">当前位置：信息采集 - 参选先进个人信息 - 市级先进个人查看</div>
<div class="center">
<div class="center_list">
  <table align="center" class="table_input">
    <tbody>
      <tr>
        <td>所属行政区</td>
        <td style="text-align:left;">${electionPersonalDetail.province}${electionPersonalDetail.city}</td>
      </tr>
      <tr>
        <td width="200">案卷类型</td>
        <td style="text-align:left;">
        <c:if test="${fileDetail.filetype==0}">行政处罚案卷</c:if>
         <c:if test="${fileDetail.filetype==1}">按日计罚案卷</c:if>
          <c:if test="${fileDetail.filetype==6}">查封扣押案卷</c:if>
           <c:if test="${fileDetail.filetype==7}">限产停产案卷</c:if>
            <c:if test="${fileDetail.filetype==2}">移送行政拘留案卷</c:if>
             <c:if test="${fileDetail.filetype==3}">涉嫌犯罪移送案卷</c:if>
        </td>
      </tr>
      <tr>
        <td>决定文书号/移送编号</td>
        <td style="text-align:left;">${fileDetail.filecode}</td>
      </tr>
      <tr>
        <td>案件调查情况材料简版说明</td>
        <td style="text-align:left;">${personalFile.filesimpledesc }</td>
      </tr>
      <tr>
        <td>案件调查情况材料详版说明</td>
        <td style="text-align:left;">${personalFile.filedetiaildesc }</td>
      </tr>
      <tr>
        <td width="200">案卷类型</td>
        <td style="text-align:left;">
        <c:if test="${fileDetail2.filetype==0}">行政处罚案卷</c:if>
         <c:if test="${fileDetail2.filetype==1}">按日计罚案卷</c:if>
          <c:if test="${fileDetail2.filetype==6}">查封扣押案卷</c:if>
           <c:if test="${fileDetail2.filetype==7}">限产停产案卷</c:if>
            <c:if test="${fileDetail2.filetype==2}">移送行政拘留案卷</c:if>
             <c:if test="${fileDetail2.filetype==3}">涉嫌犯罪移送案卷</c:if>
        </td>
      </tr>
      <tr>
        <td>决定文书号/移送编号</td>
        <td style="text-align:left;">${fileDetail2.filecode}</td>
      </tr>
      <tr>
        <td>案件调查情况材料简版说明</td>
        <td style="text-align:left;">${personalFile2.filesimpledesc }</td>
      </tr>
      <tr>
        <td>案件调查情况材料详版说明</td>
        <td style="text-align:left;">${personalFile2.filedetiaildesc }</td>
      </tr>
      <tr>
        <td>姓名</td>
        <td style="text-align:left;">${electionPersonalDetail.name}</td>
      </tr>
      <tr>
        <td>性别</td>
        <td style="text-align:left;">${electionPersonalDetail.sex}</td>
      </tr>
      <tr>
        <td>职务</td>
        <td style="text-align:left;">${electionPersonalDetail.job}</td>
      </tr>
      
      <tr>
        <td>所在单位名称</td>
        <td style="text-align:left;">${electionPersonalDetail.unitname}</td>
      </tr>
      <tr>
        <td>环保工作年限</td>
        <td style="text-align:left;">${electionPersonalDetail.workyearnum}</td>
      </tr>
      
      <tr>
        <td>身份证号码</td>
        <td style="text-align:left;">${electionPersonalDetail.cardid}</td>
      </tr>
      
      <tr>
        <td>学历</td>
        <td style="text-align:left;">${electionPersonalDetail.educationname}</td>
      </tr>
      <tr>
        <td>编制性质</td>
        <td style="text-align:left;">${electionPersonalDetail.orgpropname}</td>
      </tr>
      
      <tr>
        <td>联系电话</td>
        <td style="text-align:left;">${electionPersonalDetail.phone}</td>
      </tr>
      <tr>
         <td>参与调查处理案件</td>
         <td style="text-align:left;">
         	<span id="handlcasecodes"></span>
         </td>
       </tr>
       <tr>
         <td>参与调查处理案件数量</td>
         <td style="text-align:left;">${electionPersonalDetail.handlcasenum}</td>
       </tr>
      <tr>
        <td>个人事迹材料</td>
        <td style="text-align:left;">
        <a style="color: blue;cursor:pointer;text-decoration:none;"    onclick="downloadFile('${electionPersonalDetail.personalmaterialurl}',' ${electionPersonalDetail.personalmaterialname}')"> ${electionPersonalDetail.personalmaterialname}</a> 
        </td>
      </tr>
      <tr>
        <td>个人廉洁执法相关证明材料</td>
        <td style="text-align:left;">
        <a style="color: blue;cursor:pointer;text-decoration:none;"    onclick="downloadFile('${electionPersonalDetail.perhonestfileurl}','${electionPersonalDetail.perhonestfilename}')"> ${electionPersonalDetail.perhonestfilename}</a> 
      </tr>
    </tbody>
  </table>
</div>
</div>
</body>
</html>
