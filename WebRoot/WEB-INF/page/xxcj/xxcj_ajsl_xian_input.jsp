<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
<script type="text/javascript">
$(document).ready(function(){
    
	
});
</script>
</head>
<body>
<div class="center_weizhi">当前位置：信息采集 - 参选先进集体信息  - 重点排污单位数量上报</div>
<div class="center">
<form id="ajslForm">
<div class="center_list">
  <table align="center" class="table_input">
    <tbody>
    <c:if test="${xxcj_AJSL.id!=''&&xxcj_AJSL.id!=null}">
		      <tr>
		        <td width="250">&nbsp;</td>
		        <td><span style="color:#295A5F; font-size:18px; float:left; font-weight:bold;">${xxcj_AJSL.areaname }</span></td>
		      </tr>
		      <tr>
		        <td>行政区域内重点排污单位数量</td>
		        <td>
		        	<div class="form-group">
		        		<input type="text" class="form-control" id="areaguokongqynum" name="areaguokongqynum" value="${xxcj_AJSL.areaguokongqynum }" placeholder="请输入行政区域内重点排污单位数量">
		        		<input type="hidden" name="id" value="${xxcj_AJSL.id }" />
		        		<input type="hidden" name="areacode" value="${xxcj_AJSL.areacode }" />
		        	</div>
		        </td>
		      </tr>
      </c:if>
      <c:if test="${user.sysStatus=='1'&& user.reportState=='0'}">
	      <tr>
	        <td align="center">&nbsp;
	        <input type="hidden" name="id" value="${xxcj_AJSL.id }"/>
	        </td>
	        <td style="text-align:left;"><a href="#">
	          <button type="button"  id="xxcj_AJSL_Butt" class="btn btn-primary" name="signup" value="Sign up" style="font-size:16px;width:150px; margin-top:5px;">信息保存</button>
	          </a>
	        </td>
	      </tr>
      </c:if>
    </tbody>
  </table>
    </div>
    </form>
</div>
<script language="JavaScript">


	//表单校验
	$(document).ready(function() {
	$('#ajslForm').formValidation({
		message: 'This value is not valid',
	    icon: {
	    	valid: 'glyphicon glyphicon-ok',
	        invalid: 'glyphicon glyphicon-remove',
	        validating: 'glyphicon glyphicon-refresh'
	    },
	    autoFocus: true,
	    fields: {
	        "areaguokongqynum": {
	        	validators: {
	            	notEmpty: {
	                	message: '请填写该行政区域内重点排污单位数量.'
	                },
	                stringLength: {
	                	max: 9,
	                	message: '该项不能超过9个字符.'
	                },
	                regexp: {
	                    regexp: /^[0-9]+$/,
	                    message: '该项只能填写整数.'
	                }
	            }
	        }
	        }
	    });
	});
	
	
	
	//表单提交
	$(document).ready(function(){
		$("#xxcj_AJSL_Butt").click(function(){
			var validate = false;
			$("#ajslForm").data('formValidation').validate();
			validate = $("#ajslForm").data('formValidation').isValid();
			if(validate){
				var options = {
					url:WEBPATH+'/xxcj/saveXxcjSL_xj.do',
					type:'post',
					success:function(data){
					if(data.result=="error"){
						swal("保存失败!", data.message, "error");
		                return false;
		             }else if(data.result=="success"){
		             	swal({
						    	title: "保存成功!",
						        type: "success",
						        closeOnConfirm: true,
						        confirmButtonText: "确定",
						        confirmButtonColor: "#A7D5EA"
					    	}, function() {
						    	business.addMainContentParserHtml('xxcj/xxcjAjsl.do','');
						});
		               return false;
		             }else if(data.code=="007"){
		            	 swal(data.message, "", "info");
		            	 return false;
		             }	
					},
					error:function(){
						swal("服务异常,保存失败!", "", "error");
					}
				};
				$("#ajslForm").ajaxSubmit(options);
			}else if(validate==null){
				//表单未填写
				$("#ajslForm").data('formValidation').validate();
	       
	        }
		});
	});
	
	
	
$(document).ready(function() {
	$(".topnav").accordion({
		accordion:false,
		speed: 500,
		closedSign: '[+]',
		openedSign: '[-]'
	});
});

</script>
</body>
</html>
