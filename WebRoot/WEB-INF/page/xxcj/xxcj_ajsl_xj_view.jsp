<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
<script type="text/javascript">

</script>
</head>
<body>
<div class="center_weizhi">当前位置：信息采集 - 参选先进集体信息  - 重点排污单位数量查看</div>
<div class="center">
<form id="ajslForm">
<div class="center_list">
  <table align="center" class="table_input">
	<tbody>
		<tr>
        	<td width="250">&nbsp;</td>
        	<td><span style="color:#295A5F; font-size:18px; float:left; font-weight:bold;">${xxcj_AJSL.areaname }</span></td>
      	</tr>
      	<tr>
			<td style="width:224px;">行政区域内重点排污单位数量</td>
			<td style="text-align:left;">${xxcj_AJSL.areaguokongqynum }</td>
	  	</tr>
	  	<c:if test="${user.areaCode!='11000000'&&user.areaCode!='12000000'&&user.areaCode!='31000000'&&user.areaCode!='55000000'&&user.areaCode!='66000000' }">
	  		<c:if test="${area.arealevel!='3'}">
			  	<tr>
		    		<td>公开方式</td>
		    		<td style="text-align:left;"><c:if test="${area.opentype=='1' }">网站公开</c:if>
							<c:if test="${area.opentype=='2' }">其他公开方式</c:if>
		    		</td>
				</tr>
				<tr id="shi1${i.index}" <c:choose><c:when test="${area.opentype=='1' }"></c:when><c:otherwise>style="display:none;"</c:otherwise></c:choose>>
					<td>公开网站地址</td>
					<td style="text-align:left;">${area.openurl}</td>
				</tr>
				<tr id="shi2${i.index}" <c:choose><c:when test="${area.opentype=='2' }"></c:when><c:otherwise>style="display:none;"</c:otherwise></c:choose>>
					<td>其他公开方式描述</td>
					<td style="text-align:left;">${area.opendesc}</td>
				</tr>
			</c:if>
		</c:if> 
      
    </tbody>
  </table>
    </div>
    </form>
</div>
<script language="JavaScript">
	
$(document).ready(function() {
	$(".topnav").accordion({
		accordion:false,
		speed: 500,
		closedSign: '[+]',
		openedSign: '[-]'
	});
});

</script>
</body>
</html>
