<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
    <!-- <style type="text/css">
    div.input-group-btn div.btn.btn-primary.btn-file {
        background-color:#d9534f;
    }
    </style> -->
    <style>

        .is-required {
            content: '* ';
            color: red;
        }
    </style>
    <script type="text/javascript">

        function butChange(index) {
            var butVal = $("#reButton" + index).attr("value");
            var butStyle = $("#reButton" + index).attr("style");
            if (butStyle != "" && typeof(butStyle) != "undefined") {
                $("#reButton" + index).attr("style", "");
                $("#uploadTr" + index).attr("style", "display:none");
                $("#wsc" + index).attr("style", "");
            } else {
                if (butVal == "重新上传") {
                    $("#reButton" + index).attr("value", "返回");
                    $("#reButton" + index).attr('class',"back");
                    $("#uploadTr" + index).removeClass("style");
                    $("#uploadTr" + index).attr("style", "");
                    $("#wsc" + index).attr("style", "display:none");
                } else if (butVal == "返回") {
                    $("#reButton" + index).attr("value", "重新上传");
                    $("#reButton" + index).removeClass("back");
                    $("#uploadTr" + index).attr("style", "display:none");
                    $("#wsc" + index).attr("style", "");
                } else if (butVal == "删除") {
                    $("#reButton" + index).attr("value", "重新上传");
                    $("#uploadTr" + index).attr("style", "display:none");
                    $("#wsc" + index).attr("style", "");
                }
            }
        }


        //省级大练兵组织情况 Excel文件
        $(document).ready(function () {
            $("#jiliFile").fileinput({
                uploadUrl: WEBPATH + '/pdffileupload.do', // you must set a valid URL here else you will get an error
                language: 'zh',
                browseClass: 'btn btn-danger',//按钮样式
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize: 1,
                maxFileSize: 53500,
                enctype: 'multipart/form-data',
                dropZoneTitle: "可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany: '选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading: '文件预览',
                msgInvalidFileExtension: '不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected: '请选择文件',
                msgValidationError: '文件类型不正确或文件过大',
                initialPreviewFileType: 'els',
                browseLabel: "选择文件",
                removeLabel: '删除',
                removeTitle: '删除文件',
                uploadLabel: '上传',
                uploadTitle: '上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview: false,
                autoReplace: true,
                slugCallback: function (filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function (event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true); //信息保存按钮 隐藏

            }).on('fileuploaded', function (event, data, previewId, index) {//文件上传完成执行操作
                console.log("-------", data.response.url, data.response.fileRealName)
                $("#jiliUrl").val(data.response.url);
                $("#jiliUrlname").val(data.response.fileRealName);
                // $("#wsc1").text(data.response.fileRealName);
                $("#wsc1").addClass("wscbj1");
                $("#wsc1").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled', false);

                butChange('1');
            })

            $("#reButton1").click(function () {
                butChange('1');
                $("#wsc1").addClass("wscbj1");
            });


            //知识竞赛支撑材料
            $("#jwInform").fileinput({
                uploadUrl: WEBPATH + '/pdffileupload.do', // you must set a valid URL here else you will get an error
                // //allowedFileExtensions: ['rar'],
                language: 'zh',
                browseClass: 'btn btn-danger',//按钮样式
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize: 1,
                maxFileSize: 53500,
                enctype: 'multipart/form-data',
                dropZoneTitle: "可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany: '选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading: '文件预览',
                msgInvalidFileExtension: '不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected: '请选择文件',
                msgValidationError: '文件类型不正确或文件过大',
                initialPreviewFileType: 'rar',
                browseLabel: "选择文件",
                removeLabel: '删除',
                removeTitle: '删除文件',
                uploadLabel: '上传',
                uploadTitle: '上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview: false,
                autoReplace: true,
                slugCallback: function (filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function (event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function (event, data, previewId, index) {//文件上传完成执行操作

                $("#jwInformUrl").val(data.response.url);
                $("#jwInformUrlname").val(data.response.fileRealName);
                // $("#wsc2").text(data.response.fileRealName);
                $("#wsc2").addClass("wscbj");
                $("#wsc2").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled', false);

                butChange('2');
            })

            $("#reButton2").click(function () {
                butChange('2');
                $("#wsc2").addClass("wscbj");
            });

            //开展大练兵宣传支撑材料
            $("#jwWebFile").fileinput({
                uploadUrl: WEBPATH + '/pdffileupload.do', // you must set a valid URL here else you will get an error
                // //allowedFileExtensions: ['rar'],
                language: 'zh',
                browseClass: 'btn btn-danger',//按钮样式
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize: 1,
                maxFileSize: 53500,
                enctype: 'multipart/form-data',
                dropZoneTitle: "可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany: '选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading: '文件预览',
                msgInvalidFileExtension: '不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected: '请选择文件',
                msgValidationError: '文件类型不正确或文件过大',
                initialPreviewFileType: 'rar',
                browseLabel: "选择文件",
                removeLabel: '删除',
                removeTitle: '删除文件',
                uploadLabel: '上传',
                uploadTitle: '上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview: false,
                autoReplace: true,
                slugCallback: function (filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function (event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function (event, data, previewId, index) {//文件上传完成执行操作

                    $("#jwWebUrl").val(data.response.url);
                    $("#jwWebUrlname").val(data.response.fileRealName);
                    // $("#wsc3").text(data.response.fileRealName);
                    $("#wsc3").addClass("wscbj");
                    $("#wsc3").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                    $("#xxcj_AJSL_Butt").prop('disabled', false);
                    butChange('3');
                }
            )

            $("#reButton3").click(function () {
                butChange('3');
                $("#wsc3").addClass("wscbj");

            });


            //评查方案
            $("#pingchaFile").fileinput({
                uploadUrl: WEBPATH + '/pdffileupload.do', // you must set a valid URL here else you will get an error
                // //allowedFileExtensions: ['rar'],
                language: 'zh',
                browseClass: 'btn btn-danger',//按钮样式
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize: 1,
                maxFileSize: 53500,
                enctype: 'multipart/form-data',
                dropZoneTitle: "可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany: '选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading: '文件预览',
                msgInvalidFileExtension: '不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected: '请选择文件',
                msgValidationError: '文件类型不正确或文件过大',
                initialPreviewFileType: 'rar',
                browseLabel: "选择文件",
                removeLabel: '删除',
                removeTitle: '删除文件',
                uploadLabel: '上传',
                uploadTitle: '上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview: false,
                autoReplace: true,
                slugCallback: function (filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function (event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function (event, data, previewId, index) {//文件上传完成执行操作

                $("#pingchaUrl").val(data.response.url);
                $("#pingchaUrlname").val(data.response.fileRealName);
                // $("#wsc4").text(data.response.fileRealName);
                $("#wsc4").addClass("wscbj");
                $("#wsc4").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled', false);

                butChange('4');
            })
            $("#reButton4").click(function () {
                butChange('4');
                $("#wsc4").addClass("wscbj")
            });

            //评查工作总结或通报
            $("#worksummaryFile").fileinput({
                uploadUrl: WEBPATH + '/pdffileupload.do', // you must set a valid URL here else you will get an error
                // //allowedFileExtensions: ['rar'],
                language: 'zh',
                browseClass: 'btn btn-danger',//按钮样式
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize: 1,
                maxFileSize: 53500,
                enctype: 'multipart/form-data',
                dropZoneTitle: "可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany: '选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading: '文件预览',
                msgInvalidFileExtension: '不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected: '请选择文件',
                msgValidationError: '文件类型不正确或文件过大',
                initialPreviewFileType: 'rar',
                browseLabel: "选择文件",
                removeLabel: '删除',
                removeTitle: '删除文件',
                uploadLabel: '上传',
                uploadTitle: '上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview: false,
                autoReplace: true,
                slugCallback: function (filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function (event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function (event, data, previewId, index) {//文件上传完成执行操作

                $("#worksummaryUrl").val(data.response.url);
                $("#worksummaryUrlname").val(data.response.fileRealName);
                // $("#wsc5").text(data.response.fileRealName);
                $("#wsc5").addClass("wscbj")
                $("#wsc5").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled', false);

                butChange('5');
            })

            $("#reButton5").click(function () {
                butChange('5');
                $("#wsc5").addClass("wscbj")
            });

            //案件办理情况说明
            $("#carryOutFile").fileinput({

                uploadUrl: WEBPATH + '/pdffileupload.do', // you must set a valid URL here else you will get an error
                //allowedFileExtensions: ['rar'],
                language: 'zh',
                browseClass: 'btn btn-danger',//按钮样式
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize: 1,
                maxFileSize: 53500,
                enctype: 'multipart/form-data',
                dropZoneTitle: "可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany: '选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading: '文件预览',
                msgInvalidFileExtension: '不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected: '请选择文件',
                msgValidationError: '文件类型不正确或文件过大',
                initialPreviewFileType: 'rar',
                browseLabel: "选择文件",
                removeLabel: '删除',
                removeTitle: '删除文件',
                uploadLabel: '上传',
                uploadTitle: '上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview: false,
                autoReplace: true,
                slugCallback: function (filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function (event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function (event, data, previewId, index) {//文件上传完成执行操作

                $("#caseEndUrl").val(data.response.url);
                $("#caseEndUrlname").val(data.response.fileRealName);
                // $("#wsc7").text(data.response.fileRealName);
                $("#wsc7").addClass("wscbj")
                $("#wsc7").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled', false);
                var fileName = $("#caseEndUrlname").val();
                if(fileName) {
                    $(".iscarryOut").hide()
                    }else {
                    $(".iscarryOut").show()
                }
                butChange('7');
            })

            $("#reButton7").click(function () {
                butChange('7');
                $("#wsc7").addClass("wscbj")
            });


            //查办大案要案支撑材料
            $("#caseEndFile").fileinput({
                uploadUrl: WEBPATH + '/pdffileupload.do', // you must set a valid URL here else you will get an error
                //allowedFileExtensions: ['rar'],
                language: 'zh',
                browseClass: 'btn btn-danger',//按钮样式
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize: 1,
                maxFileSize: 53500,
                enctype: 'multipart/form-data',
                dropZoneTitle: "可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany: '选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading: '文件预览',
                msgInvalidFileExtension: '不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected: '请选择文件',
                msgValidationError: '文件类型不正确或文件过大',
                initialPreviewFileType: 'rar',
                browseLabel: "选择文件",
                removeLabel: '删除',
                removeTitle: '删除文件',
                uploadLabel: '上传',
                uploadTitle: '上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview: false,
                autoReplace: true,
                slugCallback: function (filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function (event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function (event, data, previewId, index) {//文件上传完成执行操作

                $("#caseEndsupUrl").val(data.response.url);
                $("#caseEndsupUrlname").val(data.response.fileRealName);
                // $("#wsc8").text(data.response.fileRealName);
                $("#wsc8").addClass("wscbj")
                $("#wsc8").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled', false);

                butChange('8');
            })

            $("#reButton8").click(function () {
                butChange('8');
                $("#wsc8").addClass("wscbj")
            });


            //2021年是否出台
            $("#dwReportFile").fileinput({
                uploadUrl: WEBPATH + '/pdffileupload.do', // you must set a valid URL here else you will get an error
                //allowedFileExtensions: ['rar'],
                language: 'zh',
                browseClass: 'btn btn-danger',//按钮样式
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize: 1,
                maxFileSize: 53500,
                enctype: 'multipart/form-data',
                dropZoneTitle: "可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany: '选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading: '文件预览',
                msgInvalidFileExtension: '不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected: '请选择文件',
                msgValidationError: '文件类型不正确或文件过大',
                initialPreviewFileType: 'rar',
                browseLabel: "选择文件",
                removeLabel: '删除',
                removeTitle: '删除文件',
                uploadLabel: '上传',
                uploadTitle: '上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview: false,
                autoReplace: true,
                slugCallback: function (filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function (event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function (event, data, previewId, index) {//文件上传完成执行操作

                $("#dwReportUrl").val(data.response.url);
                $("#dwReportUrlname").val(data.response.fileRealName);
                // $("#wsc9").text(data.response.fileRealName);
                $("#wsc9").addClass("wscbj")
                $("#wsc9").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled', false);

                butChange('9');
            })

            $("#reButton9").click(function () {
                butChange('9');
                $("#wsc9").addClass("wscbj")
            });

            // 建设标准文件
            $("#dwJianFile").fileinput({
                uploadUrl: WEBPATH + '/pdffileupload.do', // you must set a valid URL here else you will get an error
                //allowedFileExtensions: ['rar'],
                language: 'zh',
                browseClass: 'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize: 1,
                maxFileSize: 53500,
                enctype: 'multipart/form-data',
                dropZoneTitle: "可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany: '选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading: '文件预览',
                msgInvalidFileExtension: '不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected: '请选择文件',
                msgValidationError: '文件类型不正确或文件过大',
                initialPreviewFileType: 'rar',
                browseLabel: "选择文件",
                removeLabel: '删除',
                removeTitle: '删除文件',
                uploadLabel: '上传',
                uploadTitle: '上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview: false,
                autoReplace: true,
                slugCallback: function (filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function (event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function (event, data, previewId, index) {//文件上传完成执行操作

                $("#dwJianUrl").val(data.response.url);
                $("#dwJianUrlname").val(data.response.fileRealName);
                // $("#wsc10").text(data.response.fileRealName);
                $("#wsc10").addClass("wscbj")
                $("#wsc10").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled', false);

                butChange('10');
            })
            $("#reButton10").click(function () {
                butChange('10');
                $("#wsc10").addClass("wscbj")
            });

            //稽查计划
            $("#lawCheckplanFile").fileinput({
                uploadUrl: WEBPATH + '/pdffileupload.do', // you must set a valid URL here else you will get an error
                //allowedFileExtensions: ['rar'],
                language: 'zh',
                browseClass: 'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize: 1,
                maxFileSize: 53500,
                enctype: 'multipart/form-data',
                dropZoneTitle: "可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany: '选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading: '文件预览',
                msgInvalidFileExtension: '不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected: '请选择文件',
                msgValidationError: '文件类型不正确或文件过大',
                initialPreviewFileType: 'rar',
                browseLabel: "选择文件",
                removeLabel: '删除',
                removeTitle: '删除文件',
                uploadLabel: '上传',
                uploadTitle: '上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview: false,
                autoReplace: true,
                slugCallback: function (filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filebatchselected', function (event, files) {//文件自动上传
                //console.log(event);
                //console.log(files);
                //$(this).fileinput("upload");
            }).on('filepreupload', function (event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function (event, data, previewId, index) {//文件上传完成执行操作

                $("#lawCheckplanUrl").val(data.response.url);
                $("#lawCheckplanUrlname").val(data.response.fileRealName);
                // $("#wsc11").text(data.response.fileRealName);
                $("#wsc11").addClass("wscbj")
                $("#wsc11").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled', false);

                butChange('11');
            })
            $("#reButton11").click(function () {
                butChange('11');
                $("#wsc11").addClass("wscbj")
            });

            //稽查工作报告
            $("#caseinfoFile").fileinput({
                uploadUrl: WEBPATH + '/pdffileupload.do', // you must set a valid URL here else you will get an error
                //allowedFileExtensions: ['xls', 'xlsx'],
                language: 'zh',
                browseClass: 'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize: 1,
                maxFileSize: 53500,
                enctype: 'multipart/form-data',
                dropZoneTitle: "可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany: '选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading: '文件预览',
                msgInvalidFileExtension: '不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected: '请选择文件',
                msgValidationError: '文件类型不正确或文件过大',
                initialPreviewFileType: 'xls',
                browseLabel: "选择文件",
                removeLabel: '删除',
                removeTitle: '删除文件',
                uploadLabel: '上传',
                uploadTitle: '上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview: false,
                autoReplace: true,
                slugCallback: function (filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filebatchselected', function (event, files) {//文件自动上传
                //console.log(event);
                //console.log(files);
                //$(this).fileinput("upload");
            }).on('filepreupload', function (event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function (event, data, previewId, index) {//文件上传完成执行操作

                $("#caseinfoUrl").val(data.response.url);
                $("#caseinfoUrlname").val(data.response.fileRealName);
                // $("#wsc6").text(data.response.fileRealName);
                $("#wsc6").addClass("wscbj")
                $("#wsc6").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled', false);

                butChange('6');
            })
            $("#reButton6").click(function () {
                butChange('6');
                $("#wsc6").addClass("wscbj")
            });


            //稽查工作报告
            $("#lawCheckreportFile").fileinput({
                uploadUrl: WEBPATH + '/pdffileupload.do', // you must set a valid URL here else you will get an error
                //allowedFileExtensions: ['rar'],
                language: 'zh',
                browseClass: 'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize: 1,
                maxFileSize: 53500,
                enctype: 'multipart/form-data',
                dropZoneTitle: "可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany: '选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading: '文件预览',
                msgInvalidFileExtension: '不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected: '请选择文件',
                msgValidationError: '文件类型不正确或文件过大',
                initialPreviewFileType: 'rar',
                browseLabel: "选择文件",
                removeLabel: '删除',
                removeTitle: '删除文件',
                uploadLabel: '上传',
                uploadTitle: '上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview: false,
                autoReplace: true,
                slugCallback: function (filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function (event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function (event, data, previewId, index) {//文件上传完成执行操作

                $("#lawCheckreportUrl").val(data.response.url);
                $("#lawCheckreportUrlname").val(data.response.fileRealName);
                // $("#wsc12").text(data.response.fileRealName);
                $("#wsc12").addClass("wscbj")
                $("#wsc12").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled', false);

                butChange('12');
            })
            $("#reButton12").click(function () {
                butChange('12');
                $("#wsc12").addClass("wscbj")
            });

            // 统一证件、着装支撑材料
            $("#dwSupFile").fileinput({
                uploadUrl: WEBPATH + '/pdffileupload.do', // you must set a valid URL here else you will get an error
                //allowedFileExtensions: ['rar'],
                language: 'zh',
                browseClass: 'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize: 1,
                maxFileSize: 53500,
                enctype: 'multipart/form-data',
                dropZoneTitle: "可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany: '选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading: '文件预览',
                msgInvalidFileExtension: '不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected: '请选择文件',
                msgValidationError: '文件类型不正确或文件过大',
                initialPreviewFileType: 'rar',
                browseLabel: "选择文件",
                removeLabel: '删除',
                removeTitle: '删除文件',
                uploadLabel: '上传',
                uploadTitle: '上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview: false,
                autoReplace: true,
                slugCallback: function (filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function (event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function (event, data, previewId, index) {//文件上传完成执行操作

                $("#dwSupUrl").val(data.response.url);
                $("#dwSupUrlname").val(data.response.fileRealName);
                // $("#wsc13").text(data.response.fileRealName);
                $("#wsc13").addClass("wscbj")
                $("#wsc13").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled', false);
                butChange('13');
            })
            $("#reButton13").click(function () {
                butChange('13');
                $("#wsc13").addClass("wscbj")
            });


            //区域交叉检查制度
            $("#dwCrossFile").fileinput({
                uploadUrl: WEBPATH + '/pdffileupload.do', // you must set a valid URL here else you will get an error
                //allowedFileExtensions: ['rar'],
                language: 'zh',
                browseClass: 'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize: 1,
                maxFileSize: 53500,
                enctype: 'multipart/form-data',
                dropZoneTitle: "可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany: '选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading: '文件预览',
                msgInvalidFileExtension: '不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected: '请选择文件',
                msgValidationError: '文件类型不正确或文件过大',
                initialPreviewFileType: 'rar',
                browseLabel: "选择文件",
                removeLabel: '删除',
                removeTitle: '删除文件',
                uploadLabel: '上传',
                uploadTitle: '上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview: false,
                autoReplace: true,
                slugCallback: function (filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function (event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function (event, data, previewId, index) {//文件上传完成执行操作

                $("#dwCrossUrl").val(data.response.url);
                $("#dwCrossUrlname").val(data.response.fileRealName);
                // $("#wsc14").text(data.response.fileRealName);
                $("#wsc14").addClass("wscbj")
                $("#wsc14").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled', false);

                butChange('14');
            })

            $("#reButton14").click(function () {
                butChange('14');
                $("#wsc14").addClass("wscbj")
            });

            //专案查办制度
            $("#dwTaskFile").fileinput({
                uploadUrl: WEBPATH + '/pdffileupload.do', // you must set a valid URL here else you will get an error
                //allowedFileExtensions: ['rar'],
                language: 'zh',
                browseClass: 'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize: 1,
                maxFileSize: 53500,
                enctype: 'multipart/form-data',
                dropZoneTitle: "可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany: '选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading: '文件预览',
                msgInvalidFileExtension: '不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected: '请选择文件',
                msgValidationError: '文件类型不正确活文件过大',
                initialPreviewFileType: 'rar',
                browseLabel: "选择文件",
                removeLabel: '删除',
                removeTitle: '删除文件',
                uploadLabel: '上传',
                uploadTitle: '上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview: false,
                autoReplace: true,
                slugCallback: function (filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function (event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function (event, data, previewId, index) {//文件上传完成执行操作

                $("#dwTaskUrl").val(data.response.url);
                $("#dwTaskUrlname").val(data.response.fileRealName);
                // $("#wsc15").text(data.response.fileRealName);
                $("#wsc15").addClass("wscbj")
                $("#wsc15").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled', false);

                butChange('15');
            })

            $("#reButton15").click(function () {
                butChange('15');
                $("#wsc15").addClass("wscbj")
            });

            //部门协调联动机制
            $("#dwLianFile").fileinput({
                uploadUrl: WEBPATH + '/pdffileupload.do', // you must set a valid URL here else you will get an error
                //allowedFileExtensions: ['rar'],
                language: 'zh',
                browseClass: 'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize: 1,
                maxFileSize: 53500,
                enctype: 'multipart/form-data',
                dropZoneTitle: "可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany: '选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading: '文件预览',
                msgInvalidFileExtension: '不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected: '请选择文件',
                msgValidationError: '文件类型不正确或文件过大',
                initialPreviewFileType: 'rar',
                browseLabel: "选择文件",
                removeLabel: '删除',
                removeTitle: '删除文件',
                uploadLabel: '上传',
                uploadTitle: '上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview: false,
                autoReplace: true,
                slugCallback: function (filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function (event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function (event, data, previewId, index) {//文件上传完成执行操作

                $("#dwLianUrl").val(data.response.url);
                $("#dwLianUrlname").val(data.response.fileRealName);
                // $("#wsc16").text(data.response.fileRealName);
                $("#wsc16").addClass("wscbj")
                $("#wsc16").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled', false);

                butChange('16');
            })
            $("#reButton16").click(function () {
                butChange('16');
                $("#wsc16").addClass("wscbj")
            });

            //第三方辅助执法制度
            $("#dwFulawFile").fileinput({
                uploadUrl: WEBPATH + '/pdffileupload.do', // you must set a valid URL here else you will get an error
                //allowedFileExtensions: ['rar'],
                language: 'zh',
                browseClass: 'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize: 1,
                maxFileSize: 51200,
                enctype: 'multipart/form-data',
                dropZoneTitle: "可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany: '选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading: '文件预览',
                msgInvalidFileExtension: '不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected: '请选择文件',
                msgValidationError: '文件类型不正确或文件过大',
                initialPreviewFileType: 'rar',
                browseLabel: "选择文件",
                removeLabel: '删除',
                removeTitle: '删除文件',
                uploadLabel: '上传',
                uploadTitle: '上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview: false,
                autoReplace: true,
                slugCallback: function (filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function (event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function (event, data, previewId, index) {//文件上传完成执行操作

                $("#dwFulawUrl").val(data.response.url);
                $("#dwFulawUrlname").val(data.response.fileRealName);
                // $("#wsc17").text(data.response.fileRealName);
                $("#wsc17").addClass("wscbj")
                $("#wsc17").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled', false);

                butChange('17');
            })
            $("#reButton17").click(function () {
                butChange('17');
                $("#wsc17").addClass("wscbj")
            });

            //执法普法制度
            $("#dwLawFile").fileinput({
                uploadUrl: WEBPATH + '/pdffileupload.do', // you must set a valid URL here else you will get an error
                //allowedFileExtensions: ['rar'],
                language: 'zh',
                browseClass: 'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize: 1,
                maxFileSize: 51200,
                enctype: 'multipart/form-data',
                dropZoneTitle: "可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany: '选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading: '文件预览',
                msgInvalidFileExtension: '不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected: '请选择文件',
                msgValidationError: '文件类型不正确或文件过大',
                initialPreviewFileType: 'rar',
                browseLabel: "选择文件",
                removeLabel: '删除',
                removeTitle: '删除文件',
                uploadLabel: '上传',
                uploadTitle: '上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview: false,
                autoReplace: true,
                slugCallback: function (filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function (event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function (event, data, previewId, index) {//文件上传完成执行操作

                $("#dwLawUrl").val(data.response.url);
                $("#dwLawUrlname").val(data.response.fileRealName);
                // $("#wsc18").text(data.response.fileRealName);
                $("#wsc18").addClass("wscbj")
                $("#wsc18").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled', false);

                butChange('18');
            })
            $("#reButton18").click(function () {
                butChange('18');
                $("#wsc18").addClass("wscbj")
            });

            //轻微违法免罚制度
            $("#dwTinylawFile").fileinput({
                uploadUrl: WEBPATH + '/pdffileupload.do', // you must set a valid URL here else you will get an error
                //allowedFileExtensions: ['rar'],
                language: 'zh',
                browseClass: 'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize: 1,
                maxFileSize: 51200,
                enctype: 'multipart/form-data',
                dropZoneTitle: "可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany: '选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading: '文件预览',
                msgInvalidFileExtension: '不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected: '请选择文件',
                msgValidationError: '文件类型不正确或文件过大',
                initialPreviewFileType: 'rar',
                browseLabel: "选择文件",
                removeLabel: '删除',
                removeTitle: '删除文件',
                uploadLabel: '上传',
                uploadTitle: '上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview: false,
                autoReplace: true,
                slugCallback: function (filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function (event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function (event, data, previewId, index) {//文件上传完成执行操作

                $("#dwTinylawUrl").val(data.response.url);
                $("#dwTinylawUrlname").val(data.response.fileRealName);
                // $("#wsc19").text(data.response.fileRealName);
                $("#wsc19").addClass("wscbj")
                $("#wsc19").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled', false);

                butChange('19');
            })
            $("#reButton19").click(function () {
                butChange('19');
                $("#wsc19").addClass("wscbj")
            });

            //监督执法正面清单制度支撑材料
            $("#systemSupFile").fileinput({
                uploadUrl: WEBPATH + '/pdffileupload.do', // you must set a valid URL here else you will get an error
                //allowedFileExtensions: ['rar'],
                language: 'zh',
                browseClass: 'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize: 1,
                maxFileSize: 51200,
                enctype: 'multipart/form-data',
                dropZoneTitle: "可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany: '选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading: '文件预览',
                msgInvalidFileExtension: '不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected: '请选择文件',
                msgValidationError: '文件类型不正确或文件过大',
                initialPreviewFileType: 'rar',
                browseLabel: "选择文件",
                removeLabel: '删除',
                removeTitle: '删除文件',
                uploadLabel: '上传',
                uploadTitle: '上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview: false,
                autoReplace: true,
                slugCallback: function (filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function (event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function (event, data, previewId, index) {//文件上传完成执行操作

                $("#systemSupUrl").val(data.response.url);
                $("#systemSupUrlname").val(data.response.fileRealName);
                // $("#wsc20").text(data.response.fileRealName);
                $("#wsc20").addClass("wscbj")
                $("#wsc20").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled', false);

                butChange('20');
            })
            $("#reButton20").click(function () {
                butChange('20');
                $("#wsc20").addClass("wscbj")
            });

            //监督执法正面清单制度支撑材料
            $("#positivelistSupFile").fileinput({
                uploadUrl: WEBPATH + '/pdffileupload.do', // you must set a valid URL here else you will get an error
                //allowedFileExtensions: ['rar'],
                language: 'zh',
                browseClass: 'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize: 1,
                maxFileSize: 51200,
                enctype: 'multipart/form-data',
                dropZoneTitle: "可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany: '选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading: '文件预览',
                msgInvalidFileExtension: '不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected: '请选择文件',
                msgValidationError: '文件类型不正确或文件过大',
                initialPreviewFileType: 'rar',
                browseLabel: "选择文件",
                removeLabel: '删除',
                removeTitle: '删除文件',
                uploadLabel: '上传',
                uploadTitle: '上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview: false,
                autoReplace: true,
                slugCallback: function (filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function (event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function (event, data, previewId, index) {//文件上传完成执行操作

                $("#positivelistSupUrl").val(data.response.url);
                $("#positivelistSupUrlname").val(data.response.fileRealName);
                // $("#wsc21").text(data.response.fileRealName);
                $("#wsc21").addClass("wscbj")
                $("#wsc21").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled', false);

                butChange('21');
            })
            $("#reButton21").click(function () {
                butChange('21');
                $("#wsc21").addClass("wscbj")
            });

            //双随机、一公开”监管工作执行情况支撑材料：
            $("#randomsupFile").fileinput({
                uploadUrl: WEBPATH + '/pdffileupload.do', // you must set a valid URL here else you will get an error
                //allowedFileExtensions: ['xls', 'xlsx'],
                language: 'zh',
                browseClass: 'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize: 1,
                maxFileSize: 51200,
                enctype: 'multipart/form-data',
                dropZoneTitle: "可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany: '选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading: '文件预览',
                msgInvalidFileExtension: '不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected: '请选择文件',
                msgValidationError: '文件类型不正确或文件过大',
                initialPreviewFileType: 'els',
                browseLabel: "选择文件",
                removeLabel: '删除',
                removeTitle: '删除文件',
                uploadLabel: '上传',
                uploadTitle: '上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview: false,
                autoReplace: true,
                slugCallback: function (filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function (event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function (event, data, previewId, index) {//文件上传完成执行操作

                $("#randomsupUrl").val(data.response.url);
                $("#randomsupUrlname").val(data.response.fileRealName);
                // $("#wsc22").text(data.response.fileRealName);
                $("#wsc22").addClass("wscbj")
                $("#wsc22").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled', false);

                butChange('22');
            })
            $("#reButton22").click(function () {
                butChange('22');
                $("#wsc22").addClass("wscbj")
            });

            //发布典型案例的通知或者公开的链接
            $("#typicalcaseFile").fileinput({
                uploadUrl: WEBPATH + '/pdffileupload.do', // you must set a valid URL here else you will get an error
                //allowedFileExtensions: ['rar'],
                language: 'zh',
                browseClass: 'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize: 1,
                maxFileSize: 51200,
                enctype: 'multipart/form-data',
                dropZoneTitle: "可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany: '选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading: '文件预览',
                msgInvalidFileExtension: '不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected: '请选择文件',
                msgValidationError: '文件类型不正确或文件过大',
                initialPreviewFileType: 'rar',
                browseLabel: "选择文件",
                removeLabel: '删除',
                removeTitle: '删除文件',
                uploadLabel: '上传',
                uploadTitle: '上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview: false,
                autoReplace: true,
                slugCallback: function (filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function (event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function (event, data, previewId, index) {//文件上传完成执行操作

                $("#typicalcaseUrl").val(data.response.url);
                $("#typicalcaseUrlname").val(data.response.fileRealName);
                // $("#wsc23").text(data.response.fileRealName);
                $("#wsc23").addClass("wscbj")
                $("#wsc23").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled', false);

                butChange('23');
            })
            $("#reButton23").click(function () {
                butChange('23');
                $("#wsc23").addClass("wscbj")
            });

            //发布典型案例的通知或者公开的链接
            $("#guideFile").fileinput({
                uploadUrl: WEBPATH + '/pdffileupload.do', // you must set a valid URL here else you will get an error
                //allowedFileExtensions: ['rar'],
                language: 'zh',
                browseClass: 'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize: 1,
                maxFileSize: 51200,
                enctype: 'multipart/form-data',
                dropZoneTitle: "可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany: '选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading: '文件预览',
                msgInvalidFileExtension: '不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected: '请选择文件',
                msgValidationError: '文件类型不正确或文件过大',
                initialPreviewFileType: 'rar',
                browseLabel: "选择文件",
                removeLabel: '删除',
                removeTitle: '删除文件',
                uploadLabel: '上传',
                uploadTitle: '上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview: false,
                autoReplace: true,
                slugCallback: function (filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function (event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function (event, data, previewId, index) {//文件上传完成执行操作

                $("#guideUrl").val(data.response.url);
                $("#guideUrlname").val(data.response.fileRealName);
                // $("#wsc24").text(data.response.fileRealName);
                $("#wsc24").addClass("wscbj")
                $("#wsc24").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled', false);

                butChange('24');
            })
            $("#reButton24").click(function () {
                butChange('24');
                $("#wsc24").addClass("wscbj")
            });

            //已建立人才库的相关文件
            $("#peopoolFile").fileinput({
                uploadUrl: WEBPATH + '/pdffileupload.do', // you must set a valid URL here else you will get an error
                //allowedFileExtensions: ['pdf'],
                language: 'zh',
                browseClass: 'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize: 1,
                maxFileSize: 31800,
                enctype: 'multipart/form-data',
                dropZoneTitle: "可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany: '选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading: '文件预览',
                msgInvalidFileExtension: '不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected: '请选择文件',
                msgValidationError: '文件类型不正确或文件过大',
                initialPreviewFileType: 'pdf',
                browseLabel: "选择文件",
                removeLabel: '删除',
                removeTitle: '删除文件',
                uploadLabel: '上传',
                uploadTitle: '上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview: false,
                autoReplace: true,
                slugCallback: function (filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function (event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function (event, data, previewId, index) {//文件上传完成执行操作

                $("#peopoolUrl").val(data.response.url);
                $("#peopoolUrlname").val(data.response.fileRealName);
                // $("#wsc25").text(data.response.fileRealName);
                $("#wsc25").addClass("wscbj")
                $("#wsc25").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled', false);

                butChange('25');
            })
            $("#reButton25").click(function () {
                butChange('25');
                $("#wsc25").addClass("wscbj")
            });

            //监督帮扶典型案例 开展培训（岗位培训除外）的动态或宣传情况
            $("#trainFile").fileinput({
                uploadUrl: WEBPATH + '/pdffileupload.do', // you must set a valid URL here else you will get an error
                //allowedFileExtensions: ['xls', 'xlsx'],
                language: 'zh',
                browseClass: 'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize: 1,
                maxFileSize: 51200,
                enctype: 'multipart/form-data',
                dropZoneTitle: "可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany: '选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading: '文件预览',
                msgInvalidFileExtension: '不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected: '请选择文件',
                msgValidationError: '文件类型不正确或文件过大',
                initialPreviewFileType: 'els',
                browseLabel: "选择文件",
                removeLabel: '删除',
                removeTitle: '删除文件',
                uploadLabel: '上传',
                uploadTitle: '上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview: false,
                autoReplace: true,
                slugCallback: function (filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function (event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function (event, data, previewId, index) {//文件上传完成执行操作

                $("#trainUrl").val(data.response.url);
                $("#trainUrlname").val(data.response.fileRealName);
                // $("#wsc26").text(data.response.fileRealName);
                $("#wsc26").addClass("wscbj")
                $("#wsc26").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled', false);

                butChange('26');
            })
            $("#reButton26").click(function () {
                butChange('26');
                $("#wsc26").addClass("wscbj")
            });

            //加强排污许可执法监管的政策文件
            $("#licenseFileUpload").fileinput({
                uploadUrl: WEBPATH + '/pdffileupload.do', // you must set a valid URL here else you will get an error
                //allowedFileExtensions: ['rar'],
                language: 'zh',
                browseClass: 'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize: 1,
                maxFileSize: 51200,
                enctype: 'multipart/form-data',
                dropZoneTitle: "可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany: '选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading: '文件预览',
                msgInvalidFileExtension: '不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected: '请选择文件',
                msgValidationError: '文件类型不正确或文件过大',
                initialPreviewFileType: 'rar',
                browseLabel: "选择文件",
                removeLabel: '删除',
                removeTitle: '删除文件',
                uploadLabel: '上传',
                uploadTitle: '上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview: false,
                autoReplace: true,
                slugCallback: function (filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function (event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function (event, data, previewId, index) {//文件上传完成执行操作

                $("#licenselawUrl").val(data.response.url);
                $("#licenselawUrlname").val(data.response.fileRealName);
                // $("#wsc27").text(data.response.fileRealName);
                $("#wsc27").addClass("wscbj")
                $("#wsc27").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled', false);

                butChange('27');
            })
            $("#reButton27").click(function () {
                butChange('27');
                $("#wsc27").addClass("wscbj")
            });


            //行政处罚自由裁量规定
            $("#discretionFileUpload").fileinput({
                uploadUrl: WEBPATH + '/pdffileupload.do', // you must set a valid URL here else you will get an error
                //allowedFileExtensions: ['xls', 'xlsx'],
                language: 'zh',
                browseClass: 'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize: 1,
                maxFileSize: 51200,
                enctype: 'multipart/form-data',
                dropZoneTitle: "可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany: '选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading: '文件预览',
                msgInvalidFileExtension: '不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected: '请选择文件',
                msgValidationError: '文件类型不正确或文件过大',
                initialPreviewFileType: 'els',
                browseLabel: "选择文件",
                removeLabel: '删除',
                removeTitle: '删除文件',
                uploadLabel: '上传',
                uploadTitle: '上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview: false,
                autoReplace: true,
                slugCallback: function (filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function (event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function (event, data, previewId, index) {//文件上传完成执行操作

                $("#discretionUrl").val(data.response.url);
                $("#discretionUrlname").val(data.response.fileRealName);
                // $("#wsc28").text(data.response.fileRealName);
                $("#wsc28").addClass("wscbj")
                $("#wsc28").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled', false);

                butChange('28');
            })
            $("#reButton28").click(function () {
                butChange('28');
                $("#wsc28").addClass("wscbj")
            });

            //典型案例公开的网页截图或者通报文件
            $("#licensesFileUpload").fileinput({
                uploadUrl: WEBPATH + '/pdffileupload.do', // you must set a valid URL here else you will get an error
                //allowedFileExtensions: ['rar'],
                language: 'zh',
                browseClass: 'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize: 1,
                maxFileSize: 51200,
                enctype: 'multipart/form-data',
                dropZoneTitle: "可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany: '选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading: '文件预览',
                msgInvalidFileExtension: '不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected: '请选择文件',
                msgValidationError: '文件类型不正确或文件过大',
                initialPreviewFileType: 'rar',
                browseLabel: "选择文件",
                removeLabel: '删除',
                removeTitle: '删除文件',
                uploadLabel: '上传',
                uploadTitle: '上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview: false,
                autoReplace: true,
                slugCallback: function (filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function (event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function (event, data, previewId, index) {//文件上传完成执行操作

                $("#licensespecialUrl").val(data.response.url);
                $("#licensespecialUrlname").val(data.response.fileRealName);
                // $("#wsc29").text(data.response.fileRealName);
                $("#wsc29").addClass("wscbj")
                $("#wsc29").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled', false);

                butChange('29');
            })
            $("#reButton29").click(function () {
                butChange('29');
                $("#wsc29").addClass("wscbj")
            });

            //省本级制定的排污许可清单式执法通知文件或行动方案或现场指导等佐证材料
            $("#licenselisFileUpload").fileinput({
                uploadUrl: WEBPATH + '/pdffileupload.do', // you must set a valid URL here else you will get an error
                //allowedFileExtensions: ['rar'],
                language: 'zh',
                browseClass: 'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize: 1,
                maxFileSize: 51200,
                enctype: 'multipart/form-data',
                dropZoneTitle: "可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany: '选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading: '文件预览',
                msgInvalidFileExtension: '不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected: '请选择文件',
                msgValidationError: '文件类型不正确或文件过大',
                initialPreviewFileType: 'rar',
                browseLabel: "选择文件",
                removeLabel: '删除',
                removeTitle: '删除文件',
                uploadLabel: '上传',
                uploadTitle: '上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview: false,
                autoReplace: true,
                slugCallback: function (filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function (event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function (event, data, previewId, index) {//文件上传完成执行操作

                $("#licenselistUrl").val(data.response.url);
                $("#licenselistUrlname").val(data.response.fileRealName);
                // $("#wsc30").text(data.response.fileRealName);
                $("#wsc30").addClass("wscbj")
                $("#wsc30").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled', false);

                butChange('30');
            })
            $("#reButton30").click(function () {
                butChange('30');
                $("#wsc30").addClass("wscbj")
            });


        });
    </script>
    <style>
        h1 {
            font-size: 24px;
            font-weight: 700;
        }

        h2 {
            margin-left: 44px;
            font-size: 20px;
            font-weight: 600;
            color: #25678E;
        }

        h3 {
            font-size: 18px;
        }

        .shifou {
            /*margin-left: 283px;*/
            margin-top: 20px
        }

        .zhiding {
            font-size: 14px;
            color: #333333;
            font-weight: 400
        }

        .shangchuan {
            margin-left: 60px; margin-top: 20px;display: flex;width: calc(100% - 200px)
        }

        /*.file-input-new {*/
        /*    width: 50%;*/
        /*}*/
        #reButton1,#reButton2,#reButton3,#reButton4,#reButton5,#reButton6,#reButton7,#reButton8,#reButton9,#reButton10,#reButton11,#reButton12,#reButton13,#reButton14,#reButton15,#reButton16
        ,#reButton17,#reButton18,#reButton19,#reButton20,#reButton21,#reButton22,#reButton23,#reButton24,#reButton25,#reButton26,#reButton27,#reButton28,#reButton29,#reButton30{
            height: 32px;
            background: #0093C3;
            border-color: #0093C3;
            margin-left: 5px;
            border-radius: 3px;
            width: 80px;
            color:#ffffff;
            font-size: 14px;
        }
        /*.btn btn-danger btn-xs{*/
        /*height: 25px;*/
        /*}*/
        select,input{
            background: #FFFFFF;
            border: 1px solid #DCDFE6;
            border-radius: 5px;
            width: 380px;
            height: 39px;
            padding-left:5px;
        }
        .sp{text-align: right;}
        .wscbj{width: 380px;position: relative;left: 358px;border-radius: 3px;margin-top: 14px;padding-left:10px}
        .wscbj1{width: 380px;position: relative;left: 346px;border-radius: 3px;margin-top: 14px;padding-left:10px}
        .form-control{width: 288px;margin-left: 4px;}
        .input-group .form-control{width: 65%;border-radius:3px}
        .input-group-btn .btn{height:39px;line-height: 26px}
        .file-caption-main{width:81%}
        .file-input .form-control{height:39px;width:378px}
        .back{height:39px!important;color:#ffffff}
        label {
            display: inline-block;
            width: 30%;
            margin-bottom: 5px;
            margin-left: 5px;
            text-align: right;
        }
    </style>
</head>
<body>
<div class="center_weizhi">当前位置：信息采集 - 省内评比信息 - 行政区基础数据</div>
<div class="center form-group" style="width: calc(100% - 210px)">
    <form id="ajslForm" style="width: 100%">
        <div class="center_list" style="width: 100%">
            <input type="hidden" class="form-control" name="areacodePro" id="areacodePro" value="${areaUser.areaCode}"/>
            <input type="hidden" class="form-control" name="areatypePro" id="areatypePro"
                   value="${areaUser.arealevel}"/>
            <%--<input type="hidden" class="form-control" name="xxxx" id="xxxx"--%>
                   <%--value="${provinceSelectionUnit.publicityway}"/>--%>

            <div style="background-color: #fff; padding: 24px;">
                <p style=""><label style="font-size: 14px;font-weight: 400;color: #333333;position:relative;bottom: 1px ">行政区：</label>
                    <span style="margin-left: 10px;font-size: 24px; font-weight: 400;color: #333333;margin-bottom: 20px">${areaUser.userName}</span>
                </p>
                <p style="margin-top:20px;">
                    <label style=" float:left; font-size: 14px; color: #333333; font-weight: 400"><span
                            style="color: red;size: 60px">*</span>机构名称（全称）：</label>
                    <input style="width: 380px;margin-top:-5px"
                           <c:if test="${provinceReportUser.reportstate ==1 || sysInitConfig.code !='1'}">disabled="disabled" </c:if>
                           type="text" class="form-control" id="agencyName" name="agencyName"
                           value="${provinceSelectionUnit.agencyName}"
                           placeholder="请输入机构名称（全称）例如：河北省生态环境厅" width="100px;"/>
                    <span style="display:inline-block;color: #FB3301;margin-left: 7px; margin-top:3px;font-size: 12px;">例:河北省生态环境厅</span>
                </p>
                <p style="margin-left:600px; color: red; font-size: 14px;padding-top:10px">重要提醒：</p>
                <p style="margin-left:600px; color: red; font-size: 14px;">1.请完成本页面全部信息填报，否则本单位将无法参加大练兵集体评选。</p>
                <p style="margin-left:600px; color: red; font-size: 14px;">2.请认真填写下列信息，并仔细上传支撑材料，若未填写相关信息或支撑材料不能证明信息的真实性，将不予给分。</p>
                <p style="margin-left:600px; color: red; font-size: 14px;">3.每份支撑材料大小不超过50M，附件格式支持PNG、png、doc、pdf、jpg、JPG、xlsx、xls、csv、mp4、mp3、rar、zip。</p>
                <p style="margin-left:600px; color: red; font-size: 14px;">4.每份支撑材料需根据提示规范命名：【xx省-具体文件标题】,例：【河北省-激励措施相关文件】。</p>
                </p></div>

            </div>


            <%--111111111111111111111111111111111111111111111111111111111111111111--%>

            <div style="margin-top: 20px;">
                <div style="background-color: #fff;  padding: 2px 30px 26px 21px; line-height: 30px; width: 100%">
                    <h1 style="color: #31688F;font-size: 16px;font-weight: 400">一、省级大练兵组织情况</h1>
                    <%--<hr width="100%"/>--%>
                    <%--Excel文件/'--%>
                    <%--                    <div style="margin-left:286px; display: flex;line-height: 39px;">--%>
                    <%--                        <span style="font-size: 14px; color: #333333; font-weight: 400 ;">Excel文件：</span>--%>
                    <%--                        <span id="uploadTr1"--%>
                    <%--                              <c:if test="${provinceSelectionUnit.proLevlExcelName!='' && provinceSelectionUnit.proLevlExcelName!=null }">style='display:none'</c:if>--%>
                    <%--                              >--%>
                    <%--&lt;%&ndash;                            style="width: calc(100% - 224px)"&ndash;%&gt;--%>
                    <%--                                <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
                    <%--									<div style="display: flex;width: 100%">--%>
                    <%--										<input class="upchuan" type="file" id="proLevlFiles" value="文件上传">--%>
                    <%--										<input type="hidden" class="form-control" name="proLevlExcelName"--%>
                    <%--                                               id="proLevlFile_Filename"--%>
                    <%--                                               value="${provinceSelectionUnit.proLevlExcelName}">--%>
                    <%--										<input type="hidden" class="form-control" name="proLevlExcelUrl"--%>
                    <%--                                               id="proLevlFile_Fileurl"--%>
                    <%--                                               value="${provinceSelectionUnit.proLevlExcelUrl}">--%>

                    <%--									</div>--%>
                    <%--                                </c:if>--%>
                    <%--                        </span>--%>
                    <%--                        <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
                    <%--                            <input id="reButton1" style="margin-top:2px; "--%>
                    <%--                                   <c:if test="${provinceSelectionUnit.proLevlExcelName=='' || provinceSelectionUnit.proLevlExcelName==null }">style="display:none;"</c:if>--%>
                    <%--                                   class="btn btn-danger btn-xs" type="button" value="重新上传"/>--%>
                    <%--                        </c:if>--%>
                    <%--                    </div>--%>
                    <%--                    <p>--%>
                    <%--                        <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}"><span style="color: deepskyblue;margin-left:359px;margin-top:5px">--%>
                    <%--											<a style="color: blue;margin-left: 7px " href="#"--%>
                    <%--                                               onclick="down(1)">(下载模板）</a>--%>
                    <%--										</span></c:if>--%>
                    <%--                    </p>--%>
                    <%--                    <div id="wsc1" style="margin-left:356px;width: 380px;margin-top: 10px;border-radius: 3px;">--%>
                    <%--                         <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">--%>
                    <%--                            <c:choose>--%>
                    <%--                                <c:when test="${provinceSelectionUnit.proLevlExcelName!='' && provinceSelectionUnit.proLevlExcelName!=null }">--%>
                    <%--                                    <span style=" margin-right: 8px; padding-top: 4px;">--%>
                    <%--                                      <a href="javascript:void(0)"--%>
                    <%--                                         onclick="downloadFile('${provinceSelectionUnit.proLevlExcelUrl}','${provinceSelectionUnit.proLevlExcelName}')"> ${provinceSelectionUnit.proLevlExcelName} </a>--%>
                    <%--                                    </span>--%>
                    <%--                                </c:when>--%>
                    <%--                            </c:choose>--%>
                    <%--				        </span>--%>
                    <%--                    </div>--%>

                    <%--                    <p style="color: #FB3301;margin-left: 365px;font-size: 12px;line-height: 20px;">1.请认真填写下列信息，并仔细上传支撑材料，填写的信息未在支撑材料中体现的，将不作为给分点--%>
                    <%--                    </p>--%>
                    <%--                    <p style="color: #FB3301;margin-left: 365px;width: 560px;font-size: 12px;line-height: 20px;">2.每份支撑材料大小不超过50M，要求rar格式；存放支撑材料的文件夹（压缩包）以【省份-二级标题】--%>
                    <%--                        命名，如知识竞赛支撑材料应存放在【xx省-知识竞赛】文件夹（压缩包）中</p>--%>
                    <div style="background-color: #fff; width: 100%">
                        <h1 style="font-size: 14px;color: #31688F;font-weight:400;height: 33px;line-height:33px;background: #F8F8F8;">（一）激励措施</h1>
                        <div style="margin-top: 20px">
                            <label style="font-size: 14px; color: #333333; font-weight: 400"><span
                                    style="color: red;size: 60px;">*</span>是否采取激励措施：</label>
                            <select style="width:380px;margin-right:5px;" name="isJili"
                                    id="isJili">
                                <option value="">--请选择--</option>
                                <option value="1"
                                      <c:if test="${provinceSelectionUnit.isJili eq '1'}">selected</c:if>>是
                                </option>
                                <option value="0"
                                       <c:if test="${provinceSelectionUnit.isJili eq '0'}">selected</c:if>>否
                                </option>
                            </select>
                        </div>
                        <div style="margin-top: 20px">
                            <label style="font-size: 14px; color: #333333; font-weight: 400"><span
                                    style="color: red;size: 60px;" class="isShow">*</span>激励形式：</label>
                            <select style="width:380px;margin-right:5px;" name="jiliName"
                                    id="jiliName">
                                <option value="">--请选择--</option>
                                <option value="1"
                                        <c:if test="${provinceSelectionUnit.jiliName eq '1'}">selected</c:if>>
                                    记功嘉奖
                                </option>
                                <option value="2"
                                        <c:if test="${provinceSelectionUnit.jiliName eq '2'}">selected</c:if>>
                                    联合其他部门奖励
                                </option>
                                <option value="3"
                                        <c:if test="${provinceSelectionUnit.jiliName eq '3'}">selected</c:if>>
                                    省级生态环境部门表扬
                                </option>
                            </select>
                        </div>
                        <div style="margin-left:58px;margin-top: 20px;display: flex; width: calc(100% - 200px)">
                            <label style="font-size: 14px; color: #333333; font-weight: 400"><span
                                    style="color: red;size: 60px;" class="isShow">*</span>激励奖励相关文件：</label>
                            <span id="uploadTr1"
                                  <c:if test="${provinceSelectionUnit.jiliUrlname!='' && provinceSelectionUnit.jiliUrlname!=null }">style='display:none'</c:if>
                                  style=" ">
<%--                                width: calc(100% - 224px)--%>
								<c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                    <input type="file" id="jiliFile" value="文件上传">
                                    <input type="hidden" class="form-control" name="jiliUrlname"
                                           id="jiliUrlname"
                                           value="${provinceSelectionUnit.jiliUrlname}">
                                    <input type="hidden" class="form-control" name="jiliUrl"
                                           id="jiliUrl"
                                           value="${provinceSelectionUnit.jiliUrl}">
                                </c:if>
						   </span>
                            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                <input id="reButton1"
                                       <c:if test="${provinceSelectionUnit.jiliUrlname=='' || provinceSelectionUnit.jiliUrlname==null }">style='display:none'</c:if>
                                       class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                            </c:if>
                        </div>
                        <div id="wsc1" style="margin-left:357px;width: 380px;margin-top: 14px;border-radius: 3px;">
                            <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">
								<c:choose>
                                    <c:when test="${provinceSelectionUnit.jiliUrlname!='' && provinceSelectionUnit.jiliUrlname!=null }">
											<span style=" margin-right: 8px; padding-top: 4px;">
<%--                                                    ${provinceSelectionUnit.jiliUrlname}--%>
                                                <a href="javascript:void(0)"
                                                   onclick="downloadFile('${provinceSelectionUnit.jiliUrl}','${provinceSelectionUnit.jiliUrl}')">
                                                        ${provinceSelectionUnit.jiliUrlname}
                                                </a>
                                            </span>
                                    </c:when>
                                </c:choose>
							</span>
                        </div>
                    </div>

                    <div style="background-color: #fff; width: 100%;">
                        <h1 style="font-size: 14px;color: #31688F;font-weight:400;height: 33px;line-height:33px;background: #F8F8F8;">（二）竞赛比武</h1>
                        <div style="margin-top: 20px">
                            <label class='sp' style="font-size: 14px; color: #333333; font-weight: 400"><span
                                    style="color: red;size: 60px">*</span>是否开展竞赛比武：</label>
                            <select id="isJingwu" name="isJingwu">
                                <option value="">--请选择--</option>
                                <option value="1" <c:if test="${provinceSelectionUnit.isJingwu eq '1'}">selected</c:if>>
                                    是
                                </option>
                                <option value="0" <c:if test="${provinceSelectionUnit.isJingwu eq '0'}">selected</c:if>>
                                    否
                                </option>
                            </select>
                        </div>
                        <div style="margin-top: 20px">
                            <label class='sp' style="font-size: 14px; color: #333333; font-weight: 400"><span
                                    style="color: red;size: 60px" class="isJingwuShow">*</span>是否联合其他部门开展：</label>
                            <select id="isLianhe" name="isLianhe">
                                <option value="">--请选择--</option>
                                <option value="1"
                                        <c:if test="${provinceSelectionUnit.isLianhe eq '1'}">selected</c:if>>是
                                </option>
                                <option value="0"
                                        <c:if test="${provinceSelectionUnit.isLianhe eq '0'}">selected</c:if>>否
                                </option>
                            </select>
                            <%--                            <span style="font-size: 12px; color: red">（是否涵盖本行政区域内所有生态环境执法机构）</span>--%>
                        </div>
                        <%--                        <div style="margin-left: 200px;margin-top: 20px">--%>
                        <%--                            <span class='sp' style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
                        <%--                                    style="color: red;size: 60px">*</span>机构领导是否带头参与：</span>--%>
                        <%--                            <select id="jgld" name="isLeader"--%>
                        <%--                                    required>--%>
                        <%--                                <option value="">--请选择--</option>--%>
                        <%--                                <option value="1" <c:if test="${provinceSelectionUnit.isLeader eq '1'}">selected</c:if>>--%>
                        <%--                                    是--%>
                        <%--                                </option>--%>
                        <%--                                <option value="0" <c:if test="${provinceSelectionUnit.isLeader eq '0'}">selected</c:if>>--%>
                        <%--                                    否--%>
                        <%--                                </option>--%>
                        <%--                            </select>--%>
                        <%--                            <div id="jtldzw" style="display: none;margin-top: 20px;margin-left: 56px">--%>
                        <%--                                <span class='sp' style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
                        <%--                                        style="color: red;size: 60px">*</span>具体领导职务：</span>--%>
                        <%--                                <input name="leaderDuty" id="leaderDuty" value="${provinceSelectionUnit.leaderDuty}">--%>
                        <%--                            </div>--%>
                        <%--                        </div>--%>
                        <%--                        <div style="margin-left: 297px;margin-top: 20px">--%>
                        <%--                            <span class='sp' style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
                        <%--                                    style="color: red;size: 60px">*</span>参与率：</span>--%>
                        <%--                            <input id="parRate" style="width: 380px;" name="parRate"--%>
                        <%--                                   value="${provinceSelectionUnit.parRate}" onkeyup="clearNoNum(this)">%--%>
                        <%--                        </div>--%>
                        <div style="margin-left: 46px;margin-top: 20px; display: flex; width: calc(100% - 150px)">
                            <label class='sp' style="font-size: 14px; color: #333333; font-weight: 400"><span
                                    style="color: red;size: 60px" class="isJingwuShow">*</span>竞赛比武通知：</label>

                            <span id="uploadTr2"
                                  <c:if test="${provinceSelectionUnit.jwInformUrlname!='' && provinceSelectionUnit.jwInformUrlname!=null }">style='display:none'
                                  </c:if>style="">
                                <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                    <input type="file" id="jwInform" value="文件上传">
                                    <input type="hidden" class="form-control" name="jwInformUrlname" id="jwInformUrlname"
                                           value="${provinceSelectionUnit.jwInformUrlname}">
                                    <input type="hidden" class="form-control" name="jwInformUrl" id="jwInformUrl"
                                           value="${provinceSelectionUnit.jwInformUrl}">
                                </c:if>
                            </span>
                            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                <input id="reButton2"
                                       <c:if test="${provinceSelectionUnit.jwInformUrlname=='' || provinceSelectionUnit.jwInformUrlname==null }">style='display:none'</c:if>
                                       class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                            </c:if>
                        </div>
                        <div id="wsc2" style="margin-left:355px;width: 380px;border-radius: 3px;">
                            <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">
                                <c:choose>
                                    <c:when test="${provinceSelectionUnit.jwInformUrlname!='' && provinceSelectionUnit.jwInformUrlname!=null }">
                                    <span style=" margin-right: 8px; padding-top: 4px;">
                                        <a href="javascript:void(0)"
                                           onclick="downloadFile('${provinceSelectionUnit.jwInformUrl}','${provinceSelectionUnit.jwInformUrlname}')">
                                                ${provinceSelectionUnit.jwInformUrlname}
                                        </a>
                                    </span>
                                    </c:when>
                                </c:choose>
                            </span>
                        </div>

                        <div style="margin-left: 45px;margin-top: 20px; display: flex; width: calc(100% - 150px)">
                            <label class='sp' style="font-size: 14px; color: #333333; font-weight: 400"><span
                                    style="color: red;size: 60px" class="isJingwuShow">*</span>竞赛比武活动网页截图：</label>

                            <span id="uploadTr3"
                                  <c:if test="${provinceSelectionUnit.jwWebUrlname!='' && provinceSelectionUnit.jwWebUrlname!=null }">style='display:none'
                                  </c:if>style="">
                                <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                    <input type="file" id="jwWebFile" value="文件上传">
                                    <input type="hidden" class="form-control" name="jwWebUrlname" id="jwWebUrlname"
                                           value="${provinceSelectionUnit.jwWebUrlname}">
                                    <input type="hidden" class="form-control" name="jwWebUrl" id="jwWebUrl"
                                           value="${provinceSelectionUnit.jwWebUrl}">
                                </c:if>
                            </span>
                            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                <input id="reButton3"
                                       <c:if test="${provinceSelectionUnit.jwWebUrlname=='' || provinceSelectionUnit.jwWebUrlname==null }">style='display:none'</c:if>
                                       class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                            </c:if>
                        </div>
                        <div id="wsc3" style="margin-left:355px;width: 380px;border-radius: 3px;">
                            <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">
                                <c:choose>
                                    <c:when test="${provinceSelectionUnit.jwWebUrlname!='' && provinceSelectionUnit.jwWebUrlname!=null }">
                                    <span style=" margin-right: 8px; padding-top: 4px;">
                                        <a href="javascript:void(0)"
                                           onclick="downloadFile('${provinceSelectionUnit.jwWebUrlname}','${provinceSelectionUnit.jwWebUrlname}')">
                                                ${provinceSelectionUnit.jwWebUrlname}
                                        </a>
                                    </span>
                                    </c:when>
                                </c:choose>
                            </span>
                        </div>
                    </div>
                </div>
            </div>


            <%--222222222222222222222222222222222222222222222222222--%>

            <div style="margin-top: 40px;line-height: 30px">
                <div style="background-color: #fff; padding: 2px 30px 26px 21px;width: 100%">
                    <h1 style="color: #25678E;font-size: 16px;font-weight: 400;color: #31688F;">二、日常监督执法工作</h1>
                    <%--                    <hr width="100%"/>--%>
                    <%--                    <div style="margin-left:286px;display: flex;line-height: 39px;">--%>
                    <%--                        <span style="font-size: 14px; color: #333333; font-weight: 400;padding-top:2px">Excel文件：</span>--%>
                    <%--                        <span id="uploadTr6"--%>
                    <%--                              <c:if test="${provinceSelectionUnit.troopsConAdminFileName!='' && provinceSelectionUnit.troopsConAdminFileName!=null }">style='display:none'</c:if>--%>
                    <%--                              style="">--%>
                    <%--&lt;%&ndash;                            width: calc(100% - 224px)&ndash;%&gt;--%>
                    <%--                                <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
                    <%--									<div style="display: flex">--%>
                    <%--										<input type="file" id="troopsConAdminFile" value="文件上传">--%>
                    <%--										<input type="hidden" class="form-control" name="troopsConAdminFileName"--%>
                    <%--                                               id="troopsConAdminFile_Filename"--%>
                    <%--                                               value="${provinceSelectionUnit.troopsConAdminFileName}">--%>
                    <%--										<input type="hidden" class="form-control" name="troopsConAdminFileUrl"--%>
                    <%--                                               id="troopsConAdminFile_Fileurl"--%>
                    <%--                                               value="${provinceSelectionUnit.troopsConAdminFileUrl}">--%>

                    <%--									</div>--%>

                    <%--                                </c:if>--%>
                    <%--					    </span>--%>
                    <%--                        <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
                    <%--                            <input id="reButton6"--%>
                    <%--                                   <c:if test="${provinceSelectionUnit.troopsConAdminFileName=='' || provinceSelectionUnit.troopsConAdminFileName==null }">style='display:none'</c:if>--%>
                    <%--                                   class="btn btn-danger btn-xs" style="margin-top:8px" type="button" value="重新上传"/>--%>
                    <%--                        </c:if>--%>

                    <%--                    </div>--%>
                    <%--                    <p style="color: deepskyblue; padding-top: 5px">--%>
                    <%--                        <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
                    <%--                            <a style="color: blue;margin-left: 360px;margin-top: 5px;" href="#" onclick="down(2)">（下载模板）</a></c:if>--%>
                    <%--                    </p>--%>
                    <%--                    <div id="wsc6" style="margin-left:357px;width: 380px;margin-top: 10px;border-radius: 3px;">--%>
                    <%--                        <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">--%>
                    <%--                            <c:choose>--%>
                    <%--                                <c:when test="${provinceSelectionUnit.troopsConAdminFileName!='' && provinceSelectionUnit.troopsConAdminFileName!=null }">--%>
                    <%--                                    <span style=" margin-right: 8px; padding-top: 4px;">--%>
                    <%--                                       <a href="javascript:void(0)"--%>
                    <%--                                          onclick="downloadFile('${provinceSelectionUnit.troopsConAdminFileUrl}','${provinceSelectionUnit.troopsConAdminFileName}')"> ${provinceSelectionUnit.troopsConAdminFileName} </a>--%>
                    <%--                                    </span>--%>
                    <%--                                </c:when>--%>
                    <%--                            </c:choose>--%>
                    <%--                        </span>--%>
                    <%--                    </div>--%>
                    <%--                    <p style="color: #FB3301;width: 526px;font-size:12px;line-height: 20px;font-weight: 400;margin-left: 367px">1.请认真填写下列信息，并仔细上传支撑材料，填写的信息未在支撑材料中体现的，将不作为给分点</p>--%>

                    <%--                    <p style="color: #FB3301;width: 526px;line-height: 20px;font-size:12px;font-weight: 400;margin-left: 367px">2.每份支撑材料大小不超过50M，要求rar格式；存放支撑材料的文件夹（压缩包）以【省份-二级标题】命名，如知识竞赛支撑材料应存放在【xx省-知识竞赛】文件夹（压缩包）中</p>--%>


                    <div style="background-color: #fff; width: 100%">
                        <h1 style="font-size: 14px;color: #31688F;font-weight:400;height: 33px;line-height:33px;background: #F8F8F8;">（一）案卷评查</h1>
                        <div style="margin-top: 20px">
                            <label style="font-size: 14px; color: #333333; font-weight: 400"><span
                                    style="color: red;size: 60px">*</span>是否组织开展案卷评查：</label>
                            <select style="width:380px;margin-right:5px;" name="isCasereview" id="isCasereview">
                                <option value="">--请选择--</option>
                                <option value="1"
                                        <c:if test="${provinceSelectionUnit.isCasereview eq '1'}">selected</c:if>>是
                                </option>
                                <option value="0"
                                        <c:if test="${provinceSelectionUnit.isCasereview eq '0'}">selected</c:if>>否
                                </option>
                            </select>
                        </div>
                        <div style="margin-top: 20px">
                            <label style="font-size: 14px; color: #333333; font-weight: 400"><span
                                    style="color: red;size: 60px" class="isCaseShow">*</span>是否达到要求数量：</label>
                            <select style="width:380px;margin-right:5px;" name="isMeetRequirenum" id="isMeetRequirenum">
                                <option value="">--请选择--</option>
                                <option value="1"
                                        <c:if test="${provinceSelectionUnit.isMeetRequirenum eq '1'}">selected</c:if>>是
                                </option>
                                <option value="0"
                                        <c:if test="${provinceSelectionUnit.isMeetRequirenum eq '0'}">selected</c:if>>否
                                </option>
                            </select>
                        </div>
                        <div style="margin-top: 20px">
                            <label style="font-size: 14px; color: #333333; font-weight: 400"><span
                                    style="color: red;size: 60px" class="isCaseShow" >*</span>是否形成评查工作总结或向下通报，反馈评查情况：</label>
                            <select style="width:380px;margin-right:5px;" name="isFeedback" id="isFeedback">
                                <option value="">--请选择--</option>
                                <option value="1"
                                        <c:if test="${provinceSelectionUnit.isFeedback eq '1'}">selected</c:if>>是
                                </option>
                                <option value="0"
                                        <c:if test="${provinceSelectionUnit.isFeedback eq '0'}">selected</c:if>>否
                                </option>
                            </select>
                        </div>
                        <div style="margin-top: 20px">
                            <label style="font-size: 14px; color: #333333; font-weight: 400"><span
                                    style="color: red;size: 60px" class="isCaseShow">*</span>是否形成案卷集或典型案例汇编：</label>
                            <select style="width:380px;margin-right:5px;" name="isCasecollection" id="isCasecollection">
                                <option value="">--请选择--</option>
                                <option value="1"
                                        <c:if test="${provinceSelectionUnit.isCasecollection eq '1'}">selected</c:if>>是
                                </option>
                                <option value="0"
                                        <c:if test="${provinceSelectionUnit.isCasecollection eq '0'}">selected</c:if>>否
                                </option>
                            </select>
                        </div>
                        <div style="margin-left:60px;margin-top: 20px;display: flex;width: calc(100% - 200px)">
                            <label style="font-size: 14px; color: #333333; font-weight: 400"><span
                                    style="color: red;size: 60px" class="isCaseShow">*</span>评查方案：</label>
                            <span id="uploadTr4"
                                  <c:if test="${provinceSelectionUnit.pingchaUrlname!='' && provinceSelectionUnit.pingchaUrlname!=null }">style='display:none'</c:if>
                            >
<%--                                style="width:calc(100%-224px)"--%>
								<c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                    <input type="file" id="pingchaFile" value="文件上传">
                                    <input type="hidden" class="form-control" name="pingchaUrlname"
                                           id="pingchaUrlname" value="${provinceSelectionUnit.pingchaUrlname}">
                                    <input type="hidden" class="form-control" name="pingchaUrl"
                                           id="pingchaUrl" value="${provinceSelectionUnit.pingchaUrl}">
                                </c:if>
						   </span>
                            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                <input id="reButton4"
                                       <c:if test="${provinceSelectionUnit.pingchaUrlname=='' || provinceSelectionUnit.pingchaUrlname==null }">style='display:none'</c:if>
                                       class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                            </c:if>
                        </div>
                        <div id="wsc4" style="margin-left:355px;width: 380px;">
                            <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">
                                <c:choose>
                                    <c:when test="${provinceSelectionUnit.pingchaUrlname!='' && provinceSelectionUnit.pingchaUrlname!=null }">
                                    <span style=" margin-right: 8px; padding-top: 4px;">
<%--                                                ${provinceSelectionUnit.carryOutFileName}--%>
                                         <a href="javascript:void(0)"
                                            onclick="downloadFile('${provinceSelectionUnit.pingchaUrl}','${provinceSelectionUnit.pingchaUrlname}')">
                                                 ${provinceSelectionUnit.pingchaUrlname}
                                         </a>
                                    </span>
                                    </c:when>
                                </c:choose>
							</span>
                        </div>
                        <%--                        评查工作总结或通报--%>
                        <div style="margin-left: 60px; margin-top: 20px;display: flex;width: calc(100% - 200px)">
                            <label style="font-size: 14px; color: #333333; font-weight: 400"><span
                                    style="color: red;size: 60px" class="isCaseShow">*</span>评查工作总结或通报：</label>
                            <span id="uploadTr5"
                                  <c:if test="${provinceSelectionUnit.worksummaryUrlname!='' && provinceSelectionUnit.worksummaryUrlname!=null }">style='display:none'</c:if>
                            >
<%--                                style="width:calc(100%-224px)"--%>
								<c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                    <input type="file" id="worksummaryFile" value="文件上传">
                                    <input type="hidden" class="form-control" name="worksummaryUrlname"
                                           id="worksummaryUrlname" value="${provinceSelectionUnit.worksummaryUrlname}">
                                    <input type="hidden" class="form-control" name="worksummaryUrl"
                                           id="worksummaryUrl" value="${provinceSelectionUnit.worksummaryUrl}">
                                </c:if>
						   </span>
                            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                <input id="reButton5"
                                       <c:if test="${provinceSelectionUnit.worksummaryUrlname=='' || provinceSelectionUnit.worksummaryUrlname==null }">style='display:none'</c:if>
                                       class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                            </c:if>
                        </div>
                        <div id="wsc5" style="margin-left:355px;width: 380px;">
                            <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">
                                <c:choose>
                                    <c:when test="${provinceSelectionUnit.worksummaryUrlname!='' && provinceSelectionUnit.worksummaryUrlname!=null }">
                                    <span style=" margin-right: 8px; padding-top: 4px;">
<%--                                                ${provinceSelectionUnit.carryOutFileName}--%>
                                         <a href="javascript:void(0)"
                                            onclick="downloadFile('${provinceSelectionUnit.worksummaryUrl}','${provinceSelectionUnit.worksummaryUrlname}')">
                                                 ${provinceSelectionUnit.worksummaryUrlname}
                                         </a>
                                    </span>
                                    </c:when>
                                </c:choose>
							</span>
                        </div>
                        <%--                        案卷集或典型案例汇编--%>
                        <div style="margin-left: 60px; margin-top: 20px;display: flex;width: calc(100% - 200px)">
                            <label style="font-size: 14px; color: #333333; font-weight: 400"><span
                                    style="color: red;size: 60px" class="isCaseShow">*</span>案卷集或典型案例汇编：</label>
                            <span id="uploadTr6"
                                  <c:if test="${provinceSelectionUnit.caseinfoUrlname!='' && provinceSelectionUnit.caseinfoUrlname!=null }">style='display:none'</c:if>
                            >
<%--                                style="width:calc(100%-224px)"--%>
								<c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                    <input type="file" id="caseinfoFile" value="文件上传">
                                    <input type="hidden" class="form-control" name="caseinfoUrlname"
                                           id="caseinfoUrlname" value="${provinceSelectionUnit.caseinfoUrlname}">
                                    <input type="hidden" class="form-control" name="caseinfoUrl"
                                           id="caseinfoUrl" value="${provinceSelectionUnit.caseinfoUrl}">
                                </c:if>
						   </span>
                            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                <input id="reButton6"
                                       <c:if test="${provinceSelectionUnit.caseinfoUrlname=='' || provinceSelectionUnit.caseinfoUrlname==null }">style='display:none'</c:if>
                                       class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                            </c:if>
                        </div>
                        <div id="wsc6" style="margin-left:355px;width: 380px;">
                            <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">
                                <c:choose>
                                    <c:when test="${provinceSelectionUnit.caseinfoUrlname!='' && provinceSelectionUnit.caseinfoUrlname!=null }">
                                    <span style=" margin-right: 8px; padding-top: 4px;">
<%--                                                ${provinceSelectionUnit.carryOutFileName}--%>
                                         <a href="javascript:void(0)"
                                            onclick="downloadFile('${provinceSelectionUnit.caseinfoUrl}','${provinceSelectionUnit.caseinfoUrlname}')">
                                                 ${provinceSelectionUnit.caseinfoUrlname}
                                         </a>
                                    </span>
                                    </c:when>
                                </c:choose>
							</span>
                        </div>
                    </div>

                    <div style="background-color: #fff;width: 100%">
                        <h1 style="font-size: 14px;color: #31688F;font-weight:400;height: 33px;line-height:33px;background: #F8F8F8;">（二）查办大案要案情况<span style="color: red; font-size: 14px">（不含被部采纳作为典型案例发布的案件）</span></h1>
                        <div style="margin-left:60px;margin-top: 20px;display: flex;width: calc(100% - 200px)">
                            <label style="font-size: 14px; color: #333333; font-weight: 400">案件办理情况说明：</label>
                            <span id="uploadTr7"
                                  <c:if test="${provinceSelectionUnit.caseEndUrlname!='' && provinceSelectionUnit.caseEndUrlname!=null }">style='display:none'</c:if>
                            >
<%--                                style="width:calc(100%-224px)"--%>
								<c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                    <input type="file" id="carryOutFile" value="文件上传">
                                    <input type="hidden" class="form-control" name="caseEndUrlname"
                                           id="caseEndUrlname" value="${provinceSelectionUnit.caseEndUrlname}">
                                    <input type="hidden" class="form-control" name="caseEndUrl"
                                           id="caseEndUrl" value="${provinceSelectionUnit.caseEndUrl}">
                                </c:if>
						   </span>
                            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                <input id="reButton7"
                                       <c:if test="${provinceSelectionUnit.caseEndUrlname=='' || provinceSelectionUnit.caseEndUrlname==null }">style='display:none'</c:if>
                                       class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                            </c:if>
                        </div>
                        <p style="color: #FB3301;width: 526px;font-size:12px;line-height: 20px;font-weight: 400;margin-left: 600px">体现各类大案要案查办数量、案情简介。</p>
                        <div id="wsc7" style="margin-left:355px;width: 380px;">
                            <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">
                                <c:choose>
                                    <c:when test="${provinceSelectionUnit.caseEndUrlname!='' && provinceSelectionUnit.caseEndUrlname!=null }">
                                    <span style=" margin-right: 8px; padding-top: 4px;">
<%--                                                ${provinceSelectionUnit.carryOutFileName}--%>
                                         <a href="javascript:void(0)"
                                            onclick="downloadFile('${provinceSelectionUnit.caseEndUrl}','${provinceSelectionUnit.caseEndUrlname}')">
                                                 ${provinceSelectionUnit.caseEndUrlname}
                                         </a>
                                    </span>
                                    </c:when>
                                </c:choose>
							</span>
                        </div>
                        <div style="margin-top: 20px">
                            <label style="font-size: 14px; color: #333333; font-weight: 400"><span
                                    style="color: red;size: 60px" class="iscarryOut">*</span>查办大案要案情况类型：</label>
                            <select style="width:380px;margin-right:5px;" name="caseEndType" id="caseEndType">
                                <option value="">--请选择--</option>
                                <option value="1"
                                        <c:if test="${provinceSelectionUnit.caseEndType eq '1'}">selected</c:if>>
                                    非现场手段查处排污许可违法案件
                                </option>
                                <option value="2"
                                        <c:if test="${provinceSelectionUnit.caseEndType eq '2'}">selected</c:if>>
                                    查处新法规、新领域、新划转职能违法行为案件的
                                </option>
                                <option value="3"
                                        <c:if test="${provinceSelectionUnit.caseEndType eq '3'}">selected</c:if>>
                                    办理跨流域、涉饮用水源地及部审批的建设项目违法案件或综合运用配套办法的案件
                                </option>
                                <option value="4"
                                        <c:if test="${provinceSelectionUnit.caseEndType eq '4'}">selected</c:if>>
                                    会同公安机关等部门查处的案情重大、影响恶劣、后果严重的环境犯罪案件
                                </option>
                            </select>
                            <select style="width:380px;margin-right:5px;" name="caseEndType2" id="caseEndType2" class="isCaseTypeShow">
                                <option value="">--请选择--</option>
                                <option value="0"
                                        <c:if test="${provinceSelectionUnit.caseEndType2 eq '0'}">selected</c:if>>温室气体排放
                                </option>
                                <option value="1"
                                        <c:if test="${provinceSelectionUnit.caseEndType2 eq '1'}">selected</c:if>>土壤污染
                                </option>
                                <option value="2"
                                        <c:if test="${provinceSelectionUnit.caseEndType2 eq '2'}">selected</c:if>>新化学物质
                                </option>
                                <option value="3"
                                        <c:if test="${provinceSelectionUnit.caseEndType2 eq '3'}">selected</c:if>>消耗臭氧层物质（ODS（）
                                </option>
                                <option value="4"
                                        <c:if test="${provinceSelectionUnit.caseEndType2 eq '4'}">selected</c:if>>新法、新领域、新划转职能违法行为案件
                                </option>
                            </select>
                            <select style="width:380px;margin-right:5px;" name="caseEndType3" class="isCaseTypeShow2">
                                <option value="">--请选择--</option>
                                <option value="0"
                                        <c:if test="${provinceSelectionUnit.caseEndType2 eq '0'}">selected</c:if>>涉饮用水源地
                                </option>
                                <option value="1"
                                        <c:if test="${provinceSelectionUnit.caseEndType2 eq '1'}">selected</c:if>>跨区域跨流域违法案件
                                </option>
                                <option value="2"
                                        <c:if test="${provinceSelectionUnit.caseEndType2 eq '2'}">selected</c:if>>部级审批的建设项目
                                </option>
                                <option value="3"
                                        <c:if test="${provinceSelectionUnit.caseEndType2 eq '3'}">selected</c:if>>综合运用四个配套办法“组合拳“（至少采用2种及以上配套办法）
                                </option>
                            </select>
                        </div>

                        <div style="margin-left: 60px; margin-top: 20px;display: flex;width: calc(100% - 200px)">
                            <label style="font-size: 14px; color: #333333; font-weight: 400"><span
                                    style="color: red;size: 60px" class="iscarryOut">*</span>查办大案要案支撑材料：</label>
                            <span id="uploadTr8"
                                  <c:if test="${provinceSelectionUnit.caseEndsupUrlname!='' && provinceSelectionUnit.caseEndsupUrlname!=null }">style='display:none'</c:if>
                            >
<%--                                style="width:calc(100%-224px)"--%>
								<c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                    <input type="file" id="caseEndFile" value="文件上传">
                                    <input type="hidden" class="form-control" name="caseEndsupUrlname"
                                           id="caseEndsupUrlname" value="${provinceSelectionUnit.caseEndsupUrlname}">
                                    <input type="hidden" class="form-control" name="caseEndsupUrl"
                                           id="caseEndsupUrl" value="${provinceSelectionUnit.caseEndsupUrl}">
                                </c:if>
						   </span>
                            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                <input id="reButton8"
                                       <c:if test="${provinceSelectionUnit.caseEndsupUrlname=='' || provinceSelectionUnit.caseEndsupUrlname==null }">style='display:none'</c:if>
                                       class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                            </c:if>
                        </div>
                        <p style="color: #FB3301;font-size:12px;line-height: 20px;font-weight: 400;margin-left: 600px">案件处罚决定书或移送决定书扫描件。其中，如涉及环境犯罪的，需提供判决书或联合挂牌文书等。</p>
                        <div id="wsc8" style="margin-left:357px;width: 380px;margin-top: 14px;border-radius: 3px;">
                             <span style="font-size: 16px;margin-top: 10px; margin-left: -4px">
                                  <c:choose>
                                      <c:when test="${provinceSelectionUnit.caseEndsupUrlname!='' && provinceSelectionUnit.caseEndsupUrlname!=null }">
                                            <span style=" margin-right: 8px; padding-top: 4px;">
    <%--                                                ${provinceSelectionUnit.orgNormFileName}--%>
                                                <a href="javascript:void(0)"
                                                   onclick="downloadFile('${provinceSelectionUnit.caseEndsupUrl}','${provinceSelectionUnit.caseEndsupUrlname}')">
                                                        ${provinceSelectionUnit.caseEndsupUrlname}
                                                </a>
                                            </span>
                                      </c:when>
                                  </c:choose>
							</span>
                        </div>
                    </div>




                    <%--执法事项目录--%>
                    <%--                    <div style=" padding: 20px 0;width: 100%">--%>
                    <%--                        <h1 style="font-size: 14px;color: #31688F;font-weight:400;height: 33px;line-height:33px;background: #F8F8F8;">（四）执法事项目录</h1>--%>
                    <%--                        <div style="margin-left: 283px; margin-top: 20px">--%>
                    <%--                            <span style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
                    <%--                                    style="color: red;size: 60px">*</span>是否制定：</span>--%>
                    <%--                            <select style="width:380px;margin-right:5px;height: 39px" name="isZfEnact" id="isZfEnact">--%>
                    <%--                                <option value="">--请选择--</option>--%>
                    <%--                                <option value="1"--%>
                    <%--                                        <c:if test="${provinceSelectionUnit.isZfEnact eq '1'}">selected</c:if>>--%>
                    <%--                                    是--%>
                    <%--                                </option>--%>
                    <%--                                <option value="0"--%>
                    <%--                                        <c:if test="${provinceSelectionUnit.isZfEnact eq '0'}">selected</c:if>>--%>
                    <%--                                    否--%>
                    <%--                                </option>--%>
                    <%--                            </select>--%>
                    <%--                        </div>--%>
                    <%--                        <div style="margin-left: 200px; margin-top: 20px;display: flex;width: calc(100% - 200px)">--%>
                    <%--                            <span style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
                    <%--                                    style="color: red;size: 60px">*</span>执法事项目录支撑材料：</span>--%>

                    <%--                            <span id="uploadTr15"--%>
                    <%--                                  <c:if test="${provinceSelectionUnit.zfItemFileName!='' && provinceSelectionUnit.zfItemFileName!=null }">style='display:none'</c:if>--%>
                    <%--                                  >--%>
                    <%--&lt;%&ndash;                                                        style="width: calc(100% - 224px)&ndash;%&gt;--%>
                    <%--										<c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
                    <%--                                            <input type="file" id="zfItemFile" value="文件上传">--%>
                    <%--                                            <input type="hidden" class="form-control" name="zfItemFileName"--%>
                    <%--                                                   id="zfItemFile_Filename"--%>
                    <%--                                                   value="${provinceSelectionUnit.zfItemFileName}">--%>
                    <%--                                            <input type="hidden" class="form-control" name="zfItemFileUrl"--%>
                    <%--                                                   id="zfItemFile_Fileurl"--%>
                    <%--                                                   value="${provinceSelectionUnit.zfItemFileUrl}">--%>
                    <%--                                        </c:if>--%>
                    <%--					                </span>--%>
                    <%--                            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
                    <%--                                <input  id="reButton15"--%>
                    <%--                                       <c:if test="${provinceSelectionUnit.zfItemFileName=='' || provinceSelectionUnit.zfItemFileName==null }">style='display:none'</c:if>--%>
                    <%--                                       class="btn btn-danger btn-xs" type="button" value="重新上传"/>--%>
                    <%--                            </c:if>--%>
                    <%--                        </div>--%>
                    <%--                        <div id="wsc15" style="margin-left:357px;width: 380px;margin-top: 14px;border-radius: 3px;">--%>
                    <%--                            <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">--%>
                    <%--                            <c:choose>--%>
                    <%--                                <c:when test="${provinceSelectionUnit.zfItemFileName!='' && provinceSelectionUnit.zfItemFileName!=null }">--%>
                    <%--                                   <span style=" margin-right: 8px; padding-top: 4px;">--%>
                    <%--&lt;%&ndash;                                                   ${provinceSelectionUnit.zfItemFileName}&ndash;%&gt;--%>
                    <%--                                       <a href="javascript:void(0)"--%>
                    <%--                                          onclick="downloadFile('${provinceSelectionUnit.zfItemFileUrl}','${provinceSelectionUnit.zfItemFileName}')">--%>
                    <%--                                               ${provinceSelectionUnit.zfItemFileName}--%>
                    <%--                                       </a>--%>
                    <%--                                   </span>--%>
                    <%--                                </c:when>--%>
                    <%--                            </c:choose>--%>
                    <%--                            </span>--%>
                    <%--                        </div>--%>
                    <%--                    </div>--%>
                    <%--监督执法正面清单制度--%>
                    <%--                    <div style=" padding: 20px 0;width: 100%">--%>
                    <%--                        <h1 style="font-size: 14px;color: #31688F;font-weight:400;height: 33px;line-height:33px;background: #F8F8F8;">（五）监督执法正面清单制度</h1>--%>
                    <%--                        <div style="margin-left: 283px; margin-top: 20px">--%>
                    <%--                            <span style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
                    <%--                                    style="color: red;size: 60px">*</span>是否制定：</span>--%>
                    <%--                            <select style="width:380px;margin-right:5px;" name="isJdEnact" id="isJdEnact">--%>
                    <%--                                <option value="">--请选择--</option>--%>
                    <%--                                <option value="1"--%>
                    <%--                                        <c:if test="${provinceSelectionUnit.isJdEnact eq '1'}">selected</c:if>>--%>
                    <%--                                    是--%>
                    <%--                                </option>--%>
                    <%--                                <option value="0"--%>
                    <%--                                        <c:if test="${provinceSelectionUnit.isJdEnact eq '0'}">selected</c:if>>--%>
                    <%--                                    否--%>
                    <%--                                </option>--%>
                    <%--                            </select>--%>
                    <%--                        </div>--%>
                    <%--                        <div style="margin-left: 143px; margin-top: 20px;display: flex ;width: calc(100% - 200px)">--%>
                    <%--                            <span style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
                    <%--                                    style="color: red;size: 60px">*</span>监督执法正面清单制度支撑材料：</span>--%>
                    <%--                            <span id="uploadTr16"--%>
                    <%--                                  <c:if test="${provinceSelectionUnit.jdLawFileName!='' && provinceSelectionUnit.jdLawFileName!=null }">style='display:none'</c:if>--%>
                    <%--                            >--%>
                    <%--&lt;%&ndash;                                style='width:calc(100%-224px)'&ndash;%&gt;--%>
                    <%--										<c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
                    <%--                                            <input type="file" id="jdLawFile" value="文件上传">--%>
                    <%--                                            <input type="hidden" class="form-control" name="jdLawFileName"--%>
                    <%--                                                   id="jdLawFile_Filename"--%>
                    <%--                                                   value="${provinceSelectionUnit.jdLawFileName}">--%>
                    <%--                                            <input type="hidden" class="form-control" name="jdLawFileUrl"--%>
                    <%--                                                   id="jdLawFile_Fileurl" value="${provinceSelectionUnit.jdLawFileUrl}">--%>
                    <%--                                        </c:if>--%>
                    <%--					            </span>--%>
                    <%--                            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
                    <%--                                <input  id="reButton16"--%>
                    <%--                                       <c:if test="${provinceSelectionUnit.jdLawFileName=='' || provinceSelectionUnit.jdLawFileName==null }">style='display:none'</c:if>--%>
                    <%--                                       class="btn btn-danger btn-xs" type="button" value="重新上传"/>--%>
                    <%--                            </c:if>--%>
                    <%--                        </div>--%>
                    <%--                        <div id="wsc16" style="margin-left:357px;width: 380px;margin-top: 14px;border-radius: 3px;">--%>
                    <%--                            <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">--%>
                    <%--                                <c:choose>--%>
                    <%--                                    <c:when test="${provinceSelectionUnit.jdLawFileName!='' && provinceSelectionUnit.jdLawFileName!=null }">--%>
                    <%--                                       <span style=" margin-right: 8px; padding-top: 4px;">--%>
                    <%--&lt;%&ndash;                                                   ${provinceSelectionUnit.jdLawFileName}&ndash;%&gt;--%>
                    <%--                                           <a href="javascript:void(0)"--%>
                    <%--                                              onclick="downloadFile('${provinceSelectionUnit.jdLawFileUrl}','${provinceSelectionUnit.jdLawFileName}')">--%>
                    <%--                                                   ${provinceSelectionUnit.jdLawFileName}--%>
                    <%--                                           </a>--%>
                    <%--                                       </span>--%>
                    <%--                                    </c:when>--%>
                    <%--                                </c:choose>--%>
                    <%--                            </span>--%>
                    <%--                        </div>--%>
                    <%--                    </div>--%>
                    <%--非现场监管程序规范--%>
                    <%--                    <div style=" padding: 20px 0;width: 100%">--%>
                    <%--                        <h1 style="font-size: 14px;color: #31688F;font-weight:400;height: 33px;line-height:33px;background: #F8F8F8;">（六）非现场监管程序规范</h1>--%>
                    <%--                        <div style="margin-left: 283px; margin-top: 20px">--%>
                    <%--                            <span style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
                    <%--                                    style="color: red;size: 60px">*</span>是否制定：</span>--%>
                    <%--                            <select style="width:380px;margin-right:5px;" name="isFxcEnact" id="isFxcEnact">--%>
                    <%--                                <option value="">--请选择--</option>--%>
                    <%--                                <option value="1"--%>
                    <%--                                        <c:if test="${provinceSelectionUnit.isFxcEnact eq '1'}">selected</c:if>>--%>
                    <%--                                    是--%>
                    <%--                                </option>--%>
                    <%--                                <option value="0"--%>
                    <%--                                        <c:if test="${provinceSelectionUnit.isFxcEnact eq '0'}">selected</c:if>>--%>
                    <%--                                    否--%>
                    <%--                                </option>--%>
                    <%--                            </select>--%>
                    <%--                        </div>--%>
                    <%--                        <div style="margin-left: 156px; margin-top: 20px;display: flex;width: calc(100% - 200px)">--%>
                    <%--                            <span style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
                    <%--                                    style="color: red;size: 60px">*</span>非现场监管程序规范支撑材料：</span>--%>
                    <%--                            <span id="uploadTr17"--%>
                    <%--                                  <c:if test="${provinceSelectionUnit.fxcRoutineFileName!='' && provinceSelectionUnit.fxcRoutineFileName!=null }">style='display:none'</c:if>--%>
                    <%--                                  >--%>
                    <%--&lt;%&ndash;                                                        style="width: calc(100% - 224px)&ndash;%&gt;--%>
                    <%--										<c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
                    <%--                                            <input type="file" id="fxcRoutineFile" value="文件上传">--%>
                    <%--                                            <input type="hidden" class="form-control" name="fxcRoutineFileName"--%>
                    <%--                                                   id="fxcRoutineFile_Filename"--%>
                    <%--                                                   value="${provinceSelectionUnit.fxcRoutineFileName}">--%>
                    <%--                                            <input type="hidden" class="form-control" name="fxcRoutineFileUrl"--%>
                    <%--                                                   id="fxcRoutineFile_Fileurl"--%>
                    <%--                                                   value="${provinceSelectionUnit.fxcRoutineFileUrl}">--%>
                    <%--                                        </c:if>--%>
                    <%--					            </span>--%>
                    <%--                            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
                    <%--                                <input  id="reButton17"--%>
                    <%--                                       <c:if test="${provinceSelectionUnit.fxcRoutineFileName=='' || provinceSelectionUnit.fxcRoutineFileName==null }">style='display:none'</c:if>--%>
                    <%--                                       class="btn btn-danger btn-xs" type="button" value="重新上传"/>--%>
                    <%--                            </c:if>--%>
                    <%--                        </div>--%>
                    <%--                        <div id="wsc17" style="margin-left:357px;width: 380px;margin-top: 14px;border-radius: 3px;">--%>
                    <%--                            <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">--%>
                    <%--                                <c:choose>--%>
                    <%--                                    <c:when test="${provinceSelectionUnit.fxcRoutineFileName!='' && provinceSelectionUnit.fxcRoutineFileName!=null }">--%>
                    <%--                                       <span style=" margin-right: 8px; padding-top: 4px;">--%>
                    <%--&lt;%&ndash;                                                   ${provinceSelectionUnit.fxcRoutineFileName}&ndash;%&gt;--%>
                    <%--                                           <a href="javascript:void(0)"--%>
                    <%--                                              onclick="downloadFile('${provinceSelectionUnit.fxcRoutineFileUrl}','${provinceSelectionUnit.fxcRoutineFileName}')">--%>
                    <%--                                                   ${provinceSelectionUnit.fxcRoutineFileName}--%>
                    <%--                                           </a>--%>
                    <%--                                       </span>--%>
                    <%--                                    </c:when>--%>
                    <%--                                </c:choose>--%>
                    <%--                            </span>--%>
                    <%--                        </div>--%>
                    <%--                    </div>--%>
                    <%--履职责任制度--%>
                    <%--                    <div style=" padding: 20px 0;width: 100%">--%>
                    <%--                        <h1 style="font-size: 14px;color: #31688F;font-weight:400;height: 33px;line-height:33px;background: #F8F8F8;">（七）履职责任制度</h1>--%>
                    <%--                        <div style="margin-left: 283px; margin-top: 20px">--%>
                    <%--                            <span style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
                    <%--                                    style="color: red;size: 60px">*</span>是否制定：</span>--%>
                    <%--                            <select style="width:380px;margin-right:5px;" name="isLzEnact" id="isLzEnact">--%>
                    <%--                                <option value="">--请选择--</option>--%>
                    <%--                                <option value="1"--%>
                    <%--                                        <c:if test="${provinceSelectionUnit.isLzEnact eq '1'}">selected</c:if>>--%>
                    <%--                                    是--%>
                    <%--                                </option>--%>
                    <%--                                <option value="0"--%>
                    <%--                                        <c:if test="${provinceSelectionUnit.isLzEnact eq '0'}">selected</c:if>>--%>
                    <%--                                    否--%>
                    <%--                                </option>--%>
                    <%--                            </select>--%>
                    <%--                        </div>--%>
                    <%--                        <div style="margin-left: 200px; margin-top: 20px; display: flex;width: calc(100% - 200px)">--%>
                    <%--                            <span style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
                    <%--                                    style="color: red;size: 60px">*</span>履职责任制度支撑材料：</span>--%>

                    <%--                            <span id="uploadTr18"--%>
                    <%--                                  <c:if test="${provinceSelectionUnit.lzDutyFileName!='' && provinceSelectionUnit.lzDutyFileName!=null }">style='display:none'</c:if>--%>
                    <%--                                  >--%>
                    <%--&lt;%&ndash;                                                        style="width: calc(100% - 224px)&ndash;%&gt;--%>
                    <%--										<c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
                    <%--                                            <input type="file" id="lzDutyFile" value="文件上传">--%>
                    <%--                                            <input type="hidden" class="form-control" name="lzDutyFileName"--%>
                    <%--                                                   id="lzDutyFile_Filename"--%>
                    <%--                                                   value="${provinceSelectionUnit.lzDutyFileName}">--%>
                    <%--                                            <input type="hidden" class="form-control" name="lzDutyFileUrl"--%>
                    <%--                                                   id="lzDutyFile_Fileurl"--%>
                    <%--                                                   value="${provinceSelectionUnit.lzDutyFileUrl}">--%>
                    <%--                                        </c:if>--%>
                    <%--					            </span>--%>
                    <%--                            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
                    <%--                                <input  id="reButton18"--%>
                    <%--                                       <c:if test="${provinceSelectionUnit.lzDutyFileName=='' || provinceSelectionUnit.lzDutyFileName==null }">style='display:none'</c:if>--%>
                    <%--                                       class="btn btn-danger btn-xs" type="button" value="重新上传"/>--%>
                    <%--                            </c:if>--%>
                    <%--                        </div>--%>
                    <%--                        <div id="wsc18" style="margin-left:357px;width: 380px;margin-top: 14px;border-radius: 3px;">--%>
                    <%--                            <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">--%>
                    <%--                                <c:choose>--%>
                    <%--                                    <c:when test="${provinceSelectionUnit.lzDutyFileName!='' && provinceSelectionUnit.lzDutyFileName!=null }">--%>
                    <%--                                       <span style=" margin-right: 8px; padding-top: 4px;">--%>
                    <%--&lt;%&ndash;                                                   ${provinceSelectionUnit.lzDutyFileName}&ndash;%&gt;--%>
                    <%--                                           <a href="javascript:void(0)"--%>
                    <%--                                              onclick="downloadFile('${provinceSelectionUnit.lzDutyFileUrl}','${provinceSelectionUnit.lzDutyFileName}')">--%>
                    <%--                                                   ${provinceSelectionUnit.lzDutyFileName}--%>
                    <%--                                           </a>--%>
                    <%--                                       </span>--%>
                    <%--                                    </c:when>--%>
                    <%--                                </c:choose>--%>
                    <%--                            </span>--%>
                    <%--                        </div>--%>
                    <%--                    </div>--%>
                    <%--双随机、一公开监管制度--%>
                    <%--                    <div style="padding: 20px 0;width: 100%">--%>
                    <%--                        <h1 style="font-size: 14px;color: #31688F;font-weight:400;height: 33px;line-height:33px;background: #F8F8F8;">（八）“双随机、一公开”监管制度</h1>--%>
                    <%--                        <div class="shifou">--%>
                    <%--                            <span class="zhiding"><span style="color: red;size: 60px">*</span>是否制定：</span>--%>
                    <%--                            <select style="width:380px;margin-right:5px;" name="isSsjEnact" id="isSsjEnact">--%>
                    <%--                                <option value="">--请选择--</option>--%>
                    <%--                                <option value="1"--%>
                    <%--                                        <c:if test="${provinceSelectionUnit.isSsjEnact eq '1'}">selected</c:if>>--%>
                    <%--                                    是--%>
                    <%--                                </option>--%>
                    <%--                                <option value="0"--%>
                    <%--                                        <c:if test="${provinceSelectionUnit.isSsjEnact eq '0'}">selected</c:if>>--%>
                    <%--                                    否--%>
                    <%--                                </option>--%>
                    <%--                            </select>--%>
                    <%--                        </div>--%>
                    <%--                        <div class="shangchuan" style="width: calc(100% - 200px)">--%>
                    <%--                            <span class="zhiding"><span style="color: red;size: 60px">*</span>“双随机、一公开”监管制度支撑材料：</span>--%>

                    <%--                            <span id="uploadTr19"--%>
                    <%--                                  <c:if test="${provinceSelectionUnit.ssjOpenSystemName!='' && provinceSelectionUnit.ssjOpenSystemName!=null }">style='display:none'</c:if>--%>
                    <%--                                  >--%>
                    <%--&lt;%&ndash;                                                        style="width: calc(100% - 224px)&ndash;%&gt;--%>
                    <%--										<c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
                    <%--                                            <input type="file" id="ssjOpenSystem" value="文件上传">--%>
                    <%--                                            <input type="hidden" class="form-control" name="ssjOpenSystemName"--%>
                    <%--                                                   id="ssjOpenSystem_Filename"--%>
                    <%--                                                   value="${provinceSelectionUnit.ssjOpenSystemName}">--%>
                    <%--                                            <input type="hidden" class="form-control" name="ssjOpenSystemUrl"--%>
                    <%--                                                   id="ssjOpenSystem_Fileurl"--%>
                    <%--                                                   value="${provinceSelectionUnit.ssjOpenSystemUrl}">--%>
                    <%--                                        </c:if>--%>
                    <%--					            </span>--%>
                    <%--                            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
                    <%--                                <input  id="reButton19"--%>
                    <%--                                       <c:if test="${provinceSelectionUnit.ssjOpenSystemName=='' || provinceSelectionUnit.ssjOpenSystemName==null }">style='display:none'</c:if>--%>
                    <%--                                       class="btn btn-danger btn-xs" type="button" value="重新上传"/>--%>
                    <%--                            </c:if>--%>
                    <%--                        </div>--%>
                    <%--                        <div id="wsc19" style="margin-left:357px;width: 380px;margin-top: 14px;border-radius: 3px;">--%>
                    <%--                            <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">--%>
                    <%--                                <c:choose>--%>
                    <%--                                    <c:when test="${provinceSelectionUnit.ssjOpenSystemName!='' && provinceSelectionUnit.ssjOpenSystemName!=null }">--%>
                    <%--                                       <span style=" margin-right: 8px; padding-top: 4px;">--%>
                    <%--&lt;%&ndash;                                                   ${provinceSelectionUnit.ssjOpenSystemName}&ndash;%&gt;--%>
                    <%--                                           <a href="javascript:void(0)"--%>
                    <%--                                              onclick="downloadFile('${provinceSelectionUnit.ssjOpenSystemUrl}','${provinceSelectionUnit.ssjOpenSystemName}')">--%>
                    <%--                                                   ${provinceSelectionUnit.ssjOpenSystemName}--%>
                    <%--                                           </a>--%>
                    <%--                                       </span>--%>
                    <%--                                    </c:when>--%>
                    <%--                                </c:choose>--%>
                    <%--                            </span>--%>
                    <%--                        </div>--%>
                    <%--                    </div>--%>
                    <%--典型执法案例指导制度--%>
                    <%--                    <div style="padding: 20px 0;width: 100%">--%>
                    <%--                        <h1 style="font-size: 14px;color: #31688F;font-weight:400;height: 33px;line-height:33px;background: #F8F8F8;">（九）典型执法案例指导制度</h1>--%>
                    <%--                        <div class="shifou">--%>
                    <%--                            <span class="zhiding"><span style="color: red;size: 60px">*</span>是否制定：</span>--%>
                    <%--                            <select style="width:380px;margin-right:5px;" name="isDxLawEnact"--%>
                    <%--                                    id="isDxLawEnact">--%>
                    <%--                                <option value="">--请选择--</option>--%>
                    <%--                                <option value="1"--%>
                    <%--                                        <c:if test="${provinceSelectionUnit.isDxLawEnact eq '1'}">selected</c:if>>--%>
                    <%--                                    是--%>
                    <%--                                </option>--%>
                    <%--                                <option value="0"--%>
                    <%--                                        <c:if test="${provinceSelectionUnit.isDxLawEnact eq '0'}">selected</c:if>>--%>
                    <%--                                    否--%>
                    <%--                                </option>--%>
                    <%--                            </select>--%>
                    <%--                        </div>--%>
                    <%--                        <div class="shangchuan" style="margin-left:143px;width: calc(100% - 200px)">--%>
                    <%--                            <span class="zhiding"><span style="color: red;size: 60px">*</span>典型执法案例指导制度支撑材料：</span>--%>
                    <%--                            <span id="uploadTr20"--%>
                    <%--                                  <c:if test="${provinceSelectionUnit.dxLawFileName!='' && provinceSelectionUnit.dxLawFileName!=null }">style='display:none'</c:if>--%>
                    <%--                                  >--%>
                    <%--&lt;%&ndash;                                                        style="width: calc(100% - 224px)&ndash;%&gt;--%>
                    <%--										<c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
                    <%--                                            <input type="file" id="dxLawFile" value="文件上传">--%>
                    <%--                                            <input type="hidden" class="form-control" name="dxLawFileName"--%>
                    <%--                                                   id="dxLawFile_Filename"--%>
                    <%--                                                   value="${provinceSelectionUnit.dxLawFileName}">--%>
                    <%--                                            <input type="hidden" class="form-control" name="dxLawFileUrl"--%>
                    <%--                                                   id="dxLawFile_Fileurl" value="${provinceSelectionUnit.dxLawFileUrl}">--%>
                    <%--                                        </c:if>--%>
                    <%--					            </span>--%>
                    <%--                            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
                    <%--                                <input  id="reButton20"--%>
                    <%--                                       <c:if test="${provinceSelectionUnit.dxLawFileName=='' || provinceSelectionUnit.dxLawFileName==null }">style='display:none'</c:if>--%>
                    <%--                                       class="btn btn-danger btn-xs" type="button" value="重新上传"/>--%>
                    <%--                            </c:if>--%>
                    <%--                        </div>--%>
                    <%--                        <div id="wsc20" style="margin-left:357px;width: 380px;margin-top: 14px;border-radius: 3px;">--%>
                    <%--                            <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">--%>
                    <%--                                <c:choose>--%>
                    <%--                                    <c:when test="${provinceSelectionUnit.dxLawFileName!='' && provinceSelectionUnit.dxLawFileName!=null }">--%>
                    <%--                                       <span style=" margin-right: 8px; padding-top: 4px;">--%>
                    <%--&lt;%&ndash;                                                   ${provinceSelectionUnit.dxLawFileName}&ndash;%&gt;--%>
                    <%--                                           <a href="javascript:void(0)"--%>
                    <%--                                              onclick="downloadFile('${provinceSelectionUnit.dxLawFileUrl}','${provinceSelectionUnit.dxLawFileName}')">--%>
                    <%--                                                   ${provinceSelectionUnit.dxLawFileName}--%>
                    <%--                                           </a>--%>
                    <%--                                       </span>--%>
                    <%--                                    </c:when>--%>
                    <%--                                </c:choose>--%>
                    <%--                            </span>--%>
                    <%--                        </div>--%>
                    <%--                    </div>--%>
                    <%--执法监测工作机制--%>
                    <%--                    <div style="padding: 20px 0;width: 100%">--%>
                    <%--                        <h1 style="font-size: 14px;color: #31688F;font-weight:400;height: 33px;line-height:33px;background: #F8F8F8;">（十）执法监测工作机制</h1>--%>
                    <%--                        <div class="shifou">--%>
                    <%--                            <span class="zhiding"><span style="color: red;size: 60px">*</span>是否制定：</span>--%>
                    <%--                            <select name="isZfJcEnact" id="isZfJcEnact"--%>
                    <%--                                    style="width:380px;margin-right:5px;">--%>
                    <%--                                <option value="">--请选择--</option>--%>
                    <%--                                <option value="1"--%>
                    <%--                                        <c:if test="${provinceSelectionUnit.isZfJcEnact eq '1'}">selected</c:if>>--%>
                    <%--                                    是--%>
                    <%--                                </option>--%>
                    <%--                                <option value="0"--%>
                    <%--                                        <c:if test="${provinceSelectionUnit.isZfJcEnact eq '0'}">selected</c:if>>--%>
                    <%--                                    否--%>
                    <%--                                </option>--%>
                    <%--                            </select>--%>
                    <%--                        </div>--%>
                    <%--                        <div class="shangchuan" style="margin-left:171px;width: calc(100% - 200px)">--%>
                    <%--                            <span class="zhiding"><span style="color: red;size: 60px">*</span>执法监测工作机制支撑材料：</span>--%>
                    <%--                            <span id="uploadTr21"--%>
                    <%--                                  <c:if test="${provinceSelectionUnit.zfJcWorkFileName!='' && provinceSelectionUnit.zfJcWorkFileName!=null }">style='display:none'</c:if>--%>
                    <%--                                  >--%>
                    <%--&lt;%&ndash;                                style="width: calc(100% - 224px)&ndash;%&gt;--%>
                    <%--										<c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
                    <%--                                            <input type="file" id="zfJcWorkFile" value="文件上传">--%>
                    <%--                                            <input type="hidden" class="form-control" name="zfJcWorkFileName"--%>
                    <%--                                                   id="zfJcWorkFile_Filename"--%>
                    <%--                                                   value="${provinceSelectionUnit.zfJcWorkFileName}">--%>
                    <%--                                            <input type="hidden" class="form-control" name="zfJcWorkFileUrl"--%>
                    <%--                                                   id="zfJcWorkFile_Fileurl"--%>
                    <%--                                                   value="${provinceSelectionUnit.zfJcWorkFileUrl}">--%>
                    <%--                                        </c:if>--%>
                    <%--					            </span>--%>
                    <%--                            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
                    <%--                                <input  id="reButton21"--%>
                    <%--                                       <c:if test="${provinceSelectionUnit.zfJcWorkFileName=='' || provinceSelectionUnit.zfJcWorkFileName==null }">style='display:none'</c:if>--%>
                    <%--                                       class="btn btn-danger btn-xs" type="button" value="重新上传"/>--%>
                    <%--                            </c:if>--%>
                    <%--                        </div>--%>
                    <%--                        <div id="wsc21" style="margin-left:357px;width: 380px;margin-top: 14px;border-radius: 3px;">--%>
                    <%--                            <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">--%>
                    <%--                                <c:choose>--%>
                    <%--                                    <c:when test="${provinceSelectionUnit.zfJcWorkFileName!='' && provinceSelectionUnit.zfJcWorkFileName!=null }">--%>
                    <%--                                       <span style=" margin-right: 8px; padding-top: 4px;">--%>
                    <%--    &lt;%&ndash;                                               ${provinceSelectionUnit.zfJcWorkFileName}&ndash;%&gt;--%>
                    <%--                                           <a href="javascript:void(0)"--%>
                    <%--                                              onclick="downloadFile('${provinceSelectionUnit.zfJcWorkFileUrl}','${provinceSelectionUnit.zfJcWorkFileName}')">--%>
                    <%--                                                   ${provinceSelectionUnit.zfJcWorkFileName}--%>
                    <%--                                           </a>--%>
                    <%--                                       </span>--%>
                    <%--                                    </c:when>--%>
                    <%--                                </c:choose>--%>
                    <%--                            </span>--%>
                    <%--                        </div>--%>
                    <%--                    </div>--%>
                </div>
            </div>

            <%--            <div style=" padding:  2px 30px 26px 0;width: 100%">--%>
            <%--                <h1 style="font-size: 14px;color: #31688F;font-weight:400;height: 33px;line-height:33px;background: #F8F8F8;">（三）队伍建设与管理</h1>--%>
            <%--                <h3 style="margin-left: 219px;font-size:14px;color: #31688F;">一、规范化示范建设</h3>--%>
            <%--                <div>--%>
            <%--                    <h4 style="font-size:14px;font-weight: 400;margin-left: 246px;margin-top: 20px"><span--%>
            <%--                            style="color: red;size: 60px">*</span>规范化示范建设试点工作</h4>--%>

            <%--                    <div style="margin-left: 283px; margin-top: 20px">--%>
            <%--                                <span style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
            <%--                                        style="color: red;size: 60px">*</span>2021年是否出台：</span>--%>
            <%--                        <select style="width:380px;margin-right:5px;" name="isCustomization"--%>
            <%--                                id="isCustomization">--%>
            <%--                            <option value="">--请选择--</option>--%>
            <%--                            <option value="1"--%>
            <%--                                    <c:if test="${provinceSelectionUnit.isCustomization eq '1'}">selected</c:if>>--%>
            <%--                                是--%>
            <%--                            </option>--%>
            <%--                            <option value="0"--%>
            <%--                                    <c:if test="${provinceSelectionUnit.isCustomization eq '0'}">selected</c:if>>--%>
            <%--                                否--%>
            <%--                            </option>--%>
            <%--                        </select>--%>
            <%--                    </div>--%>

            <%--                    <div style="margin-left: 170px; margin-top: 20px;display: flex;width: calc(100% - 200px)">--%>
            <%--                                <span style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
            <%--                                        style="color: red;size: 60px">*</span>工作开展情况、取得成效等工作简报或报告：</span>--%>
            <%--                        <span id="uploadTr10"--%>
            <%--                              <c:if test="${provinceSelectionUnit.specificEnactFileName!='' && provinceSelectionUnit.specificEnactFileName!=null }">style='display:none'</c:if>--%>
            <%--                        >--%>
            <%--&lt;%&ndash;                                                            style="width: calc(100% - 224px)&ndash;%&gt;--%>
            <%--									<c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
            <%--                                        <input type="file" id="specificEnactFile" value="文件上传">--%>
            <%--                                        <input type="hidden" class="form-control" name="specificEnactFileName"--%>
            <%--                                               id="specificEnactFile_Filename"--%>
            <%--                                               value="${provinceSelectionUnit.specificEnactFileName}">--%>
            <%--                                        <input type="hidden" class="form-control" name="specificEnactFileUrl"--%>
            <%--                                               id="specificEnactFile_Fileurl"--%>
            <%--                                               value="${provinceSelectionUnit.specificEnactFileUrl}">--%>
            <%--                                    </c:if>--%>
            <%--					            </span>--%>
            <%--                        <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
            <%--                            <input id="reButton10"--%>
            <%--                                   <c:if test="${provinceSelectionUnit.specificEnactFileName=='' || provinceSelectionUnit.specificEnactFileName==null }">style='display:none'</c:if>--%>
            <%--                                   class="btn btn-danger btn-xs" type="button" value="重新上传"/>--%>
            <%--                        </c:if>--%>
            <%--                    </div>--%>
            <%--                    <div id="wsc10" style="margin-left:357px;width: 380px;margin-top: 14px;border-radius: 3px;">--%>
            <%--                                <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">--%>
            <%--                                    <c:choose>--%>
            <%--                                        <c:when test="${provinceSelectionUnit.specificEnactFileName!='' && provinceSelectionUnit.specificEnactFileName!=null }">--%>
            <%--                                           <span style=" margin-right: 8px; padding-top: 4px;">--%>
            <%--&lt;%&ndash;                                                       ${provinceSelectionUnit.specificEnactFileName}&ndash;%&gt;--%>
            <%--                                               <a href="javascript:void(0)"--%>
            <%--                                                  onclick="downloadFile('${provinceSelectionUnit.specificEnactFileUrl}','${provinceSelectionUnit.specificEnactFileName}')">--%>
            <%--                                                       ${provinceSelectionUnit.specificEnactFileName}--%>
            <%--                                               </a>--%>
            <%--                                           </span>--%>
            <%--                                        </c:when>--%>
            <%--                                    </c:choose>--%>
            <%--								</span>--%>
            <%--                    </div>--%>
            <%--                </div>--%>
            <%--                <div>--%>
            <%--                    <h4 style="font-size:14px;font-weight: 400;margin-left: 252px;margin-top: 33px">开展执法稽查</h4>--%>

            <%--                    <div style="margin-left: 283px; margin-top: 33px">--%>
            <%--                                <span style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
            <%--                                        style="color: red;size: 60px">*</span>是否开展：</span>--%>
            <%--                        <select style="width:380px;margin-right:5px;" name="isExecute"--%>
            <%--                                id="isExecute">--%>
            <%--                            <option value="">--请选择--</option>--%>
            <%--                            <option value="1"--%>
            <%--                                    <c:if test="${provinceSelectionUnit.isExecute eq '1'}">selected</c:if>>--%>
            <%--                                是--%>
            <%--                            </option>--%>
            <%--                            <option value="0"--%>
            <%--                                    <c:if test="${provinceSelectionUnit.isExecute eq '0'}">selected</c:if>>--%>
            <%--                                否--%>
            <%--                            </option>--%>
            <%--                        </select>--%>
            <%--                    </div>--%>

            <%--                    <div style="margin-left: 177px; margin-top: 20px;display: flex;width: calc(100% - 200px)">--%>
            <%--                                <span style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
            <%--                                        style="color: red;size: 60px">*</span>稽查计划：</span>--%>
            <%--                        <span id="uploadTr11"--%>
            <%--                              <c:if test="${provinceSelectionUnit.specificConditionFileName!='' && provinceSelectionUnit.specificConditionFileName!=null }">style='display:none'</c:if>--%>
            <%--                        >--%>
            <%--&lt;%&ndash;                                                            style="width: calc(100% - 224px)&ndash;%&gt;--%>
            <%--									<c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
            <%--                                        <input type="file" id="specificConditionFile" value="文件上传">--%>
            <%--                                        <input type="hidden" class="form-control" name="specificConditionFileName"--%>
            <%--                                               id="specificConditionFile_Filename"--%>
            <%--                                               value="${provinceSelectionUnit.specificConditionFileName}">--%>
            <%--                                        <input type="hidden" class="form-control" name="specificConditionFileUrl"--%>
            <%--                                               id="specificConditionFile_Fileurl"--%>
            <%--                                               value="${provinceSelectionUnit.specificConditionFileUrl}">--%>
            <%--                                    </c:if>--%>
            <%--					            </span>--%>
            <%--                        <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
            <%--                            <input  id="reButton11"--%>
            <%--                                    <c:if test="${provinceSelectionUnit.specificConditionFileName=='' || provinceSelectionUnit.specificConditionFileName==null }">style='display:none'</c:if>--%>
            <%--                                    class="btn btn-danger btn-xs" type="button" value="重新上传"/>--%>
            <%--                        </c:if>--%>
            <%--                    </div>--%>
            <%--                    <div id="wsc11" style="margin-left:357px;width: 380px;margin-top: 14px;border-radius: 3px;">--%>
            <%--                                <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">--%>
            <%--                                    <c:choose>--%>
            <%--                                        <c:when test="${provinceSelectionUnit.specificConditionFileName!='' && provinceSelectionUnit.specificConditionFileName!=null }">--%>
            <%--                                               <span style=" margin-right: 8px; padding-top: 4px;">--%>
            <%--    &lt;%&ndash;                                                   ${provinceSelectionUnit.specificConditionFileName}&ndash;%&gt;--%>
            <%--                                                   <a href="javascript:void(0)"--%>
            <%--                                                      onclick="downloadFile('${provinceSelectionUnit.specificConditionFileUrl}','${provinceSelectionUnit.specificConditionFileName}')">--%>
            <%--                                                           ${provinceSelectionUnit.specificConditionFileName}--%>
            <%--                                                   </a>--%>
            <%--                                               </span>--%>
            <%--                                        </c:when>--%>
            <%--                                    </c:choose>--%>
            <%--                                </span>--%>
            <%--                    </div>--%>

            <%--                    <div style="margin-left: 177px; margin-top: 20px;display: flex;width: calc(100% - 200px)">--%>
            <%--                                <span style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
            <%--                                        style="color: red;size: 60px">*</span>稽查工作报告：</span>--%>
            <%--                        <span id="uploadTr12"--%>
            <%--                              <c:if test="${provinceSelectionUnit.specificConditionFileName!='' && provinceSelectionUnit.specificConditionFileName!=null }">style='display:none'</c:if>--%>
            <%--                        >--%>
            <%--&lt;%&ndash;                                                            style="width: calc(100% - 224px)&ndash;%&gt;--%>
            <%--									<c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
            <%--                                        <input type="file" id="specificConditionFile" value="文件上传">--%>
            <%--                                        <input type="hidden" class="form-control" name="specificConditionFileName"--%>
            <%--                                               id="specificConditionFile_Filename"--%>
            <%--                                               value="${provinceSelectionUnit.specificConditionFileName}">--%>
            <%--                                        <input type="hidden" class="form-control" name="specificConditionFileUrl"--%>
            <%--                                               id="specificConditionFile_Fileurl"--%>
            <%--                                               value="${provinceSelectionUnit.specificConditionFileUrl}">--%>
            <%--                                    </c:if>--%>
            <%--					            </span>--%>
            <%--                        <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
            <%--                            <input  id="reButton12"--%>
            <%--                                    <c:if test="${provinceSelectionUnit.specificConditionFileName=='' || provinceSelectionUnit.specificConditionFileName==null }">style='display:none'</c:if>--%>
            <%--                                    class="btn btn-danger btn-xs" type="button" value="重新上传"/>--%>
            <%--                        </c:if>--%>
            <%--                    </div>--%>
            <%--                    <div id="wsc12" style="margin-left:357px;width: 380px;margin-top: 14px;border-radius: 3px;">--%>
            <%--                                <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">--%>
            <%--                                    <c:choose>--%>
            <%--                                        <c:when test="${provinceSelectionUnit.specificConditionFileName!='' && provinceSelectionUnit.specificConditionFileName!=null }">--%>
            <%--                                               <span style=" margin-right: 8px; padding-top: 4px;">--%>
            <%--    &lt;%&ndash;                                                   ${provinceSelectionUnit.specificConditionFileName}&ndash;%&gt;--%>
            <%--                                                   <a href="javascript:void(0)"--%>
            <%--                                                      onclick="downloadFile('${provinceSelectionUnit.specificConditionFileUrl}','${provinceSelectionUnit.specificConditionFileName}')">--%>
            <%--                                                           ${provinceSelectionUnit.specificConditionFileName}--%>
            <%--                                                   </a>--%>
            <%--                                               </span>--%>
            <%--                                        </c:when>--%>
            <%--                                    </c:choose>--%>
            <%--                                </span>--%>
            <%--                    </div>--%>


            <%--                    <h4 style="font-size:14px;font-weight: 400;margin-left: 252px;margin-top: 33px">统一证件、着装</h4>--%>

            <%--                    <div style="margin-left: 283px; margin-top: 33px">--%>
            <%--                                <span style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
            <%--                                        style="color: red;size: 60px">*</span>是否完成统一着装：</span>--%>
            <%--                        <select style="width:380px;margin-right:5px;" name="isExecute"--%>
            <%--                                id="">--%>
            <%--                            <option value="">--请选择--</option>--%>
            <%--                            <option value="1"--%>
            <%--                                    <c:if test="${provinceSelectionUnit.isExecute eq '1'}">selected</c:if>>--%>
            <%--                                是--%>
            <%--                            </option>--%>
            <%--                            <option value="0"--%>
            <%--                                    <c:if test="${provinceSelectionUnit.isExecute eq '0'}">selected</c:if>>--%>
            <%--                                否--%>
            <%--                            </option>--%>
            <%--                        </select>--%>
            <%--                    </div>--%>
            <%--                    <div style="margin-left: 283px; margin-top: 33px">--%>
            <%--                                <span style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
            <%--                                        style="color: red;size: 60px">*</span>是否完成统一证件：</span>--%>
            <%--                        <select style="width:380px;margin-right:5px;" name="isExecute"--%>
            <%--                                id="">--%>
            <%--                            <option value="">--请选择--</option>--%>
            <%--                            <option value="1"--%>
            <%--                                    <c:if test="${provinceSelectionUnit.isExecute eq '1'}">selected</c:if>>--%>
            <%--                                是--%>
            <%--                            </option>--%>
            <%--                            <option value="0"--%>
            <%--                                    <c:if test="${provinceSelectionUnit.isExecute eq '0'}">selected</c:if>>--%>
            <%--                                否--%>
            <%--                            </option>--%>
            <%--                        </select>--%>
            <%--                    </div>--%>
            <%--                    <div style="margin-left: 177px; margin-top: 20px;display: flex;width: calc(100% - 200px)">--%>
            <%--                                <span style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
            <%--                                        style="color: red;size: 60px">*</span>支撑材料：</span>--%>
            <%--                        <span id="uploadTr13"--%>
            <%--                              <c:if test="${provinceSelectionUnit.specificConditionFileName!='' && provinceSelectionUnit.specificConditionFileName!=null }">style='display:none'</c:if>--%>
            <%--                        >--%>
            <%--&lt;%&ndash;                                                            style="width: calc(100% - 224px)&ndash;%&gt;--%>
            <%--									<c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
            <%--                                        <input type="file" id="specificConditionFile" value="文件上传">--%>
            <%--                                        <input type="hidden" class="form-control" name="specificConditionFileName"--%>
            <%--                                               id="specificConditionFile_Filename"--%>
            <%--                                               value="${provinceSelectionUnit.specificConditionFileName}">--%>
            <%--                                        <input type="hidden" class="form-control" name="specificConditionFileUrl"--%>
            <%--                                               id="specificConditionFile_Fileurl"--%>
            <%--                                               value="${provinceSelectionUnit.specificConditionFileUrl}">--%>
            <%--                                    </c:if>--%>
            <%--					            </span>--%>
            <%--                        <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
            <%--                            <input  id="reButton13"--%>
            <%--                                    <c:if test="${provinceSelectionUnit.specificConditionFileName=='' || provinceSelectionUnit.specificConditionFileName==null }">style='display:none'</c:if>--%>
            <%--                                    class="btn btn-danger btn-xs" type="button" value="重新上传"/>--%>
            <%--                        </c:if>--%>
            <%--                    </div>--%>
            <%--                    <p style="color: #FB3301;width: 526px;font-size:12px;line-height: 20px;font-weight: 400;margin-left: 367px">统一着装、统一证件的完成情况报告，报告中需体现完成率、着装及持证情况等内容</p>--%>
            <%--                    <div id="wsc13" style="margin-left:357px;width: 380px;margin-top: 14px;border-radius: 3px;">--%>
            <%--                                <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">--%>
            <%--                                    <c:choose>--%>
            <%--                                        <c:when test="${provinceSelectionUnit.specificConditionFileName!='' && provinceSelectionUnit.specificConditionFileName!=null }">--%>
            <%--                                               <span style=" margin-right: 8px; padding-top: 4px;">--%>
            <%--    &lt;%&ndash;                                                   ${provinceSelectionUnit.specificConditionFileName}&ndash;%&gt;--%>
            <%--                                                   <a href="javascript:void(0)"--%>
            <%--                                                      onclick="downloadFile('${provinceSelectionUnit.specificConditionFileUrl}','${provinceSelectionUnit.specificConditionFileName}')">--%>
            <%--                                                           ${provinceSelectionUnit.specificConditionFileName}--%>
            <%--                                                   </a>--%>
            <%--                                               </span>--%>
            <%--                                        </c:when>--%>
            <%--                                    </c:choose>--%>
            <%--                                </span>--%>
            <%--                    </div>--%>
            <%--                </div>--%>

            <%--                &lt;%&ndash;人身安全保障&ndash;%&gt;--%>
            <%--                <div style="width: 100%">--%>
            <%--                    <h3 style="font-size:16px;color: #31688F;">二、人身安全保障</h3>--%>
            <%--                    <div>--%>
            <%--                        <h4 style="font-size:14px;font-weight: 400;margin-left: 252px;margin-top: 33px">制定情况</h4>--%>
            <%--                        <div style="margin-left: 283px; margin-top: 20px">--%>
            <%--                                    <span style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
            <%--                                            style="color: red;size: 60px">*</span>是否制定：</span>--%>
            <%--                            <select style="width:380px;margin-right:5px;" name="isEnact"--%>
            <%--                                    id="isEnact">--%>
            <%--                                <option value="">--请选择--</option>--%>
            <%--                                <option value="1"--%>
            <%--                                        <c:if test="${provinceSelectionUnit.isEnact eq '1'}">selected</c:if>>--%>
            <%--                                    是--%>
            <%--                                </option>--%>
            <%--                                <option value="0"--%>
            <%--                                        <c:if test="${provinceSelectionUnit.isEnact eq '0'}">selected</c:if>>--%>
            <%--                                    否--%>
            <%--                                </option>--%>
            <%--                            </select>--%>
            <%--                        </div>--%>

            <%--                        <div style="margin-left: 283px; margin-top: 20px">--%>
            <%--                                    <span style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
            <%--                                            style="color: red;size: 60px">*</span>文件类型：</span>--%>
            <%--                            <select style="width:380px;margin-right:5px;" name="rsFileType"--%>
            <%--                                    id="rsFileType">--%>
            <%--                                <option value="">--请选择--</option>--%>
            <%--                                <option value="1"--%>
            <%--                                        <c:if test="${provinceSelectionUnit.rsFileType eq '1'}">selected</c:if>>--%>
            <%--                                    正式发文--%>
            <%--                                </option>--%>
            <%--                                <option value="2"--%>
            <%--                                        <c:if test="${provinceSelectionUnit.rsFileType eq '2'}">selected</c:if>>--%>
            <%--                                    征求意见稿--%>
            <%--                                </option>--%>
            <%--                                <option value="3"--%>
            <%--                                        <c:if test="${provinceSelectionUnit.rsFileType eq '3'}">selected</c:if>>--%>
            <%--                                    无--%>
            <%--                                </option>--%>
            <%--                            </select>--%>
            <%--                        </div>--%>

            <%--                        <div style="margin-left: 283px; margin-top: 20px">--%>
            <%--                                    <span style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
            <%--                                            style="color: red;size: 60px">*</span>出台时间：</span>--%>
            <%--                            <input type="date" id="ctsj2" name="rsComeTime" height="39px"><br/>--%>
            <%--                        </div>--%>

            <%--                        <div style="margin-left: 143px; margin-top: 20px;display: flex;width: calc(100% - 200px)">--%>
            <%--                                    <span style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
            <%--                                            style="color: red;size: 60px">*</span>人身安全保障制定情况支撑材料：</span>--%>

            <%--                            <span id="uploadTr11"--%>
            <%--                                  <c:if test="${provinceSelectionUnit.rsAnQuanFileName!='' && provinceSelectionUnit.rsAnQuanFileName!=null }">style='display:none'</c:if>--%>
            <%--                            >--%>
            <%--&lt;%&ndash;                                                                style="width: calc(100% - 224px)&ndash;%&gt;--%>
            <%--												<c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
            <%--                                                    <input type="file" id="rsAnQuanFile" value="文件上传">--%>
            <%--                                                    <input type="hidden" class="form-control" name="rsAnQuanFileName"--%>
            <%--                                                           id="rsAnQuanFile_Filename"--%>
            <%--                                                           value="${provinceSelectionUnit.rsAnQuanFileName}">--%>
            <%--                                                    <input type="hidden" class="form-control" name="rsAnQuanFileUrl"--%>
            <%--                                                           id="rsAnQuanFile_Fileurl"--%>
            <%--                                                           value="${provinceSelectionUnit.rsAnQuanFileUrl}">--%>
            <%--                                                </c:if>--%>
            <%--										</span>--%>
            <%--                            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
            <%--                                <input  id="reButton11"--%>
            <%--                                        <c:if test="${provinceSelectionUnit.rsAnQuanFileName=='' || provinceSelectionUnit.rsAnQuanFileName==null }">style='display:none'</c:if>--%>
            <%--                                        class="btn btn-danger btn-xs" type="button" value="重新上传"/>--%>
            <%--                            </c:if>--%>
            <%--                        </div>--%>
            <%--                        <div id="wsc11"  style="margin-left:357px;width: 380px;margin-top: 14px;border-radius: 3px;">--%>
            <%--                                    <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">--%>
            <%--                                        <c:choose>--%>
            <%--                                            <c:when test="${provinceSelectionUnit.rsAnQuanFileName!='' && provinceSelectionUnit.rsAnQuanFileName!=null }">--%>
            <%--                                           <span style=" margin-right: 8px; padding-top: 4px;">--%>
            <%--&lt;%&ndash;                                                       ${provinceSelectionUnit.rsAnQuanFileName}&ndash;%&gt;--%>
            <%--                                               <a href="javascript:void(0)"--%>
            <%--                                                  onclick="downloadFile('${provinceSelectionUnit.rsAnQuanFileUrl}','${provinceSelectionUnit.rsAnQuanFileName}')">--%>
            <%--                                                       ${provinceSelectionUnit.rsAnQuanFileName}--%>
            <%--                                               </a>--%>
            <%--                                           </span>--%>
            <%--                                            </c:when>--%>
            <%--                                        </c:choose>--%>
            <%--                                    </span>--%>
            <%--                        </div>--%>
            <%--                    </div>--%>
            <%--                    <div style="width: 100%">--%>
            <%--                        <h4 style="font-size:14px;font-weight: 400;margin-left: 252px;margin-top: 33px">执行情况</h4>--%>
            <%--                        <div style="margin-left: 283px; margin-top: 33px">--%>
            <%--                                    <span style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
            <%--                                            style="color: red;size: 60px">*</span>是否执行：</span>--%>
            <%--                            <select style="width:380px;margin-right:5px;" name="isRsExecute"--%>
            <%--                                    id="isRsExecute">--%>
            <%--                                <option value="">--请选择--</option>--%>
            <%--                                <option value="1"--%>
            <%--                                        <c:if test="${provinceSelectionUnit.isRsExecute eq '1'}">selected</c:if>>--%>
            <%--                                    是--%>
            <%--                                </option>--%>
            <%--                                <option value="0"--%>
            <%--                                        <c:if test="${provinceSelectionUnit.isRsExecute eq '0'}">selected</c:if>>--%>
            <%--                                    否--%>
            <%--                                </option>--%>
            <%--                            </select>--%>
            <%--                        </div>--%>

            <%--                        <div style="margin-left: 283px; margin-top: 20px">--%>
            <%--                                    <span style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
            <%--                                            style="color: red;size: 60px">*</span>具体情况：</span>--%>
            <%--                            <textarea style="resize:none;padding:12px 4px 11px 7px;vertical-align: top;background: #FFFFFF;border: 1px solid #DCDFE6;border-radius: 5px;width: 379px; height: 100px;"--%>
            <%--                                      maxlength="500" placeholder="最多500字！" id="rsSpecific"--%>
            <%--                                      name="rsSpecific">${provinceSelectionUnit.rsSpecific}</textarea>--%>
            <%--                            <p style="color: #FB3301;width:379px;margin-left: 82px;font-size: 12px;font-weight:400;line-height:20px">（示例：执法局带头为全体在编执法人员（共50人）购买了人身意外伤害保险。)</p><br/>--%>
            <%--                        </div>--%>
            <%--                        <div style="margin-left: 142px; display: flex;width: calc(100% - 200px)">--%>
            <%--                                    <span style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
            <%--                                            style="color: red;size: 60px">*</span>人身安全保障执行情况支撑材料：</span>--%>
            <%--                            <span id="uploadTr12"--%>
            <%--                                  <c:if test="${provinceSelectionUnit.rsExecuteFileName!='' && provinceSelectionUnit.rsExecuteFileName!=null }">style='display:none'</c:if>--%>
            <%--                            >--%>
            <%--&lt;%&ndash;                                                                style="width: calc(100% - 224px)&ndash;%&gt;--%>
            <%--										<c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
            <%--                                            <input type="file" id="rsExecuteFile" value="文件上传">--%>
            <%--                                            <input type="hidden" class="form-control" name="rsExecuteFileName"--%>
            <%--                                                   id="rsExecuteFile_Filename"--%>
            <%--                                                   value="${provinceSelectionUnit.rsExecuteFileName}">--%>
            <%--                                            <input type="hidden" class="form-control" name="rsExecuteFileUrl"--%>
            <%--                                                   id="rsExecuteFile_Fileurl"--%>
            <%--                                                   value="${provinceSelectionUnit.rsExecuteFileUrl}">--%>
            <%--                                        </c:if>--%>
            <%--					                </span>--%>
            <%--                            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
            <%--                                <input  id="reButton12"--%>
            <%--                                        <c:if test="${provinceSelectionUnit.rsExecuteFileName=='' || provinceSelectionUnit.rsExecuteFileName==null }">style='display:none'</c:if>--%>
            <%--                                        class="btn btn-danger btn-xs" type="button" value="重新上传"/>--%>
            <%--                            </c:if>--%>
            <%--                        </div>--%>
            <%--                        <div id="wsc12" style="margin-left:357px;width: 380px;margin-top: 14px;border-radius: 3px;">--%>
            <%--                                     <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">--%>
            <%--                                        <c:choose>--%>
            <%--                                            <c:when test="${provinceSelectionUnit.rsExecuteFileName!='' && provinceSelectionUnit.rsExecuteFileName!=null }">--%>
            <%--                                           <span style=" margin-right: 8px; padding-top: 4px;">--%>
            <%--&lt;%&ndash;                                                       ${provinceSelectionUnit.rsExecuteFileName}&ndash;%&gt;--%>
            <%--                                               <a href="javascript:void(0)"--%>
            <%--                                                  onclick="downloadFile('${provinceSelectionUnit.rsExecuteFileUrl}','${provinceSelectionUnit.rsExecuteFileName}')">--%>
            <%--                                                       ${provinceSelectionUnit.rsExecuteFileName}--%>
            <%--                                               </a>--%>
            <%--                                           </span>--%>
            <%--                                            </c:when>--%>
            <%--                                        </c:choose>--%>
            <%--                                     </span>--%>
            <%--                        </div>--%>
            <%--                    </div>--%>
            <%--                </div>--%>
            <%--                &lt;%&ndash;尽职照单免责和失职照单问责&ndash;%&gt;--%>
            <%--                <div style="width: 100%;">--%>
            <%--                    <h3 style="font-size:16px;color: #31688F;">三、尽职照单免责和失职照单问责</h3>--%>
            <%--                    <div>--%>
            <%--                        <h4 style="font-size:14px;font-weight: 400;margin-left: 252px;margin-top: 33px">制定情况</h4>--%>
            <%--                        <div style="margin-left: 283px; margin-top: 20px">--%>
            <%--                                    <span style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
            <%--                                            style="color: red;size: 60px">*</span>是否制定：</span>--%>
            <%--                            <select style="width:380px;margin-right:5px;" name="isDingZhi"--%>
            <%--                                    id="isDingZhi">--%>
            <%--                                <option value="">--请选择--</option>--%>
            <%--                                <option value="1"--%>
            <%--                                        <c:if test="${provinceSelectionUnit.isDingZhi eq '1'}">selected</c:if>>--%>
            <%--                                    是--%>
            <%--                                </option>--%>
            <%--                                <option value="0"--%>
            <%--                                        <c:if test="${provinceSelectionUnit.isDingZhi eq '0'}">selected</c:if>>--%>
            <%--                                    否--%>
            <%--                                </option>--%>
            <%--                            </select>--%>
            <%--                        </div>--%>
            <%--                        <div style="margin-left: 283px; margin-top: 20px">--%>
            <%--                                    <span style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
            <%--                                            style="color: red;size: 60px">*</span>文件类型：</span>--%>
            <%--                            <select style="width:380px;margin-right:5px;" name="jzSzFileType"--%>
            <%--                                    id="jzSzFileType">--%>
            <%--                                <option value="">--请选择--</option>--%>
            <%--                                <option value="1"--%>
            <%--                                        <c:if test="${provinceSelectionUnit.jzSzFileType eq '1'}">selected</c:if>>--%>
            <%--                                    正式发文--%>
            <%--                                </option>--%>
            <%--                                <option value="2"--%>
            <%--                                        <c:if test="${provinceSelectionUnit.jzSzFileType eq '2'}">selected</c:if>>--%>
            <%--                                    征求意见稿--%>
            <%--                                </option>--%>
            <%--                                <option value="3"--%>
            <%--                                        <c:if test="${provinceSelectionUnit.jzSzFileType eq '3'}">selected</c:if>>--%>
            <%--                                    无--%>
            <%--                                </option>--%>
            <%--                            </select>--%>
            <%--                        </div>--%>

            <%--                        <div style="margin-left: 283px; margin-top: 20px">--%>
            <%--                                    <span style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
            <%--                                            style="color: red;size: 60px">*</span>出台时间：</span>--%>
            <%--                            <input type="date" name="jzSzComeTime" id="ctsj3"--%>
            <%--                                   value="${provinceSelectionUnit.jzSzComeTime}" height="39px"><br/>--%>

            <%--                        </div>--%>

            <%--                        <div style="margin-left: 44px; margin-top: 20px;display: flex;width: calc(100% - 200px)">--%>
            <%--                                    <span style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
            <%--                                            style="color: red;size: 60px">*</span>尽职照单免责和失职照单问责制定情况支撑材料：</span>--%>

            <%--                            <span id="uploadTr13"--%>
            <%--                                  <c:if test="${provinceSelectionUnit.jzSzCustomFileName!='' && provinceSelectionUnit.jzSzCustomFileName!=null }">style='display:none'</c:if>--%>
            <%--                            >--%>
            <%--&lt;%&ndash;                                                                style="width: calc(100% - 224px)&ndash;%&gt;--%>
            <%--										<c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
            <%--                                            <input type="file" id="jzSzCustomFile" value="文件上传">--%>
            <%--                                            <input type="hidden" class="form-control" name="jzSzCustomFileName"--%>
            <%--                                                   id="jzSzCustomFile_Filename"--%>
            <%--                                                   value="${provinceSelectionUnit.jzSzCustomFileName}">--%>
            <%--                                            <input type="hidden" class="form-control" name="jzSzCustomFileUrl"--%>
            <%--                                                   id="jzSzCustomFile_Fileurl"--%>
            <%--                                                   value="${provinceSelectionUnit.jzSzCustomFileUrl}">--%>
            <%--                                        </c:if>--%>
            <%--					                </span>--%>
            <%--                            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
            <%--                                <input  id="reButton13"--%>
            <%--                                        <c:if test="${provinceSelectionUnit.jzSzCustomFileName=='' || provinceSelectionUnit.jzSzCustomFileName==null }">style='display:none'</c:if>--%>
            <%--                                        class="btn btn-danger btn-xs" type="button" value="重新上传"/>--%>
            <%--                            </c:if>--%>
            <%--                        </div>--%>
            <%--                        <div id="wsc13" style="margin-left:357px;width: 380px;margin-top: 14px;border-radius: 3px;">--%>
            <%--                                     <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">--%>
            <%--                                        <c:choose>--%>
            <%--                                            <c:when test="${provinceSelectionUnit.jzSzCustomFileName!='' && provinceSelectionUnit.jzSzCustomFileName!=null }">--%>
            <%--                                           <span style=" margin-right: 8px; padding-top: 4px;">--%>
            <%--    &lt;%&ndash;                                                   ${provinceSelectionUnit.jzSzCustomFileName}&ndash;%&gt;--%>
            <%--                                               <a href="javascript:void(0)"--%>
            <%--                                                  onclick="downloadFile('${provinceSelectionUnit.jzSzCustomFileUrl}','${provinceSelectionUnit.jzSzCustomFileName}')">--%>
            <%--                                                       ${provinceSelectionUnit.jzSzCustomFileName}--%>
            <%--                                               </a>--%>
            <%--                                           </span>--%>
            <%--                                            </c:when>--%>
            <%--                                        </c:choose>--%>
            <%--                                     </span>--%>
            <%--                        </div>--%>
            <%--                    </div>--%>

            <%--                    <div style="width: 100%">--%>
            <%--                        <h4 style="font-size:14px;font-weight: 400;margin-left: 252px;margin-top: 33px">执行情况</h4>--%>
            <%--                        <div style="margin-left: 283px; margin-top: 20px">--%>
            <%--                                    <span style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
            <%--                                            style="color: red;size: 60px">*</span>是否执行：</span>--%>
            <%--                            <select style="width:380px;margin-right:5px;" name="isZhiXing"--%>
            <%--                                    id="isZhiXing">--%>
            <%--                                <option value="">--请选择--</option>--%>
            <%--                                <option value="1"--%>
            <%--                                        <c:if test="${provinceSelectionUnit.isZhiXing eq '1'}">selected</c:if>>--%>
            <%--                                    是--%>
            <%--                                </option>--%>
            <%--                                <option value="0"--%>
            <%--                                        <c:if test="${provinceSelectionUnit.isZhiXing eq '0'}">selected</c:if>>--%>
            <%--                                    否--%>
            <%--                                </option>--%>
            <%--                            </select>--%>
            <%--                        </div>--%>
            <%--                        <div style="margin-left: 283px; margin-top: 20px">--%>
            <%--                                    <span style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
            <%--                                            style="color: red;size: 60px">*</span>具体情况：</span>--%>
            <%--                            <textarea style="resize:none;width: 379px;background: #FFFFFF;border: 1px solid #DCDFE6;border-radius: 5px; height: 100px;vertical-align: top"--%>
            <%--                                      maxlength="500" placeholder="最多500字！" id="jzSzCondition"--%>
            <%--                                      name="jzSzCondition">${provinceSelectionUnit.jzSzCondition}</textarea><br/>--%>
            <%--                        </div>--%>

            <%--                        <div style="margin-left: 45px; margin-top: 20px;display: flex;width: calc(100% - 200px)">--%>
            <%--                                    <span style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
            <%--                                            style="color: red;size: 60px">*</span>尽职照单免责和失职照单问责执行情况支撑材料：</span>--%>

            <%--                            <span id="uploadTr14"--%>
            <%--                                  <c:if test="${provinceSelectionUnit.jzSzExecuteFileName!='' && provinceSelectionUnit.jzSzExecuteFileName!=null }">style='display:none'</c:if>--%>
            <%--                            >--%>
            <%--&lt;%&ndash;                                                                style="width: calc(100% - 224px)&ndash;%&gt;--%>
            <%--										<c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
            <%--                                            <input type="file" id="jzSzExecuteFile" value="文件上传">--%>
            <%--                                            <input type="hidden" class="form-control" name="jzSzExecuteFileName"--%>
            <%--                                                   id="jzSzExecuteFile_Filename"--%>
            <%--                                                   value="${provinceSelectionUnit.jzSzExecuteFileName}">--%>
            <%--                                            <input type="hidden" class="form-control" name="jzSzExecuteFileUrl"--%>
            <%--                                                   id="jzSzExecuteFile_Fileurl"--%>
            <%--                                                   value="${provinceSelectionUnit.jzSzExecuteFileUrl}">--%>
            <%--                                        </c:if>--%>
            <%--					                </span>--%>
            <%--                            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
            <%--                                <input  id="reButton14"--%>
            <%--                                        <c:if test="${provinceSelectionUnit.jzSzExecuteFileName=='' || provinceSelectionUnit.jzSzExecuteFileName==null }">style='display:none'</c:if>--%>
            <%--                                        class="btn btn-danger btn-xs" type="button" value="重新上传"/>--%>
            <%--                            </c:if>--%>
            <%--                        </div>--%>
            <%--                        <div id="wsc14" style="margin-left:357px;width: 380px;margin-top: 14px;border-radius: 3px;">--%>
            <%--                                     <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">--%>
            <%--										<c:choose>--%>
            <%--                                            <c:when test="${provinceSelectionUnit.jzSzExecuteFileName!='' && provinceSelectionUnit.jzSzExecuteFileName!=null }">--%>
            <%--										   <span style=" margin-right: 8px; padding-top: 4px;">--%>
            <%--&lt;%&ndash;                                                   ${provinceSelectionUnit.jzSzExecuteFileName}&ndash;%&gt;--%>
            <%--                                                <a href="javascript:void(0)"--%>
            <%--                                                   onclick="downloadFile('${provinceSelectionUnit.jzSzExecuteFileUrl}','${provinceSelectionUnit.jzSzExecuteFileName}')">--%>
            <%--                                                        ${provinceSelectionUnit.jzSzExecuteFileName}--%>
            <%--                                                </a>--%>
            <%--                                           </span>--%>
            <%--                                            </c:when>--%>
            <%--                                        </c:choose>--%>
            <%--								   </span>--%>
            <%--                        </div>--%>
            <%--                    </div>--%>
            <%--                </div>--%>


            <%--            </div>--%>
            <%--3333333333333333333333333333333333333333333333333333--%>
            <%--竞赛比武--%>
            <div style="margin-top: 20px; background: #fff ;padding:2px 30px 26px 21px; width: 100%">
                <h1 style="color: #31688F;font-size: 16px;font-weight: 400">三、队伍建设与管理</h1>
                <h1 style="font-size: 14px;color: #31688F;font-weight:400;height: 33px;line-height:33px;background: #F8F8F8;">（一）规范化示范建设</h1>
                <h4 style="font-size:14px;font-weight: 600;margin-left: 300px;margin-top: 20px">规范化示范建设试点工作</h4>
                <div style="padding: 20px 0;width: 100%">
                    <div>
                        <label class="zhiding"><span style="color: red;size: 60px">*</span>2021年是否出台：</label>
                        <select style="width:380px;margin-right:5px;" name="isChutai" id="isChutai">
                            <option value="">--请选择--</option>
                            <option value="1" <c:if test="${provinceSelectionUnit.isChutai eq '1'}">selected</c:if>>是
                            </option>
                            <option value="0" <c:if test="${provinceSelectionUnit.isChutai eq '0'}">selected</c:if>>否
                            </option>
                        </select>
                    </div>
                    <div class='isDwreport'>
                        <div style="margin-left:60px;width: calc(100% - 200px);margin-top: 20px;display: flex;">
                            <label class="zhiding" style="line-height: 33px;"><span style="color: red;size: 60px">*</span>工作开展情况、取得成效等工作简报或报告：</label>
                            <span id="uploadTr9"
                                  <c:if test="${provinceSelectionUnit.dwReportUrlname!='' && provinceSelectionUnit.dwReportUrlname!=null }">style='display:none'</c:if>
                            >
<%--                            style="width: calc(100% - 224px)--%>
                              <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                  <input style="width: 80px" type="file" id="dwReportFile" value="文件上传">
                                  <input type="hidden" class="form-control" name="dwReportUrlname"
                                         id="dwReportUrlname" value="${provinceSelectionUnit.dwReportUrlname}">
                                  <input type="hidden" class="form-control" name="dwReportUrl"
                                         id="dwReportUrl" value="${provinceSelectionUnit.dwReportUrl}">
                              </c:if>
                        </span>
                            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                <input  id="reButton9"
                                        <c:if test="${provinceSelectionUnit.dwReportUrlname=='' || provinceSelectionUnit.dwReportUrlname==null }">style='display:none'</c:if>
                                        class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                            </c:if>
                        </div>
                        <div id="wsc9" style="margin-left:357px;padding-top:6px;width: 380px;margin-top: 14px;border-radius: 3px;">
                        <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">
                            <c:choose>
                                <c:when test="${provinceSelectionUnit.dwReportUrlname!='' && provinceSelectionUnit.dwReportUrlname!=null }">
                                   <span style=" margin-right: 8px; padding-top: 4px;">
<%--                                               ${provinceSelectionUnit.jcBwFileName}--%>
                                       <a href="javascript:void(0)"
                                          onclick="downloadFile('${provinceSelectionUnit.dwReportUrl}','${provinceSelectionUnit.dwReportUrlname}')">
                                               ${provinceSelectionUnit.dwReportUrlname}
                                       </a>
                                   </span>
                                </c:when>
                            </c:choose>
                        </span>
                        </div>
                    </div>
                    <div class="isBuildis">
                        <div class="shangchuan" id='isfile' style="margin-left:60px;width: calc(100% - 200px);margin-top: 20px;display: flex;">
                            <label class="zhiding" style="line-height: 33px;"><span style="color: red;size: 60px" class="isBuildis">*</span>建设标准文件：</label>
                            <span id="uploadTr10"
                                  <c:if test="${provinceSelectionUnit.dwJianUrlname!='' && provinceSelectionUnit.dwJianUrlname!=null }">style='display:none'</c:if>
                            >
<%--                            style="width: calc(100% - 224px)--%>
                              <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                  <input style="width: 80px" type="file" id="dwJianFile" value="文件上传">
                                  <input type="hidden" class="form-control" name="dwJianUrlname"
                                         id="dwJianUrlname" value="${provinceSelectionUnit.dwJianUrlname}">
                                  <input type="hidden" class="form-control" name="dwJianUrl"
                                         id="dwJianUrl" value="${provinceSelectionUnit.dwJianUrl}">
                              </c:if>
                        </span>
                            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                <input  id="reButton10"
                                        <c:if test="${provinceSelectionUnit.dwJianUrlname=='' || provinceSelectionUnit.dwJianUrlname==null }">style='display:none'</c:if>
                                        class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                            </c:if>
                        </div>
                        <div id="wsc10" style="margin-left:357px;padding-top:6px;width: 380px;margin-top: 14px;border-radius: 3px;">
                        <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">
                            <c:choose>
                                <c:when test="${provinceSelectionUnit.dwJianUrlname!='' && provinceSelectionUnit.dwJianUrlname!=null }">
                                   <span style=" margin-right: 8px; padding-top: 4px;">
<%--                                               ${provinceSelectionUnit.jcBwFileName}--%>
                                       <a href="javascript:void(0)"
                                          onclick="downloadFile('${provinceSelectionUnit.dwJianUrl}','${provinceSelectionUnit.dwJianUrlname}')">
                                               ${provinceSelectionUnit.dwJianUrlname}
                                       </a>
                                   </span>
                                </c:when>
                            </c:choose>
                        </span>
                        </div>
                    </div>
                </div>
                <h4 style="font-size:14px;font-weight: 600;margin-left: 300px;margin-top: 20px">开展执法稽查</h4>
                <div style="margin-top: 33px">
                    <label style="font-size: 14px; color: #333333; font-weight: 400"><span style="color: red;size: 60px">*</span>是否开展：</label>
                    <select style="width:380px;margin-right:5px;" name="isLawcheck"
                            id="isLawcheck">
                        <option value="">--请选择--</option>
                        <option value="1"
                                <c:if test="${provinceSelectionUnit.isLawcheck eq '1'}">selected</c:if>>
                            是
                        </option>
                        <option value="0"
                                <c:if test="${provinceSelectionUnit.isLawcheck eq '0'}">selected</c:if>>
                            否
                        </option>
                    </select>
                </div>

                <div style="margin-left: 60px; margin-top: 20px;display: flex;width: calc(100% - 200px)">
                    <label style="line-height:44px;font-size: 14px; color: #333333; font-weight: 400"><span style="color: red;size: 60px" class="isLawcheckShow">*</span>稽查计划：</label>
                    <span id="uploadTr11"
                          <c:if test="${provinceSelectionUnit.lawCheckplanUrlname!='' && provinceSelectionUnit.lawCheckplanUrlname!=null }">style='display:none'</c:if>
                    >
    <%--                                                            style="width: calc(100% - 224px)--%>
    <%--                                                            style="width: calc(100% - 224px)--%>
                                <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                    <input type="file" id="lawCheckplanFile" value="文件上传">
                                    <input type="hidden" class="form-control" name="lawCheckplanUrlname"
                                           id="lawCheckplanUrlname"
                                           value="${provinceSelectionUnit.lawCheckplanUrlname}">
                                    <input type="hidden" class="form-control" name="lawCheckplanUrl"
                                           id="lawCheckplanUrl"
                                           value="${provinceSelectionUnit.lawCheckplanUrl}">
                                </c:if>
                            </span>
                    <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                        <input  id="reButton11"
                                <c:if test="${provinceSelectionUnit.lawCheckplanUrlname=='' || provinceSelectionUnit.lawCheckplanUrlname==null }">style='display:none'</c:if>
                                class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                    </c:if>
                </div>
                <div id="wsc11" style="margin-left:357px;width: 380px;margin-top: 14px;border-radius: 3px;">
                    <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">
                        <c:choose>
                            <c:when test="${provinceSelectionUnit.lawCheckplanUrlname!='' && provinceSelectionUnit.lawCheckplanUrlname!=null }">
                                   <span style=" margin-right: 8px; padding-top: 4px;">
<%--                                                   ${provinceSelectionUnit.specificConditionFileName}--%>
                                       <a href="javascript:void(0)"
                                          onclick="downloadFile('${provinceSelectionUnit.lawCheckplanUrl}','${provinceSelectionUnit.lawCheckplanUrlname}')">
                                               ${provinceSelectionUnit.lawCheckplanUrlname}
                                       </a>
                                   </span>
                            </c:when>
                        </c:choose>
                    </span>
                </div>

                <div style="margin-left: 60px; margin-top: 20px;display: flex;width: calc(100% - 200px)">
                    <label style="line-height:44px;font-size: 14px; color: #333333; font-weight: 400"><span
                            style="color: red;size: 60px" class="isLawcheckShow">*</span>稽查工作报告：</label>
                    <span id="uploadTr12"
                          <c:if test="${provinceSelectionUnit.lawCheckreportUrlname!='' && provinceSelectionUnit.lawCheckreportUrlname!=null }">style='display:none'</c:if>
                    >
    <%--                                                            style="width: calc(100% - 224px)--%>
                                <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                    <input type="file" id="lawCheckreportFile" value="文件上传">
                                    <input type="hidden" class="form-control" name="lawCheckreportUrlname"
                                           id="lawCheckreportUrlname"
                                           value="${provinceSelectionUnit.lawCheckreportUrlname}">
                                    <input type="hidden" class="form-control" name="lawCheckreportUrl"
                                           id="lawCheckreportUrl"
                                           value="${provinceSelectionUnit.lawCheckreportUrl}">
                                </c:if>
                            </span>
                    <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                        <input  id="reButton12"
                                <c:if test="${provinceSelectionUnit.lawCheckreportUrlname=='' || provinceSelectionUnit.lawCheckreportUrlname==null }">style='display:none'</c:if>
                                class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                    </c:if>
                </div>
                <div id="wsc12" style="margin-left:357px;width: 380px;margin-top: 14px;border-radius: 3px;">
                    <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">
                        <c:choose>
                            <c:when test="${provinceSelectionUnit.lawCheckreportUrlname!='' && provinceSelectionUnit.lawCheckreportUrlname!=null }">
                                   <span style=" margin-right: 8px; padding-top: 4px;">
<%--                                                   ${provinceSelectionUnit.specificConditionFileName}--%>
                                       <a href="javascript:void(0)"
                                          onclick="downloadFile('${provinceSelectionUnit.lawCheckreportUrl}','${provinceSelectionUnit.lawCheckreportUrlname}')">
                                               ${provinceSelectionUnit.lawCheckreportUrlname}
                                       </a>
                                   </span>
                            </c:when>
                        </c:choose>
                    </span>
                </div>

                <h4 style="font-size:14px;font-weight: 600;margin-left: 300px;margin-top: 50px">统一证件、着装</h4>
                <div class="shifou">
                    <label class="zhiding"><span style="color: red;size: 60px">*</span>是否完成统一着装：</label>
                    <select style="width:380px;margin-right:5px;" name="isUnifyattire" id="isUnifyattire">
                        <option value="">--请选择--</option>
                        <option value="1" <c:if test="${provinceSelectionUnit.isUnifyattire eq '1'}">selected</c:if>>是
                        </option>
                        <option value="0" <c:if test="${provinceSelectionUnit.isUnifyattire eq '0'}">selected</c:if>>否
                        </option>
                    </select>
                </div>
                <div class="shifou">
                    <label class="zhiding"><span style="color: red;size: 60px">*</span>是否完成统一证件：</label>
                    <select style="width:380px;margin-right:5px;" name="isUnifycertificate" id="isUnifycertificate">
                        <option value="">--请选择--</option>
                        <option value="1" <c:if test="${provinceSelectionUnit.isUnifycertificate eq '1'}">selected</c:if>>是
                        </option>
                        <option value="0" <c:if test="${provinceSelectionUnit.isUnifycertificate eq '0'}">selected</c:if>>否
                        </option>
                    </select>
                </div>
                <div style="margin-left: 60px; margin-top: 20px;display: flex;width: calc(100% - 200px)">
                    <label class="zhiding" style="line-height: 33px;"><span style="color: red;size: 60px">*</span>统一证件、着装支撑材料：</label>
                    <span id="uploadTr13"
                          <c:if test="${provinceSelectionUnit.dwSupUrlname!='' && provinceSelectionUnit.dwSupUrlname!=null }">style='display:none'</c:if>
                    >
<%--                            style="width: calc(100% - 224px)--%>
                              <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                  <input style="width: 80px" type="file" id="dwSupFile" value="文件上传">
                                  <input type="hidden" class="form-control" name="dwSupUrlname"
                                         id="dwSupUrlname" value="${provinceSelectionUnit.dwSupUrlname}">
                                  <input type="hidden" class="form-control" name="dwSupUrl"
                                         id="dwSupUrl" value="${provinceSelectionUnit.dwSupUrl}">
                              </c:if>
                        </span>
                    <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                        <input id="reButton13"
                                <c:if test="${provinceSelectionUnit.dwSupUrlname=='' || provinceSelectionUnit.dwSupUrlname==null }">style='display:none'</c:if>
                                class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                    </c:if>
                </div>
                <p style="color: #FB3301;width: 526px;font-size:12px;line-height: 20px;font-weight: 400;margin-left: 600px">统一着装、统一证件的完成情况报告，报告中需体现完成率、着装及持证情况等内容。</p>
                <div id="wsc13" style="margin-left:357px;padding-top:6px;width: 380px;margin-top: 14px;border-radius: 3px;">
                    <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">
                        <c:choose>
                            <c:when test="${provinceSelectionUnit.dwSupUrlname!='' && provinceSelectionUnit.dwSupUrlname!=null }">
                               <span style=" margin-right: 8px; padding-top: 4px;">
<%--                                               ${provinceSelectionUnit.jcBwFileName}--%>
                                   <a href="javascript:void(0)"
                                      onclick="downloadFile('${provinceSelectionUnit.dwSupUrl}','${provinceSelectionUnit.dwSupUrlname}')">
                                           ${provinceSelectionUnit.dwSupUrlname}
                                   </a>
                               </span>
                            </c:when>
                        </c:choose>
                    </span>
                </div>
                <h1 style="font-size: 14px;color: #31688F;font-weight:400;height: 33px;line-height:33px;background: #F8F8F8;">（二）制度建设情况</h1>
                <p style="color: #FB3301;width: 526px;font-size:12px;line-height: 20px;font-weight: 400;margin-left: 600px">以下六个材料均需提供对应制度文件的pdf盖章版扫描件。</p>
                <div class="shangchuan">
                    <label class="zhiding" style="line-height: 33px;">区域交叉检查制度：</label>
                    <span id="uploadTr14"
                          <c:if test="${provinceSelectionUnit.dwCrossUrlname!='' && provinceSelectionUnit.dwCrossUrlname!=null }">style='display:none'</c:if>
                    >
<%--                            style="width: calc(100% - 224px)--%>
                              <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                  <input style="width: 80px" type="file" id="dwCrossFile" value="文件上传">
                                  <input type="hidden" class="form-control" name="dwCrossUrlname"
                                         id="dwCrossUrlname" value="${provinceSelectionUnit.dwCrossUrlname}">
                                  <input type="hidden" class="form-control" name="dwCrossUrl"
                                         id="dwCrossUrl" value="${provinceSelectionUnit.dwCrossUrl}">
                              </c:if>
                        </span>
                    <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                        <input  id="reButton14"
                                <c:if test="${provinceSelectionUnit.dwCrossUrlname=='' || provinceSelectionUnit.dwCrossUrlname==null }">style='display:none'</c:if>
                                class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                    </c:if>
                </div>
                <div id="wsc14" style="margin-left:357px;padding-top:6px;width: 380px;margin-top: 14px;border-radius: 3px;">
                    <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">
                        <c:choose>
                            <c:when test="${provinceSelectionUnit.dwCrossUrlname!='' && provinceSelectionUnit.dwCrossUrlname!=null }">
                               <span style=" margin-right: 8px; padding-top: 4px;">
<%--                                               ${provinceSelectionUnit.jcBwFileName}--%>
                                   <a href="javascript:void(0)"
                                      onclick="downloadFile('${provinceSelectionUnit.dwCrossUrl}','${provinceSelectionUnit.dwCrossUrlname}')">
                                           ${provinceSelectionUnit.dwCrossUrlname}
                                   </a>
                               </span>
                            </c:when>
                        </c:choose>
                    </span>
                </div>

                <div class="shangchuan">
                    <label class="zhiding" style="line-height: 33px;">专案查办制度：</label>
                    <span id="uploadTr15"
                          <c:if test="${provinceSelectionUnit.dwTaskUrlname!='' && provinceSelectionUnit.dwTaskUrlname!=null }">style='display:none'</c:if>
                    >
<%--                            style="width: calc(100% - 224px)--%>
                              <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                  <input style="width: 80px" type="file" id="dwTaskFile" value="文件上传">
                                  <input type="hidden" class="form-control" name="dwTaskUrlname"
                                         id="dwTaskUrlname" value="${provinceSelectionUnit.dwTaskUrlname}">
                                  <input type="hidden" class="form-control" name="dwTaskUrl"
                                         id="dwTaskUrl" value="${provinceSelectionUnit.dwTaskUrl}">
                              </c:if>
                        </span>
                    <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                        <input  id="reButton15"
                                <c:if test="${provinceSelectionUnit.dwTaskUrlname=='' || provinceSelectionUnit.dwTaskUrlname==null }">style='display:none'</c:if>
                                class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                    </c:if>
                </div>
                <div id="wsc15" style="margin-left:357px;padding-top:6px;width: 380px;margin-top: 14px;border-radius: 3px;">
                    <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">
                        <c:choose>
                            <c:when test="${provinceSelectionUnit.dwTaskUrlname!='' && provinceSelectionUnit.dwTaskUrlname!=null }">
                               <span style=" margin-right: 8px; padding-top: 4px;">
<%--                                               ${provinceSelectionUnit.jcBwFileName}--%>
                                   <a href="javascript:void(0)"
                                      onclick="downloadFile('${provinceSelectionUnit.dwTaskUrl}','${provinceSelectionUnit.dwTaskUrlname}')">
                                           ${provinceSelectionUnit.dwTaskUrlname}
                                   </a>
                               </span>
                            </c:when>
                        </c:choose>
                    </span>
                </div>
                <div class="shangchuan">
                    <label class="zhiding" style="line-height: 33px;">部门协调联动机制：</label>
                    <span id="uploadTr16"
                          <c:if test="${provinceSelectionUnit.dwLianUrlname!='' && provinceSelectionUnit.dwLianUrlname!=null }">style='display:none'</c:if>
                    >
<%--                            style="width: calc(100% - 224px)--%>
                              <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                  <input style="width: 80px" type="file" id="dwLianFile" value="文件上传">
                                  <input type="hidden" class="form-control" name="dwLianUrlname"
                                         id="dwLianUrlname" value="${provinceSelectionUnit.dwLianUrlname}">
                                  <input type="hidden" class="form-control" name="dwLianUrl"
                                         id="dwLianUrl" value="${provinceSelectionUnit.dwLianUrl}">
                              </c:if>
                        </span>
                    <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                        <input  id="reButton16"
                                <c:if test="${provinceSelectionUnit.dwLianUrlname=='' || provinceSelectionUnit.dwLianUrlname==null }">style='display:none'</c:if>
                                class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                    </c:if>
                </div>
                <div id="wsc16" style="margin-left:357px;padding-top:6px;width: 380px;margin-top: 14px;border-radius: 3px;">
                    <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">
                        <c:choose>
                            <c:when test="${provinceSelectionUnit.dwLianUrlname!='' && provinceSelectionUnit.dwLianUrlname!=null }">
                               <span style=" margin-right: 8px; padding-top: 4px;">
<%--                                               ${provinceSelectionUnit.jcBwFileName}--%>
                                   <a href="javascript:void(0)"
                                      onclick="downloadFile('${provinceSelectionUnit.dwLianUrl}','${provinceSelectionUnit.dwLianUrlname}')">
                                           ${provinceSelectionUnit.dwLianUrlname}
                                   </a>
                               </span>
                            </c:when>
                        </c:choose>
                    </span>
                </div>
                <div class="shangchuan">
                    <label class="zhiding" style="line-height: 33px;">第三方辅助执法制度：</label>
                    <span id="uploadTr17"
                          <c:if test="${provinceSelectionUnit.dwFulawUrlname!='' && provinceSelectionUnit.dwFulawUrlname!=null }">style='display:none'</c:if>
                    >
<%--                            style="width: calc(100% - 224px)--%>
                              <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                  <input style="width: 80px" type="file" id="dwFulawFile" value="文件上传">
                                  <input type="hidden" class="form-control" name="dwFulawUrlname"
                                         id="dwFulawUrlname" value="${provinceSelectionUnit.dwFulawUrlname}">
                                  <input type="hidden" class="form-control" name="dwFulawUrl"
                                         id="dwFulawUrl" value="${provinceSelectionUnit.dwFulawUrl}">
                              </c:if>
                        </span>
                    <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                        <input  id="reButton17"
                                <c:if test="${provinceSelectionUnit.dwFulawUrlname=='' || provinceSelectionUnit.dwFulawUrlname==null }">style='display:none'</c:if>
                                class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                    </c:if>
                </div>
                <div id="wsc17" style="margin-left:357px;padding-top:6px;width: 380px;margin-top: 14px;border-radius: 3px;">
                    <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">
                        <c:choose>
                            <c:when test="${provinceSelectionUnit.dwFulawUrlname!='' && provinceSelectionUnit.dwFulawUrlname!=null }">
                               <span style=" margin-right: 8px; padding-top: 4px;">
<%--                                               ${provinceSelectionUnit.jcBwFileName}--%>
                                   <a href="javascript:void(0)"
                                      onclick="downloadFile('${provinceSelectionUnit.dwFulawUrl}','${provinceSelectionUnit.dwFulawUrlname}')">
                                           ${provinceSelectionUnit.dwFulawUrlname}
                                   </a>
                               </span>
                            </c:when>
                        </c:choose>
                    </span>
                </div>

                <div class="shangchuan">
                    <label class="zhiding" style="line-height: 33px;">执法普法制度：</label>
                    <span id="uploadTr18"
                          <c:if test="${provinceSelectionUnit.dwLawUrlname!='' && provinceSelectionUnit.dwLawUrlname!=null }">style='display:none'</c:if>
                    >
<%--                            style="width: calc(100% - 224px)--%>
                              <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                  <input style="width: 80px" type="file" id="dwLawFile" value="文件上传">
                                  <input type="hidden" class="form-control" name="dwLawUrlname"
                                         id="dwLawUrlname" value="${provinceSelectionUnit.dwLawUrlname}">
                                  <input type="hidden" class="form-control" name="dwLawUrl"
                                         id="dwLawUrl" value="${provinceSelectionUnit.dwLawUrl}">
                              </c:if>
                        </span>
                    <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                        <input  id="reButton18"
                                <c:if test="${provinceSelectionUnit.dwLawUrlname=='' || provinceSelectionUnit.dwLawUrlname==null }">style='display:none'</c:if>
                                class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                    </c:if>
                </div>
                <div id="wsc18" style="margin-left:357px;padding-top:6px;width: 380px;margin-top: 14px;border-radius: 3px;">
                    <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">
                        <c:choose>
                            <c:when test="${provinceSelectionUnit.dwLawUrlname!='' && provinceSelectionUnit.dwLawUrlname!=null }">
                               <span style=" margin-right: 8px; padding-top: 4px;">
<%--                                               ${provinceSelectionUnit.jcBwFileName}--%>
                                   <a href="javascript:void(0)"
                                      onclick="downloadFile('${provinceSelectionUnit.dwLawUrl}','${provinceSelectionUnit.dwLawUrlname}')">
                                           ${provinceSelectionUnit.dwLawUrlname}
                                   </a>
                               </span>
                            </c:when>
                        </c:choose>
                    </span>
                </div>

                <div class="shangchuan">
                    <label class="zhiding" style="line-height: 33px;">轻微违法免罚制度：</label>
                    <span id="uploadTr19"
                          <c:if test="${provinceSelectionUnit.dwTinylawUrlname!='' && provinceSelectionUnit.dwTinylawUrlname!=null }">style='display:none'</c:if>
                    >
<%--                            style="width: calc(100% - 224px)--%>
                              <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                  <input style="width: 80px" type="file" id="dwTinylawFile" value="文件上传">
                                  <input type="hidden" class="form-control" name="dwTinylawUrlname"
                                         id="dwTinylawUrlname" value="${provinceSelectionUnit.dwTinylawUrlname}">
                                  <input type="hidden" class="form-control" name="dwTinylawUrl"
                                         id="dwTinylawUrl" value="${provinceSelectionUnit.dwTinylawUrl}">
                              </c:if>
                        </span>
                    <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                        <input  id="reButton19"
                                <c:if test="${provinceSelectionUnit.dwTinylawUrlname=='' || provinceSelectionUnit.dwTinylawUrlname==null }">style='display:none'</c:if>
                                class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                    </c:if>
                </div>
                <div id="wsc19" style="margin-left:357px;padding-top:6px;width: 380px;margin-top: 14px;border-radius: 3px;">
                    <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">
                        <c:choose>
                            <c:when test="${provinceSelectionUnit.dwTinylawUrlname!='' && provinceSelectionUnit.dwTinylawUrlname!=null }">
                               <span style=" margin-right: 8px; padding-top: 4px;">
<%--                                               ${provinceSelectionUnit.jcBwFileName}--%>
                                   <a href="javascript:void(0)"
                                      onclick="downloadFile('${provinceSelectionUnit.dwTinylawUrl}','${provinceSelectionUnit.dwTinylawUrlname}')">
                                           ${provinceSelectionUnit.dwTinylawUrlname}
                                   </a>
                               </span>
                            </c:when>
                        </c:choose>
                    </span>
                </div>
                <h1 style="font-size: 14px;color: #31688F;font-weight:400;height: 33px;line-height:33px;background: #F8F8F8;">（二）制度落实情况</h1>
                <h4 style="font-size:14px;font-weight: 600;margin-left:300px;margin-top: 20px">举报奖励制度执行情况</h4>
                <div style="margin-top: 33px">
                    <label style="font-size: 14px; color: #333333; font-weight: 400"><span style="color: red;size: 60px">*</span>是否组织开展政策宣传、解读，发布典型案例：</label>
                    <select style="width:380px;margin-right:5px;" name="isTypicalcase"
                            id="isTypicalcase"  class="isTypical">
                        <option value="">--请选择--</option>
                        <option value="1"
                                <c:if test="${provinceSelectionUnit.isTypicalcase eq '1'}">selected</c:if>>
                            是
                        </option>
                        <option value="0"
                                <c:if test="${provinceSelectionUnit.isTypicalcase eq '0'}">selected</c:if>>
                            否
                        </option>
                    </select>
                </div>
                <div style="margin-top: 20px">
                    <label style="font-size: 14px; color: #333333; font-weight: 400"><span style="color: red;size: 60px">*</span>各地市是否实现举报奖励制度全覆盖：</label>
                    <select style="width:380px;margin-right:5px;" name="isCover" id="isCover" class="isTypical">
                        <option value="">--请选择--</option>
                        <option value="1"
                                <c:if test="${provinceSelectionUnit.isCover eq '1'}">selected</c:if>>是
                        </option>
                        <option value="0"
                                <c:if test="${provinceSelectionUnit.isCover eq '0'}">selected</c:if>>否
                        </option>
                    </select>
                </div>
                <div class="shangchuan">
                    <label class="zhiding" style="line-height: 33px;"><span style="color: red;size: 60px" class="isTypicalcase">*</span>监督执法正面清单制度支撑材料：</label>
                    <span id="uploadTr20"
                          <c:if test="${provinceSelectionUnit.systemSupUrlname!='' && provinceSelectionUnit.systemSupUrlname!=null }">style='display:none'</c:if>
                    >
<%--                            style="width: calc(100% - 224px)--%>
                              <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                  <input style="width: 80px" type="file" id="systemSupFile" value="文件上传">
                                  <input type="hidden" class="form-control" name="systemSupUrlname"
                                         id="systemSupUrlname" value="${provinceSelectionUnit.systemSupUrlname}">
                                  <input type="hidden" class="form-control" name="systemSupUrl"
                                         id="systemSupUrl" value="${provinceSelectionUnit.systemSupUrl}">
                              </c:if>
                        </span>
                    <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                        <input  id="reButton20"
                                <c:if test="${provinceSelectionUnit.systemSupUrlname=='' || provinceSelectionUnit.systemSupUrlname==null }">style='display:none'</c:if>
                                class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                    </c:if>
                </div>

                <div id="wsc20" style="margin-left:357px;padding-top:6px;width: 380px;margin-top: 14px;border-radius: 3px;">
                    <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">
                        <c:choose>
                            <c:when test="${provinceSelectionUnit.systemSupUrlname!='' && provinceSelectionUnit.systemSupUrlname!=null }">
                               <span style=" margin-right: 8px; padding-top: 4px;">
<%--                                               ${provinceSelectionUnit.jcBwFileName}--%>
                                   <a href="javascript:void(0)"
                                      onclick="downloadFile('${provinceSelectionUnit.systemSupUrl}','${provinceSelectionUnit.systemSupUrlname}')">
                                           ${provinceSelectionUnit.systemSupUrlname}
                                   </a>
                               </span>
                            </c:when>
                        </c:choose>
                    </span>
                </div>
                <p style="color: #FB3301;margin-left: 600px;font-size: 12px;line-height: 20px;">1.举报奖励制度执行情况的文字说明；</p>
                <p style="color: #FB3301;margin-left: 600px;font-size: 12px;line-height: 20px;">2.开展宣传、解读的方案、通知或者省级人员现场指导的宣传稿；</p>
                <p style="color: #FB3301;margin-left: 600px;font-size: 12px;line-height: 20px;">3.典型案例发布的网页截图。</p>

                <h4 style="font-size:14px;font-weight: 600;margin-left: 300px;margin-top: 30px">监督执法正面清单制度执行情况</h4>
                <div style="margin-top: 50px">
                    <label style="font-size: 14px; color: #333333; font-weight: 400"><span style="color: red;size: 60px">*</span> 是否辖区内所有地市级生态环境部门均公布正面清单企业名单，并实施动态管理 ：</label>
                    <select style="width:380px;margin-right:5px;" name="isPositivelistManage" id="isPositivelistManage" class="isPositivelistWarnShow">
                        <option value="">--请选择--</option>
                        <option value="1"
                                <c:if test="${provinceSelectionUnit.isPositivelistManage eq '1'}">selected</c:if>>是
                        </option>
                        <option value="0"
                                <c:if test="${provinceSelectionUnit.isPositivelistManage eq '0'}">selected</c:if>>否
                        </option>
                    </select>
                </div>
                <div style="margin-top: 20px">
                    <label style="font-size: 14px; color: #333333;font-weight: 400"><span style="color: red;size: 60px">*</span>是否指导组织地市级生态环境部门通过座谈、宣讲、培训等方式对正面清单企业开展帮扶指导和提醒预警 ：</label>
                    <select style="width:380px;margin-right:5px;position: relative;top:-5px" name="isPositivelistWarn" id="isPositivelistWarn" class="isPositivelistWarnShow">
                        <option value="">--请选择--</option>
                        <option value="1"
                                <c:if test="${provinceSelectionUnit.isPositivelistWarn eq '1'}">selected</c:if>>是
                        </option>
                        <option value="0"
                                <c:if test="${provinceSelectionUnit.isPositivelistWarn eq '0'}">selected</c:if>>否
                        </option>
                    </select>
                </div>
                <div class="shangchuan">
                    <label class="zhiding" style="line-height: 33px;"><span style="color: red;size: 60px" class="isPositivelistSup">*</span>监督执法正面清单制度支撑材料：</label>
                    <span id="uploadTr21"
                          <c:if test="${provinceSelectionUnit.positivelistSupUrlname!='' && provinceSelectionUnit.positivelistSupUrlname!=null }">style='display:none'</c:if>
                    >
<%--                            style="width: calc(100% - 224px)--%>
                              <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                  <input style="width: 80px" type="file" id="positivelistSupFile" value="文件上传">
                                  <input type="hidden" class="form-control" name="positivelistSupUrlname"
                                         id="positivelistSupUrlname" value="${provinceSelectionUnit.positivelistSupUrlname}">
                                  <input type="hidden" class="form-control" name="positivelistSupUrl"
                                         id="positivelistSupUrl" value="${provinceSelectionUnit.positivelistSupUrl}">
                              </c:if>
                        </span>
                    <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                        <input  id="reButton21"
                                <c:if test="${provinceSelectionUnit.positivelistSupUrlname=='' || provinceSelectionUnit.positivelistSupUrlname==null }">style='display:none'</c:if>
                                class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                    </c:if>
                </div>

                <div id="wsc21" style="margin-left:357px;padding-top:6px;width: 380px;margin-top: 14px;border-radius: 3px;">
                    <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">
                        <c:choose>
                            <c:when test="${provinceSelectionUnit.positivelistSupUrlname!='' && provinceSelectionUnit.positivelistSupUrlname!=null }">
                               <span style=" margin-right: 8px; padding-top: 4px;">
<%--                                               ${provinceSelectionUnit.jcBwFileName}--%>
                                   <a href="javascript:void(0)"
                                      onclick="downloadFile('${provinceSelectionUnit.positivelistSupUrl}','${provinceSelectionUnit.positivelistSupUrlname}')">
                                           ${provinceSelectionUnit.positivelistSupUrlname}
                                   </a>
                               </span>
                            </c:when>
                        </c:choose>
                    </span>
                </div>
                <p style="color: #FB3301;margin-left: 600px;font-size: 12px;line-height: 20px;">1.执行情况的文字说明材料；</p>
                <p style="color: #FB3301;margin-left: 600px;font-size: 12px;line-height: 20px;">2.公布清单的网页截图。如省级统一公布，则提供省级网页截图；如各地市分别公布，则提供所有地市公布的网页截图；</p>
                <p style="color: #FB3301;margin-left: 600px;font-size: 12px;line-height: 20px;">3.开展帮扶指导和提醒预警的通知、方案或者省级人员现场指导的宣传稿；</p>
                <h4 style="font-size:14px;font-weight: 600;margin-left: 300px;margin-bottom:30px;margin-top:30px">“双随机、一公开”监管工作执行情况。</h4>
                <div style="margin-top: 20px">
                    <label style="font-size: 14px; color: #333333; font-weight: 400"><span style="color: red;size: 60px">*</span>“双随机”抽查结果公开率：</label>
                    <input type="text" id="randomRate" name="randomRate" placeholder="请输入数字" height="39px" value="${provinceSelectionUnit.randomRate}" >%
                    <%--                    <select style="width:380px;margin-right:5px;" name="randomRate" id="randomRate">--%>
                    <%--                        <option value="">--请选择--</option>--%>
                    <%--                        <option value="1"--%>
                    <%--                                <c:if test="${provinceSelectionUnit.isCarryOut eq '1'}">selected</c:if>>是--%>
                    <%--                        </option>--%>
                    <%--                        <option value="0"--%>
                    <%--                                <c:if test="${provinceSelectionUnit.isCarryOut eq '0'}">selected</c:if>>否--%>
                    <%--                        </option>--%>
                    <%--                    </select>--%>
                </div>

                <div class="shangchuan">
                    <label class="zhiding" style="line-height: 33px;"><span style="color: red;size: 60px">*</span>双随机、一公开”监管工作执行情况支撑材料：</label>
                    <span id="uploadTr22"
                          <c:if test="${provinceSelectionUnit.randomsupUrlname!='' && provinceSelectionUnit.randomsupUrlname!=null }">style='display:none'</c:if>
                    >
<%--                            style="width: calc(100% - 224px)--%>
                              <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                  <input style="width: 80px" type="file" id="randomsupFile" value="文件上传">
                                  <input type="hidden" class="form-control" name="randomsupUrlname"
                                         id="randomsupUrlname" value="${provinceSelectionUnit.randomsupUrlname}">
                                  <input type="hidden" class="form-control" name="randomsupUrl"
                                         id="randomsupUrl" value="${provinceSelectionUnit.randomsupUrl}">
                              </c:if>
                        </span>
                    <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                        <input  id="reButton22"
                                <c:if test="${provinceSelectionUnit.randomsupUrlname=='' || provinceSelectionUnit.randomsupUrlname==null }">style='display:none'</c:if>
                                class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                    </c:if>
                </div>

                <div id="wsc22" style="margin-left:357px;padding-top:6px;width: 380px;margin-top: 14px;border-radius: 3px;">
                    <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">
                        <c:choose>
                            <c:when test="${provinceSelectionUnit.randomsupUrlname!='' && provinceSelectionUnit.randomsupUrlname!=null }">
                               <span style=" margin-right: 8px; padding-top: 4px;">
<%--                                               ${provinceSelectionUnit.jcBwFileName}--%>
                                   <a href="javascript:void(0)"
                                      onclick="downloadFile('${provinceSelectionUnit.randomsupUrl}','${provinceSelectionUnit.randomsupUrlname}')">
                                           ${provinceSelectionUnit.randomsupUrlname}
                                   </a>
                               </span>
                            </c:when>
                        </c:choose>
                    </span>
                </div>
                <p style="color: #FB3301;margin-left: 600px;font-size: 12px;line-height: 20px;">1.文字说明材料；</p>
                <p style="color: #FB3301;margin-left: 600px;font-size: 12px;line-height: 20px;">2.随机抽查结果公开的网页截图。</p>
                <h4 style="font-size:14px;font-weight: 600;margin-left: 300px;margin-bottom:30px;margin-top:30px">典型案例制度执行情况</h4>
                <div style="margin-top: 20px">
                    <label style="font-size: 14px; color: #333333; font-weight: 400"><span style="color: red;size: 60px">*</span>省级生态环境部门全年组织发布典型案例批次：</label>
                    <input type="text" id="typicalcasePici" name="typicalcasePici" placeholder="请输入典型案例" value="${provinceSelectionUnit.typicalcasePici}"  height="39px" />
                </div>
                <div class="shangchuan">
                    <label class="zhiding" style="line-height: 33px;"><span style="color: red;size: 60px">*</span>发布典型案例的通知或者公开的链接：</label>
                    <span id="uploadTr23"
                          <c:if test="${provinceSelectionUnit.typicalcaseUrlname!='' && provinceSelectionUnit.typicalcaseUrlname!=null }">style='display:none'</c:if>
                    >
<%--                            style="width: calc(100% - 224px)--%>
                              <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                  <input style="width: 80px" type="file" id="typicalcaseFile" value="文件上传">
                                  <input type="hidden" class="form-control" name="typicalcaseUrlname"
                                         id="typicalcaseUrlname" value="${provinceSelectionUnit.typicalcaseUrlname}">
                                  <input type="hidden" class="form-control" name="typicalcaseUrl"
                                         id="typicalcaseUrl" value="${provinceSelectionUnit.typicalcaseUrl}">
                              </c:if>
                        </span>
                    <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                        <input  id="reButton23"
                                <c:if test="${provinceSelectionUnit.typicalcaseUrlname=='' || provinceSelectionUnit.typicalcaseUrlname==null }">style='display:none'</c:if>
                                class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                    </c:if>
                </div>

                <div id="wsc23" style="margin-left:357px;padding-top:6px;width: 380px;margin-top: 14px;border-radius: 3px;">
                    <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">
                        <c:choose>
                            <c:when test="${provinceSelectionUnit.typicalcaseUrlname!='' && provinceSelectionUnit.typicalcaseUrlname!=null }">
                               <span style=" margin-right: 8px; padding-top: 4px;">
<%--                                               ${provinceSelectionUnit.jcBwFileName}--%>
                                   <a href="javascript:void(0)"
                                      onclick="downloadFile('${provinceSelectionUnit.typicalcaseUrl}','${provinceSelectionUnit.typicalcaseUrlname}')">
                                           ${provinceSelectionUnit.typicalcaseUrlname}
                                   </a>
                               </span>
                            </c:when>
                        </c:choose>
                    </span>
                </div>

                <h4 style="font-size:14px;font-weight: 600;margin-left:300px;margin-top: 30px;margin-bottom: 30px">执法普法制度执行情况</h4>
                <div style="margin-top: 20px">
                    <label style="font-size: 14px; color: #333333;text-align-last: right;font-weight: 400"><span style="color: red;size: 60px">*</span>是否指导所有地市级生态环境部门开展针对中小企业重点执法帮扶、普法培训、“送法入企”活动：</label>
                    <select style="width:380px;margin-right:5px;position: relative;top:-5px" name="isGuide" id="isGuide">
                        <option value="">--请选择--</option>
                        <option value="1"
                                <c:if test="${provinceSelectionUnit.isGuide eq '1'}">selected</c:if>>是
                        </option>
                        <option value="0"
                                <c:if test="${provinceSelectionUnit.isGuide eq '0'}">selected</c:if>>否
                        </option>
                    </select>
                </div>
                <div class="shangchuan">
                    <label class="zhiding" style="line-height: 33px;"><span style="color: red;size: 60px" class="isGuideShow">*</span>执法普法制度执行情况支撑材料：</label>
                    <span id="uploadTr24"
                          <c:if test="${provinceSelectionUnit.guideUrlname!='' && provinceSelectionUnit.guideUrlname!=null }">style='display:none'</c:if>
                    >
<%--                            style="width: calc(100% - 224px)--%>
                              <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                  <input style="width: 80px" type="file" id="guideFile" value="文件上传">
                                  <input type="hidden" class="form-control" name="guideUrlname"
                                         id="guideUrlname" value="${provinceSelectionUnit.guideUrlname}">
                                  <input type="hidden" class="form-control" name="guideUrl"
                                         id="guideUrl" value="${provinceSelectionUnit.guideUrl}">
                              </c:if>
                        </span>
                    <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                        <input  id="reButton24"
                                <c:if test="${provinceSelectionUnit.guideUrlname=='' || provinceSelectionUnit.guideUrlname==null }">style='display:none'</c:if>
                                class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                    </c:if>
                </div>

                <div id="wsc24" style="margin-left:357px;padding-top:6px;width: 380px;margin-top: 14px;border-radius: 3px;">
                    <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">
                        <c:choose>
                            <c:when test="${provinceSelectionUnit.guideUrlname!='' && provinceSelectionUnit.guideUrlname!=null }">
                               <span style=" margin-right: 8px; padding-top: 4px;">
<%--                                               ${provinceSelectionUnit.jcBwFileName}--%>
                                   <a href="javascript:void(0)"
                                      onclick="downloadFile('${provinceSelectionUnit.guideUrl}','${provinceSelectionUnit.guideUrlname}')">
                                           ${provinceSelectionUnit.guideUrlname}
                                   </a>
                               </span>
                            </c:when>
                        </c:choose>
                    </span>
                </div>
                <p style="color: #FB3301;margin-left: 600px;font-size: 12px;line-height: 20px;">1.执行情况的文字说明材料；</p>
                <p style="color: #FB3301;margin-left: 600px;font-size: 12px;line-height: 20px;">2.开展执法普法工作的通知、行动方案或者省级人员现场指导的宣传稿。</p>

                <h4 style="font-size:14px;font-weight: 600;margin-left: 300px;margin-top:30px;margin-bottom: 30px">“百千万”执法人才培养工程开展情况</h4>
                <div style="margin-top: 20px">
                    <label style="font-size: 14px; color: #333333; font-weight: 400"><span style="color: red;size: 60px">*</span>是否建立省级执法人才库：</label>
                    <select style="width:380px;margin-right:5px;" name="isPeopool" id="isPeopool">
                        <option value="">--请选择--</option>
                        <option value="1"
                                <c:if test="${provinceSelectionUnit.isPeopool eq '1'}">selected</c:if>>是
                        </option>
                        <option value="0"
                                <c:if test="${provinceSelectionUnit.isPeopool eq '0'}">selected</c:if>>否
                        </option>
                    </select>
                </div>

                <div class="shangchuan">
                    <label class="zhiding" style="line-height: 33px;font-size: 14px; color: #333333; font-weight: 400"><span style="color: red;size: 60px" class="ispeopoolShow">*</span>已建立人才库的相关文件：</label>
                    <span id="uploadTr25"
                          <c:if test="${provinceSelectionUnit.peopoolUrlname!='' && provinceSelectionUnit.peopoolUrlname!=null }">style='display:none'</c:if>
                    >
<%--                            style="width: calc(100% - 224px)--%>
                              <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                  <input style="width: 80px" type="file" id="peopoolFile" value="文件上传">
                                  <input type="hidden" class="form-control" name="peopoolUrlname"
                                         id="peopoolUrlname" value="${provinceSelectionUnit.peopoolUrlname}">
                                  <input type="hidden" class="form-control" name="peopoolUrl"
                                         id="peopoolUrl" value="${provinceSelectionUnit.peopoolUrl}">
                              </c:if>
                        </span>
                    <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                        <input  id="reButton25"
                                <c:if test="${provinceSelectionUnit.peopoolUrlname=='' || provinceSelectionUnit.peopoolUrlname==null }">style='display:none'</c:if>
                                class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                    </c:if>
                </div>

                <div id="wsc25" style="margin-left:357px;padding-top:6px;width: 380px;margin-top: 14px;border-radius: 3px;">
                    <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">
                        <c:choose>
                            <c:when test="${provinceSelectionUnit.peopoolUrlname!='' && provinceSelectionUnit.peopoolUrlname!=null }">
                               <span style=" margin-right: 8px; padding-top: 4px;">
<%--                                               ${provinceSelectionUnit.jcBwFileName}--%>
                                   <a href="javascript:void(0)"
                                      onclick="downloadFile('${provinceSelectionUnit.peopoolUrl}','${provinceSelectionUnit.peopoolUrlname}')">
                                           ${provinceSelectionUnit.peopoolUrlname}
                                   </a>
                               </span>
                            </c:when>
                        </c:choose>
                    </span>
                </div>
                <div style="margin-top: 20px">
                    <label style="font-size: 14px; color: #333333; font-weight: 400"><span style="color: red;size: 60px">*</span>是否组织执法专业、专项培训：</label>
                    <select style="width:380px;margin-right:5px;" name="isTrain" id="isTrain">
                        <option value="">--请选择--</option>
                        <option value="1"
                                <c:if test="${provinceSelectionUnit.isTrain eq '1'}">selected</c:if>>是
                        </option>
                        <option value="0"
                                <c:if test="${provinceSelectionUnit.isTrain eq '0'}">selected</c:if>>否
                        </option>
                    </select>
                </div>
                <div class="shangchuan">
                    <label class="zhiding" style="line-height: 33px;"><span
                            style="color: red;size: 60px" class="istrainShow"> *</span>开展培训（岗位培训除外）的动态或宣传情况：</label>
                    <span id="uploadTr26"
                          <c:if test="${provinceSelectionUnit.trainUrlname!='' && provinceSelectionUnit.trainUrlname!=null }">style='display:none'</c:if>
                    >
<%--                            style="width: calc(100% - 224px)--%>
                              <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                  <input style="width: 80px" type="file" id="trainFile" value="文件上传">
                                  <input type="hidden" class="form-control" name="trainUrlname"
                                         id="trainUrlname" value="${provinceSelectionUnit.trainUrlname}">
                                  <input type="hidden" class="form-control" name="trainUrl"
                                         id="trainUrl" value="${provinceSelectionUnit.trainUrl}">
                              </c:if>
                        </span>
                    <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                        <input  id="reButton26"
                                <c:if test="${provinceSelectionUnit.trainUrlname=='' || provinceSelectionUnit.trainUrlname==null }">style='display:none'</c:if>
                                class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                    </c:if>
                </div>

                <div id="wsc26" style="margin-left:357px;padding-top:6px;width: 380px;margin-top: 14px;border-radius: 3px;">
                    <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">
                        <c:choose>
                            <c:when test="${provinceSelectionUnit.trainUrlname!='' && provinceSelectionUnit.trainUrlname!=null }">
                               <span style=" margin-right: 8px; padding-top: 4px;">
<%--                                               ${provinceSelectionUnit.jcBwFileName}--%>
                                   <a href="javascript:void(0)"
                                      onclick="downloadFile('${provinceSelectionUnit.trainUrl}','${provinceSelectionUnit.trainUrlname}')">
                                           ${provinceSelectionUnit.trainUrlname}
                                   </a>
                               </span>
                            </c:when>
                        </c:choose>
                    </span>
                </div>
            </div>

            <div style="margin-top: 20px; background: #fff; padding: 2px 30px 26px 21px; width: 100%">

                <h1 style="color: #31688F;font-size: 16px;font-weight: 400">四、专项行动</h1>
                <h1 style="font-size: 14px;color: #31688F;font-weight:400;height: 33px;line-height:33px;background: #F8F8F8;">排污许可执法监管</h1>
                <h4 style="font-size:14px;font-weight: 600;margin-left: 300px;margin-top:30px;margin-bottom: 30px">排污许可制度建设情况</h4>
                <div style="margin-top: 20px;">
                    <div class="shifou">
                        <label class="zhiding" style="line-height: 33px"> <span style="color: red;size: 60px">*</span>是否出台加强排污许可执法监管政策措施：</label>
                        <select style="width:380px;margin-right:5px;" name="isLicenselaw" id="isLicenselaw">
                            <option value="">--请选择--</option>
                            <option value="1"
                                    <c:if test="${provinceSelectionUnit.isLicenselaw eq '1'}">selected</c:if>>是
                            </option>
                            <option value="0"
                                    <c:if test="${provinceSelectionUnit.isLicenselaw eq '0'}">selected</c:if>>否
                            </option>
                        </select>
                    </div>
                </div>

                <div class="shangchuan">
                    <label class="zhiding"><span style="color: red;size: 60px" class="isLicenseShow">*</span>加强排污许可执法监管的政策文件：</label>
                    <span id="uploadTr27"
                          <c:if test="${provinceSelectionUnit.licenselawUrlname!='' && provinceSelectionUnit.licenselawUrlname!=null }">style='display:none'</c:if>
                          style="">
<%--                        width: calc(100% - 190px)--%>
							<c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                <input type="file" id="licenseFileUpload" value="文件上传">
                                <input type="hidden" class="form-control" name="licenselawUrlname" id="licenselawUrlname"
                                       value="${provinceSelectionUnit.licenselawUrlname}">
                                <input type="hidden" class="form-control" name="licenselawUrl" id="licenselawUrl"
                                       value="${provinceSelectionUnit.licenselawUrl}">
                            </c:if>
					    </span>
                    <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                        <input id="reButton27"
                               <c:if test="${provinceSelectionUnit.licenselawUrlname=='' || provinceSelectionUnit.licenselawUrlname==null }">style='display:none'</c:if>
                               class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                    </c:if>
                </div>
                <div id="wsc27" style="margin-left:354px;width: 380px;margin-top: 14px;border-radius: 3px;">
                    <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">
						<c:choose>
                            <c:when test="${provinceSelectionUnit.licenselawUrlname!='' && provinceSelectionUnit.licenselawUrlname!=null }">
							<span style=" margin-right: 8px; padding-top: 4px;">
<%--                                    ${provinceSelectionUnit.jdbfFileName}--%>
                                <a href="javascript:void(0)"
                                   onclick="downloadFile('${provinceSelectionUnit.licenselawUrl}','${provinceSelectionUnit.licenselawUrlname}')">
                                        ${provinceSelectionUnit.licenselawUrlname}
                                </a>
                            </span>
                            </c:when>
                        </c:choose>
					</span>
                </div>
                <div style="margin-top: 20px;">
                    <div class="shifou">
                        <label class="zhiding"> <span style="color: red;size: 60px">*</span> 是否细化完善排污许可行政处罚自由裁量规定：</label>
                        <select style="width:380px;margin-right:5px;" name="isDiscretion" id="isDiscretion">
                            <option value="">--请选择--</option>
                            <option value="1"
                                    <c:if test="${provinceSelectionUnit.isDiscretion eq '1'}">selected</c:if>>是
                            </option>
                            <option value="0"
                                    <c:if test="${provinceSelectionUnit.isDiscretion eq '0'}">selected</c:if>>否
                            </option>
                        </select>
                    </div>
                </div>
                <div class="shangchuan">
                    <label class="zhiding" style="line-height: 33px;"> <span style="color: red;size: 60px" class="isDiscretShow">*</span>行政处罚自由裁量规定：</label>
                    <span id="uploadTr28"
                          <c:if test="${provinceSelectionUnit.discretionUrlname!='' && provinceSelectionUnit.discretionUrlname!=null }">style='display:none'</c:if>
                          style="">
<%--                        width: calc(100% - 190px)--%>
							<c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                <input type="file" id="discretionFileUpload" value="文件上传">
                                <input type="hidden" class="form-control" name="discretionUrlname" id="discretionUrlname"
                                       value="${provinceSelectionUnit.discretionUrlname}">
                                <input type="hidden" class="form-control" name="discretionUrl" id="discretionUrl"
                                       value="${provinceSelectionUnit.discretionUrl}">
                            </c:if>
					    </span>
                    <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                        <input id="reButton28"
                               <c:if test="${provinceSelectionUnit.discretionUrlname=='' || provinceSelectionUnit.discretionUrlname==null }">style='display:none'</c:if>
                               class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                    </c:if>
                </div>
                <div id="wsc28" style="margin-left:354px;width: 380px;margin-top: 14px;border-radius: 3px;">
                    <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">
						<c:choose>
                            <c:when test="${provinceSelectionUnit.discretionUrlname!='' && provinceSelectionUnit.discretionUrlname!=null }">
							<span style=" margin-right: 8px; padding-top: 4px;">
<%--                                    ${provinceSelectionUnit.jdbfFileName}--%>
                                <a href="javascript:void(0)"
                                   onclick="downloadFile('${provinceSelectionUnit.discretionUrl}','${provinceSelectionUnit.discretionUrlname}')">
                                        ${provinceSelectionUnit.discretionUrlname}
                                </a>
                            </span>
                            </c:when>
                        </c:choose>
					</span>
                </div>
                <h4 style="font-size:14px;font-weight: 600;margin-left: 300px;margin-top:30px;margin-bottom: 30px">组织开展排污许可专项执法</h4>
                <div style="margin-top: 20px;">
                    <div class="shifou">
                        <label class="zhiding"> <span style="color: red;size: 60px">*</span>是否及时曝光排污许可违法典型案件：</label>
                        <select style="width:380px;margin-right:5px;" name="isLicensespecial" id="isLicensespecial">
                            <option value="">--请选择--</option>
                            <option value="1"
                                    <c:if test="${provinceSelectionUnit.isLicensespecial eq '1'}">selected</c:if>>是
                            </option>
                            <option value="0"
                                    <c:if test="${provinceSelectionUnit.isLicensespecial eq '0'}">selected</c:if>>否
                            </option>
                        </select>
                    </div>
                </div>
                <div class="shangchuan">
                    <label class="zhiding" style="line-height: 33px;"> <span style="color: red;size: 60px" class="licensespecialShow">*</span>典型案例公开的网页截图或者通报文件：</label>
                    <span id="uploadTr29"
                          <c:if test="${provinceSelectionUnit.licensespecialUrlname!='' && provinceSelectionUnit.licensespecialUrlname!=null }">style='display:none'</c:if>
                          style="">
<%--                        width: calc(100% - 190px)--%>
							<c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                <input type="file" id="licensesFileUpload" value="文件上传">
                                <input type="hidden" class="form-control" name="licensespecialUrlname" id="licensespecialUrlname"
                                       value="${provinceSelectionUnit.licensespecialUrlname}">
                                <input type="hidden" class="form-control" name="licensespecialUrl" id="licensespecialUrl"
                                       value="${provinceSelectionUnit.licensespecialUrl}">
                            </c:if>
					    </span>
                    <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                        <input id="reButton29"
                               <c:if test="${provinceSelectionUnit.licensespecialUrlname=='' || provinceSelectionUnit.licensespecialUrlname==null }">style='display:none'</c:if>
                               class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                    </c:if>
                </div>
                <div id="wsc29" style="margin-left:354px;width: 380px;margin-top: 14px;border-radius: 3px;">
                    <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">
						<c:choose>
                            <c:when test="${provinceSelectionUnit.licensespecialUrlname!='' && provinceSelectionUnit.licensespecialUrlname!=null }">
							<span style=" margin-right: 8px; padding-top: 4px;">
<%--                                    ${provinceSelectionUnit.jdbfFileName}--%>
                                <a href="javascript:void(0)"
                                   onclick="downloadFile('${provinceSelectionUnit.licensespecialUrl}','${provinceSelectionUnit.licensespecialUrlname}')">
                                        ${provinceSelectionUnit.licensespecialUrlname}
                                </a>
                            </span>
                            </c:when>
                        </c:choose>
					</span>
                </div>
                <h4 style="font-size:14px;font-weight: 600;margin-left: 300px;margin-top:30px;margin-bottom: 30px">组织开展清单式执法</h4>
                <div style="margin-top: 20px;">
                    <div class="shifou">
                        <label class="zhiding"> <span style="color: red;size: 60px">*</span>是否开展排污许可清单式执法：</label>
                        <select style="width:380px;margin-right:5px;" name="isLicenselist" id="isLicenselist">
                            <option value="">--请选择--</option>
                            <option value="1"
                                    <c:if test="${provinceSelectionUnit.isLicenselist eq '1'}">selected</c:if>>是
                            </option>
                            <option value="0"
                                    <c:if test="${provinceSelectionUnit.isLicenselist eq '0'}">selected</c:if>>否
                            </option>
                        </select>
                    </div>
                </div>
                <div class="shangchuan">
                    <label class="zhiding"><span style="color: red;size: 60px" class="islicenselistShow">*</span>省本级制定的排污许可清单式执法通知文件或行动方案或现场指导等佐证材料：</label>
                    <span id="uploadTr30"
                          <c:if test="${provinceSelectionUnit.licenselistUrlname!='' && provinceSelectionUnit.licenselistUrlname!=null }">style='display:none'</c:if>
                          style="">
<%--                        width: calc(100% - 190px)--%>
							<c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                <input type="file" id="licenselisFileUpload" value="文件上传">
                                <input type="hidden" class="form-control" name="licenselistUrlname" id="licenselistUrlname"
                                       value="${provinceSelectionUnit.licenselistUrlname}">
                                <input type="hidden" class="form-control" name="licenselistUrl" id="licenselistUrl"
                                       value="${provinceSelectionUnit.licenselistUrl}">
                            </c:if>
					    </span>
                    <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                        <input id="reButton30"
                               <c:if test="${provinceSelectionUnit.licenselistUrlname=='' || provinceSelectionUnit.licenselistUrlname==null }">style='display:none'</c:if>
                               class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                    </c:if>
                </div>
                <div id="wsc30" style="margin-left:354px;width: 380px;margin-top: 14px;border-radius: 3px;">
                    <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">
						<c:choose>
                            <c:when test="${provinceSelectionUnit.licenselistUrlname!='' && provinceSelectionUnit.licenselistUrlname!=null }">
							<span style=" margin-right: 8px; padding-top: 4px;">
<%--                                    ${provinceSelectionUnit.jdbfFileName}--%>
                                <a href="javascript:void(0)"
                                   onclick="downloadFile('${provinceSelectionUnit.licenselistUrl}','${provinceSelectionUnit.licenselistUrlname}')">
                                        ${provinceSelectionUnit.licenselistUrlname}
                                </a>
                            </span>
                            </c:when>
                        </c:choose>
					</span>
                </div>
            </div>

            <%--66666666666666666666666666666666666666--%>
            <%--省 专项行动表现--%>
            <%--            <div style="margin-top: 20px; background: #fff; padding: 2px 30px 26px 21px; width: 100%">--%>

            <%--                <h1 style="color: #31688F;font-size: 16px;font-weight: 400">五、专项行动表现</h1>--%>
            <%--                <div style=" display: flex;margin-left:286px;margin-top: 20px;">--%>
            <%--                    <span style="font-size: 14px; color: #333333; font-weight: 400;padding-top: 5px">Excel文件：</span>--%>
            <%--                    <span id="uploadTr28"--%>
            <%--                          <c:if test="${provinceSelectionUnit.zxxdExcelName!='' && provinceSelectionUnit.zxxdExcelName!=null }">style='display:none'</c:if>--%>
            <%--                          style="">--%>
            <%--&lt;%&ndash;                        width: calc(100% - 224px)&ndash;%&gt;--%>
            <%--                        <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
            <%--                            <div style="display: flex">--%>
            <%--                                <input type="file" id="zxxdExcel" value="文件上传">--%>
            <%--                                <input type="hidden" class="form-control" name="zxxdExcelName"--%>
            <%--                                       id="zxxdExcel_Filename"--%>
            <%--                                       value="${provinceSelectionUnit.zxxdExcelName}">--%>
            <%--                                <input type="hidden" class="form-control" name="zxxdExcelUrl"--%>
            <%--                                       id="zxxdExcel_Fileurl"--%>
            <%--                                       value="${provinceSelectionUnit.zxxdExcelUrl}">--%>

            <%--                            </div>--%>
            <%--                        </c:if>--%>
            <%--                    </span>--%>
            <%--                    <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
            <%--                        <input id="reButton28"--%>
            <%--                               <c:if test="${provinceSelectionUnit.zxxdExcelName=='' || provinceSelectionUnit.zxxdExcelName==null }">style='display:none'</c:if>--%>
            <%--                               class="btn btn-danger btn-xs" type="button" value="重新上传"/>--%>
            <%--                    </c:if>--%>
            <%--                </div>--%>
            <%--                <p>--%>
            <%--                    <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
            <%--                          <span style="color: deepskyblue; margin-left:359px;margin-top:5px"><a style="color: blue;margin-left: 7px"--%>
            <%--                                                                                href="#"--%>
            <%--                                                                                onclick="down(5)">(下载模板）</a>--%>
            <%--                           </span>--%>
            <%--                    </c:if>--%>
            <%--                </p>--%>
            <%--                <div id="wsc28" style="margin-left:358px;width: 380px;margin-top: 14px;border-radius: 3px;">--%>
            <%--                    <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">--%>
            <%--                        <c:choose>--%>
            <%--                            <c:when test="${provinceSelectionUnit.zxxdExcelName!='' && provinceSelectionUnit.zxxdExcelName!=null }">--%>
            <%--                                <span style=" margin-right: 8px; padding-top: 4px;">--%>
            <%--                                 <a href="javascript:void(0)"--%>
            <%--                                    onclick="downloadFile('${provinceSelectionUnit.zxxdExcelUrl}','${provinceSelectionUnit.zxxdExcelName}')"> ${provinceSelectionUnit.zxxdExcelName} </a>--%>
            <%--                                </span>--%>
            <%--                            </c:when>--%>
            <%--                        </c:choose>--%>
            <%--                    </span>--%>
            <%--                </div>--%>
            <%--                <p style="color: #FB3301;font-size:12px;font-weight:400;margin-left: 365px;width: 543px;">1.请认真填写下列信息，并仔细上传支撑材料，填写的信息未在支撑材料中体现的，将不作为给分点</p>--%>
            <%--                <p style="color: #FB3301;font-size:12px;font-weight:400;margin-left: 365px;width: 543px;line-height:20px">2.每份支撑材料大小不超过50M，要求rar格式；存放支撑材料的文件夹（压缩包）以【省份-二级标题】命名，如知识竞赛支撑材料应存放在【xx省-知识竞赛】文件夹（压缩包）中</p>--%>


            <%--                <div>--%>
            <%--                    <div class="shifou" style="margin-left:114px">--%>
            <%--                        <span class="zhiding"><span style="color: red;size: 60px">*</span>查处涉废矿物油环境违法犯罪案件数：</span>--%>
            <%--                        <input maxlength="7" id="zxxdDelinquencyNum" name="zxxdDelinquencyNum"--%>
            <%--                               value="${provinceSelectionUnit.zxxdDelinquencyNum}"--%>
            <%--                               onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^0-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}"--%>
            <%--                               onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^0-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}">--%>
            <%--                    </div>--%>
            <%--                    <div class="shifou" style="margin-left:123px">--%>
            <%--                        <label class="zhiding" style="display: inline-block;width: 230px;text-align-last: right;"><span style="color: red;size: 60px">*</span>对跨区域非法排放、倾倒、处置危险废物案件试行提级查办案例数：</label>--%>
            <%--                        <input style='margin-top: 2px;position: absolute;margin-left: 4px;' maxlength="7" id="zxxdRiskTrashNum" name="zxxdRiskTrashNum"--%>
            <%--                               value="${provinceSelectionUnit.zxxdRiskTrashNum}"--%>
            <%--                               onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^0-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}"--%>
            <%--                               onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^0-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}">--%>
            <%--                    </div>--%>
            <%--                    <div class="shifou" style="margin-left:59px">--%>
            <%--                        <span class="zhiding" style="display: inline-block;width: 300px;text-align-last: right;"><span style="color: red;size: 60px">*</span>候选集体移交篡改、伪造自动监测数据或干扰自动监控设施等逃避监管的违法犯罪案件数：</span>--%>
            <%--                        <input style='position: absolute;margin-left: 4px;margin-top: 2px;' maxlength="7" id="zxxdForgeNum" name="zxxdForgeNum"--%>
            <%--                               value="${provinceSelectionUnit.zxxdForgeNum}"--%>
            <%--                               onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^0-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}"--%>
            <%--                               onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^0-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}">--%>
            <%--                    </div>--%>
            <%--                </div>--%>

            <%--                <div class="shangchuan" style="margin-left:283px;width: calc(100% - 200px)">--%>

            <%--                    <span style="color: red;size: 60px">*</span>--%>
            <%--                    <span class="zhiding" style="line-height: 33px;">支撑材料：</span>--%>
            <%--                    <span id="uploadTr29"--%>
            <%--                          <c:if test="${provinceSelectionUnit.zxxdExpressionFileName!='' && provinceSelectionUnit.zxxdExpressionFileName!=null }">style='display:none'</c:if>--%>
            <%--                          style=" ">--%>
            <%--&lt;%&ndash;                        width: calc(100% - 224px)&ndash;%&gt;--%>
            <%--							<c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
            <%--								<div style="display: flex">--%>
            <%--                                    <input type="file" id="zxxdExpressionFileUpload" value="文件上传">--%>
            <%--                                    <input type="hidden" class="form-control" name="zxxdExpressionFileName"--%>
            <%--                                           id="zxxdExpressionFileName_Filename"--%>
            <%--                                           value="${provinceSelectionUnit.zxxdExpressionFileName}">--%>
            <%--                                    <input type="hidden" class="form-control" name="zxxdExpressionFileUrl"--%>
            <%--                                           id="zxxdExpressionFileUrl_Fileurl"--%>
            <%--                                           value="${provinceSelectionUnit.zxxdExpressionFileUrl}">--%>
            <%--                                </div>--%>
            <%--                            </c:if>--%>
            <%--					    </span>--%>
            <%--                    <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
            <%--                        <input id="reButton29"--%>
            <%--                               <c:if test="${provinceSelectionUnit.zxxdExpressionFileName=='' || provinceSelectionUnit.zxxdExpressionFileName==null }">style='display:none'</c:if>--%>
            <%--                               class="btn btn-danger btn-xs" type="button"--%>
            <%--                               value="重新上传"/>--%>
            <%--                    </c:if>--%>
            <%--                </div>--%>
            <%--                <div id="wsc29cityPersonInputPro" style="margin-left:358px;width: 380px;margin-top: 14px;">--%>
            <%--                    <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">--%>
            <%--						<c:choose>--%>
            <%--                            <c:when test="${provinceSelectionUnit.zxxdExpressionFileName!='' && provinceSelectionUnit.zxxdExpressionFileName!=null }">--%>
            <%--							<span style=" margin-right: 8px; padding-top: 4px;">--%>
            <%--&lt;%&ndash;                                    ${provinceSelectionUnit.zxxdExpressionFileName}&ndash;%&gt;--%>
            <%--                                <a href="javascript:void(0)"--%>
            <%--                                   onclick="downloadFile('${provinceSelectionUnit.zxxdExpressionFileUrl}','${provinceSelectionUnit.zxxdExpressionFileName}')">--%>
            <%--                                        ${provinceSelectionUnit.zxxdExpressionFileName}--%>
            <%--                                </a>--%>
            <%--                            </span>--%>
            <%--                            </c:when>--%>
            <%--                        </c:choose>--%>
            <%--					</span>--%>
            <%--                </div>--%>
            <%--                &lt;%&ndash;专项行动新增的字段&ndash;%&gt;--%>
            <%--                <div class="shifou" style="margin-left:160px">--%>
            <%--                    <span class="zhiding"><span style="color: red;size: 60px">*</span>查处弄虚作假违法犯罪案件数：</span>--%>
            <%--                    <input maxlength="7" id="nxzjCaseSum" name="nxzjCaseSum"--%>
            <%--                           value="${provinceSelectionUnit.nxzjCaseSum}"--%>
            <%--                           onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^0-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}"--%>
            <%--                           onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^0-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}">--%>
            <%--                </div>--%>
            <%--                <div class="shifou" style="margin-left: 150px;">--%>
            <%--                    <span class="zhiding" style="width: 211px;display: inline-block;text-align-last: right;"><span style="color: red;size: 60px">*</span>辖区内是否30%以上的设区市都查处了数据造假违法犯罪案件：</span>--%>
            <%--                    <select name="isZjCase" id="isZjCase"--%>
            <%--                            style="position: absolute;margin-left: 5px;margin-top: 5px;width:380px;margin-right:5px;">--%>
            <%--                        <option value="">--请选择--</option>--%>
            <%--                        <option value="1"--%>
            <%--                                <c:if test="${provinceSelectionUnit.isZjCase eq '1'}">selected</c:if>>--%>
            <%--                            是--%>
            <%--                        </option>--%>
            <%--                        <option value="0"--%>
            <%--                                <c:if test="${provinceSelectionUnit.isZjCase eq '0'}">selected</c:if>>--%>
            <%--                            否--%>
            <%--                        </option>--%>
            <%--                    </select>--%>
            <%--                </div>--%>

            <%--                <div class="shangchuan" style="margin-left:144px;width: calc(100% - 200px)">--%>

            <%--                    <span style="color: red;size: 60px">*</span>--%>
            <%--                    <span class="zhiding">查处弄虚作假违法犯罪支撑材料：</span>--%>
            <%--                    <span id="uploadTr30"--%>
            <%--                          <c:if test="${provinceSelectionUnit.nxzjCaseFileName!='' && provinceSelectionUnit.nxzjCaseFileName!=null }">style='display:none'</c:if>--%>
            <%--                          style=" ">--%>
            <%--&lt;%&ndash;                        width: calc(100% - 224px)&ndash;%&gt;--%>
            <%--							<c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
            <%--								<div style="display: flex">--%>
            <%--                                    <input type="file" id="nxzjCaseFileUpload" value="文件上传">--%>
            <%--                                    <input type="hidden" class="form-control" name="nxzjCaseFileName"--%>
            <%--                                           id="nxzjCaseFileName_Filename"--%>
            <%--                                           value="${provinceSelectionUnit.nxzjCaseFileName}">--%>
            <%--                                    <input type="hidden" class="form-control" name="nxzjCaseFileUrl"--%>
            <%--                                           id="nxzjCaseFileUrl_Fileurl"--%>
            <%--                                           value="${provinceSelectionUnit.nxzjCaseFileUrl}">--%>
            <%--                                </div>--%>
            <%--                            </c:if>--%>
            <%--					    </span>--%>
            <%--                    <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
            <%--                        <input id="reButton30" style="width:80px;height:32px;margin-left: 8px;background-color:#3f91c2;border:1px solid #3f91c2"--%>
            <%--                               <c:if test="${provinceSelectionUnit.nxzjCaseFileName=='' || provinceSelectionUnit.nxzjCaseFileName==null }">style='display:none'</c:if>--%>
            <%--                               class="btn btn-danger btn-xs" type="button"--%>
            <%--                               value="重新上传"/>--%>
            <%--                    </c:if>--%>
            <%--                </div>--%>
            <%--                <div id="wsc30" style="margin-left:358px;width: 380px;margin-top: 14px;">--%>
            <%--                   <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">--%>
            <%--						<c:choose>--%>
            <%--                            <c:when test="${provinceSelectionUnit.nxzjCaseFileName!='' && provinceSelectionUnit.nxzjCaseFileName!=null }">--%>
            <%--							<span style=" margin-right: 8px; padding-top: 4px;">--%>
            <%--&lt;%&ndash;                                    ${provinceSelectionUnit.nxzjCaseFileName}&ndash;%&gt;--%>
            <%--                                <a href="javascript:void(0)"--%>
            <%--                                   onclick="downloadFile('${provinceSelectionUnit.nxzjCaseFileUrl}','${provinceSelectionUnit.nxzjCaseFileName}')">--%>
            <%--                                        ${provinceSelectionUnit.nxzjCaseFileName}--%>
            <%--                                </a>--%>
            <%--                            </span>--%>
            <%--                            </c:when>--%>
            <%--                        </c:choose>--%>
            <%--					</span>--%>
            <%--                </div>--%>
            <%--            </div>--%>


            <%--7777777777777777777777777777--%>
            <%--活动总结--%>
            <%--            <div style="margin-top: 20px; background: #fff; padding:2px 30px 26px 21px; line-height: 30px;width: 100%">--%>
            <%--                <h1 style="color: #25678E;font-size: 16px;font-weight:400">六、活动总结</h1>--%>
            <%--                <div class="shangchuan" style="width: 100%;margin-left:225px">--%>
            <%--                                                            <span class="zhiding"><span--%>
            <%--                                                                    style="color: red;size: 60px">*</span>活动总结支撑材料：</span>--%>
            <%--                    <span id="uploadTr25"--%>
            <%--                          <c:if test="${provinceSelectionUnit.activityreportname!='' && provinceSelectionUnit.activityreportname!=null }">style='display:none'</c:if>--%>
            <%--                          >--%>
            <%--&lt;%&ndash;                        style="width: calc(100% - 224px)&ndash;%&gt;--%>
            <%--										<c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
            <%--                                            <input type="file" id="activityReport" value="文件上传">--%>
            <%--                                            <input type="hidden" class="form-control" name="activityreportname"--%>
            <%--                                                   id="activityReport_Filename"--%>
            <%--                                                   value="${provinceSelectionUnit.activityreportname}">--%>
            <%--                                            <input type="hidden" class="form-control" name="activityreporturl"--%>
            <%--                                                   id="activityReport_Fileurl"--%>
            <%--                                                   value="${provinceSelectionUnit.activityreporturl}">--%>
            <%--                                        </c:if>--%>
            <%--					            </span>--%>
            <%--                    <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
            <%--                        <input  id="reButton25"--%>
            <%--                               <c:if test="${provinceSelectionUnit.activityreportname=='' || provinceSelectionUnit.activityreportname==null }">style='display:none'</c:if>--%>
            <%--                               class="btn btn-danger btn-xs" type="button"--%>
            <%--                               value="重新上传"/>--%>
            <%--                    </c:if>--%>
            <%--                </div>--%>
            <%--                <p style="color: red;margin-left: 354px;font-size:12px;font-weight: 400">（附件要求PDF格式，大小不超过30M）</p>--%>
            <%--                <div id="wsc25" style="margin-left:352px;width: 380px;">--%>
            <%--                   <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">--%>
            <%--                        <c:choose>--%>
            <%--                            <c:when test="${provinceSelectionUnit.activityreportname!='' && provinceSelectionUnit.activityreportname!=null }">--%>
            <%--                               <span style=" margin-right: 8px; padding-top: 4px;">--%>
            <%--    &lt;%&ndash;                                   ${provinceSelectionUnit.activityreportname}&ndash;%&gt;--%>
            <%--                                   <a href="javascript:void(0)"--%>
            <%--                                      onclick="downloadFile('${provinceSelectionUnit.activityreporturl}','${provinceSelectionUnit.activityreportname}')">--%>
            <%--                                           ${provinceSelectionUnit.activityreportname}--%>
            <%--                                   </a>--%>
            <%--                               </span>--%>
            <%--                            </c:when>--%>
            <%--                        </c:choose>--%>
            <%--                   </span>--%>
            <%--                </div>--%>
            <%--            </div>--%>


        </div>


        <%--snnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnn--%>

        <div style="background-color:#f6fafe;display: flex; justify-content:center; padding-top: 40px">
            <input type="hidden" name="id" id="id"
                   value="${provinceSelectionUnit.id }"/>
            <input type="hidden" name="subToken" value="${subToken}">
            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                <a href="#">
                    <button type="button" id="xxcj_XXZC_Butt" class="btn btn-danger"
                            name="signup" value="Sign up"
                            style="font-size: 16px; width: 100px;border-radius: 3px;padding: 8px 32px;margin-right: 50px">
                        暂存
                    </button>
                    <button type="button" id="xxcj_AJSL_Butt" class="btn btn-danger"
                            name="signup" value="Sign up"
                            style="font-size: 16px;width: 100px;border-radius: 3px;padding: 8px 32px; background: #BCBBBB;border-color: #BCBBBB;">
                        提交
                    </button>
                </a>
            </c:if>
        </div>
    </form>
</div>
<script type="text/javascript">

    $(function () {
        $(".isBuildis").hide()
debugger

        var caseEndType = $("#caseEndType").val();


        $(".isCaseTypeShow").hide()
        $(".isCaseTypeShow2").hide()

        if(caseEndType==2){
            $(".isCaseTypeShow").show()
        }

        if(caseEndType==3){
            $(".isCaseTypeShow2").show()
        }
        <%--//出台时间--%>
        <%--var come1 = '${provinceSelectionUnit.comeTime}';--%>
        <%--console.log(come1)--%>
        <%--timeZh(come1, 1);--%>
        <%--var come2 = '${provinceSelectionUnit.rsComeTime}';--%>
        <%--timeZh(come2, 2);--%>
        <%--var come3 = '${provinceSelectionUnit.jzSzComeTime}';--%>
        <%--timeZh(come3, 3);--%>


        //机构领导是否带头参与 如果 是 显示领导具体职务的框
        $("#jgld").change(function () {
            var option = $("#jgld option:selected");
            //要触发的事件
            if (option.val() == "1") {
                $("#jtldzw").show()
            } else {
                $("#jtldzw").hide()
            }
        });
        <%--var leader = '${provinceSelectionUnit.isLeader}';--%>
        <%--if (leader == "1") {--%>
        <%--    $("#jtldzw").show()--%>
        <%--} else {--%>
        <%--    $("#jtldzw").hide()--%>
        <%--}--%>


        $("#isJili").change(function () {
            var option = $("#isJili option:selected");
            //要触发的事件
            if (option.val() == "1") {
                $(".isShow").show()
            } else {
                $(".isShow").hide()
            }
        })
        // 是否开展竞赛比武
        $("#isJingwu").change(function () {
            var option = $("#isJingwu option:selected");
            //要触发的事件
            if (option.val() == "1") {
                $(".isJingwuShow").show()
            } else {
                $(".isJingwuShow").hide()
            }
        })
        // 案卷评查
        $("#isCasereview").change(function () {
            var option = $("#isCasereview option:selected");
            //要触发的事件
            if (option.val() == "1") {
                $(".isCaseShow").show()
            } else {
                $(".isCaseShow").hide()
            }
        })




        // 是否出台加强排污许可执法监管政策措施
        $("#isLicenselaw").change(function () {
            var option = $("#isLicenselaw option:selected");
            //要触发的事件
            if (option.val() == "1") {
                $(".isLicenseShow").show()
            } else {
                $(".isLicenseShow").hide()
            }
        })


        // 是否细化完善排污许可行政处罚自由裁量规定
        $("#isDiscretion").change(function () {
            var option = $("#isDiscretion option:selected");
            //要触发的事件
            if (option.val() == "1") {
                $(".isDiscretShow").show()
            } else {
                $(".isDiscretShow").hide()
            }
        })

        // 2021年是否出台
        $("#isChutai").change(function () {
            var option = $("#isChutai option:selected");
            //要触发的事件
            if (option.val() == "1") {
                $(".isDwreport").show()
                $(".isBuildis").hide()
            } else {
                $(".isDwreport").hide()
                $(".isBuildis").show()
            }
        })
        // 是否开展
        $("#isLawcheck").change(function () {
            var option = $("#isLawcheck option:selected");
            //要触发的事件
            if (option.val() == "1") {
                $(".isLawcheckShow").show()
            } else {
                $(".isLawcheckShow").hide()
            }
        })
        // 举报奖励制度执行情况
        $(".isTypical").change(function () {
            debugger
            var option = $("#isCover option:selected");
            var options = $("#isTypicalcase option:selected");
            //要触发的事件
            if (option.val() == "1" || options.val() == "1") {
                $(".isTypicalcase").show()
            } else {
                $(".isTypicalcase").hide()
            }
        })

        // 是否辖区内所有地市级生态环境部门均公布正面清单企业名单，并实施动态管理
        $(".isPositivelistWarnShow").change(function () {
            debugger
            var option = $("#isPositivelistWarn option:selected");
            var options = $("#isPositivelistManage option:selected");
            //要触发的事件
            if (option.val() == "1" && options.val() == "1") {
                $(".isPositivelistSup").show()
            } else {
                $(".isPositivelistSup").hide()
            }
        })
        // 是否指导组织地市级生态环境部门通过座谈、宣讲、培训等方式对正面清单企业开展帮扶指导和提醒预警
        // $("#isPositivelistManage").change(function () {
        //     var option = $("#isPositivelistManage option:selected");
        //     //要触发的事件
        //     if (option.val() == "1") {
        //         $(".isPositivelistSup").show()
        //     } else {
        //         $(".isPositivelistSup").hide()
        //     }
        // })

        // 是否指导所有地市级生态环境部门开展针对中小企业重点执法帮扶、普法培训、“送法入企”活动
        $("#isGuide").change(function () {
            var option = $("#isGuide option:selected");
            //要触发的事件
            if (option.val() == "1") {
                $(".isGuideShow").show()
            } else {
                $(".isGuideShow").hide()
            }
        })
        // 是否建立省级执法人才库
        $("#isPeopool").change(function () {
            var option = $("#isPeopool option:selected");
            //要触发的事件
            if (option.val() == "1") {
                $(".ispeopoolShow").show()
            } else {
                $(".ispeopoolShow").hide()
            }
        })
        //  开展培训（岗位培训除外）的动态或宣传情况
        $("#isTrain").change(function () {
            var option = $("#isTrain option:selected");
            //要触发的事件
            if (option.val() == "1") {
                $(".istrainShow").show()
            } else {
                $(".istrainShow").hide()
            }
        })
        //  开展培训（岗位培训除外）的动态或宣传情况
        $("#isLicensespecial").change(function () {
            var option = $("#isLicensespecial option:selected");
            //要触发的事件
            if (option.val() == "1") {
                $(".licensespecialShow").show()
            } else {
                $(".licensespecialShow").hide()
            }
        })
        //是否开展排污许可清单式执法
        $("#isLicenselist").change(function () {
            var option = $("#isLicenselist option:selected");
            //要触发的事件
            if (option.val() == "1") {
                $(".islicenselistShow").show()
            } else {
                $(".islicenselistShow").hide()
            }
        })



        $("#caseEndType").change(function () {
            var option = $("#caseEndType option:selected");
            //要触发的事件
            if (option.val() == "2") {
                $(".isCaseTypeShow").show()
            } else if (option.val() == "3") {
                $(".isCaseTypeShow").hide()
                $(".isCaseTypeShow2").show()
            }
        })


    })



    //时间转换方法
    function timeZh(come, index) {
        var cpmedate = new Date(come);
        console.log("cpmedate", cpmedate)
        var dateNow = cpmedate.getDate();
        if (cpmedate.getDate() < 10) {
            dateNow = "0" + cpmedate.getDate();
        }
        var month = cpmedate.getMonth() + 1;
        if (month < 10) {
            month = "0" + month;
        }
        var ssss = cpmedate.getFullYear() + "-"+ month + "-" + dateNow;
        if (index == 1) {
            $("#ctsj1").val(ssss)
        }
        if (index == 2) {
            $("#ctsj2").val(ssss)
        }
        if (index == 3) {
            $("#ctsj3").val(ssss)
        }

    }


    function clearNoNum(obj) {
        obj.value = obj.value.replace(/[^\d.]/g, ""); //清除“数字”和“.”以外的字符
        obj.value = obj.value.replace(/^\./g, ""); //验证第一个字符是数字而不是.
        obj.value = obj.value.replace(/\.{2,}/g, "."); //只保留第一个. 清除多余的.
        obj.value = obj.value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
        //判断百分比不能超过100%


        if (parseFloat(obj.value) > 100) {
            obj.value = "";
            return;
        }
        ;
        if (obj.value.split(".")[0].length > 1) {
            if (obj.value.split(".")[0].substring(0, 1) == '0') {
                obj.value = "";
                return;
            }
        }
        //如果有小数点，判断 只能保留两位
        if (obj.value.indexOf(".") > -1) {
            if (obj.value.split(".")[1].length > 3) {
                obj.value = "";
                return;
            }
        }
    }

    function downloadFile(url, fileName) {
        if (url != null && url != "") {
            console.log(url)
            location.href = "${webpath}/filedownload.do?url=" + url + "&fileName=" + fileName;
        } else {
            swal({title: "提示", text: "您下载的附件不存在！", type: "info"});
        }
    };



    //表单校验
    //表单校验
    /*	$(document).ready(function() {
		 $('#ajslForm').formValidation({

			message : 'This value is not valid',
			/!*icon : {
				valid : 'glyphicon glyphicon-ok',
				invalid : 'glyphicon glyphicon-remove',
				validating : 'glyphicon glyphicon-refresh'
			},*!/
			autoFocus : true,
			/!*"propagandize" : {
				verbose: false,
				validators : {
					/!*notEmpty : {
                        message : '请填写省级及以上平台媒体宣传材料数量.'
                    },*!/
					regexp : {
						regexp : /^[0-9]+$/,
						message : '该项只能填写整数.'
					},
					greaterThan : {
						value : 0,
						message : ' '
					},
					lessThan : {
						value : 10000,
						message : '该项为小于等于10000的整数.'
					}
				}
			}*!/
			/!*fields : {
				"companyname" : {
					validators : {
						notEmpty : {
							message : '请填写机构名称（全称）.'
						}
					}
				}*!/
				/!*"polluternumber" : {
                    verbose: false,
                    validators : {
                        notEmpty : {
                            message : '请填写全省重点排污单位数量.'
                        },
                        regexp : {
                            regexp : /^[0-9]+$/,
                            message : '该项只能填写整数.'
                        },
                        greaterThan : {
                            value : 0,
                            message : ' '
                        },
                        lessThan : {
                            value : 10000,
                            message : '该项为小于等于10000的整数.'
                        }
                    }
                },*!/
			//}
		});
	});*/
    //表单提交
    $(document).ready(function () {
        $("#xxcj_AJSL_Butt").click(function () {
            var validate = true;
            //$("#ajslForm").data('formValidation').validate();
            //validate = $("#ajslForm").data('formValidation').isValid();
            if (validate) {
                //上传文件验证必填
                //激励奖励相关文件
                var filen1 = $("#jiliUrlname").val();
                var fileu1 = $("#jiliUrl").val()

                //知识竞赛支撑材料
                var filen2 = $("#jwInformUrlname").val();
                var fileu2 = $("#jwInformUrl").val();

                //竞赛比武活动网页截图
                var filen3 = $("#jwWebUrlname").val();
                var fileu3 = $("#jwWebUrl").val();

                //案卷评查支撑材料
                var filen4 = $("#pingchaUrlname").val();
                var fileu4 = $("#pingchaUrl").val();

                //着装及队列演练支撑材料
                var filen5 = $("#worksummaryUrlname").val();
                var fileu5 = $("#worksummaryUrl").val();


                //规范化建设支撑材料
                var filen6 = $("#caseinfoUrlname").val();
                var fileu6 = $("#caseinfoUrl").val();

                //考核奖惩执行情况支撑材料
                var filen8 = $("#caseEndsupUrlname").val();
                var fileu8 = $("#caseEndsupUrl").val();

                //人身安全保障制定情况支撑材料
                var filen9 = $("#dwReportUrlname").val();
                var fileu9 = $("#dwReportUrl").val();

                //尽职照单免责和失职照单问责制定情况支撑材料
                var filen11 = $("#lawCheckplanUrlname").val();
                var fileu11 = $("#lawCheckplanUrl").val();

                //稽查工作报告
                var filen12 = $("#lawCheckreportUrlname").val();
                var fileu12 = $("#lawCheckreportUrl").val();

                // 统一证件、着装支撑材料
                var filen13 = $("#dwSupUrlname").val();
                var fileu13 = $("#dwSupUrl").val();

                //竞赛比武支撑材料
                var filen20 = $("#systemSupUrlname").val();
                var fileu20 = $("#systemSupUrl").val();

                //执法公众满意度支撑材料
                var filen21 = $("#positivelistSupUrlname").val();
                var fileu21 = $("#positivelistSupUrl").val();

                //监督帮扶典型案例支撑材料
                var filen22 = $("#randomsupUrlname").val();
                var fileu22 = $("#randomsupUrl").val();
                //监督帮扶典型案例支撑材料
                var filen23 = $("#typicalcaseUrlname").val();
                var fileu23 = $("#typicalcaseUrl").val();
                // 执法普法制度执行情况支撑材料
                var filen24 = $("#guideUrlname").val();
                var fileu24 = $("#guideUrl").val();
                // 已建立人才库的相关文件
                var filen25 = $("#peopoolUrlname").val();
                var fileu25 = $("#peopoolUrl").val();

                //专项行动表现支撑材料
                var filen26 = $("#trainUrlname").val();
                var fileu26 = $("#trainUrl").val();

                //活动总结支撑材料
                var filen27 = $("#licenselawUrlname").val();
                var fileu27 = $("#licenselawUrl").val();

                //行政处罚自由裁量规定
                var filen28 = $("#discretionUrlname").val();
                var fileu28 = $("#discretionUrl").val();
                //查处弄虚作假违法犯罪支撑材料
                var filen29 = $("#licensespecialUrlname").val();
                var fileu29 = $("#licensespecialUrl").val();
                //查处弄虚作假违法犯罪支撑材料
                var filen30 = $("#licenselistUrlname").val();
                var fileu30 = $("#licenselistUrl").val();

                var ver = verify();
                if (ver == false) {
                    return false;
                }
                if (filen1 != "" && fileu1 != "") {
                    if (filen2 != "" && fileu2 != "") {
                        if (filen3 != "" && fileu3 != "") {
                            if (filen4 != "" && fileu4 != "") {
                                if (filen5 != "" && fileu5 != "") {
                                    if (filen6 != "" && fileu6 != "") {
                                        if (filen8 != "" && fileu8 != "") {
                                            if (filen9 != "" && fileu9 != "") {
                                                if (filen11 != "" && fileu11 != "") {
                                                    if (filen12 != "" && fileu12 != "") {
                                                        if (filen13 != "" && fileu13 != "") {
                                                            if (filen20 != "" && fileu20 != "") {
                                                                if (filen21 != "" && fileu21 != "") {
                                                                    if (filen22 != "" && fileu22 != "") {
                                                                        if (filen23 != "" && fileu23 != "") {
                                                                            if (filen24 != "" && fileu24 != "") {
                                                                                if (filen25 != "" && fileu25 != "") {
                                                                                    if (filen26 != "" && fileu26 != "") {
                                                                                        if (filen27 != "" && fileu27 != "") {
                                                                                            if (filen28 != "" && fileu28 != "") {
                                                                                                if (filen29 != "" && fileu29 != "") {
                                                                                                    if (filen30 != "" && fileu30 != "") {
                                                                                                        // 已建立人才库的相关文件
                                                                                                        var options = {
                                                                                                            url: WEBPATH + '/xxcj/newSaveProvinceCollection.do?isRecommend=1',
                                                                                                            type: 'post',
                                                                                                            success: function (data) {
                                                                                                                console.log("data", data)
                                                                                                                if (data.result == "error") {
                                                                                                                    swal({
                                                                                                                        title: "保存失败!",
                                                                                                                        text: data.message,
                                                                                                                        type: "error",
                                                                                                                        confirmButtonColor: "#d9534f"
                                                                                                                    });
                                                                                                                    return false;
                                                                                                                } else if (data.result == "success") {
                                                                                                                    swal({
                                                                                                                        title: "保存成功!",
                                                                                                                        type: "success",
                                                                                                                        closeOnConfirm: true,
                                                                                                                        confirmButtonText: "确定",
                                                                                                                        confirmButtonColor: "#d9534f"
                                                                                                                    }, function () {
                                                                                                                        business.addMainContentParserHtml('xxcj/provinceBase.do', '');

                                                                                                                    });
                                                                                                                    return false;
                                                                                                                } else {
                                                                                                                    swal({
                                                                                                                        title: "请勿重复提交表单!",
                                                                                                                        type: "warning",
                                                                                                                        // text:"请勿重复提交表单！",
                                                                                                                        closeOnConfirm: true,
                                                                                                                        confirmButtonText: "确定",
                                                                                                                        confirmButtonColor: "#d9534f"
                                                                                                                    }, function () {
                                                                                                                        business.addMainContentParserHtml('xxcj/provinceBase.do', '');

                                                                                                                    });
                                                                                                                }
                                                                                                            },
                                                                                                            error: function () {
                                                                                                                swal({
                                                                                                                    title: "服务异常,保存失败!",
                                                                                                                    text: "",
                                                                                                                    type: "error",
                                                                                                                    confirmButtonColor: "#d9534f"
                                                                                                                });
                                                                                                            }
                                                                                                        };
                                                                                                        $("#ajslForm").ajaxSubmit(options);
                                                                                                    } else {
                                                                                                        swal({
                                                                                                            title: "未上传附件",
                                                                                                            text: "请上传省本级制定的排污许可清单式执法通知文件或行动方案或现场指导等佐证材料!",
                                                                                                            type: "error",
                                                                                                            confirmButtonColor: "#d9534f"
                                                                                                        });
                                                                                                        return false;
                                                                                                    }
                                                                                                } else {
                                                                                                    swal({
                                                                                                        title: "未上传附件",
                                                                                                        text: "典型案例公开的网页截图或者通报文件!",
                                                                                                        type: "error",
                                                                                                        confirmButtonColor: "#d9534f"
                                                                                                    });
                                                                                                    return false;
                                                                                                }
                                                                                            } else {
                                                                                                swal({
                                                                                                    title: "未上传附件",
                                                                                                    text: "请上传行政处罚自由裁量规定!",
                                                                                                    type: "error",
                                                                                                    confirmButtonColor: "#d9534f"
                                                                                                });
                                                                                                return false;
                                                                                            }
                                                                                        } else {
                                                                                            swal({
                                                                                                title: "未上传附件",
                                                                                                text: "请上传加强排污许可执法监管的政策文件!",
                                                                                                type: "error",
                                                                                                confirmButtonColor: "#d9534f"
                                                                                            });
                                                                                            return false;
                                                                                        }
                                                                                    } else {
                                                                                        swal({
                                                                                            title: "未上传附件",
                                                                                            text: "请上传开展培训（岗位培训除外）的动态或宣传情况!",
                                                                                            type: "error",
                                                                                            confirmButtonColor: "#d9534f"
                                                                                        });
                                                                                        return false;
                                                                                    }
                                                                                } else {
                                                                                    swal({
                                                                                        title: "未上传附件",
                                                                                        text: "请上传已建立人才库的相关文件!",
                                                                                        type: "error",
                                                                                        confirmButtonColor: "#d9534f"
                                                                                    });
                                                                                    return false;
                                                                                }
                                                                            } else {
                                                                                swal({
                                                                                    title: "未上传附件",
                                                                                    text: "请上传监督执法正面清单制度执行情况支撑材料!",
                                                                                    type: "error",
                                                                                    confirmButtonColor: "#d9534f"
                                                                                });
                                                                                return false;
                                                                            }
                                                                        } else {
                                                                            swal({
                                                                                title: "未上传附件",
                                                                                text: "请上传双随机、一公开”监管工作执行情况支撑材料!",
                                                                                type: "error",
                                                                                confirmButtonColor: "#d9534f"
                                                                            });
                                                                            return false;
                                                                        }
                                                                    } else {
                                                                        swal({
                                                                            title: "未上传附件",
                                                                            text: "请上传专项行动表现支撑材料!",
                                                                            type: "error",
                                                                            confirmButtonColor: "#d9534f"
                                                                        });
                                                                        return false;
                                                                    }
                                                                } else {
                                                                    swal({
                                                                        title: "未上传附件",
                                                                        text: "请上传举报奖励制度执行情况支撑材料!",
                                                                        type: "error",
                                                                        confirmButtonColor: "#d9534f"
                                                                    });
                                                                    return false;
                                                                }
                                                            } else {
                                                                swal({
                                                                    title: "未上传附件",
                                                                    text: "请上传典型执法案例指导制度支撑材料!",
                                                                    type: "error",
                                                                    confirmButtonColor: "#d9534f"
                                                                });
                                                                return false;
                                                            }
                                                        } else {
                                                            swal({
                                                                title: "未上传附件",
                                                                text: "请上传统一证件、着装支撑材料!",
                                                                type: "error",
                                                                confirmButtonColor: "#d9534f"
                                                            });
                                                            return false;
                                                        }
                                                    } else {
                                                        swal({
                                                            title: "未上传附件",
                                                            text: "请上传稽查工作报告!",
                                                            type: "error",
                                                            confirmButtonColor: "#d9534f"
                                                        });
                                                        return false;
                                                    }
                                                } else {
                                                    swal({
                                                        title: "未上传附件",
                                                        text: "请上传稽查计划支撑材料!",
                                                        type: "error",
                                                        confirmButtonColor: "#d9534f"
                                                    });
                                                    return false;
                                                }
                                            } else {
                                                swal({
                                                    title: "未上传附件",
                                                    text: "请上传工作开展情况、取得成效等工作简报或报告!",
                                                    type: "error",
                                                    confirmButtonColor: "#d9534f"
                                                });
                                                return false;
                                            }
                                        } else {
                                            swal({
                                                title: "未上传附件",
                                                text: "请上传查办大案要案支撑材料支撑材料!",
                                                type: "error",
                                                confirmButtonColor: "#d9534f"
                                            });
                                            return false;
                                        }
                                    } else {
                                        swal({
                                            title: "未上传附件",
                                            text: "请上传案卷集或典型案例汇编相关文件!",
                                            type: "error",
                                            confirmButtonColor: "#d9534f"
                                        });
                                        return false;
                                    }
                                } else {
                                    swal({
                                        title: "未上传附件",
                                        text: "请上传评查工作总结或通报相关文件!",
                                        type: "error",
                                        confirmButtonColor: "#d9534f"
                                    });
                                    return false;
                                }
                            } else {
                                swal({
                                    title: "未上传附件",
                                    text: "请上传评查方案相关文件!",
                                    type: "error",
                                    confirmButtonColor: "#d9534f"
                                });
                                return false;
                            }
                        } else {
                            swal({
                                title: "未上传附件",
                                text: "请上传竞赛比武活动网页截图相关文件!",
                                type: "error",
                                confirmButtonColor: "#d9534f"
                            });
                            return false;
                        }
                    } else {
                        swal({
                            title: "未上传附件",
                            text: "请上传竞赛比武通知相关文件!",
                            type: "error",
                            confirmButtonColor: "#d9534f"
                        });
                        return false;
                    }
                } else {
                    swal({
                        title: "未上传附件",
                        text: "请上传激励奖励相关文件!",
                        type: "error",
                        confirmButtonColor: "#d9534f"
                    });
                    return false;
                }


            } else if (validate == null) {
                //表单未填写
                $("#ajslForm").data('formValidation').validate();
            }
        })


        //信息暂存
        setInterval(function () {
        $("#xxcj_XXZC_Butt").click(function () {
            var options = {
                url: WEBPATH + '/xxcj/newSaveProvinceCollection.do?isRecommend=0',
                type: 'post',
                success: function (data) {
                    if (data.result == "error") {
                        swal({title: "保存失败!", text: data.message, type: "error", confirmButtonColor: "#d9534f"});
                        return false;
                    } else if (data.result == "success") {
                        swal({
                            title: "保存成功!",
                            type: "success",
                            closeOnConfirm: true,
                            confirmButtonText: "确定",
                            confirmButtonColor: "#d9534f"
                        }, function () {
                            business.addMainContentParserHtml('xxcj/provinceBase.do', '');
                        });
                        return false;
                    }
                },
                error: function () {
                    swal({title: "服务异常,保存失败!", text: "", type: "error", confirmButtonColor: "#d9534f"});
                }
            };
            $("#ajslForm").ajaxSubmit(options);

        })
            },1000);

    });

    $(document).ready(function () {
        $(".topnav").accordion({
            accordion: false,
            speed: 500,
            closedSign: '[+]',
            openedSign: '[-]'
        });
    });

    function down(val) {
        var name = "";
        if (val == 1) {
            name = "省级大练兵组织情况excel模板";
        } else if (val == 2) {
            name = "队伍建设与管理excel模板";
        } else if (val == 3) {
            name = "监督帮扶典型案例excel模板";
        } else if (val == 4) {
            name = "竞赛比武excel模板";
        } else if (val == 5) {
            name = "省专项行动表现";
        }

        var path = "${webpath}/xxcj/xcclExcel.do";
        window.location.href = path + "?name=" + name;
    }


    //验证字段方法
    function verify() {
        //机构名称
        var agencyName = $("#agencyName").val();
        if (agencyName == "") {
            swal("机构名称为必填项", "操作失败了!", "error");
            return false;
        }

        // 是否采取激励措施
        var is_jili = $("#isJili").val();
        if (is_jili == "") {
            swal("是否采取激励措施为必填项", "操作失败了!", "error");
            return false;
        }
        // 激励形式
        var jiliName = $("#jiliName").val();
        if (jiliName == "") {
            swal("激励形式为必填项", "操作失败了!", "error");
            return false;
        }

        // 激励形式
        var jiliUrlname = $("#jiliUrlname").val();
        if (jiliUrlname == "") {
            swal("激励奖励相关文件为必填项", "操作失败了!", "error");
            return false;
        }

        // 是否开展竞赛比武
        var isJingwu = $("#isJingwu").val();
        if (isJingwu == "") {
            swal("竞赛比武 是否开展为必填项", "操作失败了!", "error");
            return false;
        }
        // 竞赛比武 是否联合其他部门开展
        var isLianhe = $("#isLianhe").val();
        if (isLianhe == "") {
            swal("竞赛比武-是否联合其他部门开展为必填项", "操作失败了!", "error");
            return false;
        }
        // 竞赛比武 竞赛比武通知为必填项
        var jwInformUrlname = $("#jwInformUrlname").val();
        if (jwInformUrlname == "") {
            swal("竞赛比武-竞赛比武通知为必填项", "操作失败了!", "error");
            return false;
        }
        // 竞赛比武 竞赛比武活动网页截图为必填项
        var jwWebUrlname = $("#jwWebUrlname").val();
        if (jwWebUrlname == "") {
            swal("竞赛比武-竞赛比武活动网页截图为必填项", "操作失败了!", "error");
            return false;
        }
        //是否组织开展案卷评查
        var isCasereview = $("#isCasereview").val();
        if (isCasereview == "") {
            swal("案卷评查 是否组织开展案卷评查为必填项", "操作失败了!", "error");
            return false;
        }

        //是否达到要求数量
        var isMeetRequirenum = $("#isMeetRequirenum").val();
        if (isMeetRequirenum == "") {
            swal("案卷评查 是否达到要求数量为必填项", "操作失败了!", "error");
            return false;
        }
        //是否形成评查工作总结或向下通报，反馈评查情况
        var isFeedback = $("#isFeedback").val();
        if (isFeedback == "") {
            swal("案卷评查 是否形成评查工作总结或向下通报，反馈评查情况为必填项", "操作失败了!", "error");
            return false;
        }

        //是否形成案卷集或典型案例汇编
        var isCasecollection = $("#isCasecollection").val();
        if (isCasecollection == "") {
            swal("是否开设大练兵专栏为必填项", "操作失败了!", "error");
            return false;
        }
        //评查方案为必填项
        var pingchaUrlname = $("#pingchaUrlname").val();
        if (pingchaUrlname == "") {
            swal("案卷评查-评查方案为必填项", "操作失败了!", "error");
            return false;
        }
        //评查工作总结或通报
        var worksummaryUrlname = $("#worksummaryUrlname").val();
        if (worksummaryUrlname == "") {
            swal("案卷评查-评查工作总结或通报为必填项", "操作失败了!", "error");
            return false;
        }

        // 查办大案要案情况类型
        var caseEndType = $("#caseEndType").val();
        if (caseEndType == "") {
            swal("查办大案要案情况类型为必填项", "操作失败了!", "error");
            return false;
        }


        // 2021年是否出台
        var isChutai = $("#isChutai").val();
        if (isChutai == "") {
            swal("2021年是否出台为必填项", "操作失败了!", "error");
            return false;
        }
        //  宣传数量
        var isLawcheck = $("#isLawcheck").val();
        if (isLawcheck == "") {
            swal("是否开展为必填项", "操作失败了!", "error");
            return false;
        }


        //  是否完成统一着装
        var isUnifyattire = $("#isUnifyattire").val();
        if (isUnifyattire == "") {
            swal("是否完成统一着装为必填项", "操作失败了!", "error");
            return false;
        }
        // 是否完成统一证件
        var isUnifycertificate = $("#isUnifycertificate").val();
        if (isUnifycertificate == "") {
            swal("是否完成统一证件", "操作失败了!", "error");
            return false;
        }
        // 否组织开展政策宣传、解读，发布典型案例
        var isTypicalcase = $("#isTypicalcase").val();
        if (isTypicalcase == "") {
            swal("是否组织开展政策宣传、解读，发布典型案例为必填项", "操作失败了!", "error");
            return false;
        }
        // 各地市是否实现举报奖励制度全覆盖
        var isCover = $("#isCover").val();
        if (isCover == "") {
            swal("各地市是否实现举报奖励制度全覆盖必填项", "操作失败了!", "error");
            return false;
        }
        // 是否辖区内所有地市级生态环境部门均公布正面清单企业名单，并实施动态管理
        var isPositivelistManage = $("#isPositivelistManage").val();
        if (isPositivelistManage == "") {
            swal("是否辖区内所有地市级生态环境部门均公布正面清单企业名单，并实施动态管理为必填项", "操作失败了!", "error");
            return false;
        }
        // 考核奖惩 执行情况 具体情况
        var isPositivelistWarn = $("#isPositivelistWarn").val();
        if (isPositivelistWarn == "") {
            swal("是否指导组织地市级生态环境部门通过座谈、宣讲、培训等方式对正面清单企业开展帮扶指导和提醒预警为必填项", "操作失败了!", "error");
            return false;
        }
        // “双随机”抽查结果公开率
        var randomRate = $("#randomRate").val();
        if (randomRate == "") {
            swal("“双随机”抽查结果公开率为必填项", "操作失败了!", "error");
            return false;
        }
        // 省级生态环境部门全年组织发布典型案例批次
        var typicalcasePici = $("#typicalcasePici").val();
        if (typicalcasePici == "") {
            swal("省级生态环境部门全年组织发布典型案例批次为必填项", "操作失败了!", "error");
            return false;
        }
        // 是否指导所有地市级生态环境部门开展针对中小企业重点执法帮扶、普法培训、“送法入企”活动 ：
        var isGuide = $("#isGuide").val();
        if (isGuide == "") {
            swal("是否指导所有地市级生态环境部门开展针对中小企业重点执法帮扶、普法培训、“送法入企”活动为必填项", "操作失败了!", "error");
            return false;
        }
        // 是否建立省级执法人才库
        var isPeopool = $("#isPeopool").val();
        if (isPeopool == "") {
            swal("是否建立省级执法人才库为必填项", "操作失败了!", "error");
            return false;
        }
        // 人 是否组织执法专业、专项培训
        var isTrain = $("#isTrain").val();
        if (isTrain == "") {
            swal("是否组织执法专业、专项培训为必填项", "操作失败了!", "error");
            return false;
        }
        // 是否出台加强排污许可执法监管政策措施
        var isLicenselaw = $("#isLicenselaw").val();
        if (isLicenselaw == "") {
            swal("是否出台加强排污许可执法监管政策措施为必填项", "操作失败了!", "error");
            return false;
        }
        // 是否细化完善排污许可行政处罚自由裁量规定
        var isDiscretion = $("#isDiscretion").val();
        if (isDiscretion == "") {
            swal("是否细化完善排污许可行政处罚自由裁量规定为必填项", "操作失败了!", "error");
            return false;
        }
        // 是否及时曝光排污许可违法典型案件
        var isLicensespecial = $("#isLicensespecial").val();
        if (isLicensespecial == "") {
            swal("是否及时曝光排污许可违法典型案件为必填项", "操作失败了!", "error");
            return false;
        }
        // 是否开展排污许可清单式执法
        var isLicenselist = $("#isLicenselist").val();
        if (isLicenselist == "") {
            swal("是否开展排污许可清单式执法为必填项", "操作失败了!", "error");
            return false;
        }
    }
</script>
</body>
</html>
