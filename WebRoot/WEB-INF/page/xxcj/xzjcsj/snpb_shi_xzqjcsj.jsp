<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
    <style type="text/css">

        .form-control{width: 380px;height: 39px;
            background: #FFFFFF;
            border: 1px solid #DCDFE6;
            border-radius: 5px;}
        .table_input{width:100%;min-width:100%}
        /*.shangbao_panel{margin:5px 0 0 21px;background: #FAFAFA;border-radius: 3px;padding: 29px 0 0 18px;height: 208px;}*/
        /*.shangbao_titlt{margin:0 0 0 184px;font-weight: 400;color: #333333;font-size: 16px;padding:0 30px 10px 0}*/
        /*.shangbao_table{margin-left: 118px;}*/
        .col-sm-12{padding-left: 0}
    </style>
    <script type="text/javascript">
        function butChange(index){
            var butVal = $("#reButton"+index).attr("value");
            var butStyle = $("#reButton"+index).attr("style");
            if(butStyle!=""&&typeof(butStyle)!="undefined"){
                $("#reButton"+index).attr("style","");
                $("#uploadTr"+index).attr("style","display:none");
                $("#wsc"+index).attr("style","");
            }else{
                if(butVal=="重新上传"){
                    $("#reButton"+index).attr("value","返回");
                    $("#uploadTr"+index).attr("style","");
                    $("#wsc"+index).attr("style","display:none");
                }else if(butVal=="返回"){
                    $("#reButton"+index).attr("value","重新上传");
                    $("#uploadTr"+index).attr("style","display:none");
                    $("#wsc"+index).attr("style","");
                }
            }
        }


        $(document).ready(function(){
            //日常监督执法工作excel文件上传
            $("#dayLawFile").fileinput({
                uploadUrl: WEBPATH+'/pdffileupload.do', // you must set a valid URL here else you will get an error
                allowedFileExtensions : ['xls', 'xlsx'],
                language:'zh',
                browseClass:'btn btn-danger',//按钮样式
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize:1,
                maxFileSize:53500,
                enctype: 'multipart/form-data',
                dropZoneTitle:"可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany:'选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading:'文件预览',
                msgInvalidFileExtension:'不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected:'请选择文件',
                msgValidationError:'文件类型不正确或文件过大',
                initialPreviewFileType:'els',
                browseLabel:"选择文件",
                removeLabel:'删除',
                removeTitle:'删除文件',
                uploadLabel:'上传',
                uploadTitle:'上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview:false,
                autoReplace:true,
                slugCallback: function(filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function(event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function(event, data, previewId, index) {//文件上传完成执行操作

                $("#dayLaw_Fileurl").val(data.response.url);
                $("#dayLaw_Filename").val(data.response.fileRealName);
                // $("#wsc1").text(data.response.fileRealName);
                $("#wsc1").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled',false);

                butChange('1');
            })

            $("#reButton1").click(function(){
                butChange('1');
            });
            /* //排污企业证明材料文件上传、
             $("#polluterfile").fileinput({
                uploadUrl: WEBPATH+'/pdffileupload.do', // you must set a valid URL here else you will get an error
                allowedFileExtensions : ['pdf'],
                language:'zh',
                browseClass:'btn btn-danger',//按钮样式
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize:1,
                maxFileSize:53500,
                enctype: 'multipart/form-data',
                dropZoneTitle:"可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany:'选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading:'文件预览',
                msgInvalidFileExtension:'不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected:'请选择文件',
                msgValidationError:'文件类型不正确',
                initialPreviewFileType:'pdf',
                browseLabel:"选择文件",
                removeLabel:'删除',
                removeTitle:'删除文件',
                uploadLabel:'上传',
                uploadTitle:'上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview:false,
                autoReplace:true,
                slugCallback: function(filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function(event, data, previewId, index) { //文件开始上传操作

               $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function(event, data, previewId, index) {//文件上传完成执行操作

                $("#polluter_Fileurl").val(data.response.url);
                  $("#polluter_Filename").val(data.response.fileRealName);
                  $("#wsc").text(data.response.fileRealName);
                  $("#xxcj_AJSL_Butt").prop('disabled',false);
                  butChange('');
            })

            $("#reButton").click(function(){
                butChange('');
            });

            //考核奖惩制度
            $("#assessmentfile").fileinput({
                uploadUrl: WEBPATH+'/pdffileupload.do', // you must set a valid URL here else you will get an error
                allowedFileExtensions : ['pdf','rar'],
                language:'zh',
                browseClass:'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize:1,
                maxFileSize:52000,
                enctype: 'multipart/form-data',
                dropZoneTitle:"可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany:'选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading:'文件预览',
                msgInvalidFileExtension:'不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected:'请选择文件',
                msgValidationError:'文件类型不正确',
                initialPreviewFileType:'pdf',
                browseLabel:"选择文件",
                removeLabel:'删除',
                removeTitle:'删除文件',
                uploadLabel:'上传',
                uploadTitle:'上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview:false,
                autoReplace:true,
                slugCallback: function(filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function(event, data, previewId, index) { //文件开始上传操作

               $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function(event, data, previewId, index) {//文件上传完成执行操作

                $("#assessment_Fileurl").val(data.response.url);
                  $("#assessment_Filename").val(data.response.fileRealName);
                  $("#wsc7").text(data.response.fileRealName);
                  $("#xxcj_AJSL_Butt").prop('disabled',false);

                  butChange('7');
            })
            $("#reButton7").click(function(){
                butChange('7');
            });
            //执法人员人身安全保障制度
            $("#lawenforcfile").fileinput({
                uploadUrl: WEBPATH+'/pdffileupload.do', // you must set a valid URL here else you will get an error
                allowedFileExtensions : ['pdf','rar'],
                language:'zh',
                browseClass:'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize:1,
                maxFileSize:52000,
                enctype: 'multipart/form-data',
                dropZoneTitle:"可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany:'选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading:'文件预览',
                msgInvalidFileExtension:'不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected:'请选择文件',
                msgValidationError:'文件类型不正确',
                initialPreviewFileType:'pdf',
                browseLabel:"选择文件",
                removeLabel:'删除',
                removeTitle:'删除文件',
                uploadLabel:'上传',
                uploadTitle:'上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview:false,
                autoReplace:true,
                slugCallback: function(filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function(event, data, previewId, index) { //文件开始上传操作

               $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function(event, data, previewId, index) {//文件上传完成执行操作

                $("#lawenforc_Fileurl").val(data.response.url);
                  $("#lawenforc_Filename").val(data.response.fileRealName);
                  $("#wsc0").text(data.response.fileRealName);
                  $("#xxcj_AJSL_Butt").prop('disabled',false);
                  butChange('0');
            })
            $("#reButton0").click(function(){
                butChange('0');
            });*/

            //尽职照单免责和失职照单问责制度文件上传、
            $("#randomInfoUrlnamefile").fileinput({
                uploadUrl: WEBPATH+'/pdffileupload.do', // you must set a valid URL here else you will get an error
                allowedFileExtensions : ['pdf','rar'],
                language:'zh',
                browseClass:'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize:1,
                maxFileSize:52000,
                enctype: 'multipart/form-data',
                dropZoneTitle:"可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany:'选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading:'文件预览',
                msgInvalidFileExtension:'不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected:'请选择文件',
                msgValidationError:'文件类型不正确',
                initialPreviewFileType:'pdf',
                browseLabel:"选择文件",
                removeLabel:'删除',
                removeTitle:'删除文件',
                uploadLabel:'上传',
                uploadTitle:'上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview:false,
                autoReplace:true,
                slugCallback: function(filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function(event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function(event, data, previewId, index) {//文件上传完成执行操作

                $("#accountability_Fileurl").val(data.response.url);
                $("#accountability_Filename").val(data.response.fileRealName);
                $("#wsc9").text(data.response.fileRealName);
                $("#xxcj_AJSL_Butt").prop('disabled',false);

                butChange(9);
            })

            $("#reButton9").click(function(){
                butChange(9);
            });


            //集体事迹材料文件上传、
            /*$("#evidencefile").fileinput({
               uploadUrl: WEBPATH+'/pdffileupload.do', // you must set a valid URL here else you will get an error
               allowedFileExtensions : ['doc','docx'],
               language:'zh',
               browseClass:'btn btn-danger',
               //overwriteInitial: true,
               minFileCount: 1,
               maxFileCount: 1,
               minFileSize:1,
               maxFileSize:1060,
               enctype: 'multipart/form-data',
               dropZoneTitle:"可拖拽文件到此处...",
               initialPreviewShowDelete: false,
               msgFilesTooMany:'选择上传的文件数量({n}) 超过允许的最大数值{m}！',
               msgZoomModalHeading:'文件预览',
               msgInvalidFileExtension:'不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
               msgNoFilesSelected:'请选择文件',
               msgValidationError:'文件类型不正确或文件过大',
               initialPreviewFileType:'pdf',
               browseLabel:"选择文件",
               removeLabel:'删除',
               removeTitle:'删除文件',
               uploadLabel:'上传',
               uploadTitle:'上传文件',
               cancelLabel: '取消',
               cancelTitle: '取消上传',
               showPreview:false,
               autoReplace:true,
               slugCallback: function(filename) {
                   return filename.replace('(', '_').replace(']', '_');
               }
           }).on('filepreupload', function(event, data, previewId, index) { //文件开始上传操作

              $("#xxcj_AJSL_Butt").prop('disabled', true);

           }).on('fileuploaded', function(event, data, previewId, index) {//文件上传完成执行操作

               $("#evidence_Fileurl").val(data.response.url);
                 $("#evidence_Filename").val(data.response.fileRealName);
                 $("#wsc2").text(data.response.fileRealName);
                 $("#xxcj_AJSL_Butt").prop('disabled',false);
                 butChange(2);
           });

           $("#reButton2").click(function(){
               butChange(2);
           });*/

            //*查办大案要案情况支撑材料
            $("#picturefile").fileinput({
                uploadUrl: WEBPATH+'/pdffileupload.do', // you must set a valid URL here else you will get an error
                allowedFileExtensions : ['jpg','png','bmp'],
                language:'zh',
                browseClass:'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize:1,
                maxFileSize:21500,
                enctype: 'multipart/form-data',
                dropZoneTitle:"可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany:'选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading:'文件预览',
                msgInvalidFileExtension:'不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected:'请选择文件',
                msgValidationError:'文件类型不正确或文件过大',
                initialPreviewFileType:'pdf',
                browseLabel:"选择文件",
                removeLabel:'删除',
                removeTitle:'删除文件',
                uploadLabel:'上传',
                uploadTitle:'上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview:false,
                autoReplace:true,
                slugCallback: function(filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function(event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function(event, data, previewId, index) {//文件上传完成执行操作

                $("#picture_Fileurl").val(data.response.url);
                $("#picture_Filename").val(data.response.fileRealName);
                $("#wsc3").text(data.response.fileRealName);
                $("#xxcj_AJSL_Butt").prop('disabled',false);
                butChange(3);
            });

            $("#reButton3").click(function(){
                butChange(3);
            });

            //典型案例采纳情况支撑材料上传
            $("#typicalCaseAcceptFile").fileinput({
                uploadUrl: WEBPATH+'/pdffileupload.do', // you must set a valid URL here else you will get an error
                allowedFileExtensions : ['rar'],
                language:'zh',
                browseClass:'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize:1,
                maxFileSize:53500,
                enctype: 'multipart/form-data',
                dropZoneTitle:"可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany:'选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading:'文件预览',
                msgInvalidFileExtension:'不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected:'请选择文件',
                msgValidationError:'文件类型不正确或文件过大',
                initialPreviewFileType:'rar',
                browseLabel:"选择文件",
                removeLabel:'删除',
                removeTitle:'删除文件',
                uploadLabel:'上传',
                uploadTitle:'上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview:false,
                autoReplace:true,
                slugCallback: function(filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function(event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function(event, data, previewId, index) {//文件上传完成执行操作

                $("#typicalCaseAccept_Fileurl").val(data.response.url);
                $("#typicalCaseAccept_Filename").val(data.response.fileRealName);
                // $("#wsc2").text(data.response.fileRealName);
                $("#wsc2").addClass("wscbj");
                $("#wsc2").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled',false);
                butChange(2);
            });

            $("#reButton2").click(function(){
                butChange(2);
                $("#wsc2").addClass("wscbj");
            });


            //制度创新与执法任务支撑材料上传
            $("#zdcxyzfrwzcclFile").fileinput({
                uploadUrl: WEBPATH+'/pdffileupload.do', // you must set a valid URL here else you will get an error
                allowedFileExtensions : ['rar'],
                language:'zh',
                browseClass:'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize:1,
                maxFileSize:53500,
                enctype: 'multipart/form-data',
                dropZoneTitle:"可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany:'选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading:'文件预览',
                msgInvalidFileExtension:'不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected:'请选择文件',
                msgValidationError:'文件类型不正确或文件过大',
                initialPreviewFileType:'rar',
                browseLabel:"选择文件",
                removeLabel:'删除',
                removeTitle:'删除文件',
                uploadLabel:'上传',
                uploadTitle:'上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview:false,
                autoReplace:true,
                slugCallback: function(filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function(event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function(event, data, previewId, index) {//文件上传完成执行操作

                $("#zdcxyzfrwzccl_Fileurl").val(data.response.url);
                $("#zdcxyzfrwzccl_Filename").val(data.response.fileRealName);
                // $("#wsc3").text(data.response.fileRealName);
                $("#wsc3").addClass("wscbj");
                $("#wsc3").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled',false);
                butChange(3);
            });

            $("#reButton3").click(function(){
                butChange(3);
                $("#wsc3").addClass("wscbj");
            });


            //发现问题能力支撑材料上传
            $("#findQuestionFile").fileinput({
                uploadUrl: WEBPATH+'/pdffileupload.do', // you must set a valid URL here else you will get an error
                allowedFileExtensions : ['rar'],
                language:'zh',
                browseClass:'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize:1,
                maxFileSize:53500,
                enctype: 'multipart/form-data',
                dropZoneTitle:"可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany:'选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading:'文件预览',
                msgInvalidFileExtension:'不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected:'请选择文件',
                msgValidationError:'文件类型不正确或文件过大',
                initialPreviewFileType:'rar',
                browseLabel:"选择文件",
                removeLabel:'删除',
                removeTitle:'删除文件',
                uploadLabel:'上传',
                uploadTitle:'上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview:false,
                autoReplace:true,
                slugCallback: function(filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function(event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function(event, data, previewId, index) {//文件上传完成执行操作

                $("#findQuestion_Fileurl").val(data.response.url);
                $("#findQuestion_Filename").val(data.response.fileRealName);
                // $("#wsc4").text(data.response.fileRealName);
                $("#wsc4").addClass("wscbj");
                $("#wsc4").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled',false);
                butChange(4);
            });

            $("#reButton4").click(function(){
                butChange(4);
                $("#wsc4").addClass("wscbj");
            });


            //执法公众满意度支撑材料上传
            $("#lawgzmydFile").fileinput({
                uploadUrl: WEBPATH+'/pdffileupload.do', // you must set a valid URL here else you will get an error
                allowedFileExtensions : ['rar'],
                language:'zh',
                browseClass:'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize:1,
                maxFileSize:53500,
                enctype: 'multipart/form-data',
                dropZoneTitle:"可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany:'选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading:'文件预览',
                msgInvalidFileExtension:'不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected:'请选择文件',
                msgValidationError:'文件类型不正确或文件过大',
                initialPreviewFileType:'rar',
                browseLabel:"选择文件",
                removeLabel:'删除',
                removeTitle:'删除文件',
                uploadLabel:'上传',
                uploadTitle:'上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview:false,
                autoReplace:true,
                slugCallback: function(filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function(event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function(event, data, previewId, index) {//文件上传完成执行操作

                $("#lawgzmyd_Fileurl").val(data.response.url);
                $("#lawgzmyd_Filename").val(data.response.fileRealName);
                // $("#wsc5").text(data.response.fileRealName);
                $("#wsc5").addClass("wscbj");
                $("#wsc5").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled',false);
                butChange(5);
            });

            $("#reButton5").click(function(){
                butChange(5);
                $("#wsc5").addClass("wscbj");
            });

            //专项行动表现excel文件上传
            $("#citySpecialActionFile").fileinput({
                uploadUrl: WEBPATH+'/pdffileupload.do', // you must set a valid URL here else you will get an error
                allowedFileExtensions : ['xls', 'xlsx'],
                language:'zh',
                browseClass:'btn btn-danger',//按钮样式
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize:1,
                maxFileSize:53500,
                enctype: 'multipart/form-data',
                dropZoneTitle:"可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany:'选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading:'文件预览',
                msgInvalidFileExtension:'不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected:'请选择文件',
                msgValidationError:'文件类型不正确或文件过大',
                initialPreviewFileType:'els',
                browseLabel:"选择文件",
                removeLabel:'删除',
                removeTitle:'删除文件',
                uploadLabel:'上传',
                uploadTitle:'上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview:false,
                autoReplace:true,
                slugCallback: function(filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function(event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function(event, data, previewId, index) {//文件上传完成执行操作

                $("#citySpecialAction_Fileurl").val(data.response.url);
                $("#citySpecialAction_Filename").val(data.response.fileRealName);
                // $("#wsc6").text(data.response.fileRealName);
                $("#wsc6").addClass("wscbj");
                $("#wsc6").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled',false);

                butChange('6');
            })

            $("#reButton6").click(function(){
                butChange('6');
                $("#wsc5").addClass("wscbj");
            });

            //专项行动表现支撑材料上传
            $("#citySpecialActionShowFile").fileinput({
                uploadUrl: WEBPATH+'/pdffileupload.do', // you must set a valid URL here else you will get an error
                allowedFileExtensions : ['rar'],
                language:'zh',
                browseClass:'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize:1,
                maxFileSize:53500,
                enctype: 'multipart/form-data',
                dropZoneTitle:"可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany:'选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading:'文件预览',
                msgInvalidFileExtension:'不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected:'请选择文件',
                msgValidationError:'文件类型不正确或文件过大',
                initialPreviewFileType:'rar',
                browseLabel:"选择文件",
                removeLabel:'删除',
                removeTitle:'删除文件',
                uploadLabel:'上传',
                uploadTitle:'上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview:false,
                autoReplace:true,
                slugCallback: function(filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function(event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function(event, data, previewId, index) {//文件上传完成执行操作

                $("#citySpecialActionShow_Fileurl").val(data.response.url);
                $("#citySpecialActionShow_Filename").val(data.response.fileRealName);
                // $("#wsc7").text(data.response.fileRealName);
                $("#wsc7").addClass("wscbj8");
                $("#wsc7").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled',false);
                butChange(7);
            });

            $("#reButton7").click(function(){
                butChange(7);
                $("#wsc7").addClass("wscbj8");
            });

            //打击弄虚作假违法犯罪案件支撑材料上传
            $("#ccnxzjwffzCaseFile").fileinput({
                uploadUrl: WEBPATH+'/pdffileupload.do', // you must set a valid URL here else you will get an error
                allowedFileExtensions : ['rar'],
                language:'zh',
                browseClass:'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize:1,
                maxFileSize:53500,
                enctype: 'multipart/form-data',
                dropZoneTitle:"可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany:'选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading:'文件预览',
                msgInvalidFileExtension:'不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected:'请选择文件',
                msgValidationError:'文件类型不正确或文件过大',
                initialPreviewFileType:'rar',
                browseLabel:"选择文件",
                removeLabel:'删除',
                removeTitle:'删除文件',
                uploadLabel:'上传',
                uploadTitle:'上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview:false,
                autoReplace:true,
                slugCallback: function(filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function(event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function(event, data, previewId, index) {//文件上传完成执行操作

                $("#ccnxzjwffzCase_Fileurl").val(data.response.url);
                $("#ccnxzjwffzCase_Filename").val(data.response.fileRealName);
                // $("#wsc8").text(data.response.fileRealName);
                $("#wsc8").addClass("wscbj8");
                $("#wsc8").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled',false);
                butChange(8);
            });

            $("#reButton8").click(function(){
                butChange(8);
                $("#wsc8").addClass("wscbj8");
            });


            //发现问题能力支撑材料上传
            $("#randomDecisionfile").fileinput({
                uploadUrl: WEBPATH+'/pdffileupload.do', // you must set a valid URL here else you will get an error
                allowedFileExtensions : ['rar'],
                language:'zh',
                browseClass:'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize:1,
                maxFileSize:53500,
                enctype: 'multipart/form-data',
                dropZoneTitle:"可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany:'选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading:'文件预览',
                msgInvalidFileExtension:'不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected:'请选择文件',
                msgValidationError:'文件类型不正确或文件过大',
                initialPreviewFileType:'rar',
                browseLabel:"选择文件",
                removeLabel:'删除',
                removeTitle:'删除文件',
                uploadLabel:'上传',
                uploadTitle:'上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview:false,
                autoReplace:true,
                slugCallback: function(filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function(event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function(event, data, previewId, index) {//文件上传完成执行操作

                // $("#findQuestion_Fileurl").val(data.response.url);
                // $("#findQuestion_Filename").val(data.response.fileRealName);
                // $("#wsc4").text(data.response.fileRealName);
                $("#wsc10").addClass("wscbj");
                $("#wsc10").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled',false);
                butChange(10);
            });

            $("#reButton10").click(function(){
                butChange(10);
                $("#wsc10").addClass("wscbj");
            });


            //举报奖励制度案情简介说明材料
            $("#caseInfoFile").fileinput({
                uploadUrl: WEBPATH+'/pdffileupload.do', // you must set a valid URL here else you will get an error
                allowedFileExtensions : ['rar'],
                language:'zh',
                browseClass:'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize:1,
                maxFileSize:53500,
                enctype: 'multipart/form-data',
                dropZoneTitle:"可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany:'选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading:'文件预览',
                msgInvalidFileExtension:'不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected:'请选择文件',
                msgValidationError:'文件类型不正确或文件过大',
                initialPreviewFileType:'rar',
                browseLabel:"选择文件",
                removeLabel:'删除',
                removeTitle:'删除文件',
                uploadLabel:'上传',
                uploadTitle:'上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview:false,
                autoReplace:true,
                slugCallback: function(filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function(event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function(event, data, previewId, index) {//文件上传完成执行操作

                // $("#findQuestion_Fileurl").val(data.response.url);
                // $("#findQuestion_Filename").val(data.response.fileRealName);
                // $("#wsc4").text(data.response.fileRealName);
                $("#wsc11").addClass("wscbj");
                $("#wsc11").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled',false);
                butChange(11);
            });

            $("#reButton11").click(function(){
                butChange(11);
                $("#wsc11").addClass("wscbj");
            });

            //举报奖励制度处罚决定书的扫描件
            $("#desicionFile").fileinput({
                uploadUrl: WEBPATH+'/pdffileupload.do', // you must set a valid URL here else you will get an error
                allowedFileExtensions : ['rar'],
                language:'zh',
                browseClass:'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize:1,
                maxFileSize:53500,
                enctype: 'multipart/form-data',
                dropZoneTitle:"可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany:'选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading:'文件预览',
                msgInvalidFileExtension:'不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected:'请选择文件',
                msgValidationError:'文件类型不正确或文件过大',
                initialPreviewFileType:'rar',
                browseLabel:"选择文件",
                removeLabel:'删除',
                removeTitle:'删除文件',
                uploadLabel:'上传',
                uploadTitle:'上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview:false,
                autoReplace:true,
                slugCallback: function(filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function(event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function(event, data, previewId, index) {//文件上传完成执行操作

                // $("#findQuestion_Fileurl").val(data.response.url);
                // $("#findQuestion_Filename").val(data.response.fileRealName);
                // $("#wsc4").text(data.response.fileRealName);
                $("#wsc12").addClass("wscbj");
                $("#wsc12").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled',false);
                butChange(12);
            });

            $("#reButton12").click(function(){
                butChange(12);
                $("#wsc12").addClass("wscbj");
            });

            // 推行非现场监管方式5个案件的案情简介说明材料
            $("#offsiteInfoFile").fileinput({
                uploadUrl: WEBPATH+'/pdffileupload.do', // you must set a valid URL here else you will get an error
                allowedFileExtensions : ['rar'],
                language:'zh',
                browseClass:'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize:1,
                maxFileSize:53500,
                enctype: 'multipart/form-data',
                dropZoneTitle:"可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany:'选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading:'文件预览',
                msgInvalidFileExtension:'不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected:'请选择文件',
                msgValidationError:'文件类型不正确或文件过大',
                initialPreviewFileType:'rar',
                browseLabel:"选择文件",
                removeLabel:'删除',
                removeTitle:'删除文件',
                uploadLabel:'上传',
                uploadTitle:'上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview:false,
                autoReplace:true,
                slugCallback: function(filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function(event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function(event, data, previewId, index) {//文件上传完成执行操作

                // $("#findQuestion_Fileurl").val(data.response.url);
                // $("#findQuestion_Filename").val(data.response.fileRealName);
                // $("#wsc4").text(data.response.fileRealName);
                $("#wsc13").addClass("wscbj");
                $("#wsc13").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled',false);
                butChange(13);
            });

            $("#reButton13").click(function(){
                butChange(13);
                $("#wsc13").addClass("wscbj");
            });

            // 推行非现场监管方式5个案件的案情简介说明材料
            $("#offsiteDecisionFile").fileinput({
                uploadUrl: WEBPATH+'/pdffileupload.do', // you must set a valid URL here else you will get an error
                allowedFileExtensions : ['rar'],
                language:'zh',
                browseClass:'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize:1,
                maxFileSize:53500,
                enctype: 'multipart/form-data',
                dropZoneTitle:"可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany:'选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading:'文件预览',
                msgInvalidFileExtension:'不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected:'请选择文件',
                msgValidationError:'文件类型不正确或文件过大',
                initialPreviewFileType:'rar',
                browseLabel:"选择文件",
                removeLabel:'删除',
                removeTitle:'删除文件',
                uploadLabel:'上传',
                uploadTitle:'上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview:false,
                autoReplace:true,
                slugCallback: function(filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function(event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function(event, data, previewId, index) {//文件上传完成执行操作

                // $("#findQuestion_Fileurl").val(data.response.url);
                // $("#findQuestion_Filename").val(data.response.fileRealName);
                // $("#wsc4").text(data.response.fileRealName);
                $("#wsc14").addClass("wscbj");
                $("#wsc14").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled',false);
                butChange(14);
            });

            $("#reButton14").click(function(){
                butChange(14);
                $("#wsc14").addClass("wscbj");
            });

            //  执法监测计划的文件
            $("#lawplanFile").fileinput({
                uploadUrl: WEBPATH+'/pdffileupload.do', // you must set a valid URL here else you will get an error
                allowedFileExtensions : ['rar'],
                language:'zh',
                browseClass:'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize:1,
                maxFileSize:53500,
                enctype: 'multipart/form-data',
                dropZoneTitle:"可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany:'选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading:'文件预览',
                msgInvalidFileExtension:'不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected:'请选择文件',
                msgValidationError:'文件类型不正确或文件过大',
                initialPreviewFileType:'rar',
                browseLabel:"选择文件",
                removeLabel:'删除',
                removeTitle:'删除文件',
                uploadLabel:'上传',
                uploadTitle:'上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview:false,
                autoReplace:true,
                slugCallback: function(filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function(event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function(event, data, previewId, index) {//文件上传完成执行操作

                // $("#findQuestion_Fileurl").val(data.response.url);
                // $("#findQuestion_Filename").val(data.response.fileRealName);
                // $("#wsc4").text(data.response.fileRealName);
                $("#wsc15").addClass("wscbj");
                $("#wsc15").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled',false);
                butChange(15);
            });

            $("#reButton15").click(function(){
                butChange(15);
                $("#wsc15").addClass("wscbj");
            });

            //  经费纳入执法工作预算的佐证材料，文件或者截图
            $("#lawplansupFile").fileinput({
                uploadUrl: WEBPATH+'/pdffileupload.do', // you must set a valid URL here else you will get an error
                allowedFileExtensions : ['rar'],
                language:'zh',
                browseClass:'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize:1,
                maxFileSize:53500,
                enctype: 'multipart/form-data',
                dropZoneTitle:"可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany:'选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading:'文件预览',
                msgInvalidFileExtension:'不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected:'请选择文件',
                msgValidationError:'文件类型不正确或文件过大',
                initialPreviewFileType:'rar',
                browseLabel:"选择文件",
                removeLabel:'删除',
                removeTitle:'删除文件',
                uploadLabel:'上传',
                uploadTitle:'上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview:false,
                autoReplace:true,
                slugCallback: function(filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function(event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function(event, data, previewId, index) {//文件上传完成执行操作

                // $("#findQuestion_Fileurl").val(data.response.url);
                // $("#findQuestion_Filename").val(data.response.fileRealName);
                // $("#wsc4").text(data.response.fileRealName);
                $("#wsc16").addClass("wscbj");
                $("#wsc16").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled',false);
                butChange(16);
            });

            $("#reButton16").click(function(){
                butChange(16);
                $("#wsc16").addClass("wscbj");
            });

            //第三方辅助执法机制案情简介说明材料
            $("#fulawInfoFile").fileinput({
                uploadUrl: WEBPATH+'/pdffileupload.do', // you must set a valid URL here else you will get an error
                allowedFileExtensions : ['rar'],
                language:'zh',
                browseClass:'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize:1,
                maxFileSize:53500,
                enctype: 'multipart/form-data',
                dropZoneTitle:"可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany:'选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading:'文件预览',
                msgInvalidFileExtension:'不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected:'请选择文件',
                msgValidationError:'文件类型不正确或文件过大',
                initialPreviewFileType:'rar',
                browseLabel:"选择文件",
                removeLabel:'删除',
                removeTitle:'删除文件',
                uploadLabel:'上传',
                uploadTitle:'上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview:false,
                autoReplace:true,
                slugCallback: function(filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function(event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function(event, data, previewId, index) {//文件上传完成执行操作

                // $("#findQuestion_Fileurl").val(data.response.url);
                // $("#findQuestion_Filename").val(data.response.fileRealName);
                // $("#wsc4").text(data.response.fileRealName);
                $("#wsc17").addClass("wscbj");
                $("#wsc17").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled',false);
                butChange(17);
            });

            $("#reButton17").click(function(){
                butChange(17);
                $("#wsc17").addClass("wscbj");
            });

            //第三方辅助执法机制处罚决定书的扫描件
            $("#fwlawDesicionFile").fileinput({
                uploadUrl: WEBPATH+'/pdffileupload.do', // you must set a valid URL here else you will get an error
                allowedFileExtensions : ['rar'],
                language:'zh',
                browseClass:'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize:1,
                maxFileSize:53500,
                enctype: 'multipart/form-data',
                dropZoneTitle:"可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany:'选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading:'文件预览',
                msgInvalidFileExtension:'不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected:'请选择文件',
                msgValidationError:'文件类型不正确或文件过大',
                initialPreviewFileType:'rar',
                browseLabel:"选择文件",
                removeLabel:'删除',
                removeTitle:'删除文件',
                uploadLabel:'上传',
                uploadTitle:'上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview:false,
                autoReplace:true,
                slugCallback: function(filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function(event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function(event, data, previewId, index) {//文件上传完成执行操作

                // $("#findQuestion_Fileurl").val(data.response.url);
                // $("#findQuestion_Filename").val(data.response.fileRealName);
                // $("#wsc4").text(data.response.fileRealName);
                $("#wsc18").addClass("wscbj");
                $("#wsc18").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled',false);
                butChange(18);
            });

            $("#reButton18").click(function(){
                butChange(18);
                $("#wsc18").addClass("wscbj");
            });

            //开展相关活动的新闻稿网页截图或活动方案
            $("#lawGuideFile").fileinput({
                uploadUrl: WEBPATH+'/pdffileupload.do', // you must set a valid URL here else you will get an error
                allowedFileExtensions : ['rar'],
                language:'zh',
                browseClass:'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize:1,
                maxFileSize:53500,
                enctype: 'multipart/form-data',
                dropZoneTitle:"可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany:'选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading:'文件预览',
                msgInvalidFileExtension:'不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected:'请选择文件',
                msgValidationError:'文件类型不正确或文件过大',
                initialPreviewFileType:'rar',
                browseLabel:"选择文件",
                removeLabel:'删除',
                removeTitle:'删除文件',
                uploadLabel:'上传',
                uploadTitle:'上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview:false,
                autoReplace:true,
                slugCallback: function(filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function(event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function(event, data, previewId, index) {//文件上传完成执行操作

                // $("#findQuestion_Fileurl").val(data.response.url);
                // $("#findQuestion_Filename").val(data.response.fileRealName);
                // $("#wsc4").text(data.response.fileRealName);
                $("#wsc19").addClass("wscbj");
                $("#wsc19").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled',false);
                butChange(19);
            });

            $("#reButton19").click(function(){
                butChange(19);
                $("#wsc19").addClass("wscbj");
            });

            //开展相关活动的新闻稿网页截图或活动方案
            $("#assessmentFile").fileinput({
                uploadUrl: WEBPATH+'/pdffileupload.do', // you must set a valid URL here else you will get an error
                allowedFileExtensions : ['rar'],
                language:'zh',
                browseClass:'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize:1,
                maxFileSize:53500,
                enctype: 'multipart/form-data',
                dropZoneTitle:"可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany:'选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading:'文件预览',
                msgInvalidFileExtension:'不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected:'请选择文件',
                msgValidationError:'文件类型不正确或文件过大',
                initialPreviewFileType:'rar',
                browseLabel:"选择文件",
                removeLabel:'删除',
                removeTitle:'删除文件',
                uploadLabel:'上传',
                uploadTitle:'上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview:false,
                autoReplace:true,
                slugCallback: function(filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function(event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function(event, data, previewId, index) {//文件上传完成执行操作

                // $("#findQuestion_Fileurl").val(data.response.url);
                // $("#findQuestion_Filename").val(data.response.fileRealName);
                // $("#wsc4").text(data.response.fileRealName);
                $("#wsc20").addClass("wscbj");
                $("#wsc20").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled',false);
                butChange(20);
            });

            $("#reButton20").click(function(){
                butChange(20);
                $("#wsc20").addClass("wscbj");
            });

            //开展相关活动的新闻稿网页截图或活动方案
            $("#dwPeopleFiles").fileinput({
                uploadUrl: WEBPATH+'/pdffileupload.do', // you must set a valid URL here else you will get an error
                allowedFileExtensions : ['rar'],
                language:'zh',
                browseClass:'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize:1,
                maxFileSize:53500,
                enctype: 'multipart/form-data',
                dropZoneTitle:"可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany:'选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading:'文件预览',
                msgInvalidFileExtension:'不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected:'请选择文件',
                msgValidationError:'文件类型不正确或文件过大',
                initialPreviewFileType:'rar',
                browseLabel:"选择文件",
                removeLabel:'删除',
                removeTitle:'删除文件',
                uploadLabel:'上传',
                uploadTitle:'上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview:false,
                autoReplace:true,
                slugCallback: function(filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function(event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function(event, data, previewId, index) {//文件上传完成执行操作

                // $("#findQuestion_Fileurl").val(data.response.url);
                // $("#findQuestion_Filename").val(data.response.fileRealName);
                // $("#wsc4").text(data.response.fileRealName);
                $("#wsc21").addClass("wscbj");
                $("#wsc21").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled',false);
                butChange(21);
            });

            $("#reButton21").click(function(){
                butChange(21);
                $("#wsc21").addClass("wscbj");
            });

            //人员培训活动开展情况新闻稿或网页截图
            $("#dwPeopleFile").fileinput({
                uploadUrl: WEBPATH+'/pdffileupload.do', // you must set a valid URL here else you will get an error
                allowedFileExtensions : ['rar'],
                language:'zh',
                browseClass:'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize:1,
                maxFileSize:53500,
                enctype: 'multipart/form-data',
                dropZoneTitle:"可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany:'选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading:'文件预览',
                msgInvalidFileExtension:'不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected:'请选择文件',
                msgValidationError:'文件类型不正确或文件过大',
                initialPreviewFileType:'rar',
                browseLabel:"选择文件",
                removeLabel:'删除',
                removeTitle:'删除文件',
                uploadLabel:'上传',
                uploadTitle:'上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview:false,
                autoReplace:true,
                slugCallback: function(filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function(event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function(event, data, previewId, index) {//文件上传完成执行操作

                // $("#findQuestion_Fileurl").val(data.response.url);
                // $("#findQuestion_Filename").val(data.response.fileRealName);
                // $("#wsc4").text(data.response.fileRealName);
                $("#wsc22").addClass("wscbj");
                $("#wsc22").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled',false);
                butChange(22);
            });

            $("#reButton22").click(function(){
                butChange(22);
                $("#wsc22").addClass("wscbj");
            });

            //人员培训活动开展情况新闻稿或网页截图
            $("#dwTeamFile").fileinput({
                uploadUrl: WEBPATH+'/pdffileupload.do', // you must set a valid URL here else you will get an error
                allowedFileExtensions : ['rar'],
                language:'zh',
                browseClass:'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize:1,
                maxFileSize:53500,
                enctype: 'multipart/form-data',
                dropZoneTitle:"可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany:'选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading:'文件预览',
                msgInvalidFileExtension:'不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected:'请选择文件',
                msgValidationError:'文件类型不正确或文件过大',
                initialPreviewFileType:'rar',
                browseLabel:"选择文件",
                removeLabel:'删除',
                removeTitle:'删除文件',
                uploadLabel:'上传',
                uploadTitle:'上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview:false,
                autoReplace:true,
                slugCallback: function(filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function(event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function(event, data, previewId, index) {//文件上传完成执行操作

                // $("#findQuestion_Fileurl").val(data.response.url);
                // $("#findQuestion_Filename").val(data.response.fileRealName);
                // $("#wsc4").text(data.response.fileRealName);
                $("#wsc23").addClass("wscbj");
                $("#wsc23").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled',false);
                butChange(23);
            });

            $("#reButton23").click(function(){
                butChange(23);
                $("#wsc23").addClass("wscbj");
            });

            //人员培训活动开展情况新闻稿或网页截图
            $("#dwSkillFile").fileinput({
                uploadUrl: WEBPATH+'/pdffileupload.do', // you must set a valid URL here else you will get an error
                allowedFileExtensions : ['rar'],
                language:'zh',
                browseClass:'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize:1,
                maxFileSize:53500,
                enctype: 'multipart/form-data',
                dropZoneTitle:"可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany:'选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading:'文件预览',
                msgInvalidFileExtension:'不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected:'请选择文件',
                msgValidationError:'文件类型不正确或文件过大',
                initialPreviewFileType:'rar',
                browseLabel:"选择文件",
                removeLabel:'删除',
                removeTitle:'删除文件',
                uploadLabel:'上传',
                uploadTitle:'上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview:false,
                autoReplace:true,
                slugCallback: function(filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function(event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function(event, data, previewId, index) {//文件上传完成执行操作

                // $("#findQuestion_Fileurl").val(data.response.url);
                // $("#findQuestion_Filename").val(data.response.fileRealName);
                // $("#wsc4").text(data.response.fileRealName);
                $("#wsc24").addClass("wscbj");
                $("#wsc24").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled',false);
                butChange(24);
            });

            $("#reButton24").click(function(){
                butChange(24);
                $("#wsc24").addClass("wscbj");
            });

            //人员培训活动开展情况新闻稿或网页截图
            $("#licensesFile").fileinput({
                uploadUrl: WEBPATH+'/pdffileupload.do', // you must set a valid URL here else you will get an error
                allowedFileExtensions : ['rar'],
                language:'zh',
                browseClass:'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize:1,
                maxFileSize:53500,
                enctype: 'multipart/form-data',
                dropZoneTitle:"可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany:'选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading:'文件预览',
                msgInvalidFileExtension:'不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected:'请选择文件',
                msgValidationError:'文件类型不正确或文件过大',
                initialPreviewFileType:'rar',
                browseLabel:"选择文件",
                removeLabel:'删除',
                removeTitle:'删除文件',
                uploadLabel:'上传',
                uploadTitle:'上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview:false,
                autoReplace:true,
                slugCallback: function(filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function(event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function(event, data, previewId, index) {//文件上传完成执行操作

                // $("#findQuestion_Fileurl").val(data.response.url);
                // $("#findQuestion_Filename").val(data.response.fileRealName);
                // $("#wsc4").text(data.response.fileRealName);
                $("#wsc25").addClass("wscbj");
                $("#wsc25").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled',false);
                butChange(25);
            });

            $("#reButton25").click(function(){
                butChange(25);
                $("#wsc25").addClass("wscbj");
            });
            //人员培训活动开展情况新闻稿或网页截图
            $("#lawSatisfactionFile").fileinput({
                uploadUrl: WEBPATH+'/pdffileupload.do', // you must set a valid URL here else you will get an error
                allowedFileExtensions : ['rar'],
                language:'zh',
                browseClass:'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize:1,
                maxFileSize:53500,
                enctype: 'multipart/form-data',
                dropZoneTitle:"可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany:'选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading:'文件预览',
                msgInvalidFileExtension:'不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected:'请选择文件',
                msgValidationError:'文件类型不正确或文件过大',
                initialPreviewFileType:'rar',
                browseLabel:"选择文件",
                removeLabel:'删除',
                removeTitle:'删除文件',
                uploadLabel:'上传',
                uploadTitle:'上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview:false,
                autoReplace:true,
                slugCallback: function(filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function(event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function(event, data, previewId, index) {//文件上传完成执行操作

                // $("#findQuestion_Fileurl").val(data.response.url);
                // $("#findQuestion_Filename").val(data.response.fileRealName);
                // $("#wsc4").text(data.response.fileRealName);
                $("#wsc26").addClass("wscbj");
                $("#wsc26").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled',false);
                butChange(26);
            });

            $("#reButton26").click(function(){
                butChange(26);
                $("#wsc26").addClass("wscbj");
            });





        });
    </script>
    <style>
        select,input{
            background: #FFFFFF;
            border: 1px solid #DCDFE6;
            border-radius: 5px;
            width: 380px;
            height: 39px;
            padding-left:5px;
        }
        .sp{text-align: right;}
        .table{
            background-color:#FFF;
            font-family: "微软雅黑";
            font-size: 14px;
        }
        .table_input{width:1000px;max-width:1145px; text-align:right;margin-left: 25px}
        .table_input>tbody>tr>td,.table_input>tbody>tr>th,.table_input>tfoot>tr>td,.table_input>tfoot>tr>th,.table_input>thead>tr>td,.table_input>thead>tr>th{
            padding: 5px;
            line-height: 1.42857143;
            height:60px;
            font-size:14px;
            color: #333333;
            font-width: 400;
        }
        #shicenter{
            background: #fff;
        }
        .has-feedback .form-control{margin-left: -5px;}



        #reButton1,#reButton2,#reButton3,#reButton4,#reButton5,#reButton6,#reButton7,#reButton8,#reButton9,#reButton10,#reButton11,#reButton12,#reButton13,#reButton14,#reButton15,#reButton16
        ,#reButton17,#reButton18,#reButton19,#reButton20,#reButton21,#reButton22,#reButton23,#reButton24,#reButton25,#reButton26,#reButton27,#reButton28,#reButton29{
            height: 32px;
            background: #0093C3;
            border-color: #0093C3;
            margin: 2px;
            border-radius: 3px;
            width: 80px;
            margin-left: -5px;

        }
        /*.btn btn-danger btn-xs{*/
        /*height: 25px;*/
        /*}*/
        .wscbj8{width: 380px;position: relative;margin-top: 14px;padding-top:5px}

        .wscbj{width: 380px;position: relative;height: 30px;margin-top: 14px;}
        .input-group-btn .btn{height:39px;line-height: 26px}
        .file-caption-main{width:37%}
        .file-input .form-control{height:39px;width:378px}
    </style>
</head>
<body>
<c:if test="${areaUser.arealevel == '2'}">
    <div class="center_weizhi">当前位置：信息采集 - 市级评比信息  - 行政区基础数据</div>
</c:if>
<c:if test="${areaUser.arealevel == '3'}">
    <div class="center_weizhi">当前位置：信息采集 - 县级评比信息  - 行政区基础数据</div>
</c:if>
<div class="center">
    <form id="ajslForm">
        <div class="center_list form-group" id="shicenter">
            <table class="table_input">
                <tbody>
                <input type="hidden" class="form-control" name="areacodePro" id="areacodePro" value="${areaUser.areaCode}"/>
                <input type="hidden" class="form-control" name="areatypePro" id="areatypePro" value="${areaUser.arealevel}"/>
                <%--<input type="hidden" class="form-control" name="xxxx" id="xxxx" value="${provinceSelectionUnit.publicityway}"/>--%>
                <%--                <tr>--%>
                <%--<td></td>--%>
                <%--                    <td colspan="2" style="padding-left:500px;text-align: left;color:#FB3301; font-weight: 400;font-size: 14px;">--%>
                <%--                        重要提醒：请完成本页面全部信息填报，否则本单位将无法参加大练兵集体评选。--%>
                <%--<strong>上传相关附件材料命名规则：</strong>行政区名称+材料名称，例如河北石家庄集体事迹材料、河北省大练兵宣传材料。--%>
                <%--                    </td>--%>
                <%--                </tr>--%>
                <tr>
                    <td width="224"><div style="width:100px;margin-left: 400px;">行政区：</div></td>
                    <td style="text-align: left;">
                        <div class="col-sm-12" style="font-size: 24px;font-weight:400;padding: 0;color: #333333">${areaUser.userName}</div>

                    </td>
                </tr>
                <tr>
                    <td width="224"><div><span style="color: red;size: 60px">*</span>机构名称（全称）：</div></td>
                    <td colspan="2" class="form-group">
                        <input style="width: 380px;height:39px"
                               <c:if test="${provinceReportUser.reportstate ==1 || sysInitConfig.code !='1'}">disabled="disabled"</c:if>
                               type="text" class="form-control" id="companyname" name="companyname"
                               value="${provinceSelectionUnit.companyname}"
                               placeholder="请输入机构名称（全称）<c:if test="${areaUser.arealevel == '2'}">例如：河北省衡水市环境监察支队</c:if><c:if test="${areaUser.arealevel == '3'}">例如：河北省石家庄市环境保护局正定县分局</c:if>">

                        <c:if test="${areaUser.arealevel == '3'}">
                        <span style="position: relative;left: -785px;top: 13px;color: red; font-size: 12px;">注意：综合行政执法改革之后取消的市辖区执法机构不能参评</span>
                        </c:if>
                <tr>
                <tr style="text-align: left">
                    <td colspan="2" >
                        <c:if test="${areaUser.arealevel == '2'}">
                            <div style="margin:10px 509px;font-size: 14px;color: #FB3301;font-weight: 400;line-height:22px">重要提醒:</div>
                            <div style="margin:10px 509px;font-size: 14px;color: #FB3301;font-weight: 400;line-height:22px">1.请完成本页面全部信息填报，否则本单位将无法参加大练兵集体评选；</div>
                            <div style="margin:10px 509px;font-size: 14px;color: #FB3301;font-weight: 400;line-height:22px">2.请认真填写下列信息，并仔细上传支撑材料，若未填写相关信息或支撑材料不能证明信息的真实性，将不予给分;</div>
                            <div style="margin:10px 509px;font-size: 14px;color: #FB3301;font-weight: 400;line-height:22px">3.每份支撑材料大小不超过50M，附件格式支持PNG、png、doc、pdf、jpg、JPG、xlsx、xls、csv、mp4、mp3、rar、zip；</div>
                            <div style="margin:10px 509px;font-size: 14px;color: #FB3301;font-weight: 400;line-height:22px"> 4.每份支撑材料需根据提示规范命名：【xx省xx市-具体文件标题】、例：【河北省石家庄市-激励措施】。</div
                        </c:if>
                        <c:if test="${areaUser.arealevel == '3'}">
                            <div style="margin:10px 509px;font-size: 14px;color: #FB3301;font-weight: 400;line-height:22px">重要提醒:</div>
                            <div style="margin:10px 509px;font-size: 14px;color: #FB3301;font-weight: 400;line-height:22px">1.请完成本页面全部信息填报，否则本单位将无法参加大练兵集体评选；</div>
                            <div style="margin:10px 509px;font-size: 14px;color: #FB3301;font-weight: 400;line-height:22px">2.请认真填写下列信息，并仔细上传支撑材料，若未填写相关信息或支撑材料不能证明信息的真实性，将不予给分;</div>
                            <div style="margin:10px 509px;font-size: 14px;color: #FB3301;font-weight: 400;line-height:22px">3.每份支撑材料大小不超过50M，附件格式支持PNG、png、doc、pdf、jpg、JPG、xlsx、xls、csv、mp4、mp3、rar、zip；</div>
                            <div style="margin:10px 509px;font-size: 14px;color: #FB3301;font-weight: 400;line-height:22px">4.每份支撑材料需根据提示规范命名：【xx省xx市xx县-具体文件标题】、例：【河北省石家庄市长安区-激励措施】。</div
                        </c:if>
                    </td>

                </tr>
                <td style="text-align: left;font-size: 16px;font-weight: 400;color: #31688F" >一、日常监督执法工作</td>
                </tr>
                <tr style="text-align: left">
                    <td colspan='2' style="padding: 0;height: 33px;line-height:33px;background: #F8F8F8;">
                        <div> <h1 style="font-weight: 400;color: #31688F;font-size: 14px;margin-top:10px;">（一）典型案例采纳情况<span style="font-weight: 400;color: #FB3301;font-size: 12px;margin-top:10px;">（候选集体查处各类生态环境违法犯罪案件情况，同时被省级部门和我部采纳作为典型案例的，按照被我部采纳统计，无需在此填报）</span></h1></div></td>
                </tr>
                <%--                <tr style="text-align: left">--%>
                <%--                    <td colspan="2"><div style="margin-left: 22px;">(查处生态环境综合执法新增领域案件；有关超标超总量、逃避监管以及侵害公众环境权益、严重污染环境的环境违法犯罪行为等案件）</div></td>--%>
                <%--                </tr>--%>
                <%--                <tr>--%>
                <%--                    <td style="text-align: right" >Excel文件：</td>--%>
                <%--                    <td style="text-align: left; padding-top: 20px" colspan="2">--%>
                <%--                        <span id="wsc1" style="font-size: 16px;">--%>
                <%--                            <c:choose>--%>
                <%--                                <c:when test="${provinceSelectionUnit.dayLawExcelName!='' && provinceSelectionUnit.dayLawExcelName!=null }">--%>
                <%--                                    <div style="float: left; margin-right: 8px; padding-top: 4px;">--%>
                <%--&lt;%&ndash;                                            ${provinceSelectionUnit.dayLawExcelName}&ndash;%&gt;--%>
                <%--                                        <a href="javascript:void(0)" onclick="downloadFile('${provinceSelectionUnit.dayLawExcelUrl}',--%>
                <%--                                                '${provinceSelectionUnit.activityreportname}')"> ${provinceSelectionUnit.dayLawExcelName}--%>
                <%--                                        </a>--%>
                <%--                                    </div>--%>
                <%--                                </c:when>--%>
                <%--                            </c:choose>--%>
                <%--                        </span>--%>
                <%--                        <span id="uploadTr1" <c:if test="${provinceSelectionUnit.dayLawExcelName!='' && provinceSelectionUnit.dayLawExcelName!=null }">style='display:none'</c:if>>--%>
                <%--                            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
                <%--                                <input id="dayLawFile" type="file" name="dayLawFile" title="请按照模板填写并上传" class="file-loading">--%>
                <%--                                <input type="hidden" class="form-control" name="dayLawExcelName" id="dayLaw_Filename" value="${provinceSelectionUnit.dayLawExcelName}">--%>
                <%--                                <input type="hidden" class="form-control" name="dayLawExcelUrl" id="dayLaw_Fileurl" value="${provinceSelectionUnit.dayLawExcelUrl}">--%>
                <%--                            </c:if>--%>
                <%--				        </span>--%>
                <%--                        <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
                <%--                            <input id="reButton1" <c:if test="${provinceSelectionUnit.dayLawExcelName=='' || provinceSelectionUnit.dayLawExcelName==null }">style='display:none'</c:if>--%>
                <%--                                   class="btn btn-danger btn-xs" type="button" value="重新上传" />--%>
                <%--                        </c:if>--%>
                <%--                        <a style="color: blue;position:relative;top:10px " href="#" onclick="down(1)">（模板下载）</a>--%>
                <%--                    </td>--%>
                <%--                </tr>--%>
                <%--                <tr style="text-align: left">--%>
                <%--                    <td colspan="2" style="color: red"><div style="width: 560px;margin-left: 370px;font-size: 12px;color: #FB3301;font-weight: 400;line-height:22px">1.请认真填写下列信息，并仔细上传支撑材料，填写的信息未在支撑材料中体现的，将不作为给分点;</div></td>--%>
                <%--                </tr>--%>
                <%--                <tr style="text-align: left">--%>
                <%--                    <td colspan="2" >--%>
                <%--&lt;%&ndash;                        <div style="width: 465px;margin-left: 357px;font-size: 12px;color: #FB3301;font-weight: 400;line-height:22px">&ndash;%&gt;--%>
                <%--                        <c:if test="${areaUser.arealevel == '3'}"><div style="width:560px;margin-left: 509px;font-size: 12px;color: #FB3301;font-weight: 400;line-height:22px">每份支撑材料大小不超过50M，要求rar格式；存放支撑材料的文件夹(压缩包)以【xx省xx市xx县-二级标题】命名。如典型案例采纳情况（日常监督执法工作）具体的支撑材料应放在<span style="color: #FB3301;font-size:12px;font-weight: 400">【xx省xx市xx县-典型案例采纳情况（日常监督执法工作）】</span>文件夹(压缩包)中。</div></c:if>--%>
                <%--                        <c:if test="${areaUser.arealevel == '2'}"><div style="width:560px;margin-left: 509px;font-size: 12px;color: #FB3301;font-weight: 400;line-height:22px">2.每份支撑材料大小不超过50M，要求rar格式；存放支撑材料的文件夹(压缩包)以【xx省xx市-二级标题】命名。如典型案例采纳情况（日常监督执法工作）具体的支撑材料应放在<span style="font-weight: 400;font-size:12px;color: #FB3301">【xx省xx市-典型案例采纳情况（日常监督执法工作）】</span>文件夹(压缩包)中。</div></c:if></td>--%>
                <%--&lt;%&ndash;                    </div>&ndash;%&gt;--%>
                <%--                </tr>--%>
                <%--                <tr style="text-align: right">--%>
                <%--                    <td><c:if test="${areaUser.arealevel == '2'}"><span style="color: red;size: 60px">*</span></c:if>省级生态环境部门采纳作为典型案例或案例剖析数量（不含被部级采纳）：</td>--%>
                <%--                    <td style="text-align: right;">--%>
                <%--                        <div class="form-group">--%>
                <%--                            <input type="text" class="form-control" id="typicalCaseNum" name="typicalCaseNum" value="${provinceSelectionUnit.typicalCaseNum}" placeholder="请输入市级典型案例公开数量">--%>
                <%--                        </div>--%>
                <%--                    </td>--%>
                <%--                    <td></td>--%>
                <%--                </tr>--%>
                <tr>
                    <td style="text-align: right"><span style="color: red;size: 60px">*</span>省级生态环境部门采纳作为典型案例或案例剖析数量（不含被部级采纳）：</td>
                    <td style="text-align: right;">
                        <div class="form-group">
                            <input type="text" class="form-control" id="provinceAcceptNum" name="provinceAcceptNum" value="${provinceSelectionUnit.provinceAcceptNum}" placeholder="请输入被省级采纳数量">
                        </div>
                    </td>
                    <td></td>
                </tr>
                <tr style="text-align: right">
                    <td rowspan="1"><span style="color: red;size: 60px">*</span>发布典型案例的通知或者公开的链接：</td>
                    <td style="text-align: left;padding-left: 0px" colspan="2">
                        <div>
                        <span id="uploadTr1" <c:if test="${provinceSelectionUnit.typicalCaseAcceptName!='' && provinceSelectionUnit.typicalCaseAcceptName!=null }">style='display:none'</c:if>>
                            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">

                                <input id="typicalCaseAcceptFile" type="file" name="typicalCaseAcceptFile" title="如有相关材料，请上传rar格式文件，大小不超过50M" class="file-loading">
                                <input type="hidden" class="form-control" name="typicalCaseAcceptName" id="typicalCaseAccept_Filename" value="${provinceSelectionUnit.typicalCaseAcceptName}">
                                <input type="hidden" class="form-control" name="typicalCaseAcceptUrl" id="typicalCaseAccept_Fileurl" value="${provinceSelectionUnit.typicalCaseAcceptUrl}">

                            </c:if>
                        </span>
                            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                <input id="reButton1" <c:if test="${provinceSelectionUnit.typicalCaseAcceptName=='' || provinceSelectionUnit.typicalCaseAcceptName==null }">style='display:none'</c:if>
                                       class="btn btn-danger btn-xs" type="button" value="重新上传" />
                            </c:if>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td></td>
                    <td style="text-align: left;padding: 0 5px;position: relative;top: -18px;left:-5px" colspan="2">
                        <div id="wsc1" style="width: 380px;margin-top: 10px;">
                         <span style="font-size: 16px;">
                            <c:choose>
                                <c:when test="${provinceSelectionUnit.typicalCaseAcceptName!='' && provinceSelectionUnit.typicalCaseAcceptName!=null }">
                                    <div style="float: left; margin-right: 8px; padding-top: 4px;">
<%--                                            ${provinceSelectionUnit.typicalCaseAcceptName}--%>
                                        <a href="javascript:void(0)" onclick="downloadFile('${provinceSelectionUnit.typicalCaseAcceptUrl}',
                                                '${provinceSelectionUnit.typicalCaseAcceptName}')"> ${provinceSelectionUnit.typicalCaseAcceptName}
                                        </a>
                                    </div>
                                </c:when>
                            </c:choose>
                         </span>
                        </div>
                    </td>
                </tr>
                <tr style="text-align: left">
                    <td colspan='2' style="padding: 0;height: 33px;line-height:33px;background: #F8F8F8;">
                        <div><h1 style="font-weight: 400;color: #31688F;font-size: 14px;margin-top:10px;">（二）查办大案要案情况<span style="font-weight: 400;color: #FB3301;font-size: 12px;margin-top:10px;">（不含被部采纳作为典型案例发布的案件）</span></h1></div></td>
                </tr>
                <tr style="text-align: right">
                    <td rowspan="1"><span style="color: red;size: 60px">*</span>案件办理情况说明：</td>
                    <td style="text-align: left;padding-left: 0" colspan="2">
                        <div>
                    <span id="uploadTr2"
                          <c:if test="${provinceSelectionUnit.zdcxyzfrwzcclName!='' && provinceSelectionUnit.zdcxyzfrwzcclName!=null }">style='display:none'</c:if>>
                        <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">

                            <input id="zdcxyzfrwzcclFile" type="file" name="zdcxyzfrwzcclFile" title="如有相关材料，请上传rar格式文件，大小不超过50M" class="file-loading">
                            <input type="hidden" class="form-control" name="zdcxyzfrwzcclName" id="zdcxyzfrwzccl_Filename" value="${provinceSelectionUnit.zdcxyzfrwzcclName}">
                            <input type="hidden" class="form-control" name="zdcxyzfrwzcclUrl" id="zdcxyzfrwzccl_Fileurl" value="${provinceSelectionUnit.zdcxyzfrwzcclUrl}">

                        </c:if>
                         <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                    <input id="reButton2" <c:if test="${provinceSelectionUnit.zdcxyzfrwzcclName=='' || provinceSelectionUnit.zdcxyzfrwzcclName==null }">style='display:none'</c:if>
                           class="btn btn-danger btn-xs" type="button" value="重新上传" />
                         </c:if>
                    </span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td></td>
                    <td style="text-align: left;padding: 0 5px;position: relative;top: -18px;left:-5px" colspan="2">
                        <div id="wsc2" style="width: 380px;margin-top: 10px;">
                        <span style="font-size: 16px;">
                        <c:choose>
                            <c:when test="${provinceSelectionUnit.zdcxyzfrwzcclName!='' && provinceSelectionUnit.zdcxyzfrwzcclName!=null }">
                                <div style="float: left; margin-right: 8px; padding-top: 4px;">
<%--                                            ${provinceSelectionUnit.zdcxyzfrwzcclName}--%>
                                     <a href="javascript:void(0)" onclick="downloadFile('${provinceSelectionUnit.zdcxyzfrwzcclUrl}',
                                             '${provinceSelectionUnit.zdcxyzfrwzcclName}')"> ${provinceSelectionUnit.zdcxyzfrwzcclName}
                                     </a>
                                </div>
                            </c:when>
                        </c:choose>
                         </span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td style="text-align: left;position:relative;top:-30px" colspan="2">
                        <div style="margin-left: 509px;color: #FB3301;font-weight: 400;line-height:22px">
                            <%--<span>（需上传<i style="color:#F00;">集体事迹材料与集体照片</i>，存放在一个文件夹中。要求rar格式；文件夹(压缩包)以【省份-二级标题】命名；集体事迹材料与集体照片命名格式为行政区划编码+市县级行政区+机构全称+xxx）</span>--%>
                            <p style="font-size: 12px;">体现各类大案要案查办数量、案情简介。</p></div>
                    </td>
                </tr>
                <tr style="text-align: right;position:relative;left:-5px;">
                    <td><span style="color: red;size: 60px">*</span>查办大案要案情况类型：</td>
                    <td style="text-align: left;">
                        <div class="form-group">
                            <select style="width:380px;margin-right:5px;" name="caseEndType" id="caseEndType">
                                <option value="">--请选择--</option>
                                <option value="1"
                                        <c:if test="${provinceSelectionUnit.isCarryOut eq '1'}">selected</c:if>>是非现场手段查处排污许可违法案件
                                </option>
                                <option value="0"
                                        <c:if test="${provinceSelectionUnit.isCarryOut eq '0'}">selected</c:if>>查处新法规、新领域、新划转职能违法行为案件的
                                </option>
                                <option value="2"
                                        <c:if test="${provinceSelectionUnit.isCarryOut eq '2'}">selected</c:if>>办理跨流域、涉饮用水源地及部审批的建设项目违法案件或综合运用配套办法的案件
                                </option>
                                <option value="3"
                                        <c:if test="${provinceSelectionUnit.isCarryOut eq '3'}">selected</c:if>>会同公安机关等部门查处的案情重大、影响恶劣、后果严重的环境犯罪案件
                                </option>
                            </select>
                            <select style="width:380px;margin-right:5px;" name="isCaseTypeShow" id="isCaseTypeShow">
                                <option value="">--请选择--</option>
                                <option value="1"
                                        <c:if test="${provinceSelectionUnit.isCarryOut eq '1'}">selected</c:if>>土壤污染
                                </option>
                                <option value="0"
                                        <c:if test="${provinceSelectionUnit.isCarryOut eq '0'}">selected</c:if>>温室气体排放
                                </option>
                                <option value="2"
                                        <c:if test="${provinceSelectionUnit.isCarryOut eq '0'}">selected</c:if>>新化学物质
                                </option>
                                <option value="3"
                                        <c:if test="${provinceSelectionUnit.isCarryOut eq '0'}">selected</c:if>>消耗臭氧层物质（ODS（）
                                </option>
                                <option value="4"
                                        <c:if test="${provinceSelectionUnit.isCarryOut eq '0'}">selected</c:if>>新法、新领域、新划转职能违法行为案件
                                </option>
                            </select>
                            <select style="width:380px;margin-right:5px;" name="isCaseTypeShow2" id="isCaseTypeShow2">
                                <option value="">--请选择--</option>
                                <option value="1"
                                        <c:if test="${provinceSelectionUnit.isCarryOut eq '1'}">selected</c:if>>跨区域跨流域违法案件
                                </option>
                                <option value="0"
                                        <c:if test="${provinceSelectionUnit.isCarryOut eq '0'}">selected</c:if>>涉饮用水源地
                                </option>
                                <option value="2"
                                        <c:if test="${provinceSelectionUnit.isCarryOut eq '0'}">selected</c:if>>部级审批的建设项目
                                </option>
                                <option value="3"
                                        <c:if test="${provinceSelectionUnit.isCarryOut eq '0'}">selected</c:if>>综合运用四个配套办法“组合拳“（至少采用2种及以上配套办法）
                                </option>
                            </select>
                        </div>
                    </td>
                    <td></td>
                </tr>
                <tr style="text-align: right">
                    <td rowspan="1"><span style="color: red;size: 60px">*</span>查办大案要案情况支撑材料：</td>
                    <td style="text-align: left;padding-left: 0" colspan="2">
                        <div>
                        <span id="uploadTr3"
                              <c:if test="${provinceSelectionUnit.deedpicturename!='' && provinceSelectionUnit.deedpicturename!=null }">style='display:none'</c:if>>
                            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">

                                <input id="picturefile" type="file" name="picturefile" title="如有相关材料，请上传rar格式文件，大小不超过50M" class="file-loading">
                                <input type="hidden" class="form-control" name="deedpicturename" id="picture_Filename" value="${provinceSelectionUnit.deedpicturename}">
                                <input type="hidden" class="form-control" name="deedpictureurl" id="picture_Fileurl" value="${provinceSelectionUnit.deedpictureurl}">

                            </c:if>
                             <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                        <input id="reButton3" <c:if test="${provinceSelectionUnit.deedpicturename=='' || provinceSelectionUnit.deedpicturename==null }">style='display:none'</c:if>
                               class="btn btn-danger btn-xs" type="button" value="重新上传" />
                             </c:if>
                        </span>

                        </div>
                    </td>
                </tr>
                <tr>
                    <td></td>
                    <td style="text-align: left;padding: 0 5px;position: relative;top: -18px;left:-5px" colspan="2">
                        <div id="wsc3" style="width: 380px;margin-top: 10px;">
                            <span style="font-size: 16px;">
                            <c:choose>
                                <c:when test="${provinceSelectionUnit.deedpicturename!='' && provinceSelectionUnit.deedpicturename!=null }">
                                    <div style="float: left; margin-right: 8px; padding-top: 4px;">
<%--                                            ${provinceSelectionUnit.zdcxyzfrwzcclName}--%>
                                         <a href="javascript:void(0)" onclick="downloadFile('${provinceSelectionUnit.deedpictureurl}',
                                                 '${provinceSelectionUnit.deedpicturename}')"> ${provinceSelectionUnit.deedpicturename}
                                         </a>
                                    </div>
                                </c:when>
                            </c:choose>
                             </span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td style="text-align: left;position:relative;top:-30px" colspan="2">
                        <div style="margin-left: 509px;color: #FB3301;font-weight: 400;line-height:22px">
                            <%--<span>（需上传<i style="color:#F00;">集体事迹材料与集体照片</i>，存放在一个文件夹中。要求rar格式；文件夹(压缩包)以【省份-二级标题】命名；集体事迹材料与集体照片命名格式为行政区划编码+市县级行政区+机构全称+xxx）</span>--%>
                            <p style="font-size: 12px;">案件处罚决定书或移送决定书扫描件。其中，如涉及环境犯罪的，需提供判决书或联合挂牌文书等。</p></div>
                    </td>
                </tr>
                <tr style="text-align: left;">
                    <td colspan="2" style="padding: 0;height: 33px;line-height:33px;background: #F8F8F8;">
                        <div>
                            <h2 style="font-weight: 400;color: #31688F;font-size: 14px;margin-top:10px;">（三）优化执法方式制度落实情况</h2>
                        </div>
                    </td>
                </tr>
                <tr style="text-align: left">
                    <td colspan="2"><div style="margin-left: 22px;">现场检查计划制度落实情况</div></td>
                </tr>
                <tr style="text-align: right;position:relative;left:-5px;">
                    <td style="padding-top:20px"><span style="color: red;size: 60px">*</span>是否制定年度及月度现场检查计划：</td>
                    <td style="padding-top:20px;text-align: left;">
                        <div class="form-group">
                            <select style="width:380px;margin-right:5px;" name="isPlan" id="isPlan">
                                <option value="">--请选择--</option>
                                <option value="1"
                                        <c:if test="${provinceSelectionUnit.isCarryOut eq '1'}">selected</c:if>>是
                                </option>
                                <option value="0"
                                        <c:if test="${provinceSelectionUnit.isCarryOut eq '0'}">selected</c:if>>否
                                </option>
                            </select>
                        </div>
                    </td>
                </tr>
                <tr style="text-align: right">
                    <td rowspan="1"><span style="color: red;size: 60px" class="isYear">*</span>年度检查计划文件扫描件或者公开网络截图：</td>
                    <td style="text-align: left;padding-left: 0" colspan="2">
                    <span id="uploadTr4" <c:if test="${provinceSelectionUnit.findQuestionFileName!='' && provinceSelectionUnit.findQuestionFileName!=null }">style='display:none'</c:if>>
                        <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                            <input id="findQuestionFile" type="file" name="findQuestionFile" title="如有相关材料，请上传rar格式文件，大小不超过50M" class="file-loading">
                            <input type="hidden" class="form-control" name="findQuestionFileName" id="findQuestion_Filename" value="${provinceSelectionUnit.findQuestionFileName}">
                            <input type="hidden" class="form-control" name="findQuestionFileUrl" id="findQuestion_Fileurl" value="${provinceSelectionUnit.findQuestionFileUrl}">
                        </c:if>
                    </span>
                        <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                            <input id="reButton4" <c:if test="${provinceSelectionUnit.findQuestionFileName=='' || provinceSelectionUnit.findQuestionFileName==null }">style='display:none'</c:if>
                                   class="btn btn-danger btn-xs" type="button" value="重新上传" />
                        </c:if>
                    </td>
                </tr>
                <tr>
                    <td></td>
                    <td style="text-align: left;padding: 0 5px;position: relative;top: -18px;left:-5px" colspan="2">
                        <div id="wsc4" style="width: 380px;margin-top: 10px;">
                         <span style="font-size: 16px;">
                            <c:choose>
                                <c:when test="${provinceSelectionUnit.findQuestionFileName!='' && provinceSelectionUnit.findQuestionFileName!=null }">
                                    <div style="float: left;margin-right: 8px; padding-top: 4px;">
    <%--                                            ${provinceSelectionUnit.findQuestionFileName}--%>
                                        <a href="javascript:void(0)" onclick="downloadFile('${provinceSelectionUnit.findQuestionFileUrl}',
                                                '${provinceSelectionUnit.findQuestionFileName}')"> ${provinceSelectionUnit.findQuestionFileName}
                                        </a>
                                    </div>
                                </c:when>
                            </c:choose>
                        </span>
                        </div>
                    </td>
                </tr>
                <tr style="text-align: right">
                    <td rowspan="1"><span style="color: red;size: 60px" class="isYear">*</span>月度检查计划文件扫描件或者公开网络截图：</td>
                    <td style="text-align: left;padding-left: 0" colspan="2">
                    <span id="uploadTr5" <c:if test="${provinceSelectionUnit.findQuestionFileName!='' && provinceSelectionUnit.findQuestionFileName!=null }">style='display:none'</c:if>>
                        <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                            <input id="lawgzmydFile" type="file" name="findQuestionFile" title="如有相关材料，请上传rar格式文件，大小不超过50M" class="file-loading">
                            <input type="hidden" class="form-control" name="findQuestionFileName" id="lawgzmyd_Filename" value="${provinceSelectionUnit.findQuestionFileName}">
                            <input type="hidden" class="form-control" name="findQuestionFileUrl" id="lawgzmyd_Fileurl" value="${provinceSelectionUnit.findQuestionFileUrl}">
                        </c:if>
                    </span>
                        <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                            <input id="reButton5" <c:if test="${provinceSelectionUnit.findQuestionFileName=='' || provinceSelectionUnit.findQuestionFileName==null }">style='display:none'</c:if>
                                   class="btn btn-danger btn-xs" type="button" value="重新上传" />
                        </c:if>
                    </td>
                </tr>
                <tr>
                    <td></td>
                    <td style="text-align: left;padding: 0 5px;position: relative;top: -18px;left:-5px" colspan="2">
                        <div id="wsc5" style="width: 380px;margin-top: 10px;">
                         <span style="font-size: 16px;">
                            <c:choose>
                                <c:when test="${provinceSelectionUnit.findQuestionFileName!='' && provinceSelectionUnit.findQuestionFileName!=null }">
                                    <div style="float: left;margin-right: 8px; padding-top: 4px;">
        <%--                                            ${provinceSelectionUnit.findQuestionFileName}--%>
                                        <a href="javascript:void(0)" onclick="downloadFile('${provinceSelectionUnit.findQuestionFileUrl}',
                                                '${provinceSelectionUnit.findQuestionFileName}')"> ${provinceSelectionUnit.findQuestionFileName}
                                        </a>
                                    </div>
                                </c:when>
                            </c:choose>
                        </span>
                        </div>
                    </td>
                </tr>
                <tr style="text-align: right">
                    <td style="padding-top:20px"><span style="color: red;size: 60px">*</span>是否落实现场检查计划备案相关要求：</td>
                    <td style="position:relative;left:-5px;padding-top:20px;text-align: left;">
                        <div class="form-group">
                            <select style="width:380px;margin-right:5px;" name="isInspetion" id="isInspetion">
                                <option value="">--请选择--</option>
                                <option value="1"
                                        <c:if test="${provinceSelectionUnit.isCarryOut eq '1'}">selected</c:if>>是
                                </option>
                                <option value="0"
                                        <c:if test="${provinceSelectionUnit.isCarryOut eq '0'}">selected</c:if>>否
                                </option>
                            </select>
                        </div>
                    </td>
                </tr>
                <tr style="text-align: right">
                    <td rowspan="1"><span style="color: red;size: 60px" class="isInspetionShow">*</span>备案相关文件佐证材料：</td>
                    <td style="text-align: left;padding-left: 0" colspan="2">
                    <span id="uploadTr6" <c:if test="${provinceSelectionUnit.troopsConAdminFileName!='' && provinceSelectionUnit.troopsConAdminFileName!=null }">style='display:none'</c:if>>
                        <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                            <input id="citySpecialActionFile" type="file" name="citySpecialActionFile" title="如有相关材料，请上传rar格式文件，大小不超过50M" class="file-loading">
                            <input type="hidden" class="form-control" name="troopsConAdminFileName" id="citySpecialAction_Filename" value="${provinceSelectionUnit.troopsConAdminFileName}">
                            <input type="hidden" class="form-control" name="troopsConAdminFileUrl" id="citySpecialAction_Fileurl" value="${provinceSelectionUnit.troopsConAdminFileUrl}">
                        </c:if>
                    </span>
                        <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                            <input id="reButton6" <c:if test="${provinceSelectionUnit.troopsConAdminFileName=='' || provinceSelectionUnit.troopsConAdminFileName==null }">style='display:none'</c:if>
                                   class="btn btn-danger btn-xs" type="button" value="重新上传" />
                        </c:if>
                    </td>
                </tr>
                <tr>
                    <td></td>
                    <td style="text-align: left;padding: 0 5px;position: relative;top: -18px;left:-5px" colspan="2">
                        <div id="wsc6" style="width: 380px;margin-top: 10px;">
                         <span style="font-size: 16px;">
                            <c:choose>
                                <c:when test="${provinceSelectionUnit.troopsConAdminFileName!='' && provinceSelectionUnit.troopsConAdminFileName!=null }">
                                    <div style="float: left;margin-right: 8px; padding-top: 4px;">
        <%--                                            ${provinceSelectionUnit.findQuestionFileName}--%>
                                        <a href="javascript:void(0)" onclick="downloadFile('${provinceSelectionUnit.troopsConAdminFileUrl}',
                                                '${provinceSelectionUnit.troopsConAdminFileName}')"> ${provinceSelectionUnit.troopsConAdminFileName}
                                        </a>
                                    </div>
                                </c:when>
                            </c:choose>
                        </span>
                        </div>
                    </td>
                </tr>
                <tr style="text-align: left">
                    <td colspan="2"><div style="margin-left: 22px;">正面清单制度落实情况</div></td>
                </tr>
                <tr style="text-align: right">
                    <td style="padding-top:20px"><span style="color: red;size: 60px">*</span>是否公布正面清单企业名单，实施正面清单动态管理：</td>
                    <td style="padding-top:20px;text-align: left;position:relative;left: -5px">
                        <div class="form-group">
                            <select style="width:380px;margin-right:5px;" name="isPositivelistManage" id="isPositivelistManage">
                                <option value="">--请选择--</option>
                                <option value="1"
                                        <c:if test="${provinceSelectionUnit.isCarryOut eq '1'}">selected</c:if>>是
                                </option>
                                <option value="0"
                                        <c:if test="${provinceSelectionUnit.isCarryOut eq '0'}">selected</c:if>>否
                                </option>
                            </select>
                        </div>
                    </td>
                </tr>
                <tr style="text-align: right">
                    <td rowspan="1"><span style="color: red;size: 60px" class="isManage">*</span>公布清单的文件扫描件或网上截图：</td>
                    <td style="text-align: left;padding-left: 0" colspan="2">
                    <span id="uploadTr7" <c:if test="${provinceSelectionUnit.findQuestionFileName!='' && provinceSelectionUnit.findQuestionFileName!=null }">style='display:none'</c:if>>
                        <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                            <input id="citySpecialActionShowFile" type="file" name="citySpecialActionShowFile" title="如有相关材料，请上传rar格式文件，大小不超过50M" class="file-loading">
                            <input type="hidden" class="form-control" name="findQuestionFileName" id="findQuestion_Filename" value="${provinceSelectionUnit.findQuestionFileName}">
                            <input type="hidden" class="form-control" name="findQuestionFileUrl" id="findQuestion_Fileurl" value="${provinceSelectionUnit.findQuestionFileUrl}">
                        </c:if>
                    </span>
                        <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                            <input id="reButton7" <c:if test="${provinceSelectionUnit.findQuestionFileName=='' || provinceSelectionUnit.findQuestionFileName==null }">style='display:none'</c:if>
                                   class="btn btn-danger btn-xs" type="button" value="重新上传" />
                        </c:if>
                    </td>
                </tr>
                <tr>
                    <td></td>
                    <td style="text-align: left;padding: 0 5px;position: relative;top: -18px;left:-5px" colspan="2">
                        <div id="wsc7" style="width: 380px;margin-top: 10px;">
                         <span style="font-size: 16px;">
                            <c:choose>
                                <c:when test="${provinceSelectionUnit.findQuestionFileName!='' && provinceSelectionUnit.findQuestionFileName!=null }">
                                    <div style="float: left;margin-right: 8px; padding-top: 4px;">
        <%--                                            ${provinceSelectionUnit.findQuestionFileName}--%>
                                        <a href="javascript:void(0)" onclick="downloadFile('${provinceSelectionUnit.findQuestionFileUrl}',
                                                '${provinceSelectionUnit.findQuestionFileName}')"> ${provinceSelectionUnit.findQuestionFileName}
                                        </a>
                                    </div>
                                </c:when>
                            </c:choose>
                        </span>
                        </div>
                    </td>
                </tr>
                <tr style="text-align: right">
                    <td style="padding-top:20px"><span style="color: red;size: 60px">*</span>是否通过座谈、宣讲、培训对正面清单企业开展帮扶指导、提醒预警：</td>
                    <td style="padding-top:20px;text-align: left;position:relative;left: -5px">
                        <div class="form-group">
                            <select style="width:380px;margin-right:5px;" name="isPositivelistWarn" id="isPositivelistWarn">
                                <option value="">--请选择--</option>
                                <option value="1"
                                        <c:if test="${provinceSelectionUnit.isCarryOut eq '1'}">selected</c:if>>是
                                </option>
                                <option value="0"
                                        <c:if test="${provinceSelectionUnit.isCarryOut eq '0'}">selected</c:if>>否
                                </option>
                            </select>
                        </div>
                    </td>
                </tr>
                <tr style="text-align: right">
                    <td rowspan="1"><span style="color: red;size: 60px" class="isWarn">*</span>文件或新闻稿网页截图或照片等：</td>
                    <td style="text-align: left;padding-left: 0" colspan="2">
                    <span id="uploadTr8" <c:if test="${provinceSelectionUnit.findQuestionFileName!='' && provinceSelectionUnit.findQuestionFileName!=null }">style='display:none'</c:if>>
                        <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                            <input id="ccnxzjwffzCaseFile" type="file" name="ccnxzjwffzCaseFile" title="如有相关材料，请上传rar格式文件，大小不超过50M" class="file-loading">
                            <input type="hidden" class="form-control" name="findQuestionFileName" id="findQuestion_Filename" value="${provinceSelectionUnit.findQuestionFileName}">
                            <input type="hidden" class="form-control" name="findQuestionFileUrl" id="findQuestion_Fileurl" value="${provinceSelectionUnit.findQuestionFileUrl}">
                        </c:if>
                    </span>
                        <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                            <input id="reButton8" <c:if test="${provinceSelectionUnit.findQuestionFileName=='' || provinceSelectionUnit.findQuestionFileName==null }">style='display:none'</c:if>
                                   class="btn btn-danger btn-xs" type="button" value="重新上传" />
                        </c:if>
                    </td>
                </tr>
                <tr>
                    <td></td>
                    <td style="text-align: left;padding: 0 5px;position: relative;top: -18px;left:-5px" colspan="2">
                        <div id="wsc8" style="width: 380px;margin-top: 10px;">
                         <span style="font-size: 16px;">
                            <c:choose>
                                <c:when test="${provinceSelectionUnit.findQuestionFileName!='' && provinceSelectionUnit.findQuestionFileName!=null }">
                                    <div style="float: left;margin-right: 8px; padding-top: 4px;">
        <%--                                            ${provinceSelectionUnit.findQuestionFileName}--%>
                                        <a href="javascript:void(0)" onclick="downloadFile('${provinceSelectionUnit.findQuestionFileUrl}',
                                                '${provinceSelectionUnit.findQuestionFileName}')"> ${provinceSelectionUnit.findQuestionFileName}
                                        </a>
                                    </div>
                                </c:when>
                            </c:choose>
                        </span>
                        </div>
                    </td>
                </tr>
                <tr style="text-align: left">
                    <td colspan="2"><div style="margin-left: 22px;">“双随机、一公开”制度落实情况</div></td>
                </tr>
                <tr style="text-align: right">
                    <td style="padding-top:20px"><span style="color: red;size: 60px">*</span>是否形成5件处罚案件：</td>
                    <td style="padding-top:20px;text-align: left;position:relative;left: -5px">
                        <div class="form-group">
                            <select style="width:380px;margin-right:5px;" name="isRandomFive" id="isRandomFive">
                                <option value="">--请选择--</option>
                                <option value="1"
                                        <c:if test="${provinceSelectionUnit.isCarryOut eq '1'}">selected</c:if>>是
                                </option>
                                <option value="0"
                                        <c:if test="${provinceSelectionUnit.isCarryOut eq '0'}">selected</c:if>>否
                                </option>
                            </select>
                        </div>
                    </td>
                </tr>
                <tr style="text-align: right">
                    <td rowspan="1"><span style="color: red;size: 60px" class="isRandomShow">*</span>“双随机、一公开”制度5个案件的案情简介说明材料：</td>
                    <td style="text-align: left;padding-left: 0" colspan="2">
                        <span id="uploadTr9" <c:if test="${provinceSelectionUnit.findQuestionFileName!='' && provinceSelectionUnit.findQuestionFileName!=null }">style='display:none'</c:if>>
                              <c:if test="${sysInitConfig.code =='1' && provinceReportUser.electcityState !=1 && provinceReportUser.isDevInternalEva !=1}">
                                  <input id="randomInfoUrlnamefile" type="file" name="randomInfoUrlnamefile" title="提示：附件要求PDF或rar格式，大小不超过50M。" class="file-loading">
                                  <input type="hidden" class="form-control" name="accountabilityname" id="accountability_Filename" value="${provinceSelectionUnit.accountabilityname}">
                                  <input type="hidden" class="form-control" name="accountabilityurl" id="accountability_Fileurl" value="${provinceSelectionUnit.accountabilityurl}">
                              </c:if>
                        </span>
                        <c:if test="${sysInitConfig.code =='1' && provinceReportUser.electcityState !=1 && provinceReportUser.isDevInternalEva !=1}">
                            <input id="reButton9" <c:if test="${provinceSelectionUnit.accountabilityname=='' || provinceSelectionUnit.accountabilityname==null }">style='display:none'</c:if>
                                   class="btn btn-danger btn-xs" type="button" value="重新上传" />
                        </c:if>
                    </td>
                </tr>
                <tr>
                    <td></td>
                    <td style="text-align: left;padding: 0 5px;position: relative;top: -18px;left:-5px" colspan="2">
                        <div id="wsc9" style="width: 380px;margin-top: 10px;">
                             <span style="font-size: 16px;">
                                 <c:choose>
                                     <c:when
                                             test="${provinceSelectionUnit.accountabilityname!='' && provinceSelectionUnit.accountabilityname!=null }">
                                        <div style="float: left; margin-right: 8px; padding-top: 4px;">
                                                ${provinceSelectionUnit.accountabilityname}</div>
                                     </c:when>
                                 </c:choose>
                             </span>
                        </div>
                    </td>
                </tr>
                <tr style="text-align: right">
                    <td rowspan="1"><span style="color: red;size: 60px" class="isRandomShow">*</span>双随机、一公开”制度5个处罚决定书的扫描件：</td>
                    <td style="text-align: left;padding-left: 0" colspan="2">
                        <span id="uploadTr10"
                              <c:if test="${provinceSelectionUnit.accountabilityname!='' && provinceSelectionUnit.accountabilityname!=null }">style='display:none'</c:if>>
                              <c:if test="${sysInitConfig.code =='1' && provinceReportUser.electcityState !=1 && provinceReportUser.isDevInternalEva !=1}">
                                  <input id="randomDecisionfile" type="file" name="randomDecisionfile" title="提示：附件要求PDF或rar格式，大小不超过50M。" class="file-loading">
                                  <input type="hidden" class="form-control" name="accountabilityname" id="accountability_Filename" value="${provinceSelectionUnit.accountabilityname}">
                                  <input type="hidden" class="form-control" name="accountabilityurl" id="accountability_Fileurl" value="${provinceSelectionUnit.accountabilityurl}">
                              </c:if>
                        </span>
                        <c:if test="${sysInitConfig.code =='1' && provinceReportUser.electcityState !=1 && provinceReportUser.isDevInternalEva !=1}">
                            <input id="reButton10" <c:if test="${provinceSelectionUnit.accountabilityname=='' || provinceSelectionUnit.accountabilityname==null }">style='display:none'</c:if>
                                   class="btn btn-danger btn-xs" type="button" value="重新上传" />
                        </c:if>
                    </td>
                </tr>
                <tr>
                    <td></td>
                    <td style="text-align: left;padding: 0 5px;position: relative;top: -18px;left:-5px" colspan="2">
                        <div id="wsc10" style="width: 380px;margin-top: 10px;">
                             <span style="font-size: 16px;">
                                 <c:choose>
                                     <c:when
                                             test="${provinceSelectionUnit.accountabilityname!='' && provinceSelectionUnit.accountabilityname!=null }">
                                        <div style="float: left; margin-right: 8px; padding-top: 4px;">
                                                ${provinceSelectionUnit.accountabilityname}</div>
                                     </c:when>
                                 </c:choose>
                             </span>
                        </div>
                    </td>
                </tr>
                <tr style="text-align: left">
                    <td colspan="2"><div style="margin-left: 22px;">举报奖励制度落实情况</div></td>
                </tr>
                <tr style="text-align: right">
                    <td style="padding-top:20px">
                        <c:if test="${areaUser.arealevel == '2'}"><span style="color: red;size: 60px">*</span>市级办理举报奖励案件数量：</c:if>
                        <c:if test="${areaUser.arealevel == '3'}"><span style="color: red;size: 60px">*</span>县级办理举报奖励案件数量：</c:if>
                    </td>
                    <td style="padding-top:20px;text-align: right;position:relative;left: -5px">
                        <div class="form-group">
                            <input type="text" class="form-control" id="cityInfoNum" name="cityInfoNum" value="${provinceSelectionUnit.findQuestionNum}" placeholder="请输入数量">
                        </div>
                    </td>
                </tr>
                <%--                <tr style="text-align: right">--%>
                <%--                    <td style="padding-top:20px"><span style="color: red;size: 60px">*</span>内部人举报奖励案件、重奖案件数量：</td>--%>
                <%--                    <td style="padding-top:20px;text-align: right;">--%>
                <%--                        <div class="form-group">--%>
                <%--                            <input type="text" class="form-control" id="countyInfoNum" name="countyInfoNum" value="${provinceSelectionUnit.findQuestionNum}" placeholder="请输入数量">--%>
                <%--                        </div>--%>
                <%--                    </td>--%>
                <%--                </tr>--%>
                <tr style="text-align: right">
                    <td style="padding-top:20px"><span style="color: red;size: 60px">*</span>内部人举报奖励案件、重奖案件数量：</td>
                    <td style="padding-top:20px;text-align: right;position:relative;left: -5px">
                        <div class="form-group">
                            <input type="text" class="form-control" id="insiderInfoNum" name="insiderInfoNum" value="${provinceSelectionUnit.findQuestionNum}" placeholder="请输入数量">
                        </div>
                    </td>
                </tr>
                <tr style="text-align: right">
                    <td rowspan="1"><span style="color: red;size: 60px">*</span>举报奖励制度案情简介说明材料：</td>
                    <td style="text-align: left;padding-left: 0" colspan="2">
                        <span id="uploadTr11" <c:if test="${provinceSelectionUnit.findQuestionFileName!='' && provinceSelectionUnit.findQuestionFileName!=null }">style='display:none'</c:if>>
                            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                <input id="caseInfoFile" type="file" name="caseInfoFile" title="如有相关材料，请上传rar格式文件，大小不超过50M" class="file-loading">
                                <input type="hidden" class="form-control" name="findQuestionFileName" id="findQuestion_Filename" value="${provinceSelectionUnit.findQuestionFileName}">
                                <input type="hidden" class="form-control" name="findQuestionFileUrl" id="findQuestion_Fileurl" value="${provinceSelectionUnit.findQuestionFileUrl}">

                            </c:if>
                        </span>
                        <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                            <input id="reButton11" <c:if test="${provinceSelectionUnit.findQuestionFileName=='' || provinceSelectionUnit.findQuestionFileName==null }">style='display:none'</c:if>
                                   class="btn btn-danger btn-xs" type="button" value="重新上传" />
                        </c:if>
                    </td>
                </tr>
                <tr>
                    <td></td>
                    <td style="text-align: left;padding: 0 5px;position: relative;top: -18px;left:-5px" colspan="2">
                        <div id="wsc11" style="width: 380px;margin-top: 10px;">
                         <span style="font-size: 16px;">
                            <c:choose>
                                <c:when test="${provinceSelectionUnit.findQuestionFileName!='' && provinceSelectionUnit.findQuestionFileName!=null }">
                                    <div style="float: left;margin-right: 8px; padding-top: 4px;">
<%--                                            ${provinceSelectionUnit.findQuestionFileName}--%>
                                        <a href="javascript:void(0)" onclick="downloadFile('${provinceSelectionUnit.findQuestionFileUrl}',
                                                '${provinceSelectionUnit.findQuestionFileName}')"> ${provinceSelectionUnit.findQuestionFileName}
                                        </a>
                                    </div>
                                </c:when>
                            </c:choose>
                        </span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td style="text-align: left;position:relative;top:-30px" colspan="2">
                        <div style="width:560px;margin-left: 508px;color: #FB3301;font-weight: 400;line-height:22px"><p style="font-size: 12px;">若有内部人举报或重奖情况的，也予以说明。</p></div>
                    </td>
                </tr>
                <tr style="text-align: right">
                    <td rowspan="1"><span style="color: red;size: 60px">*</span>举报奖励制度处罚决定书的扫描件：</td>
                    <td style="text-align: left;padding-left: 0" colspan="2">
                        <span id="uploadTr12" <c:if test="${provinceSelectionUnit.findQuestionFileName!='' && provinceSelectionUnit.findQuestionFileName!=null }">style='display:none'</c:if>>
                            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                <input id="desicionFile" type="file" name="desicionFile" title="如有相关材料，请上传rar格式文件，大小不超过50M" class="file-loading">
                                <input type="hidden" class="form-control" name="findQuestionFileName" id="findQuestion_Filename" value="${provinceSelectionUnit.findQuestionFileName}">
                                <input type="hidden" class="form-control" name="findQuestionFileUrl" id="findQuestion_Fileurl" value="${provinceSelectionUnit.findQuestionFileUrl}">

                            </c:if>
                        </span>
                        <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                            <input id="reButton12" <c:if test="${provinceSelectionUnit.findQuestionFileName=='' || provinceSelectionUnit.findQuestionFileName==null }">style='display:none'</c:if>
                                   class="btn btn-danger btn-xs" type="button" value="重新上传" />
                        </c:if>
                    </td>
                </tr>
                <tr>
                    <td></td>
                    <td style="text-align: left;padding: 0 5px;position: relative;top: -18px;left:-5px" colspan="2">
                        <div id="wsc12" style="width: 380px;margin-top: 10px;">
                         <span style="font-size: 16px;">
                            <c:choose>
                                <c:when test="${provinceSelectionUnit.findQuestionFileName!='' && provinceSelectionUnit.findQuestionFileName!=null }">
                                    <div style="float: left;margin-right: 8px; padding-top: 4px;">
<%--                                            ${provinceSelectionUnit.findQuestionFileName}--%>
                                        <a href="javascript:void(0)" onclick="downloadFile('${provinceSelectionUnit.findQuestionFileUrl}',
                                                '${provinceSelectionUnit.findQuestionFileName}')"> ${provinceSelectionUnit.findQuestionFileName}
                                        </a>
                                    </div>
                                </c:when>
                            </c:choose>
                        </span>
                        </div>
                    </td>
                </tr>

                <tr style="text-align: left">
                    <td colspan="2"><div style="margin-left: 22px;">推行非现场监管方式情况</div></td>
                </tr>
                <tr style="text-align: right">
                    <td style="padding-top:20px"><span style="color: red;size: 60px">*</span>是否形成5件处罚案件：</td>
                    <td style="padding-top:20px;text-align: left;position: relative;left: -5px">
                        <div class="form-group">
                            <select style="width:380px;margin-right:5px;" name="isOffsiteFive" id="isOffsiteFive">
                                <option value="">--请选择--</option>
                                <option value="1"
                                        <c:if test="${provinceSelectionUnit.isCarryOut eq '1'}">selected</c:if>>是
                                </option>
                                <option value="0"
                                        <c:if test="${provinceSelectionUnit.isCarryOut eq '0'}">selected</c:if>>否
                                </option>
                            </select>
                        </div>
                    </td>
                </tr>
                <tr style="text-align: right">
                    <td rowspan="1"><span style="color: red;size: 60px" class="isOffsiteShow">*</span>推行非现场监管方式5个案件的案情简介说明材料：</td>
                    <td style="text-align: left;padding-left: 0" colspan="2">
                        <span id="uploadTr13" <c:if test="${provinceSelectionUnit.findQuestionFileName!='' && provinceSelectionUnit.findQuestionFileName!=null }">style='display:none'</c:if>>
                            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                <input id="offsiteInfoFile" type="file" name="offsiteInfoFile" title="如有相关材料，请上传rar格式文件，大小不超过50M" class="file-loading">
                                <input type="hidden" class="form-control" name="offsiteInfoUrlname" id="offsiteInfoUrlname" value="${provinceSelectionUnit.findQuestionFileName}">
                                <input type="hidden" class="form-control" name="offsiteInfoUrl" id="offsiteInfoUrl" value="${provinceSelectionUnit.findQuestionFileUrl}">
                            </c:if>
                        </span>
                        <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                            <input id="reButton13" <c:if test="${provinceSelectionUnit.findQuestionFileName=='' || provinceSelectionUnit.findQuestionFileName==null }">style='display:none'</c:if>
                                   class="btn btn-danger btn-xs" type="button" value="重新上传" />
                        </c:if>
                    </td>
                </tr>
                <tr>
                    <td></td>
                    <td style="text-align: left;padding: 0 5px;position: relative;top: -18px;left:-5px" colspan="2">
                        <div id="wsc13" style="width: 380px;margin-top: 10px;">
                         <span style="font-size: 16px;">
                            <c:choose>
                                <c:when test="${provinceSelectionUnit.findQuestionFileName!='' && provinceSelectionUnit.findQuestionFileName!=null }">
                                    <div style="float: left;margin-right: 8px; padding-top: 4px;">
<%--                                            ${provinceSelectionUnit.findQuestionFileName}--%>
                                        <a href="javascript:void(0)" onclick="downloadFile('${provinceSelectionUnit.findQuestionFileUrl}',
                                                '${provinceSelectionUnit.findQuestionFileName}')"> ${provinceSelectionUnit.findQuestionFileName}
                                        </a>
                                    </div>
                                </c:when>
                            </c:choose>
                        </span>
                        </div>
                    </td>
                </tr>
                <tr style="text-align: right">
                    <td rowspan="1"><span style="color: red;size: 60px" class="isOffsiteShow">*</span>推行非现场监管方式5个处罚决定书的扫描件：</td>
                    <td style="text-align: left;padding-left: 0" colspan="2">
                        <span id="uploadTr14" <c:if test="${provinceSelectionUnit.findQuestionFileName!='' && provinceSelectionUnit.findQuestionFileName!=null }">style='display:none'</c:if>>
                            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                <input id="offsiteDecisionFile" type="file" name="offsiteDecisionFile" title="如有相关材料，请上传rar格式文件，大小不超过50M" class="file-loading">
                                <input type="hidden" class="form-control" name="offsiteDecisionUrlname" id="offsiteDecisionUrlname" value="${provinceSelectionUnit.findQuestionFileName}">
                                <input type="hidden" class="form-control" name="offsiteDecisionUrl" id="offsiteDecisionUrl" value="${provinceSelectionUnit.findQuestionFileUrl}">
                            </c:if>
                        </span>
                        <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                            <input id="reButton14" <c:if test="${provinceSelectionUnit.findQuestionFileName=='' || provinceSelectionUnit.findQuestionFileName==null }">style='display:none'</c:if>
                                   class="btn btn-danger btn-xs" type="button" value="重新上传" />
                        </c:if>
                    </td>
                </tr>
                <tr>
                    <td></td>
                    <td style="text-align: left;padding: 0 5px;position: relative;top: -18px;left:-5px" colspan="2">
                        <div id="wsc14" style="width: 380px;margin-top: 10px;">
                         <span style="font-size: 16px;">
                            <c:choose>
                                <c:when test="${provinceSelectionUnit.findQuestionFileName!='' && provinceSelectionUnit.findQuestionFileName!=null }">
                                    <div style="float: left;margin-right: 8px; padding-top: 4px;">
<%--                                            ${provinceSelectionUnit.findQuestionFileName}--%>
                                        <a href="javascript:void(0)" onclick="downloadFile('${provinceSelectionUnit.findQuestionFileUrl}',
                                                '${provinceSelectionUnit.findQuestionFileName}')"> ${provinceSelectionUnit.findQuestionFileName}
                                        </a>
                                    </div>
                                </c:when>
                            </c:choose>
                        </span>
                        </div>
                    </td>
                </tr>
                <tr style="text-align: left">
                    <td colspan="2"><div style="margin-left: 22px;">执法监测工作机制落实情况</div></td>
                </tr>
                <tr style="text-align: right">
                    <td style="padding-top:20px"><span style="color: red;size: 60px">*</span>是否依据环境执法需求制定执法监测计划， 并将执法监测经费纳入执法工作预算：</td>
                    <td style="padding-top:20px;text-align: left;">
                        <div class="form-group">
                            <select style="width:380px;margin-right:5px;" name="isLawplan" id="isLawplan">
                                <option value="">--请选择--</option>
                                <option value="1"
                                        <c:if test="${provinceSelectionUnit.isCarryOut eq '1'}">selected</c:if>>是
                                </option>
                                <option value="0"
                                        <c:if test="${provinceSelectionUnit.isCarryOut eq '0'}">selected</c:if>>否
                                </option>
                            </select>
                        </div>
                    </td>
                </tr>
                <tr style="text-align: right">
                    <td rowspan="1"><span style="color: red;size: 60px" class="isLawShow">*</span>执法监测计划的文件：</td>
                    <td style="text-align: left;padding-left: 0" colspan="2">
                        <span id="uploadTr15" <c:if test="${provinceSelectionUnit.findQuestionFileName!='' && provinceSelectionUnit.findQuestionFileName!=null }">style='display:none'</c:if>>
                            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                <input id="lawplanFile" type="file" name="lawplanFile" title="如有相关材料，请上传rar格式文件，大小不超过50M" class="file-loading">
                                <input type="hidden" class="form-control" name="lawplanUrlname" id="lawplanUrlname" value="${provinceSelectionUnit.findQuestionFileName}">
                                <input type="hidden" class="form-control" name="lawplanUrl" id="lawplanUrl" value="${provinceSelectionUnit.findQuestionFileUrl}">
                            </c:if>
                        </span>
                        <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                            <input id="reButton15" <c:if test="${provinceSelectionUnit.findQuestionFileName=='' || provinceSelectionUnit.findQuestionFileName==null }">style='display:none'</c:if>
                                   class="btn btn-danger btn-xs" type="button" value="重新上传" />
                        </c:if>
                    </td>
                </tr>
                <tr>
                    <td></td>
                    <td style="text-align: left;padding: 0 5px;position: relative;top: -18px;left:-5px" colspan="2">
                        <div id="wsc15" style="width: 380px;margin-top: 10px;">
                         <span style="font-size: 16px;">
                            <c:choose>
                                <c:when test="${provinceSelectionUnit.findQuestionFileName!='' && provinceSelectionUnit.findQuestionFileName!=null }">
                                    <div style="float: left;margin-right: 8px; padding-top: 4px;">
<%--                                            ${provinceSelectionUnit.findQuestionFileName}--%>
                                        <a href="javascript:void(0)" onclick="downloadFile('${provinceSelectionUnit.findQuestionFileUrl}',
                                                '${provinceSelectionUnit.findQuestionFileName}')"> ${provinceSelectionUnit.findQuestionFileName}
                                        </a>
                                    </div>
                                </c:when>
                            </c:choose>
                        </span>
                        </div>
                    </td>
                </tr>
                <tr style="text-align: right">
                    <td rowspan="1"><span style="color: red;size: 60px" class="isLawShow">*</span>经费纳入执法工作预算的佐证材料，文件或者截图：</td>
                    <td style="text-align: left;padding-left: 0" colspan="2">
                        <span id="uploadTr16" <c:if test="${provinceSelectionUnit.findQuestionFileName!='' && provinceSelectionUnit.findQuestionFileName!=null }">style='display:none'</c:if>>
                            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                <input id="lawplansupFile" type="file" name="lawplansupFile" title="如有相关材料，请上传rar格式文件，大小不超过50M" class="file-loading">
                                <input type="hidden" class="form-control" name="lawplansupUrlname" id="lawplansupUrlname" value="${provinceSelectionUnit.findQuestionFileName}">
                                <input type="hidden" class="form-control" name="lawplansupUrl" id="lawplansupUrl" value="${provinceSelectionUnit.findQuestionFileUrl}">
                            </c:if>
                        </span>
                        <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                            <input id="reButton16" <c:if test="${provinceSelectionUnit.findQuestionFileName=='' || provinceSelectionUnit.findQuestionFileName==null }">style='display:none'</c:if>
                                   class="btn btn-danger btn-xs" type="button" value="重新上传" />
                        </c:if>
                    </td>
                </tr>
                <tr>
                    <td></td>
                    <td style="text-align: left;padding: 0 5px;position: relative;top: -18px;left:-5px" colspan="2">
                        <div id="wsc16" style="width: 380px;margin-top: 10px;">
                         <span style="font-size: 16px;">
                            <c:choose>
                                <c:when test="${provinceSelectionUnit.findQuestionFileName!='' && provinceSelectionUnit.findQuestionFileName!=null }">
                                    <div style="float: left;margin-right: 8px; padding-top: 4px;">
<%--                                            ${provinceSelectionUnit.findQuestionFileName}--%>
                                        <a href="javascript:void(0)" onclick="downloadFile('${provinceSelectionUnit.findQuestionFileUrl}',
                                                '${provinceSelectionUnit.findQuestionFileName}')"> ${provinceSelectionUnit.findQuestionFileName}
                                        </a>
                                    </div>
                                </c:when>
                            </c:choose>
                        </span>
                        </div>
                    </td>
                </tr>
                <tr style="text-align: left">
                    <td colspan="2"><div style="margin-left: 22px;">第三方辅助执法机制落实情况</div></td>
                </tr>
                <tr style="text-align: right">
                    <td style="padding-top:20px"><span style="color: red;size: 60px">*</span>是否形成5个处罚案件：</td>
                    <td style="padding-top:20px;text-align: left;position:relative;left:-5px">
                        <div class="form-group">
                            <select style="width:380px;margin-right:5px;" name="isFulawFive" id="isFulawFive">
                                <option value="">--请选择--</option>
                                <option value="1"
                                        <c:if test="${provinceSelectionUnit.isCarryOut eq '1'}">selected</c:if>>是
                                </option>
                                <option value="0"
                                        <c:if test="${provinceSelectionUnit.isCarryOut eq '0'}">selected</c:if>>否
                                </option>
                            </select>
                        </div>
                    </td>
                </tr>
                <tr style="text-align: right">
                    <td rowspan="1"><span style="color: red;size: 60px" class="isFulawShow">*</span> 第三方辅助执法机制案情简介说明材料：</td>
                    <td style="text-align: left;padding-left: 0" colspan="2">
                        <span id="uploadTr17" <c:if test="${provinceSelectionUnit.findQuestionFileName!='' && provinceSelectionUnit.findQuestionFileName!=null }">style='display:none'</c:if>>
                            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                <input id="fulawInfoFile" type="file" name="fulawInfoFile" title="如有相关材料，请上传rar格式文件，大小不超过50M" class="file-loading">
                                <input type="hidden" class="form-control" name="fulawInfoUrlname" id="fulawInfoUrlname" value="${provinceSelectionUnit.findQuestionFileName}">
                                <input type="hidden" class="form-control" name="fulawInfoUrl" id="fulawInfoUrl" value="${provinceSelectionUnit.findQuestionFileUrl}">
                            </c:if>
                        </span>
                        <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                            <input id="reButton17" <c:if test="${provinceSelectionUnit.findQuestionFileName=='' || provinceSelectionUnit.findQuestionFileName==null }">style='display:none'</c:if>
                                   class="btn btn-danger btn-xs" type="button" value="重新上传" />
                        </c:if>
                    </td>
                </tr>
                <tr>
                    <td></td>
                    <td style="text-align: left;padding: 0 5px;position: relative;top: -18px;left:-5px" colspan="2">
                        <div id="wsc17" style="width: 380px;margin-top: 10px;">
                         <span style="font-size: 16px;">
                            <c:choose>
                                <c:when test="${provinceSelectionUnit.findQuestionFileName!='' && provinceSelectionUnit.findQuestionFileName!=null }">
                                    <div style="float: left;margin-right: 8px; padding-top: 4px;">
<%--                                            ${provinceSelectionUnit.findQuestionFileName}--%>
                                        <a href="javascript:void(0)" onclick="downloadFile('${provinceSelectionUnit.findQuestionFileUrl}',
                                                '${provinceSelectionUnit.findQuestionFileName}')"> ${provinceSelectionUnit.findQuestionFileName}
                                        </a>
                                    </div>
                                </c:when>
                            </c:choose>
                        </span>
                        </div>
                    </td>
                </tr>
                <tr style="text-align: right">
                    <td rowspan="1"><span style="color: red;size: 60px" class="isFulawShow">*</span>第三方辅助执法机制案情简介说明材料：</td>
                    <td style="text-align: left;padding-left: 0" colspan="2">
                        <span id="uploadTr18" <c:if test="${provinceSelectionUnit.findQuestionFileName!='' && provinceSelectionUnit.findQuestionFileName!=null }">style='display:none'</c:if>>
                            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                <input id="fwlawDesicionFile" type="file" name="fwlawDesicionFile" title="如有相关材料，请上传rar格式文件，大小不超过50M" class="file-loading">
                                <input type="hidden" class="form-control" name="fwlawDesicionUrlname" id="fwlawDesicionUrlname" value="${provinceSelectionUnit.findQuestionFileName}">
                                <input type="hidden" class="form-control" name="fwlawDesicionUrl" id="fwlawDesicionUrl" value="${provinceSelectionUnit.findQuestionFileUrl}">
                            </c:if>
                        </span>
                        <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                            <input id="reButton18" <c:if test="${provinceSelectionUnit.findQuestionFileName=='' || provinceSelectionUnit.findQuestionFileName==null }">style='display:none'</c:if>
                                   class="btn btn-danger btn-xs" type="button" value="重新上传" />
                        </c:if>
                    </td>
                </tr>
                <tr>
                    <td></td>
                    <td style="text-align: left;padding: 0 5px;position: relative;top: -18px;left:-5px" colspan="2">
                        <div id="wsc18" style="width: 380px;margin-top: 10px;">
                         <span style="font-size: 16px;">
                            <c:choose>
                                <c:when test="${provinceSelectionUnit.findQuestionFileName!='' && provinceSelectionUnit.findQuestionFileName!=null }">
                                    <div style="float: left;margin-right: 8px; padding-top: 4px;">
<%--                                            ${provinceSelectionUnit.findQuestionFileName}--%>
                                        <a href="javascript:void(0)" onclick="downloadFile('${provinceSelectionUnit.findQuestionFileUrl}',
                                                '${provinceSelectionUnit.findQuestionFileName}')"> ${provinceSelectionUnit.findQuestionFileName}
                                        </a>
                                    </div>
                                </c:when>
                            </c:choose>
                        </span>
                        </div>
                    </td>
                </tr>

                <tr style="text-align: left">
                    <td colspan="2"><div style="margin-left: 22px;">执法普法制度落实情况</div></td>
                </tr>
                <tr style="text-align: right">
                    <td style="padding-top:20px"><span style="color: red;size: 60px">*</span>是否针对中小企业重点进行执法帮扶、开展普法培训、“送法入企”活动：</td>
                    <td style="padding-top:20px;text-align: left;position:relative;left:-5px">
                        <div class="form-group">
                            <select style="width:380px;margin-right:5px;" name="isLawGuide" id="isLawGuide">
                                <option value="">--请选择--</option>
                                <option value="1"
                                        <c:if test="${provinceSelectionUnit.isCarryOut eq '1'}">selected</c:if>>是
                                </option>
                                <option value="0"
                                        <c:if test="${provinceSelectionUnit.isCarryOut eq '0'}">selected</c:if>>否
                                </option>
                            </select>
                        </div>
                    </td>
                </tr>
                <tr style="text-align: right">
                    <td rowspan="1"><span style="color: red;size: 60px" class="isLawGuideShow">*</span>开展相关活动的新闻稿网页截图或活动方案：</td>
                    <td style="text-align: left;padding-left: 0" colspan="2">
                        <span id="uploadTr19" <c:if test="${provinceSelectionUnit.findQuestionFileName!='' && provinceSelectionUnit.findQuestionFileName!=null }">style='display:none'</c:if>>
                            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                <input id="lawGuideFile" type="file" name="lawGuideFile" title="如有相关材料，请上传rar格式文件，大小不超过50M" class="file-loading">
                                <input type="hidden" class="form-control" name="lawGuideUrlname" id="lawGuideUrlname" value="${provinceSelectionUnit.findQuestionFileName}">
                                <input type="hidden" class="form-control" name="lawGuideUrl" id="lawGuideUrl" value="${provinceSelectionUnit.findQuestionFileUrl}">
                            </c:if>
                        </span>
                        <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                            <input id="reButton19" <c:if test="${provinceSelectionUnit.findQuestionFileName=='' || provinceSelectionUnit.findQuestionFileName==null }">style='display:none'</c:if>
                                   class="btn btn-danger btn-xs" type="button" value="重新上传" />
                        </c:if>
                    </td>
                </tr>
                <tr>
                    <td></td>
                    <td style="text-align: left;padding: 0 5px;position: relative;top: -18px;left:-5px" colspan="2">
                        <div id="wsc19" style="width: 380px;margin-top: 10px;">
                         <span style="font-size: 16px;">
                            <c:choose>
                                <c:when test="${provinceSelectionUnit.findQuestionFileName!='' && provinceSelectionUnit.findQuestionFileName!=null }">
                                    <div style="float: left;margin-right: 8px; padding-top: 4px;">
<%--                                            ${provinceSelectionUnit.findQuestionFileName}--%>
                                        <a href="javascript:void(0)" onclick="downloadFile('${provinceSelectionUnit.findQuestionFileUrl}',
                                                '${provinceSelectionUnit.findQuestionFileName}')"> ${provinceSelectionUnit.findQuestionFileName}
                                        </a>
                                    </div>
                                </c:when>
                            </c:choose>
                        </span>
                        </div>
                    </td>
                </tr>

                <tr style="text-align: left">
                    <td colspan="2"><div style="margin-left: 22px;">执法履职评估制度落实情况</div></td>
                </tr>
                <tr style="text-align: right">
                    <td style="padding-top:20px"><span style="color: red;size: 60px">*</span>是否开展执法履职评估试点工作：</td>
                    <td style="padding-top:20px;text-align: left;position:relative;left:-5px">
                        <div class="form-group">
                            <select style="width:380px;margin-right:5px;" name="isAssessment" id="isAssessment">
                                <option value="">--请选择--</option>
                                <option value="1"
                                        <c:if test="${provinceSelectionUnit.isCarryOut eq '1'}">selected</c:if>>是
                                </option>
                                <option value="0"
                                        <c:if test="${provinceSelectionUnit.isCarryOut eq '0'}">selected</c:if>>否
                                </option>
                            </select>
                        </div>
                    </td>
                </tr>
                <tr style="text-align: right">
                    <td rowspan="1"><span style="color: red;size: 60px" class="isAssessmentShow">*</span>执法履职评估试点工作开展情况说明：</td>
                    <td style="text-align: left;padding-left: 0" colspan="2">
                        <span id="uploadTr20" <c:if test="${provinceSelectionUnit.findQuestionFileName!='' && provinceSelectionUnit.findQuestionFileName!=null }">style='display:none'</c:if>>
                            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                <input id="assessmentFile" type="file" name="assessmentFile" title="如有相关材料，请上传rar格式文件，大小不超过50M" class="file-loading">
                                <input type="hidden" class="form-control" name="assessmentUrlname" id="assessmentUrlname" value="${provinceSelectionUnit.findQuestionFileName}">
                                <input type="hidden" class="form-control" name="assessmentUrl" id="assessmentUrl" value="${provinceSelectionUnit.findQuestionFileUrl}">
                            </c:if>
                        </span>
                        <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                            <input id="reButton20" <c:if test="${provinceSelectionUnit.findQuestionFileName=='' || provinceSelectionUnit.findQuestionFileName==null }">style='display:none'</c:if>
                                   class="btn btn-danger btn-xs" type="button" value="重新上传" />
                        </c:if>
                    </td>
                </tr>
                <tr>
                    <td></td>
                    <td style="text-align: left;padding: 0 5px;position: relative;top: -18px;left:-5px" colspan="2">
                        <div id="wsc20" style="width: 380px;margin-top: 10px;">
                         <span style="font-size: 16px;">
                            <c:choose>
                                <c:when test="${provinceSelectionUnit.findQuestionFileName!='' && provinceSelectionUnit.findQuestionFileName!=null }">
                                    <div style="float: left;margin-right: 8px; padding-top: 4px;">
<%--                                            ${provinceSelectionUnit.findQuestionFileName}--%>
                                        <a href="javascript:void(0)" onclick="downloadFile('${provinceSelectionUnit.findQuestionFileUrl}',
                                                '${provinceSelectionUnit.findQuestionFileName}')"> ${provinceSelectionUnit.findQuestionFileName}
                                        </a>
                                    </div>
                                </c:when>
                            </c:choose>
                        </span>
                        </div>
                    </td>
                </tr>
                <tr style="text-align: right">
                    <td style="padding-top:20px"><span style="color: red;size: 60px">*</span>是否出台履职评估办法：</td>
                    <td style="padding-top:20px;text-align: left;position:relative;left:-5px">
                        <div class="form-group">
                            <select style="width:380px;margin-right:5px;" name="isDwPeoples" id="isDwPeoples">
                                <option value="">--请选择--</option>
                                <option value="1"
                                        <c:if test="${provinceSelectionUnit.isCarryOut eq '1'}">selected</c:if>>是
                                </option>
                                <option value="0"
                                        <c:if test="${provinceSelectionUnit.isCarryOut eq '0'}">selected</c:if>>否
                                </option>
                            </select>
                        </div>
                    </td>
                </tr>
                <tr style="text-align: right">
                    <td rowspan="1"><span style="color: red;size: 60px" class="isDwPeopleShows">*</span>履职评估办法相关文件材料：</td>
                    <td style="text-align: left;padding-left: 0" colspan="2">
                        <span id="uploadTr21" <c:if test="${provinceSelectionUnit.findQuestionFileName!='' && provinceSelectionUnit.findQuestionFileName!=null }">style='display:none'</c:if>>
                            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                <input id="dwPeopleFiles" type="file" name="dwPeopleFiles" title="如有相关材料，请上传rar格式文件，大小不超过50M" class="file-loading">
                                <input type="hidden" class="form-control" name="dwPeopleUrlname" id="dwPeopleUrlname" value="${provinceSelectionUnit.findQuestionFileName}">
                                <input type="hidden" class="form-control" name="dwPeopleUrl" id="dwPeopleUrl" value="${provinceSelectionUnit.findQuestionFileUrl}">
                            </c:if>
                        </span>
                        <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                            <input id="reButton21" <c:if test="${provinceSelectionUnit.findQuestionFileName=='' || provinceSelectionUnit.findQuestionFileName==null }">style='display:none'</c:if>
                                   class="btn btn-danger btn-xs" type="button" value="重新上传" />
                        </c:if>
                    </td>
                </tr>
                <tr>
                    <td></td>
                    <td style="text-align: left;padding: 0 5px;position: relative;top: -18px;left:-5px" colspan="2">
                        <div id="wsc21" style="width: 380px;margin-top: 10px;">
                         <span style="font-size: 16px;">
                            <c:choose>
                                <c:when test="${provinceSelectionUnit.findQuestionFileName!='' && provinceSelectionUnit.findQuestionFileName!=null }">
                                    <div style="float: left;margin-right: 8px; padding-top: 4px;">
<%--                                            ${provinceSelectionUnit.findQuestionFileName}--%>
                                        <a href="javascript:void(0)" onclick="downloadFile('${provinceSelectionUnit.findQuestionFileUrl}',
                                                '${provinceSelectionUnit.findQuestionFileName}')"> ${provinceSelectionUnit.findQuestionFileName}
                                        </a>
                                    </div>
                                </c:when>
                            </c:choose>
                        </span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td style="text-align: left;font-size: 16px;font-weight: 400;color: #31688F">二、队伍能力建设</td>
                </tr>
                <tr style="text-align: left;">
                    <td colspan="2" style="padding: 0;height: 33px;line-height:33px;background: #F8F8F8;">
                        <div>
                            <h2 style="font-weight: 400;color: #31688F;font-size: 14px;margin-top:10px;">执法队伍管理</h2>
                        </div>
                    </td>
                </tr>
                <tr style="text-align: left">
                    <td colspan="2"><div style="margin-left: 22px;">人员培训</div></td>
                </tr>
                <tr style="text-align: right">
                    <td style="padding-top:20px">
                        <c:if test="${areaUser.arealevel == '2'}"><span style="color: red;size: 60px">*</span>是否组织开展新技术、新装备应用培训：</c:if>
                        <c:if test="${areaUser.arealevel == '3'}"><span style="color: red;size: 60px">*</span>是否参加市级新技术、新装备应用培训：</c:if>
                    </td>
                    <td style="padding-top:20px;text-align: left;position:relative;left:-5px">
                        <div class="form-group">
                            <select style="width:380px;margin-right:5px;" name="isDwPeople" id="isDwPeople">
                                <option value="">--请选择--</option>
                                <option value="1"
                                        <c:if test="${provinceSelectionUnit.isCarryOut eq '1'}">selected</c:if>>是
                                </option>
                                <option value="0"
                                        <c:if test="${provinceSelectionUnit.isCarryOut eq '0'}">selected</c:if>>否
                                </option>
                            </select>
                        </div>
                    </td>
                </tr>
                <tr style="text-align: right">
                    <td rowspan="1"><span style="color: red;size: 60px" class="isDwPeopleShow">*</span>人员培训活动开展情况新闻稿或网页截图：</td>
                    <td style="text-align: left;padding-left: 0" colspan="2">
                        <span id="uploadTr22" <c:if test="${provinceSelectionUnit.findQuestionFileName!='' && provinceSelectionUnit.findQuestionFileName!=null }">style='display:none'</c:if>>
                            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                <input id="dwPeopleFile" type="file" name="dwPeopleFile" title="如有相关材料，请上传rar格式文件，大小不超过50M" class="file-loading">
                                <input type="hidden" class="form-control" name="dwPeopleUrlname" id="dwPeopleUrlname" value="${provinceSelectionUnit.findQuestionFileName}">
                                <input type="hidden" class="form-control" name="dwPeopleUrl" id="dwPeopleUrl" value="${provinceSelectionUnit.findQuestionFileUrl}">
                            </c:if>
                        </span>
                        <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                            <input id="reButton22" <c:if test="${provinceSelectionUnit.findQuestionFileName=='' || provinceSelectionUnit.findQuestionFileName==null }">style='display:none'</c:if>
                                   class="btn btn-danger btn-xs" type="button" value="重新上传" />
                        </c:if>
                    </td>
                </tr>
                <tr>
                    <td></td>
                    <td style="text-align: left;padding: 0 5px;position: relative;top: -18px;left:-5px" colspan="2">
                        <div id="wsc22" style="width: 380px;margin-top: 10px;">
                         <span style="font-size: 16px;">
                            <c:choose>
                                <c:when test="${provinceSelectionUnit.findQuestionFileName!='' && provinceSelectionUnit.findQuestionFileName!=null }">
                                    <div style="float: left;margin-right: 8px; padding-top: 4px;">
<%--                                            ${provinceSelectionUnit.findQuestionFileName}--%>
                                        <a href="javascript:void(0)" onclick="downloadFile('${provinceSelectionUnit.findQuestionFileUrl}',
                                                '${provinceSelectionUnit.findQuestionFileName}')"> ${provinceSelectionUnit.findQuestionFileName}
                                        </a>
                                    </div>
                                </c:when>
                            </c:choose>
                        </span>
                        </div>
                    </td>
                </tr>

                <tr style="text-align: left">
                    <td colspan="2"><div style="margin-left: 22px;">队列训练</div></td>
                </tr>
                <tr style="text-align: right">
                    <td style="padding-top:20px">
                        <c:if test="${areaUser.arealevel == '2'}"><span style="color: red;size: 60px">*</span>是否组织全市开展队列训练：</c:if>
                        <c:if test="${areaUser.arealevel == '3'}"><span style="color: red;size: 60px">*</span>是否参加市级队列训练：</c:if>
                    </td>
                    <td style="padding-top:20px;text-align: left;position:relative;left:-5px">
                        <div class="form-group">
                            <select style="width:380px;margin-right:5px;" name="isDwTeam" id="isDwTeam">
                                <option value="">--请选择--</option>
                                <option value="1"
                                        <c:if test="${provinceSelectionUnit.isCarryOut eq '1'}">selected</c:if>>是
                                </option>
                                <option value="0"
                                        <c:if test="${provinceSelectionUnit.isCarryOut eq '0'}">selected</c:if>>否
                                </option>
                            </select>
                        </div>
                    </td>
                </tr>
                <tr style="text-align: right">
                    <td rowspan="1"><span style="color: red;size: 60px" class="isDwTeamShow">*</span>队列训练活动开展情况新闻稿或网页截图：</td>
                    <td style="text-align: left;padding-left: 0" colspan="2">
                        <span id="uploadTr23" <c:if test="${provinceSelectionUnit.findQuestionFileName!='' && provinceSelectionUnit.findQuestionFileName!=null }">style='display:none'</c:if>>
                            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                <input id="dwTeamFile" type="file" name="dwTeamFile" title="如有相关材料，请上传rar格式文件，大小不超过50M" class="file-loading">
                                <input type="hidden" class="form-control" name="dwTeamUrlname" id="dwTeamUrlname" value="${provinceSelectionUnit.findQuestionFileName}">
                                <input type="hidden" class="form-control" name="dwTeamUrl" id="dwTeamUrl" value="${provinceSelectionUnit.findQuestionFileUrl}">
                            </c:if>
                        </span>
                        <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                            <input id="reButton23" <c:if test="${provinceSelectionUnit.findQuestionFileName=='' || provinceSelectionUnit.findQuestionFileName==null }">style='display:none'</c:if>
                                   class="btn btn-danger btn-xs" type="button" value="重新上传" />
                        </c:if>
                    </td>
                </tr>
                <tr>
                    <td></td>
                    <td style="text-align: left;padding: 0 5px;position: relative;top: -18px;left:-5px" colspan="2">
                        <div id="wsc23" style="width: 380px;margin-top: 10px;">
                         <span style="font-size: 16px;">
                            <c:choose>
                                <c:when test="${provinceSelectionUnit.findQuestionFileName!='' && provinceSelectionUnit.findQuestionFileName!=null }">
                                    <div style="float: left;margin-right: 8px; padding-top: 4px;">
<%--                                            ${provinceSelectionUnit.findQuestionFileName}--%>
                                        <a href="javascript:void(0)" onclick="downloadFile('${provinceSelectionUnit.findQuestionFileUrl}',
                                                '${provinceSelectionUnit.findQuestionFileName}')"> ${provinceSelectionUnit.findQuestionFileName}
                                        </a>
                                    </div>
                                </c:when>
                            </c:choose>
                        </span>
                        </div>
                    </td>
                </tr>

                <tr style="text-align: left">
                    <td colspan="2"><div style="margin-left: 22px;">技能竞赛</div></td>
                </tr>
                <tr style="text-align: right">
                    <td style="padding-top:20px">
                        <c:if test="${areaUser.arealevel == '2'}"><span style="color: red;size: 60px">*</span>是否组织开展全市技能竞赛比武：</c:if>
                        <c:if test="${areaUser.arealevel == '3'}"><span style="color: red;size: 60px">*</span>是否参加市级技能竞赛比武：</c:if>
                    </td>

                    <td style="padding-top:20px;text-align: left;position:relative;left:-5px">
                        <div class="form-group">
                            <select style="width:380px;margin-right:5px;" name="isDwSkill" id="isDwSkill">
                                <option value="">--请选择--</option>
                                <option value="1"
                                        <c:if test="${provinceSelectionUnit.isCarryOut eq '1'}">selected</c:if>>是
                                </option>
                                <option value="0"
                                        <c:if test="${provinceSelectionUnit.isCarryOut eq '0'}">selected</c:if>>否
                                </option>
                            </select>
                        </div>
                    </td>
                </tr>
                <tr style="text-align: right">
                    <td rowspan="1"><span style="color: red;size: 60px" class="isDwSkillShow">*</span>技能竞赛活动开展情况新闻稿或网页截图：</td>
                    <td style="text-align: left;padding-left: 0" colspan="2">
                        <span id="uploadTr24" <c:if test="${provinceSelectionUnit.findQuestionFileName!='' && provinceSelectionUnit.findQuestionFileName!=null }">style='display:none'</c:if>>
                            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                <input id="dwSkillFile" type="file" name="dwSkillFile" title="如有相关材料，请上传rar格式文件，大小不超过50M" class="file-loading">
                                <input type="hidden" class="form-control" name="dwSkillUrlname" id="dwSkillUrlname" value="${provinceSelectionUnit.findQuestionFileName}">
                                <input type="hidden" class="form-control" name="dwSkillUrl" id="dwSkillUrl" value="${provinceSelectionUnit.findQuestionFileUrl}">
                            </c:if>
                        </span>
                        <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                            <input id="reButton24" <c:if test="${provinceSelectionUnit.findQuestionFileName=='' || provinceSelectionUnit.findQuestionFileName==null }">style='display:none'</c:if>
                                   class="btn btn-danger btn-xs" type="button" value="重新上传" />
                        </c:if>
                    </td>
                </tr>
                <tr>
                    <td></td>
                    <td style="text-align: left;padding: 0 5px;position: relative;top: -18px;left:-5px" colspan="2">
                        <div id="wsc24" style="width: 380px;margin-top: 10px;">
                         <span style="font-size: 16px;">
                            <c:choose>
                                <c:when test="${provinceSelectionUnit.findQuestionFileName!='' && provinceSelectionUnit.findQuestionFileName!=null }">
                                    <div style="float: left;margin-right: 8px; padding-top: 4px;">
<%--                                            ${provinceSelectionUnit.findQuestionFileName}--%>
                                        <a href="javascript:void(0)" onclick="downloadFile('${provinceSelectionUnit.findQuestionFileUrl}',
                                                '${provinceSelectionUnit.findQuestionFileName}')"> ${provinceSelectionUnit.findQuestionFileName}
                                        </a>
                                    </div>
                                </c:when>
                            </c:choose>
                        </span>
                        </div>
                    </td>
                </tr>

                <tr>
                    <td style="text-align: left;font-size: 16px;font-weight: 400;color: #31688F">三、专项行动表现</td>
                </tr>
                <tr style="text-align: left;">
                    <td colspan="2" style="padding: 0;height: 33px;line-height:33px;background: #F8F8F8;">
                        <div>
                            <h2 style="font-weight: 400;color: #31688F;font-size: 14px;margin-top:10px;">开展排污许可执法检查</h2>
                        </div>
                    </td>
                </tr>
                <tr style="text-align: right">
                    <td style="padding-top:20px;">
                        <c:if test="${areaUser.arealevel == '2'}">
                            <div><span style="color: red;size: 60px">*</span>是否组织开展排污许可执法检查：</div></c:if>
                        <c:if test="${areaUser.arealevel == '3'}">
                            <div><span style="color: red;size: 60px">*</span>是否组织开展排污许可执法检查：</div></c:if></td>
                    <%--                    <td style="padding-top:20px;"><div><span style="color: red;size: 60px">*</span>是否组织开展排污许可执法检查：</div></td>--%>
                    <td style="padding-top:20px;text-align: left;position: relative;left: -5px">
                        <div class="form-group">
                            <select style="width:380px;margin-right:5px;" name="isLicenseLaw" id="isLicenseLaw">
                                <option value="">--请选择--</option>
                                <option value="1"
                                        <c:if test="${provinceSelectionUnit.isCarryOut eq '1'}">selected</c:if>>是
                                </option>
                                <option value="0"
                                        <c:if test="${provinceSelectionUnit.isCarryOut eq '0'}">selected</c:if>>否
                                </option>
                            </select>
                        </div>
                    </td>
                </tr>

                <tr style="text-align: right">
                    <td style="padding-top:20px;"><div><span style="color: red;size: 60px" class="isLicenseList">*</span>是否在排污许可执法检查中开展清单式执法：</div></td>
                    <td style="padding-top:20px;text-align: left;position: relative;left: -5px">
                        <div class="form-group">
                            <select style="width:380px;margin-right:5px;" name="isLicenseList" id="isLicenseList">
                                <option value="">--请选择--</option>
                                <option value="1"
                                        <c:if test="${provinceSelectionUnit.isCarryOut eq '1'}">selected</c:if>>是
                                </option>
                                <option value="0"
                                        <c:if test="${provinceSelectionUnit.isCarryOut eq '0'}">selected</c:if>>否
                                </option>
                            </select>
                        </div>
                    </td>
                </tr>
                <tr style="text-align: right">
                    <td rowspan="1"><span style="color: red;size: 60px" class="inlicensesShow">*</span>开展排污许可执法检查支撑材料：</td>
                    <td style="text-align: left;" colspan="2">
                        <span id="uploadTr25" <c:if test="${provinceSelectionUnit.citySpecialActionShowName!='' && provinceSelectionUnit.citySpecialActionShowName!=null }">style='display:none'</c:if>>
                            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                <input id="licensesFile" type="file" name="licensesFile" title="如有相关材料，请上传rar格式文件，大小不超过50M" class="file-loading">
                                <input type="hidden" class="form-control" name="licensesupUrlname" id="licensesupUrlname" value="${provinceSelectionUnit.citySpecialActionShowName}">
                                <input type="hidden" class="form-control" name="licensesupUrl" id="licensesupUrl" value="${provinceSelectionUnit.citySpecialActionShowUrl}">
                            </c:if>
                        </span>
                        <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                            <input id="reButton25" <c:if test="${provinceSelectionUnit.citySpecialActionShowName=='' || provinceSelectionUnit.citySpecialActionShowName==null }">style='display:none'</c:if>
                                   class="btn btn-danger btn-xs" type="button" value="重新上传" />
                        </c:if>
                    </td>
                </tr>
                <tr>
                    <td></td>
                    <td style="text-align: left;padding: 0 5px;position: relative;top: -18px;left:-5px" colspan="2">
                        <div id="wsc25" style="padding-left:5px;width: 380px;margin-top: 10px;">
                            <span style="font-size: 16px;">
                            <c:choose>
                                <c:when test="${provinceSelectionUnit.citySpecialActionShowName!='' && provinceSelectionUnit.citySpecialActionShowName!=null }">
                                    <div style="float: left; margin-right: 8px; ">
<%--                                            ${provinceSelectionUnit.citySpecialActionShowName}--%>
                                         <a href="javascript:void(0)" onclick="downloadFile('${provinceSelectionUnit.citySpecialActionShowUrl}',
                                                 '${provinceSelectionUnit.citySpecialActionShowName}')"> ${provinceSelectionUnit.citySpecialActionShowName}
                                         </a>
                                    </div>
                                </c:when>
                            </c:choose>
                            </span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td style="text-align: left;position:relative;top:-30px" colspan="2">
                        <div style="width:560px;margin-left: 509px;color: #FB3301;font-weight: 400;line-height:22px"><p style="font-size: 12px;">开展排污许可清单式执法的文字说明或者行动方案等相关文件材料。</p></div>
                    </td>
                </tr>
                <tr>
                    <td style="text-align: left;font-size: 16px;font-weight: 400;color: #31688F">四、执法公众满意度</td>
                </tr>
                <tr style="text-align: right">
                    <td><span style="color: red;size: 60px">*</span> 执法公众满意度支撑材料：</td>
                    <td style="padding-left:0;text-align: left;" colspan="2">

                        <span id="uploadTr26" <c:if test="${provinceSelectionUnit.ccnxzjwffzCaseFileName!='' && provinceSelectionUnit.ccnxzjwffzCaseFileName!=null }">style='display:none'</c:if>>
                        <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                            <input id="lawSatisfactionFile" type="file" name="lawSatisfactionFile" title="如有相关材料，请上传rar格式文件，大小不超过50M" class="file-loading">
                            <input type="hidden" class="form-control" name="lawSatisfactionUrlname" id="lawSatisfactionUrlname" value="${provinceSelectionUnit.ccnxzjwffzCaseFileName}">
                            <input type="hidden" class="form-control" name="lawSatisfactionUrl" id="lawSatisfactionUrl" value="${provinceSelectionUnit.ccnxzjwffzCaseFileUrl}">
                        </c:if>
                    </span>
                        <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                            <input id="reButton26" <c:if test="${provinceSelectionUnit.ccnxzjwffzCaseFileName=='' || provinceSelectionUnit.ccnxzjwffzCaseFileName==null }">style='display:none'</c:if>
                                   class="btn btn-danger btn-xs" type="button" value="重新上传" />
                        </c:if>
                    </td>
                </tr>
                <tr>
                    <td></td>
                    <td style="text-align: left;padding: 0 5px;position: relative;top: -18px;left:-5px" colspan="2">
                        <div id="wsc26" style="padding-left:5px;width: 380px;margin-top: 10px;">
                            <span style="font-size: 16px;">
                                <c:choose>
                                    <c:when test="${provinceSelectionUnit.ccnxzjwffzCaseFileName!='' && provinceSelectionUnit.ccnxzjwffzCaseFileName!=null }">
                                        <div style="float: left;width:100%; margin-right: 8px; padding-top: 0px;">
        <%--                                        ${provinceSelectionUnit.ccnxzjwffzCaseFileName}--%>
                                             <a href="javascript:void(0)" onclick="downloadFile('${provinceSelectionUnit.ccnxzjwffzCaseFileUrl}',
                                                     '${provinceSelectionUnit.ccnxzjwffzCaseFileName}')"> ${provinceSelectionUnit.ccnxzjwffzCaseFileName}
                                             </a>
                                        </div>
                                    </c:when>
                                </c:choose>
                            </span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td style="text-align: left;position:relative;top:-30px" colspan="2">
                        <div style="margin-left: 509px;color: #FB3301;font-weight: 400;line-height:22px">
                            <p style="font-size: 12px;">需上传集体事迹材料与集体照片，存放在一个文件夹中。要求rar格式，文件夹(压缩包)以【省份-二级标题】命名。</p>
                            <p style="font-size: 12px;">集体事迹材料与集体照片命名格式分别为行政区划编码-市县级行政区-机构全称-xxx。例如河北省：130000-河北省-河北省生态环境厅-集体事迹材料）</p>
                        </div>
                    </td>
                </tr>
                <%-- <tr>
                    <td>行政区内重点排污单位数量<span style="color: red;size: 60px">*</span></i></td>
                    <td>
                        <div class="form-group">
                            <input <c:if test="${provinceReportUser.isDevInternalEva ==1 || provinceReportUser.electcityState == 1 || sysInitConfig.code !='1'}">disabled="disabled"</c:if>
                             type="text" class="form-control" id="polluternumber" name="polluternumber" value="${provinceSelectionUnit.polluternumber}" placeholder="请输入行政区内重点排污单位数量">
                            &lt;%&ndash; <div class="input-group">
                                <input type="text" class="form-control" readonly id="polluternumber" name="polluternumber" value="${provinceSelectionUnit.polluternumber}" placeholder="请输入全省重点排污单位数量">
                                <input type="hidden" id="initpolluternum" name="initpolluternum" value="${provinceReportUser.initpolluternum }" />
                                <span class="input-group-btn">
                                <button id="modifyBut" class="btn btn-danger" type="button" <c:if test="${provinceReportUser.isDevInternalEva ==1 || provinceReportUser.electcityState == 1 || sysInitConfig.code !='1'}">disabled="disabled"</c:if>> 修改 </button>
                                </span>
                             </div> &ndash;%&gt;
                        </div>
                    </td>
                  </tr>

                  <c:if test="${areaUser.arealevel == '2' && areaUser.areaCode.substring(0,2)!='11' && areaUser.areaCode.substring(0,2)!='12' && areaUser.areaCode.substring(0,2)!='31' && areaUser.areaCode.substring(0,2)!='55' && areaUser.areaCode.substring(0,2)!='66'}">
                         <tr>
                            <td>行政区内重点排污单位名录（加盖公章）<span style="color: red;size: 60px">*</span></i></td>
                            <td style="text-align:left;">
                            <span id="wsc" style="font-size:16px;">
                            <c:choose>
                                <c:when test="${provinceSelectionUnit.polluterevidencename!='' && provinceSelectionUnit.polluterevidencename!=null }">
                                    <div style="float:left;margin-right:8px;padding-top:4px;">
                                        ${provinceSelectionUnit.polluterevidencename}
                                    </div>
                                </c:when>

                            </c:choose>
                            </span>
                            <span id="uploadTr" <c:if test="${provinceSelectionUnit.polluterevidencename!='' && provinceSelectionUnit.polluterevidencename!=null }">style='display:none'</c:if>>
                                <c:if test="${sysInitConfig.code =='1' && provinceReportUser.electcityState != 1 && provinceReportUser.isDevInternalEva !=1}">
                                <input id="polluterfile" type="file" name="polluterfile" title="提示：附件要求PDF格式，大小不超过50M。" class="file-loading">
                                <input type="hidden" class="form-control" name="polluterevidencename" id="polluter_Filename" value="${provinceSelectionUnit.polluterevidencename}">
                                <input type="hidden" class="form-control" name="polluterevidenceurl" id="polluter_Fileurl" value="${provinceSelectionUnit.polluterevidenceurl}">
                                </c:if>
                              </span>
                            </td>
                            <td>
                                <c:if test="${sysInitConfig.code =='1' && provinceReportUser.electcityState !=1 && provinceReportUser.isDevInternalEva !=1}">
                                        <input id="reButton" <c:if test="${provinceSelectionUnit.polluterevidencename=='' || provinceSelectionUnit.polluterevidencename==null }">style='display:none'</c:if>
                                        class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                                </c:if>
                            </td>
                          </tr>
                         <tr>
                             <td>公开方式<span style="color: red;size: 60px">*</span></i></td>
                             <td>
                                <div class="form-group">
                                <div class="col-sm-15">
                                    <select <c:if test="${provinceReportUser.isDevInternalEva ==1 || provinceReportUser.electcityState == 1  || sysInitConfig.code !='1'}">disabled="disabled"</c:if>name="publicityway" id="publicityway"  onchange="listchange(this.value);return false;" class="form-control">
                                       <option  value="">请选择</option>
                                       <option  <c:if test="${provinceSelectionUnit.publicityway==0}">selected</c:if> value="0">无</option>
                                       <option  <c:if test="${provinceSelectionUnit.publicityway==1}">selected</c:if> value="1">网站公开</option>
                                       <option  <c:if test="${provinceSelectionUnit.publicityway==2}">selected</c:if> value="2">其他公开方式</option>
                                     </select>
                                </div>
                                </div>

                             </td>
                         </tr>
                         <tr id="tasklistoriginalno1" style="display:none;">
                            <td>公开网站地址</td>
                            <td>
                                <div >
                                    <div class="form-group">
                                    <input <c:if test="${provinceReportUser.isDevInternalEva ==1 || provinceReportUser.electcityState == 1  || sysInitConfig.code !='1'}">disabled="disabled"</c:if>type="text" class="form-control" id="website" name="website" value="${provinceSelectionUnit.website}" placeholder="请输入公开网站地址">
                                    </div>
                                </div>
                            </td>
                         </tr>
                         <tr id="tasklistoriginalno2" style="display:none;">
                            <td>其他公开方式描述</td>
                            <td>
                                <div class="form-group" >
                                    <textarea <c:if test="${provinceReportUser.isDevInternalEva ==1 || provinceReportUser.electcityState == 1  || sysInitConfig.code !='1'}">disabled="disabled"</c:if>rows="6" class="form-control" id="publicitywaydescribe" name="publicitywaydescribe" placeholder="请输入其他公开方式描述">${provinceSelectionUnit.publicitywaydescribe}</textarea>
                                </div>
                            </td>
                         </tr>
                      </c:if>--%>
                <%--<tr>
                  <td>集体事迹材料<span style="color: red;size: 60px">*</span></i></td>
                  <td style="text-align:left">
                      <span id="wsc2" style="font-size:16px;">
                      <c:choose>
                          <c:when test="${provinceSelectionUnit.deedevidencename!='' && provinceSelectionUnit.deedevidencename!=null }">
                              <div style="float:left;margin-right:8px;padding-top:4px;">
                                  ${provinceSelectionUnit.deedevidencename}
                              </div>
                          </c:when>
                          &lt;%&ndash; <c:otherwise>
                          <font>未上传</font>
                          </c:otherwise> &ndash;%&gt;
                      </c:choose>
                      </span>
                      <span id="uploadTr2" <c:if test="${provinceSelectionUnit.deedevidencename!='' && provinceSelectionUnit.deedevidencename!=null }">style='display:none'</c:if>>
                          <c:if test="${sysInitConfig.code =='1' && provinceReportUser.electcityState !=1 && provinceReportUser.isDevInternalEva !=1}">
                          <input id="evidencefile" type="file" name="evidencefile" title="要求1500字以内，请上传word文件，大小不超过1M。" class="file-loading">
                              <span style="color: deepskyblue;">要求1500字以内，请上传word文件，大小不超过1M。</span>
                          <input type="hidden" class="form-control" name="deedevidencename" id="evidence_Filename" value="${provinceSelectionUnit.deedevidencename}">
                          <input type="hidden" class="form-control" name="deedevidenceurl" id="evidence_Fileurl" value="${provinceSelectionUnit.deedevidenceurl}">
                          </c:if>
                       </span>
                  </td>
                  <td>
                      <c:if test="${sysInitConfig.code =='1' && provinceReportUser.electcityState !=1 && provinceReportUser.isDevInternalEva !=1}">
                              <input id="reButton2" <c:if test="${provinceSelectionUnit.deedevidencename=='' || provinceSelectionUnit.deedevidencename==null }">style='display:none'</c:if>
                              class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                      </c:if>
                  </td>
                </tr>

                <tr>
                  <td>集体照片<span style="color: red;size: 60px">*</span></i></td>
                  <td style="text-align:left;">
                      <span id="wsc3" style="font-size:16px;">
                      <c:choose>
                          <c:when test="${provinceSelectionUnit.deedpicturename!='' && provinceSelectionUnit.deedpicturename!=null }">
                              <div style="float:left;margin-right:8px;padding-top:4px;">
                                  ${provinceSelectionUnit.deedpicturename}
                              </div>
                          </c:when>
                          &lt;%&ndash; <c:otherwise>
                          <font>未上传</font>
                          </c:otherwise> &ndash;%&gt;
                      </c:choose>
                      </span>
                      <span id="uploadTr3" <c:if test="${provinceSelectionUnit.deedpicturename!='' && provinceSelectionUnit.deedpicturename!=null }">style='display:none'</c:if>>
                          <c:if test="${sysInitConfig.code =='1' && provinceReportUser.electcityState !=1 && provinceReportUser.isDevInternalEva !=1}">
                          <input id="picturefile" type="file" name="picturefile" title="请上传单位大门或集体照片一张，要求JPG、PNG、BMP格式，大小不超过20M。" class="file-loading">
                          <!-- <span style="color: red;">提示：请上传单位大门或集体照片一张。附件要求JPG、PNG、BMP格式，大小不超过20M。</span> -->
                          <input type="hidden" class="form-control" name="deedpicturename" id="picture_Filename" value="${provinceSelectionUnit.deedpicturename}">
                          <input type="hidden" class="form-control" name="deedpictureurl" id="picture_Fileurl" value="${provinceSelectionUnit.deedpictureurl}">
                          </c:if>
                      </span>
                  </td>
                  <td>
                      <c:if test="${sysInitConfig.code =='1' && provinceReportUser.electcityState !=1 && provinceReportUser.isDevInternalEva !=1}">
                              <input id="reButton3" <c:if test="${provinceSelectionUnit.deedpicturename=='' || provinceSelectionUnit.deedpicturename==null }">style='display:none'</c:if>
                              class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                      </c:if>
                  </td>
               </tr>
              <tr>
                  <td>是否因党建工作突出被表扬<span style="color: red;size: 60px">*</span></i></td>
                  <td style="text-align: right">
                      <div class="form-group">
                          <div class="col-sm-15">
                              <select <c:if test="${provinceReportUser.isDevInternalEva ==1 || provinceReportUser.electcityState == 1  || sysInitConfig.code !='1'}">disabled="disabled"</c:if>name="isPraise" id="isPraise"  onchange="listchange(this.value);return false;" class="form-control">
                                  <option  value="">请选择</option>
                                  <option  <c:if test="${provinceSelectionUnit.isPraise==0}">selected</c:if> value="0">是</option>
                                  <option  <c:if test="${provinceSelectionUnit.isPraise==1}">selected</c:if> value="1">否</option>
                              </select>
                              <input type="hidden" id="isPraiseVal">
                          </div>
                      </div>
                      <span style="color: deepskyblue;">如选择是，请上传证明材料。</span>
                  </td>
              </tr>

              <tr>
                  <td>证明材料</td>
                  <td style="text-align:left;">
                                  <span id="wsc4" style="font-size:16px;">
                                  <c:choose>
                                      <c:when test="${provinceSelectionUnit.praiseName!='' && provinceSelectionUnit.praiseName!=null }">
                                          <div style="float:left;margin-right:8px;padding-top:4px;">
                                                  ${provinceSelectionUnit.praiseName}
                                          </div>
                                      </c:when>
                                      &lt;%&ndash; <c:otherwise>
                                      <font>未上传</font>
                                      </c:otherwise> &ndash;%&gt;
                                  </c:choose>
                                  </span>
                      <span id="uploadTr4" <c:if test="${provinceSelectionUnit.praiseName!='' && provinceSelectionUnit.praiseName!=null }">style='display:none'</c:if>>
                                      <c:if test="${sysInitConfig.code =='1' && provinceReportUser.electcityState !=1 && provinceReportUser.isDevInternalEva !=1}">
                                          <input id="praiseFile" type="file" name="praiseFile" title="请上传rar格式文件，大小不超过50M。" class="file-loading">
                                          <input type="hidden" class="form-control" name="praiseName" id="praise_Filename" value="${provinceSelectionUnit.praiseName}">
                                          <input type="hidden" class="form-control" name="praiseUrl" id="praise_Fileurl" value="${provinceSelectionUnit.praiseUrl}">
                                      </c:if>
                                  </span>
                  </td>
                  <td>
                      <c:if test="${sysInitConfig.code =='1' && provinceReportUser.electcityState !=1 && provinceReportUser.isDevInternalEva !=1}">
                          <input id="reButton4" <c:if test="${provinceSelectionUnit.praiseName=='' || provinceSelectionUnit.praiseName==null }">style='display:none'</c:if>
                                 class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                      </c:if>
                  </td>
              </tr>
              <tr>
                  <td></td>
                  <td style="text-align:left;">
                      <span style="color: deepskyblue">请上传rar格式文件，大小不超过50M。主要指因党建工作表现突出被各级组织部门表扬的情况，上传文件内容包含两部分：EXCEL文件（<a style="color: blue;" href="#" onclick="down()">模板下载</a>）和证明材料（PDF格式），证明材料名称应与Excel中材料名称保持一致。</span>
                  </td>
              </tr>--%>
                <%--
                  <tr>
                      <td>考核奖惩制度</td>
                      <td style="text-align: right;"><span id="wsc7"
                          style="font-size: 16px;"> <c:choose>
                                  <c:when
                                      test="${provinceSelectionUnit.assessmentsystemname!='' && provinceSelectionUnit.assessmentsystemname!=null }">
                                      <div
                                          style="float: left; margin-right: 8px; padding-top: 4px;">
                                          ${provinceSelectionUnit.assessmentsystemname}</div>
                                  </c:when>
                              </c:choose>
                          </span>
                          <span id="uploadTr7" <c:if test="${provinceSelectionUnit.assessmentsystemname!='' && provinceSelectionUnit.assessmentsystemname!=null }">style='display:none'</c:if>>
                              <c:if test="${sysInitConfig.code =='1' && provinceReportUser.electcityState !=1 && provinceReportUser.isDevInternalEva !=1}">
                                  <input id="assessmentfile" type="file" name="assessmentfile" title="提示：附件要求PDF或rar格式，大小不超过50M。" class="file-loading">
                                  <input type="hidden" class="form-control" name="assessmentsystemname" id="assessment_Filename" value="${provinceSelectionUnit.assessmentsystemname}">
                                  <input type="hidden" class="form-control" name="assessmentsystemurl" id="assessment_Fileurl" value="${provinceSelectionUnit.assessmentsystemurl}">
                              </c:if>
                          </span>
                      </td>
                      <td>
                          <c:if test="${sysInitConfig.code =='1' && provinceReportUser.electcityState !=1 && provinceReportUser.isDevInternalEva !=1}">
                              <input id="reButton7" <c:if test="${provinceSelectionUnit.assessmentsystemname=='' || provinceSelectionUnit.assessmentsystemname==null }">style='display:none'</c:if> class="btn btn-danger btn-xs" type="button" value="重新上传" />
                          </c:if>
                      </td>
                  </tr>
                  <tr>
                      <td>执法人员人身安全保障制度</td>
                      <td style="text-align: right;"><span id="wsc0"
                          style="font-size: 16px;"> <c:choose>
                                  <c:when test="${provinceSelectionUnit.lawenforcnetworkname!='' && provinceSelectionUnit.lawenforcnetworkname!=null }">
                                      <div
                                          style="float: left; margin-right: 8px; padding-top: 4px;">
                                          ${provinceSelectionUnit.lawenforcnetworkname}</div>
                                  </c:when>
                              </c:choose>
                          </span>
                          <span id="uploadTr0" <c:if test="${provinceSelectionUnit.lawenforcnetworkname!='' && provinceSelectionUnit.lawenforcnetworkname!=null }">style='display:none'</c:if>>
                              <c:if test="${sysInitConfig.code =='1' && provinceReportUser.electcityState !=1 && provinceReportUser.isDevInternalEva !=1}">
                                  <input id="lawenforcfile" type="file" name="lawenforcfile" title="提示：附件要求PDF或rar格式，大小不超过50M。" class="file-loading">
                                  <input type="hidden" class="form-control" name="lawenforcnetworkname" id="lawenforc_Filename" value="${provinceSelectionUnit.lawenforcnetworkname}">
                                  <input type="hidden" class="form-control" name="lawenforcnetworkurl" id="lawenforc_Fileurl" value="${provinceSelectionUnit.lawenforcnetworkurl}">
                              </c:if>
                          </span>
                      </td>
                      <td>
                          <c:if test="${sysInitConfig.code =='1' && provinceReportUser.electcityState !=1 && provinceReportUser.isDevInternalEva !=1}">
                              <input id="reButton0" <c:if test="${provinceSelectionUnit.lawenforcnetworkname=='' || provinceSelectionUnit.lawenforcnetworkname==null }">style='display:none'</c:if>
                              class="btn btn-danger btn-xs" type="button" value="重新上传" />
                          </c:if>
                      </td>
                  </tr>

                  <tr>
                      <td>尽职照单免责和失职照单问责制度</td>
                      <td style="text-align: right;"><span id="wsc9"
                          style="font-size: 16px;"> <c:choose>
                                  <c:when
                                      test="${provinceSelectionUnit.accountabilityname!='' && provinceSelectionUnit.accountabilityname!=null }">
                                      <div style="float: left; margin-right: 8px; padding-top: 4px;">
                                          ${provinceSelectionUnit.accountabilityname}</div>
                                  </c:when>
                              </c:choose>
                          </span>
                          <span id="uploadTr9"
                              <c:if test="${provinceSelectionUnit.accountabilityname!='' && provinceSelectionUnit.accountabilityname!=null }">style='display:none'</c:if>>
                              <c:if test="${sysInitConfig.code =='1' && provinceReportUser.electcityState !=1 && provinceReportUser.isDevInternalEva !=1}">
                                  <input id="accountabilityfile" type="file" name="accountabilityfile" title="提示：附件要求PDF或rar格式，大小不超过50M。" class="file-loading">
                                  <input type="hidden" class="form-control" name="accountabilityname" id="accountability_Filename" value="${provinceSelectionUnit.accountabilityname}">
                                  <input type="hidden" class="form-control" name="accountabilityurl" id="accountability_Fileurl" value="${provinceSelectionUnit.accountabilityurl}">
                              </c:if>
                          </span>
                      </td>
                      <td>
                          <c:if test="${sysInitConfig.code =='1' && provinceReportUser.electcityState !=1 && provinceReportUser.isDevInternalEva !=1}">
                              <input id="reButton9" <c:if test="${provinceSelectionUnit.accountabilityname=='' || provinceSelectionUnit.accountabilityname==null }">style='display:none'</c:if>
                              class="btn btn-danger btn-xs" type="button" value="重新上传" />
                          </c:if>
                      </td>
                  </tr>--%>

                <tr>
                    <td align="center">&nbsp;
                        <input type="hidden" name="id" id="id" value="${provinceSelectionUnit.id }"/>
                        <input type="hidden" name="subToken" value="${subToken}">
                    </td>
                    <td style="text-align:left;">
                        <c:if test="${sysInitConfig.code =='1' && provinceReportUser.electcityState !=1 && provinceReportUser.isDevInternalEva !=1}">
                            <a href="#">
                                <button type="button" id="xxcj_XXZC_Butt" class="btn btn-danger" name="signup" value="Sign up" style="font-size: 14px;width: 151px;border-radius: 3px;height:40px;margin-right: 50px">暂存</button>
                                <button type="button" id="xxcj_AJSL_Butt" class="btn btn-danger" name="signup" value="Sign up" style="font-size:14px;border-radius: 3px;width:151px;height:40px;background: #0093C3;border-color: #0093C3">提交</button>
                            </a>
                        </c:if>
                    </td>

                </tr>
                </tbody>
            </table>
    </form>
</div>
<script type="text/javascript">
    $("#isCaseTypeShow").hide()
    $("#isCaseTypeShow2").hide()
    function clearNoNum(obj) {
        obj.value = obj.value.replace(/[^\d.]/g, ""); //清除“数字”和“.”以外的字符
        obj.value = obj.value.replace(/^\./g,""); //验证第一个字符是数字而不是.
        obj.value = obj.value.replace(/\.{2,}/g, "."); //只保留第一个. 清除多余的
        obj.value = obj.value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
        obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3');//只能输入两个小数
        if (obj.value.indexOf(".") < 0 && obj.value != "") { //以上已经过滤，此处控制的是如果没有小数点，首位不能为类似于 01、02的金额
            obj.value = parseFloat(obj.value);

        }
    }



    $("#isPlan").change(function () {
        var option = $("#isPlan option:selected");
        //要触发的事件
        if (option.val() == "1") {
            debugger
            $(".isYear").show()
        } else {
            $(".isYear").hide()
        }
    })

    // 是否落实现场检查计划备案相关要求
    $("#isInspetion").change(function () {
        var option = $("#isInspetion option:selected");
        //要触发的事件
        if (option.val() == "1") {
            $(".isInspetionShow").show()
        } else {
            $(".isInspetionShow").hide()
        }
    })
    // 是否公布正面清单企业名单
    $("#isPositivelistManage").change(function () {
        var option = $("#isPositivelistManage option:selected");
        //要触发的事件
        if (option.val() == "1") {
            $(".isManage").show()
        } else {
            $(".isManage").hide()
        }
    })

    // *试点工作开展情况说明
    $("#isAssessment").change(function () {
        var option = $("#isAssessment option:selected");
        //要触发的事件
        if (option.val() == "1") {
            $(".isAssessmentShow").show()
        } else {
            $(".isAssessmentShow").hide()
        }
    })

    isAssessment


    // 是否通过座谈、宣讲、培训对正面清单企业开展帮扶指导、提醒预警
    $("#isPositivelistWarn").change(function () {
        var option = $("#isPositivelistWarn option:selected");
        //要触发的事件
        if (option.val() == "1") {
            $(".isWarn").show()
        } else {
            $(".isWarn").hide()
        }
    })

    // 双随机、一公开”制度落实情况
    $("#isRandomFive").change(function () {
        var option = $("#isRandomFive option:selected");
        //要触发的事件
        if (option.val() == "1") {
            $(".isRandomShow").show()
        } else {
            $(".isRandomShow").hide()
        }
    })

    // 推行非现场监管方式情况
    $("#isOffsiteFive").change(function () {
        var option = $("#isOffsiteFive option:selected");
        //要触发的事件
        if (option.val() == "1") {
            $(".isOffsiteShow").show()
        } else {
            $(".isOffsiteShow").hide()
        }
    })

    // 执法监测工作机制落实情况
    $("#isLawplan").change(function () {
        var option = $("#isLawplan option:selected");
        //要触发的事件
        if (option.val() == "1") {
            $(".isLawShow").show()
        } else {
            $(".isLawShow").hide()
        }
    })
    // 第三方辅助执法
    $("#isFulawFive").change(function () {
        var option = $("#isFulawFive option:selected");
        //要触发的事件
        if (option.val() == "1") {
            $(".isFulawShow").show()
        } else {
            $(".isFulawShow").hide()
        }
    })

    // 执法普法制度落实情况
    $("#isLawGuide").change(function () {
        var option = $("#isLawGuide option:selected");
        //要触发的事件
        if (option.val() == "1") {
            $(".isLawGuideShow").show()
        } else {
            $(".isLawGuideShow").hide()
        }
    })

    // 是否出台履职评估办法
    $("#isDwPeoples").change(function () {
        var option = $("#isDwPeoples option:selected");
        //要触发的事件
        if (option.val() == "1") {
            $(".isDwPeopleShows").show()
        } else {
            $(".isDwPeopleShows").hide()
        }
    })

    // 是否出台履职评估办法
    $("#isDwPeople").change(function () {
        var option = $("#isDwPeople option:selected");
        //要触发的事件
        if (option.val() == "1") {
            $(".isDwPeopleShow").show()
        } else {
            $(".isDwPeopleShow").hide()
        }
    })

    // 是否组织全市开展队列训练
    $("#isDwTeam").change(function () {
        var option = $("#isDwTeam option:selected");
        //要触发的事件
        if (option.val() == "1") {
            $(".isDwTeamShow").show()
        } else {
            $(".isDwTeamShow").hide()
        }
    })


    // 是否组织开展全市技能竞赛比武
    $("#isDwSkill").change(function () {
        var option = $("#isDwSkill option:selected");
        //要触发的事件
        if (option.val() == "1") {
            $(".isDwSkillShow").show()
        } else {
            $(".isDwSkillShow").hide()
        }
    })

    // 是否组织开展全市技能竞赛比武
    $("#isLicenseLaw").change(function () {
        var option = $("#isLicenseLaw option:selected");
        //要触发的事件
        if (option.val() == "1") {
            $(".inlicensesShow").show()
        } else {
            $(".inlicensesShow").hide()
        }
    })


    // 是否组织开展全市技能竞赛比武
    $("#isLicenseList").change(function () {
        var option = $("#isLicenseList option:selected");
        //要触发的事件
        if (option.val() == "1") {
            $(".inlicensesShow").show()
        } else {
            $(".inlicensesShow").hide()
        }
    })

    $("#caseEndType").change(function () {
        var option = $("#caseEndType option:selected");
        //要触发的事件
        if (option.val() == "0") {
            $("#isCaseTypeShow").show()
        } else if (option.val() == "2") {
            $("#isCaseTypeShow").hide()
            $("#isCaseTypeShow2").show()
        }
    })






    //表单校验
    $(document).ready(function() {
        var  areatypePro= $("#areatypePro").val();
        if(areatypePro==2){
            $('#ajslForm').formValidation({
                message: 'This value is not valid',
                icon: {
                    valid: 'glyphicon glyphicon-ok',
                    invalid: 'glyphicon glyphicon-remove',
                    validating: 'glyphicon glyphicon-refresh'
                },
                autoFocus: true,
                fields: {
                    "companyname" : {
                        validators : {
                            notEmpty : {
                                message : '请填写机构名称（全称）'
                            }
                        }
                    },
                    "typicalCaseNum": {
                        verbose: false,
                        validators : {
                            notEmpty : {
                                message : '请填写市级典型案例公开数量'
                            },
                            regexp : {
                                regexp : /^[0-9]+$/,
                                message : '该项只能填写整数'
                            },
                            greaterThan : {
                                value : 0,
                                message : ' '
                            },
                            lessThan : {
                                value : 10000,
                                message : '该项为小于等于10000的整数'
                            }
                        }
                    },
                    "provinceAcceptNum": {
                        verbose: false,
                        validators: {
                            notEmpty: {
                                message: '请填写被省级采纳数量'
                            },
                            regexp : {
                                regexp : /^[0-9]+$/,
                                message : '该项只能填写整数'
                            },
                            greaterThan : {
                                value : 0,
                                message : ' '
                            },
                            lessThan : {
                                value : 10000,
                                message : '该项为小于等于10000的整数'
                            }
                        }
                    },
                    "xwbzSpecialReportNum": {
                        verbose: false,
                        validators: {
                            notEmpty: {
                                message: '请填写向我部作专题报告次数'
                            },
                            regexp : {
                                regexp : /^[0-9]+$/,
                                message : '该项只能填写整数'
                            },
                            greaterThan : {
                                value : 0,
                                message : ' '
                            },
                            lessThan : {
                                value : 10000,
                                message : '该项为小于等于10000的整数'
                            }
                        }
                    },
                    "xshPublicNum": {
                        verbose: false,
                        validators: {
                            notEmpty: {
                                message: '请填写向社会公开数量'
                            },
                            regexp : {
                                regexp : /^[0-9]+$/,
                                message : '该项只能填写整数'
                            },
                            greaterThan : {
                                value : 0,
                                message : ' '
                            },
                            lessThan : {
                                value : 10000,
                                message : '该项为小于等于10000的整数'
                            }
                        }
                    },
                    "totalNum": {
                        verbose: false,
                        validators: {
                            notEmpty: {
                                message: '请填写制度创新与执法服务总数'
                            },
                            regexp : {
                                regexp : /^[0-9]+$/,
                                message : '该项只能填写整数'
                            },
                            greaterThan : {
                                value : 0,
                                message : ' '
                            },
                            lessThan : {
                                value : 10000,
                                message : '该项为小于等于10000的整数'
                            }
                        }
                    },
                    "findQuestionNum": {
                        verbose: false,
                        validators: {
                            notEmpty: {
                                message: '请填写发现问题数量'
                            },
                            regexp : {
                                regexp : /^[0-9]+$/,
                                message : '该项只能填写整数'
                            },
                            greaterThan : {
                                value : 0,
                                message : ' '
                            },
                            lessThan : {
                                value : 10000,
                                message : '该项为小于等于10000的整数'
                            }
                        }
                    },
                    "swxfwhjwffzajNum": {
                        verbose: false,
                        validators: {
                            notEmpty: {
                                message: '请填写查处涉废矿物油等涉危险废物环境违法犯罪案件数量'
                            },
                            regexp : {
                                regexp : /^[0-9]+$/,
                                message : '该项只能填写整数'
                            },
                            greaterThan : {
                                value : 0,
                                message : ' '
                            },
                            lessThan : {
                                value : 10000,
                                message : '该项为小于等于10000的整数'
                            }
                        }
                    },
                    "yjgaswxfwdhjajNum": {
                        verbose: false,
                        validators: {
                            notEmpty: {
                                message: '请填写移交公安涉危险废物等环境案件数量'
                            },
                            regexp : {
                                regexp : /^[0-9]+$/,
                                message : '该项只能填写整数'
                            },
                            greaterThan : {
                                value : 0,
                                message : ' '
                            },
                            lessThan : {
                                value : 10000,
                                message : '该项为小于等于10000的整数'
                            }
                        }
                    },
                    "publicSecurityCaseNum": {
                        verbose: false,
                        validators: {
                            notEmpty: {
                                message: '请填写公安机关立案数量'
                            },
                            regexp : {
                                regexp : /^[0-9]+$/,
                                message : '该项只能填写整数'
                            },
                            greaterThan : {
                                value : 0,
                                message : ' '
                            },
                            lessThan : {
                                value : 10000,
                                message : '该项为小于等于10000的整数'
                            }
                        }
                    },
                    "ccnxzjwffzCaseSum": {
                        verbose: false,
                        validators: {
                            notEmpty: {
                                message: '请填写查处弄虚作假违法犯罪案件数量'
                            },
                            regexp : {
                                regexp : /^[0-9]+$/,
                                message : '该项只能填写整数'
                            },
                            greaterThan : {
                                value : 0,
                                message : ' '
                            },
                            lessThan : {
                                value : 10000,
                                message : '该项为小于等于10000的整数'
                            }
                        }
                    },
                    "publicSecurityCaseRate": {
                        verbose: false,
                        validators: {
                            notEmpty: {
                                message: '请填写公安立案率'
                            },
                            greaterThan : {
                                value : 0,
                                message : ' '
                            },
                            lessThan : {
                                value : 100,
                                message : '该项为小于等于100的数'
                            }
                        }
                    }
                }
            });
        }else if(areatypePro==3){
            $('#ajslForm').formValidation({
                message: 'This value is not valid',
                icon: {
                    valid: 'glyphicon glyphicon-ok',
                    invalid: 'glyphicon glyphicon-remove',
                    validating: 'glyphicon glyphicon-refresh'
                },
                autoFocus: true,
                fields: {
                    "companyname" : {
                        validators : {
                            notEmpty : {
                                message : '请填写机构名称（全称）'
                            }
                        }
                    },
                    "typicalCaseNum": {
                        verbose: false,
                        validators : {
                            /*notEmpty : {
                                message : '请填写市级典型案例公开数量'
                            },*/
                            regexp : {
                                regexp : /^[0-9]+$/,
                                message : '该项只能填写整数'
                            },
                            greaterThan : {
                                value : 0,
                                message : ' '
                            },
                            lessThan : {
                                value : 10000,
                                message : '该项为小于等于10000的整数'
                            }
                        }
                    },
                    "provinceAcceptNum": {
                        verbose: false,
                        validators: {
                            notEmpty: {
                                message: '请填写被省级采纳数量'
                            },
                            regexp : {
                                regexp : /^[0-9]+$/,
                                message : '该项只能填写整数'
                            },
                            greaterThan : {
                                value : 0,
                                message : ' '
                            },
                            lessThan : {
                                value : 10000,
                                message : '该项为小于等于10000的整数'
                            }
                        }
                    },
                    "xwbzSpecialReportNum": {
                        verbose: false,
                        validators: {
                            notEmpty: {
                                message: '请填写向我部作专题报告次数'
                            },
                            regexp : {
                                regexp : /^[0-9]+$/,
                                message : '该项只能填写整数'
                            },
                            greaterThan : {
                                value : 0,
                                message : ' '
                            },
                            lessThan : {
                                value : 10000,
                                message : '该项为小于等于10000的整数'
                            }
                        }
                    },
                    "xshPublicNum": {
                        verbose: false,
                        validators: {
                            notEmpty: {
                                message: '请填写向社会公开数量'
                            },
                            regexp : {
                                regexp : /^[0-9]+$/,
                                message : '该项只能填写整数'
                            },
                            greaterThan : {
                                value : 0,
                                message : ' '
                            },
                            lessThan : {
                                value : 10000,
                                message : '该项为小于等于10000的整数'
                            }
                        }
                    },
                    "totalNum": {
                        verbose: false,
                        validators: {
                            notEmpty: {
                                message: '请填写制度创新与执法服务总数'
                            },
                            regexp : {
                                regexp : /^[0-9]+$/,
                                message : '该项只能填写整数'
                            },
                            greaterThan : {
                                value : 0,
                                message : ' '
                            },
                            lessThan : {
                                value : 10000,
                                message : '该项为小于等于10000的整数'
                            }
                        }
                    },
                    "findQuestionNum": {
                        verbose: false,
                        validators: {
                            notEmpty: {
                                message: '请填写发现问题数量'
                            },
                            regexp : {
                                regexp : /^[0-9]+$/,
                                message : '该项只能填写整数'
                            },
                            greaterThan : {
                                value : 0,
                                message : ' '
                            },
                            lessThan : {
                                value : 10000,
                                message : '该项为小于等于10000的整数'
                            }
                        }
                    },
                    "swxfwhjwffzajNum": {
                        verbose: false,
                        validators: {
                            notEmpty: {
                                message: '请填写查处涉废矿物油等涉危险废物环境违法犯罪案件数量'
                            },
                            regexp : {
                                regexp : /^[0-9]+$/,
                                message : '该项只能填写整数'
                            },
                            greaterThan : {
                                value : 0,
                                message : ' '
                            },
                            lessThan : {
                                value : 10000,
                                message : '该项为小于等于10000的整数'
                            }
                        }
                    },
                    "yjgaswxfwdhjajNum": {
                        verbose: false,
                        validators: {
                            notEmpty: {
                                message: '请填写移交公安涉危险废物等环境案件数量'
                            },
                            regexp : {
                                regexp : /^[0-9]+$/,
                                message : '该项只能填写整数'
                            },
                            greaterThan : {
                                value : 0,
                                message : ' '
                            },
                            lessThan : {
                                value : 10000,
                                message : '该项为小于等于10000的整数'
                            }
                        }
                    },
                    "publicSecurityCaseNum": {
                        verbose: false,
                        validators: {
                            notEmpty: {
                                message: '请填写公安机关立案数量'
                            },
                            regexp : {
                                regexp : /^[0-9]+$/,
                                message : '该项只能填写整数'
                            },
                            greaterThan : {
                                value : 0,
                                message : ' '
                            },
                            lessThan : {
                                value : 10000,
                                message : '该项为小于等于10000的整数'
                            }
                        }
                    },
                    "ccnxzjwffzCaseSum": {
                        verbose: false,
                        validators: {
                            notEmpty: {
                                message: '请填写查处弄虚作假违法犯罪案件数量'
                            },
                            regexp : {
                                regexp : /^[0-9]+$/,
                                message : '该项只能填写整数'
                            },
                            greaterThan : {
                                value : 0,
                                message : ' '
                            },
                            lessThan : {
                                value : 10000,
                                message : '该项为小于等于10000的整数'
                            }
                        }
                    },
                    "publicSecurityCaseRate": {
                        verbose: false,
                        validators: {
                            notEmpty: {
                                message: '请填写公安立案率'
                            },
                            greaterThan : {
                                value : 0,
                                message : ' '
                            },
                            lessThan : {
                                value : 100,
                                message : '该项为小于等于100的数'
                            }
                        }
                    }
                }
            });
        }
    });

    /*//关联验证
    $("#publicityway").on('change', function(){
        if($(this).val() == '1'){
            $('#ajslForm').formValidation('enableFieldValidators','website',true);
            $('#ajslForm').formValidation('enableFieldValidators','publicitywaydescribe',false);
            $('#publicitywaydescribe').prop('disabled', true);
            $('#website').prop('disabled', false);
        }else if($(this).val() == '2'){
            $('#ajslForm').formValidation('enableFieldValidators','website',false);
            $('#ajslForm').formValidation('enableFieldValidators','publicitywaydescribe',true);
            $('#publicitywaydescribe').prop('disabled', false);
            $('#website').prop('disabled', true);
        }else if($(this).val() == '0'){
            $('#ajslForm').formValidation('enableFieldValidators','website',false);
            $('#ajslForm').formValidation('enableFieldValidators','publicitywaydescribe',false);
            $('#tasklistoriginalno1').hide();
            $('#tasklistoriginalno2').hide();
        }
    });*/



    //表单提交
    $(document).ready(function(){
        $("#xxcj_AJSL_Butt").click(function(){
            var validate = false;
            /*if($("#areatypePro").val()==3){
                $('#ajslForm').formValidation('enableFieldValidators','publicityway',false);
            }*/
            $("#ajslForm").data('formValidation').validate();
            validate = $("#ajslForm").data('formValidation').isValid();
            if(validate){
                var fileurl2=$("#typicalCaseAccept_Fileurl").val();
                var filename2=$("#typicalCaseAccept_Filename").val();
                var fileurl3=$("#zdcxyzfrwzccl_Fileurl").val();
                var filename3=$("#zdcxyzfrwzccl_Filename").val();
                var fileurl4=$("#findQuestion_Fileurl").val();
                var filename4=$("#findQuestion_Filename").val();
                var fileurl5=$("#lawgzmyd_Fileurl").val();
                var filename5=$("#lawgzmyd_Filename").val();
                var typicalCaseNum=$('#typicalCaseNum').val();
                var areatypePro=$('#areatypePro').val();
                var filename6=$('#citySpecialActionShow_Filename').val();
                var fileurl6=$("#citySpecialActionShow_Fileurl").val();
                var filename7=$('#ccnxzjwffzCase_Filename').val();
                var fileurl7=$("#ccnxzjwffzCase_Fileurl").val();

                if(fileurl2!=""&&filename2!=""){

                    if(fileurl3!=""&&filename3!=""){

                        if(fileurl4 !="" && filename4 != ""){

                            if (fileurl5 !="" && filename5 != "") {

                                if (fileurl6 !="" && filename6 != "") {

                                    if (fileurl7 !="" && filename7 != "") {

                                        if ((areatypePro =="2" && typicalCaseNum != "")||(areatypePro =="3")) {
                                            var options = {
                                                url:WEBPATH+'/xxcj/saveProvinceBase.do',
                                                type:'post',
                                                success:function(data){
                                                    if(data.result=="error"){
                                                        swal({title:"保存失败!",text:data.message,type:"error",confirmButtonColor: "#d9534f"});
                                                        return false;
                                                    }else if(data.result=="success"){
                                                        swal({
                                                            title: "保存成功!",
                                                            type: "success",
                                                            closeOnConfirm: true,
                                                            confirmButtonText: "确定",
                                                            confirmButtonColor: "#d9534f"
                                                        }, function() {
                                                            business.addMainContentParserHtml('xxcj/xxcjcityGroupBase.do','');

                                                        });
                                                        return false;
                                                    }else{
                                                        swal({
                                                            title: "请勿重复提交表单！!",
                                                            type: "warning",
                                                            //text:"请勿重复提交表单！",
                                                            closeOnConfirm: true,
                                                            confirmButtonText: "确定",
                                                            confirmButtonColor: "#d9534f"
                                                        }, function() {
                                                            business.addMainContentParserHtml('xxcj/xxcjcityGroupBase.do','');

                                                        });
                                                    }
                                                },
                                                error:function(){
                                                    swal({title:"服务异常,保存失败!",text:"",type:"error",confirmButtonColor: "#d9534f"});
                                                }
                                            };
                                            $("#ajslForm").ajaxSubmit(options);
                                        }else {
                                            swal({title:"表单未填写",text:"请填写市级典型案例公开数量!",type:"error",confirmButtonColor: "#d9534f"});
                                            return  false ;
                                        }
                                    }else {
                                        swal({title:"未上传附件",text:"请上传打击弄虚作假违法犯罪案件支撑材料!",type:"error",confirmButtonColor: "#d9534f"});
                                        return  false ;
                                    }
                                }else {
                                    swal({title:"未上传附件",text:"请上传专项行动表现支撑材料!",type:"error",confirmButtonColor: "#d9534f"});
                                    return  false ;
                                }
                            }else {
                                swal({title:"未上传附件",text:"请上传执法公众满意度支撑材料!",type:"error",confirmButtonColor: "#d9534f"});
                                return  false ;
                            }
                        }else {
                            swal({title:"未上传附件!",text:"请上传发现问题能力支撑材料!",type:"error",confirmButtonColor: "#d9534f"});
                            return  false ;
                        }
                    }else {
                        swal({title:"未上传附件",text:"请上传制度创新与执法任务支撑材料!",type:"error",confirmButtonColor: "#d9534f"});
                        return  false ;
                    }
                }else{
                    swal({title:"未上传附件",text:"请上传典型案例采纳情况支撑材料!",type:"error",confirmButtonColor: "#d9534f"});
                    return  false ;
                }
            }else if(validate==null){
                //表单未填写
                $("#ajslForm").data('formValidation').validate();

            }
        });
    });



    $(document).ready(function() {
        $(".topnav").accordion({
            accordion:false,
            speed: 500,
            closedSign: '[+]',
            openedSign: '[-]'
        });
    });
</script>
<script language="javascript">
    function listchange(value){
        $("#isPraiseVal").val(value);


    }
    /*$(document).ready(function() {
        var value = $("#xxxx").val();
        if(value == 1){
            document.getElementById("tasklistoriginalno1").style.display= "";
            document.getElementById("tasklistoriginalno2").style.display= "none";
        }
        if(value == 2){
            document.getElementById("tasklistoriginalno1").style.display= "none";
            document.getElementById("tasklistoriginalno2").style.display= "";
        }
    })*/

    function down(val) {
        var name = "";
        if (val==1) {
            name = "市县级日常监督执法工作";
        }else if (val==2){
            name = "市县级专项行动表现";
        }
        var path = "${webpath}/xxcj/xcclExcel.do";
        window.location.href = path + "?name="+name;
    }

    function downloadFile(url, fileName) {
        if (url != null && url != "") {
            console.log(url)
            location.href = "${webpath}/filedownload.do?url=" + url + "&fileName=" + fileName;
        } else {
            swal({title: "提示", text: "您下载的附件不存在！", type: "info"});
        }
    };
</script>

</body>
</html>
