<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
    <!-- <style type="text/css">
    div.input-group-btn div.btn.btn-primary.btn-file {
        background-color:#d9534f;
    }
    </style> -->
    <style>

        .is-required {
            content: '* ';
            color: red;
        }
    </style>
    <script type="text/javascript">

        function butChange(index) {

            var butVal = $("#reButton" + index).attr("value");
            var butStyle = $("#reButton" + index).attr("style");
            if (butStyle != "" && typeof(butStyle) != "undefined") {
                $("#reButton" + index).attr("style", "");
                $("#uploadTr" + index).attr("style", "display:none");
                $("#wsc" + index).attr("style", "");
            } else {
                if (butVal == "重新上传") {
                    $("#reButton" + index).attr("value", "返回");
                    $("#reButton" + index).attr('class',"back");
                    $("#uploadTr" + index).attr("style", "");
                    $("#wsc" + index).attr("style", "display:none");
                } else if (butVal == "返回") {
                    $("#reButton" + index).attr("value", "重新上传");
                    $("#reButton" + index).removeClass("back");
                    $("#uploadTr" + index).attr("style", "display:none");
                    $("#wsc" + index).attr("style", "");
                } else if (butVal == "删除") {
                    $("#reButton" + index).attr("value", "重新上传");
                    $("#uploadTr" + index).attr("style", "display:none");
                    $("#wsc" + index).attr("style", "");
                }
            }
        }


        //省级大练兵组织情况 Excel文件
        $(document).ready(function () {
            $("#jiliFile").fileinput({
                uploadUrl: WEBPATH + '/pdffileupload.do', // you must set a valid URL here else you will get an error
                allowedFileExtensions: ['xls', 'xlsx'],
                language: 'zh',
                browseClass: 'btn btn-danger',//按钮样式
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize: 1,
                maxFileSize: 53500,
                enctype: 'multipart/form-data',
                dropZoneTitle: "可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany: '选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading: '文件预览',
                msgInvalidFileExtension: '不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected: '请选择文件',
                msgValidationError: '文件类型不正确或文件过大',
                initialPreviewFileType: 'els',
                browseLabel: "选择文件",
                removeLabel: '删除',
                removeTitle: '删除文件',
                uploadLabel: '上传',
                uploadTitle: '上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview: false,
                autoReplace: true,
                slugCallback: function (filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function (event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true); //信息保存按钮 隐藏

            }).on('fileuploaded', function (event, data, previewId, index) {//文件上传完成执行操作
                console.log("-------", data.response.url, data.response.fileRealName)
                $("#jili_url").val(data.response.url);
                $("#jili_urlname").val(data.response.fileRealName);
                // $("#wsc1").text(data.response.fileRealName);
                $("#wsc1").addClass("wscbj1");
                $("#wsc1").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled', false);

                butChange('1');
            })

            $("#reButton1").click(function () {
                butChange('1');
                $("#wsc1").addClass("wscbj1");
            });


            //知识竞赛支撑材料
            $("#jwInform").fileinput({
                uploadUrl: WEBPATH + '/pdffileupload.do', // you must set a valid URL here else you will get an error
                allowedFileExtensions: ['rar'],
                language: 'zh',
                browseClass: 'btn btn-danger',//按钮样式
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize: 1,
                maxFileSize: 53500,
                enctype: 'multipart/form-data',
                dropZoneTitle: "可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany: '选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading: '文件预览',
                msgInvalidFileExtension: '不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected: '请选择文件',
                msgValidationError: '文件类型不正确或文件过大',
                initialPreviewFileType: 'rar',
                browseLabel: "选择文件",
                removeLabel: '删除',
                removeTitle: '删除文件',
                uploadLabel: '上传',
                uploadTitle: '上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview: false,
                autoReplace: true,
                slugCallback: function (filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function (event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function (event, data, previewId, index) {//文件上传完成执行操作

                $("#jw_inform_url").val(data.response.url);
                $("#jw_inform_urlname").val(data.response.fileRealName);
                // $("#wsc2").text(data.response.fileRealName);
                $("#wsc2").addClass("wscbj");
                $("#wsc2").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled', false);

                butChange('2');
            })

            $("#reButton2").click(function () {
                butChange('2');
                $("#wsc2").addClass("wscbj");
            });

            //开展大练兵宣传支撑材料
            $("#jwWebFile").fileinput({
                uploadUrl: WEBPATH + '/pdffileupload.do', // you must set a valid URL here else you will get an error
                allowedFileExtensions: ['rar'],
                language: 'zh',
                browseClass: 'btn btn-danger',//按钮样式
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize: 1,
                maxFileSize: 53500,
                enctype: 'multipart/form-data',
                dropZoneTitle: "可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany: '选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading: '文件预览',
                msgInvalidFileExtension: '不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected: '请选择文件',
                msgValidationError: '文件类型不正确或文件过大',
                initialPreviewFileType: 'rar',
                browseLabel: "选择文件",
                removeLabel: '删除',
                removeTitle: '删除文件',
                uploadLabel: '上传',
                uploadTitle: '上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview: false,
                autoReplace: true,
                slugCallback: function (filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function (event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function (event, data, previewId, index) {//文件上传完成执行操作

                    $("#jw_web_url").val(data.response.url);
                    $("#jw_web_urlname").val(data.response.fileRealName);
                    // $("#wsc3").text(data.response.fileRealName);
                    $("#wsc3").addClass("wscbj");
                    $("#wsc3").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                    $("#xxcj_AJSL_Butt").prop('disabled', false);
                    butChange('3');
                }
            )

            $("#reButton3").click(function () {
                butChange('3');
                $("#wsc3").addClass("wscbj");

            });


            //评查方案
            $("#pingchaFile").fileinput({
                uploadUrl: WEBPATH + '/pdffileupload.do', // you must set a valid URL here else you will get an error
                allowedFileExtensions: ['rar'],
                language: 'zh',
                browseClass: 'btn btn-danger',//按钮样式
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize: 1,
                maxFileSize: 53500,
                enctype: 'multipart/form-data',
                dropZoneTitle: "可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany: '选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading: '文件预览',
                msgInvalidFileExtension: '不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected: '请选择文件',
                msgValidationError: '文件类型不正确或文件过大',
                initialPreviewFileType: 'rar',
                browseLabel: "选择文件",
                removeLabel: '删除',
                removeTitle: '删除文件',
                uploadLabel: '上传',
                uploadTitle: '上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview: false,
                autoReplace: true,
                slugCallback: function (filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function (event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function (event, data, previewId, index) {//文件上传完成执行操作

                $("#pingcha_url").val(data.response.url);
                $("#pingcha_url").val(data.response.fileRealName);
                // $("#wsc4").text(data.response.fileRealName);
                $("#wsc1").addClass("wscbj");
                $("#wsc4").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled', false);

                butChange('4');
            })
            $("#reButton4").click(function () {
                butChange('4');
                $("#wsc4").addClass("wscbj")
            });

            //评查工作总结或通报
            $("#worksummaryFile").fileinput({
                uploadUrl: WEBPATH + '/pdffileupload.do', // you must set a valid URL here else you will get an error
                allowedFileExtensions: ['rar'],
                language: 'zh',
                browseClass: 'btn btn-danger',//按钮样式
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize: 1,
                maxFileSize: 53500,
                enctype: 'multipart/form-data',
                dropZoneTitle: "可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany: '选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading: '文件预览',
                msgInvalidFileExtension: '不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected: '请选择文件',
                msgValidationError: '文件类型不正确或文件过大',
                initialPreviewFileType: 'rar',
                browseLabel: "选择文件",
                removeLabel: '删除',
                removeTitle: '删除文件',
                uploadLabel: '上传',
                uploadTitle: '上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview: false,
                autoReplace: true,
                slugCallback: function (filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function (event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function (event, data, previewId, index) {//文件上传完成执行操作

                $("#worksummary_url").val(data.response.url);
                $("#worksummary_urlname").val(data.response.fileRealName);
                // $("#wsc5").text(data.response.fileRealName);
                $("#wsc5").addClass("wscbj")
                $("#wsc5").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled', false);

                butChange('5');
            })

            $("#reButton5").click(function () {
                butChange('5');
                $("#wsc5").addClass("wscbj")
            });

            //案件办理情况说明
            $("#carryOutFile").fileinput({
                uploadUrl: WEBPATH + '/pdffileupload.do', // you must set a valid URL here else you will get an error
                allowedFileExtensions: ['rar'],
                language: 'zh',
                browseClass: 'btn btn-danger',//按钮样式
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize: 1,
                maxFileSize: 53500,
                enctype: 'multipart/form-data',
                dropZoneTitle: "可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany: '选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading: '文件预览',
                msgInvalidFileExtension: '不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected: '请选择文件',
                msgValidationError: '文件类型不正确或文件过大',
                initialPreviewFileType: 'rar',
                browseLabel: "选择文件",
                removeLabel: '删除',
                removeTitle: '删除文件',
                uploadLabel: '上传',
                uploadTitle: '上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview: false,
                autoReplace: true,
                slugCallback: function (filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function (event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function (event, data, previewId, index) {//文件上传完成执行操作

                $("#case_end_url").val(data.response.url);
                $("#case_end_urlname").val(data.response.fileRealName);
                // $("#wsc7").text(data.response.fileRealName);
                $("#wsc7").addClass("wscbj")
                $("#wsc7").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled', false);

                butChange('7');
            })

            $("#reButton7").click(function () {
                butChange('7');
                $("#wsc7").addClass("wscbj")
            });


            //查办大案要案支撑材料
            $("#caseEndFile").fileinput({
                uploadUrl: WEBPATH + '/pdffileupload.do', // you must set a valid URL here else you will get an error
                allowedFileExtensions: ['rar'],
                language: 'zh',
                browseClass: 'btn btn-danger',//按钮样式
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize: 1,
                maxFileSize: 53500,
                enctype: 'multipart/form-data',
                dropZoneTitle: "可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany: '选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading: '文件预览',
                msgInvalidFileExtension: '不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected: '请选择文件',
                msgValidationError: '文件类型不正确或文件过大',
                initialPreviewFileType: 'rar',
                browseLabel: "选择文件",
                removeLabel: '删除',
                removeTitle: '删除文件',
                uploadLabel: '上传',
                uploadTitle: '上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview: false,
                autoReplace: true,
                slugCallback: function (filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function (event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function (event, data, previewId, index) {//文件上传完成执行操作

                $("#case_endsup_url").val(data.response.url);
                $("#case_endsup_urlname").val(data.response.fileRealName);
                // $("#wsc8").text(data.response.fileRealName);
                $("#wsc8").addClass("wscbj")
                $("#wsc8").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled', false);

                butChange('8');
            })

            $("#reButton8").click(function () {
                butChange('8');
                $("#wsc8").addClass("wscbj")
            });


            //2021年是否出台
            $("#dwReportFile").fileinput({
                uploadUrl: WEBPATH + '/pdffileupload.do', // you must set a valid URL here else you will get an error
                allowedFileExtensions: ['rar'],
                language: 'zh',
                browseClass: 'btn btn-danger',//按钮样式
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize: 1,
                maxFileSize: 53500,
                enctype: 'multipart/form-data',
                dropZoneTitle: "可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany: '选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading: '文件预览',
                msgInvalidFileExtension: '不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected: '请选择文件',
                msgValidationError: '文件类型不正确或文件过大',
                initialPreviewFileType: 'rar',
                browseLabel: "选择文件",
                removeLabel: '删除',
                removeTitle: '删除文件',
                uploadLabel: '上传',
                uploadTitle: '上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview: false,
                autoReplace: true,
                slugCallback: function (filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function (event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function (event, data, previewId, index) {//文件上传完成执行操作

                $("#specificEnactFile_Fileurl").val(data.response.url);
                $("#specificEnactFile_Filename").val(data.response.fileRealName);
                // $("#wsc9").text(data.response.fileRealName);
                $("#wsc9").addClass("wscbj")
                $("#wsc9").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled', false);

                butChange('9');
            })

            $("#reButton9").click(function () {
                butChange('9');
                $("#wsc9").addClass("wscbj")
            });

            // 建设标准文件
            $("#jsbuildFile").fileinput({
                uploadUrl: WEBPATH + '/pdffileupload.do', // you must set a valid URL here else you will get an error
                allowedFileExtensions: ['rar'],
                language: 'zh',
                browseClass: 'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize: 1,
                maxFileSize: 53500,
                enctype: 'multipart/form-data',
                dropZoneTitle: "可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany: '选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading: '文件预览',
                msgInvalidFileExtension: '不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected: '请选择文件',
                msgValidationError: '文件类型不正确或文件过大',
                initialPreviewFileType: 'rar',
                browseLabel: "选择文件",
                removeLabel: '删除',
                removeTitle: '删除文件',
                uploadLabel: '上传',
                uploadTitle: '上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview: false,
                autoReplace: true,
                slugCallback: function (filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function (event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function (event, data, previewId, index) {//文件上传完成执行操作

                $("#specificConditionFile_Fileurl").val(data.response.url);
                $("#specificConditionFile_Filename").val(data.response.fileRealName);
                // $("#wsc10").text(data.response.fileRealName);
                $("#wsc10").addClass("wscbj")
                $("#wsc10").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled', false);

                butChange('10');
            })
            $("#reButton10").click(function () {
                butChange('10');
                $("#wsc10").addClass("wscbj")
            });

            //稽查计划
            $("#lawCheckplanFile").fileinput({
                uploadUrl: WEBPATH + '/pdffileupload.do', // you must set a valid URL here else you will get an error
                allowedFileExtensions: ['rar'],
                language: 'zh',
                browseClass: 'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize: 1,
                maxFileSize: 53500,
                enctype: 'multipart/form-data',
                dropZoneTitle: "可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany: '选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading: '文件预览',
                msgInvalidFileExtension: '不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected: '请选择文件',
                msgValidationError: '文件类型不正确或文件过大',
                initialPreviewFileType: 'rar',
                browseLabel: "选择文件",
                removeLabel: '删除',
                removeTitle: '删除文件',
                uploadLabel: '上传',
                uploadTitle: '上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview: false,
                autoReplace: true,
                slugCallback: function (filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filebatchselected', function (event, files) {//文件自动上传
                //console.log(event);
                //console.log(files);
                //$(this).fileinput("upload");
            }).on('filepreupload', function (event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function (event, data, previewId, index) {//文件上传完成执行操作

                $("#rsAnQuanFile_Fileurl").val(data.response.url);
                $("#rsAnQuanFile_Filename").val(data.response.fileRealName);
                // $("#wsc11").text(data.response.fileRealName);
                $("#wsc11").addClass("wscbj")
                $("#wsc11").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled', false);

                butChange('11');
            })
            $("#reButton11").click(function () {
                butChange('11');
                $("#wsc11").addClass("wscbj")
            });

            //稽查工作报告
            $("#caseinfoFile").fileinput({
                uploadUrl: WEBPATH + '/pdffileupload.do', // you must set a valid URL here else you will get an error
                allowedFileExtensions: ['xls', 'xlsx'],
                language: 'zh',
                browseClass: 'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize: 1,
                maxFileSize: 53500,
                enctype: 'multipart/form-data',
                dropZoneTitle: "可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany: '选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading: '文件预览',
                msgInvalidFileExtension: '不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected: '请选择文件',
                msgValidationError: '文件类型不正确或文件过大',
                initialPreviewFileType: 'xls',
                browseLabel: "选择文件",
                removeLabel: '删除',
                removeTitle: '删除文件',
                uploadLabel: '上传',
                uploadTitle: '上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview: false,
                autoReplace: true,
                slugCallback: function (filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filebatchselected', function (event, files) {//文件自动上传
                //console.log(event);
                //console.log(files);
                //$(this).fileinput("upload");
            }).on('filepreupload', function (event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function (event, data, previewId, index) {//文件上传完成执行操作

                $("#caseinfo_url").val(data.response.url);
                $("#caseinfo_urlname").val(data.response.fileRealName);
                // $("#wsc6").text(data.response.fileRealName);
                $("#wsc6").addClass("wscbj")
                $("#wsc6").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled', false);

                butChange('6');
            })
            $("#reButton6").click(function () {
                butChange('6');
                $("#wsc6").addClass("wscbj")
            });


            //稽查工作报告
            $("#lawCheckreportFile").fileinput({
                uploadUrl: WEBPATH + '/pdffileupload.do', // you must set a valid URL here else you will get an error
                allowedFileExtensions: ['rar'],
                language: 'zh',
                browseClass: 'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize: 1,
                maxFileSize: 53500,
                enctype: 'multipart/form-data',
                dropZoneTitle: "可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany: '选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading: '文件预览',
                msgInvalidFileExtension: '不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected: '请选择文件',
                msgValidationError: '文件类型不正确或文件过大',
                initialPreviewFileType: 'rar',
                browseLabel: "选择文件",
                removeLabel: '删除',
                removeTitle: '删除文件',
                uploadLabel: '上传',
                uploadTitle: '上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview: false,
                autoReplace: true,
                slugCallback: function (filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function (event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function (event, data, previewId, index) {//文件上传完成执行操作

                $("#rsExecuteFile_Fileurl").val(data.response.url);
                $("#rsExecuteFile_Filename").val(data.response.fileRealName);
                // $("#wsc12").text(data.response.fileRealName);
                $("#wsc12").addClass("wscbj")
                $("#wsc12").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled', false);

                butChange('12');
            })
            $("#reButton12").click(function () {
                butChange('12');
                $("#wsc12").addClass("wscbj")
            });

            // 统一证件、着装支撑材料
            $("#dwSupFile").fileinput({
                uploadUrl: WEBPATH + '/pdffileupload.do', // you must set a valid URL here else you will get an error
                allowedFileExtensions: ['rar'],
                language: 'zh',
                browseClass: 'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize: 1,
                maxFileSize: 53500,
                enctype: 'multipart/form-data',
                dropZoneTitle: "可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany: '选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading: '文件预览',
                msgInvalidFileExtension: '不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected: '请选择文件',
                msgValidationError: '文件类型不正确或文件过大',
                initialPreviewFileType: 'rar',
                browseLabel: "选择文件",
                removeLabel: '删除',
                removeTitle: '删除文件',
                uploadLabel: '上传',
                uploadTitle: '上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview: false,
                autoReplace: true,
                slugCallback: function (filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function (event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function (event, data, previewId, index) {//文件上传完成执行操作

                $("#jzSzCustomFile_Fileurl").val(data.response.url);
                $("#jzSzCustomFile_Filename").val(data.response.fileRealName);
                // $("#wsc13").text(data.response.fileRealName);
                $("#wsc13").addClass("wscbj")
                $("#wsc13").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled', false);
                butChange('13');
            })
            $("#reButton13").click(function () {
                butChange('13');
                $("#wsc13").addClass("wscbj")
            });


            //区域交叉检查制度
            $("#dwCrossFile").fileinput({
                uploadUrl: WEBPATH + '/pdffileupload.do', // you must set a valid URL here else you will get an error
                allowedFileExtensions: ['rar'],
                language: 'zh',
                browseClass: 'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize: 1,
                maxFileSize: 53500,
                enctype: 'multipart/form-data',
                dropZoneTitle: "可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany: '选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading: '文件预览',
                msgInvalidFileExtension: '不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected: '请选择文件',
                msgValidationError: '文件类型不正确或文件过大',
                initialPreviewFileType: 'rar',
                browseLabel: "选择文件",
                removeLabel: '删除',
                removeTitle: '删除文件',
                uploadLabel: '上传',
                uploadTitle: '上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview: false,
                autoReplace: true,
                slugCallback: function (filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function (event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function (event, data, previewId, index) {//文件上传完成执行操作

                $("#jzSzExecuteFile_Fileurl").val(data.response.url);
                $("#jzSzExecuteFile_Filename").val(data.response.fileRealName);
                // $("#wsc14").text(data.response.fileRealName);
                $("#wsc14").addClass("wscbj")
                $("#wsc14").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled', false);

                butChange('14');
            })

            $("#reButton14").click(function () {
                butChange('14');
                $("#wsc14").addClass("wscbj")
            });

            //专案查办制度
            $("#dwTaskFile").fileinput({
                uploadUrl: WEBPATH + '/pdffileupload.do', // you must set a valid URL here else you will get an error
                allowedFileExtensions: ['rar'],
                language: 'zh',
                browseClass: 'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize: 1,
                maxFileSize: 53500,
                enctype: 'multipart/form-data',
                dropZoneTitle: "可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany: '选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading: '文件预览',
                msgInvalidFileExtension: '不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected: '请选择文件',
                msgValidationError: '文件类型不正确活文件过大',
                initialPreviewFileType: 'rar',
                browseLabel: "选择文件",
                removeLabel: '删除',
                removeTitle: '删除文件',
                uploadLabel: '上传',
                uploadTitle: '上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview: false,
                autoReplace: true,
                slugCallback: function (filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function (event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function (event, data, previewId, index) {//文件上传完成执行操作

                $("#zfItemFile_Fileurl").val(data.response.url);
                $("#zfItemFile_Filename").val(data.response.fileRealName);
                // $("#wsc15").text(data.response.fileRealName);
                $("#wsc15").addClass("wscbj")
                $("#wsc15").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled', false);

                butChange('15');
            })

            $("#reButton15").click(function () {
                butChange('15');
                $("#wsc15").addClass("wscbj")
            });

            //部门协调联动机制
            $("#dwLianFile").fileinput({
                uploadUrl: WEBPATH + '/pdffileupload.do', // you must set a valid URL here else you will get an error
                allowedFileExtensions: ['rar'],
                language: 'zh',
                browseClass: 'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize: 1,
                maxFileSize: 53500,
                enctype: 'multipart/form-data',
                dropZoneTitle: "可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany: '选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading: '文件预览',
                msgInvalidFileExtension: '不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected: '请选择文件',
                msgValidationError: '文件类型不正确或文件过大',
                initialPreviewFileType: 'rar',
                browseLabel: "选择文件",
                removeLabel: '删除',
                removeTitle: '删除文件',
                uploadLabel: '上传',
                uploadTitle: '上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview: false,
                autoReplace: true,
                slugCallback: function (filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function (event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function (event, data, previewId, index) {//文件上传完成执行操作

                $("#jdLawFile_Fileurl").val(data.response.url);
                $("#jdLawFile_Filename").val(data.response.fileRealName);
                // $("#wsc16").text(data.response.fileRealName);
                $("#wsc16").addClass("wscbj")
                $("#wsc16").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled', false);

                butChange('16');
            })
            $("#reButton16").click(function () {
                butChange('16');
                $("#wsc16").addClass("wscbj")
            });

            //第三方辅助执法制度
            $("#dwFulawFile").fileinput({
                uploadUrl: WEBPATH + '/pdffileupload.do', // you must set a valid URL here else you will get an error
                allowedFileExtensions: ['rar'],
                language: 'zh',
                browseClass: 'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize: 1,
                maxFileSize: 51200,
                enctype: 'multipart/form-data',
                dropZoneTitle: "可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany: '选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading: '文件预览',
                msgInvalidFileExtension: '不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected: '请选择文件',
                msgValidationError: '文件类型不正确或文件过大',
                initialPreviewFileType: 'rar',
                browseLabel: "选择文件",
                removeLabel: '删除',
                removeTitle: '删除文件',
                uploadLabel: '上传',
                uploadTitle: '上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview: false,
                autoReplace: true,
                slugCallback: function (filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function (event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function (event, data, previewId, index) {//文件上传完成执行操作

                $("#fxcRoutineFile_Fileurl").val(data.response.url);
                $("#fxcRoutineFile_Filename").val(data.response.fileRealName);
                // $("#wsc17").text(data.response.fileRealName);
                $("#wsc17").addClass("wscbj")
                $("#wsc17").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled', false);

                butChange('17');
            })
            $("#reButton17").click(function () {
                butChange('17');
                $("#wsc17").addClass("wscbj")
            });

            //执法普法制度
            $("#dwLawFile").fileinput({
                uploadUrl: WEBPATH + '/pdffileupload.do', // you must set a valid URL here else you will get an error
                allowedFileExtensions: ['rar'],
                language: 'zh',
                browseClass: 'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize: 1,
                maxFileSize: 51200,
                enctype: 'multipart/form-data',
                dropZoneTitle: "可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany: '选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading: '文件预览',
                msgInvalidFileExtension: '不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected: '请选择文件',
                msgValidationError: '文件类型不正确或文件过大',
                initialPreviewFileType: 'rar',
                browseLabel: "选择文件",
                removeLabel: '删除',
                removeTitle: '删除文件',
                uploadLabel: '上传',
                uploadTitle: '上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview: false,
                autoReplace: true,
                slugCallback: function (filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function (event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function (event, data, previewId, index) {//文件上传完成执行操作

                $("#lzDutyFile_Fileurl").val(data.response.url);
                $("#lzDutyFile_Filename").val(data.response.fileRealName);
                // $("#wsc18").text(data.response.fileRealName);
                $("#wsc18").addClass("wscbj")
                $("#wsc18").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled', false);

                butChange('18');
            })
            $("#reButton18").click(function () {
                butChange('18');
                $("#wsc18").addClass("wscbj")
            });

            //轻微违法免罚制度
            $("#dwTinylawFile").fileinput({
                uploadUrl: WEBPATH + '/pdffileupload.do', // you must set a valid URL here else you will get an error
                allowedFileExtensions: ['rar'],
                language: 'zh',
                browseClass: 'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize: 1,
                maxFileSize: 51200,
                enctype: 'multipart/form-data',
                dropZoneTitle: "可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany: '选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading: '文件预览',
                msgInvalidFileExtension: '不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected: '请选择文件',
                msgValidationError: '文件类型不正确或文件过大',
                initialPreviewFileType: 'rar',
                browseLabel: "选择文件",
                removeLabel: '删除',
                removeTitle: '删除文件',
                uploadLabel: '上传',
                uploadTitle: '上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview: false,
                autoReplace: true,
                slugCallback: function (filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function (event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function (event, data, previewId, index) {//文件上传完成执行操作

                $("#ssjOpenSystem_Fileurl").val(data.response.url);
                $("#ssjOpenSystem_Filename").val(data.response.fileRealName);
                // $("#wsc19").text(data.response.fileRealName);
                $("#wsc19").addClass("wscbj")
                $("#wsc19").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled', false);

                butChange('19');
            })
            $("#reButton19").click(function () {
                butChange('19');
                $("#wsc19").addClass("wscbj")
            });

            //监督执法正面清单制度支撑材料
            $("#systemSupFile").fileinput({
                uploadUrl: WEBPATH + '/pdffileupload.do', // you must set a valid URL here else you will get an error
                allowedFileExtensions: ['rar'],
                language: 'zh',
                browseClass: 'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize: 1,
                maxFileSize: 51200,
                enctype: 'multipart/form-data',
                dropZoneTitle: "可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany: '选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading: '文件预览',
                msgInvalidFileExtension: '不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected: '请选择文件',
                msgValidationError: '文件类型不正确或文件过大',
                initialPreviewFileType: 'rar',
                browseLabel: "选择文件",
                removeLabel: '删除',
                removeTitle: '删除文件',
                uploadLabel: '上传',
                uploadTitle: '上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview: false,
                autoReplace: true,
                slugCallback: function (filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function (event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function (event, data, previewId, index) {//文件上传完成执行操作

                $("#dxLawFile_Fileurl").val(data.response.url);
                $("#dxLawFile_Filename").val(data.response.fileRealName);
                // $("#wsc20").text(data.response.fileRealName);
                $("#wsc20").addClass("wscbj")
                $("#wsc20").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled', false);

                butChange('20');
            })
            $("#reButton20").click(function () {
                butChange('20');
                $("#wsc20").addClass("wscbj")
            });

            //监督执法正面清单制度支撑材料
            $("#positivelistSupFile").fileinput({
                uploadUrl: WEBPATH + '/pdffileupload.do', // you must set a valid URL here else you will get an error
                allowedFileExtensions: ['rar'],
                language: 'zh',
                browseClass: 'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize: 1,
                maxFileSize: 51200,
                enctype: 'multipart/form-data',
                dropZoneTitle: "可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany: '选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading: '文件预览',
                msgInvalidFileExtension: '不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected: '请选择文件',
                msgValidationError: '文件类型不正确或文件过大',
                initialPreviewFileType: 'rar',
                browseLabel: "选择文件",
                removeLabel: '删除',
                removeTitle: '删除文件',
                uploadLabel: '上传',
                uploadTitle: '上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview: false,
                autoReplace: true,
                slugCallback: function (filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function (event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function (event, data, previewId, index) {//文件上传完成执行操作

                $("#zfJcWorkFile_Fileurl").val(data.response.url);
                $("#zfJcWorkFile_Filename").val(data.response.fileRealName);
                // $("#wsc21").text(data.response.fileRealName);
                $("#wsc21").addClass("wscbj")
                $("#wsc21").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled', false);

                butChange('21');
            })
            $("#reButton21").click(function () {
                butChange('21');
                $("#wsc21").addClass("wscbj")
            });

            //双随机、一公开”监管工作执行情况支撑材料：
            $("#randomsupFile").fileinput({
                uploadUrl: WEBPATH + '/pdffileupload.do', // you must set a valid URL here else you will get an error
                allowedFileExtensions: ['xls', 'xlsx'],
                language: 'zh',
                browseClass: 'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize: 1,
                maxFileSize: 51200,
                enctype: 'multipart/form-data',
                dropZoneTitle: "可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany: '选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading: '文件预览',
                msgInvalidFileExtension: '不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected: '请选择文件',
                msgValidationError: '文件类型不正确或文件过大',
                initialPreviewFileType: 'els',
                browseLabel: "选择文件",
                removeLabel: '删除',
                removeTitle: '删除文件',
                uploadLabel: '上传',
                uploadTitle: '上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview: false,
                autoReplace: true,
                slugCallback: function (filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function (event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function (event, data, previewId, index) {//文件上传完成执行操作

                $("#jcBwExcel_Fileurl").val(data.response.url);
                $("#jcBwExcel_Filename").val(data.response.fileRealName);
                // $("#wsc22").text(data.response.fileRealName);
                $("#wsc22").addClass("wscbj")
                $("#wsc22").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled', false);

                butChange('22');
            })
            $("#reButton22").click(function () {
                butChange('22');
                $("#wsc22").addClass("wscbj")
            });

            //发布典型案例的通知或者公开的链接
            $("#typicalcaseFile").fileinput({
                uploadUrl: WEBPATH + '/pdffileupload.do', // you must set a valid URL here else you will get an error
                allowedFileExtensions: ['rar'],
                language: 'zh',
                browseClass: 'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize: 1,
                maxFileSize: 51200,
                enctype: 'multipart/form-data',
                dropZoneTitle: "可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany: '选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading: '文件预览',
                msgInvalidFileExtension: '不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected: '请选择文件',
                msgValidationError: '文件类型不正确或文件过大',
                initialPreviewFileType: 'rar',
                browseLabel: "选择文件",
                removeLabel: '删除',
                removeTitle: '删除文件',
                uploadLabel: '上传',
                uploadTitle: '上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview: false,
                autoReplace: true,
                slugCallback: function (filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function (event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function (event, data, previewId, index) {//文件上传完成执行操作

                $("#jcBwFile_Fileurl").val(data.response.url);
                $("#jcBwFile_Filename").val(data.response.fileRealName);
                // $("#wsc23").text(data.response.fileRealName);
                $("#wsc23").addClass("wscbj")
                $("#wsc23").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled', false);

                butChange('23');
            })
            $("#reButton23").click(function () {
                butChange('23');
                $("#wsc23").addClass("wscbj")
            });

            //发布典型案例的通知或者公开的链接
            $("#guideFile").fileinput({
                uploadUrl: WEBPATH + '/pdffileupload.do', // you must set a valid URL here else you will get an error
                allowedFileExtensions: ['rar'],
                language: 'zh',
                browseClass: 'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize: 1,
                maxFileSize: 51200,
                enctype: 'multipart/form-data',
                dropZoneTitle: "可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany: '选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading: '文件预览',
                msgInvalidFileExtension: '不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected: '请选择文件',
                msgValidationError: '文件类型不正确或文件过大',
                initialPreviewFileType: 'rar',
                browseLabel: "选择文件",
                removeLabel: '删除',
                removeTitle: '删除文件',
                uploadLabel: '上传',
                uploadTitle: '上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview: false,
                autoReplace: true,
                slugCallback: function (filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function (event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function (event, data, previewId, index) {//文件上传完成执行操作

                $("#lawSatisficingFile_Fileurl").val(data.response.url);
                $("#lawSatisficingFile_Filename").val(data.response.fileRealName);
                // $("#wsc24").text(data.response.fileRealName);
                $("#wsc24").addClass("wscbj")
                $("#wsc24").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled', false);

                butChange('24');
            })
            $("#reButton24").click(function () {
                butChange('24');
                $("#wsc24").addClass("wscbj")
            });

            //已建立人才库的相关文件
            $("#peopoolFile").fileinput({
                uploadUrl: WEBPATH + '/pdffileupload.do', // you must set a valid URL here else you will get an error
                allowedFileExtensions: ['pdf'],
                language: 'zh',
                browseClass: 'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize: 1,
                maxFileSize: 31800,
                enctype: 'multipart/form-data',
                dropZoneTitle: "可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany: '选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading: '文件预览',
                msgInvalidFileExtension: '不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected: '请选择文件',
                msgValidationError: '文件类型不正确或文件过大',
                initialPreviewFileType: 'pdf',
                browseLabel: "选择文件",
                removeLabel: '删除',
                removeTitle: '删除文件',
                uploadLabel: '上传',
                uploadTitle: '上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview: false,
                autoReplace: true,
                slugCallback: function (filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function (event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function (event, data, previewId, index) {//文件上传完成执行操作

                $("#activityReport_Fileurl").val(data.response.url);
                $("#activityReport_Filename").val(data.response.fileRealName);
                // $("#wsc25").text(data.response.fileRealName);
                $("#wsc25").addClass("wscbj")
                $("#wsc25").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled', false);

                butChange('25');
            })
            $("#reButton25").click(function () {
                butChange('25');
                $("#wsc25").addClass("wscbj")
            });

            //监督帮扶典型案例 开展培训（岗位培训除外）的动态或宣传情况
            $("#trainFile").fileinput({
                uploadUrl: WEBPATH + '/pdffileupload.do', // you must set a valid URL here else you will get an error
                allowedFileExtensions: ['xls', 'xlsx'],
                language: 'zh',
                browseClass: 'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize: 1,
                maxFileSize: 51200,
                enctype: 'multipart/form-data',
                dropZoneTitle: "可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany: '选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading: '文件预览',
                msgInvalidFileExtension: '不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected: '请选择文件',
                msgValidationError: '文件类型不正确或文件过大',
                initialPreviewFileType: 'els',
                browseLabel: "选择文件",
                removeLabel: '删除',
                removeTitle: '删除文件',
                uploadLabel: '上传',
                uploadTitle: '上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview: false,
                autoReplace: true,
                slugCallback: function (filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function (event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function (event, data, previewId, index) {//文件上传完成执行操作

                $("#jdbfExcel_Fileurl").val(data.response.url);
                $("#jdbfExcel_Filename").val(data.response.fileRealName);
                // $("#wsc26").text(data.response.fileRealName);
                $("#wsc26").addClass("wscbj")
                $("#wsc26").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled', false);

                butChange('26');
            })
            $("#reButton26").click(function () {
                butChange('26');
                $("#wsc26").addClass("wscbj")
            });

            //加强排污许可执法监管的政策文件
            $("#licenseFileUpload").fileinput({
                uploadUrl: WEBPATH + '/pdffileupload.do', // you must set a valid URL here else you will get an error
                allowedFileExtensions: ['rar'],
                language: 'zh',
                browseClass: 'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize: 1,
                maxFileSize: 51200,
                enctype: 'multipart/form-data',
                dropZoneTitle: "可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany: '选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading: '文件预览',
                msgInvalidFileExtension: '不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected: '请选择文件',
                msgValidationError: '文件类型不正确或文件过大',
                initialPreviewFileType: 'rar',
                browseLabel: "选择文件",
                removeLabel: '删除',
                removeTitle: '删除文件',
                uploadLabel: '上传',
                uploadTitle: '上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview: false,
                autoReplace: true,
                slugCallback: function (filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function (event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function (event, data, previewId, index) {//文件上传完成执行操作

                $("#jdbfFileUrl_Fileurl").val(data.response.url);
                $("#jdbfFileName_Filename").val(data.response.fileRealName);
                // $("#wsc27").text(data.response.fileRealName);
                $("#wsc27").addClass("wscbj")
                $("#wsc27").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled', false);

                butChange('27');
            })
            $("#reButton27").click(function () {
                butChange('27');
                $("#wsc27").addClass("wscbj")
            });


            //行政处罚自由裁量规定
            $("#discretionFileUpload").fileinput({
                uploadUrl: WEBPATH + '/pdffileupload.do', // you must set a valid URL here else you will get an error
                allowedFileExtensions: ['xls', 'xlsx'],
                language: 'zh',
                browseClass: 'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize: 1,
                maxFileSize: 51200,
                enctype: 'multipart/form-data',
                dropZoneTitle: "可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany: '选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading: '文件预览',
                msgInvalidFileExtension: '不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected: '请选择文件',
                msgValidationError: '文件类型不正确或文件过大',
                initialPreviewFileType: 'els',
                browseLabel: "选择文件",
                removeLabel: '删除',
                removeTitle: '删除文件',
                uploadLabel: '上传',
                uploadTitle: '上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview: false,
                autoReplace: true,
                slugCallback: function (filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function (event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function (event, data, previewId, index) {//文件上传完成执行操作

                $("#zxxdExcel_Fileurl").val(data.response.url);
                $("#zxxdExcel_Filename").val(data.response.fileRealName);
                // $("#wsc28").text(data.response.fileRealName);
                $("#wsc28").addClass("wscbj")
                $("#wsc28").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled', false);

                butChange('28');
            })
            $("#reButton28").click(function () {
                butChange('28');
                $("#wsc28").addClass("wscbj")
            });

            //典型案例公开的网页截图或者通报文件
            $("#licensesFileUpload").fileinput({
                uploadUrl: WEBPATH + '/pdffileupload.do', // you must set a valid URL here else you will get an error
                allowedFileExtensions: ['rar'],
                language: 'zh',
                browseClass: 'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize: 1,
                maxFileSize: 51200,
                enctype: 'multipart/form-data',
                dropZoneTitle: "可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany: '选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading: '文件预览',
                msgInvalidFileExtension: '不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected: '请选择文件',
                msgValidationError: '文件类型不正确或文件过大',
                initialPreviewFileType: 'rar',
                browseLabel: "选择文件",
                removeLabel: '删除',
                removeTitle: '删除文件',
                uploadLabel: '上传',
                uploadTitle: '上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview: false,
                autoReplace: true,
                slugCallback: function (filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function (event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function (event, data, previewId, index) {//文件上传完成执行操作

                $("#zxxdExpressionFileUrl_Fileurl").val(data.response.url);
                $("#zxxdExpressionFileName_Filename").val(data.response.fileRealName);
                // $("#wsc29").text(data.response.fileRealName);
                $("#wsc29").addClass("wscbj")
                $("#wsc29").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled', false);

                butChange('29');
            })
            $("#reButton29").click(function () {
                butChange('29');
                $("#wsc29").addClass("wscbj")
            });

            //省本级制定的排污许可清单式执法通知文件或行动方案或现场指导等佐证材料
            $("#licenselisFileUpload").fileinput({
                uploadUrl: WEBPATH + '/pdffileupload.do', // you must set a valid URL here else you will get an error
                allowedFileExtensions: ['rar'],
                language: 'zh',
                browseClass: 'btn btn-danger',
                //overwriteInitial: true,
                minFileCount: 1,
                maxFileCount: 1,
                minFileSize: 1,
                maxFileSize: 51200,
                enctype: 'multipart/form-data',
                dropZoneTitle: "可拖拽文件到此处...",
                initialPreviewShowDelete: false,
                msgFilesTooMany: '选择上传的文件数量({n}) 超过允许的最大数值{m}！',
                msgZoomModalHeading: '文件预览',
                msgInvalidFileExtension: '不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
                msgNoFilesSelected: '请选择文件',
                msgValidationError: '文件类型不正确或文件过大',
                initialPreviewFileType: 'rar',
                browseLabel: "选择文件",
                removeLabel: '删除',
                removeTitle: '删除文件',
                uploadLabel: '上传',
                uploadTitle: '上传文件',
                cancelLabel: '取消',
                cancelTitle: '取消上传',
                showPreview: false,
                autoReplace: true,
                slugCallback: function (filename) {
                    return filename.replace('(', '_').replace(']', '_');
                }
            }).on('filepreupload', function (event, data, previewId, index) { //文件开始上传操作

                $("#xxcj_AJSL_Butt").prop('disabled', true);

            }).on('fileuploaded', function (event, data, previewId, index) {//文件上传完成执行操作

                $("#nxzjCaseFileUrl_Fileurl").val(data.response.url);
                $("#nxzjCaseFileName_Filename").val(data.response.fileRealName);
                // $("#wsc30").text(data.response.fileRealName);
                $("#wsc30").addClass("wscbj")
                $("#wsc30").html("<a href='javascript:void(0)' onclick='downloadFile(\""+data.response.url+"\",\""+data.response.fileRealName+"\")'>"+data.response.fileRealName+"</a>");

                $("#xxcj_AJSL_Butt").prop('disabled', false);

                butChange('30');
            })
            $("#reButton30").click(function () {
                butChange('30');
                $("#wsc30").addClass("wscbj")
            });


        });
    </script>
    <style>
        h1 {
            font-size: 24px;
            font-weight: 700;
        }

        h2 {
            margin-left: 44px;
            font-size: 20px;
            font-weight: 600;
            color: #25678E;
        }

        h3 {
            font-size: 18px;
        }

        .shifou {
            /*margin-left: 283px;*/
            margin-top: 20px
        }

        .zhiding {
            font-size: 14px;
            color: #333333;
            font-weight: 400
        }

        .shangchuan {
            margin-left: 60px; margin-top: 20px;display: flex;width: calc(100% - 200px)
        }

        /*.file-input-new {*/
        /*    width: 50%;*/
        /*}*/
        #reButton1,#reButton2,#reButton3,#reButton4,#reButton5,#reButton6,#reButton7,#reButton8,#reButton9,#reButton10,#reButton11,#reButton12,#reButton13,#reButton14,#reButton15,#reButton16
        ,#reButton17,#reButton18,#reButton19,#reButton20,#reButton21,#reButton22,#reButton23,#reButton24,#reButton25,#reButton26,#reButton27,#reButton28,#reButton29{
            height: 32px;
            background: #0093C3;
            border-color: #0093C3;
            margin-left: 5px;
            border-radius: 3px;
            width: 80px;
            color:#ffffff;
            font-size: 14px;
        }
        /*.btn btn-danger btn-xs{*/
        /*height: 25px;*/
        /*}*/
        select,input{
            background: #FFFFFF;
            border: 1px solid #DCDFE6;
            border-radius: 5px;
            width: 380px;
            height: 39px;
            padding-left:5px;
        }
        .sp{text-align: right;}
        .wscbj{width: 380px;position: relative;left: 358px;border-radius: 3px;margin-top: 14px;padding-left:10px}
        .wscbj1{width: 380px;position: relative;left: 346px;border-radius: 3px;margin-top: 14px;padding-left:10px}
        .form-control{width: 288px;margin-left: 4px;}
        .input-group .form-control{width: 65%;border-radius:3px}
        .input-group-btn .btn{height:39px;line-height: 26px}
        .file-caption-main{width:81%}
        .file-input .form-control{height:39px;width:378px}
        .back{height:39px!important;color:#ffffff}
        label {
            display: inline-block;
            width: 30%;
            margin-bottom: 5px;
            margin-left: 5px;
            text-align: right;
        }
    </style>
</head>
<body>
<div class="center_weizhi">当前位置：信息采集 - 省内评比信息 - 行政区基础数据</div>
<div class="center form-group" style="width: calc(100% - 210px)">
    <form id="ajslForm" style="width: 100%">
        <div class="center_list" style="width: 100%">
            <input type="hidden" class="form-control" name="areacodePro" id="areacodePro" value="${areaUser.areaCode}"/>
            <input type="hidden" class="form-control" name="areatypePro" id="areatypePro"
                   value="${areaUser.arealevel}"/>
            <%--<input type="hidden" class="form-control" name="xxxx" id="xxxx"--%>
                   <%--value="${provinceSelectionUnit.publicityway}"/>--%>

            <div style="background-color: #fff; padding: 24px;">
                <p style=""><label style="font-size: 14px;font-weight: 400;color: #333333;position:relative;bottom: 1px ">行政区：</label>
                    <span style="margin-left: 10px;font-size: 24px; font-weight: 400;color: #333333;margin-bottom: 20px">${areaUser.userName}</span>
                </p>
                <p style="margin-top:20px;">
                    <label style=" float:left; font-size: 14px; color: #333333; font-weight: 400"><span
                            style="color: red;size: 60px">*</span>机构名称（全称）：</label>
                    <input style="width: 380px;margin-top:-5px"
                           <c:if test="${provinceReportUser.reportstate ==1 || sysInitConfig.code !='1'}">disabled="disabled" </c:if>
                           type="text" class="form-control" id="agencyName" name="agencyName"
                           value="${provinceSelectionUnit.agencyName}"
                           placeholder="请输入机构名称（全称）例如：河北省生态环境厅" width="100px;"/>
                    <span style="display:inline-block;color: #FB3301;margin-left: 7px; margin-top:3px;font-size: 12px;">例:河北省生态环境厅</span>
                </p>
                <p style="margin-left:600px; color: red; font-size: 14px;padding-top:10px">重要提醒：</p>
                <p style="margin-left:600px; color: red; font-size: 14px;">1.请完成本页面全部信息填报，否则本单位将无法参加大练兵集体评选。</p>
                <p style="margin-left:600px; color: red; font-size: 14px;">2.请认真填写下列信息，并仔细上传支撑材料，若未填写相关信息或支撑材料不能证明信息的真实性，将不予给分。</p>
                <p style="margin-left:600px; color: red; font-size: 14px;">3.每份支撑材料大小不超过50M，附件格式支持PNG、png、doc、pdf、jpg、JPG、xlsx、xls、csv、mp4、mp3、rar、zip。</p>
                <p style="margin-left:600px; color: red; font-size: 14px;">4.每份支撑材料需根据提示规范命名：【xx省-具体文件标题】,例：【河北省-激励措施相关文件】。</p>
                </p>
            </div>


            <%--111111111111111111111111111111111111111111111111111111111111111111--%>

            <div style="margin-top: 20px;">
                <div style="background-color: #fff;  padding: 2px 30px 26px 21px; line-height: 30px; width: 100%">
                    <h1 style="color: #31688F;font-size: 16px;font-weight: 400">一、省级大练兵组织情况</h1>
                    <%--<hr width="100%"/>--%>
                    <%--Excel文件/'--%>
                    <%--                    <div style="margin-left:286px; display: flex;line-height: 39px;">--%>
                    <%--                        <span style="font-size: 14px; color: #333333; font-weight: 400 ;">Excel文件：</span>--%>
                    <%--                        <span id="uploadTr1"--%>
                    <%--                              <c:if test="${provinceSelectionUnit.proLevlExcelName!='' && provinceSelectionUnit.proLevlExcelName!=null }">style='display:none'</c:if>--%>
                    <%--                              >--%>
                    <%--&lt;%&ndash;                            style="width: calc(100% - 224px)"&ndash;%&gt;--%>
                    <%--                                <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
                    <%--									<div style="display: flex;width: 100%">--%>
                    <%--										<input class="upchuan" type="file" id="proLevlFiles" value="文件上传">--%>
                    <%--										<input type="hidden" class="form-control" name="proLevlExcelName"--%>
                    <%--                                               id="proLevlFile_Filename"--%>
                    <%--                                               value="${provinceSelectionUnit.proLevlExcelName}">--%>
                    <%--										<input type="hidden" class="form-control" name="proLevlExcelUrl"--%>
                    <%--                                               id="proLevlFile_Fileurl"--%>
                    <%--                                               value="${provinceSelectionUnit.proLevlExcelUrl}">--%>

                    <%--									</div>--%>
                    <%--                                </c:if>--%>
                    <%--                        </span>--%>
                    <%--                        <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
                    <%--                            <input id="reButton1" style="margin-top:2px; "--%>
                    <%--                                   <c:if test="${provinceSelectionUnit.proLevlExcelName=='' || provinceSelectionUnit.proLevlExcelName==null }">style="display:none;"</c:if>--%>
                    <%--                                   class="btn btn-danger btn-xs" type="button" value="重新上传"/>--%>
                    <%--                        </c:if>--%>
                    <%--                    </div>--%>
                    <%--                    <p>--%>
                    <%--                        <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}"><span style="color: deepskyblue;margin-left:359px;margin-top:5px">--%>
                    <%--											<a style="color: blue;margin-left: 7px " href="#"--%>
                    <%--                                               onclick="down(1)">(下载模板）</a>--%>
                    <%--										</span></c:if>--%>
                    <%--                    </p>--%>
                    <%--                    <div id="wsc1" style="margin-left:356px;width: 380px;margin-top: 10px;border-radius: 3px;">--%>
                    <%--                         <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">--%>
                    <%--                            <c:choose>--%>
                    <%--                                <c:when test="${provinceSelectionUnit.proLevlExcelName!='' && provinceSelectionUnit.proLevlExcelName!=null }">--%>
                    <%--                                    <span style=" margin-right: 8px; padding-top: 4px;">--%>
                    <%--                                      <a href="javascript:void(0)"--%>
                    <%--                                         onclick="downloadFile('${provinceSelectionUnit.proLevlExcelUrl}','${provinceSelectionUnit.proLevlExcelName}')"> ${provinceSelectionUnit.proLevlExcelName} </a>--%>
                    <%--                                    </span>--%>
                    <%--                                </c:when>--%>
                    <%--                            </c:choose>--%>
                    <%--				        </span>--%>
                    <%--                    </div>--%>

                    <%--                    <p style="color: #FB3301;margin-left: 365px;font-size: 12px;line-height: 20px;">1.请认真填写下列信息，并仔细上传支撑材料，填写的信息未在支撑材料中体现的，将不作为给分点--%>
                    <%--                    </p>--%>
                    <%--                    <p style="color: #FB3301;margin-left: 365px;width: 560px;font-size: 12px;line-height: 20px;">2.每份支撑材料大小不超过50M，要求rar格式；存放支撑材料的文件夹（压缩包）以【省份-二级标题】--%>
                    <%--                        命名，如知识竞赛支撑材料应存放在【xx省-知识竞赛】文件夹（压缩包）中</p>--%>
                    <div style="background-color: #fff; width: 100%">
                        <h1 style="font-size: 14px;color: #31688F;font-weight:400;height: 33px;line-height:33px;background: #F8F8F8;">（一）激励措施</h1>
                        <div style="margin-top: 20px">
                            <label style="font-size: 14px; color: #333333; font-weight: 400"><span
                                    style="color: red;size: 60px;">*</span>是否采取激励措施：</label>
                            <select style="width:380px;margin-right:5px;" name="is_jili"
                                    id="is_jili">
                                <option value="">--请选择--</option>
                                <option value="1"
                                <%--                                        <c:if test="${provinceSelectionUnit.isIncentiveMet eq '1'}">selected</c:if>>是--%>
                                </option>
                                <option value="0"
                                <%--                                        <c:if test="${provinceSelectionUnit.isIncentiveMet eq '0'}">selected</c:if>>否--%>
                                </option>
                            </select>
                        </div>
                        <div style="margin-top: 20px">
                            <label style="font-size: 14px; color: #333333; font-weight: 400"><span
                                    style="color: red;size: 60px;" class="isShow">*</span>激励形式：</label>
                            <select style="width:380px;margin-right:5px;" name="jili_name"
                                    id="jili_name">
                                <option value="">--请选择--</option>
                                <option value="1"
                                        <c:if test="${provinceSelectionUnit.incentiveMethod eq '1'}">selected</c:if>>
                                    记功嘉奖
                                </option>
                                <option value="2"
                                        <c:if test="${provinceSelectionUnit.incentiveMethod eq '2'}">selected</c:if>>
                                    联合其他部门奖励
                                </option>
                                <option value="3"
                                        <c:if test="${provinceSelectionUnit.incentiveMethod eq '3'}">selected</c:if>>
                                    省级生态环境部门表扬
                                </option>
                            </select>
                        </div>
                        <div style="margin-left:58px;margin-top: 20px;display: flex; width: calc(100% - 200px)">
                            <label style="font-size: 14px; color: #333333; font-weight: 400"><span
                                    style="color: red;size: 60px;" class="isShow">*</span>激励奖励相关文件：</label>
                            <span id="uploadTr1"
                                  <c:if test="${provinceSelectionUnit.incentiveMetFileName!='' && provinceSelectionUnit.incentiveMetFileName!=null }">style='display:none'</c:if>
                                  style=" ">
<%--                                width: calc(100% - 224px)--%>
								<c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                    <input type="file" id="jiliFile" value="文件上传">
                                    <input type="hidden" class="form-control" name="jili_urlname"
                                           id="jili_urlname"
                                           value="${provinceSelectionUnit.incentiveMetFileName}">
                                    <input type="hidden" class="form-control" name="jili_url"
                                           id="jili_url"
                                           value="${provinceSelectionUnit.incentiveMetFileUrl}">
                                </c:if>
						   </span>
                            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                <input id="reButton1"
                                       <c:if test="${provinceSelectionUnit.incentiveMetFileName=='' || provinceSelectionUnit.incentiveMetFileName==null }">style='display:none'</c:if>
                                       class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                            </c:if>
                        </div>
                        <div id="wsc1" style="margin-left:357px;width: 380px;margin-top: 14px;border-radius: 3px;">
                            <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">
								<c:choose>
                                    <c:when test="${provinceSelectionUnit.incentiveMetFileName!='' && provinceSelectionUnit.incentiveMetFileName!=null }">
											<span style=" margin-right: 8px; padding-top: 4px;">
<%--                                                    ${provinceSelectionUnit.jiliUrlname}--%>
                                                <a href="javascript:void(0)"
                                                   onclick="downloadFile('${provinceSelectionUnit.incentiveMetFileUrl}','${provinceSelectionUnit.incentiveMetFileName}')">
                                                        ${provinceSelectionUnit.incentiveMetFileName}
                                                </a>
                                            </span>
                                    </c:when>
                                </c:choose>
							</span>
                        </div>
                    </div>

                    <div style="background-color: #fff; width: 100%;">
                        <h1 style="font-size: 14px;color: #31688F;font-weight:400;height: 33px;line-height:33px;background: #F8F8F8;">（二）竞赛比武</h1>
                        <div style="margin-top: 20px">
                            <label class='sp' style="font-size: 14px; color: #333333; font-weight: 400"><span
                                    style="color: red;size: 60px">*</span>是否开展竞赛比武：</label>
                            <select id="is_jingwu" name="is_jingwu">
                                <option value="">--请选择--</option>
                                <option value="1" <c:if test="${provinceSelectionUnit.isUnfold eq '1'}">selected</c:if>>
                                    是
                                </option>
                                <option value="0" <c:if test="${provinceSelectionUnit.isUnfold eq '0'}">selected</c:if>>
                                    否
                                </option>
                            </select>
                        </div>
                        <div style="margin-top: 20px">
                            <label class='sp' style="font-size: 14px; color: #333333; font-weight: 400"><span
                                    style="color: red;size: 60px" class="isJingwuShow">*</span>是否联合其他部门开展：</label>
                            <select id="is_lianhe" name="is_lianhe">
                                <option value="">--请选择--</option>
                                <option value="1"
                                        <c:if test="${provinceSelectionUnit.parScopes eq '1'}">selected</c:if>>是
                                </option>
                                <option value="0"
                                        <c:if test="${provinceSelectionUnit.parScopes eq '0'}">selected</c:if>>否
                                </option>
                            </select>
                            <%--                            <span style="font-size: 12px; color: red">（是否涵盖本行政区域内所有生态环境执法机构）</span>--%>
                        </div>
                        <%--                        <div style="margin-left: 200px;margin-top: 20px">--%>
                        <%--                            <span class='sp' style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
                        <%--                                    style="color: red;size: 60px">*</span>机构领导是否带头参与：</span>--%>
                        <%--                            <select id="jgld" name="isLeader"--%>
                        <%--                                    required>--%>
                        <%--                                <option value="">--请选择--</option>--%>
                        <%--                                <option value="1" <c:if test="${provinceSelectionUnit.isLeader eq '1'}">selected</c:if>>--%>
                        <%--                                    是--%>
                        <%--                                </option>--%>
                        <%--                                <option value="0" <c:if test="${provinceSelectionUnit.isLeader eq '0'}">selected</c:if>>--%>
                        <%--                                    否--%>
                        <%--                                </option>--%>
                        <%--                            </select>--%>
                        <%--                            <div id="jtldzw" style="display: none;margin-top: 20px;margin-left: 56px">--%>
                        <%--                                <span class='sp' style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
                        <%--                                        style="color: red;size: 60px">*</span>具体领导职务：</span>--%>
                        <%--                                <input name="leaderDuty" id="leaderDuty" value="${provinceSelectionUnit.leaderDuty}">--%>
                        <%--                            </div>--%>
                        <%--                        </div>--%>
                        <%--                        <div style="margin-left: 297px;margin-top: 20px">--%>
                        <%--                            <span class='sp' style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
                        <%--                                    style="color: red;size: 60px">*</span>参与率：</span>--%>
                        <%--                            <input id="parRate" style="width: 380px;" name="parRate"--%>
                        <%--                                   value="${provinceSelectionUnit.parRate}" onkeyup="clearNoNum(this)">%--%>
                        <%--                        </div>--%>
                        <div style="margin-left: 46px;margin-top: 20px; display: flex; width: calc(100% - 150px)">
                            <label class='sp' style="font-size: 14px; color: #333333; font-weight: 400"><span
                                    style="color: red;size: 60px" class="isJingwuShow">*</span>竞赛比武通知：</label>

                            <span id="uploadTr2"
                                  <c:if test="${provinceSelectionUnit.knowFileName!='' && provinceSelectionUnit.knowFileName!=null }">style='display:none'
                                  </c:if>style="">
                                <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                    <input type="file" id="jwInform" value="文件上传">
                                    <input type="hidden" class="form-control" name="jw_inform_urlname" id="jw_inform_urlname"
                                           value="${provinceSelectionUnit.knowFileName}">
                                    <input type="hidden" class="form-control" name="jw_inform_url" id="jw_inform_url"
                                           value="${provinceSelectionUnit.knowFileUrl}">
                                </c:if>
                            </span>
                            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                <input id="reButton2"
                                       <c:if test="${provinceSelectionUnit.knowFileName=='' || provinceSelectionUnit.knowFileName==null }">style='display:none'</c:if>
                                       class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                            </c:if>
                        </div>
                        <div id="wsc2" style="margin-left:355px;width: 380px;border-radius: 3px;">
                            <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">
                                <c:choose>
                                    <c:when test="${provinceSelectionUnit.knowFileName!='' && provinceSelectionUnit.knowFileName!=null }">
                                    <span style=" margin-right: 8px; padding-top: 4px;">
                                        <a href="javascript:void(0)"
                                           onclick="downloadFile('${provinceSelectionUnit.knowFileUrl}','${provinceSelectionUnit.knowFileName}')">
                                                ${provinceSelectionUnit.proLevlExcelName}
                                        </a>
                                    </span>
                                    </c:when>
                                </c:choose>
                            </span>
                        </div>

                        <div style="margin-left: 45px;margin-top: 20px; display: flex; width: calc(100% - 150px)">
                            <label class='sp' style="font-size: 14px; color: #333333; font-weight: 400"><span
                                    style="color: red;size: 60px" class="isJingwuShow">*</span>竞赛比武活动网页截图：</label>

                            <span id="uploadTr3"
                                  <c:if test="${provinceSelectionUnit.knowFileName!='' && provinceSelectionUnit.knowFileName!=null }">style='display:none'
                                  </c:if>style="">
                                <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                    <input type="file" id="jwWebFile" value="文件上传">
                                    <input type="hidden" class="form-control" name="jw_web_urlname" id="jw_web_urlname"
                                           value="${provinceSelectionUnit.knowFileName}">
                                    <input type="hidden" class="form-control" name="jw_web_url" id="jw_web_url"
                                           value="${provinceSelectionUnit.knowFileUrl}">
                                </c:if>
                            </span>
                            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                <input id="reButton3"
                                       <c:if test="${provinceSelectionUnit.knowFileName=='' || provinceSelectionUnit.knowFileName==null }">style='display:none'</c:if>
                                       class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                            </c:if>
                        </div>
                        <div id="wsc3" style="margin-left:355px;width: 380px;border-radius: 3px;">
                            <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">
                                <c:choose>
                                    <c:when test="${provinceSelectionUnit.knowFileName!='' && provinceSelectionUnit.knowFileName!=null }">
                                    <span style=" margin-right: 8px; padding-top: 4px;">
                                        <a href="javascript:void(0)"
                                           onclick="downloadFile('${provinceSelectionUnit.knowFileUrl}','${provinceSelectionUnit.knowFileName}')">
                                                ${provinceSelectionUnit.proLevlExcelName}
                                        </a>
                                    </span>
                                    </c:when>
                                </c:choose>
                            </span>
                        </div>
                    </div>
                </div>
            </div>


            <%--222222222222222222222222222222222222222222222222222--%>

            <div style="margin-top: 40px;line-height: 30px">
                <div style="background-color: #fff; padding: 2px 30px 26px 21px;width: 100%">
                    <h1 style="color: #25678E;font-size: 16px;font-weight: 400;color: #31688F;">二、日常监督执法工作</h1>
                    <%--                    <hr width="100%"/>--%>
                    <%--                    <div style="margin-left:286px;display: flex;line-height: 39px;">--%>
                    <%--                        <span style="font-size: 14px; color: #333333; font-weight: 400;padding-top:2px">Excel文件：</span>--%>
                    <%--                        <span id="uploadTr6"--%>
                    <%--                              <c:if test="${provinceSelectionUnit.troopsConAdminFileName!='' && provinceSelectionUnit.troopsConAdminFileName!=null }">style='display:none'</c:if>--%>
                    <%--                              style="">--%>
                    <%--&lt;%&ndash;                            width: calc(100% - 224px)&ndash;%&gt;--%>
                    <%--                                <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
                    <%--									<div style="display: flex">--%>
                    <%--										<input type="file" id="troopsConAdminFile" value="文件上传">--%>
                    <%--										<input type="hidden" class="form-control" name="troopsConAdminFileName"--%>
                    <%--                                               id="troopsConAdminFile_Filename"--%>
                    <%--                                               value="${provinceSelectionUnit.troopsConAdminFileName}">--%>
                    <%--										<input type="hidden" class="form-control" name="troopsConAdminFileUrl"--%>
                    <%--                                               id="troopsConAdminFile_Fileurl"--%>
                    <%--                                               value="${provinceSelectionUnit.troopsConAdminFileUrl}">--%>

                    <%--									</div>--%>

                    <%--                                </c:if>--%>
                    <%--					    </span>--%>
                    <%--                        <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
                    <%--                            <input id="reButton6"--%>
                    <%--                                   <c:if test="${provinceSelectionUnit.troopsConAdminFileName=='' || provinceSelectionUnit.troopsConAdminFileName==null }">style='display:none'</c:if>--%>
                    <%--                                   class="btn btn-danger btn-xs" style="margin-top:8px" type="button" value="重新上传"/>--%>
                    <%--                        </c:if>--%>

                    <%--                    </div>--%>
                    <%--                    <p style="color: deepskyblue; padding-top: 5px">--%>
                    <%--                        <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
                    <%--                            <a style="color: blue;margin-left: 360px;margin-top: 5px;" href="#" onclick="down(2)">（下载模板）</a></c:if>--%>
                    <%--                    </p>--%>
                    <%--                    <div id="wsc6" style="margin-left:357px;width: 380px;margin-top: 10px;border-radius: 3px;">--%>
                    <%--                        <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">--%>
                    <%--                            <c:choose>--%>
                    <%--                                <c:when test="${provinceSelectionUnit.troopsConAdminFileName!='' && provinceSelectionUnit.troopsConAdminFileName!=null }">--%>
                    <%--                                    <span style=" margin-right: 8px; padding-top: 4px;">--%>
                    <%--                                       <a href="javascript:void(0)"--%>
                    <%--                                          onclick="downloadFile('${provinceSelectionUnit.troopsConAdminFileUrl}','${provinceSelectionUnit.troopsConAdminFileName}')"> ${provinceSelectionUnit.troopsConAdminFileName} </a>--%>
                    <%--                                    </span>--%>
                    <%--                                </c:when>--%>
                    <%--                            </c:choose>--%>
                    <%--                        </span>--%>
                    <%--                    </div>--%>
                    <%--                    <p style="color: #FB3301;width: 526px;font-size:12px;line-height: 20px;font-weight: 400;margin-left: 367px">1.请认真填写下列信息，并仔细上传支撑材料，填写的信息未在支撑材料中体现的，将不作为给分点</p>--%>

                    <%--                    <p style="color: #FB3301;width: 526px;line-height: 20px;font-size:12px;font-weight: 400;margin-left: 367px">2.每份支撑材料大小不超过50M，要求rar格式；存放支撑材料的文件夹（压缩包）以【省份-二级标题】命名，如知识竞赛支撑材料应存放在【xx省-知识竞赛】文件夹（压缩包）中</p>--%>


                    <div style="background-color: #fff; width: 100%">
                        <h1 style="font-size: 14px;color: #31688F;font-weight:400;height: 33px;line-height:33px;background: #F8F8F8;">（一）案卷评查</h1>
                        <div style="margin-top: 20px">
                            <label style="font-size: 14px; color: #333333; font-weight: 400"><span
                                    style="color: red;size: 60px">*</span>是否组织开展案卷评查：</label>
                            <select style="width:380px;margin-right:5px;" name="is_casereview" id="is_casereview">
                                <option value="">--请选择--</option>
                                <option value="1"
                                        <c:if test="${provinceSelectionUnit.isCarryOut eq '1'}">selected</c:if>>是
                                </option>
                                <option value="0"
                                        <c:if test="${provinceSelectionUnit.isCarryOut eq '0'}">selected</c:if>>否
                                </option>
                            </select>
                        </div>
                        <div style="margin-top: 20px">
                            <label style="font-size: 14px; color: #333333; font-weight: 400"><span
                                    style="color: red;size: 60px" class="isCaseShow">*</span>是否达到要求数量：</label>
                            <select style="width:380px;margin-right:5px;" name="is_meet_requirenum" id="is_meet_requirenum">
                                <option value="">--请选择--</option>
                                <option value="1"
                                        <c:if test="${provinceSelectionUnit.isCarryOut eq '1'}">selected</c:if>>是
                                </option>
                                <option value="0"
                                        <c:if test="${provinceSelectionUnit.isCarryOut eq '0'}">selected</c:if>>否
                                </option>
                            </select>
                        </div>
                        <div style="margin-top: 20px">
                            <label style="font-size: 14px; color: #333333; font-weight: 400"><span
                                    style="color: red;size: 60px" class="isCaseShow" >*</span>是否形成评查工作总结或向下通报，反馈评查情况：</label>
                            <select style="width:380px;margin-right:5px;" name="is_feedback" id="is_feedback">
                                <option value="">--请选择--</option>
                                <option value="1"
                                        <c:if test="${provinceSelectionUnit.isCarryOut eq '1'}">selected</c:if>>是
                                </option>
                                <option value="0"
                                        <c:if test="${provinceSelectionUnit.isCarryOut eq '0'}">selected</c:if>>否
                                </option>
                            </select>
                        </div>
                        <div style="margin-top: 20px">
                            <label style="font-size: 14px; color: #333333; font-weight: 400"><span
                                    style="color: red;size: 60px" class="isCaseShow">*</span>是否形成案卷集或典型案例汇编：</label>
                            <select style="width:380px;margin-right:5px;" name="is_casecollection" id="is_casecollection">
                                <option value="">--请选择--</option>
                                <option value="1"
                                        <c:if test="${provinceSelectionUnit.isCarryOut eq '1'}">selected</c:if>>是
                                </option>
                                <option value="0"
                                        <c:if test="${provinceSelectionUnit.isCarryOut eq '0'}">selected</c:if>>否
                                </option>
                            </select>
                        </div>
                        <div style="margin-left:60px;margin-top: 20px;display: flex;width: calc(100% - 200px)">
                            <label style="font-size: 14px; color: #333333; font-weight: 400"><span
                                    style="color: red;size: 60px" class="isCaseShow">*</span>评查方案：</label>
                            <span id="uploadT4"
                                  <c:if test="${provinceSelectionUnit.carryOutFileName!='' && provinceSelectionUnit.carryOutFileName!=null }">style='display:none'</c:if>
                            >
<%--                                style="width:calc(100%-224px)"--%>
								<c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                    <input type="file" id="pingchaFile" value="文件上传">
                                    <input type="hidden" class="form-control" name="pingcha_urlname"
                                           id="pingcha_urlname" value="${provinceSelectionUnit.carryOutFileName}">
                                    <input type="hidden" class="form-control" name="pingcha_url"
                                           id="pingcha_url" value="${provinceSelectionUnit.carryOutFileUrl}">
                                </c:if>
						   </span>
                            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                <input id="reButton4"
                                       <c:if test="${provinceSelectionUnit.carryOutFileName=='' || provinceSelectionUnit.carryOutFileName==null }">style='display:none'</c:if>
                                       class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                            </c:if>
                        </div>
                        <div id="wsc4" style="margin-left:355px;width: 380px;">
                            <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">
                                <c:choose>
                                    <c:when test="${provinceSelectionUnit.carryOutFileName!='' && provinceSelectionUnit.carryOutFileName!=null }">
                                    <span style=" margin-right: 8px; padding-top: 4px;">
<%--                                                ${provinceSelectionUnit.carryOutFileName}--%>
                                         <a href="javascript:void(0)"
                                            onclick="downloadFile('${provinceSelectionUnit.carryOutFileUrl}','${provinceSelectionUnit.carryOutFileName}')">
                                                 ${provinceSelectionUnit.carryOutFileName}
                                         </a>
                                    </span>
                                    </c:when>
                                </c:choose>
							</span>
                        </div>
                        <%--                        评查工作总结或通报--%>
                        <div style="margin-left: 60px; margin-top: 20px;display: flex;width: calc(100% - 200px)">
                            <label style="font-size: 14px; color: #333333; font-weight: 400"><span
                                    style="color: red;size: 60px" class="isCaseShow">*</span>评查工作总结或通报：</label>
                            <span id="uploadTr5"
                                  <c:if test="${provinceSelectionUnit.carryOutFileName!='' && provinceSelectionUnit.carryOutFileName!=null }">style='display:none'</c:if>
                            >
<%--                                style="width:calc(100%-224px)"--%>
								<c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                    <input type="file" id="worksummaryFile" value="文件上传">
                                    <input type="hidden" class="form-control" name="worksummary_urlname"
                                           id="worksummary_urlname" value="${provinceSelectionUnit.carryOutFileName}">
                                    <input type="hidden" class="form-control" name="worksummary_url"
                                           id="worksummary_url" value="${provinceSelectionUnit.carryOutFileUrl}">
                                </c:if>
						   </span>
                            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                <input id="reButton5"
                                       <c:if test="${provinceSelectionUnit.carryOutFileName=='' || provinceSelectionUnit.carryOutFileName==null }">style='display:none'</c:if>
                                       class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                            </c:if>
                        </div>
                        <div id="wsc5" style="margin-left:355px;width: 380px;">
                            <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">
                                <c:choose>
                                    <c:when test="${provinceSelectionUnit.carryOutFileName!='' && provinceSelectionUnit.carryOutFileName!=null }">
                                    <span style=" margin-right: 8px; padding-top: 4px;">
<%--                                                ${provinceSelectionUnit.carryOutFileName}--%>
                                         <a href="javascript:void(0)"
                                            onclick="downloadFile('${provinceSelectionUnit.carryOutFileUrl}','${provinceSelectionUnit.carryOutFileName}')">
                                                 ${provinceSelectionUnit.carryOutFileName}
                                         </a>
                                    </span>
                                    </c:when>
                                </c:choose>
							</span>
                        </div>
                        <%--                        案卷集或典型案例汇编--%>
                        <div style="margin-left: 60px; margin-top: 20px;display: flex;width: calc(100% - 200px)">
                            <label style="font-size: 14px; color: #333333; font-weight: 400"><span
                                    style="color: red;size: 60px" class="isCaseShow">*</span>案卷集或典型案例汇编：</label>
                            <span id="uploadTr6"
                                  <c:if test="${provinceSelectionUnit.carryOutFileName!='' && provinceSelectionUnit.carryOutFileName!=null }">style='display:none'</c:if>
                            >
<%--                                style="width:calc(100%-224px)"--%>
								<c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                    <input type="file" id="caseinfoFile" value="文件上传">
                                    <input type="hidden" class="form-control" name="caseinfo_urlname"
                                           id="caseinfo_urlname" value="${provinceSelectionUnit.carryOutFileName}">
                                    <input type="hidden" class="form-control" name="caseinfo_url"
                                           id="caseinfo_url" value="${provinceSelectionUnit.carryOutFileUrl}">
                                </c:if>
						   </span>
                            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                <input id="reButton6"
                                       <c:if test="${provinceSelectionUnit.carryOutFileName=='' || provinceSelectionUnit.carryOutFileName==null }">style='display:none'</c:if>
                                       class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                            </c:if>
                        </div>
                        <div id="wsc6" style="margin-left:355px;width: 380px;">
                            <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">
                                <c:choose>
                                    <c:when test="${provinceSelectionUnit.carryOutFileName!='' && provinceSelectionUnit.carryOutFileName!=null }">
                                    <span style=" margin-right: 8px; padding-top: 4px;">
<%--                                                ${provinceSelectionUnit.carryOutFileName}--%>
                                         <a href="javascript:void(0)"
                                            onclick="downloadFile('${provinceSelectionUnit.carryOutFileUrl}','${provinceSelectionUnit.carryOutFileName}')">
                                                 ${provinceSelectionUnit.carryOutFileName}
                                         </a>
                                    </span>
                                    </c:when>
                                </c:choose>
							</span>
                        </div>
                    </div>

                    <div style="background-color: #fff;width: 100%">
                        <h1 style="font-size: 14px;color: #31688F;font-weight:400;height: 33px;line-height:33px;background: #F8F8F8;">（二）查办大案要案情况<span style="color: red; font-size: 14px">（不含被部采纳作为典型案例发布的案件）</span></h1>
                        <div style="margin-left:60px;margin-top: 20px;display: flex;width: calc(100% - 200px)">
                            <label style="font-size: 14px; color: #333333; font-weight: 400"><span
                                    style="color: red;size: 60px">*</span>案件办理情况说明：</label>
                            <span id="uploadTr7"
                                  <c:if test="${provinceSelectionUnit.carryOutFileName!='' && provinceSelectionUnit.carryOutFileName!=null }">style='display:none'</c:if>
                            >
<%--                                style="width:calc(100%-224px)"--%>
								<c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                    <input type="file" id="carryOutFile" value="文件上传">
                                    <input type="hidden" class="form-control" name="case_end_urlname"
                                           id="case_end_urlname" value="${provinceSelectionUnit.carryOutFileName}">
                                    <input type="hidden" class="form-control" name="case_end_url"
                                           id="case_end_url" value="${provinceSelectionUnit.carryOutFileUrl}">
                                </c:if>
						   </span>
                            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                <input id="reButton7"
                                       <c:if test="${provinceSelectionUnit.carryOutFileName=='' || provinceSelectionUnit.carryOutFileName==null }">style='display:none'</c:if>
                                       class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                            </c:if>
                        </div>
                        <p style="color: #FB3301;width: 526px;font-size:12px;line-height: 20px;font-weight: 400;margin-left: 600px">体现各类大案要案查办数量、案情简介。</p>
                        <div id="wsc7" style="margin-left:355px;width: 380px;">
                            <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">
                                <c:choose>
                                    <c:when test="${provinceSelectionUnit.carryOutFileName!='' && provinceSelectionUnit.carryOutFileName!=null }">
                                    <span style=" margin-right: 8px; padding-top: 4px;">
<%--                                                ${provinceSelectionUnit.carryOutFileName}--%>
                                         <a href="javascript:void(0)"
                                            onclick="downloadFile('${provinceSelectionUnit.carryOutFileUrl}','${provinceSelectionUnit.carryOutFileName}')">
                                                 ${provinceSelectionUnit.carryOutFileName}
                                         </a>
                                    </span>
                                    </c:when>
                                </c:choose>
							</span>
                        </div>
                        <div style="margin-top: 20px">
                            <label style="font-size: 14px; color: #333333; font-weight: 400"><span
                                    style="color: red;size: 60px" class="iscarryOut">*</span>查办大案要案情况类型：</label>
                            <select style="width:380px;margin-right:5px;" name="case_end_type" id="case_end_type">
                                <option value="">--请选择--</option>
                                <option value="1"
                                        <c:if test="${provinceSelectionUnit.orgNormCreation eq '1'}">selected</c:if>>
                                    非现场手段查处排污许可违法案件
                                </option>
                                <option value="2"
                                        <c:if test="${provinceSelectionUnit.orgNormCreation eq '2'}">selected</c:if>>
                                    查处新法规、新领域、新划转职能违法行为案件的
                                </option>
                                <option value="3"
                                        <c:if test="${provinceSelectionUnit.orgNormCreation eq '3'}">selected</c:if>>
                                    办理跨流域、涉饮用水源地及部审批的建设项目违法案件或综合运用配套办法的案件
                                </option>
                                <option value="4"
                                        <c:if test="${provinceSelectionUnit.orgNormCreation eq '4'}">selected</c:if>>
                                    会同公安机关等部门查处的案情重大、影响恶劣、后果严重的环境犯罪案件
                                </option>
                            </select>
                            <select style="width:380px;margin-right:5px;" name="isCaseTypeShow" id="isCaseTypeShow">
                                <option value="">--请选择--</option>
                                <option value="1"
                                        <c:if test="${provinceSelectionUnit.isCarryOut eq '1'}">selected</c:if>>土壤污染
                                </option>
                                <option value="0"
                                        <c:if test="${provinceSelectionUnit.isCarryOut eq '0'}">selected</c:if>>温室气体排放
                                </option>
                                <option value="2"
                                        <c:if test="${provinceSelectionUnit.isCarryOut eq '0'}">selected</c:if>>新化学物质
                                </option>
                                <option value="3"
                                        <c:if test="${provinceSelectionUnit.isCarryOut eq '0'}">selected</c:if>>消耗臭氧层物质（ODS（）
                                </option>
                                <option value="4"
                                        <c:if test="${provinceSelectionUnit.isCarryOut eq '0'}">selected</c:if>>新法、新领域、新划转职能违法行为案件
                                </option>
                            </select>
                            <select style="width:380px;margin-right:5px;" name="isCaseTypeShow2" id="isCaseTypeShow2">
                                <option value="">--请选择--</option>
                                <option value="1"
                                        <c:if test="${provinceSelectionUnit.isCarryOut eq '1'}">selected</c:if>>跨区域跨流域违法案件
                                </option>
                                <option value="0"
                                        <c:if test="${provinceSelectionUnit.isCarryOut eq '0'}">selected</c:if>>涉饮用水源地
                                </option>
                                <option value="2"
                                        <c:if test="${provinceSelectionUnit.isCarryOut eq '0'}">selected</c:if>>部级审批的建设项目
                                </option>
                                <option value="3"
                                        <c:if test="${provinceSelectionUnit.isCarryOut eq '0'}">selected</c:if>>综合运用四个配套办法“组合拳“（至少采用2种及以上配套办法）
                                </option>
                            </select>
                        </div>

                        <div style="margin-left: 60px; margin-top: 20px;display: flex;width: calc(100% - 200px)">
                            <label style="font-size: 14px; color: #333333; font-weight: 400"><span
                                    style="color: red;size: 60px" class="iscarryOut">*</span>查办大案要案支撑材料：</label>
                            <span id="uploadTr8"
                                  <c:if test="${provinceSelectionUnit.orgNormFileName!='' && provinceSelectionUnit.orgNormFileName!=null }">style='display:none'</c:if>
                            >
<%--                                style="width:calc(100%-224px)"--%>
								<c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                    <input type="file" id="caseEndFile" value="文件上传">
                                    <input type="hidden" class="form-control" name="caseEndsupUrlname"
                                           id="caseEndsupUrlname_Filename" value="${provinceSelectionUnit.orgNormFileName}">
                                    <input type="hidden" class="form-control" name="caseEndsupUrl"
                                           id="caseEndsupUrl_Fileurl" value="${provinceSelectionUnit.orgNormFileUrl}">
                                </c:if>
						   </span>
                            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                <input id="reButton8"
                                       <c:if test="${provinceSelectionUnit.orgNormFileName=='' || provinceSelectionUnit.orgNormFileName==null }">style='display:none'</c:if>
                                       class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                            </c:if>
                        </div>
                        <p style="color: #FB3301;font-size:12px;line-height: 20px;font-weight: 400;margin-left: 600px">案件处罚决定书或移送决定书扫描件。其中，如涉及环境犯罪的，需提供判决书或联合挂牌文书等。</p>
                        <div id="wsc8" style="margin-left:357px;width: 380px;margin-top: 14px;border-radius: 3px;">
                             <span style="font-size: 16px;margin-top: 10px; margin-left: -4px">
                                  <c:choose>
                                      <c:when test="${provinceSelectionUnit.orgNormFileName!='' && provinceSelectionUnit.orgNormFileName!=null }">
                                            <span style=" margin-right: 8px; padding-top: 4px;">
    <%--                                                ${provinceSelectionUnit.orgNormFileName}--%>
                                                <a href="javascript:void(0)"
                                                   onclick="downloadFile('${provinceSelectionUnit.orgNormFileUrl}','${provinceSelectionUnit.orgNormFileName}')">
                                                        ${provinceSelectionUnit.orgNormFileName}
                                                </a>
                                            </span>
                                      </c:when>
                                  </c:choose>
							</span>
                        </div>
                    </div>




                    <%--执法事项目录--%>
                    <%--                    <div style=" padding: 20px 0;width: 100%">--%>
                    <%--                        <h1 style="font-size: 14px;color: #31688F;font-weight:400;height: 33px;line-height:33px;background: #F8F8F8;">（四）执法事项目录</h1>--%>
                    <%--                        <div style="margin-left: 283px; margin-top: 20px">--%>
                    <%--                            <span style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
                    <%--                                    style="color: red;size: 60px">*</span>是否制定：</span>--%>
                    <%--                            <select style="width:380px;margin-right:5px;height: 39px" name="isZfEnact" id="isZfEnact">--%>
                    <%--                                <option value="">--请选择--</option>--%>
                    <%--                                <option value="1"--%>
                    <%--                                        <c:if test="${provinceSelectionUnit.isZfEnact eq '1'}">selected</c:if>>--%>
                    <%--                                    是--%>
                    <%--                                </option>--%>
                    <%--                                <option value="0"--%>
                    <%--                                        <c:if test="${provinceSelectionUnit.isZfEnact eq '0'}">selected</c:if>>--%>
                    <%--                                    否--%>
                    <%--                                </option>--%>
                    <%--                            </select>--%>
                    <%--                        </div>--%>
                    <%--                        <div style="margin-left: 200px; margin-top: 20px;display: flex;width: calc(100% - 200px)">--%>
                    <%--                            <span style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
                    <%--                                    style="color: red;size: 60px">*</span>执法事项目录支撑材料：</span>--%>

                    <%--                            <span id="uploadTr15"--%>
                    <%--                                  <c:if test="${provinceSelectionUnit.zfItemFileName!='' && provinceSelectionUnit.zfItemFileName!=null }">style='display:none'</c:if>--%>
                    <%--                                  >--%>
                    <%--&lt;%&ndash;                                                        style="width: calc(100% - 224px)&ndash;%&gt;--%>
                    <%--										<c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
                    <%--                                            <input type="file" id="zfItemFile" value="文件上传">--%>
                    <%--                                            <input type="hidden" class="form-control" name="zfItemFileName"--%>
                    <%--                                                   id="zfItemFile_Filename"--%>
                    <%--                                                   value="${provinceSelectionUnit.zfItemFileName}">--%>
                    <%--                                            <input type="hidden" class="form-control" name="zfItemFileUrl"--%>
                    <%--                                                   id="zfItemFile_Fileurl"--%>
                    <%--                                                   value="${provinceSelectionUnit.zfItemFileUrl}">--%>
                    <%--                                        </c:if>--%>
                    <%--					                </span>--%>
                    <%--                            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
                    <%--                                <input  id="reButton15"--%>
                    <%--                                       <c:if test="${provinceSelectionUnit.zfItemFileName=='' || provinceSelectionUnit.zfItemFileName==null }">style='display:none'</c:if>--%>
                    <%--                                       class="btn btn-danger btn-xs" type="button" value="重新上传"/>--%>
                    <%--                            </c:if>--%>
                    <%--                        </div>--%>
                    <%--                        <div id="wsc15" style="margin-left:357px;width: 380px;margin-top: 14px;border-radius: 3px;">--%>
                    <%--                            <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">--%>
                    <%--                            <c:choose>--%>
                    <%--                                <c:when test="${provinceSelectionUnit.zfItemFileName!='' && provinceSelectionUnit.zfItemFileName!=null }">--%>
                    <%--                                   <span style=" margin-right: 8px; padding-top: 4px;">--%>
                    <%--&lt;%&ndash;                                                   ${provinceSelectionUnit.zfItemFileName}&ndash;%&gt;--%>
                    <%--                                       <a href="javascript:void(0)"--%>
                    <%--                                          onclick="downloadFile('${provinceSelectionUnit.zfItemFileUrl}','${provinceSelectionUnit.zfItemFileName}')">--%>
                    <%--                                               ${provinceSelectionUnit.zfItemFileName}--%>
                    <%--                                       </a>--%>
                    <%--                                   </span>--%>
                    <%--                                </c:when>--%>
                    <%--                            </c:choose>--%>
                    <%--                            </span>--%>
                    <%--                        </div>--%>
                    <%--                    </div>--%>
                    <%--监督执法正面清单制度--%>
                    <%--                    <div style=" padding: 20px 0;width: 100%">--%>
                    <%--                        <h1 style="font-size: 14px;color: #31688F;font-weight:400;height: 33px;line-height:33px;background: #F8F8F8;">（五）监督执法正面清单制度</h1>--%>
                    <%--                        <div style="margin-left: 283px; margin-top: 20px">--%>
                    <%--                            <span style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
                    <%--                                    style="color: red;size: 60px">*</span>是否制定：</span>--%>
                    <%--                            <select style="width:380px;margin-right:5px;" name="isJdEnact" id="isJdEnact">--%>
                    <%--                                <option value="">--请选择--</option>--%>
                    <%--                                <option value="1"--%>
                    <%--                                        <c:if test="${provinceSelectionUnit.isJdEnact eq '1'}">selected</c:if>>--%>
                    <%--                                    是--%>
                    <%--                                </option>--%>
                    <%--                                <option value="0"--%>
                    <%--                                        <c:if test="${provinceSelectionUnit.isJdEnact eq '0'}">selected</c:if>>--%>
                    <%--                                    否--%>
                    <%--                                </option>--%>
                    <%--                            </select>--%>
                    <%--                        </div>--%>
                    <%--                        <div style="margin-left: 143px; margin-top: 20px;display: flex ;width: calc(100% - 200px)">--%>
                    <%--                            <span style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
                    <%--                                    style="color: red;size: 60px">*</span>监督执法正面清单制度支撑材料：</span>--%>
                    <%--                            <span id="uploadTr16"--%>
                    <%--                                  <c:if test="${provinceSelectionUnit.jdLawFileName!='' && provinceSelectionUnit.jdLawFileName!=null }">style='display:none'</c:if>--%>
                    <%--                            >--%>
                    <%--&lt;%&ndash;                                style='width:calc(100%-224px)'&ndash;%&gt;--%>
                    <%--										<c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
                    <%--                                            <input type="file" id="jdLawFile" value="文件上传">--%>
                    <%--                                            <input type="hidden" class="form-control" name="jdLawFileName"--%>
                    <%--                                                   id="jdLawFile_Filename"--%>
                    <%--                                                   value="${provinceSelectionUnit.jdLawFileName}">--%>
                    <%--                                            <input type="hidden" class="form-control" name="jdLawFileUrl"--%>
                    <%--                                                   id="jdLawFile_Fileurl" value="${provinceSelectionUnit.jdLawFileUrl}">--%>
                    <%--                                        </c:if>--%>
                    <%--					            </span>--%>
                    <%--                            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
                    <%--                                <input  id="reButton16"--%>
                    <%--                                       <c:if test="${provinceSelectionUnit.jdLawFileName=='' || provinceSelectionUnit.jdLawFileName==null }">style='display:none'</c:if>--%>
                    <%--                                       class="btn btn-danger btn-xs" type="button" value="重新上传"/>--%>
                    <%--                            </c:if>--%>
                    <%--                        </div>--%>
                    <%--                        <div id="wsc16" style="margin-left:357px;width: 380px;margin-top: 14px;border-radius: 3px;">--%>
                    <%--                            <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">--%>
                    <%--                                <c:choose>--%>
                    <%--                                    <c:when test="${provinceSelectionUnit.jdLawFileName!='' && provinceSelectionUnit.jdLawFileName!=null }">--%>
                    <%--                                       <span style=" margin-right: 8px; padding-top: 4px;">--%>
                    <%--&lt;%&ndash;                                                   ${provinceSelectionUnit.jdLawFileName}&ndash;%&gt;--%>
                    <%--                                           <a href="javascript:void(0)"--%>
                    <%--                                              onclick="downloadFile('${provinceSelectionUnit.jdLawFileUrl}','${provinceSelectionUnit.jdLawFileName}')">--%>
                    <%--                                                   ${provinceSelectionUnit.jdLawFileName}--%>
                    <%--                                           </a>--%>
                    <%--                                       </span>--%>
                    <%--                                    </c:when>--%>
                    <%--                                </c:choose>--%>
                    <%--                            </span>--%>
                    <%--                        </div>--%>
                    <%--                    </div>--%>
                    <%--非现场监管程序规范--%>
                    <%--                    <div style=" padding: 20px 0;width: 100%">--%>
                    <%--                        <h1 style="font-size: 14px;color: #31688F;font-weight:400;height: 33px;line-height:33px;background: #F8F8F8;">（六）非现场监管程序规范</h1>--%>
                    <%--                        <div style="margin-left: 283px; margin-top: 20px">--%>
                    <%--                            <span style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
                    <%--                                    style="color: red;size: 60px">*</span>是否制定：</span>--%>
                    <%--                            <select style="width:380px;margin-right:5px;" name="isFxcEnact" id="isFxcEnact">--%>
                    <%--                                <option value="">--请选择--</option>--%>
                    <%--                                <option value="1"--%>
                    <%--                                        <c:if test="${provinceSelectionUnit.isFxcEnact eq '1'}">selected</c:if>>--%>
                    <%--                                    是--%>
                    <%--                                </option>--%>
                    <%--                                <option value="0"--%>
                    <%--                                        <c:if test="${provinceSelectionUnit.isFxcEnact eq '0'}">selected</c:if>>--%>
                    <%--                                    否--%>
                    <%--                                </option>--%>
                    <%--                            </select>--%>
                    <%--                        </div>--%>
                    <%--                        <div style="margin-left: 156px; margin-top: 20px;display: flex;width: calc(100% - 200px)">--%>
                    <%--                            <span style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
                    <%--                                    style="color: red;size: 60px">*</span>非现场监管程序规范支撑材料：</span>--%>
                    <%--                            <span id="uploadTr17"--%>
                    <%--                                  <c:if test="${provinceSelectionUnit.fxcRoutineFileName!='' && provinceSelectionUnit.fxcRoutineFileName!=null }">style='display:none'</c:if>--%>
                    <%--                                  >--%>
                    <%--&lt;%&ndash;                                                        style="width: calc(100% - 224px)&ndash;%&gt;--%>
                    <%--										<c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
                    <%--                                            <input type="file" id="fxcRoutineFile" value="文件上传">--%>
                    <%--                                            <input type="hidden" class="form-control" name="fxcRoutineFileName"--%>
                    <%--                                                   id="fxcRoutineFile_Filename"--%>
                    <%--                                                   value="${provinceSelectionUnit.fxcRoutineFileName}">--%>
                    <%--                                            <input type="hidden" class="form-control" name="fxcRoutineFileUrl"--%>
                    <%--                                                   id="fxcRoutineFile_Fileurl"--%>
                    <%--                                                   value="${provinceSelectionUnit.fxcRoutineFileUrl}">--%>
                    <%--                                        </c:if>--%>
                    <%--					            </span>--%>
                    <%--                            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
                    <%--                                <input  id="reButton17"--%>
                    <%--                                       <c:if test="${provinceSelectionUnit.fxcRoutineFileName=='' || provinceSelectionUnit.fxcRoutineFileName==null }">style='display:none'</c:if>--%>
                    <%--                                       class="btn btn-danger btn-xs" type="button" value="重新上传"/>--%>
                    <%--                            </c:if>--%>
                    <%--                        </div>--%>
                    <%--                        <div id="wsc17" style="margin-left:357px;width: 380px;margin-top: 14px;border-radius: 3px;">--%>
                    <%--                            <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">--%>
                    <%--                                <c:choose>--%>
                    <%--                                    <c:when test="${provinceSelectionUnit.fxcRoutineFileName!='' && provinceSelectionUnit.fxcRoutineFileName!=null }">--%>
                    <%--                                       <span style=" margin-right: 8px; padding-top: 4px;">--%>
                    <%--&lt;%&ndash;                                                   ${provinceSelectionUnit.fxcRoutineFileName}&ndash;%&gt;--%>
                    <%--                                           <a href="javascript:void(0)"--%>
                    <%--                                              onclick="downloadFile('${provinceSelectionUnit.fxcRoutineFileUrl}','${provinceSelectionUnit.fxcRoutineFileName}')">--%>
                    <%--                                                   ${provinceSelectionUnit.fxcRoutineFileName}--%>
                    <%--                                           </a>--%>
                    <%--                                       </span>--%>
                    <%--                                    </c:when>--%>
                    <%--                                </c:choose>--%>
                    <%--                            </span>--%>
                    <%--                        </div>--%>
                    <%--                    </div>--%>
                    <%--履职责任制度--%>
                    <%--                    <div style=" padding: 20px 0;width: 100%">--%>
                    <%--                        <h1 style="font-size: 14px;color: #31688F;font-weight:400;height: 33px;line-height:33px;background: #F8F8F8;">（七）履职责任制度</h1>--%>
                    <%--                        <div style="margin-left: 283px; margin-top: 20px">--%>
                    <%--                            <span style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
                    <%--                                    style="color: red;size: 60px">*</span>是否制定：</span>--%>
                    <%--                            <select style="width:380px;margin-right:5px;" name="isLzEnact" id="isLzEnact">--%>
                    <%--                                <option value="">--请选择--</option>--%>
                    <%--                                <option value="1"--%>
                    <%--                                        <c:if test="${provinceSelectionUnit.isLzEnact eq '1'}">selected</c:if>>--%>
                    <%--                                    是--%>
                    <%--                                </option>--%>
                    <%--                                <option value="0"--%>
                    <%--                                        <c:if test="${provinceSelectionUnit.isLzEnact eq '0'}">selected</c:if>>--%>
                    <%--                                    否--%>
                    <%--                                </option>--%>
                    <%--                            </select>--%>
                    <%--                        </div>--%>
                    <%--                        <div style="margin-left: 200px; margin-top: 20px; display: flex;width: calc(100% - 200px)">--%>
                    <%--                            <span style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
                    <%--                                    style="color: red;size: 60px">*</span>履职责任制度支撑材料：</span>--%>

                    <%--                            <span id="uploadTr18"--%>
                    <%--                                  <c:if test="${provinceSelectionUnit.lzDutyFileName!='' && provinceSelectionUnit.lzDutyFileName!=null }">style='display:none'</c:if>--%>
                    <%--                                  >--%>
                    <%--&lt;%&ndash;                                                        style="width: calc(100% - 224px)&ndash;%&gt;--%>
                    <%--										<c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
                    <%--                                            <input type="file" id="lzDutyFile" value="文件上传">--%>
                    <%--                                            <input type="hidden" class="form-control" name="lzDutyFileName"--%>
                    <%--                                                   id="lzDutyFile_Filename"--%>
                    <%--                                                   value="${provinceSelectionUnit.lzDutyFileName}">--%>
                    <%--                                            <input type="hidden" class="form-control" name="lzDutyFileUrl"--%>
                    <%--                                                   id="lzDutyFile_Fileurl"--%>
                    <%--                                                   value="${provinceSelectionUnit.lzDutyFileUrl}">--%>
                    <%--                                        </c:if>--%>
                    <%--					            </span>--%>
                    <%--                            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
                    <%--                                <input  id="reButton18"--%>
                    <%--                                       <c:if test="${provinceSelectionUnit.lzDutyFileName=='' || provinceSelectionUnit.lzDutyFileName==null }">style='display:none'</c:if>--%>
                    <%--                                       class="btn btn-danger btn-xs" type="button" value="重新上传"/>--%>
                    <%--                            </c:if>--%>
                    <%--                        </div>--%>
                    <%--                        <div id="wsc18" style="margin-left:357px;width: 380px;margin-top: 14px;border-radius: 3px;">--%>
                    <%--                            <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">--%>
                    <%--                                <c:choose>--%>
                    <%--                                    <c:when test="${provinceSelectionUnit.lzDutyFileName!='' && provinceSelectionUnit.lzDutyFileName!=null }">--%>
                    <%--                                       <span style=" margin-right: 8px; padding-top: 4px;">--%>
                    <%--&lt;%&ndash;                                                   ${provinceSelectionUnit.lzDutyFileName}&ndash;%&gt;--%>
                    <%--                                           <a href="javascript:void(0)"--%>
                    <%--                                              onclick="downloadFile('${provinceSelectionUnit.lzDutyFileUrl}','${provinceSelectionUnit.lzDutyFileName}')">--%>
                    <%--                                                   ${provinceSelectionUnit.lzDutyFileName}--%>
                    <%--                                           </a>--%>
                    <%--                                       </span>--%>
                    <%--                                    </c:when>--%>
                    <%--                                </c:choose>--%>
                    <%--                            </span>--%>
                    <%--                        </div>--%>
                    <%--                    </div>--%>
                    <%--双随机、一公开监管制度--%>
                    <%--                    <div style="padding: 20px 0;width: 100%">--%>
                    <%--                        <h1 style="font-size: 14px;color: #31688F;font-weight:400;height: 33px;line-height:33px;background: #F8F8F8;">（八）“双随机、一公开”监管制度</h1>--%>
                    <%--                        <div class="shifou">--%>
                    <%--                            <span class="zhiding"><span style="color: red;size: 60px">*</span>是否制定：</span>--%>
                    <%--                            <select style="width:380px;margin-right:5px;" name="isSsjEnact" id="isSsjEnact">--%>
                    <%--                                <option value="">--请选择--</option>--%>
                    <%--                                <option value="1"--%>
                    <%--                                        <c:if test="${provinceSelectionUnit.isSsjEnact eq '1'}">selected</c:if>>--%>
                    <%--                                    是--%>
                    <%--                                </option>--%>
                    <%--                                <option value="0"--%>
                    <%--                                        <c:if test="${provinceSelectionUnit.isSsjEnact eq '0'}">selected</c:if>>--%>
                    <%--                                    否--%>
                    <%--                                </option>--%>
                    <%--                            </select>--%>
                    <%--                        </div>--%>
                    <%--                        <div class="shangchuan" style="width: calc(100% - 200px)">--%>
                    <%--                            <span class="zhiding"><span style="color: red;size: 60px">*</span>“双随机、一公开”监管制度支撑材料：</span>--%>

                    <%--                            <span id="uploadTr19"--%>
                    <%--                                  <c:if test="${provinceSelectionUnit.ssjOpenSystemName!='' && provinceSelectionUnit.ssjOpenSystemName!=null }">style='display:none'</c:if>--%>
                    <%--                                  >--%>
                    <%--&lt;%&ndash;                                                        style="width: calc(100% - 224px)&ndash;%&gt;--%>
                    <%--										<c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
                    <%--                                            <input type="file" id="ssjOpenSystem" value="文件上传">--%>
                    <%--                                            <input type="hidden" class="form-control" name="ssjOpenSystemName"--%>
                    <%--                                                   id="ssjOpenSystem_Filename"--%>
                    <%--                                                   value="${provinceSelectionUnit.ssjOpenSystemName}">--%>
                    <%--                                            <input type="hidden" class="form-control" name="ssjOpenSystemUrl"--%>
                    <%--                                                   id="ssjOpenSystem_Fileurl"--%>
                    <%--                                                   value="${provinceSelectionUnit.ssjOpenSystemUrl}">--%>
                    <%--                                        </c:if>--%>
                    <%--					            </span>--%>
                    <%--                            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
                    <%--                                <input  id="reButton19"--%>
                    <%--                                       <c:if test="${provinceSelectionUnit.ssjOpenSystemName=='' || provinceSelectionUnit.ssjOpenSystemName==null }">style='display:none'</c:if>--%>
                    <%--                                       class="btn btn-danger btn-xs" type="button" value="重新上传"/>--%>
                    <%--                            </c:if>--%>
                    <%--                        </div>--%>
                    <%--                        <div id="wsc19" style="margin-left:357px;width: 380px;margin-top: 14px;border-radius: 3px;">--%>
                    <%--                            <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">--%>
                    <%--                                <c:choose>--%>
                    <%--                                    <c:when test="${provinceSelectionUnit.ssjOpenSystemName!='' && provinceSelectionUnit.ssjOpenSystemName!=null }">--%>
                    <%--                                       <span style=" margin-right: 8px; padding-top: 4px;">--%>
                    <%--&lt;%&ndash;                                                   ${provinceSelectionUnit.ssjOpenSystemName}&ndash;%&gt;--%>
                    <%--                                           <a href="javascript:void(0)"--%>
                    <%--                                              onclick="downloadFile('${provinceSelectionUnit.ssjOpenSystemUrl}','${provinceSelectionUnit.ssjOpenSystemName}')">--%>
                    <%--                                                   ${provinceSelectionUnit.ssjOpenSystemName}--%>
                    <%--                                           </a>--%>
                    <%--                                       </span>--%>
                    <%--                                    </c:when>--%>
                    <%--                                </c:choose>--%>
                    <%--                            </span>--%>
                    <%--                        </div>--%>
                    <%--                    </div>--%>
                    <%--典型执法案例指导制度--%>
                    <%--                    <div style="padding: 20px 0;width: 100%">--%>
                    <%--                        <h1 style="font-size: 14px;color: #31688F;font-weight:400;height: 33px;line-height:33px;background: #F8F8F8;">（九）典型执法案例指导制度</h1>--%>
                    <%--                        <div class="shifou">--%>
                    <%--                            <span class="zhiding"><span style="color: red;size: 60px">*</span>是否制定：</span>--%>
                    <%--                            <select style="width:380px;margin-right:5px;" name="isDxLawEnact"--%>
                    <%--                                    id="isDxLawEnact">--%>
                    <%--                                <option value="">--请选择--</option>--%>
                    <%--                                <option value="1"--%>
                    <%--                                        <c:if test="${provinceSelectionUnit.isDxLawEnact eq '1'}">selected</c:if>>--%>
                    <%--                                    是--%>
                    <%--                                </option>--%>
                    <%--                                <option value="0"--%>
                    <%--                                        <c:if test="${provinceSelectionUnit.isDxLawEnact eq '0'}">selected</c:if>>--%>
                    <%--                                    否--%>
                    <%--                                </option>--%>
                    <%--                            </select>--%>
                    <%--                        </div>--%>
                    <%--                        <div class="shangchuan" style="margin-left:143px;width: calc(100% - 200px)">--%>
                    <%--                            <span class="zhiding"><span style="color: red;size: 60px">*</span>典型执法案例指导制度支撑材料：</span>--%>
                    <%--                            <span id="uploadTr20"--%>
                    <%--                                  <c:if test="${provinceSelectionUnit.dxLawFileName!='' && provinceSelectionUnit.dxLawFileName!=null }">style='display:none'</c:if>--%>
                    <%--                                  >--%>
                    <%--&lt;%&ndash;                                                        style="width: calc(100% - 224px)&ndash;%&gt;--%>
                    <%--										<c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
                    <%--                                            <input type="file" id="dxLawFile" value="文件上传">--%>
                    <%--                                            <input type="hidden" class="form-control" name="dxLawFileName"--%>
                    <%--                                                   id="dxLawFile_Filename"--%>
                    <%--                                                   value="${provinceSelectionUnit.dxLawFileName}">--%>
                    <%--                                            <input type="hidden" class="form-control" name="dxLawFileUrl"--%>
                    <%--                                                   id="dxLawFile_Fileurl" value="${provinceSelectionUnit.dxLawFileUrl}">--%>
                    <%--                                        </c:if>--%>
                    <%--					            </span>--%>
                    <%--                            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
                    <%--                                <input  id="reButton20"--%>
                    <%--                                       <c:if test="${provinceSelectionUnit.dxLawFileName=='' || provinceSelectionUnit.dxLawFileName==null }">style='display:none'</c:if>--%>
                    <%--                                       class="btn btn-danger btn-xs" type="button" value="重新上传"/>--%>
                    <%--                            </c:if>--%>
                    <%--                        </div>--%>
                    <%--                        <div id="wsc20" style="margin-left:357px;width: 380px;margin-top: 14px;border-radius: 3px;">--%>
                    <%--                            <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">--%>
                    <%--                                <c:choose>--%>
                    <%--                                    <c:when test="${provinceSelectionUnit.dxLawFileName!='' && provinceSelectionUnit.dxLawFileName!=null }">--%>
                    <%--                                       <span style=" margin-right: 8px; padding-top: 4px;">--%>
                    <%--&lt;%&ndash;                                                   ${provinceSelectionUnit.dxLawFileName}&ndash;%&gt;--%>
                    <%--                                           <a href="javascript:void(0)"--%>
                    <%--                                              onclick="downloadFile('${provinceSelectionUnit.dxLawFileUrl}','${provinceSelectionUnit.dxLawFileName}')">--%>
                    <%--                                                   ${provinceSelectionUnit.dxLawFileName}--%>
                    <%--                                           </a>--%>
                    <%--                                       </span>--%>
                    <%--                                    </c:when>--%>
                    <%--                                </c:choose>--%>
                    <%--                            </span>--%>
                    <%--                        </div>--%>
                    <%--                    </div>--%>
                    <%--执法监测工作机制--%>
                    <%--                    <div style="padding: 20px 0;width: 100%">--%>
                    <%--                        <h1 style="font-size: 14px;color: #31688F;font-weight:400;height: 33px;line-height:33px;background: #F8F8F8;">（十）执法监测工作机制</h1>--%>
                    <%--                        <div class="shifou">--%>
                    <%--                            <span class="zhiding"><span style="color: red;size: 60px">*</span>是否制定：</span>--%>
                    <%--                            <select name="isZfJcEnact" id="isZfJcEnact"--%>
                    <%--                                    style="width:380px;margin-right:5px;">--%>
                    <%--                                <option value="">--请选择--</option>--%>
                    <%--                                <option value="1"--%>
                    <%--                                        <c:if test="${provinceSelectionUnit.isZfJcEnact eq '1'}">selected</c:if>>--%>
                    <%--                                    是--%>
                    <%--                                </option>--%>
                    <%--                                <option value="0"--%>
                    <%--                                        <c:if test="${provinceSelectionUnit.isZfJcEnact eq '0'}">selected</c:if>>--%>
                    <%--                                    否--%>
                    <%--                                </option>--%>
                    <%--                            </select>--%>
                    <%--                        </div>--%>
                    <%--                        <div class="shangchuan" style="margin-left:171px;width: calc(100% - 200px)">--%>
                    <%--                            <span class="zhiding"><span style="color: red;size: 60px">*</span>执法监测工作机制支撑材料：</span>--%>
                    <%--                            <span id="uploadTr21"--%>
                    <%--                                  <c:if test="${provinceSelectionUnit.zfJcWorkFileName!='' && provinceSelectionUnit.zfJcWorkFileName!=null }">style='display:none'</c:if>--%>
                    <%--                                  >--%>
                    <%--&lt;%&ndash;                                style="width: calc(100% - 224px)&ndash;%&gt;--%>
                    <%--										<c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
                    <%--                                            <input type="file" id="zfJcWorkFile" value="文件上传">--%>
                    <%--                                            <input type="hidden" class="form-control" name="zfJcWorkFileName"--%>
                    <%--                                                   id="zfJcWorkFile_Filename"--%>
                    <%--                                                   value="${provinceSelectionUnit.zfJcWorkFileName}">--%>
                    <%--                                            <input type="hidden" class="form-control" name="zfJcWorkFileUrl"--%>
                    <%--                                                   id="zfJcWorkFile_Fileurl"--%>
                    <%--                                                   value="${provinceSelectionUnit.zfJcWorkFileUrl}">--%>
                    <%--                                        </c:if>--%>
                    <%--					            </span>--%>
                    <%--                            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
                    <%--                                <input  id="reButton21"--%>
                    <%--                                       <c:if test="${provinceSelectionUnit.zfJcWorkFileName=='' || provinceSelectionUnit.zfJcWorkFileName==null }">style='display:none'</c:if>--%>
                    <%--                                       class="btn btn-danger btn-xs" type="button" value="重新上传"/>--%>
                    <%--                            </c:if>--%>
                    <%--                        </div>--%>
                    <%--                        <div id="wsc21" style="margin-left:357px;width: 380px;margin-top: 14px;border-radius: 3px;">--%>
                    <%--                            <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">--%>
                    <%--                                <c:choose>--%>
                    <%--                                    <c:when test="${provinceSelectionUnit.zfJcWorkFileName!='' && provinceSelectionUnit.zfJcWorkFileName!=null }">--%>
                    <%--                                       <span style=" margin-right: 8px; padding-top: 4px;">--%>
                    <%--    &lt;%&ndash;                                               ${provinceSelectionUnit.zfJcWorkFileName}&ndash;%&gt;--%>
                    <%--                                           <a href="javascript:void(0)"--%>
                    <%--                                              onclick="downloadFile('${provinceSelectionUnit.zfJcWorkFileUrl}','${provinceSelectionUnit.zfJcWorkFileName}')">--%>
                    <%--                                                   ${provinceSelectionUnit.zfJcWorkFileName}--%>
                    <%--                                           </a>--%>
                    <%--                                       </span>--%>
                    <%--                                    </c:when>--%>
                    <%--                                </c:choose>--%>
                    <%--                            </span>--%>
                    <%--                        </div>--%>
                    <%--                    </div>--%>
                </div>
            </div>

            <%--            <div style=" padding:  2px 30px 26px 0;width: 100%">--%>
            <%--                <h1 style="font-size: 14px;color: #31688F;font-weight:400;height: 33px;line-height:33px;background: #F8F8F8;">（三）队伍建设与管理</h1>--%>
            <%--                <h3 style="margin-left: 219px;font-size:14px;color: #31688F;">一、规范化示范建设</h3>--%>
            <%--                <div>--%>
            <%--                    <h4 style="font-size:14px;font-weight: 400;margin-left: 246px;margin-top: 20px"><span--%>
            <%--                            style="color: red;size: 60px">*</span>规范化示范建设试点工作</h4>--%>

            <%--                    <div style="margin-left: 283px; margin-top: 20px">--%>
            <%--                                <span style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
            <%--                                        style="color: red;size: 60px">*</span>2021年是否出台：</span>--%>
            <%--                        <select style="width:380px;margin-right:5px;" name="isCustomization"--%>
            <%--                                id="isCustomization">--%>
            <%--                            <option value="">--请选择--</option>--%>
            <%--                            <option value="1"--%>
            <%--                                    <c:if test="${provinceSelectionUnit.isCustomization eq '1'}">selected</c:if>>--%>
            <%--                                是--%>
            <%--                            </option>--%>
            <%--                            <option value="0"--%>
            <%--                                    <c:if test="${provinceSelectionUnit.isCustomization eq '0'}">selected</c:if>>--%>
            <%--                                否--%>
            <%--                            </option>--%>
            <%--                        </select>--%>
            <%--                    </div>--%>

            <%--                    <div style="margin-left: 170px; margin-top: 20px;display: flex;width: calc(100% - 200px)">--%>
            <%--                                <span style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
            <%--                                        style="color: red;size: 60px">*</span>工作开展情况、取得成效等工作简报或报告：</span>--%>
            <%--                        <span id="uploadTr10"--%>
            <%--                              <c:if test="${provinceSelectionUnit.specificEnactFileName!='' && provinceSelectionUnit.specificEnactFileName!=null }">style='display:none'</c:if>--%>
            <%--                        >--%>
            <%--&lt;%&ndash;                                                            style="width: calc(100% - 224px)&ndash;%&gt;--%>
            <%--									<c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
            <%--                                        <input type="file" id="specificEnactFile" value="文件上传">--%>
            <%--                                        <input type="hidden" class="form-control" name="specificEnactFileName"--%>
            <%--                                               id="specificEnactFile_Filename"--%>
            <%--                                               value="${provinceSelectionUnit.specificEnactFileName}">--%>
            <%--                                        <input type="hidden" class="form-control" name="specificEnactFileUrl"--%>
            <%--                                               id="specificEnactFile_Fileurl"--%>
            <%--                                               value="${provinceSelectionUnit.specificEnactFileUrl}">--%>
            <%--                                    </c:if>--%>
            <%--					            </span>--%>
            <%--                        <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
            <%--                            <input id="reButton10"--%>
            <%--                                   <c:if test="${provinceSelectionUnit.specificEnactFileName=='' || provinceSelectionUnit.specificEnactFileName==null }">style='display:none'</c:if>--%>
            <%--                                   class="btn btn-danger btn-xs" type="button" value="重新上传"/>--%>
            <%--                        </c:if>--%>
            <%--                    </div>--%>
            <%--                    <div id="wsc10" style="margin-left:357px;width: 380px;margin-top: 14px;border-radius: 3px;">--%>
            <%--                                <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">--%>
            <%--                                    <c:choose>--%>
            <%--                                        <c:when test="${provinceSelectionUnit.specificEnactFileName!='' && provinceSelectionUnit.specificEnactFileName!=null }">--%>
            <%--                                           <span style=" margin-right: 8px; padding-top: 4px;">--%>
            <%--&lt;%&ndash;                                                       ${provinceSelectionUnit.specificEnactFileName}&ndash;%&gt;--%>
            <%--                                               <a href="javascript:void(0)"--%>
            <%--                                                  onclick="downloadFile('${provinceSelectionUnit.specificEnactFileUrl}','${provinceSelectionUnit.specificEnactFileName}')">--%>
            <%--                                                       ${provinceSelectionUnit.specificEnactFileName}--%>
            <%--                                               </a>--%>
            <%--                                           </span>--%>
            <%--                                        </c:when>--%>
            <%--                                    </c:choose>--%>
            <%--								</span>--%>
            <%--                    </div>--%>
            <%--                </div>--%>
            <%--                <div>--%>
            <%--                    <h4 style="font-size:14px;font-weight: 400;margin-left: 252px;margin-top: 33px">开展执法稽查</h4>--%>

            <%--                    <div style="margin-left: 283px; margin-top: 33px">--%>
            <%--                                <span style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
            <%--                                        style="color: red;size: 60px">*</span>是否开展：</span>--%>
            <%--                        <select style="width:380px;margin-right:5px;" name="isExecute"--%>
            <%--                                id="isExecute">--%>
            <%--                            <option value="">--请选择--</option>--%>
            <%--                            <option value="1"--%>
            <%--                                    <c:if test="${provinceSelectionUnit.isExecute eq '1'}">selected</c:if>>--%>
            <%--                                是--%>
            <%--                            </option>--%>
            <%--                            <option value="0"--%>
            <%--                                    <c:if test="${provinceSelectionUnit.isExecute eq '0'}">selected</c:if>>--%>
            <%--                                否--%>
            <%--                            </option>--%>
            <%--                        </select>--%>
            <%--                    </div>--%>

            <%--                    <div style="margin-left: 177px; margin-top: 20px;display: flex;width: calc(100% - 200px)">--%>
            <%--                                <span style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
            <%--                                        style="color: red;size: 60px">*</span>稽查计划：</span>--%>
            <%--                        <span id="uploadTr11"--%>
            <%--                              <c:if test="${provinceSelectionUnit.specificConditionFileName!='' && provinceSelectionUnit.specificConditionFileName!=null }">style='display:none'</c:if>--%>
            <%--                        >--%>
            <%--&lt;%&ndash;                                                            style="width: calc(100% - 224px)&ndash;%&gt;--%>
            <%--									<c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
            <%--                                        <input type="file" id="specificConditionFile" value="文件上传">--%>
            <%--                                        <input type="hidden" class="form-control" name="specificConditionFileName"--%>
            <%--                                               id="specificConditionFile_Filename"--%>
            <%--                                               value="${provinceSelectionUnit.specificConditionFileName}">--%>
            <%--                                        <input type="hidden" class="form-control" name="specificConditionFileUrl"--%>
            <%--                                               id="specificConditionFile_Fileurl"--%>
            <%--                                               value="${provinceSelectionUnit.specificConditionFileUrl}">--%>
            <%--                                    </c:if>--%>
            <%--					            </span>--%>
            <%--                        <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
            <%--                            <input  id="reButton11"--%>
            <%--                                    <c:if test="${provinceSelectionUnit.specificConditionFileName=='' || provinceSelectionUnit.specificConditionFileName==null }">style='display:none'</c:if>--%>
            <%--                                    class="btn btn-danger btn-xs" type="button" value="重新上传"/>--%>
            <%--                        </c:if>--%>
            <%--                    </div>--%>
            <%--                    <div id="wsc11" style="margin-left:357px;width: 380px;margin-top: 14px;border-radius: 3px;">--%>
            <%--                                <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">--%>
            <%--                                    <c:choose>--%>
            <%--                                        <c:when test="${provinceSelectionUnit.specificConditionFileName!='' && provinceSelectionUnit.specificConditionFileName!=null }">--%>
            <%--                                               <span style=" margin-right: 8px; padding-top: 4px;">--%>
            <%--    &lt;%&ndash;                                                   ${provinceSelectionUnit.specificConditionFileName}&ndash;%&gt;--%>
            <%--                                                   <a href="javascript:void(0)"--%>
            <%--                                                      onclick="downloadFile('${provinceSelectionUnit.specificConditionFileUrl}','${provinceSelectionUnit.specificConditionFileName}')">--%>
            <%--                                                           ${provinceSelectionUnit.specificConditionFileName}--%>
            <%--                                                   </a>--%>
            <%--                                               </span>--%>
            <%--                                        </c:when>--%>
            <%--                                    </c:choose>--%>
            <%--                                </span>--%>
            <%--                    </div>--%>

            <%--                    <div style="margin-left: 177px; margin-top: 20px;display: flex;width: calc(100% - 200px)">--%>
            <%--                                <span style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
            <%--                                        style="color: red;size: 60px">*</span>稽查工作报告：</span>--%>
            <%--                        <span id="uploadTr12"--%>
            <%--                              <c:if test="${provinceSelectionUnit.specificConditionFileName!='' && provinceSelectionUnit.specificConditionFileName!=null }">style='display:none'</c:if>--%>
            <%--                        >--%>
            <%--&lt;%&ndash;                                                            style="width: calc(100% - 224px)&ndash;%&gt;--%>
            <%--									<c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
            <%--                                        <input type="file" id="specificConditionFile" value="文件上传">--%>
            <%--                                        <input type="hidden" class="form-control" name="specificConditionFileName"--%>
            <%--                                               id="specificConditionFile_Filename"--%>
            <%--                                               value="${provinceSelectionUnit.specificConditionFileName}">--%>
            <%--                                        <input type="hidden" class="form-control" name="specificConditionFileUrl"--%>
            <%--                                               id="specificConditionFile_Fileurl"--%>
            <%--                                               value="${provinceSelectionUnit.specificConditionFileUrl}">--%>
            <%--                                    </c:if>--%>
            <%--					            </span>--%>
            <%--                        <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
            <%--                            <input  id="reButton12"--%>
            <%--                                    <c:if test="${provinceSelectionUnit.specificConditionFileName=='' || provinceSelectionUnit.specificConditionFileName==null }">style='display:none'</c:if>--%>
            <%--                                    class="btn btn-danger btn-xs" type="button" value="重新上传"/>--%>
            <%--                        </c:if>--%>
            <%--                    </div>--%>
            <%--                    <div id="wsc12" style="margin-left:357px;width: 380px;margin-top: 14px;border-radius: 3px;">--%>
            <%--                                <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">--%>
            <%--                                    <c:choose>--%>
            <%--                                        <c:when test="${provinceSelectionUnit.specificConditionFileName!='' && provinceSelectionUnit.specificConditionFileName!=null }">--%>
            <%--                                               <span style=" margin-right: 8px; padding-top: 4px;">--%>
            <%--    &lt;%&ndash;                                                   ${provinceSelectionUnit.specificConditionFileName}&ndash;%&gt;--%>
            <%--                                                   <a href="javascript:void(0)"--%>
            <%--                                                      onclick="downloadFile('${provinceSelectionUnit.specificConditionFileUrl}','${provinceSelectionUnit.specificConditionFileName}')">--%>
            <%--                                                           ${provinceSelectionUnit.specificConditionFileName}--%>
            <%--                                                   </a>--%>
            <%--                                               </span>--%>
            <%--                                        </c:when>--%>
            <%--                                    </c:choose>--%>
            <%--                                </span>--%>
            <%--                    </div>--%>


            <%--                    <h4 style="font-size:14px;font-weight: 400;margin-left: 252px;margin-top: 33px">统一证件、着装</h4>--%>

            <%--                    <div style="margin-left: 283px; margin-top: 33px">--%>
            <%--                                <span style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
            <%--                                        style="color: red;size: 60px">*</span>是否完成统一着装：</span>--%>
            <%--                        <select style="width:380px;margin-right:5px;" name="isExecute"--%>
            <%--                                id="">--%>
            <%--                            <option value="">--请选择--</option>--%>
            <%--                            <option value="1"--%>
            <%--                                    <c:if test="${provinceSelectionUnit.isExecute eq '1'}">selected</c:if>>--%>
            <%--                                是--%>
            <%--                            </option>--%>
            <%--                            <option value="0"--%>
            <%--                                    <c:if test="${provinceSelectionUnit.isExecute eq '0'}">selected</c:if>>--%>
            <%--                                否--%>
            <%--                            </option>--%>
            <%--                        </select>--%>
            <%--                    </div>--%>
            <%--                    <div style="margin-left: 283px; margin-top: 33px">--%>
            <%--                                <span style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
            <%--                                        style="color: red;size: 60px">*</span>是否完成统一证件：</span>--%>
            <%--                        <select style="width:380px;margin-right:5px;" name="isExecute"--%>
            <%--                                id="">--%>
            <%--                            <option value="">--请选择--</option>--%>
            <%--                            <option value="1"--%>
            <%--                                    <c:if test="${provinceSelectionUnit.isExecute eq '1'}">selected</c:if>>--%>
            <%--                                是--%>
            <%--                            </option>--%>
            <%--                            <option value="0"--%>
            <%--                                    <c:if test="${provinceSelectionUnit.isExecute eq '0'}">selected</c:if>>--%>
            <%--                                否--%>
            <%--                            </option>--%>
            <%--                        </select>--%>
            <%--                    </div>--%>
            <%--                    <div style="margin-left: 177px; margin-top: 20px;display: flex;width: calc(100% - 200px)">--%>
            <%--                                <span style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
            <%--                                        style="color: red;size: 60px">*</span>支撑材料：</span>--%>
            <%--                        <span id="uploadTr13"--%>
            <%--                              <c:if test="${provinceSelectionUnit.specificConditionFileName!='' && provinceSelectionUnit.specificConditionFileName!=null }">style='display:none'</c:if>--%>
            <%--                        >--%>
            <%--&lt;%&ndash;                                                            style="width: calc(100% - 224px)&ndash;%&gt;--%>
            <%--									<c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
            <%--                                        <input type="file" id="specificConditionFile" value="文件上传">--%>
            <%--                                        <input type="hidden" class="form-control" name="specificConditionFileName"--%>
            <%--                                               id="specificConditionFile_Filename"--%>
            <%--                                               value="${provinceSelectionUnit.specificConditionFileName}">--%>
            <%--                                        <input type="hidden" class="form-control" name="specificConditionFileUrl"--%>
            <%--                                               id="specificConditionFile_Fileurl"--%>
            <%--                                               value="${provinceSelectionUnit.specificConditionFileUrl}">--%>
            <%--                                    </c:if>--%>
            <%--					            </span>--%>
            <%--                        <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
            <%--                            <input  id="reButton13"--%>
            <%--                                    <c:if test="${provinceSelectionUnit.specificConditionFileName=='' || provinceSelectionUnit.specificConditionFileName==null }">style='display:none'</c:if>--%>
            <%--                                    class="btn btn-danger btn-xs" type="button" value="重新上传"/>--%>
            <%--                        </c:if>--%>
            <%--                    </div>--%>
            <%--                    <p style="color: #FB3301;width: 526px;font-size:12px;line-height: 20px;font-weight: 400;margin-left: 367px">统一着装、统一证件的完成情况报告，报告中需体现完成率、着装及持证情况等内容</p>--%>
            <%--                    <div id="wsc13" style="margin-left:357px;width: 380px;margin-top: 14px;border-radius: 3px;">--%>
            <%--                                <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">--%>
            <%--                                    <c:choose>--%>
            <%--                                        <c:when test="${provinceSelectionUnit.specificConditionFileName!='' && provinceSelectionUnit.specificConditionFileName!=null }">--%>
            <%--                                               <span style=" margin-right: 8px; padding-top: 4px;">--%>
            <%--    &lt;%&ndash;                                                   ${provinceSelectionUnit.specificConditionFileName}&ndash;%&gt;--%>
            <%--                                                   <a href="javascript:void(0)"--%>
            <%--                                                      onclick="downloadFile('${provinceSelectionUnit.specificConditionFileUrl}','${provinceSelectionUnit.specificConditionFileName}')">--%>
            <%--                                                           ${provinceSelectionUnit.specificConditionFileName}--%>
            <%--                                                   </a>--%>
            <%--                                               </span>--%>
            <%--                                        </c:when>--%>
            <%--                                    </c:choose>--%>
            <%--                                </span>--%>
            <%--                    </div>--%>
            <%--                </div>--%>

            <%--                &lt;%&ndash;人身安全保障&ndash;%&gt;--%>
            <%--                <div style="width: 100%">--%>
            <%--                    <h3 style="font-size:16px;color: #31688F;">二、人身安全保障</h3>--%>
            <%--                    <div>--%>
            <%--                        <h4 style="font-size:14px;font-weight: 400;margin-left: 252px;margin-top: 33px">制定情况</h4>--%>
            <%--                        <div style="margin-left: 283px; margin-top: 20px">--%>
            <%--                                    <span style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
            <%--                                            style="color: red;size: 60px">*</span>是否制定：</span>--%>
            <%--                            <select style="width:380px;margin-right:5px;" name="isEnact"--%>
            <%--                                    id="isEnact">--%>
            <%--                                <option value="">--请选择--</option>--%>
            <%--                                <option value="1"--%>
            <%--                                        <c:if test="${provinceSelectionUnit.isEnact eq '1'}">selected</c:if>>--%>
            <%--                                    是--%>
            <%--                                </option>--%>
            <%--                                <option value="0"--%>
            <%--                                        <c:if test="${provinceSelectionUnit.isEnact eq '0'}">selected</c:if>>--%>
            <%--                                    否--%>
            <%--                                </option>--%>
            <%--                            </select>--%>
            <%--                        </div>--%>

            <%--                        <div style="margin-left: 283px; margin-top: 20px">--%>
            <%--                                    <span style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
            <%--                                            style="color: red;size: 60px">*</span>文件类型：</span>--%>
            <%--                            <select style="width:380px;margin-right:5px;" name="rsFileType"--%>
            <%--                                    id="rsFileType">--%>
            <%--                                <option value="">--请选择--</option>--%>
            <%--                                <option value="1"--%>
            <%--                                        <c:if test="${provinceSelectionUnit.rsFileType eq '1'}">selected</c:if>>--%>
            <%--                                    正式发文--%>
            <%--                                </option>--%>
            <%--                                <option value="2"--%>
            <%--                                        <c:if test="${provinceSelectionUnit.rsFileType eq '2'}">selected</c:if>>--%>
            <%--                                    征求意见稿--%>
            <%--                                </option>--%>
            <%--                                <option value="3"--%>
            <%--                                        <c:if test="${provinceSelectionUnit.rsFileType eq '3'}">selected</c:if>>--%>
            <%--                                    无--%>
            <%--                                </option>--%>
            <%--                            </select>--%>
            <%--                        </div>--%>

            <%--                        <div style="margin-left: 283px; margin-top: 20px">--%>
            <%--                                    <span style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
            <%--                                            style="color: red;size: 60px">*</span>出台时间：</span>--%>
            <%--                            <input type="date" id="ctsj2" name="rsComeTime" height="39px"><br/>--%>
            <%--                        </div>--%>

            <%--                        <div style="margin-left: 143px; margin-top: 20px;display: flex;width: calc(100% - 200px)">--%>
            <%--                                    <span style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
            <%--                                            style="color: red;size: 60px">*</span>人身安全保障制定情况支撑材料：</span>--%>

            <%--                            <span id="uploadTr11"--%>
            <%--                                  <c:if test="${provinceSelectionUnit.rsAnQuanFileName!='' && provinceSelectionUnit.rsAnQuanFileName!=null }">style='display:none'</c:if>--%>
            <%--                            >--%>
            <%--&lt;%&ndash;                                                                style="width: calc(100% - 224px)&ndash;%&gt;--%>
            <%--												<c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
            <%--                                                    <input type="file" id="rsAnQuanFile" value="文件上传">--%>
            <%--                                                    <input type="hidden" class="form-control" name="rsAnQuanFileName"--%>
            <%--                                                           id="rsAnQuanFile_Filename"--%>
            <%--                                                           value="${provinceSelectionUnit.rsAnQuanFileName}">--%>
            <%--                                                    <input type="hidden" class="form-control" name="rsAnQuanFileUrl"--%>
            <%--                                                           id="rsAnQuanFile_Fileurl"--%>
            <%--                                                           value="${provinceSelectionUnit.rsAnQuanFileUrl}">--%>
            <%--                                                </c:if>--%>
            <%--										</span>--%>
            <%--                            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
            <%--                                <input  id="reButton11"--%>
            <%--                                        <c:if test="${provinceSelectionUnit.rsAnQuanFileName=='' || provinceSelectionUnit.rsAnQuanFileName==null }">style='display:none'</c:if>--%>
            <%--                                        class="btn btn-danger btn-xs" type="button" value="重新上传"/>--%>
            <%--                            </c:if>--%>
            <%--                        </div>--%>
            <%--                        <div id="wsc11"  style="margin-left:357px;width: 380px;margin-top: 14px;border-radius: 3px;">--%>
            <%--                                    <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">--%>
            <%--                                        <c:choose>--%>
            <%--                                            <c:when test="${provinceSelectionUnit.rsAnQuanFileName!='' && provinceSelectionUnit.rsAnQuanFileName!=null }">--%>
            <%--                                           <span style=" margin-right: 8px; padding-top: 4px;">--%>
            <%--&lt;%&ndash;                                                       ${provinceSelectionUnit.rsAnQuanFileName}&ndash;%&gt;--%>
            <%--                                               <a href="javascript:void(0)"--%>
            <%--                                                  onclick="downloadFile('${provinceSelectionUnit.rsAnQuanFileUrl}','${provinceSelectionUnit.rsAnQuanFileName}')">--%>
            <%--                                                       ${provinceSelectionUnit.rsAnQuanFileName}--%>
            <%--                                               </a>--%>
            <%--                                           </span>--%>
            <%--                                            </c:when>--%>
            <%--                                        </c:choose>--%>
            <%--                                    </span>--%>
            <%--                        </div>--%>
            <%--                    </div>--%>
            <%--                    <div style="width: 100%">--%>
            <%--                        <h4 style="font-size:14px;font-weight: 400;margin-left: 252px;margin-top: 33px">执行情况</h4>--%>
            <%--                        <div style="margin-left: 283px; margin-top: 33px">--%>
            <%--                                    <span style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
            <%--                                            style="color: red;size: 60px">*</span>是否执行：</span>--%>
            <%--                            <select style="width:380px;margin-right:5px;" name="isRsExecute"--%>
            <%--                                    id="isRsExecute">--%>
            <%--                                <option value="">--请选择--</option>--%>
            <%--                                <option value="1"--%>
            <%--                                        <c:if test="${provinceSelectionUnit.isRsExecute eq '1'}">selected</c:if>>--%>
            <%--                                    是--%>
            <%--                                </option>--%>
            <%--                                <option value="0"--%>
            <%--                                        <c:if test="${provinceSelectionUnit.isRsExecute eq '0'}">selected</c:if>>--%>
            <%--                                    否--%>
            <%--                                </option>--%>
            <%--                            </select>--%>
            <%--                        </div>--%>

            <%--                        <div style="margin-left: 283px; margin-top: 20px">--%>
            <%--                                    <span style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
            <%--                                            style="color: red;size: 60px">*</span>具体情况：</span>--%>
            <%--                            <textarea style="resize:none;padding:12px 4px 11px 7px;vertical-align: top;background: #FFFFFF;border: 1px solid #DCDFE6;border-radius: 5px;width: 379px; height: 100px;"--%>
            <%--                                      maxlength="500" placeholder="最多500字！" id="rsSpecific"--%>
            <%--                                      name="rsSpecific">${provinceSelectionUnit.rsSpecific}</textarea>--%>
            <%--                            <p style="color: #FB3301;width:379px;margin-left: 82px;font-size: 12px;font-weight:400;line-height:20px">（示例：执法局带头为全体在编执法人员（共50人）购买了人身意外伤害保险。)</p><br/>--%>
            <%--                        </div>--%>
            <%--                        <div style="margin-left: 142px; display: flex;width: calc(100% - 200px)">--%>
            <%--                                    <span style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
            <%--                                            style="color: red;size: 60px">*</span>人身安全保障执行情况支撑材料：</span>--%>
            <%--                            <span id="uploadTr12"--%>
            <%--                                  <c:if test="${provinceSelectionUnit.rsExecuteFileName!='' && provinceSelectionUnit.rsExecuteFileName!=null }">style='display:none'</c:if>--%>
            <%--                            >--%>
            <%--&lt;%&ndash;                                                                style="width: calc(100% - 224px)&ndash;%&gt;--%>
            <%--										<c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
            <%--                                            <input type="file" id="rsExecuteFile" value="文件上传">--%>
            <%--                                            <input type="hidden" class="form-control" name="rsExecuteFileName"--%>
            <%--                                                   id="rsExecuteFile_Filename"--%>
            <%--                                                   value="${provinceSelectionUnit.rsExecuteFileName}">--%>
            <%--                                            <input type="hidden" class="form-control" name="rsExecuteFileUrl"--%>
            <%--                                                   id="rsExecuteFile_Fileurl"--%>
            <%--                                                   value="${provinceSelectionUnit.rsExecuteFileUrl}">--%>
            <%--                                        </c:if>--%>
            <%--					                </span>--%>
            <%--                            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
            <%--                                <input  id="reButton12"--%>
            <%--                                        <c:if test="${provinceSelectionUnit.rsExecuteFileName=='' || provinceSelectionUnit.rsExecuteFileName==null }">style='display:none'</c:if>--%>
            <%--                                        class="btn btn-danger btn-xs" type="button" value="重新上传"/>--%>
            <%--                            </c:if>--%>
            <%--                        </div>--%>
            <%--                        <div id="wsc12" style="margin-left:357px;width: 380px;margin-top: 14px;border-radius: 3px;">--%>
            <%--                                     <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">--%>
            <%--                                        <c:choose>--%>
            <%--                                            <c:when test="${provinceSelectionUnit.rsExecuteFileName!='' && provinceSelectionUnit.rsExecuteFileName!=null }">--%>
            <%--                                           <span style=" margin-right: 8px; padding-top: 4px;">--%>
            <%--&lt;%&ndash;                                                       ${provinceSelectionUnit.rsExecuteFileName}&ndash;%&gt;--%>
            <%--                                               <a href="javascript:void(0)"--%>
            <%--                                                  onclick="downloadFile('${provinceSelectionUnit.rsExecuteFileUrl}','${provinceSelectionUnit.rsExecuteFileName}')">--%>
            <%--                                                       ${provinceSelectionUnit.rsExecuteFileName}--%>
            <%--                                               </a>--%>
            <%--                                           </span>--%>
            <%--                                            </c:when>--%>
            <%--                                        </c:choose>--%>
            <%--                                     </span>--%>
            <%--                        </div>--%>
            <%--                    </div>--%>
            <%--                </div>--%>
            <%--                &lt;%&ndash;尽职照单免责和失职照单问责&ndash;%&gt;--%>
            <%--                <div style="width: 100%;">--%>
            <%--                    <h3 style="font-size:16px;color: #31688F;">三、尽职照单免责和失职照单问责</h3>--%>
            <%--                    <div>--%>
            <%--                        <h4 style="font-size:14px;font-weight: 400;margin-left: 252px;margin-top: 33px">制定情况</h4>--%>
            <%--                        <div style="margin-left: 283px; margin-top: 20px">--%>
            <%--                                    <span style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
            <%--                                            style="color: red;size: 60px">*</span>是否制定：</span>--%>
            <%--                            <select style="width:380px;margin-right:5px;" name="isDingZhi"--%>
            <%--                                    id="isDingZhi">--%>
            <%--                                <option value="">--请选择--</option>--%>
            <%--                                <option value="1"--%>
            <%--                                        <c:if test="${provinceSelectionUnit.isDingZhi eq '1'}">selected</c:if>>--%>
            <%--                                    是--%>
            <%--                                </option>--%>
            <%--                                <option value="0"--%>
            <%--                                        <c:if test="${provinceSelectionUnit.isDingZhi eq '0'}">selected</c:if>>--%>
            <%--                                    否--%>
            <%--                                </option>--%>
            <%--                            </select>--%>
            <%--                        </div>--%>
            <%--                        <div style="margin-left: 283px; margin-top: 20px">--%>
            <%--                                    <span style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
            <%--                                            style="color: red;size: 60px">*</span>文件类型：</span>--%>
            <%--                            <select style="width:380px;margin-right:5px;" name="jzSzFileType"--%>
            <%--                                    id="jzSzFileType">--%>
            <%--                                <option value="">--请选择--</option>--%>
            <%--                                <option value="1"--%>
            <%--                                        <c:if test="${provinceSelectionUnit.jzSzFileType eq '1'}">selected</c:if>>--%>
            <%--                                    正式发文--%>
            <%--                                </option>--%>
            <%--                                <option value="2"--%>
            <%--                                        <c:if test="${provinceSelectionUnit.jzSzFileType eq '2'}">selected</c:if>>--%>
            <%--                                    征求意见稿--%>
            <%--                                </option>--%>
            <%--                                <option value="3"--%>
            <%--                                        <c:if test="${provinceSelectionUnit.jzSzFileType eq '3'}">selected</c:if>>--%>
            <%--                                    无--%>
            <%--                                </option>--%>
            <%--                            </select>--%>
            <%--                        </div>--%>

            <%--                        <div style="margin-left: 283px; margin-top: 20px">--%>
            <%--                                    <span style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
            <%--                                            style="color: red;size: 60px">*</span>出台时间：</span>--%>
            <%--                            <input type="date" name="jzSzComeTime" id="ctsj3"--%>
            <%--                                   value="${provinceSelectionUnit.jzSzComeTime}" height="39px"><br/>--%>

            <%--                        </div>--%>

            <%--                        <div style="margin-left: 44px; margin-top: 20px;display: flex;width: calc(100% - 200px)">--%>
            <%--                                    <span style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
            <%--                                            style="color: red;size: 60px">*</span>尽职照单免责和失职照单问责制定情况支撑材料：</span>--%>

            <%--                            <span id="uploadTr13"--%>
            <%--                                  <c:if test="${provinceSelectionUnit.jzSzCustomFileName!='' && provinceSelectionUnit.jzSzCustomFileName!=null }">style='display:none'</c:if>--%>
            <%--                            >--%>
            <%--&lt;%&ndash;                                                                style="width: calc(100% - 224px)&ndash;%&gt;--%>
            <%--										<c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
            <%--                                            <input type="file" id="jzSzCustomFile" value="文件上传">--%>
            <%--                                            <input type="hidden" class="form-control" name="jzSzCustomFileName"--%>
            <%--                                                   id="jzSzCustomFile_Filename"--%>
            <%--                                                   value="${provinceSelectionUnit.jzSzCustomFileName}">--%>
            <%--                                            <input type="hidden" class="form-control" name="jzSzCustomFileUrl"--%>
            <%--                                                   id="jzSzCustomFile_Fileurl"--%>
            <%--                                                   value="${provinceSelectionUnit.jzSzCustomFileUrl}">--%>
            <%--                                        </c:if>--%>
            <%--					                </span>--%>
            <%--                            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
            <%--                                <input  id="reButton13"--%>
            <%--                                        <c:if test="${provinceSelectionUnit.jzSzCustomFileName=='' || provinceSelectionUnit.jzSzCustomFileName==null }">style='display:none'</c:if>--%>
            <%--                                        class="btn btn-danger btn-xs" type="button" value="重新上传"/>--%>
            <%--                            </c:if>--%>
            <%--                        </div>--%>
            <%--                        <div id="wsc13" style="margin-left:357px;width: 380px;margin-top: 14px;border-radius: 3px;">--%>
            <%--                                     <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">--%>
            <%--                                        <c:choose>--%>
            <%--                                            <c:when test="${provinceSelectionUnit.jzSzCustomFileName!='' && provinceSelectionUnit.jzSzCustomFileName!=null }">--%>
            <%--                                           <span style=" margin-right: 8px; padding-top: 4px;">--%>
            <%--    &lt;%&ndash;                                                   ${provinceSelectionUnit.jzSzCustomFileName}&ndash;%&gt;--%>
            <%--                                               <a href="javascript:void(0)"--%>
            <%--                                                  onclick="downloadFile('${provinceSelectionUnit.jzSzCustomFileUrl}','${provinceSelectionUnit.jzSzCustomFileName}')">--%>
            <%--                                                       ${provinceSelectionUnit.jzSzCustomFileName}--%>
            <%--                                               </a>--%>
            <%--                                           </span>--%>
            <%--                                            </c:when>--%>
            <%--                                        </c:choose>--%>
            <%--                                     </span>--%>
            <%--                        </div>--%>
            <%--                    </div>--%>

            <%--                    <div style="width: 100%">--%>
            <%--                        <h4 style="font-size:14px;font-weight: 400;margin-left: 252px;margin-top: 33px">执行情况</h4>--%>
            <%--                        <div style="margin-left: 283px; margin-top: 20px">--%>
            <%--                                    <span style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
            <%--                                            style="color: red;size: 60px">*</span>是否执行：</span>--%>
            <%--                            <select style="width:380px;margin-right:5px;" name="isZhiXing"--%>
            <%--                                    id="isZhiXing">--%>
            <%--                                <option value="">--请选择--</option>--%>
            <%--                                <option value="1"--%>
            <%--                                        <c:if test="${provinceSelectionUnit.isZhiXing eq '1'}">selected</c:if>>--%>
            <%--                                    是--%>
            <%--                                </option>--%>
            <%--                                <option value="0"--%>
            <%--                                        <c:if test="${provinceSelectionUnit.isZhiXing eq '0'}">selected</c:if>>--%>
            <%--                                    否--%>
            <%--                                </option>--%>
            <%--                            </select>--%>
            <%--                        </div>--%>
            <%--                        <div style="margin-left: 283px; margin-top: 20px">--%>
            <%--                                    <span style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
            <%--                                            style="color: red;size: 60px">*</span>具体情况：</span>--%>
            <%--                            <textarea style="resize:none;width: 379px;background: #FFFFFF;border: 1px solid #DCDFE6;border-radius: 5px; height: 100px;vertical-align: top"--%>
            <%--                                      maxlength="500" placeholder="最多500字！" id="jzSzCondition"--%>
            <%--                                      name="jzSzCondition">${provinceSelectionUnit.jzSzCondition}</textarea><br/>--%>
            <%--                        </div>--%>

            <%--                        <div style="margin-left: 45px; margin-top: 20px;display: flex;width: calc(100% - 200px)">--%>
            <%--                                    <span style="font-size: 14px; color: #333333; font-weight: 400"><span--%>
            <%--                                            style="color: red;size: 60px">*</span>尽职照单免责和失职照单问责执行情况支撑材料：</span>--%>

            <%--                            <span id="uploadTr14"--%>
            <%--                                  <c:if test="${provinceSelectionUnit.jzSzExecuteFileName!='' && provinceSelectionUnit.jzSzExecuteFileName!=null }">style='display:none'</c:if>--%>
            <%--                            >--%>
            <%--&lt;%&ndash;                                                                style="width: calc(100% - 224px)&ndash;%&gt;--%>
            <%--										<c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
            <%--                                            <input type="file" id="jzSzExecuteFile" value="文件上传">--%>
            <%--                                            <input type="hidden" class="form-control" name="jzSzExecuteFileName"--%>
            <%--                                                   id="jzSzExecuteFile_Filename"--%>
            <%--                                                   value="${provinceSelectionUnit.jzSzExecuteFileName}">--%>
            <%--                                            <input type="hidden" class="form-control" name="jzSzExecuteFileUrl"--%>
            <%--                                                   id="jzSzExecuteFile_Fileurl"--%>
            <%--                                                   value="${provinceSelectionUnit.jzSzExecuteFileUrl}">--%>
            <%--                                        </c:if>--%>
            <%--					                </span>--%>
            <%--                            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
            <%--                                <input  id="reButton14"--%>
            <%--                                        <c:if test="${provinceSelectionUnit.jzSzExecuteFileName=='' || provinceSelectionUnit.jzSzExecuteFileName==null }">style='display:none'</c:if>--%>
            <%--                                        class="btn btn-danger btn-xs" type="button" value="重新上传"/>--%>
            <%--                            </c:if>--%>
            <%--                        </div>--%>
            <%--                        <div id="wsc14" style="margin-left:357px;width: 380px;margin-top: 14px;border-radius: 3px;">--%>
            <%--                                     <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">--%>
            <%--										<c:choose>--%>
            <%--                                            <c:when test="${provinceSelectionUnit.jzSzExecuteFileName!='' && provinceSelectionUnit.jzSzExecuteFileName!=null }">--%>
            <%--										   <span style=" margin-right: 8px; padding-top: 4px;">--%>
            <%--&lt;%&ndash;                                                   ${provinceSelectionUnit.jzSzExecuteFileName}&ndash;%&gt;--%>
            <%--                                                <a href="javascript:void(0)"--%>
            <%--                                                   onclick="downloadFile('${provinceSelectionUnit.jzSzExecuteFileUrl}','${provinceSelectionUnit.jzSzExecuteFileName}')">--%>
            <%--                                                        ${provinceSelectionUnit.jzSzExecuteFileName}--%>
            <%--                                                </a>--%>
            <%--                                           </span>--%>
            <%--                                            </c:when>--%>
            <%--                                        </c:choose>--%>
            <%--								   </span>--%>
            <%--                        </div>--%>
            <%--                    </div>--%>
            <%--                </div>--%>


            <%--            </div>--%>
            <%--3333333333333333333333333333333333333333333333333333--%>
            <%--竞赛比武--%>
            <div style="margin-top: 20px; background: #fff ;padding:2px 30px 26px 21px; width: 100%">
                <h1 style="color: #31688F;font-size: 16px;font-weight: 400">三、队伍建设与管理</h1>
                <h1 style="font-size: 14px;color: #31688F;font-weight:400;height: 33px;line-height:33px;background: #F8F8F8;">（一）规范化示范建设</h1>
                <h4 style="font-size:14px;font-weight: 600;margin-left: 300px;margin-top: 20px">规范化示范建设试点工作</h4>
                <div style="padding: 20px 0;width: 100%">
                    <div>
                        <label class="zhiding"><span style="color: red;size: 60px">*</span>2021年是否出台：</label>
                        <select style="width:380px;margin-right:5px;" name="isChutai" id="isChutai">
                            <option value="">--请选择--</option>
                            <option value="1" <c:if test="${provinceSelectionUnit.isjcBwCarry eq '1'}">selected</c:if>>是
                            </option>
                            <option value="0" <c:if test="${provinceSelectionUnit.isjcBwCarry eq '0'}">selected</c:if>>否
                            </option>
                        </select>
                    </div>
                    <div class='isDwreport'>
                        <div style="margin-left:60px;width: calc(100% - 200px);margin-top: 20px;display: flex;">
                            <label class="zhiding" style="line-height: 33px;"><span style="color: red;size: 60px">*</span>工作开展情况、取得成效等工作简报或报告：</label>
                            <span id="uploadTr9"
                                  <c:if test="${provinceSelectionUnit.jcBwFileName!='' && provinceSelectionUnit.jcBwFileName!=null }">style='display:none'</c:if>
                            >
<%--                            style="width: calc(100% - 224px)--%>
                              <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                  <input style="width: 80px" type="file" id="dwReportFile" value="文件上传">
                                  <input type="hidden" class="form-control" name="dwReportUrlname"
                                         id="dwReportUrlname_Filename" value="${provinceSelectionUnit.jcBwFileName}">
                                  <input type="hidden" class="form-control" name="dwReportUrl"
                                         id="dwReportUrl_Fileurl" value="${provinceSelectionUnit.jcBwFileUrl}">
                              </c:if>
                        </span>
                            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                <input  id="reButton9"
                                        <c:if test="${provinceSelectionUnit.jcBwFileName=='' || provinceSelectionUnit.jcBwFileName==null }">style='display:none'</c:if>
                                        class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                            </c:if>
                        </div>
                        <div id="wsc9" style="margin-left:357px;padding-top:6px;width: 380px;margin-top: 14px;border-radius: 3px;">
                        <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">
                            <c:choose>
                                <c:when test="${provinceSelectionUnit.jcBwFileName!='' && provinceSelectionUnit.jcBwFileName!=null }">
                                   <span style=" margin-right: 8px; padding-top: 4px;">
<%--                                               ${provinceSelectionUnit.jcBwFileName}--%>
                                       <a href="javascript:void(0)"
                                          onclick="downloadFile('${provinceSelectionUnit.jcBwFileUrl}','${provinceSelectionUnit.jcBwFileName}')">
                                               ${provinceSelectionUnit.jcBwFileName}
                                       </a>
                                   </span>
                                </c:when>
                            </c:choose>
                        </span>
                        </div>
                    </div>
                    <div class="isBuildis">
                        <div class="shangchuan" id='isfile' style="margin-left:60px;width: calc(100% - 200px);margin-top: 20px;display: flex;">
                            <label class="zhiding" style="line-height: 33px;"><span style="color: red;size: 60px" class="isBuildis">*</span>建设标准文件：</label>
                            <span id="uploadTr10"
                                  <c:if test="${provinceSelectionUnit.jcBwFileName!='' && provinceSelectionUnit.jcBwFileName!=null }">style='display:none'</c:if>
                            >
<%--                            style="width: calc(100% - 224px)--%>
                              <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                  <input style="width: 80px" type="file" id="jsbuildFile" value="文件上传">
                                  <input type="hidden" class="form-control" name="jcBwFileName"
                                         id="jcBwFile_Filename" value="${provinceSelectionUnit.jcBwFileName}">
                                  <input type="hidden" class="form-control" name="jcBwFileUrl"
                                         id="jcBwFile_Fileurl" value="${provinceSelectionUnit.jcBwFileUrl}">
                              </c:if>
                        </span>
                            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                <input  id="reButton10"
                                        <c:if test="${provinceSelectionUnit.jcBwFileName=='' || provinceSelectionUnit.jcBwFileName==null }">style='display:none'</c:if>
                                        class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                            </c:if>
                        </div>
                        <div id="wsc10" style="margin-left:357px;padding-top:6px;width: 380px;margin-top: 14px;border-radius: 3px;">
                        <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">
                            <c:choose>
                                <c:when test="${provinceSelectionUnit.jcBwFileName!='' && provinceSelectionUnit.jcBwFileName!=null }">
                                   <span style=" margin-right: 8px; padding-top: 4px;">
<%--                                               ${provinceSelectionUnit.jcBwFileName}--%>
                                       <a href="javascript:void(0)"
                                          onclick="downloadFile('${provinceSelectionUnit.jcBwFileUrl}','${provinceSelectionUnit.jcBwFileName}')">
                                               ${provinceSelectionUnit.jcBwFileName}
                                       </a>
                                   </span>
                                </c:when>
                            </c:choose>
                        </span>
                        </div>
                    </div>
                </div>
                <h4 style="font-size:14px;font-weight: 600;margin-left: 300px;margin-top: 20px">开展执法稽查</h4>
                <div style="margin-top: 33px">
                    <label style="font-size: 14px; color: #333333; font-weight: 400"><span style="color: red;size: 60px">*</span>是否开展：</label>
                    <select style="width:380px;margin-right:5px;" name="isLawcheck"
                            id="isLawcheck">
                        <option value="">--请选择--</option>
                        <option value="1"
                                <c:if test="${provinceSelectionUnit.isExecute eq '1'}">selected</c:if>>
                            是
                        </option>
                        <option value="0"
                                <c:if test="${provinceSelectionUnit.isExecute eq '0'}">selected</c:if>>
                            否
                        </option>
                    </select>
                </div>

                <div style="margin-left: 60px; margin-top: 20px;display: flex;width: calc(100% - 200px)">
                    <label style="line-height:44px;font-size: 14px; color: #333333; font-weight: 400"><span style="color: red;size: 60px" class="isLawcheckShow">*</span>稽查计划：</label>
                    <span id="uploadTr11"
                          <c:if test="${provinceSelectionUnit.specificConditionFileName!='' && provinceSelectionUnit.specificConditionFileName!=null }">style='display:none'</c:if>
                    >
    <%--                                                            style="width: calc(100% - 224px)--%>
    <%--                                                            style="width: calc(100% - 224px)--%>
                                <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                    <input type="file" id="lawCheckplanFile" value="文件上传">
                                    <input type="hidden" class="form-control" name="lawCheckplanUrlname"
                                           id="lawCheckplanUrlname_Filename"
                                           value="${provinceSelectionUnit.specificConditionFileName}">
                                    <input type="hidden" class="form-control" name="lawCheckplanUrl"
                                           id="lawCheckplanUrl_Fileurl"
                                           value="${provinceSelectionUnit.specificConditionFileUrl}">
                                </c:if>
                            </span>
                    <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                        <input  id="reButton11"
                                <c:if test="${provinceSelectionUnit.specificConditionFileName=='' || provinceSelectionUnit.specificConditionFileName==null }">style='display:none'</c:if>
                                class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                    </c:if>
                </div>
                <div id="wsc11" style="margin-left:357px;width: 380px;margin-top: 14px;border-radius: 3px;">
                    <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">
                        <c:choose>
                            <c:when test="${provinceSelectionUnit.specificConditionFileName!='' && provinceSelectionUnit.specificConditionFileName!=null }">
                                   <span style=" margin-right: 8px; padding-top: 4px;">
<%--                                                   ${provinceSelectionUnit.specificConditionFileName}--%>
                                       <a href="javascript:void(0)"
                                          onclick="downloadFile('${provinceSelectionUnit.specificConditionFileUrl}','${provinceSelectionUnit.specificConditionFileName}')">
                                               ${provinceSelectionUnit.specificConditionFileName}
                                       </a>
                                   </span>
                            </c:when>
                        </c:choose>
                    </span>
                </div>

                <div style="margin-left: 60px; margin-top: 20px;display: flex;width: calc(100% - 200px)">
                    <label style="line-height:44px;font-size: 14px; color: #333333; font-weight: 400"><span
                            style="color: red;size: 60px" class="isLawcheckShow">*</span>稽查工作报告：</label>
                    <span id="uploadTr12"
                          <c:if test="${provinceSelectionUnit.specificConditionFileName!='' && provinceSelectionUnit.specificConditionFileName!=null }">style='display:none'</c:if>
                    >
    <%--                                                            style="width: calc(100% - 224px)--%>
                                <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                    <input type="file" id="lawCheckreportFile" value="文件上传">
                                    <input type="hidden" class="form-control" name="lawCheckreportUrlname"
                                           id="lawCheckreportUrlname_Filename"
                                           value="${provinceSelectionUnit.specificConditionFileName}">
                                    <input type="hidden" class="form-control" name="lawCheckreportUrl"
                                           id="lawCheckreportUrl_Fileurl"
                                           value="${provinceSelectionUnit.specificConditionFileUrl}">
                                </c:if>
                            </span>
                    <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                        <input  id="reButton12"
                                <c:if test="${provinceSelectionUnit.specificConditionFileName=='' || provinceSelectionUnit.specificConditionFileName==null }">style='display:none'</c:if>
                                class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                    </c:if>
                </div>
                <div id="wsc12" style="margin-left:357px;width: 380px;margin-top: 14px;border-radius: 3px;">
                    <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">
                        <c:choose>
                            <c:when test="${provinceSelectionUnit.specificConditionFileName!='' && provinceSelectionUnit.specificConditionFileName!=null }">
                                   <span style=" margin-right: 8px; padding-top: 4px;">
<%--                                                   ${provinceSelectionUnit.specificConditionFileName}--%>
                                       <a href="javascript:void(0)"
                                          onclick="downloadFile('${provinceSelectionUnit.specificConditionFileUrl}','${provinceSelectionUnit.specificConditionFileName}')">
                                               ${provinceSelectionUnit.specificConditionFileName}
                                       </a>
                                   </span>
                            </c:when>
                        </c:choose>
                    </span>
                </div>

                <h4 style="font-size:14px;font-weight: 600;margin-left: 300px;margin-top: 50px">统一证件、着装</h4>
                <div class="shifou">
                    <label class="zhiding"><span style="color: red;size: 60px">*</span>是否完成统一着装：</label>
                    <select style="width:380px;margin-right:5px;" name="isUnifyattire" id="isUnifyattire">
                        <option value="">--请选择--</option>
                        <option value="1" <c:if test="${provinceSelectionUnit.isjcBwCarry eq '1'}">selected</c:if>>是
                        </option>
                        <option value="0" <c:if test="${provinceSelectionUnit.isjcBwCarry eq '0'}">selected</c:if>>否
                        </option>
                    </select>
                </div>
                <div class="shifou">
                    <label class="zhiding"><span style="color: red;size: 60px">*</span>是否完成统一证件：</label>
                    <select style="width:380px;margin-right:5px;" name="isUnifycertificate" id="isUnifycertificate">
                        <option value="">--请选择--</option>
                        <option value="1" <c:if test="${provinceSelectionUnit.isjcBwCarry eq '1'}">selected</c:if>>是
                        </option>
                        <option value="0" <c:if test="${provinceSelectionUnit.isjcBwCarry eq '0'}">selected</c:if>>否
                        </option>
                    </select>
                </div>
                <div style="margin-left: 60px; margin-top: 20px;display: flex;width: calc(100% - 200px)">
                    <label class="zhiding" style="line-height: 33px;"><span style="color: red;size: 60px">*</span>统一证件、着装支撑材料：</label>
                    <span id="uploadTr13"
                          <c:if test="${provinceSelectionUnit.jcBwFileName!='' && provinceSelectionUnit.jcBwFileName!=null }">style='display:none'</c:if>
                    >
<%--                            style="width: calc(100% - 224px)--%>
                              <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                  <input style="width: 80px" type="file" id="dwSupFile" value="文件上传">
                                  <input type="hidden" class="form-control" name="dwSupUrlname"
                                         id="dwSupUrlname_Filename" value="${provinceSelectionUnit.jcBwFileName}">
                                  <input type="hidden" class="form-control" name="dwSupUrl"
                                         id="dwSupUrl_Fileurl" value="${provinceSelectionUnit.jcBwFileUrl}">
                              </c:if>
                        </>
                    <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                        <input  id="reButton13"
                                <c:if test="${provinceSelectionUnit.jcBwFileName=='' || provinceSelectionUnit.jcBwFileName==null }">style='display:none'</c:if>
                                class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                    </c:if>
                </div>
                <p style="color: #FB3301;width: 526px;font-size:12px;line-height: 20px;font-weight: 400;margin-left: 600px">统一着装、统一证件的完成情况报告，报告中需体现完成率、着装及持证情况等内容。</p>
                <div id="wsc13" style="margin-left:357px;padding-top:6px;width: 380px;margin-top: 14px;border-radius: 3px;">
                    <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">
                        <c:choose>
                            <c:when test="${provinceSelectionUnit.jcBwFileName!='' && provinceSelectionUnit.jcBwFileName!=null }">
                               <span style=" margin-right: 8px; padding-top: 4px;">
<%--                                               ${provinceSelectionUnit.jcBwFileName}--%>
                                   <a href="javascript:void(0)"
                                      onclick="downloadFile('${provinceSelectionUnit.jcBwFileUrl}','${provinceSelectionUnit.jcBwFileName}')">
                                           ${provinceSelectionUnit.jcBwFileName}
                                   </a>
                               </span>
                            </c:when>
                        </c:choose>
                    </span>
                </div>
                <h1 style="font-size: 14px;color: #31688F;font-weight:400;height: 33px;line-height:33px;background: #F8F8F8;">（二）制度建设情况</h1>
                <p style="color: #FB3301;width: 526px;font-size:12px;line-height: 20px;font-weight: 400;margin-left: 600px">以下六个材料均需提供对应制度文件的pdf盖章版扫描件。</p>
                <div class="shangchuan">
                    <label class="zhiding" style="line-height: 33px;">区域交叉检查制度：</label>
                    <span id="uploadTr14"
                          <c:if test="${provinceSelectionUnit.jcBwFileName!='' && provinceSelectionUnit.jcBwFileName!=null }">style='display:none'</c:if>
                    >
<%--                            style="width: calc(100% - 224px)--%>
                              <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                  <input style="width: 80px" type="file" id="dwCrossFile" value="文件上传">
                                  <input type="hidden" class="form-control" name="dwCrossUrlname"
                                         id="dwCrossUrlname_Filename" value="${provinceSelectionUnit.jcBwFileName}">
                                  <input type="hidden" class="form-control" name="dwCrossUrl"
                                         id="dwCrossUrl_Fileurl" value="${provinceSelectionUnit.jcBwFileUrl}">
                              </c:if>
                        </span>
                    <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                        <input  id="reButton14"
                                <c:if test="${provinceSelectionUnit.jcBwFileName=='' || provinceSelectionUnit.jcBwFileName==null }">style='display:none'</c:if>
                                class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                    </c:if>
                </div>
                <div id="wsc14" style="margin-left:357px;padding-top:6px;width: 380px;margin-top: 14px;border-radius: 3px;">
                    <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">
                        <c:choose>
                            <c:when test="${provinceSelectionUnit.jcBwFileName!='' && provinceSelectionUnit.jcBwFileName!=null }">
                               <span style=" margin-right: 8px; padding-top: 4px;">
<%--                                               ${provinceSelectionUnit.jcBwFileName}--%>
                                   <a href="javascript:void(0)"
                                      onclick="downloadFile('${provinceSelectionUnit.jcBwFileUrl}','${provinceSelectionUnit.jcBwFileName}')">
                                           ${provinceSelectionUnit.jcBwFileName}
                                   </a>
                               </span>
                            </c:when>
                        </c:choose>
                    </span>
                </div>

                <div class="shangchuan">
                    <label class="zhiding" style="line-height: 33px;">专案查办制度：</label>
                    <span id="uploadTr15"
                          <c:if test="${provinceSelectionUnit.jcBwFileName!='' && provinceSelectionUnit.jcBwFileName!=null }">style='display:none'</c:if>
                    >
<%--                            style="width: calc(100% - 224px)--%>
                              <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                  <input style="width: 80px" type="file" id="dwTaskFile" value="文件上传">
                                  <input type="hidden" class="form-control" name="dwTaskUrlname"
                                         id="dwTaskUrlname_Filename" value="${provinceSelectionUnit.jcBwFileName}">
                                  <input type="hidden" class="form-control" name="dwTaskUrl"
                                         id="dwTaskUrl_Fileurl" value="${provinceSelectionUnit.jcBwFileUrl}">
                              </c:if>
                        </span>
                    <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                        <input  id="reButton15"
                                <c:if test="${provinceSelectionUnit.jcBwFileName=='' || provinceSelectionUnit.jcBwFileName==null }">style='display:none'</c:if>
                                class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                    </c:if>
                </div>
                <div id="wsc15" style="margin-left:357px;padding-top:6px;width: 380px;margin-top: 14px;border-radius: 3px;">
                    <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">
                        <c:choose>
                            <c:when test="${provinceSelectionUnit.jcBwFileName!='' && provinceSelectionUnit.jcBwFileName!=null }">
                               <span style=" margin-right: 8px; padding-top: 4px;">
<%--                                               ${provinceSelectionUnit.jcBwFileName}--%>
                                   <a href="javascript:void(0)"
                                      onclick="downloadFile('${provinceSelectionUnit.jcBwFileUrl}','${provinceSelectionUnit.jcBwFileName}')">
                                           ${provinceSelectionUnit.jcBwFileName}
                                   </a>
                               </span>
                            </c:when>
                        </c:choose>
                    </span>
                </div>
                <div class="shangchuan">
                    <label class="zhiding" style="line-height: 33px;">部门协调联动机制：</label>
                    <span id="uploadTr16"
                          <c:if test="${provinceSelectionUnit.jcBwFileName!='' && provinceSelectionUnit.jcBwFileName!=null }">style='display:none'</c:if>
                    >
<%--                            style="width: calc(100% - 224px)--%>
                              <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                  <input style="width: 80px" type="file" id="dwLianFile" value="文件上传">
                                  <input type="hidden" class="form-control" name="dwLianUrlname"
                                         id="dwLianUrlname_Filename" value="${provinceSelectionUnit.jcBwFileName}">
                                  <input type="hidden" class="form-control" name="dwLianUrl"
                                         id="dwLianUrl_Fileurl" value="${provinceSelectionUnit.jcBwFileUrl}">
                              </c:if>
                        </span>
                    <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                        <input  id="reButton16"
                                <c:if test="${provinceSelectionUnit.jcBwFileName=='' || provinceSelectionUnit.jcBwFileName==null }">style='display:none'</c:if>
                                class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                    </c:if>
                </div>
                <div id="wsc16" style="margin-left:357px;padding-top:6px;width: 380px;margin-top: 14px;border-radius: 3px;">
                    <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">
                        <c:choose>
                            <c:when test="${provinceSelectionUnit.jcBwFileName!='' && provinceSelectionUnit.jcBwFileName!=null }">
                               <span style=" margin-right: 8px; padding-top: 4px;">
<%--                                               ${provinceSelectionUnit.jcBwFileName}--%>
                                   <a href="javascript:void(0)"
                                      onclick="downloadFile('${provinceSelectionUnit.jcBwFileUrl}','${provinceSelectionUnit.jcBwFileName}')">
                                           ${provinceSelectionUnit.jcBwFileName}
                                   </a>
                               </span>
                            </c:when>
                        </c:choose>
                    </span>
                </div>
                <div class="shangchuan">
                    <label class="zhiding" style="line-height: 33px;">第三方辅助执法制度：</label>
                    <span id="uploadTr17"
                          <c:if test="${provinceSelectionUnit.jcBwFileName!='' && provinceSelectionUnit.jcBwFileName!=null }">style='display:none'</c:if>
                    >
<%--                            style="width: calc(100% - 224px)--%>
                              <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                  <input style="width: 80px" type="file" id="dwFulawFile" value="文件上传">
                                  <input type="hidden" class="form-control" name="dwFulawUrlname"
                                         id="dwFulawUrlname_Filename" value="${provinceSelectionUnit.jcBwFileName}">
                                  <input type="hidden" class="form-control" name="dwFulawUrl"
                                         id="dwFulawUrl_Fileurl" value="${provinceSelectionUnit.jcBwFileUrl}">
                              </c:if>
                        </span>
                    <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                        <input  id="reButton17"
                                <c:if test="${provinceSelectionUnit.jcBwFileName=='' || provinceSelectionUnit.jcBwFileName==null }">style='display:none'</c:if>
                                class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                    </c:if>
                </div>
                <div id="wsc17" style="margin-left:357px;padding-top:6px;width: 380px;margin-top: 14px;border-radius: 3px;">
                    <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">
                        <c:choose>
                            <c:when test="${provinceSelectionUnit.jcBwFileName!='' && provinceSelectionUnit.jcBwFileName!=null }">
                               <span style=" margin-right: 8px; padding-top: 4px;">
<%--                                               ${provinceSelectionUnit.jcBwFileName}--%>
                                   <a href="javascript:void(0)"
                                      onclick="downloadFile('${provinceSelectionUnit.jcBwFileUrl}','${provinceSelectionUnit.jcBwFileName}')">
                                           ${provinceSelectionUnit.jcBwFileName}
                                   </a>
                               </span>
                            </c:when>
                        </c:choose>
                    </span>
                </div>

                <div class="shangchuan">
                    <label class="zhiding" style="line-height: 33px;">执法普法制度：</label>
                    <span id="uploadTr18"
                          <c:if test="${provinceSelectionUnit.jcBwFileName!='' && provinceSelectionUnit.jcBwFileName!=null }">style='display:none'</c:if>
                    >
<%--                            style="width: calc(100% - 224px)--%>
                              <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                  <input style="width: 80px" type="file" id="dwLawFile" value="文件上传">
                                  <input type="hidden" class="form-control" name="dwLawUrlname"
                                         id="dwLawUrlname_Filename" value="${provinceSelectionUnit.jcBwFileName}">
                                  <input type="hidden" class="form-control" name="dwLawUrl"
                                         id="dwLawUrl_Fileurl" value="${provinceSelectionUnit.jcBwFileUrl}">
                              </c:if>
                        </span>
                    <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                        <input  id="reButton18"
                                <c:if test="${provinceSelectionUnit.jcBwFileName=='' || provinceSelectionUnit.jcBwFileName==null }">style='display:none'</c:if>
                                class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                    </c:if>
                </div>
                <div id="wsc18" style="margin-left:357px;padding-top:6px;width: 380px;margin-top: 14px;border-radius: 3px;">
                    <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">
                        <c:choose>
                            <c:when test="${provinceSelectionUnit.jcBwFileName!='' && provinceSelectionUnit.jcBwFileName!=null }">
                               <span style=" margin-right: 8px; padding-top: 4px;">
<%--                                               ${provinceSelectionUnit.jcBwFileName}--%>
                                   <a href="javascript:void(0)"
                                      onclick="downloadFile('${provinceSelectionUnit.jcBwFileUrl}','${provinceSelectionUnit.jcBwFileName}')">
                                           ${provinceSelectionUnit.jcBwFileName}
                                   </a>
                               </span>
                            </c:when>
                        </c:choose>
                    </span>
                </div>

                <div class="shangchuan">
                    <label class="zhiding" style="line-height: 33px;">轻微违法免罚制度：</label>
                    <span id="uploadTr19"
                          <c:if test="${provinceSelectionUnit.jcBwFileName!='' && provinceSelectionUnit.jcBwFileName!=null }">style='display:none'</c:if>
                    >
<%--                            style="width: calc(100% - 224px)--%>
                              <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                  <input style="width: 80px" type="file" id="dwTinylawFile" value="文件上传">
                                  <input type="hidden" class="form-control" name="dwTinylawUrlname"
                                         id="dwTinylawUrlname_Filename" value="${provinceSelectionUnit.jcBwFileName}">
                                  <input type="hidden" class="form-control" name="dwTinylawUrl"
                                         id="dwTinylawUrl_Fileurl" value="${provinceSelectionUnit.jcBwFileUrl}">
                              </c:if>
                        </span>
                    <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                        <input  id="reButton19"
                                <c:if test="${provinceSelectionUnit.jcBwFileName=='' || provinceSelectionUnit.jcBwFileName==null }">style='display:none'</c:if>
                                class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                    </c:if>
                </div>
                <div id="wsc19" style="margin-left:357px;padding-top:6px;width: 380px;margin-top: 14px;border-radius: 3px;">
                    <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">
                        <c:choose>
                            <c:when test="${provinceSelectionUnit.jcBwFileName!='' && provinceSelectionUnit.jcBwFileName!=null }">
                               <span style=" margin-right: 8px; padding-top: 4px;">
<%--                                               ${provinceSelectionUnit.jcBwFileName}--%>
                                   <a href="javascript:void(0)"
                                      onclick="downloadFile('${provinceSelectionUnit.jcBwFileUrl}','${provinceSelectionUnit.jcBwFileName}')">
                                           ${provinceSelectionUnit.jcBwFileName}
                                   </a>
                               </span>
                            </c:when>
                        </c:choose>
                    </span>
                </div>
                <h1 style="font-size: 14px;color: #31688F;font-weight:400;height: 33px;line-height:33px;background: #F8F8F8;">（二）制度落实情况</h1>
                <h4 style="font-size:14px;font-weight: 600;margin-left:300px;margin-top: 20px">举报奖励制度执行情况</h4>
                <div style="margin-top: 33px">
                    <label style="font-size: 14px; color: #333333; font-weight: 400"><span style="color: red;size: 60px">*</span>是否组织开展政策宣传、解读，发布典型案例：</label>
                    <select style="width:380px;margin-right:5px;" name="isTypicalcase"
                            id="isTypicalcase"  class="isTypical">
                        <option value="">--请选择--</option>
                        <option value="1"
                                <c:if test="${provinceSelectionUnit.isExecute eq '1'}">selected</c:if>>
                            是
                        </option>
                        <option value="0"
                                <c:if test="${provinceSelectionUnit.isExecute eq '0'}">selected</c:if>>
                            否
                        </option>
                    </select>
                </div>
                <div style="margin-top: 20px">
                    <label style="font-size: 14px; color: #333333; font-weight: 400"><span style="color: red;size: 60px">*</span>各地市是否实现举报奖励制度全覆盖：</label>
                    <select style="width:380px;margin-right:5px;" name="isCover" id="isCover" class="isTypical">
                        <option value="">--请选择--</option>
                        <option value="1"
                                <c:if test="${provinceSelectionUnit.isCarryOut eq '1'}">selected</c:if>>是
                        </option>
                        <option value="0"
                                <c:if test="${provinceSelectionUnit.isCarryOut eq '0'}">selected</c:if>>否
                        </option>
                    </select>
                </div>
                <div class="shangchuan">
                    <label class="zhiding" style="line-height: 33px;"><span style="color: red;size: 60px" class="isTypicalcase">*</span>监督执法正面清单制度支撑材料：</label>
                    <span id="uploadTr20"
                          <c:if test="${provinceSelectionUnit.jcBwFileName!='' && provinceSelectionUnit.jcBwFileName!=null }">style='display:none'</c:if>
                    >
<%--                            style="width: calc(100% - 224px)--%>
                              <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                  <input style="width: 80px" type="file" id="systemSupFile" value="文件上传">
                                  <input type="hidden" class="form-control" name="systemSupUrlname"
                                         id="systemSupUrlname_Filename" value="${provinceSelectionUnit.jcBwFileName}">
                                  <input type="hidden" class="form-control" name="systemSupUrl"
                                         id="systemSupUrl_Fileurl" value="${provinceSelectionUnit.jcBwFileUrl}">
                              </c:if>
                        </span>
                    <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                        <input  id="reButton20"
                                <c:if test="${provinceSelectionUnit.jcBwFileName=='' || provinceSelectionUnit.jcBwFileName==null }">style='display:none'</c:if>
                                class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                    </c:if>
                </div>

                <div id="wsc20" style="margin-left:357px;padding-top:6px;width: 380px;margin-top: 14px;border-radius: 3px;">
                    <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">
                        <c:choose>
                            <c:when test="${provinceSelectionUnit.jcBwFileName!='' && provinceSelectionUnit.jcBwFileName!=null }">
                               <span style=" margin-right: 8px; padding-top: 4px;">
<%--                                               ${provinceSelectionUnit.jcBwFileName}--%>
                                   <a href="javascript:void(0)"
                                      onclick="downloadFile('${provinceSelectionUnit.jcBwFileUrl}','${provinceSelectionUnit.jcBwFileName}')">
                                           ${provinceSelectionUnit.jcBwFileName}
                                   </a>
                               </span>
                            </c:when>
                        </c:choose>
                    </span>
                </div>
                <p style="color: #FB3301;margin-left: 600px;font-size: 12px;line-height: 20px;">1.举报奖励制度执行情况的文字说明；</p>
                <p style="color: #FB3301;margin-left: 600px;font-size: 12px;line-height: 20px;">2.开展宣传、解读的方案、通知或者省级人员现场指导的宣传稿；</p>
                <p style="color: #FB3301;margin-left: 600px;font-size: 12px;line-height: 20px;">3.典型案例发布的网页截图。</p>

                <h4 style="font-size:14px;font-weight: 600;margin-left: 300px;margin-top: 30px">监督执法正面清单制度执行情况</h4>
                <div style="margin-top: 50px">
                    <label style="font-size: 14px; color: #333333; font-weight: 400"><span style="color: red;size: 60px">*</span> 是否辖区内所有地市级生态环境部门均公布正面清单企业名单，并实施动态管理 ：</label>
                    <select style="width:380px;margin-right:5px;" name="isPositivelistManage" id="isPositivelistManage" class="isPositivelistWarnShow">
                        <option value="">--请选择--</option>
                        <option value="1"
                                <c:if test="${provinceSelectionUnit.isCarryOut eq '1'}">selected</c:if>>是
                        </option>
                        <option value="0"
                                <c:if test="${provinceSelectionUnit.isCarryOut eq '0'}">selected</c:if>>否
                        </option>
                    </select>
                </div>
                <div style="margin-top: 20px">
                    <label style="font-size: 14px; color: #333333;font-weight: 400"><span style="color: red;size: 60px">*</span>是否指导组织地市级生态环境部门通过座谈、宣讲、培训等方式对正面清单企业开展帮扶指导和提醒预警 ：</label>
                    <select style="width:380px;margin-right:5px;position: relative;top:-5px" name="isPositivelistWarn" id="isPositivelistWarn" class="isPositivelistWarnShow">
                        <option value="">--请选择--</option>
                        <option value="1"
                                <c:if test="${provinceSelectionUnit.isCarryOut eq '1'}">selected</c:if>>是
                        </option>
                        <option value="0"
                                <c:if test="${provinceSelectionUnit.isCarryOut eq '0'}">selected</c:if>>否
                        </option>
                    </select>
                </div>
                <div class="shangchuan">
                    <label class="zhiding" style="line-height: 33px;"><span style="color: red;size: 60px" class="isPositivelistSup">*</span>监督执法正面清单制度支撑材料：</label>
                    <span id="uploadTr21"
                          <c:if test="${provinceSelectionUnit.jcBwFileName!='' && provinceSelectionUnit.jcBwFileName!=null }">style='display:none'</c:if>
                    >
<%--                            style="width: calc(100% - 224px)--%>
                              <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                  <input style="width: 80px" type="file" id="positivelistSupFile" value="文件上传">
                                  <input type="hidden" class="form-control" name="positivelistSupUrlname"
                                         id="positivelistSupUrlname_Filename" value="${provinceSelectionUnit.jcBwFileName}">
                                  <input type="hidden" class="form-control" name="positivelistSupUrl"
                                         id="positivelistSupUrl_Fileurl" value="${provinceSelectionUnit.jcBwFileUrl}">
                              </c:if>
                        </span>
                    <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                        <input  id="reButton21"
                                <c:if test="${provinceSelectionUnit.jcBwFileName=='' || provinceSelectionUnit.jcBwFileName==null }">style='display:none'</c:if>
                                class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                    </c:if>
                </div>

                <div id="wsc21" style="margin-left:357px;padding-top:6px;width: 380px;margin-top: 14px;border-radius: 3px;">
                    <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">
                        <c:choose>
                            <c:when test="${provinceSelectionUnit.jcBwFileName!='' && provinceSelectionUnit.jcBwFileName!=null }">
                               <span style=" margin-right: 8px; padding-top: 4px;">
<%--                                               ${provinceSelectionUnit.jcBwFileName}--%>
                                   <a href="javascript:void(0)"
                                      onclick="downloadFile('${provinceSelectionUnit.jcBwFileUrl}','${provinceSelectionUnit.jcBwFileName}')">
                                           ${provinceSelectionUnit.jcBwFileName}
                                   </a>
                               </span>
                            </c:when>
                        </c:choose>
                    </span>
                </div>
                <p style="color: #FB3301;margin-left: 600px;font-size: 12px;line-height: 20px;">1.执行情况的文字说明材料；</p>
                <p style="color: #FB3301;margin-left: 600px;font-size: 12px;line-height: 20px;">2.公布清单的网页截图。如省级统一公布，则提供省级网页截图；如各地市分别公布，则提供所有地市公布的网页截图；</p>
                <p style="color: #FB3301;margin-left: 600px;font-size: 12px;line-height: 20px;">3.开展帮扶指导和提醒预警的通知、方案或者省级人员现场指导的宣传稿；</p>
                <h4 style="font-size:14px;font-weight: 600;margin-left: 300px;margin-bottom:30px;margin-top:30px">“双随机、一公开”监管工作执行情况。</h4>
                <div style="margin-top: 20px">
                    <label style="font-size: 14px; color: #333333; font-weight: 400"><span style="color: red;size: 60px">*</span>“双随机”抽查结果公开率：</label>
                    <input type="text" id="randomRate" name="randomRate" placeholder="请输入数字"  height="39px">%
                    <%--                    <select style="width:380px;margin-right:5px;" name="randomRate" id="randomRate">--%>
                    <%--                        <option value="">--请选择--</option>--%>
                    <%--                        <option value="1"--%>
                    <%--                                <c:if test="${provinceSelectionUnit.isCarryOut eq '1'}">selected</c:if>>是--%>
                    <%--                        </option>--%>
                    <%--                        <option value="0"--%>
                    <%--                                <c:if test="${provinceSelectionUnit.isCarryOut eq '0'}">selected</c:if>>否--%>
                    <%--                        </option>--%>
                    <%--                    </select>--%>
                </div>

                <div class="shangchuan">
                    <label class="zhiding" style="line-height: 33px;"><span style="color: red;size: 60px">*</span>双随机、一公开”监管工作执行情况支撑材料：</label>
                    <span id="uploadTr22"
                          <c:if test="${provinceSelectionUnit.jcBwFileName!='' && provinceSelectionUnit.jcBwFileName!=null }">style='display:none'</c:if>
                    >
<%--                            style="width: calc(100% - 224px)--%>
                              <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                  <input style="width: 80px" type="file" id="randomsupFile" value="文件上传">
                                  <input type="hidden" class="form-control" name="randomsupUrlname"
                                         id="randomsupUrlname_Filename" value="${provinceSelectionUnit.jcBwFileName}">
                                  <input type="hidden" class="form-control" name="randomsupUrl"
                                         id="randomsupUrl_Fileurl" value="${provinceSelectionUnit.jcBwFileUrl}">
                              </c:if>
                        </span>
                    <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                        <input  id="reButton22"
                                <c:if test="${provinceSelectionUnit.jcBwFileName=='' || provinceSelectionUnit.jcBwFileName==null }">style='display:none'</c:if>
                                class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                    </c:if>
                </div>

                <div id="wsc22" style="margin-left:357px;padding-top:6px;width: 380px;margin-top: 14px;border-radius: 3px;">
                    <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">
                        <c:choose>
                            <c:when test="${provinceSelectionUnit.jcBwFileName!='' && provinceSelectionUnit.jcBwFileName!=null }">
                               <span style=" margin-right: 8px; padding-top: 4px;">
<%--                                               ${provinceSelectionUnit.jcBwFileName}--%>
                                   <a href="javascript:void(0)"
                                      onclick="downloadFile('${provinceSelectionUnit.jcBwFileUrl}','${provinceSelectionUnit.jcBwFileName}')">
                                           ${provinceSelectionUnit.jcBwFileName}
                                   </a>
                               </span>
                            </c:when>
                        </c:choose>
                    </span>
                </div>
                <p style="color: #FB3301;margin-left: 600px;font-size: 12px;line-height: 20px;">1.文字说明材料；</p>
                <p style="color: #FB3301;margin-left: 600px;font-size: 12px;line-height: 20px;">2.随机抽查结果公开的网页截图。</p>
                <h4 style="font-size:14px;font-weight: 600;margin-left: 300px;margin-bottom:30px;margin-top:30px">典型案例制度执行情况</h4>
                <div style="margin-top: 20px">
                    <label style="font-size: 14px; color: #333333; font-weight: 400"><span style="color: red;size: 60px">*</span>省级生态环境部门全年组织发布典型案例批次：</label>
                    <input type="text" id="typicalcasePici" name="typicalcasePici" placeholder="请输入典型案例"  height="39px">
                </div>
                <div class="shangchuan">
                    <label class="zhiding" style="line-height: 33px;"><span style="color: red;size: 60px">*</span>发布典型案例的通知或者公开的链接：</label>
                    <span id="uploadTr23"
                          <c:if test="${provinceSelectionUnit.jcBwFileName!='' && provinceSelectionUnit.jcBwFileName!=null }">style='display:none'</c:if>
                    >
<%--                            style="width: calc(100% - 224px)--%>
                              <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                  <input style="width: 80px" type="file" id="typicalcaseFile" value="文件上传">
                                  <input type="hidden" class="form-control" name="typicalcaseUrlname"
                                         id="typicalcaseUrlname_Filename" value="${provinceSelectionUnit.jcBwFileName}">
                                  <input type="hidden" class="form-control" name="typicalcaseUrl"
                                         id="typicalcaseUrl_Fileurl" value="${provinceSelectionUnit.jcBwFileUrl}">
                              </c:if>
                        </span>
                    <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                        <input  id="reButton23"
                                <c:if test="${provinceSelectionUnit.jcBwFileName=='' || provinceSelectionUnit.jcBwFileName==null }">style='display:none'</c:if>
                                class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                    </c:if>
                </div>

                <div id="wsc23" style="margin-left:357px;padding-top:6px;width: 380px;margin-top: 14px;border-radius: 3px;">
                    <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">
                        <c:choose>
                            <c:when test="${provinceSelectionUnit.jcBwFileName!='' && provinceSelectionUnit.jcBwFileName!=null }">
                               <span style=" margin-right: 8px; padding-top: 4px;">
<%--                                               ${provinceSelectionUnit.jcBwFileName}--%>
                                   <a href="javascript:void(0)"
                                      onclick="downloadFile('${provinceSelectionUnit.jcBwFileUrl}','${provinceSelectionUnit.jcBwFileName}')">
                                           ${provinceSelectionUnit.jcBwFileName}
                                   </a>
                               </span>
                            </c:when>
                        </c:choose>
                    </span>
                </div>

                <h4 style="font-size:14px;font-weight: 600;margin-left:300px;margin-top: 30px;margin-bottom: 30px">执法普法制度执行情况</h4>
                <div style="margin-top: 20px">
                    <label style="font-size: 14px; color: #333333;text-align-last: right;font-weight: 400"><span style="color: red;size: 60px">*</span>是否指导所有地市级生态环境部门开展针对中小企业重点执法帮扶、普法培训、“送法入企”活动：</label>
                    <select style="width:380px;margin-right:5px;position: relative;top:-5px" name="isGuide" id="isGuide">
                        <option value="">--请选择--</option>
                        <option value="1"
                                <c:if test="${provinceSelectionUnit.isCarryOut eq '1'}">selected</c:if>>是
                        </option>
                        <option value="0"
                                <c:if test="${provinceSelectionUnit.isCarryOut eq '0'}">selected</c:if>>否
                        </option>
                    </select>
                </div>
                <div class="shangchuan">
                    <label class="zhiding" style="line-height: 33px;"><span style="color: red;size: 60px" class="isGuideShow">*</span>执法普法制度执行情况支撑材料：</label>
                    <span id="uploadTr24"
                          <c:if test="${provinceSelectionUnit.jcBwFileName!='' && provinceSelectionUnit.jcBwFileName!=null }">style='display:none'</c:if>
                    >
<%--                            style="width: calc(100% - 224px)--%>
                              <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                  <input style="width: 80px" type="file" id="guideFile" value="文件上传">
                                  <input type="hidden" class="form-control" name="guideUrlname"
                                         id="guideUrlname_Filename" value="${provinceSelectionUnit.jcBwFileName}">
                                  <input type="hidden" class="form-control" name="guideUrl"
                                         id="guideUrl_Fileurl" value="${provinceSelectionUnit.jcBwFileUrl}">
                              </c:if>
                        </span>
                    <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                        <input  id="reButton24"
                                <c:if test="${provinceSelectionUnit.jcBwFileName=='' || provinceSelectionUnit.jcBwFileName==null }">style='display:none'</c:if>
                                class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                    </c:if>
                </div>

                <div id="wsc24" style="margin-left:357px;padding-top:6px;width: 380px;margin-top: 14px;border-radius: 3px;">
                    <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">
                        <c:choose>
                            <c:when test="${provinceSelectionUnit.jcBwFileName!='' && provinceSelectionUnit.jcBwFileName!=null }">
                               <span style=" margin-right: 8px; padding-top: 4px;">
<%--                                               ${provinceSelectionUnit.jcBwFileName}--%>
                                   <a href="javascript:void(0)"
                                      onclick="downloadFile('${provinceSelectionUnit.jcBwFileUrl}','${provinceSelectionUnit.jcBwFileName}')">
                                           ${provinceSelectionUnit.jcBwFileName}
                                   </a>
                               </span>
                            </c:when>
                        </c:choose>
                    </span>
                </div>
                <p style="color: #FB3301;margin-left: 600px;font-size: 12px;line-height: 20px;">1.执行情况的文字说明材料；</p>
                <p style="color: #FB3301;margin-left: 600px;font-size: 12px;line-height: 20px;">2.开展执法普法工作的通知、行动方案或者省级人员现场指导的宣传稿。</p>

                <h4 style="font-size:14px;font-weight: 600;margin-left: 300px;margin-top:30px;margin-bottom: 30px">“百千万”执法人才培养工程开展情况</h4>
                <div style="margin-top: 20px">
                    <label style="font-size: 14px; color: #333333; font-weight: 400"><span style="color: red;size: 60px">*</span>是否建立省级执法人才库：</label>
                    <select style="width:380px;margin-right:5px;" name="isPeopool" id="isPeopool">
                        <option value="">--请选择--</option>
                        <option value="1"
                                <c:if test="${provinceSelectionUnit.isCarryOut eq '1'}">selected</c:if>>是
                        </option>
                        <option value="0"
                                <c:if test="${provinceSelectionUnit.isCarryOut eq '0'}">selected</c:if>>否
                        </option>
                    </select>
                </div>

                <div class="shangchuan">
                    <label class="zhiding" style="line-height: 33px;font-size: 14px; color: #333333; font-weight: 400"><span style="color: red;size: 60px" class="ispeopoolShow">*</span>已建立人才库的相关文件：</label>
                    <span id="uploadTr25"
                          <c:if test="${provinceSelectionUnit.jcBwFileName!='' && provinceSelectionUnit.jcBwFileName!=null }">style='display:none'</c:if>
                    >
<%--                            style="width: calc(100% - 224px)--%>
                              <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                  <input style="width: 80px" type="file" id="peopoolFile" value="文件上传">
                                  <input type="hidden" class="form-control" name="peopoolUrlname"
                                         id="peopoolUrlname_Filename" value="${provinceSelectionUnit.jcBwFileName}">
                                  <input type="hidden" class="form-control" name="peopoolUrl"
                                         id="peopoolUrl_Fileurl" value="${provinceSelectionUnit.jcBwFileUrl}">
                              </c:if>
                        </span>
                    <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                        <input  id="reButton25"
                                <c:if test="${provinceSelectionUnit.jcBwFileName=='' || provinceSelectionUnit.jcBwFileName==null }">style='display:none'</c:if>
                                class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                    </c:if>
                </div>

                <div id="wsc25" style="margin-left:357px;padding-top:6px;width: 380px;margin-top: 14px;border-radius: 3px;">
                    <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">
                        <c:choose>
                            <c:when test="${provinceSelectionUnit.jcBwFileName!='' && provinceSelectionUnit.jcBwFileName!=null }">
                               <span style=" margin-right: 8px; padding-top: 4px;">
<%--                                               ${provinceSelectionUnit.jcBwFileName}--%>
                                   <a href="javascript:void(0)"
                                      onclick="downloadFile('${provinceSelectionUnit.jcBwFileUrl}','${provinceSelectionUnit.jcBwFileName}')">
                                           ${provinceSelectionUnit.jcBwFileName}
                                   </a>
                               </span>
                            </c:when>
                        </c:choose>
                    </span>
                </div>
                <div style="margin-top: 20px">
                    <label style="font-size: 14px; color: #333333; font-weight: 400"><span style="color: red;size: 60px">*</span>是否组织执法专业、专项培训：</label>
                    <select style="width:380px;margin-right:5px;" name="isTrain" id="isTrain">
                        <option value="">--请选择--</option>
                        <option value="1"
                                <c:if test="${provinceSelectionUnit.isCarryOut eq '1'}">selected</c:if>>是
                        </option>
                        <option value="0"
                                <c:if test="${provinceSelectionUnit.isCarryOut eq '0'}">selected</c:if>>否
                        </option>
                    </select>
                </div>
                <div class="shangchuan">
                    <label class="zhiding" style="line-height: 33px;"><span
                            style="color: red;size: 60px" class="istrainShow"> *</span>开展培训（岗位培训除外）的动态或宣传情况：</label>
                    <span id="uploadTr26"
                          <c:if test="${provinceSelectionUnit.jcBwFileName!='' && provinceSelectionUnit.jcBwFileName!=null }">style='display:none'</c:if>
                    >
<%--                            style="width: calc(100% - 224px)--%>
                              <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                  <input style="width: 80px" type="file" id="trainFile" value="文件上传">
                                  <input type="hidden" class="form-control" name="trainUrlname"
                                         id="trainUrlname_Filename" value="${provinceSelectionUnit.jcBwFileName}">
                                  <input type="hidden" class="form-control" name="trainUrl"
                                         id="trainUrl_Fileurl" value="${provinceSelectionUnit.jcBwFileUrl}">
                              </c:if>
                        </span>
                    <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                        <input  id="reButton26"
                                <c:if test="${provinceSelectionUnit.jcBwFileName=='' || provinceSelectionUnit.jcBwFileName==null }">style='display:none'</c:if>
                                class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                    </c:if>
                </div>

                <div id="wsc26" style="margin-left:357px;padding-top:6px;width: 380px;margin-top: 14px;border-radius: 3px;">
                    <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">
                        <c:choose>
                            <c:when test="${provinceSelectionUnit.jcBwFileName!='' && provinceSelectionUnit.jcBwFileName!=null }">
                               <span style=" margin-right: 8px; padding-top: 4px;">
<%--                                               ${provinceSelectionUnit.jcBwFileName}--%>
                                   <a href="javascript:void(0)"
                                      onclick="downloadFile('${provinceSelectionUnit.jcBwFileUrl}','${provinceSelectionUnit.jcBwFileName}')">
                                           ${provinceSelectionUnit.jcBwFileName}
                                   </a>
                               </span>
                            </c:when>
                        </c:choose>
                    </span>
                </div>
            </div>

            <div style="margin-top: 20px; background: #fff; padding: 2px 30px 26px 21px; width: 100%">

                <h1 style="color: #31688F;font-size: 16px;font-weight: 400">四、专项行动</h1>
                <h1 style="font-size: 14px;color: #31688F;font-weight:400;height: 33px;line-height:33px;background: #F8F8F8;">排污许可执法监管</h1>
                <h4 style="font-size:14px;font-weight: 600;margin-left: 300px;margin-top:30px;margin-bottom: 30px">排污许可制度建设情况</h4>
                <div style="margin-top: 20px;">
                    <div class="shifou">
                        <label class="zhiding" style="line-height: 33px"> <span style="color: red;size: 60px">*</span>是否出台加强排污许可执法监管政策措施：</label>
                        <select style="width:380px;margin-right:5px;" name="isLicenselaw" id="isLicenselaw">
                            <option value="">--请选择--</option>
                            <option value="1"
                                    <c:if test="${provinceSelectionUnit.isCarryOut eq '1'}">selected</c:if>>是
                            </option>
                            <option value="0"
                                    <c:if test="${provinceSelectionUnit.isCarryOut eq '0'}">selected</c:if>>否
                            </option>
                        </select>
                    </div>
                </div>

                <div class="shangchuan">
                    <label class="zhiding"><span style="color: red;size: 60px" class="isLicenseShow">*</span>加强排污许可执法监管的政策文件：</label>
                    <span id="uploadTr27"
                          <c:if test="${provinceSelectionUnit.jdbfFileName!='' && provinceSelectionUnit.jdbfFileName!=null }">style='display:none'</c:if>
                          style="">
<%--                        width: calc(100% - 190px)--%>
							<c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                <input type="file" id="licenseFileUpload" value="文件上传">
                                <input type="hidden" class="form-control" name="licenselawUrlname" id="licenselawUrlname_Filename"
                                       value="${provinceSelectionUnit.jdbfFileName}">
                                <input type="hidden" class="form-control" name="licenselawUrl" id="licenselawUrl_Fileurl"
                                       value="${provinceSelectionUnit.jdbfFileUrl}">
                            </c:if>
					    </span>
                    <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                        <input id="reButton27"
                               <c:if test="${provinceSelectionUnit.jdbfFileName=='' || provinceSelectionUnit.jdbfFileName==null }">style='display:none'</c:if>
                               class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                    </c:if>
                </div>
                <div id="wsc27" style="margin-left:354px;width: 380px;margin-top: 14px;border-radius: 3px;">
                    <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">
						<c:choose>
                            <c:when test="${provinceSelectionUnit.jdbfFileName!='' && provinceSelectionUnit.jdbfFileName!=null }">
							<span style=" margin-right: 8px; padding-top: 4px;">
<%--                                    ${provinceSelectionUnit.jdbfFileName}--%>
                                <a href="javascript:void(0)"
                                   onclick="downloadFile('${provinceSelectionUnit.jdbfFileUrl}','${provinceSelectionUnit.jdbfFileName}')">
                                        ${provinceSelectionUnit.jdbfFileName}
                                </a>
                            </span>
                            </c:when>
                        </c:choose>
					</span>
                </div>
                <div style="margin-top: 20px;">
                    <div class="shifou">
                        <label class="zhiding"> <span style="color: red;size: 60px">*</span> 是否细化完善排污许可行政处罚自由裁量规定：</label>
                        <select style="width:380px;margin-right:5px;" name="isDiscretion" id="isDiscretion">
                            <option value="">--请选择--</option>
                            <option value="1"
                                    <c:if test="${provinceSelectionUnit.isCarryOut eq '1'}">selected</c:if>>是
                            </option>
                            <option value="0"
                                    <c:if test="${provinceSelectionUnit.isCarryOut eq '0'}">selected</c:if>>否
                            </option>
                        </select>
                    </div>
                </div>
                <div class="shangchuan">
                    <label class="zhiding" style="line-height: 33px;"> <span style="color: red;size: 60px" class="isDiscretShow">*</span>行政处罚自由裁量规定：</label>
                    <span id="uploadTr28"
                          <c:if test="${provinceSelectionUnit.jdbfFileName!='' && provinceSelectionUnit.jdbfFileName!=null }">style='display:none'</c:if>
                          style="">
<%--                        width: calc(100% - 190px)--%>
							<c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                <input type="file" id="discretionFileUpload" value="文件上传">
                                <input type="hidden" class="form-control" name="discretionUrlname" id="discretionUrlname_Filename"
                                       value="${provinceSelectionUnit.jdbfFileName}">
                                <input type="hidden" class="form-control" name="discretionUrl" id="discretionUrl_Fileurl"
                                       value="${provinceSelectionUnit.jdbfFileUrl}">
                            </c:if>
					    </span>
                    <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                        <input id="reButton28"
                               <c:if test="${provinceSelectionUnit.jdbfFileName=='' || provinceSelectionUnit.jdbfFileName==null }">style='display:none'</c:if>
                               class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                    </c:if>
                </div>
                <div id="wsc28" style="margin-left:354px;width: 380px;margin-top: 14px;border-radius: 3px;">
                    <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">
						<c:choose>
                            <c:when test="${provinceSelectionUnit.jdbfFileName!='' && provinceSelectionUnit.jdbfFileName!=null }">
							<span style=" margin-right: 8px; padding-top: 4px;">
<%--                                    ${provinceSelectionUnit.jdbfFileName}--%>
                                <a href="javascript:void(0)"
                                   onclick="downloadFile('${provinceSelectionUnit.jdbfFileUrl}','${provinceSelectionUnit.jdbfFileName}')">
                                        ${provinceSelectionUnit.jdbfFileName}
                                </a>
                            </span>
                            </c:when>
                        </c:choose>
					</span>
                </div>
                <h4 style="font-size:14px;font-weight: 600;margin-left: 300px;margin-top:30px;margin-bottom: 30px">组织开展排污许可专项执法</h4>
                <div style="margin-top: 20px;">
                    <div class="shifou">
                        <label class="zhiding"> <span style="color: red;size: 60px">*</span>是否及时曝光排污许可违法典型案件：</label>
                        <select style="width:380px;margin-right:5px;" name="isLicensespecial" id="isLicensespecial">
                            <option value="">--请选择--</option>
                            <option value="1"
                                    <c:if test="${provinceSelectionUnit.isCarryOut eq '1'}">selected</c:if>>是
                            </option>
                            <option value="0"
                                    <c:if test="${provinceSelectionUnit.isCarryOut eq '0'}">selected</c:if>>否
                            </option>
                        </select>
                    </div>
                </div>
                <div class="shangchuan">
                    <label class="zhiding" style="line-height: 33px;"> <span style="color: red;size: 60px" class="isLicenseShow">*</span>典型案例公开的网页截图或者通报文件：</label>
                    <span id="uploadTr29"
                          <c:if test="${provinceSelectionUnit.jdbfFileName!='' && provinceSelectionUnit.jdbfFileName!=null }">style='display:none'</c:if>
                          style="">
<%--                        width: calc(100% - 190px)--%>
							<c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                <input type="file" id="licensesFileUpload" value="文件上传">
                                <input type="hidden" class="form-control" name="licensespecialUrlname" id="licensespecialUrlname_Filename"
                                       value="${provinceSelectionUnit.jdbfFileName}">
                                <input type="hidden" class="form-control" name="licensespecialUrl" id="licensespecialUrl_Fileurl"
                                       value="${provinceSelectionUnit.jdbfFileUrl}">
                            </c:if>
					    </span>
                    <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                        <input id="reButton29"
                               <c:if test="${provinceSelectionUnit.jdbfFileName=='' || provinceSelectionUnit.jdbfFileName==null }">style='display:none'</c:if>
                               class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                    </c:if>
                </div>
                <div id="wsc29" style="margin-left:354px;width: 380px;margin-top: 14px;border-radius: 3px;">
                    <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">
						<c:choose>
                            <c:when test="${provinceSelectionUnit.jdbfFileName!='' && provinceSelectionUnit.jdbfFileName!=null }">
							<span style=" margin-right: 8px; padding-top: 4px;">
<%--                                    ${provinceSelectionUnit.jdbfFileName}--%>
                                <a href="javascript:void(0)"
                                   onclick="downloadFile('${provinceSelectionUnit.jdbfFileUrl}','${provinceSelectionUnit.jdbfFileName}')">
                                        ${provinceSelectionUnit.jdbfFileName}
                                </a>
                            </span>
                            </c:when>
                        </c:choose>
					</span>
                </div>
                <h4 style="font-size:14px;font-weight: 600;margin-left: 300px;margin-top:30px;margin-bottom: 30px">组织开展清单式执法</h4>
                <div style="margin-top: 20px;">
                    <div class="shifou">
                        <label class="zhiding"> <span style="color: red;size: 60px">*</span>是否开展排污许可清单式执法：</label>
                        <select style="width:380px;margin-right:5px;" name="isLicenselist" id="isLicenselist">
                            <option value="">--请选择--</option>
                            <option value="1"
                                    <c:if test="${provinceSelectionUnit.isCarryOut eq '1'}">selected</c:if>>是
                            </option>
                            <option value="0"
                                    <c:if test="${provinceSelectionUnit.isCarryOut eq '0'}">selected</c:if>>否
                            </option>
                        </select>
                    </div>
                </div>
                <div class="shangchuan">
                    <label class="zhiding"><span style="color: red;size: 60px" class="islicenselistShow">*</span>省本级制定的排污许可清单式执法通知文件或行动方案或现场指导等佐证材料：</label>
                    <span id="uploadTr30"
                          <c:if test="${provinceSelectionUnit.jdbfFileName!='' && provinceSelectionUnit.jdbfFileName!=null }">style='display:none'</c:if>
                          style="">
<%--                        width: calc(100% - 190px)--%>
							<c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                                <input type="file" id="licenselisFileUpload" value="文件上传">
                                <input type="hidden" class="form-control" name="licenselistUrlname" id="licenselistUrlname_Filename"
                                       value="${provinceSelectionUnit.jdbfFileName}">
                                <input type="hidden" class="form-control" name="licenselistUrl" id="licenselistUrl_Fileurl"
                                       value="${provinceSelectionUnit.jdbfFileUrl}">
                            </c:if>
					    </span>
                    <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                        <input id="reButton30"
                               <c:if test="${provinceSelectionUnit.jdbfFileName=='' || provinceSelectionUnit.jdbfFileName==null }">style='display:none'</c:if>
                               class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                    </c:if>
                </div>
                <div id="wsc30" style="margin-left:354px;width: 380px;margin-top: 14px;border-radius: 3px;">
                    <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">
						<c:choose>
                            <c:when test="${provinceSelectionUnit.jdbfFileName!='' && provinceSelectionUnit.jdbfFileName!=null }">
							<span style=" margin-right: 8px; padding-top: 4px;">
<%--                                    ${provinceSelectionUnit.jdbfFileName}--%>
                                <a href="javascript:void(0)"
                                   onclick="downloadFile('${provinceSelectionUnit.jdbfFileUrl}','${provinceSelectionUnit.jdbfFileName}')">
                                        ${provinceSelectionUnit.jdbfFileName}
                                </a>
                            </span>
                            </c:when>
                        </c:choose>
					</span>
                </div>
            </div>

            <%--66666666666666666666666666666666666666--%>
            <%--省 专项行动表现--%>
            <%--            <div style="margin-top: 20px; background: #fff; padding: 2px 30px 26px 21px; width: 100%">--%>

            <%--                <h1 style="color: #31688F;font-size: 16px;font-weight: 400">五、专项行动表现</h1>--%>
            <%--                <div style=" display: flex;margin-left:286px;margin-top: 20px;">--%>
            <%--                    <span style="font-size: 14px; color: #333333; font-weight: 400;padding-top: 5px">Excel文件：</span>--%>
            <%--                    <span id="uploadTr28"--%>
            <%--                          <c:if test="${provinceSelectionUnit.zxxdExcelName!='' && provinceSelectionUnit.zxxdExcelName!=null }">style='display:none'</c:if>--%>
            <%--                          style="">--%>
            <%--&lt;%&ndash;                        width: calc(100% - 224px)&ndash;%&gt;--%>
            <%--                        <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
            <%--                            <div style="display: flex">--%>
            <%--                                <input type="file" id="zxxdExcel" value="文件上传">--%>
            <%--                                <input type="hidden" class="form-control" name="zxxdExcelName"--%>
            <%--                                       id="zxxdExcel_Filename"--%>
            <%--                                       value="${provinceSelectionUnit.zxxdExcelName}">--%>
            <%--                                <input type="hidden" class="form-control" name="zxxdExcelUrl"--%>
            <%--                                       id="zxxdExcel_Fileurl"--%>
            <%--                                       value="${provinceSelectionUnit.zxxdExcelUrl}">--%>

            <%--                            </div>--%>
            <%--                        </c:if>--%>
            <%--                    </span>--%>
            <%--                    <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
            <%--                        <input id="reButton28"--%>
            <%--                               <c:if test="${provinceSelectionUnit.zxxdExcelName=='' || provinceSelectionUnit.zxxdExcelName==null }">style='display:none'</c:if>--%>
            <%--                               class="btn btn-danger btn-xs" type="button" value="重新上传"/>--%>
            <%--                    </c:if>--%>
            <%--                </div>--%>
            <%--                <p>--%>
            <%--                    <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
            <%--                          <span style="color: deepskyblue; margin-left:359px;margin-top:5px"><a style="color: blue;margin-left: 7px"--%>
            <%--                                                                                href="#"--%>
            <%--                                                                                onclick="down(5)">(下载模板）</a>--%>
            <%--                           </span>--%>
            <%--                    </c:if>--%>
            <%--                </p>--%>
            <%--                <div id="wsc28" style="margin-left:358px;width: 380px;margin-top: 14px;border-radius: 3px;">--%>
            <%--                    <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">--%>
            <%--                        <c:choose>--%>
            <%--                            <c:when test="${provinceSelectionUnit.zxxdExcelName!='' && provinceSelectionUnit.zxxdExcelName!=null }">--%>
            <%--                                <span style=" margin-right: 8px; padding-top: 4px;">--%>
            <%--                                 <a href="javascript:void(0)"--%>
            <%--                                    onclick="downloadFile('${provinceSelectionUnit.zxxdExcelUrl}','${provinceSelectionUnit.zxxdExcelName}')"> ${provinceSelectionUnit.zxxdExcelName} </a>--%>
            <%--                                </span>--%>
            <%--                            </c:when>--%>
            <%--                        </c:choose>--%>
            <%--                    </span>--%>
            <%--                </div>--%>
            <%--                <p style="color: #FB3301;font-size:12px;font-weight:400;margin-left: 365px;width: 543px;">1.请认真填写下列信息，并仔细上传支撑材料，填写的信息未在支撑材料中体现的，将不作为给分点</p>--%>
            <%--                <p style="color: #FB3301;font-size:12px;font-weight:400;margin-left: 365px;width: 543px;line-height:20px">2.每份支撑材料大小不超过50M，要求rar格式；存放支撑材料的文件夹（压缩包）以【省份-二级标题】命名，如知识竞赛支撑材料应存放在【xx省-知识竞赛】文件夹（压缩包）中</p>--%>


            <%--                <div>--%>
            <%--                    <div class="shifou" style="margin-left:114px">--%>
            <%--                        <span class="zhiding"><span style="color: red;size: 60px">*</span>查处涉废矿物油环境违法犯罪案件数：</span>--%>
            <%--                        <input maxlength="7" id="zxxdDelinquencyNum" name="zxxdDelinquencyNum"--%>
            <%--                               value="${provinceSelectionUnit.zxxdDelinquencyNum}"--%>
            <%--                               onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^0-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}"--%>
            <%--                               onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^0-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}">--%>
            <%--                    </div>--%>
            <%--                    <div class="shifou" style="margin-left:123px">--%>
            <%--                        <label class="zhiding" style="display: inline-block;width: 230px;text-align-last: right;"><span style="color: red;size: 60px">*</span>对跨区域非法排放、倾倒、处置危险废物案件试行提级查办案例数：</label>--%>
            <%--                        <input style='margin-top: 2px;position: absolute;margin-left: 4px;' maxlength="7" id="zxxdRiskTrashNum" name="zxxdRiskTrashNum"--%>
            <%--                               value="${provinceSelectionUnit.zxxdRiskTrashNum}"--%>
            <%--                               onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^0-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}"--%>
            <%--                               onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^0-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}">--%>
            <%--                    </div>--%>
            <%--                    <div class="shifou" style="margin-left:59px">--%>
            <%--                        <span class="zhiding" style="display: inline-block;width: 300px;text-align-last: right;"><span style="color: red;size: 60px">*</span>候选集体移交篡改、伪造自动监测数据或干扰自动监控设施等逃避监管的违法犯罪案件数：</span>--%>
            <%--                        <input style='position: absolute;margin-left: 4px;margin-top: 2px;' maxlength="7" id="zxxdForgeNum" name="zxxdForgeNum"--%>
            <%--                               value="${provinceSelectionUnit.zxxdForgeNum}"--%>
            <%--                               onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^0-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}"--%>
            <%--                               onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^0-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}">--%>
            <%--                    </div>--%>
            <%--                </div>--%>

            <%--                <div class="shangchuan" style="margin-left:283px;width: calc(100% - 200px)">--%>

            <%--                    <span style="color: red;size: 60px">*</span>--%>
            <%--                    <span class="zhiding" style="line-height: 33px;">支撑材料：</span>--%>
            <%--                    <span id="uploadTr29"--%>
            <%--                          <c:if test="${provinceSelectionUnit.zxxdExpressionFileName!='' && provinceSelectionUnit.zxxdExpressionFileName!=null }">style='display:none'</c:if>--%>
            <%--                          style=" ">--%>
            <%--&lt;%&ndash;                        width: calc(100% - 224px)&ndash;%&gt;--%>
            <%--							<c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
            <%--								<div style="display: flex">--%>
            <%--                                    <input type="file" id="zxxdExpressionFileUpload" value="文件上传">--%>
            <%--                                    <input type="hidden" class="form-control" name="zxxdExpressionFileName"--%>
            <%--                                           id="zxxdExpressionFileName_Filename"--%>
            <%--                                           value="${provinceSelectionUnit.zxxdExpressionFileName}">--%>
            <%--                                    <input type="hidden" class="form-control" name="zxxdExpressionFileUrl"--%>
            <%--                                           id="zxxdExpressionFileUrl_Fileurl"--%>
            <%--                                           value="${provinceSelectionUnit.zxxdExpressionFileUrl}">--%>
            <%--                                </div>--%>
            <%--                            </c:if>--%>
            <%--					    </span>--%>
            <%--                    <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
            <%--                        <input id="reButton29"--%>
            <%--                               <c:if test="${provinceSelectionUnit.zxxdExpressionFileName=='' || provinceSelectionUnit.zxxdExpressionFileName==null }">style='display:none'</c:if>--%>
            <%--                               class="btn btn-danger btn-xs" type="button"--%>
            <%--                               value="重新上传"/>--%>
            <%--                    </c:if>--%>
            <%--                </div>--%>
            <%--                <div id="wsc29cityPersonInputPro" style="margin-left:358px;width: 380px;margin-top: 14px;">--%>
            <%--                    <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">--%>
            <%--						<c:choose>--%>
            <%--                            <c:when test="${provinceSelectionUnit.zxxdExpressionFileName!='' && provinceSelectionUnit.zxxdExpressionFileName!=null }">--%>
            <%--							<span style=" margin-right: 8px; padding-top: 4px;">--%>
            <%--&lt;%&ndash;                                    ${provinceSelectionUnit.zxxdExpressionFileName}&ndash;%&gt;--%>
            <%--                                <a href="javascript:void(0)"--%>
            <%--                                   onclick="downloadFile('${provinceSelectionUnit.zxxdExpressionFileUrl}','${provinceSelectionUnit.zxxdExpressionFileName}')">--%>
            <%--                                        ${provinceSelectionUnit.zxxdExpressionFileName}--%>
            <%--                                </a>--%>
            <%--                            </span>--%>
            <%--                            </c:when>--%>
            <%--                        </c:choose>--%>
            <%--					</span>--%>
            <%--                </div>--%>
            <%--                &lt;%&ndash;专项行动新增的字段&ndash;%&gt;--%>
            <%--                <div class="shifou" style="margin-left:160px">--%>
            <%--                    <span class="zhiding"><span style="color: red;size: 60px">*</span>查处弄虚作假违法犯罪案件数：</span>--%>
            <%--                    <input maxlength="7" id="nxzjCaseSum" name="nxzjCaseSum"--%>
            <%--                           value="${provinceSelectionUnit.nxzjCaseSum}"--%>
            <%--                           onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^0-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}"--%>
            <%--                           onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^0-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}">--%>
            <%--                </div>--%>
            <%--                <div class="shifou" style="margin-left: 150px;">--%>
            <%--                    <span class="zhiding" style="width: 211px;display: inline-block;text-align-last: right;"><span style="color: red;size: 60px">*</span>辖区内是否30%以上的设区市都查处了数据造假违法犯罪案件：</span>--%>
            <%--                    <select name="isZjCase" id="isZjCase"--%>
            <%--                            style="position: absolute;margin-left: 5px;margin-top: 5px;width:380px;margin-right:5px;">--%>
            <%--                        <option value="">--请选择--</option>--%>
            <%--                        <option value="1"--%>
            <%--                                <c:if test="${provinceSelectionUnit.isZjCase eq '1'}">selected</c:if>>--%>
            <%--                            是--%>
            <%--                        </option>--%>
            <%--                        <option value="0"--%>
            <%--                                <c:if test="${provinceSelectionUnit.isZjCase eq '0'}">selected</c:if>>--%>
            <%--                            否--%>
            <%--                        </option>--%>
            <%--                    </select>--%>
            <%--                </div>--%>

            <%--                <div class="shangchuan" style="margin-left:144px;width: calc(100% - 200px)">--%>

            <%--                    <span style="color: red;size: 60px">*</span>--%>
            <%--                    <span class="zhiding">查处弄虚作假违法犯罪支撑材料：</span>--%>
            <%--                    <span id="uploadTr30"--%>
            <%--                          <c:if test="${provinceSelectionUnit.nxzjCaseFileName!='' && provinceSelectionUnit.nxzjCaseFileName!=null }">style='display:none'</c:if>--%>
            <%--                          style=" ">--%>
            <%--&lt;%&ndash;                        width: calc(100% - 224px)&ndash;%&gt;--%>
            <%--							<c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
            <%--								<div style="display: flex">--%>
            <%--                                    <input type="file" id="nxzjCaseFileUpload" value="文件上传">--%>
            <%--                                    <input type="hidden" class="form-control" name="nxzjCaseFileName"--%>
            <%--                                           id="nxzjCaseFileName_Filename"--%>
            <%--                                           value="${provinceSelectionUnit.nxzjCaseFileName}">--%>
            <%--                                    <input type="hidden" class="form-control" name="nxzjCaseFileUrl"--%>
            <%--                                           id="nxzjCaseFileUrl_Fileurl"--%>
            <%--                                           value="${provinceSelectionUnit.nxzjCaseFileUrl}">--%>
            <%--                                </div>--%>
            <%--                            </c:if>--%>
            <%--					    </span>--%>
            <%--                    <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
            <%--                        <input id="reButton30" style="width:80px;height:32px;margin-left: 8px;background-color:#3f91c2;border:1px solid #3f91c2"--%>
            <%--                               <c:if test="${provinceSelectionUnit.nxzjCaseFileName=='' || provinceSelectionUnit.nxzjCaseFileName==null }">style='display:none'</c:if>--%>
            <%--                               class="btn btn-danger btn-xs" type="button"--%>
            <%--                               value="重新上传"/>--%>
            <%--                    </c:if>--%>
            <%--                </div>--%>
            <%--                <div id="wsc30" style="margin-left:358px;width: 380px;margin-top: 14px;">--%>
            <%--                   <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">--%>
            <%--						<c:choose>--%>
            <%--                            <c:when test="${provinceSelectionUnit.nxzjCaseFileName!='' && provinceSelectionUnit.nxzjCaseFileName!=null }">--%>
            <%--							<span style=" margin-right: 8px; padding-top: 4px;">--%>
            <%--&lt;%&ndash;                                    ${provinceSelectionUnit.nxzjCaseFileName}&ndash;%&gt;--%>
            <%--                                <a href="javascript:void(0)"--%>
            <%--                                   onclick="downloadFile('${provinceSelectionUnit.nxzjCaseFileUrl}','${provinceSelectionUnit.nxzjCaseFileName}')">--%>
            <%--                                        ${provinceSelectionUnit.nxzjCaseFileName}--%>
            <%--                                </a>--%>
            <%--                            </span>--%>
            <%--                            </c:when>--%>
            <%--                        </c:choose>--%>
            <%--					</span>--%>
            <%--                </div>--%>
            <%--            </div>--%>


            <%--7777777777777777777777777777--%>
            <%--活动总结--%>
            <%--            <div style="margin-top: 20px; background: #fff; padding:2px 30px 26px 21px; line-height: 30px;width: 100%">--%>
            <%--                <h1 style="color: #25678E;font-size: 16px;font-weight:400">六、活动总结</h1>--%>
            <%--                <div class="shangchuan" style="width: 100%;margin-left:225px">--%>
            <%--                                                            <span class="zhiding"><span--%>
            <%--                                                                    style="color: red;size: 60px">*</span>活动总结支撑材料：</span>--%>
            <%--                    <span id="uploadTr25"--%>
            <%--                          <c:if test="${provinceSelectionUnit.activityreportname!='' && provinceSelectionUnit.activityreportname!=null }">style='display:none'</c:if>--%>
            <%--                          >--%>
            <%--&lt;%&ndash;                        style="width: calc(100% - 224px)&ndash;%&gt;--%>
            <%--										<c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
            <%--                                            <input type="file" id="activityReport" value="文件上传">--%>
            <%--                                            <input type="hidden" class="form-control" name="activityreportname"--%>
            <%--                                                   id="activityReport_Filename"--%>
            <%--                                                   value="${provinceSelectionUnit.activityreportname}">--%>
            <%--                                            <input type="hidden" class="form-control" name="activityreporturl"--%>
            <%--                                                   id="activityReport_Fileurl"--%>
            <%--                                                   value="${provinceSelectionUnit.activityreporturl}">--%>
            <%--                                        </c:if>--%>
            <%--					            </span>--%>
            <%--                    <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">--%>
            <%--                        <input  id="reButton25"--%>
            <%--                               <c:if test="${provinceSelectionUnit.activityreportname=='' || provinceSelectionUnit.activityreportname==null }">style='display:none'</c:if>--%>
            <%--                               class="btn btn-danger btn-xs" type="button"--%>
            <%--                               value="重新上传"/>--%>
            <%--                    </c:if>--%>
            <%--                </div>--%>
            <%--                <p style="color: red;margin-left: 354px;font-size:12px;font-weight: 400">（附件要求PDF格式，大小不超过30M）</p>--%>
            <%--                <div id="wsc25" style="margin-left:352px;width: 380px;">--%>
            <%--                   <span style="font-size: 16px;margin-top: 10px; margin-left: 10px">--%>
            <%--                        <c:choose>--%>
            <%--                            <c:when test="${provinceSelectionUnit.activityreportname!='' && provinceSelectionUnit.activityreportname!=null }">--%>
            <%--                               <span style=" margin-right: 8px; padding-top: 4px;">--%>
            <%--    &lt;%&ndash;                                   ${provinceSelectionUnit.activityreportname}&ndash;%&gt;--%>
            <%--                                   <a href="javascript:void(0)"--%>
            <%--                                      onclick="downloadFile('${provinceSelectionUnit.activityreporturl}','${provinceSelectionUnit.activityreportname}')">--%>
            <%--                                           ${provinceSelectionUnit.activityreportname}--%>
            <%--                                   </a>--%>
            <%--                               </span>--%>
            <%--                            </c:when>--%>
            <%--                        </c:choose>--%>
            <%--                   </span>--%>
            <%--                </div>--%>
            <%--            </div>--%>


        </div>


        <%--snnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnn--%>

        <div style="background-color:#f6fafe;display: flex; justify-content:center; padding-top: 40px">
            <input type="hidden" name="id" id="id"
                   value="${provinceSelectionUnit.id }"/>
            <input type="hidden" name="subToken" value="${subToken}">
            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
                <a href="#">
                    <button type="button" id="xxcj_XXZC_Butt" class="btn btn-danger"
                            name="signup" value="Sign up"
                            style="font-size: 16px; width: 100px;border-radius: 3px;padding: 8px 32px;margin-right: 50px">
                        暂存
                    </button>
                    <button type="button" id="xxcj_AJSL_Butt" class="btn btn-danger"
                            name="signup" value="Sign up"
                            style="font-size: 16px;width: 100px;border-radius: 3px;padding: 8px 32px; background: #BCBBBB;border-color: #BCBBBB;">
                        提交
                    </button>
                </a>
            </c:if>
        </div>
    </form>
</div>
<script type="text/javascript">

    $(function () {
        $(".isBuildis").hide()
        $("#isCaseTypeShow").hide()
        $("#isCaseTypeShow2").hide()


        //出台时间
        var come1 = '${provinceSelectionUnit.comeTime}';
        console.log(come1)
        timeZh(come1, 1);
        var come2 = '${provinceSelectionUnit.rsComeTime}';
        timeZh(come2, 2);
        var come3 = '${provinceSelectionUnit.jzSzComeTime}';
        timeZh(come3, 3);


        //机构领导是否带头参与 如果 是 显示领导具体职务的框
        $("#jgld").change(function () {
            var option = $("#jgld option:selected");
            //要触发的事件
            if (option.val() == "1") {
                $("#jtldzw").show()
            } else {
                $("#jtldzw").hide()
            }
        });
        <%--var leader = '${provinceSelectionUnit.isLeader}';--%>
        <%--if (leader == "1") {--%>
        <%--    $("#jtldzw").show()--%>
        <%--} else {--%>
        <%--    $("#jtldzw").hide()--%>
        <%--}--%>


        $("#is_jili").change(function () {
            var option = $("#is_jili option:selected");
            //要触发的事件
            if (option.val() == "1") {
                $(".isShow").show()
            } else {
                $(".isShow").hide()
            }
        })
        // 是否开展竞赛比武
        $("#is_jingwu").change(function () {
            var option = $("#is_jingwu option:selected");
            //要触发的事件
            if (option.val() == "1") {
                $(".isJingwuShow").show()
            } else {
                $(".isJingwuShow").hide()
            }
        })
        // 案卷评查
        $("#is_casereview").change(function () {
            var option = $("#is_casereview option:selected");
            //要触发的事件
            if (option.val() == "1") {
                $(".isCaseShow").show()
            } else {
                $(".isCaseShow").hide()
            }
        })




        // 是否出台加强排污许可执法监管政策措施
        $("#isLicenselaw").change(function () {
            var option = $("#isLicenselaw option:selected");
            //要触发的事件
            if (option.val() == "1") {
                $(".isLicenseShow").show()
            } else {
                $(".isLicenseShow").hide()
            }
        })


        // 是否细化完善排污许可行政处罚自由裁量规定
        $("#isDiscretion").change(function () {
            var option = $("#isDiscretion option:selected");
            //要触发的事件
            if (option.val() == "1") {
                $(".isDiscretShow").show()
            } else {
                $(".isDiscretShow").hide()
            }
        })

        // 2021年是否出台
        $("#isChutai").change(function () {
            var option = $("#isChutai option:selected");
            //要触发的事件
            if (option.val() == "1") {
                $(".isDwreport").show()
                $(".isBuildis").hide()
            } else {
                $(".isDwreport").hide()
                $(".isBuildis").show()
            }
        })
        // 是否开展
        $("#isLawcheck").change(function () {
            var option = $("#isLawcheck option:selected");
            //要触发的事件
            if (option.val() == "1") {
                $(".isLawcheckShow").show()
            } else {
                $(".isLawcheckShow").hide()
            }
        })
        // 举报奖励制度执行情况
        $(".isTypical").change(function () {
            debugger
            var option = $("#isCover option:selected");
            var options = $("#isTypicalcase option:selected");
            //要触发的事件
            if (option.val() == "1" || options.val() == "1") {
                $(".isTypicalcase").show()
            } else {
                $(".isTypicalcase").hide()
            }
        })

        // 是否辖区内所有地市级生态环境部门均公布正面清单企业名单，并实施动态管理
        $("#isPositivelistWarnShow").change(function () {
            var option = $("#isPositivelistWarn option:selected");
            var options = $("#isPositivelistManage option:selected");
            //要触发的事件
            if (option.val() == "1" || options.val() == "1") {
                $(".isPositivelistSup").show()
            } else {
                $(".isPositivelistSup").hide()
            }
        })
        // 是否指导组织地市级生态环境部门通过座谈、宣讲、培训等方式对正面清单企业开展帮扶指导和提醒预警
        // $("#isPositivelistManage").change(function () {
        //     var option = $("#isPositivelistManage option:selected");
        //     //要触发的事件
        //     if (option.val() == "1") {
        //         $(".isPositivelistSup").show()
        //     } else {
        //         $(".isPositivelistSup").hide()
        //     }
        // })

        // 是否指导所有地市级生态环境部门开展针对中小企业重点执法帮扶、普法培训、“送法入企”活动
        $("#isGuide").change(function () {
            var option = $("#isGuide option:selected");
            //要触发的事件
            if (option.val() == "1") {
                $(".isGuideShow").show()
            } else {
                $(".isGuideShow").hide()
            }
        })
        // 是否建立省级执法人才库
        $("#isPeopool").change(function () {
            var option = $("#isPeopool option:selected");
            //要触发的事件
            if (option.val() == "1") {
                $(".ispeopoolShow").show()
            } else {
                $(".ispeopoolShow").hide()
            }
        })
        //  开展培训（岗位培训除外）的动态或宣传情况
        $("#isTrain").change(function () {
            var option = $("#isTrain option:selected");
            //要触发的事件
            if (option.val() == "1") {
                $(".istrainShow").show()
            } else {
                $(".istrainShow").hide()
            }
        })
        //  开展培训（岗位培训除外）的动态或宣传情况
        $("#isLicensespecial").change(function () {
            var option = $("#isLicensespecial option:selected");
            //要触发的事件
            if (option.val() == "1") {
                $(".isLicenseShow").show()
            } else {
                $(".isLicenseShow").hide()
            }
        })
        //是否开展排污许可清单式执法
        $("#isLicenselist").change(function () {
            var option = $("#isLicenselist option:selected");
            //要触发的事件
            if (option.val() == "1") {
                $(".islicenselistShow").show()
            } else {
                $(".islicenselistShow").hide()
            }
        })



        $("#case_end_type").change(function () {
            var option = $("#case_end_type option:selected");
            //要触发的事件
            if (option.val() == "2") {
                $("#isCaseTypeShow").show()
            } else if (option.val() == "3") {
                $("#isCaseTypeShow").hide()
                $("#isCaseTypeShow2").show()
            }
        })


    })



    //时间转换方法
    function timeZh(come, index) {
        var cpmedate = new Date(come);
        console.log("cpmedate", cpmedate)
        var dateNow = cpmedate.getDate();
        if (cpmedate.getDate() < 10) {
            dateNow = "0" + cpmedate.getDate();
        }
        var month = cpmedate.getMonth() + 1;
        if (month < 10) {
            month = "0" + month;
        }
        var ssss = cpmedate.getFullYear() + "-"+ month + "-" + dateNow;
        if (index == 1) {
            $("#ctsj1").val(ssss)
        }
        if (index == 2) {
            $("#ctsj2").val(ssss)
        }
        if (index == 3) {
            $("#ctsj3").val(ssss)
        }

    }


    function clearNoNum(obj) {
        obj.value = obj.value.replace(/[^\d.]/g, ""); //清除“数字”和“.”以外的字符
        obj.value = obj.value.replace(/^\./g, ""); //验证第一个字符是数字而不是.
        obj.value = obj.value.replace(/\.{2,}/g, "."); //只保留第一个. 清除多余的.
        obj.value = obj.value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
        //判断百分比不能超过100%


        if (parseFloat(obj.value) > 100) {
            obj.value = "";
            return;
        }
        ;
        if (obj.value.split(".")[0].length > 1) {
            if (obj.value.split(".")[0].substring(0, 1) == '0') {
                obj.value = "";
                return;
            }
        }
        //如果有小数点，判断 只能保留两位
        if (obj.value.indexOf(".") > -1) {
            if (obj.value.split(".")[1].length > 3) {
                obj.value = "";
                return;
            }
        }
    }

    function downloadFile(url, fileName) {
        if (url != null && url != "") {
            console.log(url)
            location.href = "${webpath}/filedownload.do?url=" + url + "&fileName=" + fileName;
        } else {
            swal({title: "提示", text: "您下载的附件不存在！", type: "info"});
        }
    };


    //表单校验
    //表单校验
    /*	$(document).ready(function() {
		 $('#ajslForm').formValidation({

			message : 'This value is not valid',
			/!*icon : {
				valid : 'glyphicon glyphicon-ok',
				invalid : 'glyphicon glyphicon-remove',
				validating : 'glyphicon glyphicon-refresh'
			},*!/
			autoFocus : true,
			/!*"propagandize" : {
				verbose: false,
				validators : {
					/!*notEmpty : {
                        message : '请填写省级及以上平台媒体宣传材料数量.'
                    },*!/
					regexp : {
						regexp : /^[0-9]+$/,
						message : '该项只能填写整数.'
					},
					greaterThan : {
						value : 0,
						message : ' '
					},
					lessThan : {
						value : 10000,
						message : '该项为小于等于10000的整数.'
					}
				}
			}*!/
			/!*fields : {
				"companyname" : {
					validators : {
						notEmpty : {
							message : '请填写机构名称（全称）.'
						}
					}
				}*!/
				/!*"polluternumber" : {
                    verbose: false,
                    validators : {
                        notEmpty : {
                            message : '请填写全省重点排污单位数量.'
                        },
                        regexp : {
                            regexp : /^[0-9]+$/,
                            message : '该项只能填写整数.'
                        },
                        greaterThan : {
                            value : 0,
                            message : ' '
                        },
                        lessThan : {
                            value : 10000,
                            message : '该项为小于等于10000的整数.'
                        }
                    }
                },*!/
			//}
		});
	});*/
    //表单提交
    $(document).ready(function () {
        $("#xxcj_AJSL_Butt").click(function () {
            var validate = true;
            //$("#ajslForm").data('formValidation').validate();
            //validate = $("#ajslForm").data('formValidation').isValid();
            if (validate) {
                //上传文件验证必填
                //激励奖励相关文件
                var filen1 = $("#jili_urlname").val();
                var fileu1 = $("#jili_url").val();

                //知识竞赛支撑材料
                var filen2 = $("#jw_inform_urlname").val();
                var fileu2 = $("#jw_inform_url").val();

                //竞赛比武活动网页截图
                var filen3 = $("#jw_web_urlname").val();
                var fileu3 = $("#jw_web_url").val();

                //案卷评查支撑材料
                var filen4 = $("#pingcha_urlname").val();
                var fileu4 = $("#pingcha_url").val();

                //着装及队列演练支撑材料
                var filen5 = $("#worksummary_urlname").val();
                var fileu5 = $("#worksummary_url").val();


                //规范化建设支撑材料
                var filen6 = $("#caseinfo_urlname").val();
                var fileu6 = $("#caseinfo_url").val();

                //考核奖惩制定情况支撑材料
                var filen7 = $("#case_end_urlname").val();
                var fileu7 = $("#case_end_url").val();

                //考核奖惩执行情况支撑材料
                var filen8 = $("#specificConditionFile_Filename").val();
                var fileu8 = $("#specificConditionFile_Fileurl").val();

                //人身安全保障制定情况支撑材料
                var filen9 = $("#rsAnQuanFile_Filename").val();
                var fileu9 = $("#rsAnQuanFile_Fileurl").val();

                //人身安全保障执行情况支撑材料
                var filen10 = $("#rsExecuteFile_Filename").val();
                var fileu10 = $("#rsExecuteFile_Fileurl").val();

                //尽职照单免责和失职照单问责制定情况支撑材料
                var filen11 = $("#jzSzCustomFile_Filename").val();
                var fileu11 = $("#jzSzCustomFile_Fileurl").val();

                //尽职照单免责和失职照单问责执行情况支撑材料
                var filen12 = $("#jzSzExecuteFile_Filename").val();
                var fileu12 = $("#jzSzExecuteFile_Fileurl").val();

                //执法事项目录支撑材料
                var filen13 = $("#zfItemFile_Filename").val();
                var fileu13 = $("#zfItemFile_Fileurl").val();

                //监督执法正面清单制度支撑材料
                var filen14 = $("#jdLawFile_Filename").val();
                var fileu14 = $("#jdLawFile_Fileurl").val();

                //非现场监管程序规范支撑材料
                var filen15 = $("#fxcRoutineFile_Filename").val();
                var fileu15 = $("#fxcRoutineFile_Fileurl").val();

                //履职责任制度支撑材料
                var filen16 = $("#lzDutyFile_Filename").val();
                var fileu16 = $("#lzDutyFile_Fileurl").val();

                //双随机、一公开监管制度支撑材料
                var filen17 = $("#ssjOpenSystem_Filename").val();
                var fileu17 = $("#ssjOpenSystem_Fileurl").val();

                //典型执法案例指导制度支撑材料
                var filen18 = $("#dxLawFile_Filename").val();
                var fileu18 = $("#dxLawFile_Fileurl").val();

                //执法监测工作机制支撑材料
                var filen19 = $("#zfJcWorkFile_Filename").val();
                var fileu19 = $("#zfJcWorkFile_Fileurl").val();

                //竞赛比武支撑材料
                var filen20 = $("#jcBwFile_Filename").val();
                var fileu20 = $("#jcBwFile_Fileurl").val();

                /* //执法公众满意度支撑材料
                 var filen21 = $("#lawSatisficingFile_Filename").val();
                 var fileu21 = $("#lawSatisficingFile_Fileurl").val();*/

                //监督帮扶典型案例支撑材料
                var filen24 = $("#jdbfFileName_Filename").val();
                var fileu24 = $("#jdbfFileUrl_Fileurl").val();

                //专项行动表现支撑材料
                var filen26 = $("#zxxdExpressionFileName_Filename").val();
                var fileu26 = $("#zxxdExpressionFileUrl_Fileurl").val();

                //活动总结支撑材料
                var filen22 = $("#activityReport_Filename").val();
                var fileu22 = $("#activityReport_Fileurl").val();

                //查处弄虚作假违法犯罪支撑材料
                var filen23 = $("#nxzjCaseFileName_Filename").val();
                var fileu23 = $("#nxzjCaseFileUrl_Fileurl").val();


                //验证字段

                var ver = verify();
                if (ver == false) {
                    return false;
                }
                if (filen1 != "" && fileu1 != "") {
                    if (filen2 != "" && fileu2 != "") {
                        if (filen3 != "" && fileu3 != "") {
                            if (filen4 != "" && fileu4 != "") {
                                if (filen5 != "" && fileu5 != "") {
                                    if (filen6 != "" && fileu6 != "") {
                                        if (filen7 != "" && fileu7 != "") {
                                            if (filen8 != "" && fileu8 != "") {
                                                if (filen9 != "" && fileu9 != "") {
                                                    if (filen10 != "" && fileu10 != "") {
                                                        if (filen11 != "" && fileu11 != "") {
                                                            if (filen12 != "" && fileu12 != "") {
                                                                if (filen13 != "" && fileu13 != "") {
                                                                    if (filen14 != "" && fileu14 != "") {
                                                                        if (filen15 != "" && fileu15 != "") {
                                                                            if (filen16 != "" && fileu16 != "") {
                                                                                if (filen17 != "" && fileu17 != "") {
                                                                                    if (filen18 != "" && fileu18 != "") {
                                                                                        if (filen19 != "" && fileu19 != "") {
                                                                                            if (filen20 != "" && fileu20 != "") {
                                                                                                if (filen24 != "" && fileu24 != "") {
                                                                                                    if (filen26 != "" && fileu26 != "") {
                                                                                                        if (filen22 != "" && fileu22 != "") {
                                                                                                            if (filen23 != "" && fileu23 != "") {
                                                                                                                var options = {
                                                                                                                    url: WEBPATH + '/xxcj/saveProvinceBase.do?isRecommend=1',
                                                                                                                    type: 'post',
                                                                                                                    success: function (data) {
                                                                                                                        console.log("data", data)
                                                                                                                        if (data.result == "error") {
                                                                                                                            swal({
                                                                                                                                title: "保存失败!",
                                                                                                                                text: data.message,
                                                                                                                                type: "error",
                                                                                                                                confirmButtonColor: "#d9534f"
                                                                                                                            });
                                                                                                                            return false;
                                                                                                                        } else if (data.result == "success") {
                                                                                                                            swal({
                                                                                                                                title: "保存成功!",
                                                                                                                                type: "success",
                                                                                                                                closeOnConfirm: true,
                                                                                                                                confirmButtonText: "确定",
                                                                                                                                confirmButtonColor: "#d9534f"
                                                                                                                            }, function () {
                                                                                                                                business.addMainContentParserHtml('xxcj/provinceBase.do', '');
                                                                                                                            });
                                                                                                                            return false;
                                                                                                                        } else {
                                                                                                                            swal({
                                                                                                                                title: "请勿重复提交表单!",
                                                                                                                                type: "warning",
                                                                                                                                // text:"请勿重复提交表单！",
                                                                                                                                closeOnConfirm: true,
                                                                                                                                confirmButtonText: "确定",
                                                                                                                                confirmButtonColor: "#d9534f"
                                                                                                                            }, function () {
                                                                                                                                business.addMainContentParserHtml('xxcj/provinceBase.do', '');

                                                                                                                            });
                                                                                                                        }
                                                                                                                    },
                                                                                                                    error: function () {
                                                                                                                        swal({
                                                                                                                            title: "服务异常,保存失败!",
                                                                                                                            text: "",
                                                                                                                            type: "error",
                                                                                                                            confirmButtonColor: "#d9534f"
                                                                                                                        });
                                                                                                                    }
                                                                                                                };
                                                                                                                $("#ajslForm").ajaxSubmit(options);
                                                                                                            } else {
                                                                                                                swal({
                                                                                                                    title: "未上传附件",
                                                                                                                    text: "请上传查处弄虚作假违法犯罪支撑材料!",
                                                                                                                    type: "error",
                                                                                                                    confirmButtonColor: "#d9534f"
                                                                                                                });
                                                                                                                return false;
                                                                                                            }
                                                                                                        } else {
                                                                                                            swal({
                                                                                                                title: "未上传附件",
                                                                                                                text: "请上传活动总结支撑材料!",
                                                                                                                type: "error",
                                                                                                                confirmButtonColor: "#d9534f"
                                                                                                            });
                                                                                                            return false;
                                                                                                        }
                                                                                                    } else {
                                                                                                        swal({
                                                                                                            title: "未上传附件",
                                                                                                            text: "请上传专项行动表现支撑材料!",
                                                                                                            type: "error",
                                                                                                            confirmButtonColor: "#d9534f"
                                                                                                        });
                                                                                                        return false;
                                                                                                    }
                                                                                                } else {
                                                                                                    swal({
                                                                                                        title: "未上传附件",
                                                                                                        text: "请上传监督帮扶典型案例支撑材料!",
                                                                                                        type: "error",
                                                                                                        confirmButtonColor: "#d9534f"
                                                                                                    });
                                                                                                    return false;
                                                                                                }
                                                                                            } else {
                                                                                                swal({
                                                                                                    title: "未上传附件",
                                                                                                    text: "请上传竞赛比武支撑材料!",
                                                                                                    type: "error",
                                                                                                    confirmButtonColor: "#d9534f"
                                                                                                });
                                                                                                return false;
                                                                                            }
                                                                                        } else {
                                                                                            swal({
                                                                                                title: "未上传附件",
                                                                                                text: "请上传执法监测工作机制支撑材料!",
                                                                                                type: "error",
                                                                                                confirmButtonColor: "#d9534f"
                                                                                            });
                                                                                            return false;
                                                                                        }
                                                                                    } else {
                                                                                        swal({
                                                                                            title: "未上传附件",
                                                                                            text: "请上传典型执法案例指导制度支撑材料!",
                                                                                            type: "error",
                                                                                            confirmButtonColor: "#d9534f"
                                                                                        });
                                                                                        return false;
                                                                                    }
                                                                                } else {
                                                                                    swal({
                                                                                        title: "未上传附件",
                                                                                        text: "请上传双随机、一公开监管制度支撑材料!",
                                                                                        type: "error",
                                                                                        confirmButtonColor: "#d9534f"
                                                                                    });
                                                                                    return false;
                                                                                }
                                                                            } else {
                                                                                swal({
                                                                                    title: "未上传附件",
                                                                                    text: "请上传履职责任制度支撑材料!",
                                                                                    type: "error",
                                                                                    confirmButtonColor: "#d9534f"
                                                                                });
                                                                                return false;
                                                                            }
                                                                        } else {
                                                                            swal({
                                                                                title: "未上传附件",
                                                                                text: "请上传非现场监管程序规范支撑材料!",
                                                                                type: "error",
                                                                                confirmButtonColor: "#d9534f"
                                                                            });
                                                                            return false;
                                                                        }
                                                                    } else {
                                                                        swal({
                                                                            title: "未上传附件",
                                                                            text: "请上传监督执法正面清单制度支撑材料!",
                                                                            type: "error",
                                                                            confirmButtonColor: "#d9534f"
                                                                        });
                                                                        return false;
                                                                    }
                                                                } else {
                                                                    swal({
                                                                        title: "未上传附件",
                                                                        text: "请上传执法事项目录支撑材料!",
                                                                        type: "error",
                                                                        confirmButtonColor: "#d9534f"
                                                                    });
                                                                    return false;
                                                                }
                                                            } else {
                                                                swal({
                                                                    title: "未上传附件",
                                                                    text: "请上传尽职照单免责和失职照单问责执行情况支撑材料!",
                                                                    type: "error",
                                                                    confirmButtonColor: "#d9534f"
                                                                });
                                                                return false;
                                                            }
                                                        } else {
                                                            swal({
                                                                title: "未上传附件",
                                                                text: "请上传尽职照单免责和失职照单问责制定情况支撑材料!",
                                                                type: "error",
                                                                confirmButtonColor: "#d9534f"
                                                            });
                                                            return false;
                                                        }
                                                    } else {
                                                        swal({
                                                            title: "未上传附件",
                                                            text: "请上传人身安全保障执行情况支撑材料!",
                                                            type: "error",
                                                            confirmButtonColor: "#d9534f"
                                                        });
                                                        return false;
                                                    }
                                                } else {
                                                    swal({
                                                        title: "未上传附件",
                                                        text: "请上传人身安全保障制定情况支撑材料!",
                                                        type: "error",
                                                        confirmButtonColor: "#d9534f"
                                                    });
                                                    return false;
                                                }
                                            } else {
                                                swal({
                                                    title: "未上传附件",
                                                    text: "请上传考核奖惩执行情况支撑材料!",
                                                    type: "error",
                                                    confirmButtonColor: "#d9534f"
                                                });
                                                return false;
                                            }
                                        } else {
                                            swal({
                                                title: "未上传附件",
                                                text: "请上传考核奖惩制定情况支撑材料!",
                                                type: "error",
                                                confirmButtonColor: "#d9534f"
                                            });
                                            return false;
                                        }
                                    } else {
                                        swal({
                                            title: "未上传附件",
                                            text: "请上传规范化建设支撑材料!",
                                            type: "error",
                                            confirmButtonColor: "#d9534f"
                                        });
                                        return false;
                                    }
                                } else {
                                    swal({
                                        title: "未上传附件",
                                        text: "请上传着装及队列演练支撑材料!",
                                        type: "error",
                                        confirmButtonColor: "#d9534f"
                                    });
                                    return false;
                                }
                            } else {
                                swal({
                                    title: "未上传附件",
                                    text: "请上传案卷评查支撑材料!",
                                    type: "error",
                                    confirmButtonColor: "#d9534f"
                                });
                                return false;
                            }
                        } else {
                            swal({
                                title: "未上传附件",
                                text: "请上传激励措施支撑材料!",
                                type: "error",
                                confirmButtonColor: "#d9534f"
                            });
                            return false;
                        }
                    } else {
                        swal({
                            title: "未上传附件",
                            text: "请上传开展大练兵宣传支撑材料!",
                            type: "error",
                            confirmButtonColor: "#d9534f"
                        });
                        return false;
                    }
                } else {
                    swal({
                        title: "未上传附件",
                        text: "请上传知识竞赛支撑材料!",
                        type: "error",
                        confirmButtonColor: "#d9534f"
                    });
                    return false;
                }


            } else if (validate == null) {
                //表单未填写
                $("#ajslForm").data('formValidation').validate();
            }
        })

        //信息暂存
        $("#xxcj_XXZC_Butt").click(function () {
            var options = {
                url: WEBPATH + '/xxcj/saveProvinceBase.do?isRecommend=0',
                type: 'post',
                success: function (data) {
                    if (data.result == "error") {
                        swal({title: "保存失败!", text: data.message, type: "error", confirmButtonColor: "#d9534f"});
                        return false;
                    } else if (data.result == "success") {
                        swal({
                            title: "保存成功!",
                            type: "success",
                            closeOnConfirm: true,
                            confirmButtonText: "确定",
                            confirmButtonColor: "#d9534f"
                        }, function () {
                            business.addMainContentParserHtml('xxcj/provinceBase.do', '');
                        });
                        return false;
                    }
                },
                error: function () {
                    swal({title: "服务异常,保存失败!", text: "", type: "error", confirmButtonColor: "#d9534f"});
                }
            };
            $("#ajslForm").ajaxSubmit(options);

        })

    });

    $(document).ready(function () {
        $(".topnav").accordion({
            accordion: false,
            speed: 500,
            closedSign: '[+]',
            openedSign: '[-]'
        });
    });

    function down(val) {
        var name = "";
        if (val == 1) {
            name = "省级大练兵组织情况excel模板";
        } else if (val == 2) {
            name = "队伍建设与管理excel模板";
        } else if (val == 3) {
            name = "监督帮扶典型案例excel模板";
        } else if (val == 4) {
            name = "竞赛比武excel模板";
        } else if (val == 5) {
            name = "省专项行动表现";
        }

        var path = "${webpath}/xxcj/xcclExcel.do";
        window.location.href = path + "?name=" + name;
    }


    //验证字段方法
    function verify() {

        //机构名称
        var agencyName = $("#agency_name").val();
        if (agencyName == "") {
            swal("机构名称为必填项", "操作失败了!", "error");
            return false;
        }

        // 是否采取激励措施
        var is_jili = $("#is_jili").val();
        if (is_jili == "") {
            swal("是否采取激励措施为必填项", "操作失败了!", "error");
            return false;
        }
        // 激励形式
        var jiliName = $("#jiliName").val();
        if (jiliName == "") {
            swal("激励形式为必填项", "操作失败了!", "error");
            return false;
        }
        //知识竞赛 是否开展
        var isUnfold = $("#isUnfold").val();
        if (isUnfold == "") {
            swal("知识竞赛 是否开展为必填项", "操作失败了!", "error");
            return false;
        }
        //参与范围
        var parScopes = $("#parScopes").val();
        if (parScopes == "") {
            swal("参与范围为必填项", "操作失败了!", "error");
            return false;
        }
        //机构领导是否带头参与
        var jgld = $("#jgld").val();
        if (jgld == "") {
            swal("机构领导是否带头参与为必填项", "操作失败了!", "error");
            return false;
        }
        //具体领导职务
        // if (jgld == "1") {
        //     var leaderDuty = $("#leaderDuty").val();
        //     if (leaderDuty == "") {
        //         swal("具体领导职务为必填项", "操作失败了!", "error");
        //         return false;
        //     }
        // }
        //参与率
        // var parRate = $("#parRate").val();
        // if (parRate == "") {
        //     swal("参与率为必填项", "操作失败了!", "error");
        //     return false;
        // }
        //是否开设大练兵专栏
        var isDlbSpecial = $("#isDlbSpecial").val();
        if (isDlbSpecial == "") {
            swal("是否开设大练兵专栏为必填项", "操作失败了!", "error");
            return false;
        }
        // 是否定期发布练兵动态
        var isIssueDyn = $("#isIssueDyn").val();
        if (isIssueDyn == "") {
            swal("是否定期发布练兵动态为必填项", "操作失败了!", "error");
            return false;
        }
        // 大练兵专栏网址
        var dlbSpecialUrl = $("#dlbSpecialUrl").val();
        if (dlbSpecialUrl == "") {
            swal("大练兵专栏网址为必填项", "操作失败了!", "error");
            return false;
        }
        //  宣传数量
        var propagandize = $("#propagandize").val();
        if (propagandize == "") {
            swal("宣传数量为必填项", "操作失败了!", "error");
            return false;
        }

        // 是否制定评查方案
        // var isZdscheme = $("#isZdscheme").val();
        // if (isZdscheme == "") {
        //     swal("是否制定评查方案为必填项", "操作失败了!", "error");
        //     return false;
        // }
        // 是否达到评查比例
        // var isRatio = $("#isRatio").val();
        // if (isRatio == "") {
        //     swal("是否达到评查比例为必填项", "操作失败了!", "error");
        //     return false;
        // }
        // 是否形成典型案例汇编
        // var isModelCase = $("#isModelCase").val();
        // if (isModelCase == "") {
        //     swal("是否形成典型案例汇编为必填项", "操作失败了!", "error");
        //     return false;
        // }
        //  是否开展队列训练活动
        var isCarryOut = $("#isCarryOut").val();
        if (isCarryOut == "") {
            swal("是否开展队列训练活动为必填项", "操作失败了!", "error");
            return false;
        }
        // 考核奖惩 是否制定
        var isCustomization = $("#isCustomization").val();
        if (isCustomization == "") {
            swal("考核奖惩 是否制定为必填项", "操作失败了!", "error");
            return false;
        }
        // 考核奖惩 文件类型
        var custFileType = $("#custFileType").val();
        if (custFileType == "") {
            swal("考核奖惩 文件类型为必填项", "操作失败了!", "error");
            return false;
        }
        // 考核奖惩 出台时间
        var ctsj1 = $("#ctsj1").val();
        if (ctsj1 == "") {
            swal("考核奖惩 出台时间为必填项", "操作失败了!", "error");
            return false;
        }
        // 考核奖惩 执行情况 是否执行
        var isExecute = $("#isExecute").val();
        if (isExecute == "") {
            swal("考核奖惩 是否执行为必填项", "操作失败了!", "error");
            return false;
        }
        // 考核奖惩 执行情况 具体情况
        if (specificCondition == "") {
            swal("考核奖惩 具体情况为必填项", "操作失败了!", "error");
            return false;
        }
        // 人身安全保障 是否制定
        var isEnact = $("#isEnact").val();
        if (isEnact == "") {
            swal("人身安全保障 是否制定为必填项", "操作失败了!", "error");
            return false;
        }
        // 人身安全保障 文件类型
        var rsFileType = $("#rsFileType").val();
        if (rsFileType == "") {
            swal("人身安全保障 文件类型为必填项", "操作失败了!", "error");
            return false;
        }
        // 人身安全保障 出台时间
        var ctsj2 = $("#ctsj2").val();
        if (ctsj2 == "") {
            swal("人身安全保障 出台时间为必填项", "操作失败了!", "error");
            return false;
        }
        // 人身安全保障 执行情况 是否执行
        var isRsExecute = $("#isRsExecute").val();
        if (isRsExecute == "") {
            swal("人身安全保障 是否执行为必填项", "操作失败了!", "error");
            return false;
        }
        // 人身安全保障 执行情况 具体情形
        var rsSpecific = $("#rsSpecific").val();
        if (rsSpecific == "") {
            swal("人身安全保障 具体情形为必填项", "操作失败了!", "error");
            return false;
        }
        // 尽职照单免责和失职照单问责 制定情况
        var isDingZhi = $("#isDingZhi").val();
        if (isDingZhi == "") {
            swal("尽职照单免责和失职照单问责 是否制定为必填项", "操作失败了!", "error");
            return false;
        }
        // 尽职照单免责和失职照单问责 文件类型
        var jzSzFileType = $("#jzSzFileType").val();
        if (jzSzFileType == "") {
            swal("尽职照单免责和失职照单问责 文件类型为必填项", "操作失败了!", "error");
            return false;
        }
        // 尽职照单免责和失职照单问责 出台时间
        var ctsj3 = $("#ctsj3").val();
        if (ctsj3 == "") {
            swal("尽职照单免责和失职照单问责 出台时间为必填项", "操作失败了!", "error");
            return false;
        }
        // 尽职照单免责和失职照单问责 执行情况 是否执行
        var isZhiXing = $("#isZhiXing").val();
        if (isZhiXing == "") {
            swal("尽职照单免责和失职照单问责 是否执行为必填项", "操作失败了!", "error");
            return false;
        }
        // 尽职照单免责和失职照单问责 执行情况 具体情况
        var jzSzCondition = $("#jzSzCondition").val();
        if (jzSzCondition == "") {
            swal("尽职照单免责和失职照单问责 具体情况为必填项", "操作失败了!", "error");
            return false;
        }
        // 执法事项目录 是否制定
        var isZfEnact = $("#isZfEnact").val();
        if (isZfEnact == "") {
            swal("执法事项目录 是否制定为必填项", "操作失败了!", "error");
            return false;
        }

        // 监督执法正面清单制度 是否制定
        var isJdEnact = $("#isJdEnact").val();
        if (isJdEnact == "") {
            swal("监督执法正面清单制度 是否制定为必填项", "操作失败了!", "error");
            return false;
        }
        // 非现场监管程序规范 是否制定
        var isFxcEnact = $("#isFxcEnact").val();
        if (isFxcEnact == "") {
            swal("非现场监管程序规范 是否制定为必填项", "操作失败了!", "error");
            return false;
        }
        // 履职责任制度 是否制定
        var isLzEnact = $("#isLzEnact").val();
        if (isLzEnact == "") {
            swal("履职责任制度 是否制定为必填项", "操作失败了!", "error");
            return false;
        }
        //
        // 双随机、一公开监管制度 是否制定
        var isSsjEnact = $("#isSsjEnact").val();
        if (isSsjEnact == "") {
            swal("双随机、一公开监管制度 是否制定为必填项", "操作失败了!", "error");
            return false;
        }
        // 典型执法案例指导制度 是否制定
        var isDxLawEnact = $("#isDxLawEnact").val();
        if (isDxLawEnact == "") {
            swal("典型执法案例指导制度 是否制定为必填项", "操作失败了!", "error");
            return false;
        }
        // 执法监测工作机制 是否制定
        var isZfJcEnact = $("#isZfJcEnact").val();
        if (isZfJcEnact == "") {
            swal("执法监测工作机制 是否制定为必填项", "操作失败了!", "error");
            return false;
        }
        // 竞赛比武 是否开展
        var isjcBwCarry = $("#isjcBwCarry").val();
        if (isjcBwCarry == "") {
            swal("竞赛比武 是否开展为必填项", "操作失败了!", "error");
            return false;
        }
        // 竞赛比武 是否联合其他部门开展
        var isUniteOtherCarry = $("#isUniteOtherCarry").val();
        if (isUniteOtherCarry == "") {
            swal("竞赛比武 是否联合其他部门开展为必填项", "操作失败了!", "error");
            return false;
        }
        //省级生态环境部门公开典型案例数
        var jdbfModelNum = $("#jdbfModelNum").val();
        if (jdbfModelNum == "") {
            swal("省级生态环境部门公开典型案例数为必填项", "操作失败了!", "error");
            return false;
        }
        //报送生态环境部典型案例数量
        var jdbfSubmisModelNum = $("#jdbfSubmisModelNum").val();
        if (jdbfSubmisModelNum == "") {
            swal("报送生态环境部典型案例数量为必填项", "操作失败了!", "error");
            return false;
        }
        //查处涉废矿物油环境违法犯罪案件数
        var zxxdDelinquencyNum = $("#zxxdDelinquencyNum").val();
        if (zxxdDelinquencyNum == "") {
            swal("查处涉废矿物油环境违法犯罪案件数为必填项", "操作失败了!", "error");
            return false;
        }
        //对跨区域非法排放、倾倒、处置危险废物案件试行提级查办案例数
        var zxxdRiskTrashNum = $("#zxxdRiskTrashNum").val();
        if (zxxdRiskTrashNum == "") {
            swal("对跨区域非法排放、倾倒、处置危险废物案件试行提级查办案例数为必填项", "操作失败了!", "error");
            return false;
        }
        //候选集体移交篡改、伪造自动监测数据或干扰自动监控设施等逃避监管的违法犯罪案件数
        var zxxdForgeNum = $("#zxxdForgeNum").val();
        if (zxxdForgeNum == "") {
            swal("候选集体移交篡改、伪造自动监测数据或干扰自动监控设施等逃避监管的违法犯罪案件数为必填项", "操作失败了!", "error");
            return false;
        }

        //查处弄虚作假违法犯罪案件数
        var nxzjCaseSum = $("#nxzjCaseSum").val();
        if (nxzjCaseSum == "") {
            swal("查处弄虚作假违法犯罪案件数为必填项", "操作失败了!", "error");
            return false;
        }
        //辖区内是否30%以上的设区市都查处了数据造假违法犯罪案件
        var isZjCase = $("#isZjCase").val();
        if (isZjCase == "") {
            swal("辖区内是否30%以上的设区市都查处了数据造假违法犯罪案件为必填项", "操作失败了!", "error");
            return false;
        }
    }
</script>
</body>
</html>
