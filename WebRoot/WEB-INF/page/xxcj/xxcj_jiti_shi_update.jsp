<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
<script type="text/javascript">
	$(document).ready(function(){
		
		//文件上传、
		 $("#file1").fileinput({
	      uploadUrl: WEBPATH+'/pdffileupload.do', // you must set a valid URL here else you will get an error
	      allowedFileExtensions : ['doc','docx'],
	      language:'zh',
	      minFileCount: 1,
	      maxFileCount: 1,
	      minFileSize:1,
	      maxFileSize:51200,
	      enctype: 'multipart/form-data',        
	      showPreview:false,
	      autoReplace:true,
	      slugCallback: function(filename) {
	          return filename.replace('(', '_').replace(']', '_');
	      }
		}).on('filepreupload', function(event, data, previewId, index) { //文件开始上传操作
		   $("#saveXxcjPersonBtn").prop('disabled', true);
		}).on('fileuploaded', function(event, data, previewId, index) {//文件上传完成执行操作
			$("#teamMaterialName").val(data.response.fileRealName);
		  	$("#teamMaterialURL").val(data.response.url);
		  	$("#jtcl").text(data.response.fileRealName);
			$("#saveXxcjPersonBtn").prop('disabled',false);
		})
	  
		//保存数据方法
		$('#saveCityGroupBtn').click(function() {
	       	 var options = {
	           url: WEBPATH+'/xxcj/updateXxcjGroup.do',
	           type: 'post',
	           success:function(data){
		           if(data.result=="error"){
		        	   swal("操作失败",data.message, "error");
		               return false;
		           }else if(data.result=="success"){
		        	  business.addMainContentParserHtml('xxcj/xxcjcityGroupList.do',null);
		        	  swal({title: "操作成功",text: "",type:"success"});
		             return false;
		           }else if(data.code == '007'){
	            		 swal({ title : data.message, text : "", type : "info" });
	            		 return false;
	               }
	         	}
	       	 };
	       	
	       	$("#saveCityGroupForm").data('formValidation').validate();
	       	var validate = $("#saveCityGroupForm").data('formValidation').isValid();
	       	if(validate){
	           	var xzcf1=$("#typeaheadxxcf1").val();
	           	if(xzcf1!=""&&xzcf1!=null){
   			 			var xzcf2=$("#typeaheadxxcf2").val();
     			 		if(xzcf2!=""&&xzcf2!=null){
 			 				if(xzcf1==xzcf2){
  			 					swal({title: "提示",text: "不能选择相同的行政处罚案卷",type:"error"});
     			           		return;
 			 				}
 			 			}
	           	}else{
	           		swal({title: "提示",text: "请选择行政处罚案卷1",type:"error"});
	           	}
	           	
	           	var tn=$("#teamMaterialName").val();
				var tu=$("#teamMaterialURL").val();
				if(tn!=""&&tn!=null&&tu!=""&&tu!=null){
					$('#saveCityGroupForm').ajaxSubmit(options);
				}else{
					swal("提示", "请上传集体事迹材料", "error"); 
				}
	           	
	       	}else{
		       	 $("#saveCityGroupForm").data('formValidation').validate();
		    	 return false;
		     } 
	   	});
	          
		//表单验证
		//表单校验
		$("#saveCityGroupForm").formValidation({
	        framework: 'bootstrap',
	        message: 'This value is not valid',
	        icon:{
		            valid: 'glyphicon glyphicon-ok',
		            invalid: 'glyphicon glyphicon-remove',
		            validating: 'glyphicon glyphicon-refresh'
	        },
	        fields:{
	        	"anjuanwenhao1": {
	                validators: {
	                	notEmpty: {
	                        message: '请选择案卷文号'
	                    }
	                }
	            },
	        	"filesList[0].filetype": {
	                validators: {
	                	notEmpty: {
	                        message: '请选择案卷类型'
	                    }
	                }
	            },
	            "filesList[0].filesimpledesc": {
	                validators: {
	                	stringLength: {
	                        min: 1,
	                        max: 150,
	                        message: '请输入1-150个字符'
	                    }
	                }
	            },
	            "filesList[0].filedetiaildesc": {
	                validators: {
	                	stringLength: {
	                        min: 1,
	                        max: 2000,
	                        message: '请输入1-2000个字符'
	                    }
	                }
	            },
	            "filesList[0].isPublic": {
	                validators: {
	                    notEmpty: {
	                        message: '请选择处罚情况是否在门户网站公开'
	                    },
	                    callback: {
	                       	message: '',
	                           callback: function(value, validator, $field) {
	                           	if(value=='1'){
	                           		$("#saveCityGroupForm").formValidation('enableFieldValidators', 'filesList[0].publicAddress', true);
	                           	}else{
	                           		$("#saveCityGroupForm").formValidation('enableFieldValidators', 'filesList[0].publicAddress', false);
	                           	}
	           					return true;
	                          }
	                     }
	                }
	            },
	            "filesList[0].publicAddress": {
	                validators: {
	                    notEmpty: {
	                        message: '请输入信息公开网址'
	                    }
	                }
	            },
	           
	            //第二份案卷验证
	            "anjuanwenhao2": {
	                validators: {
	                	callback: {
	                       	message: '',
	                           callback: function(value, validator, $field) {
	                        	   var val=$("#typeaheadxxcf2").val();
			                       	if(val!=""&&val!=null&&val!="undefined"){
			                           	if(value=='1'){
			                           		$("#saveCityGroupForm").formValidation('enableFieldValidators', 'filesList[1].publicAddress', true);
			                           	}else if(value=='0'){
			                           		$("#saveCityGroupForm").formValidation('enableFieldValidators', 'filesList[1].publicAddress', false);
			                           	}else{
			                           		$("#saveCityGroupForm").formValidation('enableFieldValidators', 'filesList[1].isPublic', true);
			                           		$("#saveCityGroupForm").formValidation('enableFieldValidators', 'filesList[1].publicAddress', false);
			                           	}
			                           	return true;
			                       	}else{
			                       		$("#saveCityGroupForm").formValidation('enableFieldValidators', 'filesList[1].isPublic', false);
			                       		$("#saveCityGroupForm").formValidation('enableFieldValidators', 'filesList[1].publicAddress', false);
			                       		return true;
			                       	}   	
	                          }
	                     }
	                }
	            },
	            "filesList[1].filesimpledesc": {
	                validators: {
	                	stringLength: {
	                        min: 1,
	                        max: 150,
	                        message: '请输入1-150个字符'
	                    }
	                }
	            },
	            "filesList[1].filedetiaildesc": {
	                validators: {
	                	stringLength: {
	                        min: 1,
	                        max: 2000,
	                        message: '请输入1-2000个字符'
	                    }
	                }
	            },
				"filesList[1].isPublic": {
					validators: {
	                    notEmpty: {
	                        message: '请选择处罚情况是否在门户网站公开'
	                    },
	                    callback: {
	                       	message: '',
	                           callback: function(value, validator, $field) {
	                        	var val=$("#typeaheadxxcf2").val();
		                       	if(val!=""&&val!=null&&val!="undefined"){
		                           	if(value=='1'){
		                           		$("#saveCityGroupForm").formValidation('enableFieldValidators', 'filesList[1].publicAddress', true);
		                           	}else if(value=='0'){
		                           		$("#saveCityGroupForm").formValidation('enableFieldValidators', 'filesList[1].publicAddress', false);
		                           	}else{
		                           		$("#saveCityGroupForm").formValidation('enableFieldValidators', 'filesList[1].isPublic', true);
		                           		$("#saveCityGroupForm").formValidation('enableFieldValidators', 'filesList[1].publicAddress', false);
		                           	}
		                           	return true;
		                       	}else{
		                       		$("#saveCityGroupForm").formValidation('enableFieldValidators', 'filesList[1].isPublic', false);
		                       		$("#saveCityGroupForm").formValidation('enableFieldValidators', 'filesList[1].publicAddress', false);
		                       		return true;
		                       	}   	
	                          }
	                     }
	                }
	            },
	            "filesList[1].publicAddress": {
	            	validators: {
	                    notEmpty: {
	                        message: '请输入信息公开网址'
	                    }
	                }
	            },
	            //第三份案卷验证
	            "anjuanwenhao3":{
	                validators: {
	                	callback: {
	                       	message: '',
	                           callback: function(value, validator, $field) {
	                        	   var transferPoliceType=$("#transferPoliceType").val();
		                        	if(transferPoliceType=='0'){
		                        		var val=$("#typeaheadxzjl").val();
				                       	if(val!=""&&val!=null&&val!="undefined"){
				                           	if(value=='1'){
				                           		$("#saveCityGroupForm").formValidation('enableFieldValidators', 'filesList[2].publicAddress', true);
				                           	}else if(value=='0'){
				                           		$("#saveCityGroupForm").formValidation('enableFieldValidators', 'filesList[2].publicAddress', false);
				                           	}else{
				                           		$("#saveCityGroupForm").formValidation('enableFieldValidators', 'filesList[2].isPublic', true);
				                           		$("#saveCityGroupForm").formValidation('enableFieldValidators', 'filesList[2].publicAddress', false);
				                           	}
				                           	return true;
				                       	}else{
				                       		$("#saveCityGroupForm").formValidation('enableFieldValidators', 'filesList[2].isPublic', false);
				                       		$("#saveCityGroupForm").formValidation('enableFieldValidators', 'filesList[2].publicAddress', false);
				                       		return true;
				                       	}   
		                        	}else if(transferPoliceType=='1'){
		                        		var val=$("#typeaheadsxfz").val();
				                       	if(val!=""&&val!=null&&val!="undefined"){
				                           	if(value=='1'){
				                           		$("#saveCityGroupForm").formValidation('enableFieldValidators', 'filesList[2].publicAddress', true);
				                           	}else if(value=='0'){
				                           		$("#saveCityGroupForm").formValidation('enableFieldValidators', 'filesList[2].publicAddress', false);
				                           	}else{
				                           		$("#saveCityGroupForm").formValidation('enableFieldValidators', 'filesList[2].isPublic', true);
				                           		$("#saveCityGroupForm").formValidation('enableFieldValidators', 'filesList[2].publicAddress', false);
				                           	}
				                           	return true;
				                       	}else{
				                       		$("#saveCityGroupForm").formValidation('enableFieldValidators', 'filesList[2].isPublic', false);
				                       		$("#saveCityGroupForm").formValidation('enableFieldValidators', 'filesList[2].publicAddress', false);
				                       		return true;
				                       	}   
		                        	}  
	                           
	                           }
	                     }
	                }
	            },
	            "filesList[2].filesimpledesc": {
	                validators: {
	                	stringLength: {
	                        min: 1,
	                        max: 150,
	                        message: '请输入1-150个字符'
	                    }
	                }
	            },
	            "filesList[2].filedetiaildesc": {
	                validators: {
	                	stringLength: {
	                        min: 1,
	                        max: 2000,
	                        message: '请输入1-2000个字符'
	                    }
	                }
	            },
				"filesList[2].isPublic": {
					validators: {
	                    notEmpty: {
	                        message: '请选择处罚情况是否在门户网站公开'
	                    },
	                    callback: {
	                       	message: '',
	                           callback: function(value, validator, $field) {
		                        	var transferPoliceType=$("#transferPoliceType").val();
		                        	if(transferPoliceType=='0'){
		                        		var val=$("#typeaheadxzjl").val();
				                       	if(val!=""&&val!=null&&val!="undefined"){
				                           	if(value=='1'){
				                           		$("#saveCityGroupForm").formValidation('enableFieldValidators', 'filesList[2].publicAddress', true);
				                           	}else if(value=='0'){
				                           		$("#saveCityGroupForm").formValidation('enableFieldValidators', 'filesList[2].publicAddress', false);
				                           	}else{
				                           		$("#saveCityGroupForm").formValidation('enableFieldValidators', 'filesList[2].isPublic', true);
				                           		$("#saveCityGroupForm").formValidation('enableFieldValidators', 'filesList[2].publicAddress', false);
				                           	}
				                           	return true;
				                       	}else{
				                       		$("#saveCityGroupForm").formValidation('enableFieldValidators', 'filesList[2].isPublic', false);
				                       		$("#saveCityGroupForm").formValidation('enableFieldValidators', 'filesList[2].publicAddress', false);
				                       		return true;
				                       	}   
		                        	}else if(transferPoliceType=='1'){
		                        		var val=$("#typeaheadsxfz").val();
				                       	if(val!=""&&val!=null&&val!="undefined"){
				                           	if(value=='1'){
				                           		$("#saveCityGroupForm").formValidation('enableFieldValidators', 'filesList[2].publicAddress', true);
				                           	}else if(value=='0'){
				                           		$("#saveCityGroupForm").formValidation('enableFieldValidators', 'filesList[2].publicAddress', false);
				                           	}else{
				                           		$("#saveCityGroupForm").formValidation('enableFieldValidators', 'filesList[2].isPublic', true);
				                           		$("#saveCityGroupForm").formValidation('enableFieldValidators', 'filesList[2].publicAddress', false);
				                           	}
				                           	return true;
				                       	}else{
				                       		$("#saveCityGroupForm").formValidation('enableFieldValidators', 'filesList[2].isPublic', false);
				                       		$("#saveCityGroupForm").formValidation('enableFieldValidators', 'filesList[2].publicAddress', false);
				                       		return true;
				                       	}   
		                        	}  
		                        		
	                          }
	                     }
	                }
	            },
	            "filesList[2].publicAddress": {
	            	validators: {
	                    notEmpty: {
	                        message: '请输入信息公开网址'
	                    }
	                }
	            },
	           /*  //第四份案卷验证
	             "filesList[3].filesimpledesc": {
	                validators: {
	                	stringLength: {
	                        min: 1,
	                        max: 150,
	                        message: '请输入1-150个字符'
	                    }
	                }
	            },
	            "filesList[3].filedetiaildesc": {
	                validators: {
	                	stringLength: {
	                        min: 1,
	                        max: 2000,
	                        message: '请输入1-2000个字符'
	                    }
	                }
	            },
				"filesList[3].isPublic": {
					validators: {
	                    notEmpty: {
	                        message: '请选择处罚情况是否在门户网站公开'
	                    },
	                    callback: {
	                       	message: '',
	                           callback: function(value, validator, $field) {
	                        	var val=$("#typeaheadsxfz").val();
		                       	if(val!=""&&val!=null&&val!="undefined"){
		                           	if(value=='1'){
		                           		$("#saveCityGroupForm").formValidation('enableFieldValidators', 'filesList[3].publicAddress', true);
		                           	}else if(value=='0'){
		                           		$("#saveCityGroupForm").formValidation('enableFieldValidators', 'filesList[3].publicAddress', false);
		                           	}else{
		                           		$("#saveCityGroupForm").formValidation('enableFieldValidators', 'filesList[3].isPublic',true);
		                           		$("#saveCityGroupForm").formValidation('enableFieldValidators', 'filesList[3].publicAddress', false);
		                           	}
		                           	return true;
		                       	}else{
		                       		$("#saveCityGroupForm").formValidation('enableFieldValidators', 'filesList[3].isPublic', false);
		                       		$("#saveCityGroupForm").formValidation('enableFieldValidators', 'filesList[3].publicAddress', false);
		                       		return true;
		                       	}   	
	                          }
	                     }
	                }
	            },
	            "filesList[3].publicAddress": {
	            	validators: {
	                    notEmpty: {
	                        message: '请输入信息公开网址'
	                    }
	                }
	            } */
	        }
		 });
		
		ajaxAutoCompleteInit();
		showTr();
		
	});
	function ajaxAutoCompletToXxcf1(){
		 $("#typeaheadxxcf1").select2({
			  language:'zh-CN',
			  data:[{id:'${xzaj1.oldid}'+'${xzaj1.filetype}',text:'${xzaj1.filecode}'}],
			  allowClear:true
			}).val(['${xzaj1.oldid}'+'${xzaj1.filetype}']).trigger("change").select2({
			  language:'zh-CN',
			  ajax: {
			    url: "${webpath}/xxcj/selectAnjuanByAreacodePrecise.do",
			    dataType: 'json',
			    delay: 500,
			    type:'POST',
			    data: function (params) {
			      return {
			        query: params.term,
			        type:$("#xzcf1filetype").val(),
			        qtype:1,
			        areacode:$("#areacode").val()
			        
			      };
			    },
			    processResults: function (data, params) {
			      return {
			        results: data
			      };
			    },
			    cache: true
			  },
			  escapeMarkup: function (markup) 
			  { 
				  return markup; 
			  },
			  templateResult:function (result) {
			        return result.text;
		      },
			  templateSelection:function (result) {
			        return result.text;
		      },
			  minimumInputLength:1,
			  theme:'bootstrap',
			  placeholder:'请选择案件决定书文号',
			  allowClear:true
			});
	    	//绑定选择select事件
			$('#typeaheadxxcf1').on('select2:select', function (evt) {
				var res = $("#typeaheadxxcf1").select2("data");
				$("#filecodexxcf1").val(res[0].text);
				$("#belongareacodexxcf1").val(res[0].areacode);
				$("#xxcf1oldid").val(res[0].oldid);
			});
			//绑定取消选择select事件
			$('#typeaheadxxcf1').on('select2:unselecting', function (evt) {
				$("#filecodexxcf1").val("");
				$("#belongareacodexxcf1").val("");
				$("#xxcf1oldid").val("");
			});
	}
	
	function ajaxAutoCompletToXxcf2(){
		$("#typeaheadxxcf2").select2({
			  language:'zh-CN',
			  data:[{id:'${xzaj2.oldid}'+'${xzaj2.filetype}',text:'${xzaj2.filecode}'}],
			  allowClear:true
			}).val(['${xzaj2.oldid}'+'${xzaj2.filetype}']).trigger("change").select2({
			  language:'zh-CN',
			  ajax: {
			    url: "${webpath}/xxcj/selectAnjuanByAreacodePrecise.do",
			    dataType: 'json',
			    delay: 500,
			    type:'POST',
			    data: function (params) {
			      return {
			        query: params.term,
			        type:$("#xzcf2filetype").val(),
			        qtype:1,
			        areacode:$("#areacode").val()
			        
			      };
			    },
			    processResults: function (data, params) {
			      return {
			        results: data
			      };
			    },
			    cache: true
			  },
			  escapeMarkup: function (markup) 
			  { 
				  return markup; 
			  },
			  templateResult:function (result) {
			        return result.text;
		      },
			  templateSelection:function (result) {
			        return result.text;
		      },
			  minimumInputLength:1,
			  theme:'bootstrap',
			  placeholder:'请选择案件决定书文号',
			  allowClear:true
			});
	    	//绑定选择select事件
			$('#typeaheadxxcf2').on('select2:select', function (evt) {
				var res = $("#typeaheadxxcf2").select2("data");
				$("#filecodexxcf2").val(res[0].text);
				$("#belongareacodexxcf2").val(res[0].areacode);
				$("#xxcf2oldid").val(res[0].oldid);
			});
			//绑定取消选择select事件
			$('#typeaheadxxcf2').on('select2:unselecting', function (evt) {
				$("#filecodexxcf2").val("");
				$("#belongareacodexxcf2").val("");
				$("#xxcf2oldid").val("");
			});
	}
	
	
	
	function ajaxAutoCompletToXzjl() {
		$("#typeaheadxzjl").select2({
			  language:'zh-CN',
			  data:[{id:'${ysjlaj.oldid}'+'${ysjlaj.filetype}',text:'${ysjlaj.filecode}'}],
			  allowClear:true
			}).val(['${ysjlaj.oldid}'+'${ysjlaj.filetype}']).trigger("change").select2({
			  language:'zh-CN',
			  ajax: {
			    url: "${webpath}/xxcj/selectAnjuanByAreacodePrecise.do",
			    dataType: 'json',
			    delay: 500,
			    type:'POST',
			    data: function (params) {
			      return {
			        query: params.term,
			        type:2,
			        qtype:1,
			        areacode:$("#areacode").val()
			        
			      };
			    },
			    processResults: function (data, params) {
			      return {
			        results: data
			      };
			    },
			    cache: true
			  },
			  escapeMarkup: function (markup) 
			  { 
				  return markup; 
			  },
			  templateResult:function (result) {
			        return result.text;
		      },
			  templateSelection:function (result) {
			        return result.text;
		      },
			  minimumInputLength:1,
			  theme:'bootstrap',
			  placeholder:'请选择案件移送编号',
			  allowClear:true
			});
	    	//绑定选择select事件
			$('#typeaheadxzjl').on('select2:select', function (evt) {
				var res = $("#typeaheadxzjl").select2("data");
				$("#filecodexzjl").val(res[0].text);
				$("#belongareacodexzjl").val(res[0].areacode);
				$("#xzjloldid").val(res[0].oldid);
			});
			//绑定取消选择select事件
			$('#typeaheadxzjl').on('select2:unselecting', function (evt) {
				$("#filecodexzjl").val("");
				$("#belongareacodexzjl").val("");
				$("#xzjloldid").val("");
			});
	}
	function ajaxAutoCompletToSxfz(){
		$("#typeaheadsxfz").select2({
			  language:'zh-CN',
			  data:[{id:'${sxfzaj.oldid}'+'${sxfzaj.filetype}',text:'${sxfzaj.filecode}'}],
			  allowClear:true
			}).val(['${sxfzaj.oldid}'+'${sxfzaj.filetype}']).trigger("change").select2({
			  language:'zh-CN',
			  ajax: {
			    url: "${webpath}/xxcj/selectAnjuanByAreacodePrecise.do",
			    dataType: 'json',
			    delay: 500,
			    type:'POST',
			    data: function (params) {
			      return {
			        query: params.term,
			        type:3,
			        qtype:1,
			        areacode:$("#areacode").val()
			        
			      };
			    },
			    processResults: function (data, params) {
			      return {
			        results: data
			      };
			    },
			    cache: true
			  },
			  escapeMarkup: function (markup) 
			  { 
				  return markup; 
			  },
			  templateResult:function (result) {
			        return result.text;
		      },
			  templateSelection:function (result) {
			        return result.text;
		      },
			  minimumInputLength:1,
			  theme:'bootstrap',
			  placeholder:'请选择案件移送编号',
			  allowClear:true
			});
	    	//绑定选择select事件
			$('#typeaheadsxfz').on('select2:select', function (evt) {
				var res = $("#typeaheadsxfz").select2("data");
				$("#filecodesxfz").val(res[0].text);
				$("#belongareacodesxfz").val(res[0].areacode);
				$("#sxfzoldid").val(res[0].oldid);
			});
			//绑定取消选择select事件
			$('#typeaheadsxfz').on('select2:unselecting', function (evt) {
				$("#filecodesxfz").val("");
				$("#belongareacodesxfz").val("");
				$("#sxfzoldid").val("");
			});
	}
	
	function ajaxAutoCompleteInit(){
		ajaxAutoCompletToXxcf1();
		ajaxAutoCompletToXxcf2();
		ajaxAutoCompletToXzjl();
		ajaxAutoCompletToSxfz();			
	};
	
	
	function showTr(){
		var transferPoliceType=$("#transferPoliceType").val();
		if(transferPoliceType==0){
			$("#ysjltr").show();
			$("#sxfztr").hide();
			
			$("#ysjltr input").prop("disabled",false);
			$("#ysjltr textarea").prop("disabled",false);
			$("#ysjltr select").prop("disabled",false);
			$("#sxfztr input").prop("disabled",true);
			$("#sxfztr textarea").prop("disabled",true);
			$("#sxfztr select").prop("disabled",true);
		}else if(transferPoliceType==1){
			$("#ysjltr").hide();
			$("#sxfztr").show();
			$("#ysjltr input").prop("disabled",true);
			$("#ysjltr textarea").prop("disabled",true);
			$("#ysjltr select").prop("disabled",true);
			
			$("#sxfztr input").prop("disabled",false);
			$("#sxfztr textarea").prop("disabled",false);
			$("#sxfztr select").prop("disabled",false);
		}
	}
	
	function clearAnJuanWenHaoXzcf1(){

		$("#filecodexxcf1").val("");
		$("#belongareacodexxcf1").val("");
		$("#xxcf1oldid").val("");
		$("#typeaheadxxcf1").val(null).trigger("change");
	}
	
	
	function clearAnJuanWenHaoXzcf2(){
		$("#filecodexxcf2").val("");
		$("#belongareacodexxcf2").val("");
		$("#xxcf2oldid").val("");
		$("#typeaheadxxcf2").val(null).trigger("change");
	}
	
	$("#showUpload1").click(function(){
		$("#uploadTr").attr("style","");
	});
	
</script>
</head>

<body>
<div class="center_weizhi">当前位置：信息采集 - 参选先进集体信息 - 市级案件修改</div>
<div class="center">
<div class="center_list">
        <form id="saveCityGroupForm" method="post" class="form-horizontal">
        <table class="table_input">
         <tbody>
           <tr>
             <td width="200">市级参选单位</td>
             <td>
             <div style="text-align: left;" class="form-group">
                 <div class="col-sm-12">
                 	${electionUnit.areaname}
                  </div>
		     </div> 
             </td>
           </tr>
            <tr>
           		<td>
           		集体事迹材料
           		</td>
           		<td style="text-align: left;">
           		 <span id="jtcl" style="font-size: 16px;">
           		 		<c:choose>
		                 		<c:when test="${electionUnit.teamMaterialName!=''&&electionUnit.teamMaterialName!=null}">
		                 			 ${electionUnit.teamMaterialName}
		                 			  <c:if test="${areaUser.sysStatus=='1'&&areaUser.reportState=='0'}">
		                 			  <button type="button" id="showUpload1" class="btn btn-primary btn-xs">重新上传</button>
		                 			  </c:if>
		                 		</c:when>
		                 		<c:otherwise>
		                 			<span style="font-size: 14px;">未上传</span>
		                 		</c:otherwise>
		            	</c:choose>
           		 </span>
           		</td>
           </tr>
            <c:if test="${areaUser.sysStatus=='1'&&areaUser.reportState=='0'}">
            <tr id="uploadTr" <c:if test="${electionUnit.teamMaterialName!=''&&electionUnit.teamMaterialName!=null}">style='display:none'</c:if>>
             <td>集体事迹材料上传</td>
             <td style="text-align:left;">
                       <input id="file1" type="file" name="file" class="file-loading" /> 
                       <span style="color: red;">仅允许上传doc和docx格式文件，大小不超过50M</span>
					   <input type="hidden" name="electionUnit.id" value="${electionUnit.id}"/>
					   <input type="hidden" id="areacode" name="electionUnit.areacode" value="${electionUnit.areacode}"/>
                       <input type="hidden" id="teamMaterialName" name="electionUnit.teamMaterialName" value="${electionUnit.teamMaterialName}"/>
                       <input type="hidden" id="teamMaterialURL" name="electionUnit.teamMaterialURL" value="${electionUnit.teamMaterialURL}"/>
             </td>
           </tr>
           </c:if>
           <tr>
             <td colspan="2">
             	<div class="shangbao_panel">
                <div class="shangbao_titlt">行政处罚案卷一</div>
             	 <table style="width:100%;" class="table_input">
             	 	<tr>
             	 		<td>案卷类型</td>
			             <td>
			              <div class="form-group">
			                 <div class="col-sm-12">
			           		 		<select class="form-control" name="filesList[0].filetype" id="xzcf1filetype" onchange="clearAnJuanWenHaoXzcf1()">
			           		 		   <option value="" >请选择</option>
						               <option value="0" <c:if test="${xzaj1.filetype==0}">selected</c:if> >行政处罚案卷</option>
						               <option value="1" <c:if test="${xzaj1.filetype==1}">selected</c:if> >按日计罚案卷</option>
						               <option value="6" <c:if test="${xzaj1.filetype==6}">selected</c:if> >查封扣押案卷</option>
						               <option value="7" <c:if test="${xzaj1.filetype==7}">selected</c:if> >限产停产案卷</option>
					             	</select>
			              	 </div>
					     </div> 
			             </td>
             	 	</tr>
             	 	<tr>
             	 		<td>案卷文号</td>
             	 		<td style="text-align:left;">
             	 			 <div class="form-group">
				                 <div class="col-sm-12">
				               <select  id="typeaheadxxcf1" name="anjuanwenhao1" class="multiselect-data-ajax form-control" >
								</select>
				               <input type="hidden" name="filesList[0].filecode" id="filecodexxcf1" value="${xzaj1.filecode}"/>
				               <input type="hidden"name="filesList[0].belongareacode" id="belongareacodexxcf1" value="${xzaj1.belongareacode}"/>
				               <input type="hidden" name="filesList[0].id" value="${xzaj1.id}"/>
				               <input type="hidden" name="filesList[0].oldid" id="xxcf1oldid" value="${xzaj1.oldid}"/>
				               
				               <input type="hidden" name="filesList[0].penalizetype" value="0"/>
				                </div>
				             </div>
             	 		</td>
             	 	</tr>
             	 	<tr>
		             <td>案卷材料简版说明</td>
		             <td>
		              <div class="form-group">
		                 <div class="col-sm-12">
		             <textarea name="filesList[0].filesimpledesc" rows="5" class="form-control"  placeholder="请输入案卷材料简版说明">${xzaj1.filesimpledesc}</textarea>
		              </div>
				     </div> 
		             </td>
		           </tr>
		           <tr>
		             <td>案卷材料详版说明</td>
		             <td>
		              <div class="form-group">
		                 <div class="col-sm-12">
		             <textarea name="filesList[0].filedetiaildesc" rows="5" class="form-control"  placeholder="请输入案卷材料详版说明">${xzaj1.filedetiaildesc}</textarea>
		              </div>
				     </div> 
		             </td>
		           </tr>
		           <tr>
                     <td width="224">处罚情况是否在门户网站公开</td>
                     <td>
                      	<div class="form-group">
			                 <div class="col-sm-12">
		                     <select name="filesList[0].isPublic" class="form-control">
		                       <option value="">请选择</option>
		                       <option value="1" <c:if test="${xzaj1.isPublic==1}">selected</c:if> >是</option>
		                       <option value="0" <c:if test="${xzaj1.isPublic==0}">selected</c:if> >否</option>
		                     </select>
		                     </div>
		                </div>
                     </td>
                   </tr>
                   <tr>
                     <td>信息公开网址</td>
                     <td>
                     	<div class="form-group">
			                 <div class="col-sm-12">
                     	<input type="text" class="form-control" name="filesList[0].publicAddress" placeholder="请输入信息公开网址" value="${xzaj1.publicAddress}">
                         </div>
		                </div>
                     </td>
                   </tr>
             	 </table>
             	 </div>
             </td>
           </tr>
           <tr>
             <td colspan="2">
             	<div class="shangbao_panel">
                <div class="shangbao_titlt">行政处罚案卷二</div>
                	<table style="width:100%;" class="table_input">
                		<tr>
             	 		<td>案卷类型</td>
			             <td>
			              <div class="form-group">
			                 <div class="col-sm-12">
			           		 		<select class="form-control" name="filesList[1].filetype" id="xzcf2filetype" onchange="clearAnJuanWenHaoXzcf2()">
			           		 		   <option value="">请选择</option>
						               <option value="0" <c:if test="${xzaj2.filetype==0}">selected</c:if> >行政处罚案卷</option>
						               <option value="1" <c:if test="${xzaj2.filetype==1}">selected</c:if> >按日计罚案卷</option>
						               <option value="6" <c:if test="${xzaj2.filetype==6}">selected</c:if> >查封扣押案卷</option>
						               <option value="7" <c:if test="${xzaj2.filetype==7}">selected</c:if> >限产停产案卷</option>
					             	</select>
			              	 </div>
					     </div> 
			             </td>
             	 		</tr>
                		<tr>
	             	 		<td>案卷文号</td>
	             	 		<td style="text-align:left;">
	             	 			 <div class="form-group">
					                 <div class="col-sm-12">
					                 <select id="typeaheadxxcf2" name="anjuanwenhao2" class="multiselect-data-ajax form-control"  >
					                 </select>
					               <input type="hidden" name="filesList[1].filecode" id="filecodexxcf2" value="${xzaj2.filecode}"/>
					               <input type="hidden"name="filesList[1].belongareacode" id="belongareacodexxcf2" value="${xzaj2.belongareacode}"/>
					                <input type="hidden" name="filesList[1].id" value="${xzaj2.id}"/>
					                <input type="hidden" name="filesList[1].oldid" id="xxcf2oldid" value="${xzaj2.oldid}"/>
					                
					                 <input type="hidden" name="filesList[1].penalizetype" value="1"/>
					              </div>
					             </div>
	             	 		</td>
             	 		</tr>
             	 		 <tr>
				             <td>案卷材料简版说明</td>
				             <td>
				              <div class="form-group">
				                 <div class="col-sm-12">
				             <textarea name="filesList[1].filesimpledesc" rows="5" class="form-control"  placeholder="请输入案卷材料简版说明">${xzaj2.filesimpledesc}</textarea>
				             
				              </div>
						     </div> 
				             </td>
				           </tr>
				           <tr>
				             <td>案卷材料详版说明</td>
				             <td>
				              <div class="form-group">
				                 <div class="col-sm-12">
				             <textarea name="filesList[1].filedetiaildesc" rows="5" class="form-control"  placeholder="请输入案卷材料详版说明">${xzaj2.filedetiaildesc}</textarea>
				              </div>
						     </div> 
				             </td>
				           </tr>
				           <tr>
		                     <td width="224">处罚情况是否在门户网站公开</td>
		                     <td>
		                      	<div class="form-group">
					                 <div class="col-sm-12">
				                     <select name="filesList[1].isPublic" class="form-control">
				                       <option value="">请选择</option>
				                       <option value="1" <c:if test="${xzaj2.isPublic==1}">selected</c:if> >是</option>
				                       <option value="0" <c:if test="${xzaj2.isPublic==0}">selected</c:if> >否</option>
				                     </select>
				                     </div>
				                </div>
		                     </td>
		                   </tr>
		                   <tr>
		                     <td>信息公开网址</td>
		                     <td>
		                     	<div class="form-group">
					                 <div class="col-sm-12">
		                     	<input type="text" class="form-control" name="filesList[1].publicAddress" placeholder="请输入信息公开网址" value="${xzaj2.publicAddress}">
		                         </div>
				                </div>
		                     </td>
		                   </tr>
                	</table>
                </div>
             </td>
           </tr>
           <tr>
             <td width="300">移送公安机关类型</td>
             <td>
             		<div class="form-group" >
				     <div class="col-sm-12">
	             	<select class="form-control" name="electionUnit.transferPoliceType" id="transferPoliceType" onchange="showTr()">
		               <option value="0" <c:if test="${electionUnit.transferPoliceType==0}">selected</c:if> >移送行政拘留</option>
		               <option value="1" <c:if test="${electionUnit.transferPoliceType==1}">selected</c:if> >涉嫌犯罪移送</option>
	             	</select>
	              </div>
			       </div>
             </td>
           </tr>
           <tr id="ysjltr" style="display:none;">
             <td colspan="2">
             	<div class="shangbao_panel">
                <div class="shangbao_titlt">移送行政拘留一</div>
                	<table style="width:100%;" class="table_input">
                		<tr>
	             	 		<td>案卷文号</td>
	             	 		<td style="text-align:left;">
	             	 			  <div class="form-group">
					                 <div class="col-sm-12">
					                 <select id="typeaheadxzjl" name="anjuanwenhao3"  class="multiselect-data-ajax form-control" style="width:100%;" >
					                 </select>
					                <input type="hidden"  name="filesList[2].filecode" id="filecodexzjl" value="${ysjlaj.filecode}"/>
					               <input type="hidden"name="filesList[2].belongareacode" id="belongareacodexzjl" value="${ysjlaj.belongareacode}"/>
					               <input type="hidden" name="filesList[2].id" value="${ysjlaj.id}"/>
					               <input type="hidden" name="filesList[2].oldid" id="xzjloldid" value="${ysjlaj.oldid}"/>
					               
					                <input type="hidden" name="filesList[2].penalizetype" value="4"/>
					                <input type="hidden" name="filesList[2].filetype" value="2"/>
					                </div>
					             </div>
	             	 		</td>
             	 		</tr>
             	 		<tr>
			             <td>案卷材料简版说明</td>
			             <td>
			             <div class="form-group">
			                 <div class="col-sm-12">
			             <textarea name="filesList[2].filesimpledesc" rows="5" class="form-control"  placeholder="请输入案卷材料简版说明">${ysjlaj.filesimpledesc}</textarea>
			               </div>
					     </div> 
			             </td>
			           </tr>
			           <tr>
			             <td>案卷材料详版说明</td>
			             <td>
			              <div class="form-group">
			                 <div class="col-sm-12">
			             <textarea name="filesList[2].filedetiaildesc" rows="5" class="form-control"  placeholder="请输入案卷材料详版说明">${ysjlaj.filedetiaildesc}</textarea>
			              </div>
					     </div> 
			             </td>
			           </tr>
			           <tr>
	                     <td width="224">处罚情况是否在门户网站公开</td>
	                     <td>
	                      	<div class="form-group">
				                 <div class="col-sm-12">
			                     <select name="filesList[2].isPublic" class="form-control">
			                       <option value="">请选择</option>
			                       <option value="1" <c:if test="${ysjlaj.isPublic==1}">selected</c:if> >是</option>
			                       <option value="0" <c:if test="${ysjlaj.isPublic==0}">selected</c:if> >否</option>
			                     </select>
			                     </div>
			                </div>
	                     </td>
	                   </tr>
	                   <tr>
	                     <td>信息公开网址</td>
	                     <td>
	                     	<div class="form-group">
				                 <div class="col-sm-12">
	                     	<input type="text" class="form-control" name="filesList[2].publicAddress" placeholder="请输入信息公开网址" value="${ysjlaj.publicAddress}">
	                         </div>
			                </div>
	                     </td>
	                   </tr>
                	</table>
                </div>
             </td>
           </tr>
           <tr id="sxfztr" style="display:none;">
             <td colspan="2">
             	<div class="shangbao_panel">
                <div class="shangbao_titlt">涉嫌犯罪移送一</div>
                	<table style="width:100%;" class="table_input">
                		<tr>
	             	 		<td>案卷文号</td>
	             	 		<td style="text-align:left;">
	             	 			  <div class="form-group">
					                 <div class="col-sm-12">
					                 <select id="typeaheadsxfz" name="anjuanwenhao3"  class="multiselect-data-ajax form-control"  style="width:100%;">
					                 </select>
					               <input type="hidden" name="filesList[2].filecode" id="filecodesxfz" value="${sxfzaj.filecode}"/>
					               <input type="hidden"name="filesList[2].belongareacode" id="belongareacodesxfz" value="${sxfzaj.belongareacode}"/>
					               <input type="hidden" name="filesList[2].id" value="${sxfzaj.id}"/>
					               <input type="hidden" name="filesList[2].oldid" id="sxfzoldid" value="${sxfzaj.oldid}"/>
					               
					               <input type="hidden" name="filesList[2].penalizetype" value="5"/>
					               <input type="hidden" name="filesList[2].filetype" value="3"/>
							     </div> 
					             </div>
	             	 		</td>
             	 		</tr>
             	 		<tr>
			             <td>案卷材料简版说明</td>
			             <td>
			              <div class="form-group">
			                 <div class="col-sm-12">
			             <textarea name="filesList[2].filesimpledesc" rows="5" class="form-control"  placeholder="请输入案卷材料简版说明">${sxfzaj.filesimpledesc}</textarea>
			              </div>
					     </div> 
			             </td>
			           </tr>
			           <tr>
			             <td>案卷材料详版说明</td>
			             <td>
			              <div class="form-group">
			                 <div class="col-sm-12">
			             <textarea name="filesList[2].filedetiaildesc" rows="5" class="form-control" placeholder="请输入案卷材料详版说明">${sxfzaj.filedetiaildesc}</textarea>
			              </div>
					     </div> 
			             </td>
			           </tr>
			           <tr>
	                     <td width="224">处罚情况是否在门户网站公开</td>
	                     <td>
	                      	<div class="form-group">
				                 <div class="col-sm-12">
			                     <select name="filesList[2].isPublic" class="form-control">
			                       <option value="">请选择</option>
			                       <option value="1" <c:if test="${sxfzaj.isPublic==1}">selected</c:if> >是</option>
			                       <option value="0" <c:if test="${sxfzaj.isPublic==0}">selected</c:if> >否</option>
			                     </select>
			                     </div>
			                </div>
	                     </td>
	                   </tr>
	                   <tr>
	                     <td>信息公开网址</td>
	                     <td>
	                     	<div class="form-group">
				                 <div class="col-sm-12">
	                     	<input type="text" class="form-control" name="filesList[2].publicAddress" placeholder="请输入信息公开网址" value="${sxfzaj.publicAddress}">
	                         </div>
			                </div>
	                     </td>
	                   </tr>
                	</table>
                </div>
             </td>
           </tr>
           <tr>
             <td align="center">&nbsp;</td>
             <td style="text-align:left;"><a href="#"><button type="button"  id="saveCityGroupBtn" class="btn btn-primary" style="font-size:16px;width:150px; margin-top:5px;">信息保存</button></a></td>
           </tr>
         </tbody>
       </table>
        </form>
    </div>
</div>
</body>
</html>
