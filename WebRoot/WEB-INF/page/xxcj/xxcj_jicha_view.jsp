<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
<meta charset="utf-8">
<title>环境执法大练兵评分系统</title>
<script type="text/javascript">
function downloadFile(url,fileName){
	if(url!=null&&url!=""){
	  window.location.href="${webpath}/filedownload.do?url="+url+"&fileName="+fileName;
	}else{
	  swal({title: "提示",text: "您下载的附件不存在！",type:"info"});
	}
}
</script>
</head>
<body>
<div class="center_weizhi">当前位置：信息采集 - 参选先进集体信息 - 稽查案卷查看</div>
<div class="center">
<div class="center_list">
        <table align="center" class="table_input">
         <tbody>
         	<tr>
             <td width="200">参选单位</td>
             <td style="color:#F84300; text-align:left; font-size:16px; font-weight:bold;">${unitInfo.areaname }</td>
           </tr>
           <tr>
             <td width="200">&nbsp;</td>
             <td><span style="color:#295A5F; font-size:18px; float:left; font-weight:bold;">稽查案卷一</span></td>
           </tr>
           <tr>
             <td>稽查单位</td>
             <td style="text-align:left;">${fd1.checkFileDesc.checkunit}</td>
           </tr>
           <tr>
             <td>稽查时间</td>
             <td style="text-align:left;"><fmt:formatDate value="${fd1.checkFileDesc.checkdate}" pattern="yyyy-MM-dd"/></td>
           </tr>
           <tr>
             <td>稽查内容</td>
             <td style="text-align:left;">${fd1.checkFileDesc.checkcontent}</td>
           </tr>
           <tr>
             <td>被稽查单位</td>
             <td style="text-align:left;">${fd1.checkFileDesc.checkedunit}</td>
           </tr>
           <tr>
             <td>稽查意见书文号</td>
             <td style="text-align:left;">${fd1.files.filecode}</td>
           </tr>
           <tr>
             <td>稽查案卷文本</td>
             <td style="text-align:left;"><a href="#" style="color: blue;cursor:pointer;text-decoration:none;" onclick="downloadFile('${fd1.files.fileurl}','${fd1.files.filename}')">${fd1.files.filename}</a></td>
           </tr>
           <tr>
             <td width="200">&nbsp;</td>
             <td>&nbsp;</td>
           </tr>
           <c:if test="${fd2.checkFileDesc.id!=null}">
           <tr>
             <td width="200">&nbsp;</td>
             <td><span style="color:#295A5F; font-size:18px; float:left; font-weight:bold;">稽查案卷二</span></td>
           </tr>
           <tr>
             <td>稽查单位</td>
             <td style="text-align:left;">${fd2.checkFileDesc.checkunit}</td>
           </tr>
           <tr>
             <td>稽查时间</td>
             <td style="text-align:left;"><fmt:formatDate value="${fd2.checkFileDesc.checkdate}" pattern="yyyy-MM-dd"/></td>
           </tr>
           <tr>
             <td>稽查内容</td>
             <td style="text-align:left;">${fd2.checkFileDesc.checkcontent}</td>
           </tr>
           <tr>
             <td>被稽查单位</td>
             <td style="text-align:left;">${fd2.checkFileDesc.checkedunit}</td>
           </tr>
           <tr>
             <td>稽查意见书文号</td>
             <td style="text-align:left;">${fd2.files.filecode}</td>
           </tr>
           <tr>
             <td>稽查案卷文本</td>
             <td style="text-align:left;"><a href="#" style="color: blue;cursor:pointer;text-decoration:none;" onclick="downloadFile('${fd2.files.fileurl}','${fd2.files.filename}')">${fd2.files.filename}</a></td>
           </tr>
           </c:if>
         </tbody>
       </table>
    </div>
</div>
</body>
</html>
