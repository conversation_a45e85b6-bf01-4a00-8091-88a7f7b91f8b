<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
<script type="text/javascript">
	    	  //删除方法
	    	  function deletePersonById(id){
	    		 swal({
	                 title: "您确定执行删除操作吗？",
	                 type: "warning",
	                 showCancelButton: true,
	                 closeOnConfirm: false,
	                 confirmButtonText: "是的，我要删除",
	                 confirmButtonColor: "#ec6c62"
	             }, function() {
	            	$.ajax({
          			   type: "POST",
          			   url: "${webpath}/xxcj/deleteXxcjPerson.do",
          			   data:{id:id},
          			   async:false,
          			   success: function(data){
          				   if(data.result=="error"){
          					  swal({title: "删除失败",text: "",type:"error"});
          		               return false;
          			       }else if(data.result=="success"){
          			    	  swal({title: "删除成功",text: "",type:"success"});
          			    	business.addMainContentParserHtml('xxcj/cityPerson.do',null);
          			       }
          			   }
          			});
	             });
	    	  }
    </script>
</head>

<body>
<div class="center_weizhi">当前位置：信息采集 - 参选先进个人信息 - 市级先进个人上报</div>
<div class="center">
<div class="center_list">
    <%--<div class="panel-group" id="accordion">
             <div class="btn-group" style="margin-right:20px;float:left;">
            <c:if test="${areaUser.sysStatus=='1'}">
               <a href="#"  onclick="macroMgr.onLevelTwoMenuClick(null, 'xxcj/cityPersonInput.do')" ><button type="button" class="btn btn-primary" style="font-size:16px;">先进个人信息录入</button></a>
            </c:if>
            </div>  --%>      
      <!--       
          -快速查询-
          <form role="form">
              <select class="form-control" style="width:120px;margin-right:5px;">
                 <option>省</option>
                 <option>北京市</option>
                 <option>天津市</option>
                 <option>河北省</option>
                 <option>..</option>
              </select>
              
              <select class="form-control" style="width:120px;margin-right:5px;">
                 <option>地（市）</option>
                 <option>石家庄市</option>
                 <option>保定市</option>
                 <option>张家口市</option>
                 <option>..</option>
              </select>
        </form>
          
            -搜索-
            <div style="width:260px;" class="btn-group">
               <form class="bs-example bs-example-form" role="form">
                  <div class="row">                     
                     <div class="col-lg-6">
                        <div class="input-group">
                           <input type="text" class="form-control" style="width:200px;">
                           <span class="input-group-btn">
                              <button class="btn btn-success" type="button">
                                 快速搜索
                              </button>
                           </span>
                        </div>/input-group
                     </div>/.col-lg-6
                  </div>/.row
               </form>
            </div>          
    </div>-->   
        <table class="table table-bordered table-hover table-condensed">
         <thead>
           <tr>
             <td width="50" height="30" bgcolor="#efefef">序号</td>
             <td bgcolor="#efefef">所属行政区</td>
             <td width="100" bgcolor="#efefef">姓名</td>
             <td width="60" bgcolor="#efefef">性别</td>
             <td width="100" bgcolor="#efefef">职务</td>
             <td width="150" bgcolor="#efefef">身份证号码</td>
             <td width="100" bgcolor="#efefef">联系电话</td>
             <td width="160" bgcolor="#efefef">参与调查处理案件数量</td>
             <td width="130" bgcolor="#efefef">操作</td>
           </tr>
         </thead>
         <tbody>
           <tr>
           <c:forEach  var="electionPerson" items="${cityPersonalList}" varStatus="status">
	          <tr>
	             <td height="30" align="center">${status.index+1}</td>
	             <td>${electionPerson.province}${electionPerson.city}</td>
	             <td>${electionPerson.name}</td>
	             <td>${electionPerson.sex}</td>
	             <td>${electionPerson.job}</td>
	             <td>${electionPerson.cardid}</td>
	             <td>${electionPerson.phone}</td>
	             <td>${electionPerson.handlcasenum}</td>
	             <td>
	             <button class="btn btn-info btn-xs" onclick="macroMgr.onLevelTwoMenuClick(null, 'xxcj/cityPersonDetail.do?id=${electionPerson.id}')">查看</button> 
	             <%-- <c:if test="${areaUser.sysStatus=='1'}">
	             <a href="#"><button class="btn btn-success btn-xs" onclick="macroMgr.onLevelTwoMenuClick(null, 'xxcj/cityPersonUpdate.do?id=${electionPerson.id}')">编辑</button></a>
	             <button class="btn btn-danger btn-xs" onclick="deletePersonById(${electionPerson.id})">删除</button>
	             </c:if> --%>
	             </td>
	           </tr>
	         </c:forEach>
           </tr>
         </tbody>
       </table>
    </div>
    <!-- <div class="page">
        <ul class="pagination">
            <li><a href="#">&laquo;</a></li>
            <li class="active"><a href="#">1</a></li>
            <li><a href="#">2</a></li>
            <li><a href="#">3</a></li>
            <li><a href="#">4</a></li>
            <li><a href="#">5</a></li>
            <li><a href="#">&raquo;</a></li>
        </ul>
    </div> -->
</div>
</body>
</html>
