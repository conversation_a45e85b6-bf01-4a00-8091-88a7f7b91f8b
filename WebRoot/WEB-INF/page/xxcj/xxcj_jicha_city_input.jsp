<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
<script type="text/javascript">
$(document).ready(function(){
	//文件上传、
	 $("#file").fileinput({
        uploadUrl: WEBPATH+'/pdffileupload.do', // you must set a valid URL here else you will get an error
        allowedFileExtensions : ['pdf'],
        language:'zh',
        //overwriteInitial: true,
        minFileCount: 1,
        maxFileCount: 1,
        minFileSize:1,
        maxFileSize:51200,
        enctype: 'multipart/form-data',        
        dropZoneTitle:"可拖拽文件到此处...",
        initialPreviewShowDelete: false,
        msgFilesTooMany:'选择上传的文件数量({n}) 超过允许的最大数值{m}！',
        msgZoomModalHeading:'文件预览',
        msgInvalidFileExtension:'不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
        msgNoFilesSelected:'请选择文件',
        msgValidationError:'文件类型不正确或文件过大',
        initialPreviewFileType:'pdf',
        browseLabel:"选择文件",
        removeLabel:'删除',
        removeTitle:'删除文件',
        uploadLabel:'上传',
        uploadTitle:'上传文件',
        cancelLabel: '取消',
        cancelTitle: '取消上传',
        showPreview:false,
        autoReplace:true,
        slugCallback: function(filename) {
            return filename.replace('(', '_').replace(']', '_');
        }
	}).on('filepreupload', function(event, data, previewId, index) {
		
	   $("#saveProvinceJichaBtn").prop('disabled', true);
	   
	}).on('fileuploaded', function(event, data, previewId, index) {//文件上传完成执行操作
		
		$("#fileurl").val(data.response.url);
	  	$("#filename").val(data.response.fileRealName);
	  	$("#wsc").text(data.response.fileRealName);
	  	$("#saveProvinceJichaBtn").prop('disabled',false);
	  	
	})/* .on('fileclear', function(event) {  //文件上传删除文件执行的操作
		
    	$("#fileurl").val("");
	  	$("#filename").val("");
	  	$("#wsc").text("未上传");
    }) */
   
	//保存数据方法
	$('#saveCityJichaBtn').click(function() {

       	 var options = {
           url: WEBPATH+'/xxcj/saveXxcjJicha.do',
           type: 'post',
           success:function(data){
	           if(data.result=="error"){
	        	   swal("操作失败", "信息保存操作失败了!", "error");
	               return false;
	           }else if(data.result=="success"){
	        	   business.addMainContentParserHtml('xxcj/xxcjElectionUintList.do',null);
	        	  swal({title: "保存成功",text: "",type:"success"});
	             return false;
	           }
         	}
       	 };
       	$("#saveCityJichaForm").data('formValidation').validate();
       	var validate = $("#saveCityJichaForm").data('formValidation').isValid();
       	if(validate){
       		var fileurl=$("#fileurl").val();
       		var filename=$("#filename").val();
       		$("#fileareacode").val($("#areacode").val());
       		if(fileurl==""||filename==""){
       			 swal("提示", "请选择上传文件", "error");
       		}else{
       			$('#saveCityJichaForm').ajaxSubmit(options);
       		}
       	}
   	});
	//表单验证
	$("#saveCityJichaForm").formValidation({
        framework: 'bootstrap',
        message: 'This value is not valid',
        icon:{
	            valid: 'glyphicon glyphicon-ok',
	            invalid: 'glyphicon glyphicon-remove',
	            validating: 'glyphicon glyphicon-refresh'
        },
        fields: {
        	"checkFileDesc.checkunit": {
                validators: {
                    notEmpty: {
                        message: '请输入稽查单位'
                    },
                    stringLength: {
                        min: 1,
                        max: 30,
                        message: '1-30个字符'
                    }
                }
            },
            "checkFileDesc.checkdate": {
                validators: {
                    notEmpty: {
                        message: '请输入稽查时间'
                    },
                    stringLength: {
                        min: 1,
                        max: 20,
                        message: '1-20个字符'
                    }
                }
            },
            "checkFileDesc.checkcontent": {
                validators: {
                    notEmpty: {
                        message: '请输入稽查内容'
                    },
                    stringLength: {
                        min: 1,
                        max: 200,
                        message: '1-200个字符'
                    }
                }
            },
            "checkFileDesc.checknumber": {
                validators: {
                    notEmpty: {
                        message: '请选择稽查意见书文号'
                    },
                    stringLength: {
                        min: 1,
                        max: 50,
                        message: '1-50个字符'
                    }
                }
            },
            /* "checkFileDesc.checkusername": {
                validators: {
                    notEmpty: {
                        message: '请输入稽查人员姓名'
                    },
                    stringLength: {
                        min: 1,
                        max: 20,
                        message: '1-20个字符'
                    }
                }
            },
            "checkFileDesc.checkuserunit": {
                validators: {
                    notEmpty: {
                        message: '请输入稽查人员单位'
                    },
                    stringLength: {
                        min: 1,
                        max: 50,
                        message: '1-50个字符'
                    }
                }
            },*/
            "files.filecode": {
                validators: {
                    notEmpty: {
                        message: '稽查意见书文号'
                    },
                    stringLength: {
                        min: 1,
                        max: 20,
                        message: '1-20个字符'
                    }
                }
            }, 
            "checkFileDesc.checkedunit": {
                validators: {
                    notEmpty: {
                        message: '请输入被稽查单位'
                    },
                    stringLength: {
                        min: 1,
                        max: 50,
                        message: '1-50个字符'
                    }
                }
            }
	   }
	});
	
	
	$("#showUpload1").click(function(){
    	$("#uploadTr1").attr("style","");
 	});
});




</script>
</head>
<body>
<div class="center_weizhi">当前位置：信息采集 - 参选先进集体信息 - 市级稽查案卷上报</div>
<div class="center">
<div class="center_list">
    	<form id="saveCityJichaForm" method="post" class="form-horizontal">
    	<input type="hidden" name="checkFileDesc.id" value="${CheckFileDescList.id}">
    	<input type="hidden" name="checkFileDesc.areatype" value="2">
    	<!-- 文件信息隐藏域 -->
    	<input type="hidden" name="files.id" value="${CheckFileDescList.fileId}" >
		<input type="hidden" name="files.areacode" id="fileareacode" >
		<input type="hidden" name="files.filetype" value="5">
		<input type="hidden" name="files.filename" id="filename" value="${CheckFileDescList.fileName}">
 		<input type="hidden" name="files.fileurl" id="fileurl" value="${CheckFileDescList.fileUrl}"> 
 		<input type="hidden" name="files.belongareacode" value="${areaCode}"> 
 		
 		<input type="hidden" name="checkFileDesc.areacode" id="areacode" value="${areaCode}"> 
        <table class="table_input">
         <tbody>
           <tr>
             <td  width="200">&nbsp;</td>
             <td><span style="color:#295A5F; font-size:18px; float:left; font-weight:bold;">市级</span></td>
           </tr>
           <tr>
           	  <td width="200">市级参选单位</td>
             <td>
                 <span style="color:#295A5F; font-size:18px; float:left; font-weight:bold;">${city}</span></td>
		           <%--   <select name="checkFileDesc.areacode" class="form-control" id="areacode" >
		               <option value="">请选择地市</option>
			              <c:forEach var="city" items="${cityList}">
			               	<option onreadystatechange="false" <c:if test="${CheckFileDescList.areaCode==city.areaCode}">selected </c:if> value="${city.areaCode}" disabled ='disabled'>${city.city}</option>
			               </c:forEach> 
		             </select> --%>
           </tr>
           <tr>
             <td>稽查单位</td>
             <td>
             <div class="form-group">
                 <div class="col-sm-12">
             <input type="text" class="form-control input-sm" name="checkFileDesc.checkunit" placeholder="请输入稽查单位" value="${CheckFileDescList.checkUnit}">
             </div>
		       </div> 
             </td>
           </tr>
           <tr>
             <td>稽查时间</td>
             <td>
             <div class="form-group">
                 <div class="col-sm-12">
             <input type="text" class="form-control input-sm" readonly="readonly" value="<fmt:formatDate value="${CheckFileDescList.checkDate}" pattern="yyyy-MM-dd"/>"  name="checkFileDesc.checkdate" id="datepicker" placeholder="请输入稽查时间">
             </div>
		       </div> 
             </td>
           </tr>
           <tr>
             <td>稽查内容</td>
             <td>
             <div class="form-group">
                 <div class="col-sm-12">
                 <textarea rows="5" class="form-control" name="checkFileDesc.checkcontent" placeholder="请输入稽查内容">${CheckFileDescList.checkContent}</textarea>
             </div>
		       </div> 
             </td>
           </tr>
            <tr>
             <td>被稽查单位</td>
             <td>
             <div class="form-group">
                 <div class="col-sm-12">
             <input type="text" class="form-control input-sm" name="checkFileDesc.checkedunit" placeholder="请输入被稽查单位" value="${CheckFileDescList.checkedUnit}">
             </div>
		       </div> 
             </td>
           </tr>
           <tr>
             <td>稽查意见书文号</td>
             <td>
             <div class="form-group">
                 <div class="col-sm-12">
                    <input type="text" class="form-control input-sm" name="files.filecode" placeholder="请输入稽查意见书文号" value="${CheckFileDescList.fileCode}">
             	 </div>
		       </div> 
             </td>
           </tr>
           <!-- <tr>
             <td>稽查类型</td>
             <td>
             <div class="form-group">
                 <div class="col-sm-12">
             <select name="checkFileDesc.checktype" class="form-control">
               <option value="">请选择稽查类型</option>
               <option value="0">日常稽查</option>
               <option value="1">专项稽查</option>
               <option value="2">专案稽查</option>
             </select>
             </div>
		       </div> 
             </td>
           </tr> -->
          <!--  <tr>
             <td>稽查人员姓名</td>
             <td>
             <div class="form-group">
                 <div class="col-sm-12">
             <input type="text" class="form-control input-sm" name="checkFileDesc.checkusername" placeholder="请输入稽查人员姓名">
             </div>
		       </div> 
             </td>
           </tr>
           <tr>
             <td>稽查人员单位</td>
             <td>
             <div class="form-group">
                 <div class="col-sm-12">
             <input type="text" class="form-control input-sm" name="checkFileDesc.checkuserunit" placeholder="请输入稽查人员单位">
             </div>
		       </div> 
             </td>
           </tr>
           <tr>
             <td>稽查人员职务</td>
             <td>
             <div class="form-group">
                 <div class="col-sm-12">
             <input type="text" class="form-control input-sm" name="checkFileDesc.checkuserjob" placeholder="请输入稽查人员职务">
             </div>
		       </div> 
             </td>
           </tr> -->
            <tr>
             <td>稽查案卷文本</td>
             <td style="text-align:left;">
             		<span id="wsc" style="font-size: 16px;">
             		<c:choose>
		                 		<c:when test="${CheckFileDescList.id!=''&&CheckFileDescList.id!=null}">
		                 			${CheckFileDescList.fileName}   
		                 		<button type="button" id="showUpload1" class="btn btn-primary btn-xs">重新上传</button>
		                 		</c:when>
		                 		<c:otherwise>
		                 			<span style="font-size: 14px;">未上传</span>
		                 		</c:otherwise>
		            </c:choose>
		            </span> 
 		    </td>
           </tr>
          <tr  id="uploadTr1" <c:if test="${CheckFileDescList.id!=''&&CheckFileDescList.id!=null}">style='display:none'</c:if>>
               <td>上传附件</td>
               <td style="text-align:left;">
	               		<input id="file" type="file" name="file" class="file-loading" > 
	               		<span style="color: red;">仅允许上传PDF格式文件，大小不超过50M</span>
               </td>
           </tr>
           <tr>
             <td align="center">&nbsp;</td>
             <td style="text-align:left;"><a href="#"><button type="button" class="btn btn-primary" id="saveCityJichaBtn" style="font-size:16px;width:150px; margin-top:5px;">信息保存</button></a></td>
           </tr>
         </tbody>
       </table>
       </form>
    </div>
</div>
</body>
<script>
  $(function() {
    $( "#datepicker" ).datepicker({
    	monthNames: [ "1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月" ],
		dayNamesMin: [ "日", "一", "二", "三", "四", "五", "六" ],
        dateFormat: 'yy-mm-dd',
        maxDate: 0,
        changeYear: true,
        onSelect: function(selectedDate) {//选择日期后执行的操作  
        	$('#saveCityJichaForm').data('formValidation').revalidateField("checkFileDesc.checkdate");
        }  
    });
  });
</script>
</html>
