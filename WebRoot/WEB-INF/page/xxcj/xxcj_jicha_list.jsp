<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
</head>
<script type="text/javascript">
		//下载附件
		/* function xxcjDownJiCha(index){
			 //行政区域内省级重点监控企业数量
			 window.location.href= WEBPATH+'/xxcj/xxcjDownJiCha.do?id='+index;
		} */
		
		//下载文件
		function xxcjDownJiCha(index){
		 var fileId = $("#fileId").val();
		  $.ajax({
			    type:"post",
			    url:WEBPATH+'/xxcj/xxjcExistFileUrl.do',
			    data:{fileId:index},           //注意数据用{}
			    success:function(data){  //成功
				 if("yes" == data){
					 window.location.href= WEBPATH+'/xxcj/xxcjDownJiCha.do?id='+index;
					    return false;
			         }else if("no" == data){
			            	  swal( "操作失败","该案卷不存在!", "error");
			            	  return false;
					}else if("suffixerror" ==data){
						  swal( "操作失败","该案卷上传数据格式有问题!", "error");
		            	  return false;
					}
			         }
			});
		}
		
		$(document).ready(function(){
			
			//监听enter
			business.listenEnter("search");
			//下载execl表
				
		$("#viewExcel").click(function(){
			var content=  $("#checkunit").val();
			window.location.href= WEBPATH+'/xxcj/downJiChaListExcel.do?content='+content;
		});
			
			
			//初始化分页
			var pageCount = eval('${checkFileDescList.pages}'); //取到pageCount的值(把返回数据转成object类型)
			var currentPage = eval('${checkFileDescList.pageNum}'); //得到urrentPage
			if(pageCount>0){
				  var options = {
			            bootstrapMajorVersion: 3, //版本
			            currentPage: currentPage, //当前页数
			            totalPages: pageCount, //总页数
			            numberOfPages: 5,
			            itemTexts: function (type, page, current) {
			            	 
			              switch (type) {
			                case "first":
			                  return "首页";
			                case "prev":
			                  return "&laquo;";
			                case "next":
			                  return "&raquo;";
			                case "last":
			                  return "尾页";
			                case "page":
			                  return page;
			              }
			            },//点击事件，用于通过Ajax来刷新整个list列表 
			            onPageClicked: function (event, originalEvent, type, page) {
			          		$("#pageNum").val(page);
			            	business.addMainContentParserHtml('xxcj/xxcjJichaList.do',$("#searchForm").serialize());
			            }
			      };
			      $('#pageUl').bootstrapPaginator(options);
			}
			 //绑定搜索按钮
  			$('#search').click(function() {
  		    	$("#content").val(($("#checkunit").val()));
  				business.addMainContentParserHtml('xxcj/xxcjJichaList.do',$("#searchForm").serialize());
  		   	});
			 
	
		});
</script>
<body>
<div class="center_weizhi">当前位置：信息汇总- 稽查案卷列表</div>
<div class="center">
<div class="center_list">
    	<div class="panel-group" id="accordion">
          	<!---快速查询--->
          	<form id="searchForm">
              <input type="hidden" name="pagenum" id="pageNum" value="1"/>
               <input type="hidden" name="content" id="content"/>
        	</form>
            <!---搜索--->
            <div style="width:260px;" class="btn-group">
              <form class="bs-example bs-example-form">
                  <div class="row">                     
                     <div class="col-lg-6">
                        <div class="input-group">
                           <input type="text" class="form-control" placeholder="稽查单位关键字" value ="${content}" id="checkunit" style="width:200px;">
                           <span class="input-group-btn">
                              <button class="btn btn-success" id="search" type="button">
                              		快速搜索
                              </button>
                              <button id ="viewExcel" type="button" class="btn btn-primary">导出EXCEL</button>
                           </span>
                        </div><!-- /input-group -->
                     </div><!-- /.col-lg-6 -->
                  </div><!-- /.row -->
                </form>
            </div>            
    	</div>
        <table class="table table-bordered table-hover table-condensed">
         <thead>
             <tr>
             <td width="50" height="30" bgcolor="#efefef">序号</td>
             <td bgcolor="#efefef">稽查单位</td>
             <td width="150" bgcolor="#efefef">稽查时间</td>
             <td bgcolor="#efefef">被稽查单位名称</td>
             <td bgcolor="#efefef">稽查意见书文号</td>
             <td bgcolor="#efefef">稽查案卷文本</td>
             <td width="60" bgcolor="#efefef">操作</td>
           </tr>
         </thead>
         <tbody>
         <c:forEach var="item" items="${checkFileDescList.list}" varStatus="status">
         	<tr>
             <td height="30" align="center">${status.index+1}</td>
             <td>${item.checkunitPro}</td>
             <td><fmt:formatDate value="${item.checkdatePro}" pattern="yyyy-MM-dd"/></td>
	             <td>${item.checkedunitPro}</td>
	             <td>${item.filecodePro}</td>
	              <td>${item.checkfiletextPro}</td>
             <td>
             <%--2017用 <a href ="${checkFileDesc.downUrl}" target="_Blank"><button class="btn btn-success btn-xs" data-toggle="modal" data-target="#myModal">下载</button></a> --%>
	             <c:if test="${item.checkfiletextPro!=null and item.checkfiletextPro!=''}" > 
		             	<button class="btn btn-success btn-xs" onclick="javascript:window.location.href='${item.downUrl}'">下载</button>
		         </c:if>
             <!--<button class="btn btn-success btn-xs" onclick="javascript:window.location.href='${checkFileDesc.downUrl}'">下载</button>-->
         	</td>
           </tr>
         </c:forEach>
         </tbody>
       </table>
    </div>
     <!--列表翻页 开始-->
    <div class="page">
    	<span style="padding:12px; float:left; color:#0099cc;">共${checkFileDescList.total}条记录</span>
    	<ul class="pagination" id="pageUl">
        </ul>
    </div>
    <!--列表翻页 结束-->
</div>
</body>
</html>
