<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<html>
<head>
</head>
<body>
<div class="center_weizhi">当前位置：信息采集 - 参选先进集体信息 - 稽查案卷上报</div>
<div class="center">
<div class="center_list">
        <table class="table table-bordered table-hover table-condensed">
         <thead>
           <tr>
             <td width="50" height="30" bgcolor="#efefef">序号</td>
             <td bgcolor="#efefef">参选单位</td>
             <!-- <td bgcolor="#efefef">稽查单位</td>
             <td width="100" bgcolor="#efefef">稽查时间</td>
             <td bgcolor="#efefef">被稽查单位</td> -->
             <td bgcolor="#efefef">稽查意见书文号（一）</td>
             <td bgcolor="#efefef">稽查意见书文号（二）</td>
             <!-- <td bgcolor="#efefef">稽查案卷文本</td> -->
             <td width="90" bgcolor="#efefef">操作</td>
           </tr>
         </thead>
         <tbody>
            <c:forEach var="checkFileUnitBean" items="${list}" varStatus="status" >
         	<tr>
             <td height="30" align="center">${status.index+1}</td>
             <td> ${checkFileUnitBean.areaName}</td>
             <%-- <td>${checkFileDescList.checkUnit}</td>
             <td><fmt:formatDate value="${checkFileDescList.checkDate}" pattern="yyyy-MM-dd"/></td>
             <td>${checkFileDescList.checkedUnit}</td> --%>
             <td>${checkFileUnitBean.fileCode1}</td>
             <td>${checkFileUnitBean.fileCode2}</td>
             <%-- <td>${checkFileDescList.fileName}</td> --%>
             <td><a href="#"><button class="btn btn-info btn-xs"  onclick="macroMgr.onLevelTwoMenuClick(null, 'xxcj/xxcjJichaView.do?areaCode=${checkFileUnitBean.areaCode}')" data-target="#myModal">查看</button></a>
               <c:if test="${areaUser.sysStatus=='1'&&areaUser.reportState=='0'&&areaUser.areaCode==checkFileUnitBean.areaCode }">
                 <a href="#"><button class="btn btn-success btn-xs" onclick="macroMgr.onLevelTwoMenuClick(null, 'xxcj/xxcjJichaProvinceInput.do')">填报</button></a>
               </c:if>  
             </td>
           </tr>
         </c:forEach>
     <%--         <c:forEach var="checkFileDescList" items="${checkFileDescList}" varStatus="status" >
         	<tr>
             <td height="30" align="center">${status.index+1}</td>
             <td> ${checkFileDescList.city}</td>
             <td>${checkFileDescList.checkUnit}</td>
             <td><fmt:formatDate value="${checkFileDescList.checkDate}" pattern="yyyy-MM-dd"/></td>
             <td>${checkFileDescList.checkedUnit}</td>
             <td>${checkFileDescList.fileCode}</td>
             <td>${checkFileDescList.fileName}</td>
             <td> <a href="#"><button class="btn btn-success btn-xs" onclick="macroMgr.onLevelTwoMenuClick(null, 'xxcj/xxcjJichaCityInput.do?id=${checkFileDescList.id}')">编辑</button></a></td>
           </tr>
         </c:forEach> --%>
         </tbody>
       </table>
    </div>
</div>
</body>
</html>
