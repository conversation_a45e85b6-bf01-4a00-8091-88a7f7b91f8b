<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<script type="text/javascript">
 	  //删除方法
 	  function deleteGroupById(id){
 		 swal({
              title: "您确定执行删除操作吗？删除操作会将关联的稽查案卷同时删除",
              type: "warning",
              showCancelButton: true,
              closeOnConfirm: false,
              confirmButtonText: "是的，我要删除",
              confirmButtonColor: "#ec6c62"
          }, function() {
         	$.ajax({
      			   type: "POST",
      			   url: "${webpath}/xxcj/deleteXxcjGroup.do",
      			   data:{id:id},
      			   async:false,
      			   success: function(data){
      				   if(data.result=="error"){
      					  swal({title: "删除失败",text: "",type:"error"});
      		               return false;
      			       }else if(data.result=="success"){
      			    	  swal({title: "删除成功",text: "",type:"success"});
      			    	business.addMainContentParserHtml('xxcj/xxcjcityGroupList.do',null);
      			       }
      			   }
      			});
          });
 	  }
 	  
 	 $(function(){
 		 var reportState = $("#reportState").val();
 		 if(reportState==1){
 			 $("#subButton").text("已报送");
 			 $("#subButton").attr("disabled",true);
 		 } 
 		 
 		 $("#subButton").click(function(){
 			/* if(confirm("请确认本省候选单位和候选个人信息已全部录入完毕，且省级完成审核。一旦提交，所有推选信息将不允许修改。是否确认？")){
 				var areacode = ${areaUser.areaCode}
 	 			$.ajax({
 					 type: "POST",
 					   url: WEBPATH+'/xxcj/submit.do',
 					   data:{areacode:areacode},
 					   async:false,
 					   success: function(data){
 						   swal("报送成功");
 						   $("#subButton").attr("disabled",true);
 				 		   $("#subButton").text("已报送");
 					   }
 			    });
 			} */
 			var areacode = ${areaUser.areaCode}
	           swal({
	                   title: "请确认本省候选单位和候选个人信息已全部录入完毕，且省级完成审核。一旦提交，所有推选信息将不允许修改。是否确认？",
	                   type: "warning",
	                   showCancelButton: true,
	                   closeOnConfirm: false,
	                   confirmButtonText: "确认提交",
	                   confirmButtonColor: "#ec6c62"
	               }, function() {
	                $.ajax({
	                   type: "POST",
	                   url: "${webpath}/xxcj/submit.do",
	                   data:{areacode:areacode},
	                   async:false,
	                   success: function(data){
	                     if(data.result=="error"){
	                      swal({title: "审核报送失败",text: "",type:"error"});
	                            return false;
	                       }else if(data.result=="success"){
	                       		swal({title: "审核报送成功",text: "",type:"success"});
	                        	$("#subButton").attr("disabled",true);
	 				 		    $("#subButton").text("已报送");
	                       }
	                   }
	                });
	               });
 		          
 		 });
 	 }); 
</script>
<html>
<div class="center_weizhi">当前位置：信息采集 - 审核报送 - 报送信息</div>
<div class="center">
<div class="center_list">
	 <input type="hidden" id="reportState" value="${reportState}">
	 <button class="btn btn-danger" id="subButton" type="button" style="width:120px;">报送</button>
     <h4>省级案件</h4>
     <table class="table table-bordered table-hover table-condensed">
         <thead>
           <tr>
             <td width="50" height="30" bgcolor="#efefef">序号</td>
             <td bgcolor="#efefef">参选单位</td>
             <td width="200" bgcolor="#efefef">行政处罚案卷1</td>
           </tr>
         </thead>
         <tbody>
           <tr>
             <td height="30" align="center">1</td>
             <td>${areaUser.userName}</td>
             <td>${electionUnitFile.filecode}</td>
           </tr>
         </tbody>
     </table>
     <h4>市级案件</h4>
        <table class="table table-bordered table-hover table-condensed">
         <thead>
           <tr>
             <td width="50" height="30" bgcolor="#efefef">序号</td>
             <td bgcolor="#efefef">参选单位</td>
             <td width="200" bgcolor="#efefef">行政处罚案卷1</td>
             <td width="200" bgcolor="#efefef">行政处罚案卷2</td>
             <td width="200" bgcolor="#efefef">移送行政拘留案卷1</td>
             <td width="200" bgcolor="#efefef">涉嫌犯罪移送案卷1</td>
           </tr>
         </thead>
         <tbody>
           <c:forEach  var="electionunit" items="${cityGrouplList}" varStatus="status">
         		 <tr>
	             <td height="30" align="center">${status.index+1}</td>
	             <td>${electionunit.province }${electionunit.city }${electionunit.county }</td>
	             <td>${electionunit.xzcf1}</td>
	             <td>${electionunit.xzcf2 }</td>
	             <td>${electionunit.xzjl }</td>
	             <td>${electionunit.sxfz }</td>
	             </td>
	           </tr>
          	</c:forEach>
         </tbody>
       </table>
       <h4>县级案件</h4>
        <table class="table table-bordered table-hover table-condensed">
         <thead>
           <tr>
             <td width="50" height="30" bgcolor="#efefef">序号</td>
             <td bgcolor="#efefef">参选单位</td>
             <td width="200" bgcolor="#efefef">行政处罚案卷1</td>
             <td width="200" bgcolor="#efefef">行政处罚案卷2</td>
             <td width="200" bgcolor="#efefef">移送行政拘留案卷1</td>
             <td width="200" bgcolor="#efefef">涉嫌犯罪移送案卷1</td>
           </tr>
         </thead>
         <tbody>
           <c:forEach  var="electionunit" items="${countyGrouplList}" varStatus="status">
         		 <tr>
	             <td height="30" align="center">${status.index+1}</td>
	             <td>${electionunit.province }${electionunit.city }${electionunit.county }</td>
	             <td>${electionunit.xzcf1}</td>
	             <td>${electionunit.xzcf2 }</td>
	             <td>${electionunit.xzjl }</td>
	             <td>${electionunit.sxfz }</td>
	           </tr>
          	</c:forEach>
         </tbody>
       </table>
       <h4>稽查案卷</h4>
        <table class="table table-bordered table-hover table-condensed">
         <thead>
           <tr>
             <td width="50" height="30" bgcolor="#efefef">序号</td>
             <td bgcolor="#efefef">参选单位</td>
             <td bgcolor="#efefef">稽查意见书文号（一）</td>
             <td bgcolor="#efefef">稽查意见书文号（二）</td>
           </tr>
         </thead>
         <tbody>
           <c:forEach var="checkFileUnitBean" items="${list}" varStatus="status" >
         	<tr>
             <td height="30" align="center">${status.index+1}</td>
             <td> ${checkFileUnitBean.areaName}</td>
             <td>${checkFileUnitBean.fileCode1}</td>
             <td>${checkFileUnitBean.fileCode2}</td>
             <%-- <td>${checkFileDescList.fileName}</td> --%>
           </tr>
         </c:forEach>
         </tbody>
       </table>
       <h4>重点排污单位数量</h4>
       <table class="table table-bordered table-hover table-condensed">
         <thead>
           <tr>
             <td width="50" height="30" bgcolor="#efefef">序号</td>
             <td bgcolor="#efefef">参选单位</td>
             <td width="220" bgcolor="#efefef">行政区域内重点排污单位数量</td>
           </tr>
         </thead>
         <tbody>
           <c:forEach items="${XXCJ_ajslList }" var="unitAJSL" varStatus="i">
           <tr>
             <td height="30" align="center">${i.index+1 }</td>
             <td>${unitAJSL.areaname }</td>
             <td>${unitAJSL.areaguokongqynum }</td>
           </tr>
           </c:forEach>
         </tbody>
       </table>
       <h4>先进个人</h4>
       <table class="table table-bordered table-hover table-condensed">
         <thead>
           <tr>
             <td width="50" height="30" bgcolor="#efefef">序号</td>
             <td bgcolor="#efefef">所属行政区</td>
             <td width="100" bgcolor="#efefef">姓名</td>
             <td width="60" bgcolor="#efefef">性别</td>
             <td width="100" bgcolor="#efefef">职务</td>
             <td width="100" bgcolor="#efefef">所在单位名称</td>
             <td width="100" bgcolor="#efefef">环保工作年限</td>
             <td width="150" bgcolor="#efefef">身份证号码</td>
             <td width="100" bgcolor="#efefef">联系电话</td>
             <td width="150" bgcolor="#efefef">学历</td>
             <td width="150" bgcolor="#efefef">编制性质</td>
             <td width="160" bgcolor="#efefef">参与调查处理案件数量</td>
           </tr>
         </thead>
         <tbody>
           <c:forEach  var="electionPerson" items="${electionPersonalList}" varStatus="status">
	          <tr>
	             <td height="30" align="center">${status.index+1}</td>
	             <td>${electionPerson.province}${electionPerson.city}${electionPerson.country}</td>
	             <td>${electionPerson.name}</td>
	             <td>${electionPerson.sex}</td>
	             <td>${electionPerson.job}</td>
	             <td>${electionPerson.unitname}</td>
	             <td>${electionPerson.workyearnum}</td>             
	             <td>${electionPerson.cardid}</td>
	             <td>${electionPerson.phone}</td>
	             <td>${electionPerson.educationname}</td>
	             <td>${electionPerson.orgpropname}</td>
	             <td>${electionPerson.handlcasenum}</td>
	           </tr>
	         </c:forEach>
         </tbody>
       </table>
       <!-- <div style="float:right;"><button class="btn btn-danger" type="button" style="width:120px;">报送</button></div>		 -->	
</div>
</div>
</body>
</html>
