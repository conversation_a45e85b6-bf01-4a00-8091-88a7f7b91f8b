<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
<meta charset="utf-8">
<title>环境执法大练兵评分系统</title>
</head>
<body>
<div class="center_weizhi">当前位置：信息采集 - 参选先进集体信息 - 市级稽查案卷查看</div>
<div class="center">
<div class="center_list">
        <table align="center" class="table_input">
         <tbody>
           <tr>
             <td width="200">&nbsp;</td>
             <td><span style="color:#295A5F; font-size:18px; float:left; font-weight:bold;">市级</span></td>
           </tr>
           <tr>
             <td>市级参选单位</td>
             <td style="text-align:left;">${checkFileDescList.city }</td>
           </tr>
           <tr>
             <td>稽查单位</td>
             <td style="text-align:left;">${checkFileDescList.checkUnit }</td>
           </tr>
           <tr>
             <td>稽查时间</td>
             <td style="text-align:left;"><fmt:formatDate value="${checkFileDescList.checkDate}" pattern="yyyy-MM-dd"/></td>
           </tr>
           <tr>
             <td>稽查内容</td>
             <td style="text-align:left;">${checkFileDescList.checkContent }</td>
           </tr>
           <tr>
             <td>被稽查单位</td>
             <td style="text-align:left;">${checkFileDescList.checkedUnit }</td>
           </tr>
           <tr>
             <td>稽查意见书文号</td>
             <td style="text-align:left;">${checkFileDescList.fileCode }</td>
           </tr>
           <tr>
             <td>稽查案卷文本</td>
             <td style="text-align:left;">${checkFileDescList.fileName }</td>
           </tr>
         </tbody>
       </table>
    </div>
</div>
</body>
</html>
