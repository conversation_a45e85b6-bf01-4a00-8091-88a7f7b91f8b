<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
</head>
<body>
<div class="center_weizhi">当前位置：信息采集 - 参选先进集体信息 - 重点排污单位数量上报</div>
<div class="center">
<div class="center_list">
    <!-- <div class="panel-group" id="accordion"> -->
<!--          <form id="searchForm" name="searchForm" class="bs-example bs-example-form" role="form">-->
<!--            <div style="width:260px;" class="btn-group">-->
<!--               -->
<!--                  <div class="row">                     -->
<!--                     <div class="col-lg-6">-->
<!--                        <div class="input-group">-->
<!--                           <input name="name" type="text" value="${name }" class="form-control"  placeholder="请输入……" style="width:200px;">-->
<!--                           <span class="input-group-btn">-->
<!--                              <button id="searchButt" class="btn btn-success" type="button">-->
<!--                                 快速搜索-->
<!--                              </button>-->
<!--                           </span>-->
<!--                        </div> /input-group -->
<!--                     </div> /.col-lg-6 -->
<!--                  </div> /.row -->
<!--               -->
<!--            </div>   -->
<!--          </form>-->
    <!-- </div> -->
        <table class="table table-bordered table-hover table-condensed">
         <thead>
           <tr>
             <td width="50" height="30" bgcolor="#efefef">序号</td>
             <td bgcolor="#efefef">参选单位</td>
             <td bgcolor="#efefef">行政区域重点排污单位数量</td>
             <%-- <td width="230" bgcolor="#efefef">行政区域内省级重点监控企业数量</td>
      <c:if test="${user.areaCode!='55000000' && user.areaCode!='11000000' && user.areaCode!='12000000' && user.areaCode!='31000000' && user.areaCode!='66000000'}">
             <td width="230" bgcolor="#efefef">行政区域内市级重点监控企业数量</td>
      </c:if>  --%>      
             <td width="90" bgcolor="#efefef">操作</td>
           </tr>
         </thead>
         <tbody>
         	<c:forEach items="${XXCJ_ajslList }" var="unitAJSL" varStatus="i">
           <tr>
             <td height="30" align="center">${i.index+1 }</td>
             <td>${unitAJSL.areaname }</td>
             <td>${unitAJSL.areaguokongqynum }</td>
             <%-- <td>${unitAJSL.provincezhongdianqynum }</td>
      <c:if test="${user.areaCode!='55000000' && user.areaCode!='11000000' && user.areaCode!='12000000' && user.areaCode!='31000000' && user.areaCode!='66000000'}">
             <td>${unitAJSL.cityzhongdianqynum }</td>
      </c:if> --%>
             <td>
             	<c:if test="${unitAJSL.areatype=='1' }">
             		<a href="#" onclick="business.addMainContentParserHtml('xxcj/xxcjAjsl_view.do','ID=${unitAJSL.id}');"><button class="btn btn-info btn-xs" data-toggle="modal" data-target="#myModal">查看</button></a>
             	</c:if>
             	<c:if test="${unitAJSL.areatype=='2'||unitAJSL.areatype=='3' }">
             		<a href="#" onclick="business.addMainContentParserHtml('xxcj/xxcjAjsl_sjORxj_view.do','ID=${unitAJSL.id}');"><button class="btn btn-info btn-xs" data-toggle="modal" data-target="#myModal">查看</button></a> 
             	</c:if>
             	
             	<c:if test="${user.sysStatus=='1'&&user.reportState=='0'&& user.areaCode==unitAJSL.areacode }">
             		<c:choose>
	             		<c:when test="${unitAJSL.areatype=='1' }">
	             	    	<a href="#" onclick="business.addMainContentParserHtml('xxcj/xxcjAjsl_sj_input.do','ID=${unitAJSL.id}');"><button class="btn btn-success btn-xs" data-toggle="modal" data-target="#myModal">填报</button></a>
	             		</c:when>
	             		<c:when test="${unitAJSL.areatype=='3' }">
             				<a href="#" onclick="business.addMainContentParserHtml('xxcj/xxcjAjsl_xj_input.do','ID=${unitAJSL.id}');"><button class="btn btn-success btn-xs" data-toggle="modal" data-target="#myModal">填报</button></a>
             			</c:when>
             		</c:choose>
             	</c:if>
             </td>
           </tr>
           </c:forEach>
         </tbody>
       </table>
    </div>
</div>
<script language="JavaScript">

$(document).ready(function() {
	$(".topnav").accordion({
		accordion:false,
		speed: 500,
		closedSign: '[+]',
		openedSign: '[-]'
	});
});

</script>
</body>
</html>
