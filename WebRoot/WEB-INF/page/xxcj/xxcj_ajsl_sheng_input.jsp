<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
<script type="text/javascript">
$(document).ready(function(){
	 //文件上传1、
	 $("#Profile").fileinput({
        uploadUrl: WEBPATH+'/pdffileupload.do', // you must set a valid URL here else you will get an error
        allowedFileExtensions : ['pdf'],
        language:'zh',
		minFileCount: 1,
		maxFileCount: 1,
		minFileSize:1,
		maxFileSize:51200,
		enctype: 'multipart/form-data',        
		showPreview:false,
		autoReplace:true,
		slugCallback: function(filename) {
		    return filename.replace('(', '_').replace(']', '_');
		}
	}).on('filepreupload', function(event, data, previewId, index) { //文件开始上传操作
		
	   $("#xxcj_AJSL_Butt").prop('disabled', true);
	
	}).on('fileuploaded', function(event, data, previewId, index) {//文件上传完成执行操作

		$("#province_Fileurl").val(data.response.url);
	  	$("#province_Filename").val(data.response.fileRealName);
	  	$("#wsc").text(data.response.fileRealName);
	  	$("#xxcj_AJSL_Butt").prop('disabled',false);
	  	
	})
    
    $("#reButton").click(function(){
    	$("#uploadTr").attr("style","");
    });
		 
});
    
function shichange(i){
	var shi_type = $('#opentype'+i).val();
	if(shi_type=='1'){
		$("#shi1"+i).attr("style", "");
		$("#shi2"+i).attr("style", "display:none");
		$("#shi1"+i+' input').prop('disabled', false);
		$("#shi2"+i+' textarea').prop('disabled', true);
	}else if(shi_type=='2'){
		$("#shi2"+i).attr("style", "");
		$("#shi1"+i).attr("style", "display:none");
		$("#shi2"+i+' textarea').prop('disabled', false);
		$("#shi1"+i+' input').prop('disabled', true);
	}
}
</script>
</head>
<body>
<div class="center_weizhi">当前位置：信息采集 - 参选先进集体信息  - 重点排污单位数量上报</div>
<div class="center">
<form id="ajslForm">
<div class="center_list">
  <table align="center" class="table_input">
    <tbody>
      <tr>
        <td style="width:300px; height:80px;"><strong> ${user.userName }行政区域内重点排污单位数量</strong></td>
        <td style="text-align:left;">${xxcj_AJSL.areaguokongqynum }<font color="red">（提示：根据下方填报的地级市重点排污单位数量自动累计）</font></td>
      </tr>
      <%-- <tr>
        <td>证明文件</td>
        <td style="text-align:left;"><div style="width:260px;" class="btn-group">
          
            <div class="row">
              <div class="col-lg-6">
                <div class="input-group">
                  <input type="text" class="form-control" style="width:200px;">
                  <span class="input-group-btn">
                    <button class="btn btn-success" type="button"> 上传附件 </button>
                  </span></div>
                <!-- /input-group -->
              </div>
              <!-- /.col-lg-6 -->
            </div>
            <!-- /.row -->
            <input type="hidden" class="form-control" name="electionUnit.provinceofficialdocument" id="province_Filename" value="${xxcj_AJSL.provinceofficialdocument}">
			<input type="hidden" class="form-control" name="electionUnit.provinceofficialdocumenturl" id="province_Fileurl" value="${xxcj_AJSL.provinceofficialdocumenturl}">
        </div></td>
      </tr> --%>
      
      <tr>
        <td><strong>证明文件</strong></td>
        <td style="text-align:left;">
        <span id="wsc" style="font-size:16px;">
        <c:choose>
        	<c:when test="${xxcj_AJSL.provinceofficialdocument!='' && xxcj_AJSL.provinceofficialdocument!=null }">
        		<div style="float:left;margin-right:8px;padding-top:4px;">
        			${xxcj_AJSL.provinceofficialdocument}
        		</div>
        	</c:when>
        	<c:otherwise>
        	<font >未上传</font>
        	</c:otherwise>
        </c:choose>
        </span> 
        <c:if test="${xxcj_AJSL.provinceofficialdocument!='' && xxcj_AJSL.provinceofficialdocument!=null }">
        		<input id="reButton" class="btn btn-primary btn-xs" type="button" value="重新上传"/>
        </c:if>
        </td>
      </tr>
      <tr id="uploadTr" <c:if test="${xxcj_AJSL.provinceofficialdocument!='' && xxcj_AJSL.provinceofficialdocument!=null }">style='display:none'</c:if>>
        <td>&nbsp;</td>
        <td style="text-align:left;">
        <input id="Profile" type="file" name="Profile" class="file-loading">
        <span style="color: red;">仅允许上传PDF格式文件，大小不超过50M</span>
        <input type="hidden" class="form-control" name="electionUnit.provinceofficialdocument" id="province_Filename" value="${xxcj_AJSL.provinceofficialdocument}">
		<input type="hidden" class="form-control" name="electionUnit.provinceofficialdocumenturl" id="province_Fileurl" value="${xxcj_AJSL.provinceofficialdocumenturl}">
		<input type="hidden" class="form-control" name="electionUnit.id" id="id" value="${xxcj_AJSL.id}">
		<input type="hidden" class="form-control" name="electionUnit.areacode" id="areacode" value="${xxcj_AJSL.areacode}">
        </td>
      </tr>
      
      <c:forEach var="cityInfo" items="${areaList }" varStatus="i">
      <tr>
           	<td colspan="2">
           	<div class="shangbao_panel">
                <div class="shangbao_titlt">${cityInfo.name }</div>
                <table style="width:100%;" class="table_input">
                    <tr>
                        <td style="width:224px;">行政区域内重点排污单位数量</td>
                        <td>
                        <div class="form-group">
                        <input type="text" class="form-control" id="keypollennum${i.index}" <c:if test="${user.sysStatus!='1'|| user.reportState=='1'}">disabled="disabled"</c:if> name="cityList[${i.index}].keypollennum" value="${cityInfo.keypollennum }" placeholder="请输入行政区域内市级重点排污单位数量">
                        </div>
                        <input type="hidden" name="cityList[${i.index}].id" value="${cityInfo.id }"/>
                        <input type="hidden" name="cityList[${i.index}].code" value="${cityInfo.code }"/>
                        </td>
                    </tr>
                    <c:if test="${user.areaCode!='11000000'&&user.areaCode!='12000000'&&user.areaCode!='31000000'&&user.areaCode!='55000000'&&user.areaCode!='66000000' }">
                    <tr>
                        <td>公开方式</td>
                        <td>
                        <div class="form-group">
                        <select name="cityList[${i.index}].opentype" id="opentype${i.index}" <c:if test="${user.sysStatus!='1'|| user.reportState=='1'}">disabled="disabled"</c:if> onchange="shichange('${i.index}');" class="form-control">
                           <option value="">请选择</option>
                           <option value="1" <c:if test="${cityInfo.opentype=='1' }">selected</c:if>>网站公开</option>
                           <option value="2" <c:if test="${cityInfo.opentype=='2' }">selected</c:if>>其他公开方式</option>
                         </select>
                         </div>
                        </td>
                    </tr>
                    <tr id="shi1${i.index}" <c:choose><c:when test="${cityInfo.opentype=='1' }"></c:when><c:otherwise>style="display:none;"</c:otherwise></c:choose>>
                        <td>公开网站地址</td>
                        <td>
                        <div class="form-group">
                        <input type="text" class="form-control" <c:if test="${user.sysStatus!='1'|| user.reportState=='1'}">disabled="disabled"</c:if> id="openurl${i.index}" name="cityList[${i.index}].openurl" value="${cityInfo.openurl}" placeholder="请输入公开网站地址">
                        </div>
                        </td>
                    </tr>
                    <tr id="shi2${i.index}" <c:choose><c:when test="${cityInfo.opentype=='2' }"></c:when><c:otherwise>style="display:none;"</c:otherwise></c:choose>>
                        <td>其他公开方式描述</td>
                        <td>
                        <div class="form-group">
                        <textarea rows="6" class="form-control" id="opendesc${i.index}" <c:if test="${user.sysStatus!='1'|| user.reportState=='1'}">disabled="disabled"</c:if> name="cityList[${i.index}].opendesc" placeholder="请输入其他公开方式描述">${cityInfo.opendesc}</textarea>
                        </div>
                        </td>
                    </tr>
                  </c:if>  
                </table>
            </div>
            </td>
       </tr>
      </c:forEach>  
      <tr>
	        <td align="center">&nbsp;
	        <input type="hidden" name="id" value="${xxcj_AJSL.id }"/>
	        </td>
	        <td style="text-align:left;">
	        <c:if test="${user.sysStatus=='1'&& user.reportState=='0'}">
	        	<a href="#">
	          	<button type="button"  id="xxcj_AJSL_Butt" class="btn btn-primary" name="signup" value="Sign up" style="font-size:16px;width:150px; margin-top:5px;">信息保存</button>
	          	</a>
	        </c:if>
	        </td>
	      </tr>
    </tbody>
  </table>
    </div>
    </form>
</div>
<script language="JavaScript">
	//console.info("123");
	//var arr_OBJ = "{\"cityList[0].keypollennum\":{\"validators\": {\"notEmpty\": {\"message\": \"请填写该行政区域内重点排污单位数量.\"},\"stringLength\": {\"max\": 9,\"message\": \"该项不能超过9个字符.\"},\"regexp\": {\"regexp\": \"/^[0-9]+$/\",\"message\": \"该项只能填写整数.\"}}},\"cityList[0].opentype\": {\"validators\": {\"notEmpty\": {\"message\": \"请选择公开方式.\"}}}}"; 
	/*
	var arr_OBJ = "{\"cityList[0].keypollennum\":{\"validators\": {\"notEmpty\": {\"message\": \"请填写该行政区域内重点排污单位数量.\"},\"stringLength\": {\"max\": 9,\"message\": \"该项不能超过9个字符.\"},\"regexp\": {\"regexp\": \"/^[0-9]+$/\",\"message\": \"该项只能填写整数.\"}}},\"cityList[0].opentype\": {\"validators\": {\"notEmpty\": {\"message\": \"请选择公开方式.\"},"+
		"\"callback\": {\"message\": \"\",\"callback\": \"function(value, validator, $field) {openTypeCasc(value,0);return true;}\"}}},"+
		"\"cityList[0].openurl\": {\"validators\": {\"notEmpty\": {\"message\": \"公开网址选择网站公开,请输入公开网址地址\"}}},\"cityList[0].opendesc\": {\"validators\": {\"notEmpty\": {\"message\": \"公开网址选择其他公开方式,请输入其他公开方式描述\"}}}}"; 

	console.info(arr_OBJ);
	*/
	
	//表单校验
	$(document).ready(function() {
		
		var loginAreacode = '${user.areaCode}';
		var subAreaCode = loginAreacode.substring(0, 2);
		var arr_OBJ = "{";
			
		if(subAreaCode=='11'||subAreaCode=='12'||subAreaCode=='31'||subAreaCode=='55'||subAreaCode=='66'){//直辖市
			for (var i = 0; i < '${areaList.size()}'; i++) {
				arr_OBJ += "\"cityList["+i+"].keypollennum\":{\"validators\": {\"notEmpty\": {\"message\": \"请填写该行政区域内重点排污单位数量.\"},\"stringLength\": {\"max\": 9,\"message\": \"该项不能超过9个字符.\"},\"integer\":{\"message\": \"该项只能填写整数.\"}}},";
			}
		}else{//普通省
			for (var i = 0; i < '${areaList.size()}'; i++) {
				arr_OBJ += "\"cityList["+i+"].keypollennum\":{\"validators\": {\"notEmpty\": {\"message\": \"请填写该行政区域内重点排污单位数量.\"},\"stringLength\": {\"max\": 9,\"message\": \"该项不能超过9个字符.\"},\"integer\":{\"message\": \"该项只能填写整数.\"}}},\"cityList["+i+"].opentype\": {\"validators\": {\"notEmpty\": {\"message\": \"请选择公开方式.\"},"+
				"\"callback\": {\"message\": \"\",\"callback\": \"function(value, validator, $field) {openTypeCasc(value,"+i+");return true;}\"}}},"+
				"\"cityList["+i+"].openurl\": {\"validators\": {\"notEmpty\": {\"message\": \"公开网址选择网站公开,请输入公开网址地址\"},\"stringLength\": {\"max\": 500,\"message\": \"该项不能超过500个字符.\"}}},\"cityList["+i+"].opendesc\": {\"validators\": {\"notEmpty\": {\"message\": \"公开网址选择其他公开方式,请输入其他公开方式描述\"},\"stringLength\": {\"max\": 1000,\"message\": \"该项不能超过1000个字符.\"}}},";
			}
		}
		
		if(arr_OBJ!="{"&&arr_OBJ.length>1){
			arr_OBJ = arr_OBJ.substring(0, arr_OBJ.length-1)+"}";
		}
		
		
	$('#ajslForm').formValidation({
		message: 'This value is not valid',
	    icon: {
	    	valid: 'glyphicon glyphicon-ok',
	        invalid: 'glyphicon glyphicon-remove',
	        validating: 'glyphicon glyphicon-refresh'
	    },
	    autoFocus: true,
	    fields:JSON.parse(arr_OBJ)
	    });
	//JSON.parse(arr_OBJ)
	/*
	{
	        'cityList[0].keypollennum': {
	        	'validators': {
	            	'notEmpty': {
	                	'message': '请填写该行政区域内重点排污单位数量.'
	                },
	                'stringLength': {
	                	'max': 9,
	                	'message': '该项不能超过9个字符.'
	                },
	                'regexp': {
	                    'regexp': /^[0-9]+$/,
	                    'message': '该项只能填写整数.'
	                }
	            }
	        },
	        'cityList[0].opentype': {
	        	'validators': {
	            	'notEmpty': {
	                	'message': '请选择公开方式.'
	                },
	                'callback': {
                       	message: '',
                        callback: function(value, validator, $field) {
                        	openTypeCasc(value,0);
        					return true;
                       }
                  }
	            }
	        },
            "cityList[0].openurl": {
                validators: {
                    notEmpty: {
                        message: '公开网址选择网站公开,请输入公开网址地址'
                    }
                }
            },
            "cityList[0].opendesc": {
                validators: {
                    notEmpty: {
                        message: '公开网址选择其他公开方式,请输入其他公开方式描述'
                    }
                }
            }
	    }
	
	*/
	
	
	
	function openTypeCasc(value,i){
		if(value==1){
    		$("#ajslForm").formValidation('enableFieldValidators', "cityList["+i+"].openurl", true);
    		$("#ajslForm").formValidation('enableFieldValidators', "cityList["+i+"].opendesc", false);
    		$("#openurl"+i).prop("disabled",false);
    		$("#opendesc"+i).prop("disabled",true);
    	}else if(value==2){
    		$("#ajslForm").formValidation('enableFieldValidators', "cityList["+i+"].opendesc", true);
    		$("#ajslForm").formValidation('enableFieldValidators', "cityList["+i+"].openurl", false);
    		$("#openurl"+i).prop("disabled",true);
    		$("#opendesc"+i).prop("disabled",false);
    	}
	}
	});
	
	//表单提交
	$(document).ready(function(){
		$("#xxcj_AJSL_Butt").click(function(){
			var validate = true;
			$("#ajslForm").data('formValidation').validate();
			validate = $("#ajslForm").data('formValidation').isValid();
			
			if(validate){
				var fileurl=$("#province_Fileurl").val();
       			var filename=$("#province_Filename").val();
				if(fileurl!=""&&filename!=""){//
					var options = {
						url:WEBPATH+'/xxcj/saveXxcjSL_sj.do',
						type:'post',
						success:function(data){
						if(data.result=="error"){
							swal("保存失败!", data.message, "error");
			                return false;
			             }else if(data.result=="success"){
			             	swal({
							    	title: "保存成功!",
							        type: "success",
							        closeOnConfirm: true,
							        confirmButtonText: "确定",
							        confirmButtonColor: "#A7D5EA"
						    	}, function() {
							    	business.addMainContentParserHtml('xxcj/xxcjAjsl.do','');
							});
			               return false;
			             }else if(data.code=="007"){
			            	 swal(data.message, "", "info");
			            	 return false;
			             }	
						},
						error:function(){
							swal("服务异常,保存失败!", "", "error");
						}
					};
					$("#ajslForm").ajaxSubmit(options);
				}else{
					swal("请上传正式发文的名单扫描件!", "", "error");
				}
			}else if(validate==null){
				//表单未填写
				$("#ajslForm").data('formValidation').validate();
	       
	        }
		});
	});
	
	
	
$(document).ready(function() {
	$(".topnav").accordion({
		accordion:false,
		speed: 500,
		closedSign: '[+]',
		openedSign: '[-]'
	});
});

</script>
</body>
</html>
