<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
<script type="text/javascript">
$(document).ready(function(){
	 //文件上传、
	 $("#file1,#file2").fileinput({
      uploadUrl: WEBPATH+'/pdffileupload.do', // you must set a valid URL here else you will get an error
      allowedFileExtensions : ['pdf'],
      language:'zh',
      //overwriteInitial: true,
      minFileCount: 1,
      maxFileCount: 1,
      minFileSize:1,
      maxFileSize:51200,
      enctype: 'multipart/form-data',        
      dropZoneTitle:"可拖拽文件到此处...",
      initialPreviewShowDelete: false,
      msgFilesTooMany:'选择上传的文件数量({n}) 超过允许的最大数值{m}！',
      msgZoomModalHeading:'文件预览',
      msgInvalidFileExtension:'不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
      msgNoFilesSelected:'请选择文件',
      msgValidationError:'文件类型不正确或文件过大',
      initialPreviewFileType:'pdf',
      browseLabel:"选择文件",
      removeLabel:'删除',
      removeTitle:'删除文件',
      uploadLabel:'上传',
      uploadTitle:'上传文件',
      cancelLabel: '取消',
      cancelTitle: '取消上传',
      showPreview:false,
      autoReplace:true,
      slugCallback: function(filename) {
          return filename.replace('(', '_').replace(']', '_');
      }
	}).on('filepreupload', function(event, data, previewId, index) { //文件开始上传操作
		
	   $("#saveXxcjPersonBtn").prop('disabled', true);
	
	}).on('fileuploaded', function(event, data, previewId, index) {//文件上传完成执行操作
		
		if(event.currentTarget.id=="file1"){
			$("#personalmaterialname").val(data.response.fileRealName);
		  	$("#personalmaterialurl").val(data.response.url);
		  	$("#filetext1").text(data.response.fileRealName);
		}else{
			$("#perhonestfilename").val(data.response.fileRealName);
		  	$("#perhonestfileurl").val(data.response.url);
		  	$("#filetext2").text(data.response.fileRealName);
		}
		$("#saveXxcjPersonBtn").prop('disabled',false);
	})/* .on('fileclear', function(event) {  //文件上传删除文件执行的操作
		if(event.currentTarget.id=="file1"){
			$("#personalmaterialname").val("");
		  	$("#personalmaterialurl").val("");
		  	$("#filetext1").text("未上传");
		}else{
			$("#perhonestfilename").val("");
		  	$("#perhonestfileurl").val("");
		  	$("#filetext2").text("未上传");
		}
  		
  	}) */
  
	//保存数据方法
	$('#saveXxcjPersonBtn').click(function() {
     	 var options = {
         url: WEBPATH+'/xxcj/saveXxcjPerson.do',
         type: 'post',
         success:function(data){
	           if(data.result=="error"){
	        	   swal("操作失败", "信息保存操作失败了!", "error");
	               return false;
	           }else if(data.result=="success"){
	        	   business.addMainContentParserHtml('xxcj/countyPerson.do',null);
	        	  swal({title: "保存成功",text: "",type:"success"});
	             return false;
	           }
       	}
     	 };
     	$("#saveXxcjPersonForm").data('formValidation').validate();
     	var validate = $("#saveXxcjPersonForm").data('formValidation').isValid();
     	if(validate){
     		var materialname=$("#personalmaterialname").val();
      		var materialurl=$("#personalmaterialurl").val();
      		
      		var honestfilename=$("#perhonestfilename").val();
      		var honestfileurl=$("#perhonestfileurl").val();
      		
     		if((materialname==""||materialurl=="")||(honestfilename==""||honestfileurl=="")){
     		 swal("提示", "请选择上传文件", "error");
     		}else{
     			var anjuan=$("#typeaheadxxcf1").val();
     			if(anjuan!=""&&anjuan!=null){
     				var res = $(".multiselect-data-ajax").select2("data");
     				if(res.length>0){
     					$.ajax({
             			   type: "POST",
             			   url: "${webpath}/xxcj/selectPersonAnjuanCount.do",
             			   //data:{areacode:'${areaUser.areaCode}'.substring(0,2),type:$("#filetype").val()},
             			   async:false,
             			   success: function(data){
             				   if(data.result=="error"){
             					   swal({title: "查询失败",text: "",type:"error"});
             		               return false;
             			       }else if(data.result=="success"){
             			          if(data.data>=10){
             			        	swal({title: "操作失败",text: "个人信息上报数量已经达到上限",type:"error"});
             			          }else{
             			        	var areaname=$("[name='electionPersonal.areacode']").find("option:selected").text();
             			       		var cityname=$("#city").find("option:selected").text();
             			       		var areacode=$("[name='electionPersonal.areacode']").val();
             			       		$("[name='files[0].areacode']").val(areacode)
             			           	$("#areaname").val(areaname);
             			           	$("#cityname").val(cityname);
             			           	$("#countryname").val(areaname);
             		     			$('#saveXxcjPersonForm').ajaxSubmit(options);
             			          }
             			       }
             			   }
             			});
     				}else{
     					swal("提示", "请选择参与调查处理案件", "error");
     				}
     			}else{
     				swal("提示", "请选择案卷", "error");
     			}
     			
     		}
     	}
 	});
	//表单验证
	$("#saveXxcjPersonForm").formValidation({
      framework: 'bootstrap',
      message: 'This value is not valid',
      icon:{
	            valid: 'glyphicon glyphicon-ok',
	            invalid: 'glyphicon glyphicon-remove',
	            validating: 'glyphicon glyphicon-refresh'
      },
      fields: {
      		"files[0].filetype": {
              validators: {
                  notEmpty: {
                      message: '请选择案卷类型'
                  }
              }
          },
          "electionPersonal.areacode": {
              validators: {
                  notEmpty: {
                      message: '请选择区县'
                  }
              }
          },
          "city": {
              validators: {
                  notEmpty: {
                      message: '请选择市'
                  }
              }
          },
          "electionPersonal.filesimpledesc": {
              validators: {
                  notEmpty: {
                      message: '请输入案卷材料简版说明'
                  },
                  stringLength: {
                      min: 1,
                      max: 300,
                      message: '1-300个字符'
                  }
              }
          },
           "electionPersonal.filedetiaildesc": {
              validators: {
                  notEmpty: {
                      message: '请输入案卷材料详版说明'
                  },
                  stringLength: {
                      min: 1,
                      max: 2000,
                      message: '1-2000个字符'
                  }
              }
          },
          "electionPersonal.name": {
              validators: {
                  notEmpty: {
                      message: '请输入姓名'
                  },
                  stringLength: {
                      min: 1,
                      max: 15,
                      message: '1-15个字符'
                  }
              }
          },
          "electionPersonal.sex": {
              validators: {
                  notEmpty: {
                      message: '请选择性别'
                  }
              }
          },
          "electionPersonal.job": {
              validators: {
                  notEmpty: {
                      message: '请输入职务'
                  },
                  stringLength: {
                      min: 1,
                      max: 30,
                      message: '1-30个字符'
                  }
              }
          },
          "electionPersonal.cardid": {
              validators: {
                  notEmpty: {
                      message: '请输入身份证号码'
                  },
                  regexp:{
                   	message:'请输入正确的身份证号码',
                   	regexp:/^\d{17}(\d|x)$/i
                  },
                  remote: {
                  	   dataType:'json',
                  	   type:'POST',
                  	   delay:500,
                         url: '${webpath}/xxcj/checkIdCardInput.do',
                         data: function(validator, $field, value) {
                             return {
                                 idcard:validator.getFieldElements('electionPersonal.cardid').val()
                             };
                         },
                  	   message:'身份证号码已经存在，不允许重复录入'
                     }
              }
          },
          "electionPersonal.phone": {
              validators: {
                  notEmpty: {
                      message: '请输入联系电话'
                  },
                  phone:{
                  	message:'请输入正确的手机号码',
                  	country:'CN'
               	}
              }
          }
	   }
	});
	
	//初始化加载select2
	$(".multiselect-data-ajax").select2({
		  language:'zh-CN',
		  ajax: {
		    url: "${webpath}/xxcj/selectAnjuanByAreacodeAll.do",
		    dataType: 'json',
		    delay: 500,
		    type:'POST',
		    data: function (params) {
		      return {
		        query: params.term,
		        areacode:$("#county").val()
		      };
		    },
		    processResults: function (data, params) {
		      return {
		        results: data,
		      };
		    },
		    cache: true
		  },
		  escapeMarkup: function (markup) 
		  { 
			  return markup; 
		  }, 
		  templateResult:function (result) {
		        return result.text;
	      },
		  templateSelection:function (result) {
		        return result.text;
	      },
		  minimumInputLength:3,
		  placeholder:'请选择参与调查处理案件的决定书文号或移送编号',
		  allowClear:true
		});
		//绑定选择select事件
		$('.multiselect-data-ajax').on('select2:select', function (evt) {
			var res = $(".multiselect-data-ajax").select2("data");
			var value= [];
			$.each(res,function(index,obj){
				var objValue={id:obj.id,text:obj.text}
				value.push(objValue);
			});
			$("#handlcasecodesselectvalue").val(JSON.stringify(value).replace(/\"/g, "'"));
			$("#handlcasenumtext").text(res.length);
	     	$("#handlcasenum").val(res.length);
		});
		//绑定取消选择select事件
		$('.multiselect-data-ajax').on('select2:unselect', function (evt) {
			var res = $(".multiselect-data-ajax").select2("data");
			var value= [];
			$.each(res,function(index,obj){
				var objValue={id:obj.id,text:obj.text}
				value.push(objValue);
			});
			$("#handlcasecodesselectvalue").val(JSON.stringify(value).replace(/\"/g, "'"));
			$("#handlcasenumtext").text(res.length);
	     	$("#handlcasenum").val(res.length);
		});
		
		//初始化加载select2
		$("#typeaheadxxcf1").select2({
			  language:'zh-CN',
			  ajax: {
			    url: "${webpath}/xxcj/selectAnjuanByAreacode.do",
			    dataType: 'json',
			    delay: 500,
			    type:'POST',
			    data: function (params) {
			      return {
			        query: params.term,
			        type:$("#filetype").val(),
			        qtype:0,
			        areacode:$("#county").val()
			        
			      };
			    },
			    processResults: function (data, params) {
			      return {
			        results: data
			      };
			    },
			    cache: true
			  },
			  escapeMarkup: function (markup) 
			  { 
				  return markup; 
			  },
			  templateResult:function (result) {
			        return result.text;
		      },
			  templateSelection:function (result) {
			        return result.text;
		      },
			  minimumInputLength:3,
			  theme:'bootstrap',
			  placeholder:'请选择决定文书号/移送编号',
			  allowClear:true
			});
	    	//绑定选择select事件
			$('#typeaheadxxcf1').on('select2:select', function (evt) {
				var res = $("#typeaheadxxcf1").select2("data");
				$("#filecodexxcf1").val(res[0].text);
				$("#belongareacodexxcf1").val(res[0].areacode);
				$("#xxcf1oldid").val(res[0].oldid);
			});
			//绑定取消选择select事件
			$('#typeaheadxxcf1').on('select2:unselecting', function (evt) {
				$("#filecodexxcf1").val("");
				$("#belongareacodexxcf1").val("");
				$("#xxcf1oldid").val("");
			});
});
var resetSelect=function(){
	 $("#filecodexxcf1").val("");
	 $("#belongareacodexxcf1").val("");
	 $("#xxcf1oldid").val("");
	 $("#typeaheadxxcf1").val(null).trigger("change");
	 
	 $("#handlcasecodesselectvalue").val("");
	 $("#handlcasenumtext").text("");
    $("#handlcasenum").val("");
	 $(".multiselect-data-ajax").val(null).trigger("change");
}
</script>
</head>
<body>
<div class="center_weizhi">当前位置：信息采集 - 参选先进个人信息 - 县级先进个人上报</div>
<div class="center">
<div class="center_list">
	<form id="saveXxcjPersonForm" method="post">
  <table class="table_input">
    <tbody>
      <tr>
        <td width="200">请选择市</td>
        <td>
        	<div class="form-group">
                 <div class="col-sm-12">
		            <select  id="city" class="form-control" name="city" onchange="business.cascaded($('#city').val(),'county');resetSelect();">
		             <option value="">请选择地市</option>
		              <c:forEach var="city" items="${cityList}">
						               	<option value="${city.code}">${city.name}</option>
						  </c:forEach>
		            </select>
          	</div>
          	</div>
            <input type="hidden" name="electionPersonal.areatype" value="3"/>
            <input type="hidden" name="electionPersonal.areaname" id="areaname" />
            <input type="hidden" name="electionPersonal.country" id="countryname" />
            <input type="hidden" name="electionPersonal.province" value="${areaUser.userName}"/>
            <input type="hidden" name="electionPersonal.city" id="cityname" />
          </td>
      </tr>
      <tr>
        <td width="200">请选择区县</td>
        <td>
        	<div class="form-group">
                 <div class="col-sm-12">
		            <select name="electionPersonal.areacode" id="county"  class="form-control" onchange="resetSelect();">
			               <option value="">请选择区县</option>
					</select>
          	</div>
          	</div>
          </td>
      </tr>
     	<tr>
             <td>案卷类型</td>
             <td>
             <div class="form-group">
                 <div class="col-sm-12">
             <select name="files[0].filetype" class="form-control" id="filetype" onchange="resetSelect();">
               <option value="">请选择案卷类型</option>
               <option value="0">行政处罚案卷</option>
               <option value="1">按日计罚案卷</option>
               <option value="2">移送行政拘留案卷</option>
               <option value="3">涉嫌犯罪移送案卷</option>
               <option value="4">申请法院强制执行案卷</option>
             </select>
             </div>
             </div>
             </td>
           </tr>
            <tr>
             <td>决定文书号/移送编号</td>
             <td style="text-align: left;">
             <div class="form-group">
                 <div class="col-sm-12">
                  <select id="typeaheadxxcf1" class="form-control" >
				 </select>
                 <input type="hidden" name="files[0].belongareacode" id="belongareacodexxcf1" />
                 <input type="hidden" name="files[0].filecode" id="filecodexxcf1" />
                  <input type="hidden" name="files[0].oldid" id="xxcf1oldid"/>
               </div>
             </div>
              </td>
           </tr>
           <!-- <tr>
             <td>案卷名称</td>
             <td>
             <div class="form-group">
                 <div class="col-sm-12">
                 <select name="files[0].belongareacode"   class="form-control" id="anjuanname">
                 		<option value="">请输入案卷名称</option>
                 </select>
               </div>
             </div>
              </td>
           </tr> -->
            <tr>
             <td>案件调查情况材料简版说明</td>
             <td>
             <div class="form-group">
                 <div class="col-sm-12">
             <textarea rows="5" class="form-control" name="electionPersonal.filesimpledesc" placeholder="请输入案卷材料简版说明"></textarea>
             </div>
             </div>
             </td>
           </tr>
           <tr>
             <td>案件调查情况材料详版说明</td>
             <td>
             <div class="form-group">
                 <div class="col-sm-12">
             	<textarea rows="5" class="form-control" name="electionPersonal.filedetiaildesc" placeholder="请输入案卷材料详版说明"></textarea>
             </div>
             </div>
             </td>
           </tr>
           <tr>
             <td>姓名</td>
             <td>
             <div class="form-group">
                 <div class="col-sm-12">
             <input type="text" class="form-control" name="electionPersonal.name" placeholder="请输入姓名">
             </div>
             </div>
             </td>
           </tr>
           <tr>
             <td>性别</td>
             <td>
             <div class="form-group">
                 <div class="col-sm-12">
             <select name="electionPersonal.sex" class="form-control">
               <option value="">请选择性别</option>
               <option>男</option>
               <option>女</option>
             </select>
             </div>
             </div>
             </td>
           </tr>
           <tr>
             <td>职务</td>
             <td>
             <div class="form-group">
                 <div class="col-sm-12">
             <input type="text" class="form-control" name="electionPersonal.job" placeholder="请输入职务">
             </div>
             </div>
             </td>
           </tr>
           <tr>
             <td>身份证号码</td>
             <td>
             <div class="form-group">
                 <div class="col-sm-12">
             <input type="text" class="form-control" name="electionPersonal.cardid" placeholder="请输入身份证号码">
             </div>
             </div>
             </td>
           </tr>
           <tr>
             <td>联系电话</td>
             <td>
             <div class="form-group">
                 <div class="col-sm-12">
             <input type="text" class="form-control" name="electionPersonal.phone" placeholder="请输入联系电话">
             </div>
             </div>
             </td>
           </tr>
            <tr>
             <td>参与调查处理案件</td>
             <td>
	             <!-- <div class="form-group">
                 <div class="col-sm-12">
                 	<select class="multiselect-data-ajax form-control" multiple="multiple">
					</select>
					<input type="hidden" name="electionPersonal.handlcasecodes" id="handlcasecodesselectvalue" />
             	</div>
             	</div> -->
             	 <div class="form-group">
                 <div class="col-sm-12">
                 	<select class="multiselect-data-ajax  form-control" multiple="multiple">
					</select>
					<input type="hidden" name="electionPersonal.handlcasecodes" id="handlcasecodesselectvalue" />
	             </div>
	             </div>
             </td>
           </tr>
           <tr>
             <td>参与调查处理案件数量</td>
             <td style="text-align:left;">
             <div class="form-group">
                 <div class="col-sm-12">
		             <span id="handlcasenumtext" >根据选择的参与调查处理案件案卷号数量自动获取</span>
		             <input type="hidden" class="form-control" name="electionPersonal.handlcasenum" id="handlcasenum"/>
             </div>
                 </div>    
             </td>
           </tr>
          <!--  <tr>
             <td>参与调查处理案件数量</td>
             <td>
             <div class="form-group">
                 <div class="col-sm-12">
             <input type="text" class="form-control" name="electionPersonal.handlcasenum" placeholder="请输入参与调查处理案件数量">
             </div>
             </div>
             </td>
           </tr>
           <tr>
             <td>参与调查处理案件案卷号</td>
             <td style="text-align:left;">
             <div class="form-group">
                 <div class="col-sm-12">
             <textarea name="electionPersonal.handlcasecodes" rows="5" class="form-control" id="name8" placeholder="请输入参与调查处理案件案卷号"></textarea>
             </div>
             </div>
             </td>
           </tr> -->
           <tr>
             <td>个人事迹材料</td>
             <td style="text-align:left;">
             		<div class="form-group">
                 <div class="col-sm-12">
                 <span id="filetext1" style="font-size: 14px;">
              			  未上传
		        </span> 
                 </div>
                 </div>
                 <input type="hidden" name="electionPersonal.personalmaterialname" id="personalmaterialname"/>
                 <input type="hidden" name="electionPersonal.personalmaterialurl" id="personalmaterialurl"/>
             </td>
           </tr>
           <tr>
               <td>上传附件</td>
               <td style="text-align:left;">
               			<div class="form-group">
                 <div class="col-sm-12">
	               		<input id="file1" type="file" name="file1" class="file-loading"  value="1"> 
	               		<span style="color: red;">仅允许上传PDF格式文件，大小不超过50M</span>
	               		</div>
	               		</div>
               </td>
           </tr>
           <tr>
             <td>个人廉洁执法相关证明材料</td>
             <td style="text-align:left;">
             <div class="form-group">
                 <div class="col-sm-12">
                <span id="filetext2" style="font-size: 14px;">
              			  未上传
		        </span> 
		        </div>
		        </div>
		        <input type="hidden" name="electionPersonal.perhonestfilename" id="perhonestfilename"/>
                 <input type="hidden" name="electionPersonal.perhonestfileurl" id="perhonestfileurl"/>
             </td>
           </tr>
            <tr>
               <td>上传附件</td>
               <td style="text-align:left;">
               	<div class="form-group">
                 <div class="col-sm-12">
	               		<input id="file2" type="file" name="file2" class="file-loading" value="2"> 
	               		<span style="color: red;">仅允许上传PDF格式文件，大小不超过50M</span>
	               </div>
	               </div>		
               </td>
           </tr>
      <tr>
      <tr>
        <td align="center">&nbsp;</td>
        <td style="text-align:left;"><a href="#">
          <button type="button" class="btn btn-primary" id="saveXxcjPersonBtn" style="font-size:16px;width:150px; margin-top:5px;">信息保存</button>
        </a></td>
      </tr>
    </tbody>
  </table>
  </form>
</div>
</div>

<script type="text/javascript">
$(document).ready(function(){
});
</script>
</body>
</html>
