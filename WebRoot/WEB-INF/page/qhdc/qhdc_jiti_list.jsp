<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
</head>
<body>
<div class="center_weizhi">当前位置：强化督查 - 先进个人评选 - 督查结果
</div>
<div class="center">
<div class="center_list">
    <div class="panel-group" id="accordion">
          <h4 class="panel-title">
          <!---快速查询--->
         <form id= "searchForm"  role="form">
              <select class="form-control" style="width:150px;margin-right:5px;" name="areaType" id="areaTypeSelect">
                 <option value="" >请选择行政区</option>
                 <option value="1" <c:if test="${areaType=='1' }">selected</c:if>  >省级</option>
                 <option value="2" <c:if test="${areaType=='2' }">selected</c:if> >地市级</option>
                 <option value="3" <c:if test="${areaType=='3' }">selected</c:if> >区县级</option>
              </select>
        </form>
               <button type="button" class="btn btn-primary" id ="viewExcel" >导出EXCEL</button> 
          </h4>
    </div>
        <table class="table table-bordered table-hover table-condensed">
        
         <thead>
         <tr>
             <td width="50" height="30" bgcolor="#efefef">序号</td>
             <td bgcolor="#efefef">省</td>
             <td width="150" bgcolor="#efefef">地市</td>
             <td width="150" bgcolor="#efefef">区县</td>
             <td width="150" bgcolor="#efefef">分值</td>
           </tr>
         </thead>
         <tbody>
            <c:forEach items="${electionUnitList.list}" var="item" varStatus="status">
	           <tr>
	             <td height="30" align="center">${status.index+1}<input id="id${status.index+1}" type="hidden"  value="${item.id}"></td>
	             <td>${item.province}</td>
	             <td>${item.city}</td>
	             <td>${item.country}</td>
	             <td>${item.superviseScore}</td>
	           </tr>
           </c:forEach>
         </tbody>
       </table>
       <input type="hidden" value="${areaType}" id ="areaType">
    </div>
     <!--列表翻页 开始-->
    <div class="page">
    	<span style="padding:12px; float:left; color:#0099cc;">共${electionUnitList.total}条记录</span>
        <ul class="pagination" id="pageCon">
            
        </ul>
    </div>
    <!--列表翻页 结束-->
</div>
<script type="text/javascript">

// 搜索条件
$(document).ready(function(){
	$("#areaTypeSelect").change(function(){
		business.addMainContentParserHtml("qhdtJitiList.do",$("#searchForm").serialize());
	});
	$("#viewExcel").click(function(){
	  var path = WEBPATH+"/exportJitiExcel.do"; 
      window.location.href=path;
	});
});
//分页
$(document).ready(function(){
	var curentPage = eval('${electionUnitList.pageNum}');
	var totalPage = eval('${electionUnitList.pages}');
	var areaType = eval('${areaType}');
	if(totalPage>0){
		var options = {
			bootstrapMajorVersion: 3,
		    currentPage: curentPage,
		    totalPages: totalPage,
		    numberOfPages: 5,
		    itemTexts: function (type, page, current) {
		            switch (type) {
		            case "first":
		                return "首页";
		            case "prev":
		                return "&laquo;";
		            case "next":
		                return "&raquo;";
		            case "last":
		                return "尾页";
		            case "page":
		                return page;
		            }
		    },
		    onPageClicked: function (event, originalEvent, type, page) {
            	business.addMainContentParserHtml(WEBPATH+'/qhdtJitiList.do?pageNum='+page,$("#searchForm").serialize());
            }
    	};
    	$('#pageCon').bootstrapPaginator(options);
   	}
});
</script>
</body>
</html>
