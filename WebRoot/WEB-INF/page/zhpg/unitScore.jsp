<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
<style type="text/css">
	.pdfobject-container {
		width: 100%;
		height:580px;
		margin: 2em 0;
	}
	.textarea-success {
		border-top:green 1px solid;
		border-bottom:green 1px solid; 
		border-left:green 1px solid;
     	border-right:green 1px solid;
	}
	.textarea-error {
		border-top:red 1px solid;
		border-bottom:red 1px solid; 
		border-left:red 1px solid;
     	border-right:red 1px solid;
	}
</style>
</head>
<script type="text/javascript">
		//business.listenEnter();
		var unit =null;
		//var expertFileId = $("#expertFileId").val();
		$.ajax({
			cache : true,
			type : "GET",
			async : false,
			//api/test/detail/behaviour/74
			url: WEBPATH+"/zhpg/getIndexList.do",
			data:{
				id:'${id}'
			},
			error : function(request) {
				swal("错误!","请求异常！", "error");
			},
			success : function(data) {
			  	if(data.result =='success'){
			  		unit=data.data;
			  	}
			} 
		});
		
		if('${view}'=='1'){
			$("input").attr("disabled","disalbed");
			$("#submitBtn").hide();
		}
		
		var xzcfVue = new Vue({
			  el: '#ExpertVue',
			  data: {
				  unit:unit,
			  },
			  methods: {
				  updateItem:function (index,itemIndex){
					  var num = 0;
					  	var childNum =0;
					  	var itemScore = xzcfVue.unit.indexList[index].itemList[itemIndex].itemscore;
					  	var value = xzcfVue.unit.indexList[index].itemList[itemIndex].score;
					  	 if(value != '' && value != null){
					  		 if(parseFloat(value) <=parseFloat(itemScore) && parseFloat(value) >= 0){
					  		 		if(value.substring(value.length-1,value.length) =="."){
					  		 			$("#expertItem"+itemIndex+index).removeClass("has-success");	
										$("#expertItem"+itemIndex+index).addClass("has-error");	  
										xzcfVue.unit.indexList[index].itemList[itemIndex].validatorFlag= false;
										xzcfVue.unit.indexList[index].itemList[itemIndex].validatorMessage="分值项不能为空，且在0-"+itemScore+"分之间！";
								   		return false;
					  		 		}
						  		 	var re = /^-?\d+\.?\d{0,2}$/;
						    		if( re.test(value) ){   // 返回true
						    			$("#expertItem"+itemIndex+index).removeClass("has-error");	
										$("#expertItem"+itemIndex+index).addClass("has-success");	 
										xzcfVue.unit.indexList[index].itemList[itemIndex].validatorFlag= true;
										xzcfVue.unit.indexList[index].itemList[itemIndex].validatorMessage="";
						    		}else{
						    			$("#expertItem"+itemIndex+index).removeClass("has-success");	
										$("#expertItem"+itemIndex+index).addClass("has-error");	  
										xzcfVue.unit.indexList[index].itemList[itemIndex].validatorFlag= false;
										xzcfVue.unit.indexList[index].itemList[itemIndex].validatorMessage="分值项不能为空，且在0-"+itemScore+"分之间！";
								   		return false;
						    		}
								 	var score = xzcfVue.unit.indexList[index];
							  		if(score != null){
							  			for(var i=0;i<score.itemList.length;i++){
							  					  if(!isNaN(parseFloat(score.itemList[i].score))){
							  						childNum = (Math.round((childNum + parseFloat(score.itemList[i].score))*100))/100; 
							  					  }
							  		 		}
							  			}
							  		 if(xzcfVue.unit.indexList[index].isAdd ==1){
							  			//加分项
							  			 xzcfVue.unit.indexList[index].resultscore= childNum;
							  		 }else{
							  			 //减分项
							  			xzcfVue.unit.indexList[index].resultscore=
							  				Math.round((xzcfVue.unit.indexList[index].indexscore-childNum)*100)/100;
							  		 	if( xzcfVue.unit.indexList[index].resultscore<0){
							  		 	 	xzcfVue.unit.indexList[index].resultscore =0;
							  		 	}
							  		 }
							  		 //总分技算
							  		 var score1 = xzcfVue.unit.indexList;
									  if(score1 != null){
								  			for(var i=0;i<score1.length;i++){
								  					  if(!isNaN(parseFloat(score1[i].resultscore))){
								  						num = (Math.round((num + parseFloat(score1[i].resultscore))*100))/100; 
								  					  }
								  		 		}
								  	 }
							  		xzcfVue.unit.tczzscore =num;
					  		}else{
					  			$("#expertItem"+itemIndex+index).removeClass("has-success");	
								$("#expertItem"+itemIndex+index).addClass("has-error");	  
								xzcfVue.unit.indexList[index].itemList[itemIndex].validatorFlag= false;
								xzcfVue.unit.indexList[index].itemList[itemIndex].validatorMessage="分值项不能为空，且在0-"+itemScore+"分之间！";
						   		return false;
					  		}
					  	}else{
					  		$("#expertItem"+itemIndex+index).removeClass("has-success");	
							$("#expertItem"+itemIndex+index).addClass("has-error");	  
							xzcfVue.unit.indexList[index].itemList[itemIndex].validatorFlag= false;
							xzcfVue.unit.indexList[index].itemList[itemIndex].validatorMessage="分值项不能为空，且在0-"+itemScore+"分之间！";
					   		return false;
					  	}
				  },
				  saveSubmit:function(id){
						if(xzcfVue.unit!=null){
							var data = xzcfVue.unit.indexList;
							for(var i=0;i<data.length;i++){
								if(data[i].itemList ==null&&(data[i].validatorFlag == null || !data[i].validatorFlag) ){
									xzcfVue.unit.indexList[i].validatorFlag= false;
									xzcfVue.unit.indexList[i].validatorMessage="分值项不能为空，且在0-"+xzcfVue.unit.indexList[i].indexscore+"分之间！";
									$("#expertIndex"+i).removeClass("has-success");	
									$("#expertIndex"+i).addClass("has-error");	
									return false;
								}else{
									if(data[i].itemList != null){
										for(var j=0;j<data[i].itemList.length;j++){
											if(data[i].itemList[j].validatorFlag == null || !data[i].itemList[j].validatorFlag){
												xzcfVue.unit.indexList[i].itemList[j].validatorFlag= false;
												xzcfVue.unit.indexList[i].itemList[j].validatorMessage="分值项不能为空，且在0-"+xzcfVue.unit.indexList[i].itemList[j].itemscore+"分之间！";
												$("#expertItem"+xzcfVue.unit.indexList[i].itemList[j].temItemId+""+i).removeClass("has-success");	
												$("#expertItem"+xzcfVue.unit.indexList[i].itemList[j].temItemId+""+i).addClass("has-error");
												return false;
											}
										}
									}
								}
							}
							
							//loding('submitBtn', '信息保存');
							$("#submitBtn").attr("disabled","disabled");
					  		$("#submitBtn").text("提交中...");
							$.ajax({
							    type:"post",
							    url:WEBPATH+'/zhpg/saveZhpgScore.do',
							    data:{id:id,scoringIndex:JSON.stringify(xzcfVue.unit)},           //注意数据用{}
							    success:function(data){//成功
							    	if(data.result=="success"){
								    	swal({title: "保存成功",text: "",type:"success"});
								    	$("#submitBtn").attr("disabled","");
										$("#submitBtn").text("保存信息");
									  	business.addMainContentParserHtml('zhpg/main.do','pageNum=${pageNum}');
							          	return false;
							    	}else{
							    		$("#submitBtn").attr("disabled","");
								  		$("#submitBtn").text("保存信息");
							    		swal("提示", "信息保存操作失败了!", "error");
								        return false; 
							    	}
							    }
						  });
					}
				  }
			 }
		});
		function backURL(){
			var tczzpageNum=$("#tczzpageNum").val();
			business.addMainContentParserHtml(WEBPATH+'/pgfs/pgfs.do?pageNum='+pageNum,$("#tczzForm").serialize());
		}
</script>
<body>
<div id="ExpertVue">
<div class="center_weizhi">综合评估 - 表现突出组织 - 突出组织评审</div>
<div class="center">
<div class="center_list">
<c:if test="${not empty tczzpageNum}">
<!-- <a href="#" onClick="backURL()"><i class="fa fa-chevron-left"></i> 返回</a> -->
</c:if>
<input id="tczzpageNum" value="${tczzpageNum}" type="hidden"/>
        <table class="table table-bordered table-hover table-condensed">
         <thead>
           <tr>
             <td width="3%" height="30" bgcolor="#efefef">序号</td>
             <td width="7%" bgcolor="#efefef">评审指标</td>
             <td width="7%" bgcolor="#efefef">分值</td>
             <!-- <td  width="17%"  bgcolor="#efefef">指标说明</td> -->
             <td width="44%" bgcolor="#efefef"> <span style="float: left;">判断标准</span></td>
             <td width="5%" bgcolor="#efefef">初评得分</td>
           </tr>
         </thead>
         <tbody  class="form-group">
         	   <tr v-for="(scoringIndex, index) in unit.indexList">
	             <td height="30" align="center" >{{index+1}}</td>
	             
	             <!-- 评审指标 -->
	             <td >{{scoringIndex.indexname}}</td>
	             
	             <!-- 分值 -->
	             <td >{{scoringIndex.indexscore}}</td>
	             
	             <!-- 指标说明 -->
	             <!-- <td >{{scoringIndex.indexDesc}}</td> -->
	             
	             <!-- 判断标准 -->
	             <td v-if="scoringIndex.itemList != null && scoringIndex.itemList.length !=0 "  style="padding:0;">
                        <table width="100%" border="0" cellspacing="0" cellpadding="0" style="padding:0;">
                          <tr  v-for="(scoringItem, index1) in scoringIndex.itemList">   
                          	<td style="border-bottom:solid 1px #ddd; border-right:solid 1px #ddd;vertical-align:middle;">
                                 <div style="vertical-align:middle; margin:0 5px 5px 0;">
		            			{{scoringItem.itemname}}（{{scoringItem.itemscore}}分）
		            			</div>
                            </td>                      
                            <td style="border-bottom:solid 1px #ddd; width:155px;vertical-align:middle;">
                                 <div  :id="'expertItem'+index1+index" style="margin:0 0 5px 0; float:left;">
	                                 <div v-if="scoringIndex.isAdd==1" style="float:left; padding:5px 2px 7px 3px; color:red; font-size:20px;">＋</div>
	                                 <div v-if="scoringIndex.isAdd!=1" style="float:left; padding:0 0 3px 0; margin:0; color:red; font-size:26px;">－</div>
	                                 <div style="float:right; padding-top:4px;">
	                                 	<input name="scoreInput" @input="updateItem(index,index1)"  
	                                       v-model="scoringItem.score" type="text" :class="'checkitem'+index" autocomplete="off"
	                                      class="form-control inputDisabled"   placeholder="请输入初评得分" style="width:125px;">
	                                 </div>
                                 	<div style="color:red; font-size: 12px;">{{scoringItem.validatorMessage}}</div>
                                 </div>
                            </td>                          
                          </tr>
                        </table>
		             </td>
		            <td v-else >
		             <table width="100%" border="0" cellspacing="0" cellpadding="0">
		             	<tr>
		             		<td style="border-bottom:solid 1px #f7f7f7;">
                                 <div style="vertical-align:middle; margin:0 5px 5px 0;">
		            				{{scoringIndex.indexdesc}}
		            			</div>
                            </td>    
                            <td style="border-bottom:solid 1px #f7f7f7;">
                            	<div :id="'expertIndex'+index"  class="form-group" style="width: 150px;float: right;">
	                            	<div v-if="scoringIndex.isAdd==1" style="float:left; padding:5px 2px 7px 3px; color:red; font-size:20px;">+</div>
		                            <div v-if="scoringIndex.isAdd!=1" style="float:left; padding:0 0 3px 0; margin:0; color:red; font-size:26px;">-</div>
				                		<input name="scoreInput" type="text" v-model="scoringIndex.score" autocomplete="off"
				                		 @input="updateIndex(index,scoringIndex.indexscore)" :class="'checkitem'+index"
				               	  		 class="form-control inputDisabled"  placeholder="请输入初评得分">
			         			  	<div style="color:red;font-size: 5px;">{{scoringIndex.validatorMessage}}</div>
			         			 </div>
                            </td>
		             	</tr>
		             </table>
		         </td>
		         
	           	 <!-- 初评得分 -->
	             <td >
	             	<div style="width:120px;float: right;">
	           			<input type="text"  disabled="disabled" v-model="scoringIndex.resultscore" class="form-control">
	           		</div>
	             </td>
	           </tr>
	           <tr>
	           		<td></td>
	           		<td>合计</td>
	           		<td>70</td>
	           		<td></td>
	           		<td>
		           		<div style="width:120px;float: right;">
		           			<input type="text"  disabled="disabled" v-model="unit.tczzscore" class="form-control">
		           		</div>
	           		</td>
	           		
	           </tr>
          	</tbody>
       </table>
       <div>
 	<a style="float: right;" href="#"><button class="btn btn-primary" id="submitBtn" v-on:click="saveSubmit(unit.id)" type="button" style="font-size:16px;width:150px; margin-top:5px;">信息保存</button></a>	

</div>
</div>
</div>
</body>
</html>