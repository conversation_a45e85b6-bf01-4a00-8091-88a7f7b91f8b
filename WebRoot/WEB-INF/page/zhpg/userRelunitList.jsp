<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<html>
<head>
<script type="text/javascript">
	//分页
	$(document).ready(function(){
		var curentPage = eval('${pageBean.pageNum}');
		var totalPage = eval('${pageBean.pages}');
		if(totalPage>0){
			var options = {
				bootstrapMajorVersion: 3,
			    currentPage: curentPage,
			    totalPages: totalPage,
			    numberOfPages: 5,
			    itemTexts: function (type, page, current) {
			            switch (type) {
			            case "first":
			                return "首页";
			            case "prev":
			                return "&laquo;";
			            case "next":
			                return "&raquo;";
			            case "last":
			                return "尾页";
			            case "page":
			                return page;
			            }
			    },
			    onPageClicked: function (event, originalEvent, type, page) {
	            	business.addMainContentParserHtml(WEBPATH+'/jcpf/jcpfList.do?pageNum='+page,$("#searchForm").serialize());
	            }
	    	};
	    	$('#pageCon').bootstrapPaginator(options);
    	}
	});

</script>
</head>
<body>
<div class="center_weizhi">当前位置：综合评估 - 表现突出组织 - 突出组织评审</div>
<div class="center">
<div class="center_list">
    <div class="form-group">
		<!-- <select class="form-control" style="width:150px;margin-bottom: 5px;">
			<option>请选择评分状态</option>
			<option>已评</option>
			<option>未评</option>
		</select> -->
	</div>           
    </div>
    <table class="table table-bordered table-hover table-condensed">
         <thead>
           <tr>
             <td width="50" height="30" bgcolor="#efefef">序号</td>
             <td bgcolor="#efefef">省</td>
             <td width="270" bgcolor="#efefef">评分人员姓名</td>
             <td width="100" bgcolor="#efefef">初评得分</td>
             <td width="60" bgcolor="#efefef">状态</td>
             <td width="100" bgcolor="#efefef">操作</td>
           </tr>
         </thead>
         <tbody>
         	<c:forEach  varStatus="index"  items="${list}" var="obj">
         		<tr>
	             <td>${index.index+1 }</td>
	             <td>${obj.areaName }</td>
	             <td>${obj.name }</td>
	             <td>${obj.tczzscore }</td>
	             <td>
	             	<c:if test="${obj.state==0}">
	             		<button class="btn btn-warning btn-xs" data-toggle="modal" data-target="#myModal">未评</button>
	             	</c:if>
	             	<c:if test="${obj.state==1}">
	             		<button class="btn btn-success btn-xs" data-toggle="modal" data-target="#myModal">已评</button>
	             	</c:if>
	             </td>
	             <td>
	             	<button onclick="macroMgr.onLevelTwoMenuClick(null, 'zhpg/unitScore.do?id=${obj.id }&view=1' )" class="btn btn-info btn-xs" data-toggle="modal" data-target="#myModal">查看</button> 
	             	<c:if test="${obj.isconsider==0}">
	             		<button onclick="macroMgr.onLevelTwoMenuClick(null, 'zhpg/unitScore.do?id=${obj.id }&view=0' )" class="btn btn-primary btn-xs" data-toggle="modal" data-target="#myModal">评分</button>
	             	</c:if>
	             </td>
           		</tr>
         	</c:forEach>
           
         </tbody>
       </table>
    </div>
<script type="text/javascript">

</script>
</body>
</html>
