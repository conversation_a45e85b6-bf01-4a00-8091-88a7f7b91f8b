<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
<script type="text/javascript">
	//分页
	$(document).ready(function(){
		business.listenEnter("searchButt");
		var curentPage = eval('${Unit_SLPFList.pageNum}');
		var totalPage = eval('${Unit_SLPFList.pages}');
		if(totalPage>0){
			var options = {
				bootstrapMajorVersion: 3,
			    currentPage: curentPage,
			    totalPages: totalPage,
			    numberOfPages: 5,
			    itemTexts: function (type, page, current) {
			            switch (type) {
			            case "first":
			                return "首页";
			            case "prev":
			                return "&laquo;";
			            case "next":
			                return "&raquo;";
			            case "last":
			                return "尾页";
			            case "page":
			                return page;
			            }
			    },
			    onPageClicked: function (event, originalEvent, type, page) {
	            	business.addMainContentParserHtml(WEBPATH+'/slpf/cityGroup.do?pageNum='+page,$("#searchForm").serialize());
	            }
	    	};
	    	$('#pageCon').bootstrapPaginator(options);
    	}
	});
	
	$(document).ready(function(){
		$("#province,#city").change(function(){
			business.addMainContentParserHtml("slpf/cityGroup.do",$("#searchForm").serialize());
		});
		
	});
	
	$(document).ready(function() {
		var areacode="${areaCode }";
		if(typeof(areacode)!="undefined"&&areacode!=""&&areacode!=null){
			business.cascadedDLB(areacode,'city','2');
		}
		
	});
	// 导出表格
	$(document).ready(function(){
		$("#viewExcel").click(function(){
		  var path = WEBPATH+"/slpf-export.do?viewType=2"; 
	      window.location.href=path;
		});
	});
</script>
</head>

<body>
<div class="center_weizhi">当前位置：数量评分 - 先进集体评选 - 市级数量评分</div>
<div class="center">
<div class="center_list">
    <div class="panel-group" id="accordion">
          <!---快速查询--->
          <form id="searchForm" name="searchForm" role="form">
              <select id="province" name="province"  class="form-control" style="width:120px;margin-right:5px;" onchange="business.cascadedDLB($('#province').val(),'city','2');">
                  <option value="" <c:if test="${areaCode=='' }">selected</c:if>>省（直辖市）</option>
	              <c:forEach items="${electionUnits }" var="electionUnit">
              	  	<option value="${fn:substring(electionUnit.areacode,0,2) }000000" <c:if test="${fn:substring(areaCode,0,2)==fn:substring(electionUnit.areacode,0,2) }">selected</c:if> >${electionUnit.province }</option>
	              </c:forEach>
              </select>
              
              <select id="city" name="city" class="form-control" style="width:120px;margin-right:5px;">
                 <option value="">地（市）</option>
              </select>
              
        
          
            <!---搜索--->
            <div style="width:260px;" class="btn-group">
                   <button type="button" class="btn btn-primary" id ="viewExcel" >导出EXCEL</button> 
            </div>            
          </form>
    </div>
        <table class="table table-bordered table-hover table-condensed">
         <thead>
           <tr>
             <td width="50" rowspan="2" bgcolor="#efefef">序号</td>
             <td width="200" rowspan="2" bgcolor="#efefef">省</td>
             <td width="200" rowspan="2" bgcolor="#efefef">地市</td>
             <td height="30" colspan="3" bgcolor="#efefef">案件数量</td>
             <td colspan="3" bgcolor="#efefef">罚款金额</td>
             <td colspan="3" bgcolor="#efefef">重大案件数量</td>
             <td width="60" rowspan="2" bgcolor="#efefef">总分</td>
             <td width="60" rowspan="2" bgcolor="#efefef">排名</td>
           </tr>
           <tr>
             <td width="80" height="30" bgcolor="#efefef">本单位基准值</td>
             <td width="160" bgcolor="#efefef">本区域2018年全年查处的环境违法案件数</td>
             <td width="60" bgcolor="#efefef">分值</td>
             <td width="80" bgcolor="#efefef">本单位基准值</td>
             <td width="130" bgcolor="#efefef">本区域2018年全年罚款金额</td>
             <td width="60" bgcolor="#efefef">分值</td>
             <td width="80" bgcolor="#efefef">本单位基准值</td>
             <td width="130" bgcolor="#efefef">本区域2018年全年重大案件数量</td>
             <td width="60" bgcolor="#efefef">分值</td>
           </tr>
         </thead>
         <tbody>
           <c:forEach items="${Unit_SLPFList.list}" var="ElectionUnit_SLPF" varStatus="i">
           	<tr>
             <td height="30" align="center">${i.index+1}</td>
             <td width="200">${ElectionUnit_SLPF.province }</td>
             <td width="200">${ElectionUnit_SLPF.areaname }</td>
             <td width="80">${ElectionUnit_SLPF.unitcasenumbasevalue }</td>
             <td>${ElectionUnit_SLPF.illegalcasenum }</td>
             <td>${ElectionUnit_SLPF.illegalcasenumscore }</td>
             <td width="80">${ElectionUnit_SLPF.unitfinetotalbasevalue }</td>
             <td>${ElectionUnit_SLPF.fineamount }</td>
             <td>${ElectionUnit_SLPF.fineamountscore }</td>
             <td width="80">${ElectionUnit_SLPF.unitmajorcasebasevalue }</td>
             <td>${ElectionUnit_SLPF.majorcasenum }</td>
             <td>${ElectionUnit_SLPF.majorcasenumscore }</td>
             <td width="60">${ElectionUnit_SLPF.numberfinalscore }</td>
             <td width="60">
             <c:choose>
             	<c:when test="${ElectionUnit_SLPF.numberranking !='' && ElectionUnit_SLPF.numberranking !=null }">
             		第${ElectionUnit_SLPF.numberranking }名
             	</c:when>
             	<c:otherwise>
             	</c:otherwise>
             </c:choose>
             </td>
           </tr>
           </c:forEach>
         </tbody>
       </table>
    </div>
    <div class="page">
        <span style="padding:12px; float:left; color:#0099cc;">共${Unit_SLPFList.total}条记录</span>
        <ul class="pagination" id="pageCon">
            
        </ul>
    </div>
</div>
<script language="JavaScript">

$(document).ready(function() {
	$(".topnav").accordion({
		accordion:false,
		speed: 500,
		closedSign: '[+]',
		openedSign: '[-]'
	});
});

</script>
</body>
</html>
