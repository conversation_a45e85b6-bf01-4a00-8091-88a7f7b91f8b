<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
<script type="text/javascript">
	//分页
	$(document).ready(function(){
		business.listenEnter("searchButt");
		var curentPage = eval('${Personal_SLPFList.pageNum}');
		var totalPage = eval('${Personal_SLPFList.pages}');
		if(totalPage>0){
			var options = {
				bootstrapMajorVersion: 3,
			    currentPage: curentPage,
			    totalPages: totalPage,
			    numberOfPages: 5,
			    itemTexts: function (type, page, current) {
			            switch (type) {
			            case "first":
			                return "首页";
			            case "prev":
			                return "&laquo;";
			            case "next":
			                return "&raquo;";
			            case "last":
			                return "尾页";
			            case "page":
			                return page;
			            }
			    },
			    onPageClicked: function (event, originalEvent, type, page) {
	            	business.addMainContentParserHtml(WEBPATH+'/slpf/provincePerson.do?pageNum='+page,$("#searchForm").serialize());
	            }
	    	};
	    	$('#pageCon').bootstrapPaginator(options);
    	}
	});
	
	$(document).ready(function(){
		$("#province").change(function(){
			business.addMainContentParserHtml("slpf/provincePerson.do",$("#searchForm").serialize());
		});
	});
	
	// 导出表格
	$(document).ready(function(){
		$("#viewExcel").click(function(){
		  var path = WEBPATH+"/ajpfViewExcel.do?target=7&viewType=1"; 
	      window.location.href=path;
		});
	});
</script>
</head>
<body>
<div class="center_weizhi">当前位置：数量评分 - 先进个人评选 - 省级数量评分</div>
<div class="center">
<div class="center_list">
    <div class="panel-group" id="accordion">
          <!---快速查询--->
          <form id="searchForm" name="searchForm" role="form">
              <select id="province" name="province"  class="form-control" style="width:120px;margin-right:5px;">
                  <option value="" <c:if test="${areaCode=='' }">selected</c:if>>省（直辖市）</option>
	              <c:forEach items="${electionPersonAreas }" var="electionPersonArea">
	              <option value="${electionPersonArea.areacode }" <c:if test="${areaCode==electionPersonArea.areacode }">selected</c:if> >${electionPersonArea.areaname }</option>
	              </c:forEach>
              </select>

            <!---搜索--->
            <div style="width:260px;" class="btn-group">
                 <button type="button" class="btn btn-primary" id ="viewExcel" >导出EXCEL</button> 
            </div>            
          </form>
    </div>
    <table class="table table-bordered table-hover table-condensed">
      <thead>
        <tr>
          <td width="50" bgcolor="#efefef">序号</td>
          <td bgcolor="#efefef">省</td>
          <td width="100" height="30" bgcolor="#efefef">候选人姓名</td>
          <td width="150" bgcolor="#efefef">身份证号码</td>
          <td width="160" bgcolor="#efefef">参与调查处理案件数量</td>
          <td width="80" bgcolor="#efefef">得分</td>
        </tr>
      </thead>
      <tbody>
        <c:forEach items="${Personal_SLPFList.list}" var="ElectionPersonal_SLPF" varStatus="i">
           	<tr>
             <td height="30" align="center">${i.index+1}</td>
             <td>${ElectionPersonal_SLPF.province }</td>
             <td>${ElectionPersonal_SLPF.name }</td>
             <td>${ElectionPersonal_SLPF.cardid }</td>
             <td>${ElectionPersonal_SLPF.handlcasenum }</td>
             <td>${ElectionPersonal_SLPF.perfilenubersscore }</td>
           </tr>
           </c:forEach>
      </tbody>
    </table>
</div>
    <div class="page">
        <span style="padding:12px; float:left; color:#0099cc;">共${Personal_SLPFList.total}条记录</span>
        <ul class="pagination" id="pageCon">
            
        </ul>
    </div>
</div>
<script language="JavaScript">
$(document).ready(function() {
	$(".topnav").accordion({
		accordion:false,
		speed: 500,
		closedSign: '[+]',
		openedSign: '[-]'
	});
});

</script>
</body>
</html>
