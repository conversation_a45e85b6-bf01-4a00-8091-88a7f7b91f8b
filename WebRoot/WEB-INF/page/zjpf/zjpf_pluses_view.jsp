<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
<style type="text/css">
	.pdfobject-container {
		width: 100%;
		height:580px;
		margin: 2em 0;
	}
	.textarea-success {
		border-top:green 1px solid;
		border-bottom:green 1px solid;
		border-left:green 1px solid;
     	border-right:green 1px solid;
	}
	.textarea-error {
		border-top:red 1px solid;
		border-bottom:red 1px solid;
		border-left:red 1px solid;
     	border-right:red 1px solid;
	}
	.center_list{background-color: #ffffff;padding:15px 0px 0px 5px}
	.center{background: #FFFFFF; position: absolute;left: 0px;top: 237px; width: 98%;margin: 0 20px;bottom: 35px;right: 5px; overflow-y: auto;}
	.table>thead>tr>td{line-height: 3.1}
	.table-bordered>thead>tr>td{background-color: #F5F7FA;border:1px solid #EBEEF5;text-align: center}
	input[type=radio]{ margin-right: 5px;margin-left: 10px;}
	input[type=checkbox], input[type=radio]{height:16px;width:16px;position: relative;top:3px;right:2px;}
</style>
</head>
<script type="text/javascript">
	$(".left_menu").hide();
		business.listenEnter();
		var pageNum =$("#pageNum").val();
		var scoringPlusesList =null;
		var expertFileId = $("#expertFileId").val();
		$.ajax({
			cache : true,
			type : "GET",
			async : false,
			url: WEBPATH+"/zjpf/getPlusesData.do",
			data:{
				id:expertFileId
			},
			error : function(request) {
				swal("错误!","请求异常！", "error");
			},
			success : function(data) {
			  	if(data.result =='success'){
			  		scoringPlusesList = data.data;
			  	}else{
			  		swal("错误!",data.message, "error");
			  	}
			}
		});

	var myVue = new Vue({
		el: '#ExpertVue',
		data: {
			scoringPlusesList: scoringPlusesList,
			plusesSelectedNum: 0,
			plusesTotalScore: 0
		},
		computed: {
			//加分项-选择数量
			plusesSelectedNum: function () {
				let count = 0;
				if (this.scoringPlusesList && this.scoringPlusesList.expertHandlIndexScoreList) {
					this.scoringPlusesList.expertHandlIndexScoreList.forEach(function (item) {
						if (item.expertHandlItemScoreList && item.expertHandlItemScoreList.length > 0) {
							item.expertHandlItemScoreList.forEach(function (v) {
								if (v.score == "1") {
									count++;
								}
							})
						}
					});
				}
				return count;
			},
			//加分项-总分数
			plusesTotalScore: function () {
				let totalScore = 0;
				if (this.scoringPlusesList && this.scoringPlusesList.expertHandlIndexScoreList) {
					// 遍历每个大项
					this.scoringPlusesList.expertHandlIndexScoreList.forEach(function (item, index) {
						let majorItemScore = 0; // 当前大项得分
						if (item.expertHandlItemScoreList && item.expertHandlItemScoreList.length > 0) {
							// 计算当前大项下所有选择"符合"的小项数量
							item.expertHandlItemScoreList.forEach(function (v) {
								if (v.score == "1") {
									majorItemScore++;
								}
							});
						}
						// 每个大项最多5分
						majorItemScore = Math.min(majorItemScore, 5);
						totalScore += majorItemScore;
					});
				}
				// 总分最高10分限制
				totalScore = Math.min(totalScore, 10);
				return totalScore;
			}
		},
		methods: {
			// 计算序号
			getItemNumber: function(indexIndex, itemIndex) {
				let number = 1;
				if (this.scoringPlusesList && this.scoringPlusesList.expertHandlIndexScoreList) {
					for (let i = 0; i < indexIndex; i++) {
						if (this.scoringPlusesList.expertHandlIndexScoreList[i].expertHandlItemScoreList) {
							number += this.scoringPlusesList.expertHandlIndexScoreList[i].expertHandlItemScoreList.length;
						}
					}
					number += itemIndex;
				}
				return number;
			}
		}
	});
</script>
<body>
	<div id="ExpertVue">
		<div class="center_weizhi">当前位置：专家评分 - 专家评审案卷 - ${filetypeName} - 加分项查看</div>
		<div style="position: absolute;top: 131px;width: 98%;margin: 0 20px;padding: 5px 0 0 19px;background: #ffffff;">
<%--			<button class="btn btn-default" style="padding:9px 34px;margin-right:14px" onclick="macroMgr.onLevelTwoMenuClick(null, 'zjpf/zjpfScoreView.do?id=${expertFileId}&pageNum=${pageNum}')">规范性评查</button>--%>
			<button class="btn btn-default" style="padding:9px 34px;margin-right:14px" onclick="macroMgr.onLevelTwoMenuClick(null, 'zjpf/entityAView.do?id=${expertFileId}&pageNum=${pageNum}')">合法性评查</button>
			<button class="btn btn-danger" style="padding:9px 34px;margin-right:14px">加分项</button>
			<%--专家--%>
			<c:if test ="${userTypeCode != '4' }">
				<button class="btn btn-primary"  onclick="macroMgr.onLevelOneMenuClick(null, 'zjpf.do')"  style="font-size:16px;width:98px;height: 40px; padding: 6px 34px; margin-right: 10px;float: right;" >返回</button>
			</c:if>
			<%--专家委员--%>
			<c:if test ="${userTypeCode == '4' }">
				<button class="btn btn-primary"  onclick="macroMgr.onLevelOneMenuClick(null, 'zjpf/zjpfCommitList.do')"  style="font-size:16px;width:98px;height: 40px; padding: 6px 34px; margin-right: 10px;float: right;" >返回</button>
			</c:if>
		</div>
		<div class="center" id="scrollBarCenter">
			<div class="center_list">
				<div style="margin-left:19px;padding-top:3px;font-weight: 400; font-size: 14px;color: #333333">
<%--				<p style="margin-top:21px;font-weight: 600; font-size: 16px">评审说明：</p>--%>
<%--					<p>共选择数：<span style="font-weight: bold; color: #009CCD;">{{ plusesSelectedNum }}</span>项，加分分数：<span style="font-weight: bold; color: #009CCD;">{{ plusesTotalScore }}</span>分，案卷总得分：<span style="font-weight: bold; color: #009CCD;">{{ scoringPlusesList.paperscore }}</span>分</p>--%>
				<div style="color:#333333;font-size:14px; font-weight:400;">案卷或材料：<span style="color:#009CCD;font-size:14px; font-weight:400;" id ="filecode">
					{{scoringPlusesList.filecode }}
					<span style="color:red;font-size:14px; font-weight:400;"><c:choose><c:when test="${expertHandlFileList.closed==1}">（已结案）</c:when><c:when test="${expertHandlFileList.closed==0}">（未结案）</c:when> </c:choose></span>
					</span>
					<c:if test ="${sessionScope.sa_session.sysStatus == '5' }">
						<button id="download" class="btn btn-success btn-xs"  style="padding:5px 12px;margin-left:10px" onclick="xiaZaiAnJuan()">下载</button>
						<c:if test="${expertHandlFileList.suffix== 'pdf'}">
							<button id="look" class="btn btn-info btn-xs" style="padding:5px 12px;margin-left:10px" data-toggle="modal" data-remote="${webpath}/jcpf/showFileModal.do?fastDFSUrl=+'${fastDFS}'+" data-target="#view">预览</button>
						</c:if>
					</c:if>
				</div>
			</div>

			<form action="#" id ="zjpfForm" method="post" style="padding: 19px">
				<table class="table table-bordered table-hover table-condensed" v-if="scoringPlusesList && scoringPlusesList.expertHandlIndexScoreList">
					<thead>
						<tr>
							<td width="3%" height="30" bgcolor="#efefef">序号</td>
							<td width="7%" bgcolor="#efefef">评查项目</td>
							<td bgcolor="#efefef">
								<table width="100%" border="0" cellspacing="0" cellpadding="0" style="padding:0;">
									<tr>
										<td style="text-align: center">加分项评分标准</td>
										<td style="width:15%; padding:0px 5px 0px 5px;text-align: center;">符合情况</td>
										<td style="width:30%;padding:0px 5px 0px 5px;text-align: center;">具体说明</td>
									</tr>
								</table>
							</td>
						</tr>
					</thead>
					<tbody class="form-group">
						<template v-for="(indexScore, indexIndex) in scoringPlusesList.expertHandlIndexScoreList">
							<tr v-for="(itemScore, itemIndex) in indexScore.expertHandlItemScoreList" :key="indexIndex + '_' + itemIndex">
								<td height="30" align="center">{{ getItemNumber(indexIndex, itemIndex) }}</td>
								<td v-if="itemIndex==0" :rowspan="indexScore.expertHandlItemScoreList.length" align="center">{{ indexScore.indexname }}</td>
								<td style="padding:5px;">
									<table width="100%" border="0" cellspacing="0" cellpadding="0" style="padding:0;">
										<tr>
											<td style="padding:5px; vertical-align:middle;">{{ itemScore.itemname }}</td>
											<td style="width:15%; padding:0px 5px 0px 5px; vertical-align:middle; text-align: center;">
												<div style="display: flex; justify-content: center;">
													<input type="radio" value="1" :name="'scoreRadio'+indexIndex+'_'+itemIndex"
														   :checked="itemScore.score == '1'" disabled>符合
													<input type="radio" value="0" :name="'scoreRadio'+indexIndex+'_'+itemIndex" style="margin-left: 20px;"
														   :checked="itemScore.score == '0'" disabled>不符合
												</div>
											</td>
											<td style="width:30%; padding:0px 5px 0px 5px; vertical-align:middle;">
												<textarea readonly class="form-control" rows="4"
														  style="width: 100%; height: 100px; resize: none; background-color: #f5f5f5; border: 1px solid #ddd;"
												>{{ itemScore.commeStr }}</textarea>
											</td>
										</tr>
									</table>
								</td>
							</tr>
						</template>
					</tbody>
				</table>

				<div v-else style="text-align: center; padding: 50px;">
					<p>暂无加分项评分数据</p>
				</div>
				<%--是否优秀案卷回显--%>
				<div v-if="scoringPlusesList" style="margin-top: 20px; border-top: 1px solid #ddd; padding-top: 15px;">
					<div class="form-group">
						<label class="control-label">是否为优秀案卷：</label>
						<div>
							<input type="radio" disabled :checked="scoringPlusesList.ajpjYxdxanlituijianCode == '1'" name="ajpjYxdxanlituijianCode"> 是
							<input type="radio" disabled :checked="scoringPlusesList.ajpjYxdxanlituijianCode == '0' || !scoringPlusesList.ajpjYxdxanlituijianCode" name="ajpjYxdxanlituijianCode" style="margin-left: 20px;"> 否
						</div>
					</div>
					<div class="form-group" v-if="scoringPlusesList.ajpjYxdxanlituijianCode == '1'">
						<label class="control-label">优秀案卷理由：</label>
						<textarea readonly class="form-control" rows="4" style="width: 100%; resize: none; background-color: #f5f5f5; border: 1px solid #ddd;">{{ scoringPlusesList.problemRemark }}</textarea>
					</div>
				</div>
				
			</form>
		</div>
	</div>
	</div>

	<input type="hidden" id="pageNum" value="${pageNum}"/>
	<input type="hidden" id="expertFileId" value="${expertFileId}"/>

		<!-- 预览模态框 -->
		<div class="modal fade" id="view" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
			<div class="modal-dialog" style="width: 80%;">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
						<h4 class="modal-title" id="myModalLabel">案卷预览</h4>
					</div>
					<div class="modal-body">
						<!-- 预览内容将在这里加载 -->
					</div>
				</div>
			</div>
		</div>

		<script type="text/javascript">
			// 下载案卷函数
			function xiaZaiAnJuan() {
				var downUrl = app.scoringPlusesList.downUrl;
				if (downUrl) {
					// 检查文件是否存在
					$.ajax({
						url: "${webpath}/xzcfExistFileUrl.do",
						type: "POST",
						data: {
							"downUrl": downUrl
						},
						success: function(data) {
							if (data.result == "success") {
								// 文件存在，执行下载
								window.open("${webpath}/zjpf/zjpfAnJuandown.do?downUrl=" + encodeURIComponent(downUrl));
							} else {
								alert("文件不存在或已被删除！");
							}
						},
						error: function() {
							alert("检查文件存在性时发生错误！");
						}
					});
				} else {
					alert("下载链接不存在！");
				}
			}
		</script>
</body>
</html>
