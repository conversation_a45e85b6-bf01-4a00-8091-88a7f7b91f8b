<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Insert title here</title>
</head>
<body>
<div class="center_weizhi">当前位置：专家评分 - 委员评审案卷 - 委员评审列表</div>
<div class="center">
<div class="center_list">
    <div class="panel-group" id="accordion">
          <h4 class="panel-title">
            <!---信息填报
            <div class="btn-group" style="margin-right:20px;float:left;">
               <a href="CementEnterprises_input.html"><button type="button" class="btn btn-success" style="font-size:16px;">企业信息录入</button></a>
            </div>--->          
            
          
          <form role="form">
              <select class="form-control" style="width:150px;margin-right:5px;" id="scoredState">
                 <option value=''>请选择评分状态</option>
                 <option value = '1'>已评</option>
                 <option value = '0'>未评</option>
              </select>
              
        
        </form>
          
            <!---搜索--->
            <div style="width:260px;" class="btn-group">
               <form class="bs-example bs-example-form" role="form">
                  <div class="row">                     
                     <div class="col-lg-6">
                        <div class="input-group">
                           <input type="text" class="form-control" id="fileCode" style="width:200px;" placeholder="请输入案卷文号">
                           <span class="input-group-btn">
                              <button class="btn btn-success" type="button" onclick="search();" id="searchID">
                                 快速搜索
                              </button>
                           </span>
                        </div>
                     </div>
                  </div>
               </form>
            </div>            
          </h4>
    </div>
        <table class="table table-bordered table-hover table-condensed">
         <thead>
           <tr>
             <td width="50" height="30" bgcolor="#efefef">序号</td>
             <td bgcolor="#efefef">案卷文号</td>
             <td width="150" bgcolor="#efefef">专家姓名</td>
             <td width="150" bgcolor="#efefef">初评得分</td>
             <td width="150" bgcolor="#efefef">专家姓名</td>
             <td width="150" bgcolor="#efefef">初评得分</td>
             <td width="150" bgcolor="#efefef">委员姓名</td>
             <td width="150" bgcolor="#efefef">初评得分</td>
             <td width="60" bgcolor="#efefef">状态</td>
             <td width="100" bgcolor="#efefef">操作</td>
           </tr>
         </thead>
         <tbody>
          <!-- function linkExpert(fileType,fileId,expertId,fileCodeCurrent,pageNum) -->
           <c:forEach var="list" items="${pageBean.list}" varStatus="index">
           <tr>
           		<td>${index.index+1}</td>
           		<td>${list.fileCode}</td>
           		<td>${list.expertAName}</td>
           		<td onclick="checkExpertFile('${list.expertAID}','${list.fID}')">${list.expertAScore}</td>
           		<td>${list.expertBName}</td>
           		<td onclick="checkExpertFile('${list.expertBID}','${list.fID}')">${list.expertBScore}</td>
           		<td>${list.expertName}</td>
           		<td>${list.expertFinalScore}</td>
           		
           		
           		<%-- <c:if test="${list.considerState == '0' }">
	           		<c:choose>
	           			<c:when test="${list.scoredState == '1' }">
	           			<td><button class="btn btn-success btn-xs" data-toggle="modal" data-target="#myModal">已评</button></td>
	           			<td><a ><button onclick="saveScore('${list.eID}',1)" class="btn btn-primary btn-xs" data-toggle="modal" data-target="#myModal">查看</button></a>
	           			    <a ><button onclick="saveScore('${list.eID}','')" class="btn btn-primary btn-xs" data-toggle="modal" data-target="#myModal">评分</button></a></td>
	           		</c:when>
           			<c:otherwise>
           			 	<td><button class="btn btn-warning btn-xs" data-toggle="modal" data-target="#myModal">未评</button></td>
           			 	<td><a ><button onclick="saveScore('${list.eID}',1)" class="btn btn-primary btn-xs" data-toggle="modal" data-target="#myModal">查看</button></a>
           				    <a ><button onclick="saveScore('${list.eID}','')" class="btn btn-primary btn-xs" data-toggle="modal" data-target="#myModal">评分</button></a></td>
	           		</c:otherwise>
	           		</c:choose>
           		</c:if>
           		<c:if test="${list.considerState == '1' }">
	           		<td><button class="btn btn-success btn-xs" data-toggle="modal" data-target="#myModal">已评</button></td>
	           		<td><button onclick="checkFile('${list.eID}')" class="btn btn-info btn-xs" data-toggle="modal" data-target="#myModal">查看</button></td>
           		</c:if> --%>
           		
           		<c:choose>
           			<c:when test="${list.scoredState == '1' }">
           			<td><button class="btn btn-success btn-xs" data-toggle="modal" data-target="#myModal">已评</button></td>
           			<td><a ><button onclick="saveScore('${list.eID}',1)" class="btn btn-primary btn-xs" >查看</button></a>
           			    <a ><button onclick="saveScore('${list.eID}','')" class="btn btn-primary btn-xs" >评分</button></a></td>
           		</c:when>
          			<c:otherwise>
          			 	<td><button class="btn btn-warning btn-xs" data-toggle="modal" data-target="#myModal">未评</button></td>
          			 	<td><a ><button onclick="saveScore('${list.eID}',1)" class="btn btn-primary btn-xs" >查看</button></a>
          				    <a ><button onclick="saveScore('${list.eID}','')" class="btn btn-primary btn-xs" >评分</button></a></td>
           		</c:otherwise>
           		</c:choose>
           		
           </tr>
           </c:forEach>
           
         </tbody>
       </table>
    </div>
     <div class="page">
    <span style="padding:12px; float:left; color:#0099cc;">共${pageBean.total}条记录</span>
        <ul class="pagination" id="pageCon">
        </ul>
    </div>
</div>
</body>
<script type="text/javascript">
var curentPage = eval('${pageBean.pageNum}');
var totalPage = eval('${pageBean.pages}');
var fileCode = '${fileCode}';
var scoredState = '${scoredState}';
$(document).ready(function(){
	if(fileCode != ''){
		 $("#fileCode").val(fileCode);
	}
	
	if(scoredState != ''){
		$("#scoredState").val(scoredState);
	}
	console.log("......"+scoredState);
	if(totalPage>0){
		var options = {
			bootstrapMajorVersion: 3,
		    currentPage: curentPage,
		    totalPages: totalPage,
		    numberOfPages: 5,
		    itemTexts: function (type, page, current) {
		            switch (type) {
		            case "first":
		                return "首页";
		            case "prev":
		                return "&laquo;";
		            case "next":
		                return "&raquo;";
		            case "last":
		                return "尾页";
		            case "page":
		                return page;
		            }
		    },
		    onPageClicked: function (event, originalEvent, type, page) {
		    	var scoreState = $("#scoreState").val();
		    	var fileCode = $("#fileCode").val();
            	business.addMainContentParserHtml(WEBPATH+'/zjpf/committeeRating.do?pageNum='+page+'&fileCode='+fileCode);
            }
    	};
    	$('#pageCon').bootstrapPaginator(options);
	}
	
	
});
function saveScore(id,status){
	business.addMainContentParserHtml(WEBPATH+'/zjpf/zjpfCommiteeScore.do?consider=1&pageNum='+curentPage+'&id='+id+'&status='+status);
};

function checkFile(id){
	business.addMainContentParserHtml(WEBPATH+'/zjpf/zjpfCommiteeScore.do?status=1&id='+id);
}
function search(){

	var scoredState = $("#scoredState").val();
	var fileCode = $("#fileCode").val();
	
	 macroMgr.onLevelTwoMenuClick(null, '/zjpf/committeeRating.do?scoredState='+scoredState+'&fileCode='+fileCode+'&pageNum='+curentPage);
}	
function checkExpertFile(expertID,fileID){
	if(expertID==null||expertID == ''){
		return ;
	}
	$.ajax({
		data:{expertID:expertID,fileID:fileID},
		dataType:"JSON",
		Type:"post",
		url:WEBPATH+'/zjpf/getExpertHanderID.do',
		success:function(data){
			if(data.id !=null && data.id!=''){
				business.addMainContentParserHtml(WEBPATH+'/zjpf/zjpfScore.do?consider=1&status=1&id='+data.id);//跳转到第一轮专家打分的详情
			}else{
				swal("操作失败!", data.text, "error");
			}
		}
	});
}
</script>
</html>