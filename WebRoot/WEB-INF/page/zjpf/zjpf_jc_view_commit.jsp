<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
	<meta http-equiv="pragma" content="no-cache">
	<meta http-equiv="cache-control" content="no-cache">
	<meta http-equiv="expires" content="0"> 
<head>
<style>
/*
PDFObject appends the classname "pdfobject-container" to the target element.
This enables you to style the element differently depending on whether the embed was successful.
In this example, a successful embed will result in a large box.
A failed embed will not have dimensions specified, so you don't see an oddly large empty box.
*/
.pdfobject-container {
	width: 100%;
	height:580px;
	margin: 2em 0;
}
.textarea-success {
	border-top:green 1px solid;
	border-bottom:green 1px solid; 
	border-left:green 1px solid;
    	border-right:green 1px solid;
}
.textarea-error {
	border-top:red 1px solid;
	border-bottom:red 1px solid; 
	border-left:red 1px solid;
    	border-right:red 1px solid;
}
.center{	position: absolute;left: 0px;top: 237px; width: 98%;margin: 0 auto;bottom: 35px;right: 5px;
	}
</style>
<script type="text/javascript">
/* 	判断评分的状态scoredState为1 */
$(document).ready(function(){
	var scoredState= eval('${expertHandlFileList.scoredstate}');
	var status = eval('${status}');
	//查看
	
	if(scoredState == '1'){
		if(status !='1'){
			$("#submitBtn").show();
			$("#jcpj").removeAttr("disabled");
			$("#yxdxanlituijian").attr("disabled",false);
			//$("#chickAnjuan").hide();
			/*$("#fileCodeInput").attr("disabled","disabled");
			$("#fileCodeInput").val('${ expertHandlFileList.filecode}');*/
			$("#fileCodeInput").hide();
			 $("#queren").hide();
		 	$(".inputDisabled").removeAttr("readonly");
			//扣分理由
		 	$("textarea").removeAttr("disabled");
	 	}
	}else{
		 //$(".inputDisabled").attr("readonly","readonly");
		 //$("input:checkbox").attr("disabled",true);
	}
});


function trim(str) {
	return str.replace(/(^\s+)|(\s+$)/g, "");
}

//下载文件
function xiaZaiAnJuan(){
	//var fileCode = $("#fileCode").html();
	var fileid  = $("#fileId").val();
 	$.ajax({
	    type:"post",
	    url:WEBPATH+'/jcpf/jcpfExistFileUrl.do',
	    data:{fileId:fileid },           //注意数据用{}
	    success:function(data){  //成功
		 if("yes" == data){
				window.location.href="${pageContext.request.contextPath}/jcpf/jcpfAnJuandown.do?fileid="+fileid;
			    return false;
	         }else if("no" == data){
	            	  swal( "操作失败","该案卷不存在!", "error");
	            	  return false;
			}else if("suffixerror" ==data){
				  swal( "操作失败","该案卷上传数据格式有问题!", "error");
            	  return false;
			}
	         }
	});
 }


$(document).ready(function(){
	//保存数据方法
	var pageNum = $("#pageNum").val();
	
	$(":input[type='checkbox']").attr("disabled","disabled");
	$(":input[type='text'], textarea").attr("disabled","disabled");
});
</script>

</head>
<div id='zjpfJCVue'>
<div   class="center_weizhi">当前位置：：专家评分 - 委员评审案卷  - ${filetypeName }</div>
<div class="center" id="scrollBarCenter">
<div class="center_list" id="anjuanxinxi">
	<input id="expertFileId"  type="hidden" value="${expertFileId }" >
	<input id="fileId" type="hidden" :value="scoringIndexList.fileid">
	<div class="dingwei">案卷或材料：<span style="color:#009CCD;font-size:14px; font-weight:400;" id ="filecode">
						{{scoringIndexList.filecode }} <span style="color:red;font-size:16px; font-weight:bold;"><c:choose><c:when test="${expertHandlFileList.closed==1}">（已结案）</c:when><c:when test="${expertHandlFileList.closed==0}">（未结案）</c:when> </c:choose></span>
						</span>
	<c:if test ="${sessionScope.sa_session.sysStatus == '5' }">	
		<a v-bind:href="scoringIndexList.downUrl" target="_Blank">
			<button class="btn btn-success btn-xs" id="xiaZai" data-toggle="modal" data-target="#myModal">下载</button>
		</a>	
		<span id="viewBtn" v-html='datas' ></span>

	</c:if>
	</div>
	<select id="indexName" onchange="change(this.value)"></select>
    <!-- <div class="dingwei" id ="queren">案卷处罚决定书文号确认：<input type="text" class="form-control" style="width:200px; float:right;"  id="fileCodeInput" placeholder="请输入案卷处罚决定书文号" ></div>
    <div class="dingwei"><button id ="chickAnjuan" class="btn btn-primary" data-toggle="modal"  onclick="chickAnjuan()" data-target="#myModal">开始评分</button></div> -->
 	   <table class="table table-striped table-bordered table-condensed" id="tableOuter" >
      <thead>
           <tr>
             <td width="50" height="30" bgcolor="#efefef">序号</td>
             <td width="100" bgcolor="#efefef">分指标</td>
             <td width="90" bgcolor="#efefef">分指标分值</td>
             <td width="200" bgcolor="#efefef">指标说明</td>
             <td bgcolor="#efefef">判断标准</td>
             <td width="70px" bgcolor="#efefef">专家A评分值</td>
             <td width="70px" bgcolor="#efefef">专家A评审依据</td>
             <td width="70px" bgcolor="#efefef">专家B评分值</td>
             <td width="70px" bgcolor="#efefef">专家B评审依据</td>
             <td width="155px" bgcolor="#efefef">分值</td>
             <td bgcolor="#efefef" width="70">一票否决</td>
             <td bgcolor="#efefef" width="70">无需评查</td>
             <td bgcolor="#efefef" width="80">得分</td>
             <td width="15%" bgcolor="#efefef">扣分理由</td>
           </tr>
         </thead>
         	<tbody  class="form-group">
          		 <tr v-for="(scoringIndex, index) in scoringIndexList.expertHandlIndexScoreList" :class="scoringIndex.className">
		             <td height="30" align="center" style="vertical-align:middle;">{{index+1}}</td>
		             <td v-if="scoringIndex.indexname=='结案表'" :class="scoringIndex.className">
						{{scoringIndex.indexname}}
						<span v-if="scoringIndexList.closed==1" style="color:red;font-size:16px; font-weight:bold;">（已结案）</span>
						<span v-if="scoringIndexList.closed==0" style="color:red;font-size:16px; font-weight:bold;">（未结案）</span>
					</td>
					<td v-else>{{scoringIndex.indexname}}</td>
		             <td style="vertical-align:middle;">{{scoringIndex.indexscore}}</td>
		             <td style="vertical-align:middle; width:200px;">{{scoringIndex.indexdesc}}</td>
		             <td colspan="6" v-if="scoringIndex.expertHandlItemScoreList != null && scoringIndex.expertHandlItemScoreList.length !=0 "  style="padding:0;">
                        <table width="100%" border="0" cellspacing="0" cellpadding="0" style="padding:0;">
                          <tr  v-for="(scoringItem, index1) in scoringIndex.expertHandlItemScoreList">   
                          	<td style="border-bottom:solid 1px #ddd; border-right:solid 1px #ddd;vertical-align:middle;">
                                 <div style="vertical-align:middle; margin:0 5px 5px 0;">
		            			{{scoringItem.itemname}}<!-- （{{scoringItem.itemscore}}分） -->
		            			</div>
		            		<span style="color:red" v-if="scoringItem.isInCheck==1 && scoringIndex.inCheckValue!=1 && scoringIndex.voteDownValue!=1">
		            			&nbsp;&nbsp;是否无需评查：
                                			<input type="checkbox" id="" v-model="scoringItem.inCheckValue"
					              		  @change="wuxupingcha2(index,index1)" 
					              		  style="width:16px; height:16px;  display: table-cell;vertical-align: middle; text-align: center; ">
                                		</span>	
                            </td>
                            
                            <td style="width:70px; border-bottom:solid 1px #ddd; border-right:solid 1px #ddd; vertical-align:middle;align:center;">{{scoringItem.aexpertScore}}</td>
							<td :rowspan="scoringIndex.expertHandlItemScoreList.length" v-if="index1==0" style="width:70px; border-bottom:solid 1px #ddd; border-right:solid 1px #ddd; vertical-align:middle;">{{scoringIndex.aexpertReason}}</td>
               				<td style="width:70px; border-bottom:solid 1px #ddd; border-right:solid 1px #ddd; vertical-align:middle;align:center;">{{scoringItem.bexpertScore}}</td>
                			<td :rowspan="scoringIndex.expertHandlItemScoreList.length" v-if="index1==0" style="width:70px; border-bottom:solid 1px #ddd; border-right:solid 1px #ddd; vertical-align:middle;">{{scoringIndex.bexpertReason}}</td>                   
                            
                            <td style="border-bottom:solid 1px #ddd; width:155px;vertical-align:middle;">
                                 <div  :id="index+'expertItem'+index1" style="margin:0 0 5px 0; float:left;">
	                                 <div v-if="scoringIndex.isAdd==1" style="float:left; padding:5px 2px 7px 3px; color:red; font-size:20px;">＋</div>
	                                 <div v-if="scoringIndex.isAdd!=1" style="float:left; padding:0 0 3px 0; margin:0; color:red; font-size:26px;">－</div>
	                                 <div style="float:right; padding-top:4px;">
	                                 	<input @input="updateItem(index,index1,scoringItem.itemscore,scoringItem.temItemId)"  
	                                       v-model="scoringItem.score" type="text" :class="'checkitem'+index" :id="index+'scoreInput'+index1" :name = "index+'item'+index1"
	                                      class="form-control inputDisabled"   placeholder="请输入初评得分" style="width:125px;">
	                                 </div>
                                 	<div style="color:red; font-size: 12px;">{{scoringItem.validatorMessage}}
                                 	</div>
                                 </div>
                            </td>                          
                          </tr>
                        </table>
		             </td>
		            <td v-else >
		             <table width="100%" border="0" cellspacing="0" cellpadding="0">
		             	<tr>
		             		<td style="border-bottom:solid 1px #f7f7f7;">
                                 <div style="vertical-align:middle; margin:0 5px 5px 0;">
		            				{{scoringIndex.indexdesc}}
		            			</div>
                            </td>    
                            <td style="border-bottom:solid 1px #f7f7f7;">
                            	<div :id="'expertIndex'+index"  class="form-group" style="width: 150px;float: right;">
	                            	<div v-if="scoringIndex.isAdd==1" style="float:left; padding:5px 2px 7px 3px; color:red; font-size:20px;">+</div>
		                            <div v-if="scoringIndex.isAdd!=1" style="float:left; padding:0 0 3px 0; margin:0; color:red; font-size:26px;">-</div>
				                		<input type="text" v-model="scoringIndex.score" :id="index+'scoreInput'+index1"
				                		 @input="updateIndex(index,scoringIndex.indexscore)" :class="'checkitem'+index"
				               	  		 class="form-control inputDisabled"  placeholder="请输入初评得分">
			         			  	<div style="color:red;font-size: 5px;">{{scoringIndex.validatorMessage}}</div>
			         			 </div>
                            </td>
		             	</tr>
		             </table>
		           </td>
		           <!-- 一票否决 -->
		            <td style="vertical-align:middle; text-align:center;">
		              <span v-if="scoringIndex.isVoteDown ==1" >
		              		 <input type="checkbox" :id="'checkbox'+index" 
		              		  @change="changeCheckBoxCli(index,'1')" v-model="scoringIndex.voteDownValue"
		              		  style="width:16px; height:16px; display: table-cell;vertical-align: middle; text-align: center;">
               				 <label :for="'checkbox'+index" class="checkbox-blue" checked></label>
		              </span>
		           </td>
		           <!-- 无需评查 -->
		            <td style="vertical-align:middle;text-align:center;">
		            <span v-if="scoringIndex.isInCheck ==1">
		              		 <input type="checkbox" id="checkbox1" v-model="scoringIndex.inCheckValue"
		              		  @change="wuxupingcha(index)" 
		              		  style="width:16px; height:16px;  display: table-cell;vertical-align: middle; text-align: center; ">
               				 <label for="checkbox1" class="checkbox-blue" checked></label>
		              </span>
		           </td>
		           <td style="vertical-align:middle;text-align:center;">
		           	  <input type="text" id="111" disabled="disabled" v-model="scoringIndex.resultScore" style="background-color:transparent; width:80px; border:0px;vertical-align:middle;text-align:center;">
		           	  <!-- <input type="text" id="222" v-if="scoringIndex.calculScore!=null && scoringIndex.calculScore!=''" disabled="disabled" v-model="scoringIndex.calculScore" style="background-color:transparent; width:80px; border:0px;vertical-align:middle;text-align:center;"> -->
		           </td>
		           
		           <!-- 扣分理由 -->
	             <td >																   
	           	   <textarea maxlength="1000" @input="updateCommon(index)" :id="'comment'+index" :rows="scoringIndex.expertHandlItemScoreList.length" class="form-control" v-model="scoringIndex.comment" placeholder="请输入扣分理由" style="width:100%;">
				   
				   </textarea>
				   <div style="color:red; font-size: 12px;">{{scoringIndex.validatorMessage}}</div>
	             </td>
		           
		    	</tr>
		           <tr>
		           		<td></td>
		           		<td align="center"><strong>合计</strong></td>
		           		<td>103</td>
		           		<td></td>
		           		<td></td>
		           		<td></td>
		           		<td></td>
		           		<td></td>
		           		<td></td>
		           		<td></td>
		           		<td></td>
		           		<td align="center"><strong>最终得分</strong></td>
		           		<td style="vertical-align:middle;text-align:center;">{{scoringIndexList.expertfinalscore}}</td>
		           		<td></td>
		           </tr>
				 <tr>
					 <td height="30" colspan="15">
						 <textarea maxlength="2000" v-model="scoringIndexList.problemRemark" rows="5" class="form-control" id="problem_remark" placeholder="疑难（争议）问题描述">${expertHandlFileList.problemRemark}</textarea>
					 </td>
				 </tr>
				 <tr>
					 <td colspan="2">专家A</br>疑难（争议）问题描述</td>
					 <td height="30" colspan="13">
						 <textarea maxlength="2000" v-model="scoringIndexList.problemRemarkA" rows="5" class="form-control" id="problem_remarkA" placeholder="专家A 疑难（争议）问题描述"></textarea>
					 </td>
				 </tr>
				 <tr>
					 <td colspan="2">专家B</br>疑难（争议）问题描述</td>
					 <td height="30" colspan="13">
						 <textarea maxlength="2000" v-model="scoringIndexList.problemRemarkB" rows="5" class="form-control" id="problem_remarkB" placeholder="疑难（争议）问题描述"></textarea>
					 </td>
				 </tr>
<%--	           <tr>--%>
<%--           		<td height="30" colspan="14">--%>
<%--           		<input id="yxdxanlituijian" name = "yxdxanlituijian" type="checkbox"  v-on:click="jcpjCheckBoxClick()" >优秀案卷推荐--%>
<%--            		 <div class="col-sm-12" style="display: none;" id ="yxdxAnLiTuiJianReviews1"  >--%>
<%--            			<textarea maxlength="2000" v-model="scoringIndexList.yxdxanlituijianreviews" rows="5" class="form-control" id="jcpj" placeholder="请输入专家评语，长度不能超过2000个字符"></textarea>--%>
<%--            		 </div>--%>
<%--            	</td>--%>
<%--	           </tr>--%>
<%--	           <tr>--%>
<%--           		<td height="30" colspan="14">--%>
<%--           		<input id="ajpjYxdxanlituijian" name = "ajpjYxdxanlituijian" type="checkbox"  v-on:click="ajpjCheckBoxClick()" >较差案卷推荐--%>
<%--            		 <div class="col-sm-12" style="display: none;" id ="ajpjYxdxAnLiTuiJianReviews1"  >--%>
<%--            			<textarea maxlength="2000" v-model="scoringIndexList.ajpjYxdxanlituijianreviews" rows="5" class="form-control" id="jcpj" placeholder="请输入专家评语，长度不能超过2000个字符"></textarea>--%>
<%--            		 </div>--%>
<%--            	</td>--%>
<%--	           </tr>--%>
          	</tbody>
       </table>
       
       	<div class="submit">
       	</div>
         <div><input id ="pageNum" type="hidden" value="${pageNum }"></div>
 		<div class="submit">
 			<c:choose> 
 			 <c:when test = "${sessionScope.sa_session.sysStatus != 5}"> 
			 </c:when> 
			 <c:otherwise> 
			<!-- 	<a href="#"><button type="button" class="btn btn-primary" id="saveSubmit" name="signup" value="Sign up" style="font-size:16px;width:150px; margin-top:5px; display:none">信息保存</button></a>
			 --></c:otherwise> 
			</c:choose></div>
 	</div>
</div>
</div>
<script >
	var firScolltop=0;
	var oldValue='';
	var scoringIndexList =null;
	var expertFileId = $("#expertFileId").val();
	if(expertFileId != null){
		$.ajax({
			cache : true,
			type : "GET",
			async : false,
			//api/test/detail/behaviour/74
			url: WEBPATH+"/zjpf/getCommitIndexList.do",
			data:{
				id:expertFileId,handType:0
			},
			error : function(request) {
				swal("错误!","请求异常！", "error");
			},
			success : function(data) {
		  		if(data.result =='success'){
                    scoringIndexList = data.data;
                    // scoringIndexList.problemRemark = problemRemark;
                    for(var i=0;i<data.data.expertHandlIndexScoreList.length;i++){
                        data.data.expertHandlIndexScoreList[i].className = 'class'+ data.data.expertHandlIndexScoreList[i].id;
                        var index = data.data.expertHandlIndexScoreList[i];
                        //先创建好select里面的option元素
                        var option=document.createElement("option");
                        //转换DOM对象为JQ对象,好用JQ里面提供的方法 给option的value赋值
                        $(option).val('class' + index.id);
                        $(option).text(index.indexname);
                        $("#indexName").append(option);
                    }
                    console.log(scoringIndexList);
		  		}
			} 
		});			
	}

    function change(value) {
		var mao;
		var pos;
		var poshigh;
		mao = $("." + value);
		pos = mao.offset().top;
		poshigh = mao.height();
		//alert(poshigh)
		var temp=pos+firScolltop;
		if(oldValue!=''){
			// alert("old:"+$("." + oldValue).offset().top);
			temp=temp-$("." + oldValue).offset().top;
		}
		// alert(pos);
		$("#scrollBarCenter").animate({scrollTop: temp - 210})
		// alert(mao.offset().top)
		firScolltop = temp;
		oldValue=value;
    }
	var veiwBtnVue = new Vue({
		el:'#viewBtn',
		data:{
			datas:'<button class="btn btn-info btn-xs" id="xiaZaiFuJian" v-on:click="showPdfClick()">预览</button>',
		},
		
	});
	var xzcfVue = new Vue({
		  el: '#zjpfJCVue',
		  data: {
			  scoringIndexList:scoringIndexList,
		  },
		  mounted: function(){
		  },
		  methods: {
		  }
	});
	
	//优秀推荐
	//var yxtj = xzcfVue.scoringIndexList.yxdxanlituijian;
	if(xzcfVue.scoringIndexList.yxdxanlituijian=="1"){
		//$('#yxdxanlituijian').attr('checked', true)
		
	    $("[name = yxdxanlituijian]:checkbox").attr("checked", true);
		$("#yxdxAnLiTuiJianReviews1").css("display","block");
		
		//禁止反面推荐
		$("#ajpjYxdxanlituijian").attr("disabled","disabled");
		$("#ajpjYxdxAnLiTuiJianReviews1").css("display", "none");
	}
	
	//反面推荐
	//var ajpja = xzcfVue.scoringIndexList.ajpjYxdxanlituijian;
	if(xzcfVue.scoringIndexList.ajpjYxdxanlituijian=="1"){
		//$('#ajpjYxdxanlituijian').attr('checked', true)
		
	    $("[name = ajpjYxdxanlituijian]:checkbox").attr("checked", true);
		$("#ajpjYxdxAnLiTuiJianReviews1").css("display","block");
		
		//禁止优秀推荐
		$("#yxdxanlituijian").attr("disabled","disabled");
		$("#yxdxAnLiTuiJianReviews1").css("display", "none");
	}
	

</script>
	<!-- 附件预览  -->
	<div class="modal fade" id="view" tabindex="-1" role="dialog"
		aria-labelledby="myModalLabel" aria-hidden="true">
		<div class="modal-dialog modal-lg">
			<div class="modal-content">
			</div>
		</div>
	</div>
</body>
</html>
