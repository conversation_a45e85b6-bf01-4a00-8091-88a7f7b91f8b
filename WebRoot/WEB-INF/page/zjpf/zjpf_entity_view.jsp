<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
<style type="text/css">
	.pdfobject-container {
		width: 100%;
		height:580px;
		margin: 2em 0;
	}
	.textarea-success {
		border-top:green 1px solid;
		border-bottom:green 1px solid;
		border-left:green 1px solid;
     	border-right:green 1px solid;
	}
	.textarea-error {
		border-top:red 1px solid;
		border-bottom:red 1px solid;
		border-left:red 1px solid;
     	border-right:red 1px solid;
	}
	.center_list{background-color: #ffffff;padding:15px 0px 0px 5px}
	.table>thead>tr>td{line-height: 3.1}
	.table-bordered>thead>tr>td{background-color: #F5F7FA;border:1px solid #EBEEF5;text-align: center}
	input[type=radio]{ margin-right: 5px;margin-left: 10px;}
	input[type=checkbox], input[type=radio]{height:16px;width:16px;position: relative;top:3px;right:2px;}
</style>
</head>
<script type="text/javascript">
	$(".left_menu").hide();
		business.listenEnter();
		var pageNum =$("#pageNum").val();
		var scoringIndexList =null;
		var expertFileId = $("#expertFileId").val();
		$.ajax({
			cache : true,
			type : "GET",
			async : false,
			url: WEBPATH+"/zjpf/getEntityIndexs.do",
			data:{
				id:expertFileId
			},
			error : function(request) {
				swal("错误!","请求异常！", "error");
			},
			success : function(data) {
			  	if(data.result =='success'){
			  		scoringIndexList=data.data;
			  		//console.log(scoringIndexList);
			  	}
			}
		});

		var xzcfVue = new Vue({
			  el: '#ExpertVue',
			  data: {
				  scoringIndexList:scoringIndexList,
			  },
			  methods: {

			  }
		});

		//下载文件
		function xiaZaiAnJuan(){
		  var fileId = $("#fileId").val();
		  $.ajax({
			    type:"post",
			    url:WEBPATH+'/zjpf/xzcfExistFileUrl.do',
			    data:{fileId:fileId},           //注意数据用{}
			    success:function(data){  //成功
				 	if("yes" == data){
						window.location.href="${pageContext.request.contextPath}/zjpf/zjpfAnJuandown.do?fileId="+fileId;
					    return false;
			         }else if("no" == data){
			            	  swal( "操作失败","该案卷不存在!", "error");
			            	  return false;
					}else if("suffixerror" ==data){
						  swal( "操作失败","该案卷上传数据格式有问题!", "error");
		            	  return false;
					}
			    }
		    });
		}

	$(document).ready(function(){
		var mater = '${expertHandlFileList.fileMaterials}'
		if (mater == '1'){
			$("#fileMaterials").attr("checked",true);
		}
		$("#fileMaterials").attr('disabled','disabled')
		$(":input[type='radio']").attr("disabled","disabled");
		$(":input[type='text'],textarea").attr("disabled","disabled");
	})
</script>
<body>
	<div id="ExpertVue">
		<div class="center_weizhi">当前位置：专家评分 - 专家评审案卷 - ${filetypeName}</div>
		<div class="center">
			<div class="center_list">
				<input id="pageNum"  type="hidden" value="${pageNum}" >
		 		<input id="expertFileId"  type="hidden" value="${expertFileId}" >
		 		<input id="fileId"  type="hidden" value="${expertHandlFileList.fileid}" >
				<button    class="btn btn-primary"  style="font-size:16px;width:98px; height:40px; padding:6px 34px;margin-right: 10px;float: right;" onclick="macroMgr.onLevelOneMenuClick(null, 'zjpf.do')">返回</button>
				<div class="dingwei" style="margin-left:19px;padding-top:3px;font-weight: 400; font-size: 14px;color: #333333">
<%--					<button class="btn btn-default" style='padding:9px 34px;margin-right:14px' onclick="macroMgr.onLevelTwoMenuClick(null, 'zjpf/zjpfScoreView.do?id=${expertFileId}&pageNum=${pageNum}' )">规范性评查</button>--%>
					<button class="btn btn-danger" style='padding:9px 34px;margin-right:14px'>合法性评查</button>
					<button class="btn btn-default" style='padding:9px 34px;margin-left:14px' onclick="macroMgr.onLevelTwoMenuClick(null, 'zjpf/plusesView.do?id=${expertFileId}&pageNum=${pageNum}')">加分项</button>
					<p style="margin-top:21px;font-weight: 600; font-size: 16px">评审说明：</p>
					<%--<p>1.出现下面任一项情形的属于错案，扣50分。</p>--%>
					<p>与其中任一项表现情形相吻合的，在序号处打“√”（找不到合适的表述请在相应评查项目的“其他”项前打“√”）并在审核栏说明具体情形。</p>
					<div class="dingwei" style="color:#333333;font-size:14px; font-weight:400;">案卷或材料：<span style="color:#009CCD;font-size:14px; font-weight:400;" id ="filecode">
						{{scoringIndexList.filecode }}
						<span style="color:red;font-size:14px; font-weight:400;"><c:choose><c:when test="${expertHandlFileList.closed==1}">（已结案）</c:when><c:when test="${expertHandlFileList.closed==0}">（未结案）</c:when> </c:choose></span>
						</span>
					<c:if test ="${sessionScope.sa_session.sysStatus == '5' }">
						<a v-bind:href="scoringIndexList.downUrl" target="_Blank">
							<button id="download" class="btn btn-success btn-xs" style="padding:5px 12px;margin-left:10px" data-toggle="modal" data-target="#myModal">下载</button>
						</a>
						<c:if test="${expertHandlFileList.suffix== 'pdf'}">
							<button id="look" class="btn btn-info btn-xs" data-toggle="modal" style="padding:5px 12px;margin-left:10px" data-remote="${webpath}/jcpf/showFileModal.do?fastDFSUrl=+'${fastDFS}'+" data-target="#view">预览</button>
						</c:if>
					</c:if>
					</div>
				</div>

		       	<form action="#" id ="zjpfForm" method="post" style="padding-left: 19px">
		        	<table class="table table-bordered table-hover table-condensed">
						<thead>
							<tr>
								<td width="3%" height="30" bgcolor="#efefef">序号</td>
								<td width="7%" bgcolor="#efefef">评查项目</td>
								<td bgcolor="#efefef">
								<table width="100%" border="0" cellspacing="0" cellpadding="0" style="padding:0;">
									<tr>
										<td style="text-align: center">错案表现形式</td>
<%--										style="border-right:solid 1px #ddd;"--%>
										<td style="width:7%; padding:0px 5px 0px 5px;text-align: center;">评查</td>
<%--										style="border-right:solid 1px #ddd;--%>
										<td style="width:30%;padding:0px 5px 0px 5px;text-align: center;">具体情形</td>
									</tr>
								</table>
								</td>
								<!-- <td width="15%" bgcolor="#efefef">具体情形</td> -->
							</tr>
						</thead>
						<tbody  class="form-group">
							<tr v-for="(scoringIndex, index) in scoringIndexList.expertHandlIndexScoreList">
								<td height="30" align="center" >{{index+1}}</td>

								<!-- 评查项目 -->
								<td align="center">{{scoringIndex.indexname}}</td>

								<!-- 标准分 -->
								<!-- <td style="vertical-align:middle;">{{scoringIndex.indexscore}}</td> -->

								<!-- 评分细则 -->
								<td v-if="scoringIndex.expertHandlItemScoreList != null && scoringIndex.expertHandlItemScoreList.length !=0 "  style="padding:5px;">
									<table width="100%" border="0" cellspacing="0" cellpadding="0" style="padding:0;">
										<tr  v-for="(scoringItem, index1) in scoringIndex.expertHandlItemScoreList">
											<td style="vertical-align:middle;border-right:solid 1px #EBEEF5">
<%--												border-bottom:solid 1px #ddd; border-right:solid 1px #ddd;--%>
												<div style="vertical-align:middle; width:815px;line-height:24px;padding-left: 5px;margin:0 -110px 5px 0;">{{scoringItem.itemname}}（{{scoringItem.itemscore}}分）</div>
											</td>
											<td style=" vertical-align:middle;border-right:solid 1px #EBEEF5; ">
<%--												border-bottom:solid 1px #ddd; border-right:solid 1px #ddd; width:10%;--%>
												<div  :id="index+'expertItem'+index1" style="float:left;">

													<div style="float:right; padding:0 5px 0 10px;">

															<input type="radio" value="1" :name="index+'scoreRadio'+index1" v-model="scoringItem.score" >是
															<input type="radio" value="0" :name="index+'scoreRadio'+index1" v-model="scoringItem.score" >否
													</div>
													<div style="color:red; font-size: 12px;">{{scoringItem.validatorMessage}}</div>
												</div>
											</td>
											<td style="border-bottom:solid 1px #ddd; width:30%;vertical-align:middle;padding:0px 5px 0px 5px;">
												<textarea maxlength="1000" :id="index+'comment'+index1" name="commeInput" v-model="scoringItem.commeStr"
												@input="updateCommon(index,index1)"  rows="4" class="form-control" style="width:100%;"
												:title="scoringItem.commeStr">
												</textarea>
												<div style="color:red; font-size: 12px;">{{scoringItem.validatorComment}}</div>
											</td>
										</tr>
									</table>
								</td>
							</tr>
							<tr>
				             <td colspan="2">合计得分</td>
				             <td colspan="2">任意一小项选择是，则扣全50分，每一小项都是必选项<input type="text" v-model="scoringIndexList.entityscore" class="form-control"></td>

				           </tr>
						<%--	<tr>
								<td height="30" colspan="9">
									<div class="col-sm-12" style="padding-left:30px">
									<input id="fileMaterials" name="fileMaterials" type="checkbox" value="1"><span style="font-size: 14px;color: #333333;font-weight:400">&nbsp;案卷材料严重不全，导致无法全面评价案卷实体和程序问题</span>
									</div>
									<div class="col-sm-12" style="padding-left:30px">
										<h4>具体情形：</h4>
										<p>①一般行政处罚类案卷：仅有立案审批表、现场检查（勘察）笔录、调查询问笔录、证据材料、案件调查报告、行政处罚事先（听证）告知书、行政处罚决定书中的三项或不足三项的；</p>
										<p>②按日计罚类案卷：仅有立案审批表、现场检查（勘察）笔录、调查询问笔录、证据材料、案件调查报告、责令改正违法行为决定书、行政处罚事先（听证）告知书、行政处罚听证通知书、听证笔录、原行政处罚决定书、
											按日连续处罚决定书、按日连续处罚后再次责令改正违法行为决定书中的三项或不足三项的；</p>
										<p>③查封扣押类案卷：仅有立案审批表、现场检查（勘察）笔录、调查询问笔录、证据材料、查封/扣押决定书、查封/扣押清单中的三项或不足三项的；</p>
										<p>④限产停产类案卷：仅有立案审批表、现场检查（勘察）笔录、调查询问笔录、证据材料、责令改正违法行为决定书、责令限制生产/停产整治事先（听证）告知书、责令限制生产/停产整治听证通知书、责令限制生产/停产整治决定书中的三项或不足三项的；</p>
										<p>⑤移送拘留类案卷：仅有立案审批表、现场检查（勘察）笔录、调查询问笔录、证据材料、案件调查报告、行政处罚事先（听证）告知书、行政处罚听证通知书、听证笔录、行政处罚决定书（或责改决定书）、环境违法适用行政拘留处罚案件移送书、环境违法适用行政拘留处罚案件移送材料清单中的三项或不足三项的；</p>
										<p>⑥环境污染犯罪类案卷：仅有立案审批表、现场检查（勘察）笔录、调查询问笔录、证据材料、案件调查报告、行政处罚事先（听证）告知书、行政处罚听证通知书、听证笔录、行政处罚决定书、移送涉嫌环境犯罪案件审批表、涉嫌环境犯罪案件移送书、涉嫌环境犯罪案件移送材料清单中的三项或不足三项的；</p>
									</div>
								</td>
							</tr>--%>
						</tbody>
					</table>
				</form>
				<!--  附件查看 -->
				<div class="modal fade" id="view" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
					<div class="modal-dialog modal-lg">
						<div class="modal-content"></div>
					</div>
				</div>
			</div>
		</div>
	</div>
</body>
</html>
