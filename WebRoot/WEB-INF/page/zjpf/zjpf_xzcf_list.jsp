<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<html>
<head>
</head>
<script type="text/javascript">

	$(document).ready(function(){
		business.listenEnter("searchButt");
		$("#searchButt").click(function(){
			business.addMainContentParserHtml("zjpf/xzcfList.do",$("#searchForm").serialize());
		});
		$("#scoredState").change(function(){
			business.addMainContentParserHtml("zjpf/xzcfList.do",$("#searchForm").serialize());
		});
	});
	//分页
	$(document).ready(function(){
		var curentPage = eval('${pageBean.pageNum}');
		var totalPage = eval('${pageBean.pages}');
		if(totalPage>0){
			var options = {
				bootstrapMajorVersion: 3,
			    currentPage: curentPage,
			    totalPages: totalPage,
			    numberOfPages: 5,
			    itemTexts: function (type, page, current) {
			            switch (type) {
			            case "first":
			                return "首页";
			            case "prev":
			                return "&laquo;";
			            case "next":
			                return "&raquo;";
			            case "last":
			                return "尾页";
			            case "page":
			                return page;
			            }
			    },
			    onPageClicked: function (event, originalEvent, type, page) {
	            	business.addMainContentParserHtml(WEBPATH+'/zjpf/xzcfList.do?pageNum='+page,$("#searchForm").serialize());
	            }
	    	};
	    	$('#pageCon').bootstrapPaginator(options);
    	}
	});

</script>
<body>
<div class="center_weizhi">当前位置：专家评分 - 专家评审案卷 - 行政处罚案卷</div>
<div class="center">
<div class="center_list">
    <div class="panel-group" id="accordion">
          <h4 class="panel-title">
            <!---信息填报
            <div class="btn-group" style="margin-right:20px;float:left;">
               <a href="CementEnterprises_input.html"><button type="button" class="btn btn-success" style="font-size:16px;">企业信息录入</button></a>
            </div>--->          
            
      
          <form role="form" method="get" id ="searchForm" name ="form">
              <select id ="scoredState" class="form-control" style="width:150px;margin-right:5px;" name="scoredState" >
                 <option value ="">请选择评分状态</option>
                 <option value="1" <c:if test="${scoredState=='1' }">selected</c:if> >已评</option>
                 <option value="0" <c:if test="${scoredState=='0' }">selected</c:if>>未评</option>
              </select>
        	<!-- </form>  -->
            <!---搜索--->
            <div style="width:260px;" class="btn-group">
          <!--      <form class="bs-example bs-example-form" role="form" id="fileCodeForm"> -->
                  <div class="row">                     
                     <div class="col-lg-6">
                        <div class="input-group">
                           <input type="text" placeholder="案卷文号关键字" class="form-control" name ="fileCode" value="${fileCode }" style="width:200px;">
                           <span class="input-group-btn">
                              <button id="searchButt"  class="btn btn-success" type="button">
                               	  快速搜索
                              </button>
                           </span>
                        </div><!-- /input-group -->
                     </div><!-- /.col-lg-6 -->
                  </div><!-- /.row -->
            </div>            
          </form>
          </h4>
    </div>
        <table class="table table-bordered table-hover table-condensed" >
         <thead>
           <tr>
             <td width="50" height="30" bgcolor="#efefef">序号</td>
             <td bgcolor="#efefef">案卷文号</td>
             <td width="150" bgcolor="#efefef">专家姓名</td>
             <td width="150" bgcolor="#efefef">初评得分</td>
             <td width="60" bgcolor="#efefef">状态</td>
             <td width="100" bgcolor="#efefef">操作</td>
           </tr>
         </thead>
         <tbody>
         <c:if test="${not empty pageBean.list }">
         <c:forEach  varStatus="id"  items="${pageBean.list}" var="experList">
         	<tr>
         		<td height="30" align="center" >${ id.index + 1}</td>
         		<td>${experList.fileCode}</td>
         		<td>${experList.name}</td>
         		<td>${experList.expertFinalScore }</td>
         		<c:choose>
					<c:when test="${experList.scoredState == '1' }">
						<td><button class="btn btn-success btn-xs" data-toggle="modal" data-target="#myModal">已评</button></td>
					</c:when>
					<c:otherwise>
						<td><button class="btn btn-warning btn-xs" data-toggle="modal" data-target="#myModal">未评</button></td>
					</c:otherwise>
				</c:choose>
		        <td><a  href="javascript:void(0);" onclick="macroMgr.onLevelTwoMenuClick(null, 'zjpf/zjpf_input.do?id=${experList.id}&status=1' )"><button class="btn btn-info btn-xs" data-toggle="modal" data-target="#myModal">查看</button></a> 
		        
		         <c:if test ="${sessionScope.sa_session.sysStatus == '5' and experList.considerState != '1' }">
		  			   <a  href="javascript:void(0);" onclick="macroMgr.onLevelTwoMenuClick(null, 'zjpf/zjpf_input.do?id=${experList.id}&pageNum=${pageNum}' )"><button class="btn btn-primary btn-xs" data-toggle="modal" data-target="#myModal">评分</button></a>
         		</c:if>
         		</td>
		     	</tr>
         </c:forEach>
         </c:if>
         </tbody>
       </table>
    </div>
      <div class="page">
    <span style="padding:12px; float:left; color:#0099cc;">共${pageBean.total}条记录</span>
        <ul class="pagination" id="pageCon">
        </ul>
    </div>
</div>
</body>
</html>