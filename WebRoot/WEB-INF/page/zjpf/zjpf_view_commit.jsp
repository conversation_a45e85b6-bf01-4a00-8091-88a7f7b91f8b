<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
<style type="text/css">
	.pdfobject-container {
		width: 100%;
		height:580px;
		margin: 2em 0;
	}
	.textarea-success {
		border-top:green 1px solid;
		border-bottom:green 1px solid;
		border-left:green 1px solid;
     	border-right:green 1px solid;
	}
	.textarea-error {
		border-top:red 1px solid;
		border-bottom:red 1px solid;
		border-left:red 1px solid;
     	border-right:red 1px solid;
	}
	.ceshi{
		color: #DF1912;
		font-weight: 600;
		font-size: 14px;
	}
	.table-bordered>thead>tr>td{background-color: #F5F7FA;border:1px solid #EBEEF5;text-align: center}
	input[type=checkbox], input[type=radio]{height:16px;width:16px;position: relative;top:3px;right:2px;margin:12px 5px 12px 0}
	textarea.form-control{margin:15px 0}
</style>
</head>
<script type="text/javascript">
	$(".left_menu").hide();
	business.listenEnter();
	var pageNum = $("#pageNum").val();
	var scoringIndexList = null;
	var temIndexId = 0;
	var expertFileId = $("#expertFileId").val();
	var mater = '${expertHandlFileList.fileMaterials}'
	if (mater == '1'){
		$("#fileMaterials").attr("checked",true);

	}
	$.ajax({
		cache : true,
		type : "GET",
		async : false,
		//api/test/detail/behaviour/74
		url: WEBPATH+"/zjpf/getCommitIndexList.do",
		data:{
			id:expertFileId,handType:0
		},
		error : function(request) {
			swal("错误!","请求异常！", "error");
		},
		success : function(data) {
		  	if(data.result =='success'){
		  		scoringIndexList = data.data;
		  		console.log(scoringIndexList);
				switch (scoringIndexList.filetype) {
					case "2":
						temIndexId = 118;
						break;
					case "0":
						temIndexId = 110;
						break;
					case "1":
						temIndexId = 111;
						break;
					case "3":
						temIndexId = 120;
						break;
					case "6":
						temIndexId = 115;
						break;
					case "7":
						temIndexId = 116;
						break;
				}
		  	}
		}
	});

	var xzcfVue = new Vue({
		el: '#CommitVue',
		data: {
			scoringIndexList:scoringIndexList,
		},
		computed:{
			totalScore:function(){

				var sum = 0;
				this.scoringIndexList.expertHandlIndexScoreList.forEach(function(item){
					if(item.inCheckValue != 1) {
						if(item.indexscore=="null"||item.indexscore===""||item.temIndexId==this.temIndexId||item.indexid==this.temIndexId){
							sum += 0;
						}else{
							sum += item.indexscore;
						}
					}
				});
				return sum;
			},
			inputScore:function(){
				var inpuSco = 0;
				this.scoringIndexList.expertHandlIndexScoreList.forEach(function(item){
					if(item.resultScore=="null"||item.resultScore===""||(item.temIndexId==this.temIndexId||item.indexid==this.temIndexId)){
						inpuSco += 0;
					}else{
						inpuSco += item.resultScore;
					}

				});
				return inpuSco;
			}
		},
		methods: {
		}//vue对象中method结束

	});//vue对象结束


	/**
	 * 回显案卷类型
	 */
	var a = xzcfVue.scoringIndexList.yxdxanlituijian;
	$(function () {
		if(xzcfVue.scoringIndexList.yxdxanlituijian=="1"){
			//$('#yxdxanlituijian').attr('checked', true)

			$("[name = yxdxanlituijian]:checkbox").attr("checked", true);
			$("#yxdxAnLiTuiJianReviews1").css("display","block");

			//优秀案卷选项回显
			var checkArray = $("input[name='yxaj']");
			var codes = xzcfVue.scoringIndexList.ajpjYxdxanlituijianCode;
			if(codes != null && codes != "" ){
				var myArray=codes.split(",");
				for (var i = 0; i < myArray.length; i++) {
					//获取所有复选框对象的value属性，然后，用checkArray[i]和他们匹配，如果有，则说明他应被选中
					$.each(checkArray, function (j, checkbox) {
						//获取复选框的value属性
						var checkValue=$(checkbox).val();
						//console.log(j+"----"+checkValue)
						if (myArray[i] == checkValue) {
							$(checkbox).attr("checked", true);
						}
					})
				}
			}
			//禁止较差推荐
			$("#ajpjYxdxanlituijian").attr("disabled","disabled");
			$("#ajpjYxdxAnLiTuiJianReviews1").css("display", "none");
			//禁止不推荐为典型案例
			$("#noTuiJian").attr("disabled","disabled");
		}


		var ajpja = xzcfVue.scoringIndexList.ajpjYxdxanlituijian;
		if(xzcfVue.scoringIndexList.ajpjYxdxanlituijian=="1"){
			//$('#ajpjYxdxanlituijian').attr('checked', true)
			$("[name = ajpjYxdxanlituijian]:checkbox").attr("checked", true);
			$("#ajpjYxdxAnLiTuiJianReviews1").css("display","block");
			//较差案卷选项回显
			var checkArray = $("input[name='jcaj']");
			var codes = xzcfVue.scoringIndexList.jcajtuijianCode;
			if(codes != null && codes != "" ){
				var myArray=codes.split(",");
				for (var i = 0; i < myArray.length; i++) {
					//获取所有复选框对象的value属性，然后，用checkArray[i]和他们匹配，如果有，则说明他应被选中
					$.each(checkArray, function (j, checkbox) {
						//获取复选框的value属性
						var checkValue=$(checkbox).val();
						//console.log(j+"----"+checkValue)
						if (myArray[i] == checkValue) {
							$(checkbox).attr("checked", true);
						}
					})
				}
			}
			//禁止优秀推荐
			$("#yxdxanlituijian").attr("disabled","disabled");
			$("#yxdxAnLiTuiJianReviews1").css("display", "none");
			//禁止不推荐为典型案例
			$("#noTuiJian").attr("disabled","disabled");
		}

		// 不推荐为典型案例 回显
		if(xzcfVue.scoringIndexList.noTuiJian=="1"){
			$("#noTuiJian").attr("checked", true);
			//禁止优秀推荐
			$("#yxdxanlituijian").attr("disabled","disabled");
			$("#yxdxAnLiTuiJianReviews1").css("display", "none");
			//禁止较差推荐
			$("#ajpjYxdxanlituijian").attr("disabled","disabled");
			$("#ajpjYxdxAnLiTuiJianReviews1").css("display", "none");
		}

	})


	//下载文件
	function xiaZaiAnJuan(){
	  var fileId = $("#fileId").val();
	  $.ajax({
		    type:"post",
		    url:WEBPATH+'/zjpf/xzcfExistFileUrl.do',
		    data:{fileId:fileId},           //注意数据用{}
		    success:function(data){  //成功
			 	if("yes" == data){
					window.location.href="${pageContext.request.contextPath}/zjpf/zjpfAnJuandown.do?fileId="+fileId;
				    return false;
		         }else if("no" == data){
		            	  swal( "操作失败","该案卷不存在!", "error");
		            	  return false;
				}else if("suffixerror" ==data){
					  swal( "操作失败","该案卷上传数据格式有问题!", "error");
	            	  return false;
				}
		    }
	    });
	}

	$(document).ready(function(){
		$(":input[type='checkbox']").attr("disabled","disabled");
		$(":input[type='text'], textarea").attr("disabled","disabled");

		//保存数据方法
		var status = '${status}';
		if(status==1){//status为1时是查看，否则为编辑
			$("#chickAnjuan").hide();
			$("#fileCodeInput").attr("disabled","disabled");
			var scoredstate = ${expertHandlFileList.scoredstate};
			// if(scoredstate==1 || scoredstate ==5){
			$("#fileCodeInput").val('${expertHandlFileList.filecode}');
			// }else{
			// 	/* $("#download").attr("disabled","disalbed"); */
			// 	$("#look").attr("disabled","disalbed");//预览按钮在评分后才能点击
			// }
		}else{
			var scoredstate = ${expertHandlFileList.scoredstate};
			if(scoredstate==1){
				$("input[name='scoreInput']").removeAttr("disabled");

				$("#zjpy").removeAttr("disabled");
				$("#ajpj").removeAttr("disabled");
				$("#submitBtn").removeAttr("style display");
				$("#chickAnjuan").hide();
				$("#fileCodeInput").attr("disabled","disabled");
				$("textarea").removeAttr("disabled");
				$("#fileCodeInput").val('${ expertHandlFileList.filecode}');
				$("#yxdxanlituijian").attr("disabled",false);
				$("#ajpjYxdxanlituijian").attr("disabled",false);

				/* 页面初始化加载设置隐藏 input输入框，当用户选择一票否决和无须评查  */
				if(xzcfVue.scoringIndexList != null && xzcfVue.scoringIndexList.expertHandlIndexScoreList != null ){
					for( var i=0;i< xzcfVue.scoringIndexList.expertHandlIndexScoreList.length;i++){
						if(xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].inCheckValue ==1 ||xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].voteDownValue ==1){
							var checkitem =".checkitem"+i;
							$(checkitem).attr("disabled","disabled");
							if(xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].inCheckValue ==1){
								//如果选了无须评查
								xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].resultScore=xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].indexscore;
								var isVoteDownid = '#isVoteDown'+i;
								$(isVoteDownid).attr("disabled","disabled");
							}
							if(xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].voteDownValue ==1){
								//若果选了一票否决
								xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].resultScore=0;
								var isInCheckid = '#isInCheck'+i;
								$(isInCheckid).attr("disabled","disabled");
							}
						}
					}
				}
			}else{
				/* $("#download").attr("disabled","disalbed"); */
				$("#look").attr("disabled","disalbed");
			}
		}
	})

</script>
<body>
	<div id="CommitVue">
		<div class="center_weizhi">当前位置：专家评分 - 委员评审案卷 - ${filetypeName}</div>
		<div class="center">
			<div class="center_list">
				<input id="pageNum"  type="hidden" value="${pageNum}" >
		 		<input id="expertFileId"  type="hidden" value="${expertFileId}" >
		 		<input id="fileId"  type="hidden" value="${expertHandlFileList.fileid}" >
				<button    class="btn btn-primary"  style="font-size:16px;margin-top:10px;width:98px;height:40px;  padding:6px 34px;margin-right: 10px;float: right;" onclick="macroMgr.onLevelOneMenuClick(null, 'zjpf.do')">返回</button>
		 		<div class="dingwei">
					<button class="btn btn-danger" style='padding:9px 34px;margin-right:14px'>卷面评查</button>
					<c:choose>
	         			<c:when test ="${expertHandlFileList.scoredstate == 0 or expertHandlFileList.scoredstate == 2 }">
							<button disabled class="btn btn-default" style='padding:9px 34px;margin-right:14px'>实体和程序评查</button>
						</c:when>
						<c:otherwise>
							<button class="btn btn-default" style='padding:9px 34px;margin-right:14px' onclick="macroMgr.onLevelTwoMenuClick(null, '/zjpf/entityCommitView.do?id=${expertFileId}&pageNum=${pageNum}')">实体和程序评查</button>
						</c:otherwise>
					</c:choose>
					<p style="font-weight: 800; font-size: 16px">使用说明：</p>
					<p>1.本标准用于评查案卷卷面内容。</p>
					<p>2.可以根据证据类型和所发文书种类确定评查项目。</p>
					<p>3.卷面分=50*对应评查项目得分之和/参与评查项目标准分之和。</p>
					<p>4.内容完整、规范、正确的，得相应分值。不完整、不规范或者不正确的，不得分。</p>
					<p class="ceshi">5.提示：无需评查是指根据案情需要，无需对此项文书进行评查！</p>
					<div class="dingwei">案卷或材料：<span style="color:#009CCD;font-size:14px; font-weight:400;" id ="filecode">
					{{scoringIndexList.filecode }}
					<span style="color:red;font-size:16px; font-weight:bold;"><c:choose><c:when test="${expertHandlFileList.closed==1}">（已结案）</c:when><c:when test="${expertHandlFileList.closed==0}">（未结案）</c:when> </c:choose></span>
					</span>
					<c:if test ="${sessionScope.sa_session.sysStatus == '5' }">
						<a v-if="scoringIndexList.fileurl != null && scoringIndexList.fileurl != ''" v-bind:href="scoringIndexList.downUrl" target="_Blank">
							<button id="download" class="btn btn-success btn-xs" style="padding:5px 12px;margin-left:10px" data-toggle="modal" data-target="#myModal">下载</button>
						</a>
						<span v-else style="color:red;font-size:16px; font-weight:bold;">（无案卷文件）</span>
						<c:if test="${expertHandlFileList.suffix== 'pdf'}">
							<button id="look" class="btn btn-info btn-xs" style="padding:5px 12px;margin-left:10px" data-toggle="modal" data-remote="${webpath}/jcpf/showFileModal.do?fastDFSUrl=+'${fastDFS}'+" data-target="#view">预览</button>
						</c:if>
					</c:if>
					<%-- <c:if test ="${expertHandlFileList.scoredstate == 0}">
						<button class="btn btn-info btn-xs" data-toggle="modal" data-target="#zeroScoreFile" id ="chickAnjuan">零分案卷</button>
					</c:if> --%>
					</div>
				</div>

		       	<form action="#" id ="zjpfForm" method="post">
		        	<table class="table table-bordered table-hover table-condensed">
						<thead>
							<tr>
								<td width="3%" height="30" bgcolor="#efefef">序号</td>
								<td width="7%" bgcolor="#efefef">评查项目</td>
								<td width="7%" bgcolor="#efefef">标准分</td>
								<!-- <td width="44%" bgcolor="#efefef"><span style="float: left;">评分细则</span><span style="float: right;margin-right: 70px;">得分值</span></td> -->
								 <td bgcolor="#efefef">评分细则</td>
								 <td width="70px" bgcolor="#efefef">专家A评分值</td>
					             <td width="70px" bgcolor="#efefef">专家A评审依据</td>
					             <td width="70px" bgcolor="#efefef">专家B评分值</td>
					             <td width="70px" bgcolor="#efefef">专家B评审依据</td>
					             <td width="155px" bgcolor="#efefef">打分值</td>
								<td width="5%" bgcolor="#efefef">一票否决</td>
								<td width="5%" bgcolor="#efefef">无需评查</td>
								<td width="5%" bgcolor="#efefef">得分</td>
								<td width="15%" bgcolor="#efefef">评审依据</td>
							</tr>
						</thead>
						<tbody  class="form-group">
							<tr v-for="(scoringIndex, index) in scoringIndexList.expertHandlIndexScoreList">
								<td height="30" align="center" >{{index+1}}</td>

								<!-- 评查项目 -->
								<td style="text-align: center">{{scoringIndex.indexname}}</td>

								<!-- 标准分 -->
								<td style="vertical-align:middle;">{{scoringIndex.indexscore}}</td>

								<!-- 评分细则 -->
								<td colspan="6" v-if="scoringIndex.expertHandlItemScoreList != null && scoringIndex.expertHandlItemScoreList.length !=0" style="padding:0;">
									<table width="100%" border="0" cellspacing="0" cellpadding="0" style="padding:0;">
										<tr v-for="(scoringItem, index1) in scoringIndex.expertHandlItemScoreList">
											<td style="border-bottom:solid 1px #ddd; border-right:solid 1px #ddd;vertical-align:middle;">
												<div style="vertical-align:middle; margin:0 5px 5px 0;">{{scoringItem.itemname}}</div>
											</td>

											<td style="width:70px; border-bottom:solid 1px #ddd; border-right:solid 1px #ddd; vertical-align:middle;align:center;">{{scoringItem.aexpertScore}}</td>
											<td :rowspan="scoringIndex.expertHandlItemScoreList.length" v-if="index1==0" style="width:70px; border-bottom:solid 1px #ddd; border-right:solid 1px #ddd; vertical-align:middle;">{{scoringIndex.aexpertReason}}</td>
                            				<td style="width:70px; border-bottom:solid 1px #ddd; border-right:solid 1px #ddd; vertical-align:middle;align:center;">{{scoringItem.bexpertScore}}</td>
                            				<td :rowspan="scoringIndex.expertHandlItemScoreList.length" v-if="index1==0" style="width:70px; border-bottom:solid 1px #ddd; border-right:solid 1px #ddd; vertical-align:middle;">{{scoringIndex.bexpertReason}}</td>

											<td style="border-bottom:solid 1px #ddd; width:155px;vertical-align:middle;">
												<div v-if="scoringItem.itemscore == 0" class="custom-checkbox text-center" >
													<input name="scoreInput" type="checkbox" :id="index+'scoreInput'+index1"
														@change="changeCheckBoxNegative(index, index1)" v-model="scoringItem.score"
														style="width:16px; height:16px; display: table-cell;vertical-align: middle; text-align: center;">
													<label :for="'checkbox'+index" class="checkbox-blue" checked></label>
												</div>
												<div v-else :id="'expertItem'+index1+index" style="margin:0 0 5px 0; float:left;">
													<div v-if="scoringItem.isOperator==1" style="float:left; padding:5px 2px 7px 3px; color:red; font-size:20px;">＋</div>
													<div v-if="scoringItem.isOperator!=1" style="float:left; padding:0 0 3px 0; margin:0; color:red; font-size:26px;">－</div>

													<div v-if="scoringIndex.voteDownValue == 1 || scoringIndex.voteDownValue == 2 || scoringIndex.inCheckValue == 1"
														style="float:right; padding-top:4px;">
														<input disabled name="scoreInput" @input="updateItem(index,index1,scoringItem.itemscore,scoringItem.temItemId)"
															v-model="scoringItem.score" type="text" :class="'checkitem'+index" autocomplete="off"
															class="form-control inputDisabled" placeholder="请输入初评得分" style="width:125px;">
													</div>
													<div v-else style="float:right; padding-top:4px;">
														<input name="scoreInput" @input="updateItem(index,index1,scoringItem.itemscore,scoringItem.temItemId)"
															:id="index+'scoreInput'+index1" v-model="scoringItem.score" type="text" :class="'checkitem'+index" autocomplete="off"
															class="form-control inputDisabled" placeholder="请输入初评得分" style="width:125px;">
													</div>

													<div style="color:red; font-size: 12px;">{{scoringItem.validatorMessage}}</div>
												</div>
											</td>
										</tr>
									</table>
								</td>

			             		<!-- 一票否决 -->
								<td style="vertical-align:middle;">
									<div class="custom-checkbox text-center" v-if="scoringIndex.isVoteDown == 1" >
										<input name="scoreInput" type="checkbox" :id="'isVoteDown'+index"
											@change="changeCheckBoxCli(index,'1')" v-model="scoringIndex.voteDownValue"
											style="width:16px; height:16px; display: table-cell;vertical-align: middle; text-align: center;">
										<label :for="'checkbox'+index" class="checkbox-blue" checked>一票否决</label>
									</div>
								</td>

								<!-- 无需评查 -->
								<td style="vertical-align:middle;">
									<div class="custom-checkbox text-center" v-if="scoringIndex.isInCheck ==1">
										<input name="scoreInput" type="checkbox" :id="'isInCheck'+index"
		 									@change="changeCheckBoxCli(index,'2')"  v-model="scoringIndex.inCheckValue"
											style="width:16px; height:16px;  display: table-cell;vertical-align: middle; text-align: center; ">
										<label for="checkbox1" class="checkbox-blue" checked>无需评查</label>
									</div>
								</td>

								<!-- 得分 -->
								<td style="vertical-align:middle;">
									<div style="width:120px;float: right;">
					           			<input type="text"  disabled="disabled" v-model="scoringIndex.resultScore" class="form-control">
					           		</div>
								</td>

								<!-- 评审依据 -->
								<td >
									<textarea maxlength="1000" @input="updateCommon(index)" :id="'comment'+index" :rows="scoringIndex.expertHandlItemScoreList.length"
										class="form-control" v-model="scoringIndex.comment" placeholder="请输入评审依据，长度不能超过1000个字符" style="width:100%;">
									</textarea>
									<div style="color:red; font-size: 12px;">{{scoringIndex.validatorMessage}}</div>
								</td>
							</tr>
							<tr>
								<td rowspan="2"></td>
								<td rowspan="2"><strong>合计</strong></td>
								<td colspan="7"><strong>参与评查得分</strong></td>
								<td colspan="2"><strong>参与评查标准分</strong></td>
								<td colspan="2"><strong>卷面分</strong></td>
							</tr>
							<tr>
								<td colspan="7">{{inputScore}}</td>
								<td colspan="2">{{totalScore}}</td>
								<td colspan="2">
									<div style="width:120px;float: left;">
					           			<input type="text" disabled v-model="scoringIndexList.paperscore" class="form-control">
					           		</div>
								</td>
							</tr>
							<tr>
								<td height="30" colspan="15">
									<textarea maxlength="2000" v-model="scoringIndexList.problemRemark" rows="5" class="form-control" id="problem_remark" placeholder="疑难（争议）问题描述">${expertHandlFileList.problemRemark}</textarea>
								</td>
							</tr>
							<tr>
								<td colspan="2">专家A</br>疑难（争议）问题描述</td>
								<td height="30" colspan="13">
									<textarea maxlength="2000" v-model="scoringIndexList.problemRemarkA" rows="5" class="form-control" id="problem_remarkA" placeholder="专家A 疑难（争议）问题描述"></textarea>
								</td>
							</tr>
							<tr>
								<td colspan="2">专家B</br>疑难（争议）问题描述</td>
								<td height="30" colspan="13">
									<textarea maxlength="2000" v-model="scoringIndexList.problemRemarkB" rows="5" class="form-control" id="problem_remarkB" placeholder="疑难（争议）问题描述"></textarea>
								</td>
							</tr>
							<tr>
								<td height="30" colspan="9" style="line-height: 3.1">
									<div class="col-sm-12" style="padding-left: 30px">
									<input id="yxdxanlituijian" name="yxdxanlituijian" type="checkbox" disabled style="margin-right: 10px">优秀案卷推荐</div>
									<div class="col-sm-12" style="display: none;padding-left: 30px" id ="yxdxAnLiTuiJianReviews1"  >
										<table>
											<c:forEach items="${tcDictionaryList}" var="item" varStatus="status">
												<tr>
													<input  type="checkbox" name="yxaj" value="${item.code}" >&nbsp;${item.name} &nbsp; &nbsp;
													<c:if test="${status.count%4==0}"><br></c:if>
												</tr>
											</c:forEach>
										</table>
										<textarea disabled v-model="scoringIndexList.yxdxanlituijianreviews" rows="5" class="form-control" id="zjpy" disabled></textarea>
									</div>
								</td>
							</tr>
							<tr>
								<td height="30" colspan="9" style="line-height: 3.1">
									<div class="col-sm-12" style="padding-left: 30px">
										<input id="ajpjYxdxanlituijian" name="ajpjYxdxanlituijian" type="checkbox" style="margin-right: 10px" disabled>较差案卷推荐</div>
									<div class="col-sm-12" style="display: none;padding-left: 30px" id ="ajpjYxdxAnLiTuiJianReviews1"  >
										<table>
											<c:forEach items="${jcajDictionaryList}" var="item" varStatus="status">
												<tr>
													<input  type="checkbox" name="jcaj" value="${item.code}" >&nbsp;${item.name}&nbsp; &nbsp;
													<c:if test="${status.count%4==0}"><br/></c:if>
												</tr>
											</c:forEach>
										</table>
										<textarea v-model="scoringIndexList.ajpjYxdxanlituijianreviews" rows="5" class="form-control" id="ajpj" disabled></textarea>
									</div>
								</td>
							</tr>
							<tr>
								<td height="30" colspan="9">
									<div class="col-sm-12" style="padding-left:30px">
									<input id="noTuiJian" name ="noTuiJian" type="checkbox"  v-on:click="noCheckBoxClick()" style="margin-right: 10px" >不推荐为典型案例
								</div>
								</td>
							</tr>
						</tbody>
					</table>
					<tr>
						<td height="30" colspan="9">
							<div  class="col-sm-12" style="padding-left: 3px;">
								<input id="fileMaterials" name="fileMaterials" type="checkbox" onchange="changeFileMaterials()"><span style="font-size: 14px; color:#DF1912">&nbsp;案卷材料严重不全，导致无法全面评价案卷实体和程序问题</span></div>
							<div class="col-sm-12" style="padding-left: 3px;line-height: 40px;">
								<h4>具体情形：</h4>
								<p>案卷中只有三项或不足三项，无相关决定性文书，导致无法全面评价案卷规范性、合法性。</p>
							</div>
						</td>
					</tr>
				</form>
		 		<div class="submit">
 				</div>
				<!--  附件查看 -->
				<div class="modal fade" id="view" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
					<div class="modal-dialog modal-lg">
						<div class="modal-content"></div>
					</div>
				</div>

			</div>
		</div>
	</div>
</body>
</html>
