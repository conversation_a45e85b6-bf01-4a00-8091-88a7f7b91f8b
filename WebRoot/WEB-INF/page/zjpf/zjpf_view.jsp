<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
	<script type="text/javascript">
		function onLevelTwoMenuClick() {
			var expertFileId = encodeURIComponent('${expertFileId}');
			var pageNum = encodeURIComponent('${pageNum}');
			var gopath = encodeURIComponent('${gopath}');
			var pageIndex = encodeURIComponent('${pageIndex}');
			var areaCodeLeave = encodeURIComponent('${areaCodeLeave}');
			var fileCode = encodeURIComponent('${fileCode}');
			var isInCheck = encodeURIComponent('${isInCheck}');

			var url = 'zjpf/entityAScore.do?id=' + expertFileId +
					'&status=1' +
					'&pageNum=' + pageNum +
					'&gopath=' + gopath +
					'&pageIndex=' + pageIndex +
					'&areaCodeLeave=' + areaCodeLeave +
					'&fileCode=' + fileCode +
					'&isInCheck=' + isInCheck;

			macroMgr.onLevelTwoMenuClick(null, url);
		}
	</script>
</head>
<body>
	<div id="ExpertVue">
		<div class="center_weizhi">当前位置：专家评分 - 专家评审案卷 - ${filetypeName}</div>
		<div style="position: absolute;top: 131px;width: 98%;margin: 0 20px;padding: 5px 0 0 19px;background: #ffffff;">
			<button class="btn btn-danger" style="padding:9px 34px;margin-right:14px" >规范性评查</button>
			<button class="btn btn-default" style="padding:9px 34px;margin-right:14px" onclick="macroMgr.onLevelTwoMenuClick(null, 'zjpf/entityAView.do?id=${expertFileId}&pageNum=${pageNum}')">合法性评查</button>
			<button class="btn btn-default" style='padding:9px 34px;margin-left:14px' onclick="macroMgr.onLevelTwoMenuClick(null, 'zjpf/plusesView.do?id=${expertFileId}&pageNum=${pageNum}')">加分项</button>

<%--			<c:choose>--%>
<%--				<c:when test ="${expertHandlFileList.scoredstate == 0 or expertHandlFileList.scoredstate == 2 or expertHandlFileList.fileMaterials ==1 }">--%>
<%--					<button disabled class="btn btn-default" style='padding:9px 34px'>实体和程序评查</button>--%>
<%--				</c:when>--%>
<%--				<c:otherwise>--%>
<%--&lt;%&ndash;					<button class="btn btn-default" style='padding:9px 34px' onclick="macroMgr.onLevelTwoMenuClick(null, 'zjpf/entityAScore.do?id=${expertFileId}&status=1&pageNum=${pageNum}&gopath=${gopath}&pageIndex=${pageIndex}&areaCodeLeave=${areaCodeLeave}&fileCode=${fileCode}&isInCheck=${isInCheck}')">实体和程序评查</button>&ndash;%&gt;--%>
<%--					<button class="btn btn-default" style='padding:9px 34px' onclick="onLevelTwoMenuClick()">实体和程序评查</button>--%>
<%--				</c:otherwise>--%>
<%--			</c:choose>--%>
<%--				<c:choose>--%>
<%--					<c:when test ="${expertHandlFileList.scoredstate == 0 or expertHandlFileList.scoredstate == 2 or expertHandlFileList.scoredstate == 3 or expertHandlFileList.fileMaterials ==1 }">--%>
<%--						<button disabled class="btn btn-default" style='padding:9px 34px;margin-left:14px'>加分项</button>--%>
<%--					</c:when>--%>
<%--					<c:otherwise>--%>
<%--						<button class="btn btn-default" style='padding:9px 34px;margin-left:14px' onclick="macroMgr.onLevelTwoMenuClick(null, 'zjpf/plusesView.do?id=${expertFileId}&pageNum=${pageNum}')">加分项</button>--%>
<%--					</c:otherwise>--%>
<%--				</c:choose>--%>

<%--
			<button class="btn btn-default" style="padding:9px 34px"  onclick="macroMgr.onLevelTwoMenuClick(null, '/zjpf/entityAView.do?id=${expertFileId}&pageNum=${pageNum}')">实体和程序评查</button>
--%>
			<%--专家--%>
			<c:if test ="${userTypeCode != '4' }">
				<button class="btn btn-primary"  onclick="macroMgr.onLevelOneMenuClick(null, 'zjpf.do')"  style="font-size:16px;width:98px;height: 40px; padding: 6px 34px; margin-right: 10px;float: right;" >返回</button>
			</c:if>
			<%--管理员 --%>
			<c:if test ="${userTypeCode == '4' }">
				<c:if test="${gopath != null and gopath !='' }">

					<button class="btn btn-primary"  onclick="macroMgr.onLevelOneMenuClick(null,'zlpf.do?pageIndex=${pageIndex}&areaCodeLeave=${areaCodeLeave}&fileCode=${fileCode}&isInCheck=${isInCheck}&pageNum=${pageNum}&gopath=${gopath}')"  style="font-size:16px;width:98px;height: 40px; padding: 6px 34px; margin-right: 10px;float: right;" >返回</button>
				</c:if>
				<c:if test="${gopath == null or gopath =='' }">
					<button class="btn btn-primary"  onclick="macroMgr.onLevelOneMenuClick(null, 'zlpf.do')"  style="font-size:16px;width:98px;height: 40px; padding: 6px 34px; margin-right: 10px;float: right;" >返回</button>
				</c:if>

			</c:if>
			<div>
				<span style="font-size:14px; font-weight:400;color:#333333">案卷或材料：</span><span style="color:#009CCD;font-size:14px; font-weight:400;" id ="filecode">
						{{scoringIndexList.filecode }}
						<span style="color:red;font-size:16px; font-weight:bold;"><c:choose><c:when test="${expertHandlFileList.closed==1}">（已结案）</c:when><c:when test="${expertHandlFileList.closed==0}">（未结案）</c:when> </c:choose></span>
						</span>
				<c:if test ="${sessionScope.sa_session.sysStatus == '5' }">
					<a v-if="scoringIndexList.fileurl != null && scoringIndexList.fileurl != ''" v-bind:href="scoringIndexList.downUrl" target="_Blank">
						<button id="download" class="btn btn-info btn-xs" style="padding:5px 12px;margin-left:10px" data-toggle="modal" data-target="#myModal">下载</button>
					</a>
					<span v-else style="color:red;font-size:16px; font-weight:400;">（无案卷文件）</span>
					<c:if test="${expertHandlFileList.suffix== 'pdf'}">
						<button id="look" class="btn btn-success btn-xs" style="padding:5px 12px;margin-right:10px;margin-left:10px" data-toggle="modal" data-remote="${webpath}/jcpf/showFileModal.do?fastDFSUrl=+'${fastDFS}'+" data-target="#view">预览</button>
					</c:if>
				</c:if>
				<img  src='${webpath}/static/img/chaxun.png'/>
				<select id="indexName" onchange="change(this.value)" style="margin-top: 10px"></select>

			</div>

		</div>
		<div class="center" id="scrollBarCenter">
			<div class="center_list">
				<input id="pageNum"  type="hidden" value="${pageNum}" >
		 		<input id="expertFileId"  type="hidden" value="${expertFileId}" >
		 		<input id="fileId"  type="hidden" value="${expertHandlFileList.fileid}" >
				<input id="expertId"  type="hidden" value="${expertHandlFileList.expertId}" >
				<input id="handlType"  type="hidden" value="${expertHandlFileList.handlType}" >
				<input id="problemRemark" type="hidden" value="${expertHandlFileList.problemRemark}">
				<div class="dingwei" style="font-weight: 400;padding-top:0; font-size: 14px;color: #333333">

					<p style="font-weight: 600; font-size: 14px;color: #333333">使用说明：</p>
					<p>1.本标准用于评查案卷卷面内容。</p>
					<p>2.可以根据证据类型和所发文书种类确定评查项目。</p>
					<p>3.卷面分 = 50*对应评查项目得分之和/参与评查标准分之和-基本要素扣分值之和。</p>
					<p>4.内容完整、规范、正确的，得相应分值。不完整、不规范或者不正确的，不得分。</p>
					<p class="ceshi">5.提示：无需评查是指根据案情需要，无需对此项文书进行评查！</p>

				</div>

		       	<form action="#" id ="zjpfForm" method="post">

		        	<table class="table table-bordered table-hover table-condensed">
						<thead>
							<tr>
								<td width="3%" height="30" bgcolor="#efefef">序号</td>
								<td width="7%" bgcolor="#efefef">评查项目</td>
								<td width="7%" bgcolor="#efefef">标准分</td>
								<td width="44%" bgcolor="#efefef"><span style="text-align: center">评分细则</span><span style="float: right;margin-right: 70px;">得分值</span></td>
								<td width="5%" bgcolor="#efefef">一票否决</td>
								<td width="5%" bgcolor="#efefef">无需评查</td>
								<td width="5%" bgcolor="#efefef">得分</td>
								<td width="15%" bgcolor="#efefef">评审依据</td>
							</tr>
						</thead>
						<tbody  class="form-group">
							<tr v-for="(scoringIndex, index) in scoringIndexList.expertHandlIndexScoreList"  :class="scoringIndex.className" :style="scoringIndex.isError==1?'background:#ECD9E0':''">
								<td height="30" align="center" >{{index+1}}</td>

								<!-- 评查项目 -->
								<td  style="text-align: center">
									{{scoringIndex.indexname}}<span v-if="scoringIndex.isOperator=='0'" style="color:red;">（扣分项）</span>
									<p v-if="scoringIndex.isError==1" style="margin:0 auto;width: 67px;height: 24px;background: #DF1912;border-radius: 3px;">
										<a href="#" style="line-height: 24px;font-size:14px;font-weight: 400;color: #ffffff" v-on:click="showErrorMsg_view(scoringIndex)">查看争议</a>
									</p>
								</td>

								<!-- 标准分 -->
								<td style="text-align: center;vertical-align:middle;">{{scoringIndex.indexscore}}</td>

								<!-- 评分细则 -->
								<td v-if="scoringIndex.expertHandlItemScoreList != null && scoringIndex.expertHandlItemScoreList.length !=0" style="padding:5px;">
									<table width="100%" border="0" cellspacing="0" cellpadding="0" style="padding:0;">
										<tr v-for="(scoringItem, index1) in scoringIndex.expertHandlItemScoreList" :class="scoringIndex.className">
											<td style="border-bottom:solid 1px #ddd; border-right:solid 1px #ddd;vertical-align:middle;">
												<div style="vertical-align:middle; margin:0 5px 5px 5px;">{{scoringItem.itemname}}</div>
											</td>
											<td style="border-bottom:solid 1px #ddd; width:155px;vertical-align:middle;">
												<div v-if="scoringItem.itemscore == 0" class="custom-checkbox text-center" >
													<input disabled name="scoreInput" type="checkbox" :id="'isVoteDown'+index" v-model="scoringItem.score"
														style="width:16px; height:16px; display: table-cell;vertical-align: middle; text-align: center;margin-left: 10px">
													<label :for="'checkbox'+index" class="checkbox-blue" checked></label>
												</div>
												<div v-else :id="'expertItem'+index1+index" style="margin:0 0 5px 0; float:left;">
													<div v-if="scoringItem.isOperator==1" style="float:left; padding:5px 2px 7px 3px; color:red; font-size:20px;">＋</div>
													<div v-if="scoringItem.isOperator!=1" style="float:left; padding:0 0 3px 0; margin:0; color:red; font-size:26px;">－</div>

													<div style="float:right; padding-top:4px;">
														<input disabled name="scoreInput" @input="updateItem(index,index1,scoringItem.itemscore,scoringItem.temItemId)"
															v-model="scoringItem.score" type="text" :class="'checkitem'+index" autocomplete="off"
															class="form-control inputDisabled" placeholder="请输入初评得分" style="width:125px;">
													</div>
												</div>
											</td>
										</tr>
									</table>
								</td>

			             		<!-- 一票否决 -->
								<td style="vertical-align:middle;">
									<div class="custom-checkbox text-center" v-if="scoringIndex.isVoteDown == 1" >
										<input disabled name="scoreInput" type="checkbox" :id="'isVoteDown'+index" v-model="scoringIndex.voteDownValue"
											style="margin-left: 10px;width:16px; height:16px; display: table-cell;vertical-align: middle; text-align: center;">
										<label :for="'checkbox'+index" class="checkbox-blue" checked>一票否决</label>
									</div>
								</td>

								<!-- 无需评查 -->
								<td style="vertical-align:middle;">
									<div class="custom-checkbox text-center" v-if="scoringIndex.isInCheck ==1">
										<input disabled name="scoreInput" type="checkbox" :id="'isInCheck'+index" v-model="scoringIndex.inCheckValue"
											style="margin-left: 10px;width:16px; height:16px;  display: table-cell;vertical-align: middle; text-align: center; ">
										<label for="checkbox1" class="checkbox-blue" checked>无需评查</label>
									</div>
								</td>
								<!-- 得分 -->
								<td style="vertical-align:middle;">
									<div style="width:120px;float: right;">
					           			<input type="text"  disabled="disabled" v-model="scoringIndex.resultScore" class="form-control">
					           		</div>
								</td>

								<!-- 评审依据 -->
								<td >
									<textarea disabled @input="updateCommon(index)" :id="'comment'+index" :rows="scoringIndex.expertHandlItemScoreList.length"
										class="form-control" v-model="scoringIndex.comment" placeholder="请输入评审依据" style="width:100%;">
									</textarea>
								</td>
							</tr>
							<tr>
								<td rowspan="2"></td>
								<td rowspan="2">合计</td>
								<td colspan="2">参与评查得分</td>
								<td colspan="2">参与评查标准分</td>
								<td colspan="2">卷面分</td>
							</tr>
							<tr>
								<td colspan="2">{{inputScore}}</td>
								<td colspan="2">{{totalScore}}</td>
								<td colspan="2">
									<div style="width:120px;float: right;">
					           			<input type="text" disabled v-model="scoringIndexList.paperscore" class="form-control">
					           		</div>
								</td>
							</tr>
							<%--<tr>--%>
								<%--<td height="30" colspan="15">--%>
									<%--<textarea maxlength="2000" v-model="scoringIndexList.problemRemark" rows="5" class="form-control" id="problem_remark" placeholder="疑难（争议）问题描述">${expertHandlFileList.problemRemark}</textarea>--%>
								<%--</td>--%>
							<%--</tr>--%>
							<%--<tr>--%>
			           			<%--<td height="30" colspan="9" style="line-height: 3.1">--%>
									<%--<div style="padding-left:30px">--%>
			           				<%--<input id="yxdxanlituijian" name="yxdxanlituijian" type="checkbox" style="margin-right: 10px" disabled>优秀案卷推荐--%>
									<%--</div><div class="col-sm-12" style="display: none;padding-left:30px" id ="yxdxAnLiTuiJianReviews1"  >--%>
										<%--<table>--%>
											<%--<c:forEach items="${tcDictionaryList}" var="item" varStatus="status">--%>
												<%--<tr>--%>
													<%--<input  type="checkbox" name="yxaj" value="${item.code}" >&nbsp;${item.name} &nbsp; &nbsp;--%>
													<%--<c:if test="${status.count%4==0}"><br></c:if>--%>
												<%--</tr>--%>
											<%--</c:forEach>--%>
										<%--</table>--%>
		             					<%--<textarea  v-model="scoringIndexList.yxdxanlituijianreviews" rows="5" class="form-control" id="zjpy" disabled></textarea>--%>
									<%--</div>--%>
		             			<%--</td>--%>
							<%--</tr>--%>
							<%--<tr>--%>
								<%--<td height="30" colspan="9" style="line-height: 3.1">--%>
									<%--<div class="col-sm-12" style="padding-left:30px">--%>
									    <%--<input id="ajpjYxdxanlituijian" name="ajpjYxdxanlituijian" type="checkbox" style="margin-right: 10px"  disabled>较差案卷推荐--%>
									<%--</div>--%>
									<%--<div class="col-sm-12" style="display: none;padding-left:30px" id ="ajpjYxdxAnLiTuiJianReviews1"  >--%>
										<%--<table>--%>
											<%--<c:forEach items="${jcajDictionaryList}" var="item" varStatus="status">--%>
												<%--<tr>--%>
													<%--<input  type="checkbox" name="jcaj" value="${item.code}" ><span style="margin: 0 30px 0 10px;">${item.name}</span>--%>
													<%--<c:if test="${status.count%4==0}"><br/></c:if>--%>
												<%--</tr>--%>
											<%--</c:forEach>--%>
										<%--</table>--%>
										<%--<textarea v-model="scoringIndexList.ajpjYxdxanlituijianreviews" rows="5" class="form-control" id="ajpj" style="font-size: 12px;color:#333333" disabled></textarea>--%>
									<%--</div>--%>
								<%--</td>--%>
							<%--</tr>--%>
							<%--<tr>--%>
								<%--<td height="30" colspan="9">--%>
									<%--<div class="col-sm-12" style="padding-left:30px">--%>
									<%--<input id="noTuiJian" name ="noTuiJian" type="checkbox"  v-on:click="noCheckBoxClick()" style="margin-right: 10px"  >不推荐为典型案例--%>
									<%--</div>--%>
								<%--</td>--%>
							<%--</tr>--%>
						</tbody>
					</table>
					<tr>
						<td height="30" colspan="9">
							<div  class="col-sm-12" style="padding-left: 3px;">
								<input id="fileMaterials" name="fileMaterials" type="checkbox" onchange="changeFileMaterials()"><span style="font-size: 14px; color:#DF1912">&nbsp;案卷材料严重不全，导致无法全面评价案卷实体和程序问题</span></div>
							<div class="col-sm-12" style="padding-left: 3px;line-height: 40px;">
								<h4>具体情形：</h4>
								<p>案卷中只有三项或不足三项，无相关决定性文书，导致无法全面评价案卷规范性、合法性。</p>
							</div>
						</td>
					</tr>
				</form>
				<!--  附件查看 -->
				<div class="modal fade" id="view" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
					<div class="modal-dialog modal-lg">
						<div class="modal-content"></div>
					</div>
				</div>
			</div>
		</div>

		<%--查看异常信息--%>
		<div id="error_dialog_scoreView" style="height: 450px; display: flex; flex-direction: column; justify-content: space-between;">
			<input type="hidden" id="error_id_scoreView">
			<input type="hidden" id="isAOrB_scoreView">
			<span class="glyphicon glyphicon-info-sign" aria-hidden="true" style="text-align: center;font-size: 50px;color: #337ab7;"></span>
			<div style="width: 85%;padding-left: 20%;; font-size: 20px">
				<span style="font-weight: bold; font-size: 20px">该项对方专家</span>
				<span style="font-size: 20px;color: #337ab7" id="itemOption_scoreView">xxx</span>
				<span style="font-weight: bold; font-size: 20px">，评审依据为：</span>
				<span style="font-size: 20px;color: #337ab7" id="itemComme_scoreView">xxx</span>

			</div>

			<div style="text-align: center; padding: 20px;">
				<button id="error_close_scoreView" class="my-btn-gray"  OnClick="error_close_scoreView()" style="background: #ECF5FF; margin-right: 50px;margin-top: 10px; outline: none; border: 1px solid #C6E2FF;border-radius: 3px;font-size: 18px;color:#409EFF; width: 80px; height: 40px">关闭</button>
			</div>
		</div>

	</div>
</body>
<script type="text/javascript">
	//系统初始化函数
	$(function(){
		/*查看争议异常的弹窗*/
		$('#error_dialog_scoreView').dialog({
			title: "查看争议",
			width : "900",
			height : "450",
			autoOpen : false,
			resizable : false,
			modal : true,
			closed: true
		});
		// 隐藏dialog右上角叉号关闭按钮
		$('.panel-tool-close').hide();
	});

	//关闭查看异常的窗口
	function error_close_scoreView(){
		$('#error_dialog_scoreView').dialog("close")

		//清空专家否决选项
		$('#itemOption_scoreView').val('');
		//清空专家否决描述
		$('#itemComme_scoreView').val('');
		$('#isAOrB_scoreView').val('');
		$('#error_id_scoreView').val('');

	}

	$(".left_menu").hide();
	var firScolltop=0;
	var oldValue='';
	business.listenEnter();
	var pageNum = $("#pageNum").val();
	var scoringIndexList = null;
	var expertFileId = $("#expertFileId").val();
	var mater = '${expertHandlFileList.fileMaterials}'
	if (mater == '1'){
		$("#fileMaterials").attr("checked",true);

	}
	$.ajax({
		cache : true,
		type : "GET",
		async : false,
		//api/test/detail/behaviour/74
		url: WEBPATH+"/zjpf/getIndexList.do",
		data:{
			id:expertFileId
		},
		error : function(request) {
			swal("错误!","请求异常！", "error");
		},
		success : function(data) {
			if(data.result =='success'){
				scoringIndexList = data.data;
				for(var i=0;i<data.data.expertHandlIndexScoreList.length;i++){
					data.data.expertHandlIndexScoreList[i].className = 'class'+ data.data.expertHandlIndexScoreList[i].id;
					var index = data.data.expertHandlIndexScoreList[i];
					//先创建好select里面的option元素
					var option=document.createElement("option");
					//转换DOM对象为JQ对象,好用JQ里面提供的方法 给option的value赋值
					$(option).val('class' + index.id);
					$(option).text(index.indexname);
					$("#indexName").append(option);
				}
				//	console.log(scoringIndexList);
			}
		}
	});

	var xzcfVue = new Vue({
		el: '#ExpertVue',
		data: {
			scoringIndexList:scoringIndexList,
		},
		computed:{
			//分母:参与评查标准分
			totalScore:function(){
				var sum = 0;
				this.scoringIndexList.expertHandlIndexScoreList.forEach(function(item){

					//inCheckValue无需评查,修改为无需评查默认满分了
					// if(item.inCheckValue != 1) {
					// }
					//20241110,加分项也加到[参与评查标准分]
					if((this.scoringIndexList.closed==0? item.indexname == '结案审批表':false) || (item.indexscore=="null"||item.indexscore==="")|| item.indexname == null || item.indexname == "" /*|| item.indexname == '加分项'*/ || item.isOperator=='0'){
						sum += 0;
					}else{
						sum += item.indexscore;
					}
				});
				return sum;
			},
			//分子:得分,参与评查得分,输入分数
			inputScore:function(){

				var inpuSco = 0;
				this.scoringIndexList.expertHandlIndexScoreList.forEach(function(item){
					if(item.indexname == null || item.indexname == "" /*|| item.indexname == '加分项'*/ || item.isOperator=='0'){
						inpuSco += 0;
					}else if (item.resultScore!=''){
						inpuSco += item.resultScore;
					}

				});
				if (inpuSco == null || inpuSco == "" || inpuSco == 0){
					return 0.00;
				}else {
					return parseFloat(inpuSco).toFixed(2);;
				}
			}
		},
		methods: {
			showErrorMsg_view(obj){
				$.ajax({
					type:"post",
					url:WEBPATH+'/zjpf/getOtherExpertScore.do',
					data:{
						fileId:$("#fileId").val(),
						expertId:$("#expertId").val(),
						handlType:$("#handlType").val(),
						indexId:obj.indexid
					},
					success:function(data){  //成功
						if (data.result=="success") {

							//如果不为空,说明对面选择了一票否决或者无需评查,否则就展示对方的评分
							if (data.data.option != '' && data.data.option != null && data.data.option!=undefined){
								$('#itemOption_scoreView').text(data.data.option);
							}else {
								$('#itemOption_scoreView').text("评分值:"+data.data.expertSocre);
							}

							if (data.data.comme != '' && data.data.comme != null && data.data.comme!=undefined){
								$('#itemComme_scoreView').text(data.data.comme);
							}else {
								$('#itemComme_scoreView').text("无内容");
							}

							//主键ID
							$('#error_id_scoreView').val(data.data.id);
							//现在是A专家还是B砖家
							$('#isAOrB_scoreView').val(data.data.isAOrB);
							//打开查看异常的弹窗
							$( "#error_dialog_scoreView" ).dialog( "open" );
						}else{
							swal("信息", data.message, "error");
						}
					}
				});
			}
		}
	});


	function change(value) {
		var mao;
		var pos;
		var poshigh;
		mao = $("." + value);
		pos = mao.offset().top;
		poshigh = mao.height();
		//alert(poshigh)
		var temp=pos+firScolltop;
		if(oldValue!=''){
			// alert("old:"+$("." + oldValue).offset().top);
			temp=temp-$("." + oldValue).offset().top;
		}
		// alert(pos);
		$("#scrollBarCenter").animate({scrollTop: temp - 210})
		// alert(mao.offset().top)
		firScolltop = temp;
		oldValue=value;
	}



	//处理浮点型数据相减
	function handFloat(biaozhunfen,subScore){
		var r1,r2,m,n;
		try{r1=biaozhunfen.toString().split(".")[1].length}catch(e){r1=0};
		try{r2=subScore.toString().split(".")[1].length}catch(e){r2=0};
		m=Math.pow(10,Math.max(r1,r2));
		n = (r1 >= r2) ? r1 : r2;
		return ((biaozhunfen * m - subScore * m) / m).toFixed(n);
	}

	/**
	 * 回显案卷类型
	 */
	// var a = xzcfVue.scoringIndexList.yxdxanlituijian;
	// $(function () {
	// 	if(xzcfVue.scoringIndexList.yxdxanlituijian=="1"){
	// 		//$('#yxdxanlituijian').attr('checked', true)
	//
	// 		$("[name = yxdxanlituijian]:checkbox").attr("checked", true);
	// 		$("#yxdxAnLiTuiJianReviews1").css("display","block");
	//
	// 		//优秀案卷选项回显
	// 		var checkArray = $("input[name='yxaj']");
	// 		var codes = xzcfVue.scoringIndexList.ajpjYxdxanlituijianCode;
	// 		if(codes != null && codes != "" ){
	// 			var myArray=codes.split(",");
	// 			for (var i = 0; i < myArray.length; i++) {
	// 				//获取所有复选框对象的value属性，然后，用checkArray[i]和他们匹配，如果有，则说明他应被选中
	// 				$.each(checkArray, function (j, checkbox) {
	// 					//获取复选框的value属性
	// 					var checkValue=$(checkbox).val();
	// 					//console.log(j+"----"+checkValue)
	// 					if (myArray[i] == checkValue) {
	// 						$(checkbox).attr("checked", true);
	// 					}
	// 				})
	// 			}
	// 		}
	// 		//禁止较差推荐
	// 		$("#ajpjYxdxanlituijian").attr("disabled","disabled");
	// 		$("#ajpjYxdxAnLiTuiJianReviews1").css("display", "none");
	// 		//禁止不推荐为典型案例
	// 		$("#noTuiJian").attr("disabled","disabled");
	// 	}
	//
	//
	// 	var ajpja = xzcfVue.scoringIndexList.ajpjYxdxanlituijian;
	// 	if(xzcfVue.scoringIndexList.ajpjYxdxanlituijian=="1"){
	// 		//$('#ajpjYxdxanlituijian').attr('checked', true)
	// 		$("[name = ajpjYxdxanlituijian]:checkbox").attr("checked", true);
	// 		$("#ajpjYxdxAnLiTuiJianReviews1").css("display","block");
	// 		//较差案卷选项回显
	// 		var checkArray = $("input[name='jcaj']");
	// 		var codes = xzcfVue.scoringIndexList.jcajtuijianCode;
	// 		if(codes != null && codes != "" ){
	// 			var myArray=codes.split(",");
	// 			for (var i = 0; i < myArray.length; i++) {
	// 				//获取所有复选框对象的value属性，然后，用checkArray[i]和他们匹配，如果有，则说明他应被选中
	// 				$.each(checkArray, function (j, checkbox) {
	// 					//获取复选框的value属性
	// 					var checkValue=$(checkbox).val();
	// 					//console.log(j+"----"+checkValue)
	// 					if (myArray[i] == checkValue) {
	// 						$(checkbox).attr("checked", true);
	// 					}
	// 				})
	// 			}
	// 		}
	// 		//禁止优秀推荐
	// 		$("#yxdxanlituijian").attr("disabled","disabled");
	// 		$("#yxdxAnLiTuiJianReviews1").css("display", "none");
	// 		//禁止不推荐为典型案例
	// 		$("#noTuiJian").attr("disabled","disabled");
	// 	}
	//
	// 	// 不推荐为典型案例 回显
	// 	if(xzcfVue.scoringIndexList.noTuiJian=="1"){
	// 		$("#noTuiJian").attr("checked", true);
	// 		//禁止优秀推荐
	// 		$("#yxdxanlituijian").attr("disabled","disabled");
	// 		$("#yxdxAnLiTuiJianReviews1").css("display", "none");
	// 		//禁止较差推荐
	// 		$("#ajpjYxdxanlituijian").attr("disabled","disabled");
	// 		$("#ajpjYxdxAnLiTuiJianReviews1").css("display", "none");
	// 	}
	//
	// })


	//下载文件
	function xiaZaiAnJuan(){
		var fileId = $("#fileId").val();
		$.ajax({
			type:"post",
			url:WEBPATH+'/zjpf/xzcfExistFileUrl.do',
			data:{fileId:fileId},           //注意数据用{}
			success:function(data){  //成功
				if("yes" == data){
					window.location.href="${pageContext.request.contextPath}/zjpf/zjpfAnJuandown.do?fileId="+fileId;
					return false;
				}else if("no" == data){
					swal( "操作失败","该案卷不存在!", "error");
					return false;
				}else if("suffixerror" ==data){
					swal( "操作失败","该案卷上传数据格式有问题!", "error");
					return false;
				}
			}
		});
	}

	$(document).ready(function(){
		$(":input[type='checkbox']").attr("disabled","disabled");
		$(":input[type='text'], textarea").attr("disabled","disabled");

		//保存数据方法
		var status = '${status}';
		if(status==1){//status为1时是查看，否则为编辑
			$("#chickAnjuan").hide();
			$("#fileCodeInput").attr("disabled","disabled");
			var scoredstate = ${expertHandlFileList.scoredstate};
			// if(scoredstate==1 || scoredstate ==5){
			$("#fileCodeInput").val('${expertHandlFileList.filecode}');
			// }else{
			// 	/* $("#download").attr("disabled","disalbed"); */
			// 	$("#look").attr("disabled","disalbed");//预览按钮在评分后才能点击
			// }
		}else{
			var scoredstate = ${expertHandlFileList.scoredstate};
			if(scoredstate==1){
				$("input[name='scoreInput']").removeAttr("disabled");

				$("#zjpy").removeAttr("disabled");
				$("#ajpj").removeAttr("disabled");
				$("#submitBtn").removeAttr("style display");
				$("#chickAnjuan").hide();
				$("#fileCodeInput").attr("disabled","disabled");
				$("textarea").removeAttr("disabled");
				$("#fileCodeInput").val('${ expertHandlFileList.filecode}');
				// $("#yxdxanlituijian").attr("disabled",false);
				// $("#ajpjYxdxanlituijian").attr("disabled",false);

				/* 页面初始化加载设置隐藏 input输入框，当用户选择一票否决和无须评查  */
				if(xzcfVue.scoringIndexList != null && xzcfVue.scoringIndexList.expertHandlIndexScoreList != null ){
					for( var i=0;i< xzcfVue.scoringIndexList.expertHandlIndexScoreList.length;i++){
						if(xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].inCheckValue ==1 ||xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].voteDownValue ==1){
							var checkitem =".checkitem"+i;
							$(checkitem).attr("disabled","disabled");
							if(xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].inCheckValue ==1){
								//如果选了无须评查
								xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].resultScore=xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].indexscore;
								var isVoteDownid = '#isVoteDown'+i;
								$(isVoteDownid).attr("disabled","disabled");
							}
							if(xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].voteDownValue ==1){
								//若果选了一票否决
								xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].resultScore=0;
								var isInCheckid = '#isInCheck'+i;
								$(isInCheckid).attr("disabled","disabled");
							}
						}
					}
				}
			}else{
				/* $("#download").attr("disabled","disalbed"); */
				$("#look").attr("disabled","disalbed");
			}
		}
	});

	function loding(btn,itext){
		document.getElementById(btn).innerHTML = "加载.."
		document.getElementById(btn).disabled = "disabled"
		setTimeout(function () {
			document.getElementById(btn).innerHTML = itext;
			document.getElementById(btn).removeAttribute("disabled");
		},3000);
	}


	// $('#zjpfScoreView').quicksearch('table tbody tr', {
	//     'delay': 100,
	//     'bind': 'keyup keydown',
	//     'show': function() {
	//         if ($('#searcher').val() === '') {
	//             return;
	//         }
	//         $(this).addClass('show');
	//     },
	//     'onAfter': function() {
	//         if ($('#searcher').val() === '') {
	//             return;
	//         }
	//         if ($('.show:first').length > 0){
	//             $('html,body').scrollTop($('.show:first').offset().top);
	//         }
	//     },
	//     'hide': function() {
	//         $(this).removeClass('show');
	//     },
	//     'prepareQuery': function(val) {
	//         return new RegExp(val, "i");
	//     },
	//     'testQuery': function(query, txt, _row) {
	//         return query.test(txt);
	//     }
	// });

	$('#searcher').focus();
</script>
<style type="text/css">
	.pdfobject-container {
		width: 100%;
		height:580px;
		margin: 2em 0;
	}
	.textarea-success {
		border-top:green 1px solid;
		border-bottom:green 1px solid;
		border-left:green 1px solid;
		border-right:green 1px solid;
	}
	.textarea-error {
		border-top:red 1px solid;
		border-bottom:red 1px solid;
		border-left:red 1px solid;
		border-right:red 1px solid;
	}
	.ceshi{
		color: #DF1912;
		font-weight: 400;
		font-size: 14px;
	}
	.center_list{background-color: #ffffff;}
	#indexName{
		height: 40px;
		width: 270px;
		background: #FFFFFF;
		border: 1px solid #DCDFE6;
		border-radius: 3px;
		margin-left: 15px;}
	.table>thead>tr>td{line-height:3.1}
	.center{background: #FFFFFF;	position: absolute;left: 0px;top: 237px; width: 98%;margin: 0 20px;bottom: 35px;right: 5px;
	}
	.table-bordered>thead>tr>td{
		background-color: #F5F7FA;
		border: 1px solid #EBEEF5;
		text-align: center;
	}
	.panel-tool-close {
		display: none;
	}

</style>
</html>
