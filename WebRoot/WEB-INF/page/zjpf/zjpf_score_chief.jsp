<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
<style type="text/css">
	.pdfobject-container {
		width: 100%;
		height:580px;
		margin: 2em 0;
	}
	.textarea-success {
		border-top:green 1px solid;
		border-bottom:green 1px solid; 
		border-left:green 1px solid;
     	border-right:green 1px solid;
	}
	.textarea-error {
		border-top:red 1px solid;
		border-bottom:red 1px solid; 
		border-left:red 1px solid;
     	border-right:red 1px solid;
	}
	.ceshi{
		color: #DF1912;
		font-weight: 600;
		font-size: 14px;
	}
	#indexName{
		height: 40px;
		width: 270px;
		background: #FFFFFF;
		border: 1px solid #DCDFE6;
		border-radius: 3px;
		margin-left: 15px;}
	.center_list{background-color: #ffffff;}
	.center{	position: absolute;left: 0px; width: 98%;margin: 0 20px;bottom: 35px;right: 5px;background-color: #ffffff;}
	.table>thead>tr>td{line-height: 3.1}
	.table-bordered>thead>tr>td{background-color: #F5F7FA;border:1px solid #EBEEF5;text-align: center}
</style>
</head>
<script type="text/javascript">
	$(".left_menu").hide();
	var firScolltop=0;
	var oldValue='';
	business.listenEnter();
	var pageNum = $("#pageNum").val();
	var scoringIndexList = null;
	var expertFileId = $("#expertFileId").val();
	$.ajax({
		cache : true,
		type : "GET",
		async : false,
		//api/test/detail/behaviour/74
		url: WEBPATH+"/zjpf/getCommitIndexList.do",
		data:{
			id:expertFileId,handType:1
		},
		error : function(request) {
			swal("错误!","请求异常！", "error");
		},
		success : function(data) {
		  	if(data.result =='success'){
                if(data.result =='success'){
                    scoringIndexList = data.data;
                    // scoringIndexList.problemRemark = problemRemark;
                    for(var i=0;i<data.data.expertHandlIndexScoreList.length;i++){
                        data.data.expertHandlIndexScoreList[i].className = 'class'+ data.data.expertHandlIndexScoreList[i].id;
                        var index = data.data.expertHandlIndexScoreList[i];
                        //先创建好select里面的option元素
                        var option=document.createElement("option");
                        //转换DOM对象为JQ对象,好用JQ里面提供的方法 给option的value赋值
                        $(option).val('class' + index.id);
                        $(option).text(index.indexname);
                        $("#indexName").append(option);
                    }
                    console.log(scoringIndexList);
                }
		  	}
		} 
	});


    function change(value) {
		var mao;
		var pos;
		var poshigh;
		mao = $("." + value);
		pos = mao.offset().top;
		poshigh = mao.height();
		//alert(poshigh)
		var temp=pos+firScolltop;
		if(oldValue!=''){
			// alert("old:"+$("." + oldValue).offset().top);
			temp=temp-$("." + oldValue).offset().top;
		}
		// alert(pos);
		$("#scrollBarCenter").animate({scrollTop: temp - 210})
		// alert(mao.offset().top)
		firScolltop = temp;
		oldValue=value;
    }

	var xzcfVue = new Vue({
		el: '#CommitVue',
		data: {
			scoringIndexList:scoringIndexList,
		},
		mounted: function(){
			this.sumScore();
		//	setTimeout(this.saveTime,1000*60*30);//30分钟自动保存
		},
		computed:{
			totalScore:function(){
				var sum = 0;
				this.scoringIndexList.expertHandlIndexScoreList.forEach(function(item){
					if(item.inCheckValue != 1) {
						if(item.indexname == null || item.indexname == "" || item.indexname == '加分项'){
							sum += 0;
						}else{
							sum += item.indexscore;
						}
					}
				});
				return sum;
			},
			inputScore:function(){
				var inpuSco = 0;
				this.scoringIndexList.expertHandlIndexScoreList.forEach(function(item){
					if(item.indexname == null || item.indexname == "" || item.indexname == '加分项'){
						inpuSco += 0;
					}else{
						inpuSco += item.resultScore;
					}

				});
				if (inpuSco == null || inpuSco == "" || inpuSco == 0){
					return 0.00;
				}else {
					return parseFloat(inpuSco).toFixed(2);;
				}
			}
		},
		methods: {
			sumScore:function(){//判断是否有总分，没有则相加求分
				var paperScore = this.scoringIndexList.paperscore;
				var firstScore = this.scoringIndexList.expertHandlIndexScoreList;

				if(paperScore==""|| paperScore==null){
					var totalScore = 0;
					var childScore = 0;
					var denominatorScore = 0;
					var bonus = 0;
					if(firstScore != null&&firstScore.length>0) {
						for(var i = 0; i < firstScore.length; i++) {
							if(!isNaN(parseFloat(firstScore[i].resultScore))&& firstScore[i].indexname != '加分项') {
								childScore += firstScore[i].resultScore;
							}
							if(!firstScore[i].inCheckValue&& firstScore[i].indexname != '加分项') {
								denominatorScore += firstScore[i].indexscore;
							}
							if(firstScore[i].indexname == '加分项'){
								bonus = firstScore[i].resultScore;
							}
						}
					}
					if (denominatorScore == 0){
						totalScore = 0.00 + bonus
					} else {
						totalScore = 50 * (childScore / denominatorScore)+bonus;
					}
					this.scoringIndexList.paperscore = totalScore.toFixed(2);
				}
			},
			checkBoxClick:function(textId) {//优秀典型案例推荐复选框点击事件
				if ($("#yxdxAnLiTuiJianReviews1").css("display") == 'none' ) {//如果show是隐藏的
					$("#yxdxAnLiTuiJianReviews1").css("display", "block"); //  yxdxanlituijianreviews show的display属性设置为block（显示）
					//$("#yxdxanlituijian").val("1");
					xzcfVue.scoringIndexList.yxdxanlituijian = "1";

					//禁止反面推荐
					$("#ajpjYxdxanlituijian").attr("disabled","disabled");
					$("#ajpjYxdxAnLiTuiJianReviews1").css("display", "none");
					$('input:checkbox[name=jcaj]').attr('checked',false);
					$('#ajpj').val("")

					xzcfVue.scoringIndexList.ajpjYxdxanlituijianreviews="";
					xzcfVue.scoringIndexList.ajpjYxdxanlituijian="0";
					//禁止不推荐为典型案例
					$("#noTuiJian").attr("disabled","disabled");
					xzcfVue.scoringIndexList.noTuiJian="0";
				} else {//如果show是显示的
					xzcfVue.scoringIndexList.yxdxanlituijianreviews="";
					xzcfVue.scoringIndexList.yxdxanlituijian="0";
					$("#yxdxAnLiTuiJianReviews1").css("display", "none");//show的display属性设置为none（隐藏）

					//放开反面推荐
					$("#ajpjYxdxanlituijian").removeAttr("disabled");
					//放开不推荐为典型案例
					$("#noTuiJian").removeAttr("disabled");
				}
			},
			ajpjCheckBoxClick:function(){//是否进行案卷评价复选框点击事件
				if($("#ajpjYxdxAnLiTuiJianReviews1").css("display")=='none' ){//如果show是隐藏的
					$("#ajpjYxdxAnLiTuiJianReviews1").css("display","block"); //  yxdxanlituijianreviews show的display属性设置为block（显示）
					//$("#ajpjYxdxanlituijian").val("1");
					xzcfVue.scoringIndexList.ajpjYxdxanlituijian="1";

					//禁止优秀推荐
					$("#yxdxanlituijian").attr("disabled","disabled");
					$("#yxdxAnLiTuiJianReviews1").css("display", "none");
					$('input:checkbox[name=yxaj]').attr('checked',false);
					$('#zjpy').val("")

					xzcfVue.scoringIndexList.yxdxanlituijianreviews="";
					xzcfVue.scoringIndexList.yxdxanlituijian="0";
					//禁止不推荐为典型案例
					$("#noTuiJian").attr("disabled","disabled");
					xzcfVue.scoringIndexList.noTuiJian="0";
				}else{//如果show是显示的
					xzcfVue.scoringIndexList.ajpjYxdxanlituijianreviews="";
					xzcfVue.scoringIndexList.ajpjYxdxanlituijian="0";
					$("#ajpjYxdxAnLiTuiJianReviews1").css("display","none");//show的display属性设置为none（隐藏）

					//放开优秀推荐
					$("#yxdxanlituijian").removeAttr("disabled");
					//放开不推荐为典型案例
					$("#noTuiJian").removeAttr("disabled");
				}
			},
			noCheckBoxClick:function (){
				var isChecked = $("#noTuiJian").prop('checked');
				if(isChecked){
					xzcfVue.scoringIndexList.noTuiJian="1";
					//禁止优秀推荐
					$("#yxdxanlituijian").attr("disabled","disabled");
					$("#yxdxAnLiTuiJianReviews1").css("display", "none");
					xzcfVue.scoringIndexList.yxdxanlituijianreviews="";
					xzcfVue.scoringIndexList.yxdxanlituijian="0";
					$('input:checkbox[name=yxaj]').attr('checked',false);
					$('#zjpy').val("")
					//禁止反面推荐
					$("#ajpjYxdxanlituijian").attr("disabled","disabled");
					$("#ajpjYxdxAnLiTuiJianReviews1").css("display", "none");
					xzcfVue.scoringIndexList.ajpjYxdxanlituijianreviews="";
					xzcfVue.scoringIndexList.ajpjYxdxanlituijian="0";
					$('input:checkbox[name=jcaj]').attr('checked',false);
					$('#ajpj').val("")
				}else {

					xzcfVue.scoringIndexList.noTuiJian="0";
					//放开反面推荐
					$("#ajpjYxdxanlituijian").removeAttr("disabled");
					//放开优秀推荐
					$("#yxdxanlituijian").removeAttr("disabled");

				}

			},
			changeCheckBoxNegative:function(indexScore, itemScore) {
				var data = xzcfVue.scoringIndexList.expertHandlIndexScoreList[indexScore].expertHandlItemScoreList;
				var flag = data[itemScore].score;
				var checkitem = ".checkitem" + indexScore;
			  	var isInCheckid = '#isInCheck' + indexScore;

			  	if(flag) {//一票否决选中
			  		xzcfVue.scoringIndexList.expertHandlIndexScoreList[indexScore].expertHandlItemScoreList[itemScore].score = 1;
			  		if(data != null && data.length > 0) {
			  			for(var i = 0; i < data.length - 2; i++){
			  				data[i].score = '';
			  				$("#expertItem" + i + "" + indexScore).removeClass("has-error");	
							$("#expertItem" + i + "" + indexScore).removeClass("has-success");
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[indexScore].expertHandlItemScoreList[i].validatorMessage = "";
			  			}
			  		} else {
			  			xzcfVue.scoringIndexList.expertHandlIndexScoreList[indexScore].score = '';
			  			xzcfVue.scoringIndexList.expertHandlIndexScoreList[indexScore].resultScore = '';
			  			$("#expertIndex" + indexScore).removeClass("has-error");	
						$("#expertIndex" + indexScore).removeClass("has-success");	
						xzcfVue.scoringIndexList.expertHandlIndexScoreList[indexScore].validatorMessage = "";
			  		}
		  			xzcfVue.scoringIndexList.expertHandlIndexScoreList[indexScore].resultScore = 0;
			  		$(checkitem).attr("disabled", "disabled");
			  		$(isInCheckid).attr("disabled", "disabled");
			  		
			  		var text = $("#comment" + indexScore).val().replace(/\s+/g,"");
			  		if(text == null || text == ''){
			  			$("#comment" + indexScore).removeClass("textarea-success");	
				  		$("#comment" + indexScore).addClass("textarea-error");	  
						xzcfVue.scoringIndexList.expertHandlIndexScoreList[indexScore].validatorFlag = false;
						xzcfVue.scoringIndexList.expertHandlIndexScoreList[indexScore].validatorMessage = "此项评审依据必填";
					}
			  		xzcfVue.scoringIndexList.expertHandlIndexScoreList[indexScore].voteDownValue = 2;
			  	} else {
			  		xzcfVue.scoringIndexList.expertHandlIndexScoreList[indexScore].expertHandlItemScoreList[itemScore].score = 0;
			  		
			  		/* 
			  		 *	2019二级指标为复选框, 指标项在集合中最后两个
			  		 *
			  		 *		先判断传入二级指标是否为最后一项
			  		 *			是:判断上一个是否选中
			  		 *			否:判断下一个是否选中
			  		 */
			  		var state = data[itemScore].score;
			  		if(itemScore == data.length - 1) {
			  			if(data[itemScore - 1].score == 1) {
			  				state = true;
			  			}
			  		} else {
			  			if(data[itemScore + 1].score == 1) {
			  				state = true;
			  			}
			  		}
			  		if(!state) {
				  		xzcfVue.scoringIndexList.expertHandlIndexScoreList[indexScore].score = '';
				  		xzcfVue.scoringIndexList.expertHandlIndexScoreList[indexScore].resultScore = '';
				  		if(data != null && data.length > 0) {
				  			for(var i = 0; i < data.length; i++){
								xzcfVue.scoringIndexList.expertHandlIndexScoreList[indexScore].expertHandlItemScoreList[i].validatorFlag = false;
				  			}
				  		} else {
				  			xzcfVue.scoringIndexList.expertHandlIndexScoreList[indexScore].validatorFlag = false;
				  		}
				  		$(checkitem).removeAttr("disabled");
				  		$(isInCheckid).removeAttr("disabled");
				  		
				  		$("#comment" + indexScore).removeClass("textarea-error");	
				  		$("#comment" + indexScore).addClass("textarea-success");	  
						xzcfVue.scoringIndexList.expertHandlIndexScoreList[indexScore].validatorFlag = true;
						xzcfVue.scoringIndexList.expertHandlIndexScoreList[indexScore].validatorMessage = "";
						xzcfVue.scoringIndexList.expertHandlIndexScoreList[indexScore].voteDownValue = 0;
			  		}
			  	}
				var totalScore = 0;
				var childScore = 0;
				var denominatorScore = 0;
				var firstScore = xzcfVue.scoringIndexList.expertHandlIndexScoreList;
				var bonus = 0;
				if(firstScore != null&&firstScore.length>0) {
					for(var i = 0; i < firstScore.length; i++) {
						if(!isNaN(parseFloat(firstScore[i].resultScore))&&firstScore[i].indexname != '加分项') {
							childScore += firstScore[i].resultScore;
						}
						if(!firstScore[i].inCheckValue&&firstScore[i].indexname != '加分项') {
							denominatorScore += firstScore[i].indexscore;
						}
						if(firstScore[i].indexname == '加分项'){
							bonus = firstScore[i].resultScore;
						}
					}
				}
				if (denominatorScore == 0){
					totalScore = 0.00 + bonus
				} else {
					totalScore = 50 * (childScore / denominatorScore)+bonus;
				}
				xzcfVue.scoringIndexList.paperscore = totalScore.toFixed(2);
			},
			changeCheckBoxCli:function(index, status) {//一票否决或无需评查复选框点击事件
				// 获取选中未选中的状态 stauts=1一票否决\status=2无需评查
				if(status == '1') {
					//一票否决
					var flag = xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].voteDownValue;
				  	var checkitem = ".checkitem" + index;
				  	var isInCheckid = '#isInCheck' + index;
				  	if(flag) {//一票否决选中
				  		var data = xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList;
				  		if(data != null && data.length > 0) {
				  			for(var i = 0; i < data.length; i++){
				  				data[i].score='';
				  				$("#expertItem" + i + "" + index).removeClass("has-error");	
								$("#expertItem" + i + "" + index).removeClass("has-success");
								xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[i].validatorMessage = "";
				  			}
				  		} else {
				  			xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].score = '';
				  			xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore = '';
				  			$("#expertIndex"+index).removeClass("has-error");	
							$("#expertIndex"+index).removeClass("has-success");	
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].validatorMessage = "";
				  		}
			  			xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore = 0;
				  		$(checkitem).attr("disabled", "disabled");
				  		$(isInCheckid).attr("disabled", "disabled");
				  		
				  		var text = $("#comment" + index).val().replace(/\s+/g,"");
				  		if(text == null || text == ''){
				  			$("#comment"+index).removeClass("textarea-success");	
					  		$("#comment"+index).addClass("textarea-error");	  
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].validatorFlag = false;
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].validatorMessage = "此项评审依据必填";
						}
				  	} else {//一票否决取消
				  		xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].score = '';
				  		xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore = '';
				  		var data = xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList;
				  		if(data!=null &&  data.length>0) {
				  			for(var i=0;i<data.length;i++){
								xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[i].validatorFlag = false;
				  			}
				  		} else {
				  			xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].validatorFlag = false;
				  		}
				  		$(checkitem).removeAttr("disabled");
				  		$(isInCheckid).removeAttr("disabled");
				  		
				  		$("#comment"+index).removeClass("textarea-error");	
				  		$("#comment"+index).addClass("textarea-success");	  
						xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].validatorFlag = true;
						xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].validatorMessage = "";
				  	}
				} else {
					//无需评查
					var flag = xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].inCheckValue;
				  	var checkitem = ".checkitem"+index;
				  	var isVoteDownid = '#isVoteDown'+index;
				  	if(flag) {//无需评查选中
				  		var data = xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList;
				  		if(data != null && data.length > 0) {
				  			for(var i = 0; i < data.length; i++) {
				  				data[i].score = '';
						  		$("#expertItem" + i + "" + index).removeClass("has-error");	
								$("#expertItem" + i + "" + index).removeClass("has-success");
								xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[i].validatorMessage = "";
				  			}
				  		} else {
				  			xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].score = '';
				  			xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore = '';
				  			$("#expertIndex"+index).removeClass("has-error");	
							$("#expertIndex"+index).removeClass("has-success");	
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].validatorMessage = "";
				  		}
			  			xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore = 0;
				  		$(checkitem).attr("disabled","disabled");
				  		$(isVoteDownid).attr("disabled","disabled");
				  	} else {//无需评查取消
				  		var data = xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList;
				  		if(data!=null &&  data.length>0) {
				  			for(var i=0;i<data.length;i++) {
								xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[i].validatorFlag = false;
				  			}
				  		} else {
				  			xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].validatorFlag= false;
				  		}
				  		xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].score='';
				  		xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore='';
				  		$(checkitem).removeAttr("disabled");
				  		$(isVoteDownid).removeAttr("disabled");
				  	}
				}
				// 总分计算
				var totalScore = 0;
				var childScore = 0;
				var denominatorScore = 0;
				var firstScore = xzcfVue.scoringIndexList.expertHandlIndexScoreList;
				var bonus = 0;
				if(firstScore != null&&firstScore.length>0) {
					for(var i = 0; i < firstScore.length; i++) {
						if(!isNaN(parseFloat(firstScore[i].resultScore))&&firstScore[i].indexname != '加分项') {
							childScore += firstScore[i].resultScore;
						}
						if(!firstScore[i].inCheckValue&&firstScore[i].indexname != '加分项') {
							denominatorScore += firstScore[i].indexscore;
						}
						if(firstScore[i].indexname == '加分项'){
							bonus = firstScore[i].resultScore;
						}
					}
				}
				if (denominatorScore == 0){
					totalScore = 0.00 + bonus
				} else {
					totalScore = 50 * (childScore / denominatorScore)+bonus;
				}

				xzcfVue.scoringIndexList.paperscore = totalScore.toFixed(2);
			},
			updateItem:function (index, itemIndex, itemScore, id) {//
				var num = 0;
			  	var childNum =0;
			  	var value = xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].score;
				if(value != '' && value != null){
					if(parseFloat(value) <=parseFloat(itemScore)){
						if(value.substring(value.length-1,value.length) =="."){
							$("#expertItem"+itemIndex+index).removeClass("has-success");	
							$("#expertItem"+itemIndex+index).addClass("has-error");	  
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorFlag= false;
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorMessage="分值项不能为空，且在0-"+itemScore+"分之间！";
							return false;
						}
			  		 	var re = /^-?\d+\.?\d{0,2}$/;
			    		if( re.test(value) ){   // 返回true
			    			$("#expertItem"+itemIndex+index).removeClass("has-error");	
							$("#expertItem"+itemIndex+index).addClass("has-success");	 
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorFlag= true;
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorMessage="";
			    		}else{
			    			$("#expertItem"+itemIndex+index).removeClass("has-success");	
							$("#expertItem"+itemIndex+index).addClass("has-error");	  
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorFlag= false;
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorMessage="分值项不能为空，且在0-"+itemScore+"分之间！";
					   		return false;
			    		}
						var score = xzcfVue.scoringIndexList.expertHandlIndexScoreList[index];
						var indexScore = xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].indexscore;
						if(score != null){
							for(var i=0;i<score.expertHandlItemScoreList.length;i++){
								if(!isNaN(parseFloat(score.expertHandlItemScoreList[i].score))){
									if(score.expertHandlItemScoreList[i].isOperator ==1){
										childNum = (Math.round((childNum + parseFloat(score.expertHandlItemScoreList[i].score))*100))/100;
									} else {
										//减分项
										childNum = (Math.round((childNum - parseFloat(score.expertHandlItemScoreList[i].score))*100))/100;
									}

								}
							}
						}
						xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore= childNum;
						if(xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore>indexScore){
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore=indexScore;
						}
						if( xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore<0){
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore =0;
						}
						// 总分计算
						var totalScore = 0;
						var childScore = 0;
						var denominatorScore = 0;
						var firstScore = xzcfVue.scoringIndexList.expertHandlIndexScoreList;
						// if(firstScore != null) {
						// 	for(var i = 0; i < firstScore.length; i++) {
						// 		if(!isNaN(parseFloat(firstScore[i].resultScore))) {
						// 			childScore += firstScore[i].resultScore;
						// 		}
						// 		if(!firstScore[i].inCheckValue) {
						// 			denominatorScore += firstScore[i].indexscore;
						// 		}
						// 	}
						// }
						// totalScore = (childScore / denominatorScore) * 50;
						var bonus =0;
						if(firstScore != null&&firstScore.length>0) {
							for(var i = 0; i < firstScore.length; i++) {
								if(!isNaN(parseFloat(firstScore[i].resultScore)) && firstScore[i].indexname!='加分项') {
									childScore += firstScore[i].resultScore;
								}
								if(!firstScore[i].inCheckValue && firstScore[i].indexname!='加分项') {
									denominatorScore += firstScore[i].indexscore;
								}
								if(firstScore[i].indexname=='加分项'){
									bonus = firstScore[i].resultScore;
								}
							}
						}
						if (denominatorScore == 0){
							totalScore = 0.00 + bonus
						} else {
							totalScore = 50 * (childScore / denominatorScore)+bonus;
						}
						xzcfVue.scoringIndexList.paperscore = totalScore.toFixed(2);
				  		} else {
				  			$("#expertItem"+itemIndex+index).removeClass("has-success");	
							$("#expertItem"+itemIndex+index).addClass("has-error");	  
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorFlag= false;
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorMessage="分值项不能为空，且在0-"+itemScore+"分之间！";
					   		return false;
				  		}
					} else {
				  		$("#expertItem"+itemIndex+index).removeClass("has-success");	
						$("#expertItem"+itemIndex+index).addClass("has-error");	  
						xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorFlag= false;
						xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorMessage="分值项不能为空，且在0-"+itemScore+"分之间！";
				   		return false;
					}
				},
				updateCommon:function (index){
					var flag=  xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].voteDownValue;
					var text = $("#comment"+index).val().replace(/\s+/g,"");
					var indexname = xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].indexname;
					if(indexname == "责令改正违法行为决定书") {
						var itemScores = xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList;
						var item1 = itemScores[itemScores.length - 1].score;
						var item2 =  itemScores[itemScores.length - 2].score;
						if(item1 == 1 || item2 == 1) {
							flag = true;
						}
					}
					if(flag){
					   	if(text!=null && text!=''){
					 		$("#comment"+index).removeClass("textarea-error");	
					  		$("#comment"+index).addClass("textarea-success");	  
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].validatorFlag= true;
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].validatorMessage="";
						}
					   	if(text==null || text==''){
				 			$("#comment"+index).removeClass("textarea-success");	
					  		$("#comment"+index).addClass("textarea-error");	  
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].validatorFlag= false;
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].validatorMessage="此项评审依据必填";
						}
					}
				},
				saveTime:function(){
					if(this.scoringIndexList.scoredstate == 0 || this.scoringIndexList.scoredstate == 2){
						this.saveSubmit(this.scoringIndexList.id,'0');//暂存
					}else{
						this.saveSubmit(this.scoringIndexList.id,'1');//保存
					}
				},
				saveSubmit:function(id, status){// 0.暂存  1.保存
					
					/*判断优秀案卷与反面案卷的关联验证（保存的时候必验）*/
					// if(status == "1") {
					// 	var tuijian = document.getElementById("yxdxanlituijian").checked;
					// 	var pingjia = document.getElementById("ajpjYxdxanlituijian").checked;
					//
					// 	if(tuijian == true){
					// 		var yxFileStr = $("#zjpy").val().replace(/\s+/g,"");
					// 		if(yxFileStr==null || yxFileStr==""){
					// 			swal("提示", "优秀典型案例推荐不能为空!", "error");
					// 			return false;
					// 		};
					//  	}
					// 	if(pingjia == true){
					// 		var fmFileStr = $("#ajpj").val().replace(/\s+/g,"");
					// 		if(fmFileStr==null || fmFileStr==""){
					// 			swal("提示", "反面典型案例推荐不能为空!", "error");
					// 			return false;
					// 		}
					// 	}
					// }
					
					/*逐项验证vue对象中的文本框*/

					if(status == "1") {

						var  isYXchecked=$("#yxdxanlituijian").prop('checked');
						var  isJCchecked=$("#ajpjYxdxanlituijian").prop('checked');
						var  isNOchecked=$("#noTuiJian").prop('checked');
						if(isYXchecked == false && isJCchecked == false && isNOchecked == false ){
							swal({title: "提示",text: "请选择案件状态！",type:"error"});
							return false;
						}




						var checked_array=new Array();
						if(xzcfVue.scoringIndexList.yxdxanlituijian=="1") {
							var flag = false;
							var flagtext = false;
							$('input[name="yxaj"]:checked').each(function () {
								checked_array.push($(this).val())
								if (checked_array.indexOf("1000") != -1) {
									var yxFileStr = $("#zjpy").val().replace(/\s+/g, "");
									if (yxFileStr == null || yxFileStr == "") {

										flag = true;
									}
									if(yxFileStr.length>2000){
										flagtext=true;
									}
								}
							});
							if (flag) {
								swal({title: "提示", text: "优秀典型案例推荐不能为空", type: "error"});
								return false;
							}
							if (flagtext) {
								swal({title: "提示", text: "案卷评价长度不能超过2000个字符", type: "error"});
								return false;
							}
						}
						//优秀典型案例选中 下边的子项必填一个
						if(xzcfVue.scoringIndexList.yxdxanlituijian=="1"){
							var checked=$("input[name='yxaj']:checked");//获取复选框被选中状态
							if(!(checked&&checked.length>0)){//判断复选框是否被选中
								swal({title: "提示",text: "优秀典型案例子项不能为空",type:"error"});
								return false;
							}
						}
						//反面典型案例
						/*var ajpjYxdxanlituijianreviews = xzcfVue.scoringIndexList.ajpjYxdxanlituijianreviews;
                        if(xzcfVue.scoringIndexList.ajpjYxdxanlituijian=="1"){
                            if ($("#ck_others_id").is(':checked') && ajpjYxdxanlituijianreviews==""){
                                swal({title: "提示",text: "较差案例推荐不能为空",type:"error"});
                                return false;
                            }

                            if (ajpjYxdxanlituijianreviews.length>2000){
                                swal({title: "提示",text: "案卷评价长度不能超过2000个字符",type:"info"});
                                return false;
                            }
                        }*/
						//反面典型案例
						var flags = false;
						var flagss = false;
						var ajpjYxdxanlituijianreviews = xzcfVue.scoringIndexList.ajpjYxdxanlituijianreviews;
						if(xzcfVue.scoringIndexList.ajpjYxdxanlituijian=="1"){
							var checked_array=new Array();
							$('input[name="jcaj"]:checked').each(function(){
								checked_array.push($(this).val())
								if(checked_array.indexOf("1000") != -1){
									if(ajpjYxdxanlituijianreviews==null || ajpjYxdxanlituijianreviews==""){
										flags = true;
									}else{
										if(ajpjYxdxanlituijianreviews.length>2000){
											flagss = true;
										}
									}
								}
							});
							if (flags){
								swal({title: "提示",text: "较差案例推荐不能为空",type:"error"});
								return false;
							}
							if (flagss){
								swal({title: "提示",text: "案卷评价长度不能超过2000个字符",type:"info"});
								return false;
							}
						}
						//反面典型案例选中 下边的子项必填一个
						if(xzcfVue.scoringIndexList.ajpjYxdxanlituijian=="1"){
							var checked=$("input[name='jcaj']:checked");//获取复选框被选中状态
							if(!(checked&&checked.length>0)){//判断复选框是否被选中
								swal({title: "提示",text: "较差案例推荐子项不能为空",type:"error"});
								return false;
							}
						}

					}
					if(xzcfVue.scoringIndexList != null) {
						var data = xzcfVue.scoringIndexList.expertHandlIndexScoreList;//一级指标
						for(var i = 0; i < data.length; i++){
							//一票否决
							if(data[i].voteDownValue != 2) {
								if((data[i].voteDownValue != 0 || data[i].voteDownValue != 1) && data[i].voteDownValue) {
									data[i].voteDownValue = 1;
								} else {
									data[i].voteDownValue = 0;
								}
							}
							
							//无需评查
							if((data[i].inCheckValue != 0 || data[i].inCheckValue != 1) && data[i].inCheckValue) {
								data[i].inCheckValue = 1;
							}else{
								data[i].inCheckValue = 0;
							}
							
							//评审依据的验证
							if(status == "1") {//保存的时候必验
								var flag=  data[i].voteDownValue;
							    var text = $("#comment"+i).val().replace(/\s+/g,"");
							    if(flag) {
							    	if(text == null || text == ''){
							  			$("#comment"+i).removeClass("textarea-success");	
								  		$("#comment"+i).addClass("textarea-error");	
								  		$("#comment"+i).focus();
								  		data[i].validatorFlag= false;
								  		data[i].validatorMessage="此项评审依据必填";
								  		return false;
						  			}
							    }
							}
							
							//一级指标一票否决或无需评查，则跳出循环，无需校验二级指标
							if(data[i].voteDownValue == 2 || data[i].voteDownValue == 1 || data[i].inCheckValue == 1){continue;}
							
							/*二级指标验证*/
							if(data[i].expertHandlItemScoreList != null){
								for(var j=0;j<data[i].expertHandlItemScoreList.length;j++){
									var standScore = data[i].expertHandlItemScoreList[j].itemscore//标准分
									var score=data[i].expertHandlItemScoreList[j].score;//输入分
									
									/*
									*无论暂存或是保存，都需要验证的是格式
									*/
									if(score != null && score!=""){
										var re = /^-?\d+\.?\d{0,2}$/;
										if(standScore!=0&&(score>standScore || !(re.test(score)) )){
											xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].validatorFlag= false;
											xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].validatorMessage="分值项不能为空，且在0-"+xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].itemscore+"分之间！";
											$("#expertItem"+xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].temItemId+""+i).removeClass("has-success");	
											$("#expertItem"+xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].temItemId+""+i).addClass("has-error");
											$("#"+i+"scoreInput"+j).focus();
											return false;
										}
									}
									
									/*
									*【保存】的时候验证必填项
									*/
									if(status == "1") {
										if(standScore!=0){//标准分不是0的是必填项
											if((score == null || score==="")){
												xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].validatorFlag= false;
												xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].validatorMessage="分值项不能为空，且在0-"+xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].itemscore+"分之间！";
												$("#expertItem"+xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].temItemId+""+i).removeClass("has-success");	
												$("#expertItem"+xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].temItemId+""+i).addClass("has-error");
												$("#"+i+"scoreInput"+j).focus();
												return false;
											}
										}else{//此操作是为了后台验证通过
											xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].validatorFlag= true;
											xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].validatorMessage="";
										}
									}
									
								}
							}//二级指标验证结束
							 
						}//一级指标验证结束
						
					}//vue对象验证结束
					var code_array=new Array();
					$('input[name="yxaj"]:checked').each(function(){
						code_array.push($(this).val())
					});
					xzcfVue.scoringIndexList.ajpjYxdxanlituijianCode = code_array.join(',')

					//存入较差案卷code
					var jcajArr=new Array();
					$('input[name="jcaj"]:checked').each(function(){
						jcajArr.push($(this).val())
					});
					xzcfVue.scoringIndexList.jcajtuijianCode = jcajArr.join(',')
					
					/*开始提交保存*/
					$("#submitBtn").attr("disabled", "disabled");
					$("#submitTemporaryBtn").attr("disabled", "disabled");
			  		$("#submitBtn").text("提交中...");
			  		$.ajax({
			  			type:"post",
						url:WEBPATH+'/zjpf/saveExpertScore.do',
						data:{
							id:id, 
							status:status,
							scoringIndex:JSON.stringify(xzcfVue.scoringIndexList)
						},
						success:function(data){  //成功
							if (data.result=="success") {
								swal({title: "保存成功",text: "",type:"success"});
								$("#submitBtn").attr("disabled", false);
								$("#submitTemporaryBtn").attr("disabled", false);
								$("#submitBtn").text("保存信息");
								if(status == "1") {
									business.addMainContentParserHtml('zjpf/entityChiefScore.do?id=${expertFileId}','pageNum=${pageNum}');
								}else{
									business.addMainContentParserHtml('zjpf/zjpfChiefScore.do?id='+id,'pageNum=${pageNum}');
								}
								return false;
							} else {
								if(data.code=="007"){
									swal("提示", "正在保存中……，请稍等片刻", "info");
								}else if(data.code=="000"){//登录信息失效
									swal({ 
								        title: "提示",  
								        text: data.message,  
								        type: "error", 
								        confirmButtonText: "确定", 
								        confirmButtonColor: "#ec6c62" 
								    }, function() { 
								        window.location.href=WEBPATH+"/index.do";
								    });
								}
								$("#submitBtn").attr("disabled", false);
								$("#submitTemporaryBtn").attr("disabled", false);
								$("#submitBtn").text("保存信息");
								return false; 
							}
						}
			  		});
					
				}
			
		}//vue对象中method结束
		
	});//vue对象结束

		
		
	//处理浮点型数据相减
	function handFloat(biaozhunfen,subScore){
		var r1,r2,m,n;
		try{r1=biaozhunfen.toString().split(".")[1].length}catch(e){r1=0};
		try{r2=subScore.toString().split(".")[1].length}catch(e){r2=0};
		m=Math.pow(10,Math.max(r1,r2));
		n = (r1 >= r2) ? r1 : r2;
		return ((biaozhunfen * m - subScore * m) / m).toFixed(n);  
	}

	var a = xzcfVue.scoringIndexList.yxdxanlituijian;
	if(xzcfVue.scoringIndexList.yxdxanlituijian=="1"){
		//$('#yxdxanlituijian').attr('checked', true)
debugger;
		$("[name = yxdxanlituijian]:checkbox").attr("checked", true);
		$("#yxdxAnLiTuiJianReviews1").css("display","block");

		//优秀案卷选项回显
		var checkArray = $("input[name='yxaj']");
		var yy = xzcfVue.scoringIndexList;
		var codes = xzcfVue.scoringIndexList.ajpjYxdxanlituijianCode;
		if(codes != null && codes != "" ){
			var myArray=codes.split(",");
			for (var i = 0; i < myArray.length; i++) {
				//获取所有复选框对象的value属性，然后，用checkArray[i]和他们匹配，如果有，则说明他应被选中
				$.each(checkArray, function (j, checkbox) {
					//获取复选框的value属性
					var checkValue=$(checkbox).val();
					console.log(j+"----"+checkValue)
					if (myArray[i] == checkValue) {
						$(checkbox).attr("checked", true);
					}
				})
			}
		}


		//禁止反面推荐
		$("#ajpjYxdxanlituijian").attr("disabled","disabled");
		$("#ajpjYxdxAnLiTuiJianReviews1").css("display", "none");
		//禁止不推荐为典型案例
		$("#noTuiJian").attr("checked", false);
	}

	var ajpja = xzcfVue.scoringIndexList.ajpjYxdxanlituijian;
	if(xzcfVue.scoringIndexList.ajpjYxdxanlituijian=="1"){
		//$('#ajpjYxdxanlituijian').attr('checked', true)

		$("[name = ajpjYxdxanlituijian]:checkbox").attr("checked", true);
		$("#ajpjYxdxAnLiTuiJianReviews1").css("display","block");

		//较差案卷选项回显
		var checkArray = $("input[name='jcaj']");
		var codes = xzcfVue.scoringIndexList.jcajtuijianCode;
		if(codes != null && codes != "" ){
			var myArray=codes.split(",");
			for (var i = 0; i < myArray.length; i++) {
				//获取所有复选框对象的value属性，然后，用checkArray[i]和他们匹配，如果有，则说明他应被选中
				$.each(checkArray, function (j, checkbox) {
					//获取复选框的value属性
					var checkValue=$(checkbox).val();
					console.log(j+"----"+checkValue)
					if (myArray[i] == checkValue) {
						$(checkbox).attr("checked", true);
					}
				})
			}
		}


		//禁止优秀推荐
		$("#yxdxanlituijian").attr("disabled","disabled");
		$("#yxdxAnLiTuiJianReviews1").css("display", "none");
		//禁止不推荐为典型案例
		$("#noTuiJian").attr("checked", false);
	}
	// 不推荐为典型案例 回显
	if(xzcfVue.scoringIndexList.noTuiJian=="1"){
		$("#noTuiJian").attr("checked", true);
		//禁止优秀推荐
		$("#yxdxanlituijian").attr("disabled","disabled");
		$("#yxdxAnLiTuiJianReviews1").css("display", "none");
		//禁止较差推荐
		$("#ajpjYxdxanlituijian").attr("disabled","disabled");
		$("#ajpjYxdxAnLiTuiJianReviews1").css("display", "none");
	}

	function trim(str) {
		return str.replace(/(^\s+)|(\s+$)/g, "");
	}
		
	//下载文件
	function xiaZaiAnJuan(){
	  var fileId = $("#fileId").val();
	  $.ajax({
		    type:"post",
		    url:WEBPATH+'/zjpf/xzcfExistFileUrl.do',
		    data:{fileId:fileId},           //注意数据用{}
		    success:function(data){  //成功
			 	if("yes" == data){
					window.location.href="${pageContext.request.contextPath}/zjpf/zjpfAnJuandown.do?fileId="+fileId;
				    return false;
		         }else if("no" == data){
		            	  swal( "操作失败","该案卷不存在!", "error");
		            	  return false;
				}else if("suffixerror" ==data){
					  swal( "操作失败","该案卷上传数据格式有问题!", "error");
	            	  return false;
				}
		    }
	    });
	}
	
	$(document).ready(function(){
		var scoredstate = ${expertHandlFileList.scoredstate};
		if(scoredstate==1){
			$("input[name='scoreInput']").removeAttr("disabled");
			
			$("#zjpy").removeAttr("disabled");
			$("#ajpj").removeAttr("disabled");
			$("#submitBtn").removeAttr("style display");
			$("#fileCodeInput").attr("disabled","disabled");
			$("textarea").removeAttr("disabled");
			$("#problem_remarkA").attr("disabled","disabled");
			$("#problem_remarkB").attr("disabled","disabled");
			$("#fileCodeInput").val('${ expertHandlFileList.filecode}');
			$("#yxdxanlituijian").attr("disabled",false);
			$("#ajpjYxdxanlituijian").attr("disabled",false);
			/* 页面初始化加载设置隐藏 input输入框，当用户选择一票否决和无须评查  */
			if(xzcfVue.scoringIndexList != null && xzcfVue.scoringIndexList.expertHandlIndexScoreList != null ){
				for( var i=0;i< xzcfVue.scoringIndexList.expertHandlIndexScoreList.length;i++){
					if(xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].inCheckValue ==1 ||xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].voteDownValue ==1){
						var checkitem =".checkitem"+i;
						$(checkitem).attr("disabled","disabled");
						if(xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].inCheckValue ==1){
							//如果选了无须评查
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].resultScore=xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].resultScore;
							var isVoteDownid = '#isVoteDown'+i;
							$(isVoteDownid).attr("disabled","disabled");
						}
						if(xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].voteDownValue ==1){
							//若果选了一票否决
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].resultScore=0;
							var isInCheckid = '#isInCheck'+i;
							$(isInCheckid).attr("disabled","disabled");
						}
					}
				}
			}
		}else{
			$("#look").attr("disabled","disalbed");
		}
	})
	
		 
	 function loding(btn,itext){
		document.getElementById(btn).innerHTML = "加载.."
		document.getElementById(btn).disabled = "disabled"
	    setTimeout(function () {
			document.getElementById(btn).innerHTML = itext;
			document.getElementById(btn).removeAttribute("disabled");
	    },3000);
	 }
	 //零分案卷理由返回
	 /* function bcakList(){
		 $("#zeroScoreFile").modal("hide");
		 $('.modal-backdrop').remove();
		 business.addMainContentParserHtml('zjpf/zjpfList.do','pageNum=${pageNum}');
	 } */
</script>
<body>
	<div id="CommitVue">
		<div class="center_weizhi">当前位置：专家评分 - 首席专家评审案卷 - ${filetypeName}</div>
		<div class="center" id="scrollBarCenter">
			<div class="center_list">
				<button    class="btn btn-primary"  style="font-size:16px;width:98px;height:40px; padding:6px 34px;margin-right: 10px;float: right;" onclick="macroMgr.onLevelOneMenuClick(null, 'zjpf.do')">返回</button>
		 		<div class="dingwei">
					<button class="btn btn-danger" >卷面评查</button>
					<c:choose>
	         			<c:when test ="${expertHandlFileList.expertId == null }">
							<button disabled class="btn btn-default" >实体和程序评查</button>
						</c:when>
						<c:otherwise>
							<button class="btn btn-default" onclick="macroMgr.onLevelTwoMenuClick(null, '/zjpf/entityChiefScore.do?id=${expertFileId}&pageNum=${pageNum}')">实体和程序评查</button>
						</c:otherwise>
					</c:choose>
					<p style="font-weight: 800; font-size: 16px">使用说明：</p>
					<p>1.本标准用于评查案卷卷面内容。</p>
					<p>2.可以根据证据类型和所发文书种类确定评查项目。</p>
					<p>3.卷面分=50*对应评查项目得分之和/参与评查项目标准分之和。</p>
					<p>4.内容完整、规范、正确的，得相应分值。不完整、不规范或者不正确的，不得分。</p>
                    <p class="ceshi">5.提示：无需评查是指根据案情需要，无需对此项文书进行评查！</p>
					<div class="dingwei" style="color:#333333;font-size:14px; font-weight:400;">案卷或材料：<span style="color:#009CCD;font-size:14px; font-weight:400;" id ="filecode">
						{{scoringIndexList.filecode }}
						<span style="color:red;font-size:16px; font-weight:bold;"><c:choose><c:when test="${expertHandlFileList.closed==1}">（已结案）</c:when><c:when test="${expertHandlFileList.closed==0}">（未结案）</c:when> </c:choose></span>
						</span>
					<c:if test ="${sessionScope.sa_session.sysStatus == '5' }">
						<a v-if="scoringIndexList.fileurl != null && scoringIndexList.fileurl != ''" v-bind:href="scoringIndexList.downUrl" target="_Blank">
							<button id="download" class="btn btn-success btn-xs" data-toggle="modal" style="padding:5px 12px;margin-left:10px;" data-target="#myModal">下载</button>
						</a>
						<span v-else style="color:red;font-size:16px; font-weight:bold;">（无案卷文件）</span>
						<c:if test="${expertHandlFileList.suffix== 'pdf'}">
							<button id="look" class="btn btn-info btn-xs" data-toggle="modal" style="padding:5px 12px;margin-left:10px;margin-right:10px;" data-remote="${webpath}/jcpf/showFileModal.do?fastDFSUrl=+'${fastDFS}'+" data-target="#view">预览</button>
						</c:if>
					</c:if>
					<%-- <c:if test ="${expertHandlFileList.scoredstate == 0}">
						<button class="btn btn-info btn-xs" data-toggle="modal" data-target="#zeroScoreFile" id ="chickAnjuan">零分案卷</button>
					</c:if> --%>

					</div>
					<select id="indexName" onchange="change(this.value)" style="margin-top: 10px"></select>
				</div>

		       	<form action="#" id ="zjpfForm" method="post">
		        	<table class="table table-bordered table-hover table-condensed">
						<thead>
							<tr>
								<td width="3%" height="30" bgcolor="#efefef">序号</td>
								<td width="7%" bgcolor="#efefef">评查项目</td>
								<td width="7%" bgcolor="#efefef">标准分</td>
								<!-- <td width="44%" bgcolor="#efefef"><span style="float: left;">评分细则</span><span style="float: right;margin-right: 70px;">得分值</span></td> -->
								 <td bgcolor="#efefef" style="text-align: center;">评分细则</td>
								 <td width="70px" bgcolor="#efefef">委员A评分值</td>
					             <td width="70px" bgcolor="#efefef">委员A评审依据</td>
					             <td width="70px" bgcolor="#efefef">委员B评分值</td>
					             <td width="70px" bgcolor="#efefef">委员B评审依据</td>
					             <td width="155px" bgcolor="#efefef">打分值</td>
								<td width="5%" bgcolor="#efefef">一票否决</td>
								<td width="5%" bgcolor="#efefef">无需评查</td>
								<td width="5%" bgcolor="#efefef">得分</td>
								<td width="15%" bgcolor="#efefef">评审依据</td>

							</tr>
						</thead>
						<tbody  class="form-group">
						    <tr v-for="(scoringIndex, index) in scoringIndexList.expertHandlIndexScoreList" :class="scoringIndex.className"  v-if="scoringIndex.isError!=2">
							<td height="30" align="center" >{{index+1}}</td>

							<!-- 评查项目 -->
							<td style="text-align: center" :class="scoringIndex.className">
								{{scoringIndex.indexname}}
								<p v-if="scoringIndex.isError==1" style="width: 67px;height: 24px;background: #DF1912;border-radius: 3px;text-align: center;">
									<a href="#"style="margin-left:15px;line-height: 22px;font-size:14px;font-weight: 400;color: #ffffff"  v-on:click="showErrorMsg(scoringIndex)">存在争议</a>
								</p>
							</td>

							<!-- 标准分 -->
							<td style="vertical-align:middle;">{{scoringIndex.indexscore}}</td>

							<!-- 评分细则 -->
							<td colspan="6" v-if="scoringIndex.expertHandlItemScoreList != null && scoringIndex.expertHandlItemScoreList.length !=0" style="padding:5px;">
								<table width="100%" border="0" cellspacing="0" cellpadding="0" style="padding:0;">
									<tr v-for="(scoringItem, index1) in scoringIndex.expertHandlItemScoreList">
										<td style="border-bottom:solid 1px #ddd; border-right:solid 1px #ddd;vertical-align:middle;">
											<div style="vertical-align:middle; margin:0 5px 5px 0;">{{scoringItem.itemname}}</div>
										</td>

										<td style="width:70px; border-bottom:solid 1px #ddd; border-right:solid 1px #ddd; vertical-align:middle;align:center;">{{scoringItem.aexpertScore}}</td>
										<td :rowspan="scoringIndex.expertHandlItemScoreList.length" v-if="index1==0" style="width:70px; border-bottom:solid 1px #ddd; border-right:solid 1px #ddd; vertical-align:middle;">{{scoringIndex.aexpertReason}}</td>
										<td style="width:70px; border-bottom:solid 1px #ddd; border-right:solid 1px #ddd; vertical-align:middle;align:center;">{{scoringItem.bexpertScore}}</td>
										<td :rowspan="scoringIndex.expertHandlItemScoreList.length" v-if="index1==0" style="width:70px; border-bottom:solid 1px #ddd; border-right:solid 1px #ddd; vertical-align:middle;">{{scoringIndex.bexpertReason}}</td>

										<td style="border-bottom:solid 1px #ddd; width:155px;vertical-align:middle;">
											<div v-if="scoringItem.itemscore == 0" class="custom-checkbox text-center" >
												<input name="scoreInput" type="checkbox" :id="index+'scoreInput'+index1"
													   @change="changeCheckBoxNegative(index, index1)" v-model="scoringItem.score"
													   style="width:16px; height:16px; display: table-cell;vertical-align: middle; text-align: center;">
												<label :for="'checkbox'+index" class="checkbox-blue" checked></label>
											</div>
											<div v-else :id="'expertItem'+index1+index" style="margin:0 0 5px 0; float:left;">
												<div v-if="scoringItem.isOperator==1" style="float:left; padding:5px 2px 7px 3px; color:red; font-size:20px;">＋</div>
												<div v-if="scoringItem.isOperator!=1" style="float:left; padding:0 0 3px 0; margin:0; color:red; font-size:26px;">－</div>

												<div v-if="scoringIndex.voteDownValue == 1 || scoringIndex.voteDownValue == 2 || scoringIndex.inCheckValue == 1"
													 style="float:right; padding-top:4px;">
													<input disabled name="scoreInput" @input="updateItem(index,index1,scoringItem.itemscore,scoringItem.temItemId)"
														   v-model="scoringItem.score" type="text" :class="'checkitem'+index" autocomplete="off"
														   class="form-control inputDisabled" placeholder="请输入初评得分" style="width:125px;">
												</div>
												<div v-else style="float:right; padding-top:4px;">
													<input name="scoreInput" @input="updateItem(index,index1,scoringItem.itemscore,scoringItem.temItemId)"
														   :id="index+'scoreInput'+index1" v-model="scoringItem.score" type="text" :class="'checkitem'+index" autocomplete="off"
														   class="form-control inputDisabled" placeholder="请输入初评得分" style="width:125px;">
												</div>

												<div style="color:red; font-size: 12px;">{{scoringItem.validatorMessage}}</div>
											</div>
										</td>
									</tr>
								</table>
							</td>

							<!-- 一票否决 -->
							<td style="vertical-align:middle;">
								<div class="custom-checkbox text-center" v-if="scoringIndex.isVoteDown == 1" >
									<input v-if="scoringIndex.inCheckValue != 1" name="scoreInput" type="checkbox" :id="'isVoteDown'+index"
										   @change="changeCheckBoxCli(index,'1')" v-model="scoringIndex.voteDownValue"
										   style="margin-left:18px;width:16px; height:16px; display: table-cell;vertical-align: middle; text-align: center;">
									<input v-else name="scoreInput" type="checkbox" :id="'isVoteDown'+index"
										   disabled v-model="scoringIndex.voteDownValue"
										   style="margin-left:18px;width:16px; height:16px; display: table-cell;vertical-align: middle; text-align: center;">
									<label :for="'checkbox'+index" class="checkbox-blue" checked></label>
								</div>
							</td>

							<!-- 无需评查 -->
							<td style="vertical-align:middle;">
								<div class="custom-checkbox text-center" v-if="scoringIndex.isInCheck ==1">
									<input v-if="scoringIndex.voteDownValue != 1" name="scoreInput" type="checkbox" :id="'isInCheck'+index"
										   @change="changeCheckBoxCli(index,'2')"  v-model="scoringIndex.inCheckValue"
										   style="margin-left:18px;width:16px; height:16px;  display: table-cell;vertical-align: middle; text-align: center; ">
									<input v-else name="scoreInput" type="checkbox" :id="'isInCheck'+index"
										   disabled  v-model="scoringIndex.inCheckValue"
										   style="margin-left:18px;width:16px; height:16px;  display: table-cell;vertical-align: middle; text-align: center; ">
									<label for="checkbox1" class="checkbox-blue" checked></label>
								</div>
							</td>

							<!-- 得分 -->
							<td style="vertical-align:middle;">
								<div style="width:120px;float: right;">
									<input type="text"  disabled="disabled" v-model="scoringIndex.resultScore" class="form-control">
								</div>
							</td>

							<!-- 评审依据 -->
							<td >
									<textarea maxlength="1000" @input="updateCommon(index)" :id="'comment'+index" :rows="scoringIndex.expertHandlItemScoreList.length"
											  class="form-control" v-model="scoringIndex.comment" placeholder="请输入评审依据，长度不能超过1000个字符" style="width:100%;">
									</textarea>
								<div style="color:red; font-size: 12px;">{{scoringIndex.validatorMessage}}</div>
							</td>
						</tr>
							<tr v-for="(scoringIndex, index) in scoringIndexList.expertHandlIndexScoreList" :class="scoringIndex.className"  v-if="scoringIndex.isError==2" style="background-color: #ECD9E0">
								<td height="30" align="center" >{{index+1}}</td>

								<!-- 评查项目 -->
								<td style="text-align: center" :class="scoringIndex.className">
									{{scoringIndex.indexname}}
									<p v-if="scoringIndex.isError==1" style="width: 67px;height: 24px;background: #DF1912;border-radius: 3px;text-align: center;">
										<a href="#"style="margin-left:15px;line-height: 22px;font-size:14px;font-weight: 400;color: #ffffff"  v-on:click="showErrorMsg(scoringIndex)">存在争议</a>
									</p>
								</td>

								<!-- 标准分 -->
								<td style="vertical-align:middle;">{{scoringIndex.indexscore}}</td>

								<!-- 评分细则 -->
								<td colspan="6" v-if="scoringIndex.expertHandlItemScoreList != null && scoringIndex.expertHandlItemScoreList.length !=0" style="padding:5px;">
									<table width="100%" border="0" cellspacing="0" cellpadding="0" style="padding:0;">
										<tr v-for="(scoringItem, index1) in scoringIndex.expertHandlItemScoreList">
											<td style="border-bottom:solid 1px #ddd; border-right:solid 1px #ddd;vertical-align:middle;">
												<div style="vertical-align:middle; margin:0 5px 5px 0;">{{scoringItem.itemname}}</div>
											</td>

											<td style="width:70px; border-bottom:solid 1px #ddd; border-right:solid 1px #ddd; vertical-align:middle;align:center;">{{scoringItem.aexpertScore}}</td>
											<td :rowspan="scoringIndex.expertHandlItemScoreList.length" v-if="index1==0" style="width:70px; border-bottom:solid 1px #ddd; border-right:solid 1px #ddd; vertical-align:middle;">{{scoringIndex.aexpertReason}}</td>
                            				<td style="width:70px; border-bottom:solid 1px #ddd; border-right:solid 1px #ddd; vertical-align:middle;align:center;">{{scoringItem.bexpertScore}}</td>
                            				<td :rowspan="scoringIndex.expertHandlItemScoreList.length" v-if="index1==0" style="width:70px; border-bottom:solid 1px #ddd; border-right:solid 1px #ddd; vertical-align:middle;">{{scoringIndex.bexpertReason}}</td>

											<td style="border-bottom:solid 1px #ddd; width:155px;vertical-align:middle;">
												<div v-if="scoringItem.itemscore == 0" class="custom-checkbox text-center" >
													<input name="scoreInput" type="checkbox" :id="index+'scoreInput'+index1"
														@change="changeCheckBoxNegative(index, index1)" v-model="scoringItem.score"
														style="width:16px; height:16px; display: table-cell;vertical-align: middle; text-align: center;">
													<label :for="'checkbox'+index" class="checkbox-blue" checked></label>
												</div>
												<div v-else :id="'expertItem'+index1+index" style="margin:0 0 5px 0; float:left;">
													<div v-if="scoringItem.isOperator==1" style="float:left; padding:5px 2px 7px 3px; color:red; font-size:20px;">＋</div>
													<div v-if="scoringItem.isOperator!=1" style="float:left; padding:0 0 3px 0; margin:0; color:red; font-size:26px;">－</div>

													<div v-if="scoringIndex.voteDownValue == 1 || scoringIndex.voteDownValue == 2 || scoringIndex.inCheckValue == 1"
														style="float:right; padding-top:4px;">
														<input disabled name="scoreInput" @input="updateItem(index,index1,scoringItem.itemscore,scoringItem.temItemId)"
															v-model="scoringItem.score" type="text" :class="'checkitem'+index" autocomplete="off"
															class="form-control inputDisabled" placeholder="请输入初评得分" style="width:125px;">
													</div>
													<div v-else style="float:right; padding-top:4px;">
														<input name="scoreInput" @input="updateItem(index,index1,scoringItem.itemscore,scoringItem.temItemId)"
															:id="index+'scoreInput'+index1" v-model="scoringItem.score" type="text" :class="'checkitem'+index" autocomplete="off"
															class="form-control inputDisabled" placeholder="请输入初评得分" style="width:125px;">
													</div>

													<div style="color:red; font-size: 12px;">{{scoringItem.validatorMessage}}</div>
												</div>
											</td>
										</tr>
									</table>
								</td>

			             		<!-- 一票否决 -->
								<td style="vertical-align:middle;">
									<div class="custom-checkbox text-center" v-if="scoringIndex.isVoteDown == 1" >
										<input v-if="scoringIndex.inCheckValue != 1" name="scoreInput" type="checkbox" :id="'isVoteDown'+index"
											@change="changeCheckBoxCli(index,'1')" v-model="scoringIndex.voteDownValue"
											style="margin-left:18px;width:16px; height:16px; display: table-cell;vertical-align: middle; text-align: center;">
										<input v-else name="scoreInput" type="checkbox" :id="'isVoteDown'+index"
											disabled v-model="scoringIndex.voteDownValue"
											style="margin-left:18px;width:16px; height:16px; display: table-cell;vertical-align: middle; text-align: center;">
										<label :for="'checkbox'+index" class="checkbox-blue" checked></label>
									</div>
								</td>

								<!-- 无需评查 -->
								<td style="vertical-align:middle;">
									<div class="custom-checkbox text-center" v-if="scoringIndex.isInCheck ==1">
										<input v-if="scoringIndex.voteDownValue != 1" name="scoreInput" type="checkbox" :id="'isInCheck'+index"
		 									@change="changeCheckBoxCli(index,'2')"  v-model="scoringIndex.inCheckValue"
											style="margin-left:18px;width:16px; height:16px;  display: table-cell;vertical-align: middle; text-align: center; ">
										<input v-else name="scoreInput" type="checkbox" :id="'isInCheck'+index"
		 									disabled  v-model="scoringIndex.inCheckValue"
											style="margin-left:18px;width:16px; height:16px;  display: table-cell;vertical-align: middle; text-align: center; ">
										<label for="checkbox1" class="checkbox-blue" checked></label>
									</div>
								</td>

								<!-- 得分 -->
								<td style="vertical-align:middle;">
									<div style="width:120px;float: right;">
					           			<input type="text"  disabled="disabled" v-model="scoringIndex.resultScore" class="form-control">
					           		</div>
								</td>

								<!-- 评审依据 -->
								<td >
									<textarea maxlength="1000" @input="updateCommon(index)" :id="'comment'+index" :rows="scoringIndex.expertHandlItemScoreList.length"
										class="form-control" v-model="scoringIndex.comment" placeholder="请输入评审依据，长度不能超过1000个字符" style="width:100%;">
									</textarea>
									<div style="color:red; font-size: 12px;">{{scoringIndex.validatorMessage}}</div>
								</td>
							</tr>
							<tr>
								<td rowspan="2"></td>
								<td rowspan="2"><strong>合计</strong></td>
								<td colspan="7"><strong>参与评查得分</strong></td>
								<td colspan="2"><strong>参与评查标准分</strong></td>
								<td colspan="2"><strong>卷面分</strong></td>
							</tr>
							<tr>
								<td colspan="7">{{inputScore}}</td>
								<td colspan="2">{{totalScore}}</td>
								<td colspan="2">
									<div style="width:120px;float: left;">
										<input type="text" disabled v-model="scoringIndexList.paperscore" class="form-control">
									</div>
								</td>
							</tr>
							<tr>
								<td height="30" colspan="13">
									<textarea maxlength="2000" v-model="scoringIndexList.problemRemark" rows="5" class="form-control" id="problem_remark" placeholder="疑难（争议）问题描述"></textarea>
								</td>
							</tr>
							<tr>
								<td colspan="2">委员A</br>疑难（争议）问题描述</td>
								<td height="30" colspan="13">
									<textarea maxlength="2000" v-model="scoringIndexList.problemRemarkA" rows="5" class="form-control" id="problem_remarkA" disabled="disabled" placeholder="疑难（争议）问题描述"></textarea>
								</td>
							</tr>
							<tr>
								<td colspan="2">委员B</br>疑难（争议）问题描述</td>
								<td height="30" colspan="13">
									<textarea maxlength="2000" v-model="scoringIndexList.problemRemarkB" rows="5" class="form-control" id="problem_remarkB" disabled="disabled" placeholder="疑难（争议）问题描述"></textarea>
								</td>
							</tr>
<%--							<tr>--%>
<%--			           			<td height="30" colspan="13">--%>
<%--			           				<input id="yxdxanlituijian" name="yxdxanlituijian" type="checkbox" v-on:click="checkBoxClick('1')" >优秀案卷推荐--%>
<%--		             		 		<div class="col-sm-12" style="display: none;" id ="yxdxAnLiTuiJianReviews1"  >--%>
<%--		             					<textarea maxlength="2000" v-model="scoringIndexList.yxdxanlituijianreviews" rows="5" class="form-control" id="zjpy" placeholder="请输入专家评语，长度不能超过2000个字符"></textarea>--%>
<%--		             		 		</div>--%>
<%--		             			</td>--%>
<%--							</tr>--%>
<%--							<tr>--%>
<%--								<td height="30" colspan="13">--%>
<%--									<input id="ajpjYxdxanlituijian" name="ajpjYxdxanlituijian" type="checkbox"  v-on:click="ajpjCheckBoxClick()" >较差案卷推荐--%>
<%--									<div class="col-sm-12" style="display: none;" id ="ajpjYxdxAnLiTuiJianReviews1"  >--%>
<%--										<textarea maxlength="2000" v-model="scoringIndexList.ajpjYxdxanlituijianreviews" rows="5" class="form-control" id="ajpj" placeholder="请输入专家评语，长度不能超过2000个字符"></textarea>--%>
<%--									</div>--%>
<%--								</td>--%>
<%--							</tr>--%>
							<tr>
								<td height="30" colspan="13" style="line-height: 3.1">
									<div class="col-sm-12" style="padding-left: 30px">
										<input id="yxdxanlituijian" name="yxdxanlituijian" type="checkbox" v-on:click="checkBoxClick('1')"  style="margin-right: 10px" >优秀案卷推荐</div>
									<div class="col-sm-12" style="display: none;padding-left: 30px" id ="yxdxAnLiTuiJianReviews1"  >
										<table>
											<c:forEach items="${tcDictionaryList}" var="item" varStatus="status">
												<tr>
													<input  type="checkbox" name="yxaj" value="${item.code}" >&nbsp;${item.name} &nbsp; &nbsp;
													<c:if test="${status.count%4==0}"><br></c:if>
												</tr>
											</c:forEach>

										</table>
										<textarea maxlength="2000" v-model="scoringIndexList.yxdxanlituijianreviews" rows="5" class="form-control" id="zjpy" placeholder="请输入专家评语，长度不能超过2000个字符"></textarea>
									</div>
								</td>
							</tr>
							<tr>
								<td height="30" colspan="13" style="line-height: 3.1">
									<div class="col-sm-12" style="padding-left: 30px">
										<input id="ajpjYxdxanlituijian" name="ajpjYxdxanlituijian" type="checkbox" style="margin-right: 10px" v-on:click="ajpjCheckBoxClick()" >较差案卷推荐</div>
									<div class="col-sm-12" style="display: none;padding-left: 30px" id ="ajpjYxdxAnLiTuiJianReviews1"  >
										<table>
											<c:forEach items="${jcajDictionaryList}" var="item" varStatus="status">
												<tr>
													<input  type="checkbox" name="jcaj" value="${item.code}" >&nbsp;${item.name}&nbsp; &nbsp;
													<c:if test="${status.count%4==0}"><br/></c:if>
												</tr>
											</c:forEach>
										</table>
										<textarea maxlength="2000" v-model="scoringIndexList.ajpjYxdxanlituijianreviews" rows="5" class="form-control" id="ajpj" placeholder="请输入专家评语，长度不能超过2000个字符"></textarea>
									</div>
								</td>
							</tr>
							<tr>
								<td height="30" colspan="9">
									<div class="col-sm-12" style="padding-left:30px">
										<input id="noTuiJian" name ="noTuiJian" type="checkbox" v-on:click="noCheckBoxClick()"  style="margin-right: 10px" >不推荐为典型案例
									</div>
								</td>
							</tr>
						</tbody>
					</table>
					<input id="pageNum"  type="hidden" value="${pageNum}" >
					<input id="expertFileId"  type="hidden" value="${expertFileId}" >
					<input id="fileId"  type="hidden" value="${expertHandlFileList.fileid}" >
				</form>
		 		<div class="submit">
<%--		 			<button v-if="scoringIndexList.scoredstate == 0 || scoringIndexList.scoredstate == 2" type="submit" class="btn btn-primary" id="submitTemporaryBtn" v-on:click="saveSubmit(scoringIndexList.id, '0')" style="font-size:14px;width:126px; height:40px;color:#ffffff;margin-top:5px;padding:0">信息暂存</button>--%>
		 			<button v-if="scoringIndexList.zeroScoreFileReviews != 1"   type="submit" class="btn btn-primary" id="submitTemporaryBtn" v-on:click="saveSubmit(scoringIndexList.id, '0')" style="font-size:14px;width:126px; height:40px;color:#ffffff;margin-top:5px;padding:0">信息暂存</button>
	 				<button v-if="scoringIndexList.zeroScoreFileReviews != 1" type="submit" class="btn btn-danger" id="submitBtn" v-on:click="saveSubmit(scoringIndexList.id, '1')" style="font-size:14px;width:126px; height:40px;color:#ffffff;padding:0;margin-top:5px;">信息保存</button>
 				</div>
				<!--  附件查看 -->
				<div class="modal fade" id="view" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
					<div class="modal-dialog modal-lg">
						<div class="modal-content"></div>
					</div>
				</div>
				
				<!-- 零分案卷（Modal） -->
				<!-- <div class="modal fade" id="zeroScoreFile" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true" data-backdrop="static" data-keyboard="false">
				    <div class="modal-dialog">
				        <div class="modal-content">
				            <div class="modal-header">
				                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
				                <h4 class="modal-title" id="myModalLabel">零分案卷</h4>
				            </div>
				            <div class="modal-body form-horizontal">
				                <div class="form-group" style="padding:2px;">
				                	<label class="col-lg-3 control-label">零分案卷原因</label>
				                    <div class="col-lg-8">
				                		<textarea maxlength="1000" @input="updateZeroScoreReviews()" id="zeroScoreFileReviews" v-model="scoringIndexList.zeroScoreFileReviews" 
				                			class="form-control" rows="5" placeholder="请输入零分案卷原因"></textarea>
				                    </div>
				                </div>
				                <div class="form-group">
				                	<label class="col-lg-3 control-label"></label>
				                	<div id="zeroScoreText" class="col-lg-8 hide" style="color:red; font-size: 12px;">零分案卷原因不可为空！</div>
			                	</div>
				                <div class="form-group">
				                	<label class="col-lg-3 control-label"></label>
				                	<div class="col-lg-8">
				                		<span style="color:red;">* 提示：评为零分案卷后将不可更改！且长度不能超过1000个字符！</span>
				                	</div>
			                	</div>
				            </div>
				            
				            <div class="modal-footer">
				                <button type="button" class="btn btn-primary" v-on:click="subZeroScoreReviews(scoringIndexList.id)">保存</button>
				                <button v-if="scoringIndexList.zeroScoreFileReviews != null && scoringIndexList.zeroScoreFileReviews != ''" 
				                onclick="bcakList()" type="button" class="btn btn-default">返回</button>
				                <button v-else type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
				            </div>
				        </div>
				    </div>
				</div> -->
			</div>
		</div>
	</div>
</body>
</html>