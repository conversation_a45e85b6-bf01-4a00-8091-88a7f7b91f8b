<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<html>
<head>

<script type="text/javascript">
    $(".left_menu").hide();

	$(document).ready(function(){
		business.listenEnter("searchButt");
		$("#searchButt").click(function(){
			business.addMainContentParserHtml("zjpf/zjpfList.do",$("#searchForm").serialize());
		});
		$("#scoredState").change(function(){
			business.addMainContentParserHtml("zjpf/zjpfList.do",$("#searchForm").serialize());
		});
	});
	//分页
	$(document).ready(function(){
        var userTypeCode = ${userTypeCode};
        var UserId = '${UserId}';

		var curentPage = eval('${pageBean.pageNum}');
		var totalPage = eval('${pageBean.pages}');
// debugger
		var errorRate= '${errorRate}';
		if(errorRate!=null&& errorRate!=''){
		    // alert(errorRate);
            swal({title: "" ,text: errorRate,type:"success"});

            // swal("提示", "有异常的案卷,请先检查异常的评查", errorRate);
        }
		if(totalPage>0){
			var options = {
				bootstrapMajorVersion: 3,
			    currentPage: curentPage,
			    totalPages: totalPage,
			    numberOfPages: 5,
			    itemTexts: function (type, page, current) {
			            switch (type) {
			            case "first":
			                return "首页";
			            case "prev":
			                return "&laquo;";
			            case "next":
			                return "&raquo;";
			            case "last":
			                return "尾页";
			            case "page":
			                return page;
			            }
			    },
			    onPageClicked: function (event, originalEvent, type, page) {
                    if (userTypeCode == '4' && UserId!=null){
                        business.addMainContentParserHtml(WEBPATH+'/zjpf/zjpfLists.do?UserId='+UserId+'&pageNum='+page,$("#searchForm").serialize());
                    }else {
                        business.addMainContentParserHtml(WEBPATH+'/zjpf/zjpfList.do?pageNum='+page,$("#searchForm").serialize());
                    }
	            }
	    	};
	    	$('#pageCon').bootstrapPaginator(options);
    	}
	});

	/* function showZeroScoreModel(handfileId) {
		var zeroReviews = $("#zeroComment"+handfileId).val();
		$("#zeroScoreText").html(zeroReviews);
		$("#zeroScoreFile").modal("show");
	} */

	function startScore(id){
		$.ajax({
			url: WEBPATH+"/zjpf/checkExpert.do",
			type:'post',
			async:false,
			data:{experHandID:id},
			success:function(data){
				if(data.result=="success"){
					if(data.data=="1"){
					    /*跳转评实体页面*/
                        <%--macroMgr.onLevelTwoMenuClick(null, "zjpf/entityAScore.do?id="+id+"&pageNum=${pageNum}");--%>
                        /*跳转评案卷页面*/
						macroMgr.onLevelTwoMenuClick(null, "zjpf/zjpfScore.do?id="+id+"&pageNum=${pageNum}");

					}else if(data.data=="0"){
						swal("提示", "有未完成的案卷，请先完成上一份案卷的评查", "info");
					}else if(data.data=="3"){
					    //有异常也允许评案卷
                        <%--macroMgr.onLevelTwoMenuClick(null, "zjpf/zjpfScore.do?id="+id+"&pageNum=${pageNum}");--%>
                        swal("提示", "有异常的案卷,请先检查异常的评查", "info");
                    }
	            }else{
	            	if(data.code=="000"){
	            		swal({
					        title: "提示",
					        text: data.message,
					        type: "error",
					        confirmButtonText: "确定",
					        confirmButtonColor: "#ec6c62"
					    }, function() {
					        window.location.href=WEBPATH+"/index.do";
					    });
	            	}else{
	            		swal("提示", "后台出现未知异常", "error");
	            	}
	            }
			},
			error:function(){
				 swal("error", "请求错误", "error");
			}
		})
	}

	/**
     * 异常信息详情
	 */
    function errorState(fileId){
        macroMgr.onLevelTwoMenuClick(null, "zjpf/errorState.do?fileId="+fileId);
    }
</script>
</head>
<body>
<div class="center_weizhi" id ="top_title">当前位置：专家评分 - 专家评审案卷 - ${expertTypeName}</div>
<div class="center">
<div class="center_list">
    <div class="panel-group" id="accordion">
        <input type="hidden" value="${errorRate}" id="errorRate"/>
        <h4 class="panel-title">
            <!---信息填报
            <div class="btn-group" style="margin-right:20px;float:left;">
               <a href="CementEnterprises_input.html"><button type="button" class="btn btn-success" style="font-size:16px;">企业信息录入</button></a>
            </div>--->
          <form role="form" method="get" id ="searchForm" name ="form">
              <select id ="scoredState" class="form-control" style="width:150px;margin-right:5px;" name="scoredState" >
                 <option value ="">请选择评分状态</option>
                 <!-- 1代表已评状态，0代表未评状态 -->
                 <option value="1" <c:if test="${scoredState=='1' }">selected</c:if> >已评</option>
                 <option value="0" <c:if test="${scoredState=='0' }">selected</c:if>>未评</option>
<%--                 <option value="5" <c:if test="${scoredState=='5' }">selected</c:if>>异常</option>--%>
              </select>
        	<!-- </form>  -->
            <!---搜索--->
            <div style="width:260px;" class="btn-group">
          <!--      <form class="bs-example bs-example-form" role="form" id="fileCodeForm"> -->
                  <div class="row">
                     <div class="col-lg-6">
                        <div class="input-group">
                           <input type="text" placeholder="案卷文号关键字" class="form-control" name ="fileCode" value="${fileCode }" style="width:200px;">
                           <span class="input-group-btn">
                              <button id="searchButt"  class="btn btn-success" type="button">
                               	  快速搜索
                              </button>
                           </span>
                        </div><!-- /input-group -->
                     </div><!-- /.col-lg-6 -->
                  </div><!-- /.row -->
            </div>
     </form>
          </h4>
      </div>
        <table class="table table-bordered table-hover table-condensed" >
         <thead>
           <tr>
             <td width="50" height="30" bgcolor="#efefef">序号</td>
             <td bgcolor="#efefef">案卷文号</td>
               <td width="200"  bgcolor="#efefef">案卷类型</td>
<%--             <td width="150"  bgcolor="#efefef">是否填写加分项</td>--%>
<%--               <td width="150" bgcolor="#efefef">加分类型</td>--%>
               <td width="150" bgcolor="#efefef">专家姓名</td>
<%--               <td width="150" bgcolor="#efefef">初评得分</td>--%>
               <td width="150" bgcolor="#efefef">是否推荐</td>
             <td width="60" bgcolor="#efefef">状态</td>
             <td width="100" bgcolor="#efefef">操作</td>
           </tr>
         </thead>

         <c:if test="${not empty pageBean.list }">
         <c:forEach  varStatus="id"  items="${pageBean.list}" var="experList">
          <c:choose>
            <c:when test="${experList.errorState == '1'}">
                <tbody style="background-color: #f0d8e0">
            </c:when>
            <c:otherwise>
                <tbody>
            </c:otherwise>
         </c:choose>

         	<tr>
         		<td height="30" align="center" >${ id.index + 1}</td>
         	<c:choose>
        		<c:when test="${experList.zeroScoreFileReviews eq null or experList.zeroScoreFileReviews eq '' }">
	          		<td >${experList.fileCode}</td>
	          	</c:when>
	          	<c:otherwise>
	          		<td style="color:red" title="零分案卷">${experList.fileCode}</td>
		    	</c:otherwise>
			</c:choose>
         		<td>${experList.fileTypeName}</td>
<%--         		<td>${experList.hasPlusItems}</td>--%>
<%--         		<td>${experList.plusItemTypes}</td>--%>
         		<td>${experList.expertName}</td>
<%--         		<td>${experList.expertFinalScore }</td>--%>
         		<td>${experList.ajpjYxdxanlituijianCode}</td>
         		<c:choose>
         			<c:when test="${experList.scoredState == '0' }">
						<td><button class="btn btn-info btn-xs" >未评</button></td>
					</c:when>
					<c:when test="${experList.scoredState == '1' }">
						<td><button class="btn btn-success btn-xs">已评</button></td>
					</c:when>
                    <c:when test="${experList.scoredState == '5' }">
                        <td><button class="btn btn-danger btn-xs" onclick="errorState('${experList.fileId}')">查看异常</button></td>
                    </c:when>
					<c:otherwise>
						<td><button class="btn btn-warning btn-xs">评审中</button></td>
					</c:otherwise>
				</c:choose>

               	<td align="center">
               		<c:if test ="${sessionScope.sa_session.sysStatus == '5' and experList.considerState != '1'  and userTypeCode!='4'}">
		          		<c:choose>
         					<c:when test="${experList.enabled eq '1' }">
         						<c:if test="${experList.scoredState == '1' }">
         							<a  href="javascript:void(0);" onclick="macroMgr.onLevelTwoMenuClick(null, 'zjpf/zjpfScore.do?id=${experList.id}&pageNum=${pageNum}' )"><button class="btn btn-danger btn-xs" >评分</button></a>
         						</c:if>
         						<c:if test="${experList.scoredState != '1' }">
         		   	  				<a href="javascript:void(0);" onclick="startScore('${experList.id}')"><button class="btn btn-danger btn-xs" >评分</button></a>
         		   	  			</c:if>
         		   	  		</c:when>
		          			<c:otherwise>
		          				待开放…
		          			</c:otherwise>
		          		</c:choose>
         		    </c:if>

               		<c:if test ="${experList.scoredState != '0'  }">
<%--               			<a href="javascript:void(0);" onclick="macroMgr.onLevelTwoMenuClick(null, 'zjpf/zjpfScoreView.do?id=${experList.id}&status=1' )"><button class="btn btn-warning btn-xs" >查看</button></a>--%>
               			<a href="javascript:void(0);" onclick="macroMgr.onLevelTwoMenuClick(null, 'zjpf/entityAView.do?id=${experList.id}')"><button class="btn btn-warning btn-xs" >查看</button></a>
<%--                        <button class="btn btn-default" style="padding:9px 34px;margin-right:14px" onclick="macroMgr.onLevelTwoMenuClick(null, 'zjpf/entityAView.do?id=${expertFileId}&pageNum=${pageNum}')">合法性评查</button>--%>
	               		<%-- <c:choose>
	         				<c:when test="${experList.zeroScoreFileReviews eq null or experList.zeroScoreFileReviews eq '' }">
	               				<a href="javascript:void(0);" onclick="macroMgr.onLevelTwoMenuClick(null, 'zjpf/zjpfScoreView.do?id=${experList.id}&status=1' )"><button class="btn btn-warning btn-xs" >查看</button></a>
			          		</c:when>
			          		<c:otherwise>
			          			<button class="btn btn-warning btn-xs" onclick="showZeroScoreModel('${experList.id}')" id ="chickAnjuan">查看</button>
			          			<input type="hidden" id="zeroComment${experList.id}" value="${experList.zeroScoreFileReviews}" />
			          		</c:otherwise>
			          	</c:choose> --%>
		          	</c:if>
               	</td>
		    	</tr>
         </c:forEach>
         </c:if>
         </tbody>
       </table>
    </div>
      <div class="page">
    <span style="padding:12px; float:left; color:#0099cc;">共${pageBean.total}条记录</span>
        <ul class="pagination" id="pageCon">
        </ul>
    </div>


    <!-- 零分案卷（Modal） -->
	<!-- <div class="modal fade" id="zeroScoreFile" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
	    <div class="modal-dialog">
	        <div class="modal-content">
	            <div class="modal-header">
	                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
	                <h4 class="modal-title" id="myModalLabel">零分案卷</h4>
	            </div>
	            <div class="modal-body form-horizontal">
	                <div class="form-group" style="padding:2px;">
	                	<label class="col-lg-3 control-label">零分案卷原因</label>
	                    <div class="col-lg-8">
	                		<textarea id="zeroScoreText" rows="10" class="form-control"></textarea>
	                    </div>
	                </div>
	            </div>

	            <div class="modal-footer">
	                <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
	            </div>
	        </div>
	    </div>
	</div> -->
</div>
</body>
</html>
