<!DOCTYPE html>
<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>

<style type="text/css">
	.panel-body{
		height: auto!important;
		min-height: 500px!important;
	}
	.pdfobject-container {
		width: 100%;
		height:580px;
		margin: 2em 0;
	}
	.textarea-success {
		border-top:green 1px solid;
		border-bottom:green 1px solid;
		border-left:green 1px solid;
     	border-right:green 1px solid;
	}
	.textarea-error {
		border-top:red 1px solid;
		border-bottom:red 1px solid;
		border-left:red 1px solid;
     	border-right:red 1px solid;
	}
	.no-close .ui-dialog-titlebar-close {
		display: none;
	}
	.from_gaozhi {
		border-bottom-width: 1px;
		border-bottom-style: solid;
		border-bottom-color: #CCC;
		border-top-width: 1px;
		border-right-width: 1px;
		border-left-width: 1px;
		border-top-style: solid;
		border-right-style: solid;
		border-left-style: solid;
		border-top-color: #FFF;
		border-right-color: #FFF;
		border-left-color: #FFF;
		width:100%;
		font-size:14px;
	}
	.tag {
		border: solid 1px #ddd;
		padding: 5px;
		margin: 5px 0;
		cursor: pointer;
		width: 300px; /* 根据需要调整宽度 */
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}
	.modal {
		display: none;
		position: fixed;
		z-index: 1000;
		left: 0;
		top: 0;
		width: 100%;
		height: 100%;
		overflow: auto;
		background-color: rgba(0, 0, 0, 0.7);
		padding: 60px;
	}
	.modal-content {
		background-color: #fff;
		margin: auto;
		padding: 20px;
		border: 1px solid #888;
		width: 80%;
	}
	.close {
		color: #aaa;
		float: right;
		font-size: 28px;
		font-weight: bold;
	}
	.close:hover,
	.close:focus {
		color: black;
		text-decoration: none;
		cursor: pointer;
	}

	/*.modal {*/
	/*display: none;*/
	/*position: fixed;*/
	/*z-index: 1;*/
	/*left: 0;*/
	/*top: 0;*/
	/*width: 100%;*/
	/*height: 100%;*/
	/*overflow: auto;*/
	/*background-color: rgba(0,0,0,0.4);*/
	/*}*/
	.noline-input{
		border: none;
		border-bottom: 1px solid black;
		outline: none;
	}
	/*.modal-content {*/
	/*background-color: #fefefe;*/
	/*margin: 15% auto;*/
	/*padding: 20px;*/
	/*border: 1px solid #888;*/
	/*width: 80%;*/
	/*}*/
	.icon {
		margin: 3px 0; /* 每个图标之间的间距 */
		display: block; /* 确保图标在单独一行 */
		font-size: 15px; /* 增大图标的大小 */
		text-align: center; /* 使内容居中 */

	}
	.close {
		color: #aaa;
		float: right;
		font-size: 28px;
		font-weight: bold;
	}

	.close:hover,
	.close:focus {
		color: black;
		text-decoration: none;
		cursor: pointer;
	}
	body {
		font-family: Arial, sans-serif;
		margin: 0;
		padding: 20px;
	}

	/*thead tr {
		background-color: #efefef; !* 表头背景色 *!
	}*/

	/* 固定表头 */
	/*th {*/
	/*position: sticky; !* 使表头固定 *!*/
	/*top: 0; !* 距离顶部0 *!*/
	/*z-index: 10; !* 设置z轴顺序，使其在其他内容上方 *!*/
	/*}*/

	/*tbody {*/
	/*display: block; !* 将tbody设为块级元素 *!*/
	/*max-height: 800px; !* 设置最大高度 *!*/
	/*overflow-y: auto; !* 允许垂直滚动 *!*/
	/*}*/
	/*tr {*/
	/*display: table; !* 让tr表现为表格行 *!*/
	/*table-layout: fixed; !* 固定表格布局 *!*/
	/*width: 100%; !* 使行宽度100% *!*/
	/*}*/



	button {
		padding: 10px 20px;
		font-size: 16px;
	}

	button#confirmButton {
		background-color: #007bff; /* 蓝色背景 */
		color: white; /* 白色文字 */
		display: block; /* 设置为块级元素，便于居中 */
		margin: 20px auto; /* 自动左右边距，居中对齐 */
		border: none; /* 去掉默认边框 */
		border-radius: 5px; /* 圆角效果 */
	}

	button#confirmButton:hover {
		background-color: #0056b3; /* 悬停时的颜色变化 */
	}
	#completionMessage h3 {
		margin-top: 20px; /* 图标与标题之间的间隔 */
		margin-bottom: 10px; /* 标题与内容之间的间隔 */
	}

	#completionMessage p {
		margin: 0; /* 清除段落的默认外边距 */
	}

	.jdmodal {
		display: none; /* 隐藏模态框 */
		position: fixed; /* 固定定位，以覆盖整个视口 */
		z-index: 1000; /* 确保模态框位于最上层 */
		left: 0;
		top: 0;
		width: 100%;
		height: 100%;
		overflow: auto;
		background-color: rgba(0, 0, 0, 0.5); /* 半透明背景 */

	}

	.jdmodal-content {
		background-color: #fefefe;
		position: absolute; /* 使用绝对定位 */
		left: 50%; /* 左边距为50% */
		top: 50%; /* 顶边距为50% */
		transform: translate(-50%, -50%); /* 通过变换实现真正的居中 */
		padding: 20px;
		border: 1px solid #888;
		width: 800px;
		text-align: center;
		z-index: 1001; /* 确保模态内容位于模态背景之上 */
	}

	.progress {
		background-color: #f3f3f3;
		border: 1px solid #ccc;
		height: 20px;
		width: 100%;
		margin: 20px 0;
	}

	.progress-bar {
		background-color: #4caf50;
		height: 100%;
		width: 0;
		transition: width 0.1s; /* 动画效果 */
	}
	#span1{
		display: block;
		font-size: 18px;
		max-height:100px;
		overflow:auto;
		font-weight: 400;
		color:#333333;
	}
	.panel-title{
		font-size: 18px;
		margin-left: 10px;
	}
	.error {
		border: 1px solid red;
	}
	.center_list {
		/*margin: px 20px;*/
	       background: #ffffff;
		width: 100%;
		left: 0;
		margin: 0;}

	/* 完成消息样式 */
	#completionMessage {
		margin-top: 20px;
	}

	.custom-container {
		position: relative;
		text-align: left; /* 居中显示内容 */
	}

	.custom-icon-button {
		font-size: 24px; /* 调整图标大小 */
		cursor: pointer;
		background: none;
		border: none;
		color: #f39c12; /* 图标颜色 */
		transition: transform 0.2s; /* 添加动画效果 */
	}

	.custom-icon-button:hover {
		transform: scale(1.2); /* 鼠标悬停时放大 */
	}

	.custom-modal {
		display: none; /* 默认隐藏 */
		position: fixed;
		z-index: 1;
		left: 0;
		top: 0;
		width: 100%;
		height: 100%;
		overflow: auto;
		background-color: rgba(0, 0, 0, 0.7); /* 深色背景 */
	}

	.custom-modal-content {
		background-color: #ffffff;
		margin: 10% auto;
		padding: 30px;
		border-radius: 10px; /* 圆角边框 */
		width: 90%;
		max-width: 600px; /* 最大宽度 */
		box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3); /* 阴影效果 */
	}

	.custom-modal-title {
		font-size: 24px; /* 标题字体大小 */
		margin-bottom: 20px; /* 标题下边距 */
		color: #333; /* 标题颜色 */
	}

	.custom-modal-list {
		list-style: none; /* 移除列表样式 */
		padding-left: 0; /* 移除左边距 */
	}

	.custom-modal-list li {
		margin-bottom: 10px; /* 列表项下边距 */
		line-height: 1.6; /* 行间距 */
		color: #555; /* 列表项颜色 */
	}

	.custom-close-button {
		color: #aaa;
		float: right;
		font-size: 28px;
		font-weight: bold;
	}

	.custom-close-button:hover {
		color: #333; /* 鼠标悬停时变色 */
		cursor: pointer;
	}

	.custom-highlight {
		color: red; /* 重点内容颜色 */
		font-weight: bold; /* 加粗重点内容 */
	}

	.panel-tool a{
		display: none;
	}
	.table>thead>tr>td{line-height: 3.1}
	.table-bordered>thead>tr>td{background-color: #F5F7FA;border:1px solid #EBEEF5;}
	input[type=checkbox], input[type=radio]{height:16px;width:16px;position: relative;top:3px;right:2px;}
	input[type=radio]{ margin-right: 5px;margin-left: 10px;}
	select.bs-select-hidden, select.selectpicker{display: inline!important;}
	.ismycheck{margin: 0 10px!important;}
</style>
</head>
<script type="text/javascript">

	$(".left_menu").hide();
	var indexOneList="${indexOneList}";
	var arr=indexOneList.split("@");
	//多选下拉框所在的div
	var selecteddiv = document.getElementById("selectdiv");
    var problemRemark= $("#problemRemark").val();

	//鼠标是否在【多选下拉框div】上面（如果在div上面，需要控制鼠标的点击事件，不让div隐藏；否则要让该div隐藏）
	var indiv = false;

	//选中的股票代码（需要传到后台的参数）
	var selectedlist = [];
	//选中的股票名称（展示在前台给业务员看的）
	var selectednamelist = [];

    //系统初始化函数
	$(function(){

		//初始化卷面一级指标
		for(var i = 0; i < arr.length; i++){
			// data.data.expertHandlIndexScoreList[i].className = 'class' + data.data.expertHandlIndexScoreList[i].id;
			var tmpdiv = document.createElement("div");
			var tmpinput = document.createElement("input");
			var str = arr[i];
			tmpinput.setAttribute("name","mycheckbox");
			tmpinput.setAttribute("class","ismycheck");
			tmpinput.setAttribute("type","checkbox");
			tmpinput.setAttribute("onclick","mycheck(this)");
			tmpinput.setAttribute("value",str);
			tmpinput.setAttribute("data",str);
			var tmptext = document.createTextNode(arr[i],arr[i].length);
			tmpdiv.appendChild(tmpinput);
			tmpdiv.appendChild(tmptext);
			selecteddiv.appendChild(tmpdiv);
			//console.log(tmpinput)
		}
		console.log(selecteddiv)
		//

		$('#my_dialog').dialog({
			width : "900",
			autoOpen : false,
			resizable : false,
			modal : true,
			closed: true
		});
		$(".disNo").removeAttr("disabled");

		//$("#my_dialog").css('display','none')

		/*查看争议异常的弹窗*/
		$('#error_dialog').dialog({
			title: "查看争议",
			width : "900",
			height : "450",
			autoOpen : false,
			resizable : false,
			modal : true,
			closed: true
		});

	});
	//关闭查看异常的窗口
	function entity_error_close(){
		$('#error_dialog').dialog("close")
		//清空具体描述的单选框
		$('#caseInfo').val('');
		//清空专家否决选项
		$('#itemOption_entity').val('');
		//清空专家否决描述
		$('#itemComme_entity').val('');
		$('#isAOrB_entity').val('');
		$('#entity_error_id').val('');
		$('#error-message').text('');
		document.getElementById('isAgree_entity_0').disabled = false;
		document.getElementById('isAgree_entity_1').disabled = false;
		document.getElementById('caseInfo').readOnly = false;
		//清空是否认同单选框
		$('input[name="isAgree_entity"]').prop('checked', false);
		// document.querySelector('#error_submit').style.display = 'block';

		// business.addMainContentParserHtml(WEBPATH+'/zjpf/entityAScore.do?id='+expertFileId+'&pageNum=1');
	}

	//异常窗口的 确认提交 按钮
	function error_submit(){
		//是否认可:0不认可,1认可
		var isAgree = $('input[name=isAgree_entity]:checked').val();
		//是否认可的具体情形
		var caseInfo = $("#caseInfo").val();
		var isAOrB = $("#isAOrB_entity").val();
		var id = $("#entity_error_id").val();

		console.log("是否认可:",isAgree,",描述:",caseInfo)
		if (isAgree==null || isAgree== undefined){
			swal("信息", "【是否认同】不能为空", "error");
			return false;
		}
		// 具体情形改为非必填(20241015)
		// if (isAgree == '0'){
		// 	if (caseInfo == null || caseInfo ==undefined || caseInfo==''){
		// 		swal("信息", "请输入【不认同】的【具体情形】", "error");
		// 		return false;
		// 	}
		// }

		//调保存接口,将是否认可和具体情形存到库里
		$.ajax({
			type: "post",
			url: WEBPATH + '/zjpf/saveErrorIsAgree.do',
			data: {
				isAgree: isAgree,
				caseInfo: caseInfo,
				isAOrB: isAOrB,
				id: id
			},
			success: function (data) {
				console.log(data)
				if (data.result=="success"){
					entity_error_close();
				}else {
					swal("信息", data.message, "error");
				}

			}
		});


	}



		// 鼠标点击事件，如果点击在 selectedbutton，或者是在多选框div中的点击事件，不作处理。其他情况的点击事件，将多选空div隐藏
		document.onclick=function(event){
			if(event.target.id=="commeStrThree" || indiv){
				return;
			}
			selecteddiv.style.display="none";
			document.getElementById("fuzzysearchdiv").style.display="none";
		};
			// 设法循环点击 checkbox 使之触发全选
		// defaultChecked();

		// 页面加载时 循环遍历 生成初始化默认选择checkbox
		// function defaultChecked(){
		// 	for(var i = 0; i < arr.length; i++){
		// 		//   alert('i======'+i);
		// 	}
		// }

		//点击selectButton，展示多选框
		function myclick (){
			// document.getElementById("fuzzysearchdiv").style.display="block";
			// selecteddiv.style.display="block";
			$("#selectdiv").css("display","block");
		}

		//鼠标进入多选框的div【selectdiv】
		function mousein(){
			// console.log('1111')
			// $("#selectdiv").css("display","block");
			indiv = true;
			fn()
		}

		//鼠标离开多选框的div【selectdiv】
		function  mouseout(){
			// console.log('2')
			// $("#selectdiv").css("display","none");
			indiv = false;
			fn()
		}
		function fn() {
			if(indiv) {
				$("#selectdiv").css("display","block");
			} else {
				$("#selectdiv").css("display","none");
			}
		}
		//checkbox的点击事件
		function mycheck(obj){
			console.log("obj",obj)
			//debugger
			var dataValue = obj.getAttribute('data');
			$("#commeStrThree").val("");
			if(obj.checked){
				// selectedlist.push(obj.value);
				selectedlist.push(dataValue);
				selectednamelist.push(obj.nextSibling.nodeValue);
			}else{
				for(var i = 0; i < selectedlist.length; i++){
					// if(selectedlist[i] == obj.value){
					if(selectedlist[i] == dataValue){
						selectedlist.splice(i,1);
						selectednamelist.splice(i,1);
					}
				}
			}
			// console.log(selectedlist,selectednamelist)
			document.getElementById("commeStrThree").value=selectednamelist;
			// $('input[name="selectButton"]').val(selectednamelist);
		}


		var mater = '${expertHandlFileList.fileMaterials}'
		if (mater == '1'){
			$("#fileMaterials").attr("checked",true);
		}


		business.listenEnter();
		var pageNum =$("#pageNum").val();
		var scoringIndexList =null;
		var expertFileId = $("#expertFileId").val();
		$.ajax({
			cache : true,
			type : "GET",
			async : false,
			url: WEBPATH+"/zjpf/getEntityIndexs.do",
			data:{
				id:expertFileId
			},
			error : function(request) {
				swal("错误!","请求异常！", "error");
			},
			success : function(data) {
			  	if(data.result =='success'){

			  		scoringIndexList=data.data;
                    scoringIndexList.problemRemark = problemRemark;
			  		// console.log(scoringIndexList);
			  	}
			}
		});

		var xzcfVue = new Vue({
			  el: '#ExpertVue',
			  data: {
				  scoringIndexList:scoringIndexList,
			  },
			  mounted: function(){
					this.sumScore();
                  this.initAi();
				  //ai智能识别赋值
				  // this.aiInfoColor();
				//  setTimeout(this.saveTime,1000*60*30);//30分钟自动保存
				},
			  methods: {
                  initAi:function(){
                      if (this.scoringIndexList != null){
                          var isEntityAiView = this.scoringIndexList.isEntityAiView;
                          // if(isEntityAiView=="1"){
                              // document.getElementById('aistartEntity').disabled = true;
                              this.entityiAInfoColor();
                          // }
                      }
                  },
                  startEntitytion:function(){
                      document.getElementById("jdmodal").style.display = "block";
                      this.startEntityProgress();
				  },
                  closejdModal:function() {
                      document.getElementById("jdmodal").style.display = "none";
                  },
                  confirmEntityRecognition:function() {
                      // alert("确认识别已完成！最终分数：" + currentScore.toFixed(1));

                      this.closejdModal();

                      this.entityiAInfoColor();
                      this.updateAiEntityView();
                      // document.getElementById('aistartEntity').disabled = true;
                  },
                  updateAiEntityView:function(){
                      $.ajax({
                          type:"post",
                          url:WEBPATH+'/zjpf/updateEntityAiView.do',
                          data:{id:expertFileId},           //注意数据用{}
                          success:function(data){  //成功
                          }
                      });
                  },
                  startEntityProgress:function() {

                      let progress = 0;
                      const progressBar = document.getElementById("progressBar");
                      const status = document.getElementById("jdstatus");
                      const confirmButton = document.getElementById("confirmButton");

                      const completionMessage = document.getElementById("completionMessage");
                      const modalTitle = document.getElementById("modalTitle");

                      const totalDuration = 15000; // 总时间 15 秒
                      const totalSteps = 50; // 进度条分为 50 步
                      const stepDuration = totalDuration / totalSteps; // 每步的持续时间
                      const incrementPerStep = 100 / totalSteps; // 每步进度增加的百分比

                      const interval = setInterval(function() {
                          if (progress < 100) {
                              // 增加固定的进度增量
                              progress += incrementPerStep;

                              // 确保进度不超过100%
                              if (progress > 100) {
                                  progress = 100;
                              }

                              progressBar.style.width = progress + "%";
                              status.innerText = "进度：" + Math.round(progress) + "%";

                          }

                          if (progress >= 100) {
                              clearInterval(interval);
                              status.innerText = "已完成识别";

                              document.getElementById("modalTitle").style.display = "none"; // 隐藏标题
                              progressBar.parentNode.style.display = "none"; // 隐藏进度条
                              status.style.display = "none"; // 隐藏状态

                              // 显示完成信息
                              completionMessage.style.display = "block"; // 显示完成信息
                              confirmButton.style.display = "block"; // 显示确认按钮
                          }
                      }, stepDuration) // 每步持续时间
                  },

				  //ai评查内容赋值
                  entityiAInfoColor:function(){

					  if (scoringIndexList != null && scoringIndexList.expertHandlIndexScoreList !=null){
						  console.log("处理AI数据:",scoringIndexList.expertHandlIndexScoreList);
						  var keywords = ["未提到", "没有提到", "未提供", "没有提供", "无法确定", "没有提及", "未在", "未明确提及", "没有明确提及", "未提及", "没有包含","没有明确标注","没有找到","疑似存在问题"];
						  var expertHandlIndexScoreList = scoringIndexList.expertHandlIndexScoreList;
						  //循环大项
						  for (let i = 0; i < expertHandlIndexScoreList.length; i++) {
							  var expertHandlItemScoreList = expertHandlIndexScoreList[i].expertHandlItemScoreList;
							  //循环大项里面的小项
							  for (let j = 0; j < expertHandlItemScoreList.length; j++) {
								  var itemScore = expertHandlItemScoreList[j];
								  // console.log("isAi:",itemScore.isAi,itemScore.isAi ==null,"itemScore:",itemScore)
								  //判断小项不为空,并取出ai内容
								  if (itemScore!=null && itemScore.aiInfo != null){
									  console.log("ai评查项")
									  var aiInfo = itemScore.aiInfo;
									  // console.log("这里是ai智能评查内容:",i,j,aiInfo)

                                      var formattedString = itemScore.htmlaiInfo;
                                      var aiInfoId = 'aiInfo'+i+j;

                                      var aiStatusId = 'aiStatus'+i+j;

                                      if (formattedString !== null && formattedString !== undefined) {
										  var tagsContainer  = document.getElementById(aiInfoId);
										  tagsContainer.innerHTML = '';
										  const tag = document.createElement("span");
										   if (formattedString.includes('暂未发现问题')){
										     tag.innerHTML = "<span style='color: green; font-weight: bold;'>暂未发现问题</span> \n<br/>";
										   }else{
										     tag.innerHTML = formattedString;
										   }
										      tagsContainer.appendChild(tag);
										      tagsContainer.appendChild(document.createElement("br"));

                                          // document.getElementById(aiInfoId).innerHTML = formattedString;
                                          // const lines = formattedString.split("\r\n");
                                          // var tagsContainer  = document.getElementById(aiInfoId);
                                          // 清空容器





                                          // lines.forEach(line => {
                                          //     const tag = document.createElement("span");
										  //
                                          //  if (line.includes('暂未发现问题')){
											//    tag.innerHTML = "<span style='color: green; font-weight: bold;'>暂未发现问题</span> \n<br/>";
										  //  }else{
											//    tag.innerHTML = line;
										  //  }
										  //
										  //
                                          //     // 使用 innerHTML 渲染HTML内容
                                          //     tagsContainer.appendChild(tag);
                                          //     tagsContainer.appendChild(document.createElement("br"));
                                          // });

                                      }

								  }
								  //如果不是ai评查项,需要展示文字: 该项人工评查
								  if (itemScore!=null && (itemScore.isAi ==null || itemScore.isAi == '0' )){
									  var manualInfo = 'aiInfo'+i+j;
									  console.log("该项人工评查:",manualInfo)
									  var spanInfo  = document.getElementById(manualInfo);

									  if(spanInfo) {
										  var newSpan = document.createElement("span");
										  newSpan.innerHTML = "该项人工评查";
										  spanInfo.appendChild(newSpan);
									  } else {
										  console.error("Element does not exist: ", manualInfo);
									  }
								  }




							  }
						  }
					  }
				  },


				  showErrorMsg:function(obj){

					  document.getElementById('isAgree_entity_0').disabled = false;
					  document.getElementById('isAgree_entity_1').disabled = false;
					  $.ajax({
						  type:"post",
						  url:WEBPATH+'/zjpf/getOtherExpertScore.do',
						  data:{
							  fileId:$("#fileId").val(),
							  expertId:$("#expertId").val(),
							  handlType:$("#handlType").val(),
							  indexId:obj.indexId,
							  itemid:obj.itemid
						  },
						  success:function(data){  //成功
							  if (data.result=="success") {
								 console.log(data.data)
								  if (data.data.option == '0'){
									  $('#itemOption_entity').text("未否决");
								  }else if (data.data.option == '1'){
									  $('#itemOption_entity').text("已否决");
								  }else {
									  $('#itemOption_entity').text("无内容");
								  }

								  if (data.data.comme == null){
									  $('#itemComme_entity').text("无内容");
								  }else {
									  $('#itemComme_entity').text(data.data.comme);
								  }
								  //是否认同  回显赋值
								  if (data.data.isAgree!=null && data.data.isAgree!=undefined){
									  console.log("是否认同回显值:",data.data.isAgree)
                                      var test = 'isAgree_entity_'+data.data.isAgree;
                                      document.getElementById(test).checked = true;

									  // $('input[name=isAgree_entity][value="' + data.data.isAgree + '"]').prop('checked', true);
									  // document.querySelector('#error_submit').style.display = 'none';
									  //禁用只读,允许修改
									  // document.getElementById('caseInfo').readOnly = true;
									  // document.getElementById('isAgree_entity_0').disabled = true;
									  // document.getElementById('isAgree_entity_1').disabled = true;

								  }else {
									  document.getElementById('caseInfo').readOnly = false;
									  document.getElementById('isAgree_entity_0').disabled = false;
									  document.getElementById('isAgree_entity_1').disabled = false;
								  }
								  //是否认同的描述  回显赋值
								  if (data.data.caseInfo!=null && data.data.caseInfo!=undefined){
									  $('#caseInfo').val(data.data.caseInfo);
								  }
								  //主键ID
								  $('#entity_error_id').val(data.data.id);
								  //现在是A专家还是B砖家
								  $('#isAOrB_entity').val(data.data.isAOrB);
                                  //打开查看异常的弹窗
                                  $( "#error_dialog" ).dialog( "open" );

							  }else{
								  swal("信息", data.message, "error");
							  }
						  }
					  });
				  },
                  checkBoxClick:function(textId) {//优秀典型案例推荐复选框点击事件
                      if ($("#yxdxAnLiTuiJianReviews1").css("display") == 'none' ) {//如果show是隐藏的
                          $("#yxdxAnLiTuiJianReviews1").css("display", "block"); //  yxdxanlituijianreviews show的display属性设置为block（显示）
                          //$("#yxdxanlituijian").val("1");
                          xzcfVue.scoringIndexList.yxdxanlituijian = "1";

                          //禁止反面推荐
                          $("#ajpjYxdxanlituijian").attr("disabled","disabled");
                          $("#ajpjYxdxAnLiTuiJianReviews1").css("display", "none");
                          $('input:checkbox[name=jcaj]').attr('checked',false);
                          $('#ajpj').val("")

                          xzcfVue.scoringIndexList.ajpjYxdxanlituijianreviews="";
                          xzcfVue.scoringIndexList.ajpjYxdxanlituijian="0";
                          //禁止不推荐为典型案例
                          $("#noTuiJian").attr("disabled","disabled");
                          xzcfVue.scoringIndexList.noTuiJian="0";
                      } else {//如果show是显示的
                          xzcfVue.scoringIndexList.yxdxanlituijianreviews="";
                          xzcfVue.scoringIndexList.yxdxanlituijian="0";
                          $("#yxdxAnLiTuiJianReviews1").css("display", "none");//show的display属性设置为none（隐藏）

                          //放开反面推荐
                          $("#ajpjYxdxanlituijian").removeAttr("disabled");
                          //放开不推荐为典型案例
                          $("#noTuiJian").removeAttr("disabled");
                      }
                  },
                  ajpjCheckBoxClick:function(){//是否进行案卷评价复选框点击事件
                      if($("#ajpjYxdxAnLiTuiJianReviews1").css("display")=='none' ){//如果show是隐藏的
                          $("#ajpjYxdxAnLiTuiJianReviews1").css("display","block"); //  yxdxanlituijianreviews show的display属性设置为block（显示）
                          //$("#ajpjYxdxanlituijian").val("1");
                          xzcfVue.scoringIndexList.ajpjYxdxanlituijian="1";

                          //禁止优秀推荐
                          $("#yxdxanlituijian").attr("disabled","disabled");
                          $("#yxdxAnLiTuiJianReviews1").css("display", "none");
                          $('input:checkbox[name=yxaj]').attr('checked',false);
                          $('#zjpy').val("")

                          xzcfVue.scoringIndexList.yxdxanlituijianreviews="";
                          xzcfVue.scoringIndexList.yxdxanlituijian="0";
                          //禁止不推荐为典型案例
                          $("#noTuiJian").attr("disabled","disabled");
                          xzcfVue.scoringIndexList.noTuiJian="0";
                      }else{//如果show是显示的
                          xzcfVue.scoringIndexList.ajpjYxdxanlituijianreviews="";
                          xzcfVue.scoringIndexList.ajpjYxdxanlituijian="0";
                          $("#ajpjYxdxAnLiTuiJianReviews1").css("display","none");//show的display属性设置为none（隐藏）

                          //放开优秀推荐
                          $("#yxdxanlituijian").removeAttr("disabled");
                          //放开不推荐为典型案例
                          $("#noTuiJian").removeAttr("disabled");
                      }
                  },
                  noCheckBoxClick:function (){
                      var isChecked = $("#noTuiJian").prop('checked');
                      if(isChecked){
                          xzcfVue.scoringIndexList.noTuiJian="1";
                          //禁止优秀推荐
                          $("#yxdxanlituijian").attr("disabled","disabled");
                          $("#yxdxAnLiTuiJianReviews1").css("display", "none");
                          xzcfVue.scoringIndexList.yxdxanlituijianreviews="";
                          xzcfVue.scoringIndexList.yxdxanlituijian="0";
                          $('input:checkbox[name=yxaj]').attr('checked',false);
                          $('#zjpy').val("")
                          //禁止反面推荐
                          $("#ajpjYxdxanlituijian").attr("disabled","disabled");
                          $("#ajpjYxdxAnLiTuiJianReviews1").css("display", "none");
                          xzcfVue.scoringIndexList.ajpjYxdxanlituijianreviews="";
                          xzcfVue.scoringIndexList.ajpjYxdxanlituijian="0";
                          $('input:checkbox[name=jcaj]').attr('checked',false);
                          $('#ajpj').val("")
                      }else {

                          xzcfVue.scoringIndexList.noTuiJian="0";
                          //放开反面推荐
                          $("#ajpjYxdxanlituijian").removeAttr("disabled");
                          //放开优秀推荐
                          $("#yxdxanlituijian").removeAttr("disabled");
                      }

                  },
				  sumScore:function(){//判断是否有总分，没有则相加求分
					  	var entityScore = this.scoringIndexList.entityscore
				  		var indexObj = this.scoringIndexList.expertHandlIndexScoreList;

				  		if(entityScore==""|| entityScore==null){
				  			var indexStr = "";
							if(indexObj != null&&indexObj.length>0){
						  		for(var i=0;i<indexObj.length;i++){
						  			//整个保存完的逻辑
						  			if(indexObj[i].resultScore != null){
										if(!isNaN(parseFloat(indexObj[i].resultScore))){
											indexStr = indexStr+","+indexObj[i].resultScore;
										}
									}else{
						  				//弹框完成整个保存的逻辑
										var itemScore =indexObj[i].expertHandlItemScoreList;
										for(var j=0;j<itemScore.length;j++){
											indexStr = indexStr+","+itemScore[j].score;
										}
									}

						  		}
						  	}

							// console.log("indexStr",indexStr)
							if(indexStr.indexOf("1")!=-1){
								this.scoringIndexList.entityscore =0;
					  		}else if(indexStr.indexOf("1")==-1){
					  			this.scoringIndexList.entityscore =50;
					  		}else{
								this.scoringIndexList.entityscore =0;
							}
				  		}
					},
				updateItem:function (index,itemIndex,itemScore,id){//
				  	var value = xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].score;
				  	if(value != '' && value != null){

				  		$("#"+index+"expertItem"+itemIndex).removeClass("has-error");
				  		$("#"+index+"expertItem"+itemIndex).addClass("has-success");
						xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorFlag= true;
						xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorMessage="";


				  		if(value=="1"){
				  			// var comment = $("#"+index+"comment"+itemIndex).val().replace(/\s+/g,"");
				  			var comment = $("#"+index+"comment"+itemIndex).val();
							// var comment = $("#"+index+"comment"+itemIndex).val().val();
					  		//if(comment){
					  			$("#"+index+"comment"+itemIndex).removeClass("textarea-success");
						  		$("#"+index+"comment"+itemIndex).addClass("textarea-error");
								xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorFlag= false;
								xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorComment="评查结果若选择‘是’，具体情形必填";
					  		//}
				  		}else{
				  			$("#"+index+"comment"+itemIndex).removeClass("textarea-error");
				  			$("#"+index+"comment"+itemIndex).addClass("textarea-success");
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorFlag== true;
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorComment="";
				  		}

						var indexObj = xzcfVue.scoringIndexList.expertHandlIndexScoreList[index];
						var indexScore = xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].indexscore;
						var itemStr = "";
						if(indexObj != null){
				  			for(var i=0;i<indexObj.expertHandlItemScoreList.length;i++){
			  					if(!isNaN(parseFloat(indexObj.expertHandlItemScoreList[i].score))){
			  						itemStr = itemStr+","+indexObj.expertHandlItemScoreList[i].score;
			  					}
			  		 		}
				  		}
				  		//一级指标分
				  		if(itemStr.indexOf("1")!=-1){
				  			xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore= 1;
				  		}else{
				  			xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore= 0;
				  		}
// debugger;

				  		//实体总分技算
				  		var indexObj1 = xzcfVue.scoringIndexList.expertHandlIndexScoreList;
				  		var indexStr = "";
						if(indexObj1 != null){
					  		for(var i=0;i<indexObj1.length;i++){
					  			if(!isNaN(parseFloat(indexObj1[i].resultScore))){
					  				indexStr = indexStr+","+indexObj1[i].resultScore;
					  			}
					  		}
					  	}
						if(indexStr.indexOf("1")!=-1){
							xzcfVue.scoringIndexList.entityscore =0;
				  		}else{
				  			xzcfVue.scoringIndexList.entityscore =50;
				  		}
				  	}else{
				  		$("#"+index+"expertItem"+itemIndex).removeClass("has-success");
				  		$("#"+index+"expertItem"+itemIndex).addClass("has-error");
						xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorFlag= false;
						xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorMessage="请选择评查结果！";
				   		return false;
				  	}
				},
				updateCommon:function (index,itemIndex){
					var radio = xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].score;
					if(radio != '' && radio != null){
						if(radio=="1"){
							// var comment = $("#"+index+"comment"+itemIndex).val().replace(/\s+/g,"");
							var comment = $("#"+index+"comment"+itemIndex).val();
							if(comment){
								$("#"+index+"comment"+itemIndex).removeClass("textarea-success");
								$("#"+index+"comment"+itemIndex).addClass("textarea-error");
								xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorFlag= false;
								xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorComment="评查结果若选择‘是’，具体情形必填";
					  		}else{
					  			$("#"+index+"comment"+itemIndex).removeClass("textarea-error");
					  			$("#"+index+"comment"+itemIndex).addClass("textarea-success");
								xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorFlag= true;
								xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorComment="";
					  		}
					  	}else{
				  			$("#"+index+"comment"+itemIndex).removeClass("textarea-error");
				  			$("#"+index+"comment"+itemIndex).addClass("textarea-success");
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorFlag= true;
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorComment="";
				  		}
				  	}else{
				  		$("#"+index+"comment"+itemIndex).removeClass("textarea-error");
			  			$("#"+index+"comment"+itemIndex).addClass("textarea-success");
						xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorFlag= true;
						xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorComment="";
				  	}
				},
				  // saveTime:function(){
					//   if(this.scoringIndexList.scoredstate == 3 || this.scoringIndexList.scoredstate == 4){
					// 	  this.saveSubmit(this.scoringIndexList.id,'0');//暂存
					//   }else{
					// 	  this.saveSubmit(this.scoringIndexList.id,'1');//保存
					//   }
				  // },
				saveSubmit:function(id,status){//0代表缓存，1代表保存
					  //保存之前,先判断有没有没确认的异常,如果有没确认的异常,需要弹窗提示, 先确认完异常
					if(status=='1'){

                        var  isYXchecked=$("#yxdxanlituijian").prop('checked');
                        var  isJCchecked=$("#ajpjYxdxanlituijian").prop('checked');
                        var  isNOchecked=$("#noTuiJian").prop('checked');
                        if(isYXchecked == false && isJCchecked == false && isNOchecked == false ){
                            swal({title: "提示",text: "请选择案件状态！",type:"error"});
                            return false;
                        }

                        var checked_array=new Array();
                        if(xzcfVue.scoringIndexList.yxdxanlituijian=="1") {
                            var flag = false;
                            var flagtext = false;
                            $('input[name="yxaj"]:checked').each(function () {
                                checked_array.push($(this).val())
                                if (checked_array.indexOf("1000") != -1) {
                                    var yxFileStr = $("#zjpy").val().replace(/\s+/g, "");
                                    if (yxFileStr == null || yxFileStr == "") {

                                        flag = true;
                                    }
                                    if(yxFileStr.length>2000){
                                        flagtext=true;
                                    }
                                }
                            });
                            if (flag) {
                                swal({title: "提示", text: "优秀典型案例推荐不能为空", type: "error"});
                                return false;
                            }
                            if (flagtext) {
                                swal({title: "提示", text: "案卷评价长度不能超过2000个字符", type: "error"});
                                return false;
                            }
                        }
                        //优秀典型案例选中 下边的子项必填一个
                        if(xzcfVue.scoringIndexList.yxdxanlituijian=="1"){
                            var checked=$("input[name='yxaj']:checked");//获取复选框被选中状态
                            if(!(checked&&checked.length>0)){//判断复选框是否被选中
                                swal({title: "提示",text: "优秀典型案例子项不能为空",type:"error"});
                                return false;
                            }
                        }
                        //反面典型案例
                        /*var ajpjYxdxanlituijianreviews = xzcfVue.scoringIndexList.ajpjYxdxanlituijianreviews;
                        if(xzcfVue.scoringIndexList.ajpjYxdxanlituijian=="1"){
                            if ($("#ck_others_id").is(':checked') && ajpjYxdxanlituijianreviews==""){
                                swal({title: "提示",text: "较差案例推荐不能为空",type:"error"});
                                return false;
                            }

                            if (ajpjYxdxanlituijianreviews.length>2000){
                                swal({title: "提示",text: "案卷评价长度不能超过2000个字符",type:"info"});
                                return false;
                            }
                        }*/
                        //反面典型案例
                        var flags = false;
                        var flagss = false;
                        var ajpjYxdxanlituijianreviews = xzcfVue.scoringIndexList.ajpjYxdxanlituijianreviews;
                        if(xzcfVue.scoringIndexList.ajpjYxdxanlituijian=="1"){
                            var checked_array=new Array();
                            $('input[name="jcaj"]:checked').each(function(){
                                checked_array.push($(this).val())
                                if(checked_array.indexOf("1000") != -1){
                                    if(ajpjYxdxanlituijianreviews==null || ajpjYxdxanlituijianreviews==""){
                                        flags = true;
                                    }else{
                                        if(ajpjYxdxanlituijianreviews.length>2000){
                                            flagss = true;
                                        }
                                    }
                                }
                            });
                            if (flags){
                                swal({title: "提示",text: "较差案例推荐不能为空",type:"error"});
                                return false;
                            }
                            if (flagss){
                                swal({title: "提示",text: "案卷评价长度不能超过2000个字符",type:"info"});
                                return false;
                            }
                        }
                        //反面典型案例选中 下边的子项必填一个
                        if(xzcfVue.scoringIndexList.ajpjYxdxanlituijian=="1"){
                            var checked=$("input[name='jcaj']:checked");//获取复选框被选中状态
                            if(!(checked&&checked.length>0)){//判断复选框是否被选中
                                swal({title: "提示",text: "较差案例推荐子项不能为空",type:"error"});
                                return false;
                            }
                        }

                    }


						if(xzcfVue.scoringIndexList!=null){
							var falg= false;
							var index = xzcfVue.scoringIndexList.expertHandlIndexScoreList;
							for(var i=0;i<index.length;i++){
								if(index[i].expertHandlItemScoreList != null){
									var item = index[i].expertHandlItemScoreList;
									for(var j=0;j<item.length;j++){
										var value=item[j].score;
										if(value ==="" || value == null){
											xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].validatorFlag= false;
											xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].validatorMessage="请选择评查结果";
											$("#"+i+"expertItem"+""+j).removeClass("has-success");
											$("#"+i+"expertItem"+""+j).addClass("has-error");
											$("[name='"+i+"scoreRadio"+""+j+"']").focus();
											swal( "操作失败","请先开始评查!", "error");
											falg = true
											return false;
										}
										// else{
										// 	if(value=="1"){
										// 		// var comment = $("#"+i+"comment"+j).val().replace(/\s+/g,"");
										// 		var comment =$("#"+i+"comment"+j).val();
										// 		if(comment){
										//   			$("#"+i+"comment"+j).removeClass("textarea-success");
										// 	  		$("#"+i+"comment"+j).addClass("textarea-error");
										// 	  		$("#"+i+"comment"+j).focus();
										// 			xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].validatorFlag= false;
										// 			xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].validatorComment="评查结果若选择‘是’，具体情形必填";
										// 			return false;
										//   		}
										// 	}
										// }
									}
								}
							}
							if(falg){
								return false;
							}

						}

                    //存入优秀案卷code
                    var code_array=new Array();
                    $('input[name="yxaj"]:checked').each(function(){
                        code_array.push($(this).val())
                    });
                    if(code_array.length == 0){

                    }
                    xzcfVue.scoringIndexList.ajpjYxdxanlituijianCode = code_array.join(',')

                    //存入较差案卷code
                    var jcajArr=new Array();
                    $('input[name="jcaj"]:checked').each(function(){
                        jcajArr.push($(this).val())
                    });
                    xzcfVue.scoringIndexList.jcajtuijianCode = jcajArr.join(',')



					$.ajax({
						type:"post",
						url:WEBPATH+'/zjpf/saveEntityScore.do',
						data:{id:id,scoringIndex:JSON.stringify(xzcfVue.scoringIndexList),status:status},           //注意数据用{}
						success:function(data){//成功
							if(data.result=="success"){
								swal({title: "保存成功",text: "",type:"success"});
								if(status=='1'){
									//window.locat//ion.href=WEBPATH+"/zjpf/zjpfList.do";
									location.reload();
									//business.addMainContentParserHtml('zjpf/zjpfList.do','pageNum=${pageNum}');
									//history.go(-1);
									// macroMgr.onLevelTwoMenuClick(null, "zjpf/zjpfList.do");
							  	}else{
							  		business.addMainContentParserHtml('zjpf/entityAScore.do?id='+id,'pageNum=${pageNum}');
							  	}
						        return false;
							}else{
								if(data.code=="007"){
									swal("提示", "正在保存中……，请稍等片刻", "info");
								}else if(data.code=="000"){//登录信息失效
									swal({
								        title: "提示",
								        text: data.message,
								        type: "error",
								        confirmButtonText: "确定",
								        confirmButtonColor: "#ec6c62"
								    }, function() {
								        window.location.href=WEBPATH+"/index.do";
								    });
								}else if (data.code=="400"){
									swal({title: "提示",text: data.message,type:"error"});
								}else if (data.code=="500"){
									//实体评查,输入框不能为空,前端不会处理,直接在后端判断的---20230824
									swal({title:data.data,text: data.message,type:"error"});
								}else if(data.code=="5000"){
									swal({title:data.data,text: data.message,type:"error"});
								}
								return false;
						    }
						}
					});
				}
			}
		});
		function changeFileMaterials(){
			if ($("#fileMaterials").prop("checked")) {
				xzcfVue.scoringIndexList.entityscore =0;
				xzcfVue.scoringIndexList.fileMaterials = '1';
			}else {
				xzcfVue.sumScore();
				xzcfVue.scoringIndexList.fileMaterials = '0';
			}
		}

	   var a = xzcfVue.scoringIndexList.yxdxanlituijian;
    $(function () {
        if(xzcfVue.scoringIndexList.yxdxanlituijian=="1"){
            //$('#yxdxanlituijian').attr('checked', true)

            $("[name = yxdxanlituijian]:checkbox").attr("checked", true);
            $("#yxdxAnLiTuiJianReviews1").css("display","block");

            //优秀案卷选项回显
            var checkArray = $("input[name='yxaj']");
            var codes = xzcfVue.scoringIndexList.ajpjYxdxanlituijianCode;
            if(codes != null && codes != "" ){
                var myArray=codes.split(",");
                for (var i = 0; i < myArray.length; i++) {
                    //获取所有复选框对象的value属性，然后，用checkArray[i]和他们匹配，如果有，则说明他应被选中
                    $.each(checkArray, function (j, checkbox) {
                        //获取复选框的value属性
                        var checkValue=$(checkbox).val();
                        console.log(j+"----"+checkValue)
                        if (myArray[i] == checkValue) {
                            $(checkbox).attr("checked", true);
                        }
                    })
                }
            }


            //禁止较差推荐
            $("#ajpjYxdxanlituijian").attr("disabled","disabled");
            $("#ajpjYxdxAnLiTuiJianReviews1").css("display", "none");
            //禁止不推荐为典型案例
            $("#noTuiJian").attr("disabled","disabled");
        }


        var ajpja = xzcfVue.scoringIndexList.ajpjYxdxanlituijian;
        if(xzcfVue.scoringIndexList.ajpjYxdxanlituijian=="1"){
            //$('#ajpjYxdxanlituijian').attr('checked', true)

            $("[name = ajpjYxdxanlituijian]:checkbox").attr("checked", true);
            $("#ajpjYxdxAnLiTuiJianReviews1").css("display","block");

            //较差案卷选项回显
            var checkArray = $("input[name='jcaj']");
            var codes = xzcfVue.scoringIndexList.jcajtuijianCode;
            if(codes != null && codes != "" ){
                var myArray=codes.split(",");
                for (var i = 0; i < myArray.length; i++) {
                    //获取所有复选框对象的value属性，然后，用checkArray[i]和他们匹配，如果有，则说明他应被选中
                    $.each(checkArray, function (j, checkbox) {
                        //获取复选框的value属性
                        var checkValue=$(checkbox).val();
                        console.log(j+"----"+checkValue)
                        if (myArray[i] == checkValue) {
                            $(checkbox).attr("checked", true);
                        }
                    })
                }
            }


            //禁止优秀推荐
            $("#yxdxanlituijian").attr("disabled","disabled");
            $("#yxdxAnLiTuiJianReviews1").css("display", "none");
            //禁止不推荐为典型案例
            $("#noTuiJian").attr("disabled","disabled");
        }

        // 不推荐为典型案例 回显
        console.log("不推荐为典型案例 回显",xzcfVue.scoringIndexList)
        if(xzcfVue.scoringIndexList.noTuiJian=="1"){
            $("[name = noTuiJian]:checkbox").attr("checked", true);
            $("#noTuiJian").attr("checked", true);
            //禁止优秀推荐
            $("#yxdxanlituijian").attr("disabled","disabled");
            $("#yxdxAnLiTuiJianReviews1").css("display", "none");
            //禁止较差推荐
            $("#ajpjYxdxanlituijian").attr("disabled","disabled");
            $("#ajpjYxdxAnLiTuiJianReviews1").css("display", "none");
        }
    })



    //下载文件
		function xiaZaiAnJuan(){
		  var fileId = $("#fileId").val();
		  $.ajax({
			    type:"post",
			    url:WEBPATH+'/zjpf/xzcfExistFileUrl.do',
			    data:{fileId:fileId},           //注意数据用{}
			    success:function(data){  //成功
				 	if("yes" == data){
						window.location.href="${pageContext.request.contextPath}/zjpf/zjpfAnJuandown.do?fileId="+fileId;
					    return false;
			         }else if("no" == data){
			            	  swal( "操作失败","该案卷不存在!", "error");
			            	  return false;
					}else if("suffixerror" ==data){
						  swal( "操作失败","该案卷上传数据格式有问题!", "error");
		            	  return false;
					}
			    }
		    });
		}

//-----------------------------------------------------------------------------------------------------

		//开始评查
		var index = 0;
		var normId = 0;
		var itemId = 0;
        var	commeStr0="";
		var commeStr3 ="";
		var commeStr2 ="";
		var commeStr3 ="";
     	var commeStr4="";
     	var scoress=""
	    var newCommeStr = ""
		function startComment() {
			$('#my_dialog').dialog({closed:false})
			$( "#my_dialog" ).dialog( "open" );
			$("#textare").val("");
			$(".disNo").removeAttr("disabled");
			$("#topBnt").attr("disabled")

			$.ajax({
				type:"post",
				url:WEBPATH+'/zjpf/selectEntity.do',
				data:{id:expertFileId,index:index},
				success:function(data){  //成功
					if(data.result =='success'){


						var itemData =data.data;
						//console.log("-----------",itemData)
						 indexId =itemData.indexId;
						 itemId =itemData.itemid;
						 normId =itemData.normId;
						// console.log("1",normId)
						// var selectDev = $("input[name='mycheckbox']");
						// // selectedlist = itemData.commeStr.split(',');
						// 	//获取所有复选框对象的value属性，然后，用checkArray[i]和他们匹配，如果有，则说明他应被选中
						// 	$.each(selectDev,function(j,checkbox){
						// 		for(var i=0;i<arr.length;i++){
						//
						// 			//获取复选框的value属性
						// 		var checkValue=$(checkbox).val().split(',');
						// 		console.log(arr[i],$(checkbox).val(),'checkValue')
						// 		// checkValue.map(item =>{
						// 			if(arr[i]==$(checkbox).val()) {
						// 				$(checkbox).attr("checked",true);
						// 			}
						// 		// })
						// 		}
						// 	})
						commeStr0 = itemData.commeStr;
						// newCommeStr= itemData.commeStr;
						commeStr1 = itemData.commeStrOne;
						commeStr2 = itemData.commeStrTwo;
						commeStr3 = itemData.commeStrThree;
						// commeStr4 = itemData.commeStrFour;
                        commeStr5 = itemData.commeStrFive;
                        commeStr6 = itemData.commeStrSix;
                        // commeStr7 = itemData.commeStrSeven;
                        commeStr8 = itemData.commeStrEight;



						scoress =itemData.score;
						if(scoress == null){
							$('#topBnts').hide()
						}else {
							$('#topBnts').show()
						}
						// console.log("itemData.score",itemData.score == parseFloat("1"))
							$('#my_dialog').dialog({title :itemData.sort+"."+itemData.indexName})
					    	$("#span1").html(itemData.itemname)
						//console.log("11111",itemData.commeStr)
							if(itemData.score == parseFloat("1")){
								$("#score1").prop("checked", true);
								$('#selectdiv').hide()
								//console.log("commeStrThree",itemData.commeStrThree,"commeStrTwo",itemData.commeStrTwo,"commeStrThree",itemData.commeStrThree)
								$("#commeStrOne").val(itemData.commeStrOne == ""? "":itemData.commeStrOne);
								$("#commeStrTwo").val(itemData.commeStrTwo == ""? "":itemData.commeStrTwo);
								$("#commeStrThree").val(itemData.commeStrThree == ""? "":itemData.commeStrThree);
								// $("#commeStrFour").val(itemData.commeStrFour ==""? "":itemData.commeStrFour);

                                $("#commeStrFive").val(itemData.commeStrFive == ""? "":itemData.commeStrFive);
                                $("#commeStrSix").val(itemData.commeStrSix == ""? "":itemData.commeStrSix);
                                // $("#commeStrSeven").val(itemData.commeStrSeven== ""? "":itemData.commeStrSeven);
                                $("#commeStrEight").val(itemData.commeStrEight ==""? "":itemData.commeStrEight);
								// $("#score1").prop("checked", true);
								// $(".ismycheck").val(itemData.commeStrThree);
								$("#commeStrDiv").show();
								$("#textare").hide();
							}
						    if(itemData.score == parseFloat("0")){
								 $("#score0").prop("checked", true);
								$("#textare").show();
								$("#commeStrDiv").hide();
								if(itemData.commeStr!=""){
									$("#textare").val(itemData.commeStr);
								}else{
									$("#textare").val("");
								}
							}
							if(itemData.score == null){
								$("#commeStrDiv").hide();
								$("#textare").hide();
							}
							if(commeStr3 != "" && commeStr2 != "" && commeStr3 != "" ){
								$("#commeStrOne").val(commeStr1);
								$("#commeStrTwo").val(commeStr2);
								$("#commeStrThree").val(commeStr3);
								// $("#commeStrFour").val(commeStr4);
                                $("#commeStrFive").val(commeStr5);
                                $("#commeStrSix").val(commeStr6);
                                // $("#commeStrSeven").val(commeStr7);
                                $("#commeStrEight").val(commeStr8);


							}
					}
				}
			});

		};

		//下一项
		function confirm() {

			$(".disNo").removeAttr("disabled");
			var score=$("input[name='score']:checked").val();
			// $("input[name='mycheckbox']:checked").prop("checked", false);
		    var	commeStr=$("#textare").val();//否的情况
		    var	commeStrOne=$("#commeStrOne").val();//是的情况
		    var	commeStrTwo=$("#commeStrTwo").val();//是的情况
		    var	commeStrThree=$("#commeStrThree").val();//是的情况
            // var commeStrFour=$("#commeStrFour").val();//是的情况
            var	commeStrFive=$("#commeStrFive").val();//是的情况
            var	commeStrSix=$("#commeStrSix").val();//是的情况
            // var	commeStrSeven=$("#commeStrSeven").val();//是的情况
            var	commeStrEight=$("#commeStrEight").val();//是的情况



            if(score == null){
				swal( "提示信息","请先选是否！", "error");
				return false;
			}

			if(score =='1' &&( commeStrOne == ""|| commeStrTwo == "" || commeStrThree == "" || commeStrFive==""|| commeStrSix==""  ||commeStrEight =="")){

                if(commeStrOne == ""){
                    document.getElementById('commeStrOne').classList.add('error');
                }else {
                    document.getElementById('commeStrOne').classList.remove('error');
                }
                if(commeStrTwo == ""){
                    document.getElementById('commeStrTwo').classList.add('error');
                }else {
                    document.getElementById('commeStrTwo').classList.remove('error');
                }
                if(commeStrThree == ""){
                    document.getElementById('commeStrThree').classList.add('error');
                }else {
                    document.getElementById('commeStrThree').classList.remove('error');
                }
                if(commeStrFive == ""){
                    document.getElementById('commeStrFive').classList.add('error');
                }else {
                    document.getElementById('commeStrFive').classList.remove('error');
                }
                if(commeStrSix == ""){
                    document.getElementById('commeStrSix').classList.add('error');
                }else {
                    document.getElementById('commeStrSix').classList.remove('error');
                }
                // if(commeStrSeven == ""){
                //     document.getElementById('commeStrSeven').classList.add('error');
                // }else {
                //     document.getElementById('commeStrSeven').classList.remove('error');
                // }
                if(commeStrEight == ""){
                    document.getElementById('commeStrEight').classList.add('error');
                }else {
                    document.getElementById('commeStrEight').classList.remove('error');
                }
                swal( "提示信息","相关输入框信息必填！", "error");

			}else{
				if(score == 1){
					//是 拼接
					commeStr = "该案卷在" + commeStrOne + "处存在" + commeStrTwo + "问题，详见"+commeStrThree +"文书，"+commeStrFive+"页，"+commeStrSix+"错误,因此认定该案卷错误。详情说明:"+ commeStrEight;

				}
           //保存大项小项
			$.ajax({
				type:"post",
				url:WEBPATH+'/zjpf/svaeIndexEntity.do',
				data:{ id:expertFileId,score:score,commeStr:commeStr,normId:normId,itemId:itemId,
                    commeStrOne:commeStrOne,commeStrTwo:commeStrTwo,commeStrThree:commeStrThree,commeStrFour:'',
                    commeStrFive:commeStrFive,commeStrSix:commeStrSix,commeStrSeven:'',commeStrEight:commeStrEight
				     },
				success:function(data){  //成功
					//business.addMainContentParserHtml(WEBPATH+'/zjpf/entityAScore.do?id='+expertFileId+'&pageNum=1');
					//window.location.Reload()


					//window.history.back();

					if(data.result =='success'){
						$.ajax({
							type:"post",
							url:WEBPATH+'/zjpf/selectEntity.do',
							data:{id:expertFileId,index:index+1},
							success:function(data){  //成功
								if(data.result =='success'){
									$("#topBnt").removeAttr("disabled")
									clearInput();
									$("#textare").val("");
									var itemData =data.data;
									indexId =itemData.indexId;
									itemId =itemData.itemid;
									normId =itemData.normId;
									index = index+1;
									commeStr0 = itemData.commeStr;
									commeStr1 = itemData.commeStrOne;
									commeStr2 = itemData.commeStrTwo;
									commeStr3 = itemData.commeStrThree;
									// commeStr4 = itemData.commeStrFour;

                                    commeStr5 = itemData.commeStrFive;
                                    commeStr6 = itemData.commeStrSix;
                                    // commeStr7 = itemData.commeStrSeven;
                                    commeStr8 = itemData.commeStrEight;
									scoress =itemData.score;
									if(scoress == null){
										$('#topBnts').hide()
									}else {
										$('#topBnts').show()
									}
									$('#my_dialog').dialog({title :itemData.sort+"."+itemData.indexName})
									$("#span1").html(itemData.itemname)
									// console.log("123123123",itemData.commeStr)
									if(itemData.score == parseFloat("1")){
										$("#score1").prop("checked", true);

										$("#textare").hide();
										$("#commeStrOne").val(itemData.commeStrOne == ""? "":itemData.commeStrOne);
										$("#commeStrTwo").val(itemData.commeStrTwo == ""? "":itemData.commeStrTwo);
										$("#commeStrThree").val(itemData.commeStrThree == ""? "":itemData.commeStrThree);
										// $("#commeStrFour").val(itemData.commeStrFour ==""? "":itemData.commeStrFour);

                                        $("#commeStrFive").val(itemData.commeStrFive == ""? "":itemData.commeStrFive);
                                        $("#commeStrSix").val(itemData.commeStrSix == ""? "":itemData.commeStrSix);
                                        // $("#commeStrSeven").val(itemData.commeStrSeven== ""? "":itemData.commeStrSeven);
                                        $("#commeStrEight").val(itemData.commeStrEight ==""? "":itemData.commeStrEight);

										$("#commeStrDiv").show();
									}
									if(itemData.score == parseFloat("0")){

										$("#score0").prop("checked", true);
										$("#textare").show();
										$("#commeStrDiv").hide();
										if(itemData.commeStr!=""){
											$("#textare").val(itemData.commeStr);
										}else{
											$("#textare").val("");
										}
									}

									if(itemData.score == null){
										$("#commeStrDiv").hide();
										$("#textare").hide();
									}
									if(commeStr3 != "" && commeStr2 != "" && commeStr3 != "" ){
										$("#commeStrOne").val(commeStr1);
										$("#commeStrTwo").val(commeStr2);
										$("#commeStrThree").val(commeStr3);
										// $("#commeStrFour").val(commeStr4);
                                        $("#commeStrFive").val(commeStr5);
                                        $("#commeStrSix").val(commeStr6);
                                        // $("#commeStrSeven").val(commeStr7);
                                        $("#commeStrEight").val(commeStr8);
									}
								}
								if(data.result == "null"){
									//关闭弹框
									$('#my_dialog').dialog("close");
									//刷新页面
									business.addMainContentParserHtml(WEBPATH+'/zjpf/entityAScore.do?id='+expertFileId+'&pageNum=1');
								}
							}
						});

					}
				}
			});
				$("input[name='score']:checked").prop("checked", false);


		 }

		};

	function confirms() {
		$(".disNo").removeAttr("disabled");
		var score = $("input[name='score']:checked").val();
		// $("input[name='mycheckbox']:checked").prop("checked", false);
		var commeStr = $("#textare").val();//否的情况
		var commeStrOne = $("#commeStrOne").val();//是的情况
		var commeStrTwo = $("#commeStrTwo").val();//是的情况
		var commeStrThree = $("#commeStrThree").val();//是的情况
		// var commeStrFour = $("#commeStrFour").val();//是的情况
        var	commeStrFive=$("#commeStrFive").val();//是的情况
        var	commeStrSix=$("#commeStrSix").val();//是的情况
        // var	commeStrSeven=$("#commeStrSeven").val();//是的情况
        var	commeStrEight=$("#commeStrEight").val();//是的情况

		if (score == null) {
			swal("提示信息", "请先选是否！", "error");
			return false;
		}
        if(score =='1' &&(commeStrOne == "" || commeStrTwo == "" || commeStrThree == "" || commeStrFive==""|| commeStrSix==""  ||commeStrEight =="")){
            if(commeStrOne == ""){
                document.getElementById('commeStrOne').classList.add('error');
            }else {
                document.getElementById('commeStrOne').classList.remove('error');
            }
            if(commeStrTwo == ""){
                document.getElementById('commeStrTwo').classList.add('error');
            }else {
                document.getElementById('commeStrTwo').classList.remove('error');
            }
            if(commeStrThree == ""){
                document.getElementById('commeStrThree').classList.add('error');
            }else {
                document.getElementById('commeStrThree').classList.remove('error');
            }
            if(commeStrFive == ""){
                document.getElementById('commeStrFive').classList.add('error');
            }else {
                document.getElementById('commeStrFive').classList.remove('error');
            }
            if(commeStrSix == ""){
                document.getElementById('commeStrSix').classList.add('error');
            }else {
                document.getElementById('commeStrSix').classList.remove('error');
            }
            // if(commeStrSeven == ""){
            //     document.getElementById('commeStrSeven').classList.add('error');
            // }else {
            //     document.getElementById('commeStrSeven').classList.remove('error');
            // }
            if(commeStrEight == ""){
                document.getElementById('commeStrEight').classList.add('error');
            }else {
                document.getElementById('commeStrEight').classList.remove('error');
            }
            swal( "提示信息","相关输入框信息必填！", "error");
        } else {
			if (score == 1) {
                commeStr = "该案卷在" + commeStrOne + "处存在" + commeStrTwo + "问题，详见"+commeStrThree +"文书，"+commeStrFive+"页，"+commeStrSix+"错误,因此认定该案卷错误。详情说明:"+ commeStrEight;
            }
		}
		//保存大项小项
		$.ajax({
			type: "post",
			url: WEBPATH + '/zjpf/svaeIndexEntity.do',
			data: {
				id: expertFileId,
				score: score,
				commeStr: commeStr,
				normId: normId,
				itemId: itemId,
				commeStrOne: commeStrOne,
				commeStrTwo: commeStrTwo,
				commeStrThree: commeStrThree,
				// commeStrFour: commeStrFour,
                commeStrFive: commeStrFive,
                commeStrSix: commeStrSix,
                // commeStrSeven: commeStrSeven,
                commeStrEight: commeStrEight,
			},
			success: function (data) {  //成功
				if (data.result == 'success') {
					//关闭弹框
					$('#my_dialog').dialog("close");
					//刷新页面
					business.addMainContentParserHtml(WEBPATH + '/zjpf/entityAScore.do?id=' + expertFileId + '&pageNum=1');
				}
			}
		});
	}
		//上一项
		function Cancel() {

            // if(selectednamelist){
			// 	$("input[name='mycheckbox']:checked").prop("checked", true);
			// 	$("#commeStrThree").val(selectednamelist);
			// }else{
			// 	$("input[name='mycheckbox']:checked").prop("checked", false);
			// 	$("#commeStrThree").val('');
			// }
			$(".disNo").removeAttr("disabled");
			$.ajax({
				type:"post",
				url:WEBPATH+'/zjpf/selectEntity.do',
				data:{id:expertFileId,index:index-1},
				success:function(data){
					//成功
					if(data.result =='success'){
						var itemData =data.data;
						index = index -1;
						indexId =itemData.indexId;
						itemId =itemData.itemid;
						normId =itemData.normId;
						commeStr0 = itemData.commeStr;
						commeStr1 = itemData.commeStrOne;
						commeStr2 = itemData.commeStrTwo;
						commeStr3 = itemData.commeStrThree;
						// commeStr4 = itemData.commeStrFour;
                        commeStr5 = itemData.commeStrFive;
                        commeStr6 = itemData.commeStrSix;
                        // commeStr7 = itemData.commeStrSeven;
                        commeStr8 = itemData.commeStrEight;

						scoress =itemData.score;
						//console.log("indexId",indexId,"itemId",itemId,"normId",normId)
						$('#my_dialog').dialog({title :itemData.sort+"."+itemData.indexName})
						$("#span1").html(itemData.itemname)

						if(itemData.score == parseFloat("1")){
							 $("#score1").prop("checked", true);
							$("#textare").hide();
							$("#commeStrDiv").show();

							$("#commeStrOne").val(itemData.commeStrOne == ""? "":itemData.commeStrOne);
							$("#commeStrTwo").val(itemData.commeStrTwo == ""? "":itemData.commeStrTwo);
							$("#commeStrThree").val(itemData.commeStrThree == ""? "":itemData.commeStrThree);
							// $("#commeStrFour").val(itemData.commeStrFour ==""? "":itemData.commeStrFour);

                            $("#commeStrFive").val(itemData.commeStrFive == ""? "":itemData.commeStrFive);
                            $("#commeStrSix").val(itemData.commeStrSix == ""? "":itemData.commeStrSix);
                            // $("#commeStrSeven").val(itemData.commeStrSeven== ""? "":itemData.commeStrSeven);
                            $("#commeStrEight").val(itemData.commeStrEight ==""? "":itemData.commeStrEight);


                            // $("#ismycheck").val(itemData.commeStrThree == ""? "":itemData.commeStrThree);
							// $("input[name='mycheckbox']:checked").prop("checked", true);
						}
						if(itemData.score == parseFloat("0")){
							$("#textare").show();
							$("#commeStrDiv").hide();
							 $("#score0").prop("checked", true);
							 // $("input[name='mycheckbox']:checked").prop("checked", true);


							if(itemData.commeStr!="" && itemData.commeStr!=null){
								$("#textare").val(itemData.commeStr);
							}else{
								//清空否的文本框
								$("#textare").val("");
							}
						}
						if(itemData.score == null){
							$("#commeStrDiv").hide();
							$("#textare").hide();
						}

						if(itemData.score != parseFloat("0") && itemData.score != parseFloat("1")){
							if(commeStr1 != "" && commeStr2 != "" && commeStr3 != "" ){
								$("#commeStrOne").val(commeStr1);
								$("#commeStrTwo").val(commeStr2);
								$("#commeStrThree").val(commeStr3);
								// $("#commeStrFour").val(commeStr4);
                                $("#commeStrFive").val(commeStr5);
                                $("#commeStrSix").val(commeStr6);
                                // $("#commeStrSeven").val(commeStr7);
                                $("#commeStrEight").val(commeStr8);

							}
							//没有值 就清空
							$("input[name='score']:checked").prop("checked", false);
							$("input[name='mycheckbox']:checked").prop("checked", false);
						}

					}
				}
			});
		};

		function updataRadio(type){
			console.log("传参是否值:----"+type)
			if(type=='1'){
				if((commeStr3 !== null && commeStr2 !== null && commeStr3 !== null )||(commeStr3 !== "" && commeStr2 !== "" && commeStr3 !== "")){
					$("#commeStrOne").val(commeStr1);
					$("#commeStrTwo").val(commeStr2);
					$("#commeStrThree").val(commeStr3);
					// $("#commeStrFour").val(commeStr4);
                    $("#commeStrFive").val(commeStr5);
                    $("#commeStrSix").val(commeStr6);
                    // $("#commeStrSeven").val(commeStr7);
                    $("#commeStrEight").val(commeStr8);
				}
				//切换radio 清空内容
                if(scoress == ""){
					clearInput()
				}
				$("#commeStrDiv").show();
				$("#textare").hide();
			}else{
				//切换radio 清空内容
				if(commeStr0 == null || commeStr0 ==""){
					$("#textare").val("");
				}
				$("#commeStrDiv").hide();
				$("#textare").attr("style","display:block;width:750px;height:270px;resize:none;");
				confirm();
			}
		}

		//点击下一项时，清空输入框
		function clearInput() {
			$("#commeStrOne").val("");
			$("#commeStrTwo").val("");
			$("#commeStrThree").val("");
			// $("#commeStrFour").val("");
            $("#commeStrFive").val("");
            $("#commeStrSix").val("");
            // $("#commeStrSeven").val("");
            $("#commeStrEight").val("");
			$(".ismycheck").val("");
            $("input[name='mycheckbox']:checked").prop("checked", false);

			// var selectedlist = [];
			//选中的股票名称（展示在前台给业务员看的）
			selectednamelist = [];
		}








		//设置不可点击 后显示蓝色不置灰
		$(".radoos").css("pointer-events","none");
	    //设置单选按钮不可点击
		 //$(".radoos").attr("disabled","disabled");


	$('#caseInfo').on('input', function() {
		var text = $(this).val();
		var length = text.length;
		if (length > 499) {
			$(this).val(text.substring(0, 500));
			$('#error-message-500').text('(限制500字)');
		} else {
			$('#error-message-500').text('');

		}
	});
	function goBackHome(){
		console.log(1)
		location.reload();
		console.log(2)
		macroMgr.onLevelOneMenuClick(null, 'zjpf.do')
		console.log(3)
	}


</script>
<body>
<%--第一轮专家评分实体页面--%>
<div id="ExpertVue">
	<%--查看异常信息--%>
	<div id="error_dialog" style="height: 450px; display: flex; flex-direction: column; justify-content: space-between;">
		<input type="hidden" id="entity_error_id">
		<input type="hidden" id="isAOrB_entity">
		<span class="glyphicon glyphicon-info-sign" aria-hidden="true" style="text-align: center;font-size: 50px;color: #337ab7;"></span>
		<div style="width: 85%;padding-left: 20%;; font-size: 20px">
			<span style="font-weight: bold; font-size: 20px">该项对方专家</span>
			<span style="font-size: 20px;color: #337ab7" id="itemOption_entity">xxx</span>
			<span style="font-weight: bold; font-size: 20px">，评审依据为：</span>
			<span style="font-size: 20px;color: #337ab7" id="itemComme_entity">xxx</span>

		</div>

        <div style="padding-left: 20%; font-size: 20px">
			<label style="font-weight: bold; font-size: 20px">是否认同:</label>
			<input class="form-check-input" type="radio"  name="isAgree_entity" id="isAgree_entity_0" value="0" ><span style="margin-right: 48px;color:#333333;font-size:18px;">不认同</span>
			<input class="form-check-input" type="radio"  name="isAgree_entity" id="isAgree_entity_1" value="1"><span style="color:#333333;font-size:18px;"> 认同</span>
		</div>
		<div style="padding-left: 20%;">
			<textarea style="width: 80%;" id="caseInfo" placeholder="请输入具体情形"></textarea>
			<span id="error-message-500" style="color: red;"></span>

		</div>

		<div style="text-align: center; padding: 20px;">
			<button id="entity_error_close" class="my-btn-gray"  OnClick="entity_error_close()" style="background: #ECF5FF; margin-right: 50px;margin-top: 10px; outline: none; border: 1px solid #C6E2FF;border-radius: 3px;font-size: 18px;color:#409EFF; width: 80px; height: 40px">取消</button>
			<button id="error_submit" class="my-btn-blue" OnClick="error_submit()" style="background: #009CCD; margin-top: 10px;outline: none; border: none; width: 80px; height: 40px;border: 1px solid #C6E2FF;border-radius: 3px;font-size: 18px;color:#ffffff;">确认</button>
		</div>
	</div>
<%------------------------实体弹框开始------------------------------------------------%>
	<div id="my_dialog">

		<p style="text-align: left;margin-left: 25px;margin-right:30px">
			<span id="span1"></span>
		</p>

		<p style="text-align: left;margin-left: 35px; margin-top: 28px">
			<input disabled type="radio" id="score1" class="disNo" value="1"  name="score"  onclick="updataRadio(1)" /><span style="margin-right: 48px;color:#333333;font-size:18px;"> 存在问题</span>
			<input disabled type="radio" id="score0" class="disNo" value="0"  name="score" onclick="updataRadio(0)" /><span style="color:#333333;font-size:18px;"> 不存在问题</span>
		</p>

		<p style="text-align:center;">
			<%--<textarea  id="textare" name="commeStr" style="resize:none; width: 700px; height: 150px" placeholder="请输入具体情形，长度不能超过2000字符" ></textarea>--%>
			<div style="height: 270px;width: 750px;border:1px solid #0006;margin-left: 25px;margin-top:10px" class="form-group">
					<textarea maxlength="2000" class="disNo"  id="textare" name="commeStr" style=" width: 750px; height: 270px;display: none;" placeholder="请输入具体情形，长度不能超过2000字符" ></textarea>
					<div id="commeStrDiv" style="height: 270px;line-height:37px;width: 750px;margin-left: 15px;display: none">

<%--							 <input autocomplete="off" maxlength="50" type="text" id="commeStrThree" class="from_gaozhi disNo"  style="width:150px;outline:none;margin-top: 40px;margin-left: 10px" placeholder="（具体的文书,仅限200字符之内）">在--%>
<%--							 <select  id="commeStrThree" class="selectpicker show-tick form-control"  multiple data-live-search="false" style="border: 1px solid #DCDFE6;border-radius: 3px;width:150px;outline:none;margin-top: 17px;height:28px;margin-right:10px" placeholder="（具体的文书,仅限200字符之内）">--%>
<%--								 <option></option>--%>
<%--								 from_gaozhi disNo--%>
<%--							 </select>--%>
							<span style="font-size: 16px;color: #ffffff; display: inline-block;  visibility: hidden;letter-spacing: 1.5px;">空格</span>
							<span style="font-size: 16px;color:#333333;letter-spacing: 1.5px;">该案卷在</span>
							 <input autocomplete="off" maxlength="200" type="text" id="commeStrOne" class="from_gaozhi disNo"  style="width:240px;outline:none;margin-right: 0px;font-size: 16px;font-weight: 400;color: #333333;" placeholder="仅限200字符之内"><span style="font-size: 16px;font-weight: 400;color: #333333;letter-spacing: 1.5px;">处存在</span>
							 <input autocomplete="off" maxlength="200" type="text" id="commeStrTwo" class="from_gaozhi disNo"  style="width:240px;outline:none;margin-right: 0px;font-size: 16px;font-weight: 400;color: #333333;" placeholder="仅限200字符之内"><span style="font-size: 16px;color: #333333;letter-spacing: 1.5px;">问题,详见</span>
						     <input type="text" maxlength="200" id="commeStrThree" class='from_gaozhi disNo' onclick="myclick();" readonly="true" style="width:200px;height:30px;">

							<div id="fuzzysearchdiv" style="display:none;width:200px;z-index:3;position:absolute;height:20px;"
								 onMouseOver="mousein()" onMouseOut="mouseout()">
							</div>
							<div id="selectdiv" style="display:none;top:230px;border:1px solid #A9A9A9;width:200px;z-index:2;position:absolute;overflow-y :scroll;margin-left:50px;height:270px;background-color:white;"
								 onMouseOver="mousein()" onMouseOut="mouseout()">
							</div>

						    <span style="font-size: 16px;color: #333333;letter-spacing: 1.5px;">文书,</span>
					    	 <input autocomplete="off" maxlength="200" type="text" id="commeStrFive" class="from_gaozhi disNo"  style="width:100px;outline:none;margin-right: 0px;font-size: 16px;font-weight: 400;color: #333333;" placeholder="分别第XX页"><span style="font-size: 16px;color: #333333;letter-spacing: 1.5px;">页,</span>
						     <input autocomplete="off" maxlength="200" type="text" id="commeStrSix" class="from_gaozhi disNo"  style="width:280px;outline:none;margin-right: 0px;font-size: 16px;font-weight: 400;color: #333333;" placeholder="仅限200字符之内"><span style="font-size: 16px;color: #333333;letter-spacing: 1.5px;">错误,</span>
						<br/>
						<span style="font-size: 16px;color: #333333;letter-spacing: 1.5px;">因此认定该案卷错误。</span>	<br/>
						<span style="font-size: 16px;color:#333333;letter-spacing: 1.5px;">详情说明：</span>
						<textarea autocomplete="off" maxlength="2000" class="disNo"  id="commeStrEight" placeholder="需对存在的问题进行详细说明，仅限2000字" style=" width: 720px; height: 80px;outline: none;font-weight: 400;font-size:16px;color: #333333;"></textarea>
				   </div>
			</div>
		</p>

		<div style="text-align: center">
			<button id="topBnt" class="my-btn-gray"  OnClick="Cancel()" style="background: #ECF5FF; margin-right: 50px;margin-top: 10px; outline: none; border: 1px solid #C6E2FF;border-radius: 3px;font-size: 18px;color:#409EFF; width: 100px; height: 40px">上一项</button>
			<button class="my-btn-blue" OnClick="confirm()" style="background: #009CCD; margin-top: 10px;outline: none; border: none; width: 100px; height: 40px;border: 1px solid #C6E2FF;border-radius: 3px;font-size: 18px;color:#ffffff;">下一项</button>
<%--			<button id="topBnts" class="my-btn-gray"  OnClick="confirms()" style="background: #ECF5FF; margin-right: 50px;margin-top: 10px; outline: none; border: 1px solid #C6E2FF;border-radius: 3px;font-size: 18px;color:#409EFF; width: 80px; height: 40px">修改</button>--%>
		</div>
<%--		<div style="text-align: center">--%>
<%--			--%>
<%--&lt;%&ndash;			//<button class="my-btn-blue" OnClick="confirm()" style="background: #009CCD; margin-top: 10px;outline: none; border: none; width: 80px; height: 40px;border: 1px solid #C6E2FF;border-radius: 3px;font-size: 18px;color:#ffffff;">下一项</button>&ndash;%&gt;--%>
<%--		</div>--%>
		<div style="text-align: center;color: red;" >
			注意:您需点击至最后一项才可评查或修改成功! 中途不可强行关闭，否则保存失败！
		</div>
		</div>
<%--	------------------------实体弹框结束-----------------------------------------------%>
		<div class="center_weizhi" style="top: 110px">当前位置：专家评分 - 专家评审案卷 - ${filetypeName}</div>
		<div class="center" style="top: 135px">
			<div class="center_list">
				<input id="pageNum"  type="hidden" value="${pageNum}" >
		 		<input id="expertFileId"  type="hidden" value="${expertFileId}" >
		 		<input id="fileId"  type="hidden" value="${expertHandlFileList.fileid}" >
				<input id="expertId"  type="hidden" value="${expertHandlFileList.expertId}" >
				<input id="handlType"  type="hidden" value="${expertHandlFileList.handlType}" >
				<input id="problemRemark" type="hidden" value="${expertHandlFileList.problemRemark}">



<%--				<button  id="aistartEntity" v-on:click="startEntitytion()" class="btn btn-primary"  style="font-size:14px;margin-top:10px;width:98px;height:40px;  padding:6px 6px;margin-right: 10px;float: right;">开始智能评查</button>--%>

				<div id="jdmodal" class="jdmodal" style="display: none;">
					<div class="jdmodal-content">

						<span class="close" id="closeButton" v-on:click="closejdModal()">&times;</span>
						<h2 id="modalTitle">识别进度</h2>
						<div class="progress">
							<div id="progressBar" class="progress-bar"></div>
						</div>
						<div id="jdstatus">进度：0%</div>

						<div id="completionMessage" style="display: none;"> <!-- 完成消息区域 -->
							<i class="fas fa-check-circle" style="font-size: 50px; color: green;"></i> <!-- 完成图标 -->
							<h3>已完成识别</h3>
							<p>请注意智能识别内容，仅供参考，具体需以实际核对结果为准。</p>
						</div>



						<button id="confirmButton" style="display: none;" v-on:click="confirmEntityRecognition()"  onclick="confirmEntityRecognition()">确认</button>
					</div>
				</div>



				<div >
<%--					<button class="btn btn-default" style='padding:9px 34px;margin-right:14px' onclick="macroMgr.onLevelTwoMenuClick(null, 'zjpf/zjpfScore.do?id=${expertFileId}&pageNum=${pageNum}' )">卷面评查</button>--%>
	<c:if test ="${status == '1' }">
		<button class="btn btn-default" style='padding:9px 34px;margin-right:14px' onclick="macroMgr.onLevelTwoMenuClick(null, 'zjpf/zjpfScoreView.do?id=${expertFileId}&status=1&pageNum=${pageNum}' )">卷面评查</button>
	</c:if>
	<c:if test ="${status == null ||status != '1' }">
		<button class="btn btn-default" style='padding:9px 34px;margin-right:14px' onclick="macroMgr.onLevelTwoMenuClick(null, 'zjpf/zjpfScore.do?id=${expertFileId}&status=0&pageNum=${pageNum}' )">卷面评查</button>
	</c:if>
					<button class="btn btn-danger" style='padding:9px 34px'>实体和程序评查</button>


	<%--管理员 --%>
	<c:if test ="${userTypeCode == '4' }">
		<c:if test="${gopath != null and gopath !='' }">
			<button class="btn btn-primary"  onclick="macroMgr.onLevelOneMenuClick(null,'zlpf.do?pageIndex=${pageIndex}&areaCodeLeave=${areaCodeLeave}&fileCode=${fileCode}&isInCheck=${isInCheck}&pageNum=${pageNum}&gopath=${gopath}')"  style="font-size:16px;width:98px;height: 40px; padding: 6px 34px; margin-right: 10px;float: right;" >返回</button>
		</c:if>

	</c:if>

					<div style="font-weight: 400; font-size: 14px">
					<p style="margin-top:21px;font-weight: 600; font-size: 16px">评审说明：</p>
					<%--<p>1.出现下面任一项情形的属于错案，全卷0分。</p>--%>
					<p>其中任一项表现情形相吻合的，请在具体选项处进行选择“存在问题”或者“不存在问题”；如果选择“存在问题”请在审核栏说明具体情形。</p>
					<div class="dingwei" style="color:#333333;font-size:14px; font-weight:400;">案卷或材料：<span style="color:#009CCD;font-size:14px; font-weight:400;" id ="filecode">
<%--						{{scoringIndexList.filetype }}--%>
						{{scoringIndexList.filecode }}
						<span style="color:red;font-size:16px; font-weight:bold;"><c:choose><c:when test="${expertHandlFileList.closed==1}">（已结案）</c:when><c:when test="${expertHandlFileList.closed==0}">（未结案）</c:when> </c:choose></span>
						</span>
					<c:if test ="${sessionScope.sa_session.sysStatus == '5' }">
						<a v-if="scoringIndexList.fileurl != null && scoringIndexList.fileurl != ''" v-bind:href="scoringIndexList.downUrl" target="_Blank">
							<button id="download" class="btn btn-success btn-xs" style="padding:5px 12px;margin-left:10px" data-toggle="modal" data-target="#myModal">下载</button>
						</a>
						<span v-else style="color:red;font-size:16px; font-weight:bold;">（无案卷文件）</span>
						<c:if test="${expertHandlFileList.suffix== 'pdf'}">
							<button id="look" class="btn btn-info btn-xs" data-toggle="modal" style="padding:5px 12px;margin-left:10px" data-remote="${webpath}/jcpf/showFileModal.do?fastDFSUrl=+'${fastDFS}'+" data-target="#view">预览</button>
						</c:if>
					</c:if>
					</div>
					<div style="float: right">
						<c:if test ="${status == null ||status != '1' }">
							<div style="float: right">
								<button class="btn btn-info "  onclick="startComment()" style="width: 120px;height: 40px;padding:6px 34px">开始评查</button>
							</div>
						</c:if>
					</div>
					</div>
				</div>


		       	<form action="#" id ="zjpfForm" method="post">
		        	<table class="table table-bordered table-hover table-condensed">
						<thead>
							<tr>
								<td width="3%" height="30" bgcolor="#efefef">序号</td>
								<td width="7%" bgcolor="#efefef">评查项目</td>
								<td bgcolor="#efefef">
								<table width="100%" border="0" cellspacing="0" cellpadding="0" style="padding:0;">
									<tr>
										<td style="border-right:solid 1px #ddd;text-align: center">重大问题案卷表现形式</td>
										<td style="border-right:solid 1px #ddd;text-align: center; width:20%; padding:0px 5px 0px 5px;">智能评查识别内容</td>
										<td style="border-right:solid 1px #ddd; text-align: center;width:10%; padding:0px 5px 0px 5px;">评查</td>
										<td style="width:30%;text-align: center;padding:0px 5px 0px 5px;">具体情形</td>
									</tr>
								</table>
								</td>
								<!-- <td width="15%" bgcolor="#efefef">具体情形</td> -->
							</tr>
						</thead>
						<tbody  class="form-group">
						<tr v-for="(scoringIndex, index) in scoringIndexList.expertHandlIndexScoreList" v-if="scoringIndex.isError!=2" >
							<td height="30" align="center" >{{index+1}}</td>

							<!-- 评查项目 -->
							<td align="center" >{{scoringIndex.indexname}}
							</td>

							<!-- 标准分 -->
							<!-- <td style="vertical-align:middle;">{{scoringIndex.indexscore}}</td> -->

							<!-- 评分细则 -->
							<td disabled v-if="scoringIndex.expertHandlItemScoreList != null && scoringIndex.expertHandlItemScoreList.length !=0 "  style="padding:0;">
								<table width="100%" border="0" cellspacing="0" cellpadding="0" style="padding:0;">
									<tr  v-for="(scoringItem, index1) in scoringIndex.expertHandlItemScoreList">
										<td style="background-color:#ECD9E0;border-bottom:solid 1px #ddd; border-right:solid 1px #ddd;vertical-align:middle;"  v-if="scoringItem.isError==1" >
											<div style="vertical-align:middle; margin:0 5px;" >{{scoringItem.itemname}}
												<p v-if="scoringItem.isError==1 && scoringItem.isAgree==null" style="text-align:center;width: 67px;line-height:24px;height: 24px;background: #DF1912;border-radius: 3px;" >
													<a href="#" style="font-size:14px;font-weight: 400;color: #ffffff"  v-on:click="showErrorMsg(scoringItem)">存在争议</a>
												</p>
												<p v-if="scoringItem.isError==1 && scoringItem.isAgree!=null" style="text-align:center;width: 67px;line-height:24px;height: 24px;background: rgba(129,194,105,1);border-radius: 3px;" >
													<a href="#" style="font-size:14px;font-weight: 400;color: #ffffff"  v-on:click="showErrorMsg(scoringItem)">查看争议</a>
												</p>
											</div>
										</td>
										<td style="border-bottom:solid 1px #ddd; border-right:solid 1px #ddd;vertical-align:middle;"  v-if="scoringItem.isError !=1" >
											<div style="vertical-align:middle; margin:0 5px;" >{{scoringItem.itemname}}
												<p v-if="scoringItem.isError==1" style="text-align:center;width: 67px;line-height:24px;height: 24px;background: #DF1912;border-radius: 3px;" >
													<a href="#" style="font-size:14px;font-weight: 400;color: #ffffff"  v-on:click="showErrorMsg(scoringItem)">存在争议</a>
												</p>
											</div>
										</td>
										<%--实体智能评查内容--%>
										<td style="border-bottom:solid 1px #ddd; border-right:solid 1px #ddd;vertical-align:middle; width:20%;"  >
											<div style="vertical-align:middle; margin:0 5px;" :id="'aiInfo'+index+index1">
<%--												{{scoringItem.aiInfo}}--%>
											</div>
										</td>

										<td align="center" style="border-bottom:solid 1px #ddd; border-right:solid 1px #ddd; width:10%; vertical-align:middle;">
											<div  :id="index+'expertItem'+index1" style="float:left;">
												<div v-if="scoringItem.score !=null " style="float:right; padding:0 5px 0 10px;" >
													<input disabled   type="radio" value="1" :name="index+'scoreRadio'+index1" v-model="scoringItem.score" @click="updateItem(index,index1,scoringItem.itemscore,scoringItem.temItemId)" >存在问题
													<input  disabled  type="radio" value="0" :name="index+'scoreRadio'+index1" v-model="scoringItem.score" @click="updateItem(index,index1,scoringItem.itemscore,scoringItem.temItemId)" >不存在问题
												</div>
												<div v-else style="float:right; padding:0 5px 0 10px;">

													<input disabled class="radoos" type="radio" value="1" :name="index+'scoreRadio'+index1" v-model="scoringItem.score" @click="updateItem(index,index1,scoringItem.itemscore,scoringItem.temItemId)" readonly>存在问题
													<input disabled class="radoos" type="radio" value="0" :name="index+'scoreRadio'+index1" v-model="scoringItem.score" @click="updateItem(index,index1,scoringItem.itemscore,scoringItem.temItemId)" readonly>不存在问题
												</div>
												<div style="color:red; font-size: 12px;">{{scoringItem.validatorMessage}}</div>
											</div>
										</td>
										<td disabled align="center" style="border-bottom:solid 1px #ddd; width:30%;vertical-align:middle;padding:11px 5px">
											<div disabled style="height: 150px;width: 500px;border:1px solid #0006;font-size: 14px" >
												<textarea disabled  v-if="scoringItem.score == 0" maxlength="2000" :id="index+'comment'+index1" name="commeInput" v-model="scoringItem.commeStr"
															  @input="updateCommon(index,index1)"  rows="4" class="form-control" style="width: 100%;height: 149px;"
															  placeholder="请输入具体情形，长度不能超过2000字符" :title="scoringItem.commeStr" >
												</textarea>
												<div   v-show="scoringItem.score == 1" :id="index+'commeStrDiv'+index1" style="height: 150px;width: 500px;line-height: 20px">
<%--													<div style="width:200px;height:30px;margin-top:7px;float:left;overflow:hidden;">--%>
	<%--														<input type="text" :id="index+'commentOne'+index1" name="commeStrThreeInput" v-model="scoringItem.commeStrThree" class="from_gaozhi disNo"  style="width:150px;outline:none;margin-top: 10px;margin-left: 10px" onclick="updateCommon(index,index1)" readonly="true" style="width:200px;height:30px;">--%>
	<%--													</div>--%>
	<%--													<div id="fuzzysearchdiv" style="display:none;width:200px;z-index:3;position:absolute;height:20px;"--%>
	<%--														 onMouseOver="mousein()" onMouseOut="mouseout()">--%>
	<%--													</div>--%>
	<%--													<div id="selectdiv" style="display:none;top:230px;border:1px solid #A9A9A9;width:200px;z-index:2;position:absolute;overflow-y :scroll;height:200px;background-color:white;"--%>
	<%--														 onMouseOver="mousein()" onMouseOut="mouseout()">--%>
	<%--													</div>--%>
														<%--<input disabled autocomplete="off" maxlength="50" type="text"--%>
															   <%--from_gaozhi disNo name="commeStrThreeInput"--%>
															   <%--:id="index+'commentOne'+index1"--%>
															   <%--v-model="scoringItem.commeStrThree"--%>
															   <%--class="from_gaozhi "--%>
															   <%--style="width:197px;outline:none;margin-top: 10px" placeholder="（具体的文书,仅限200字符之内）"--%>
															   <%--@input="updateCommon(index,index1)" >--%>
<%--													<select  :id="index+'commentOne'+index1" name="commeStrThreeInput" v-model="scoringItem.commeStrThree" class="from_gaozhi disNo"  style="width:150px;outline:none;margin-top: 10px;margin-left: 10px"  @select="updateCommon(index,index1)">--%>
<%--															<template v-for="(level, is) in scoringIndexList.indexOneList">--%>
<%--																<option :value="level">{{level}}</option>--%>
<%--															</template>--%>
<%--														</select>--%>

	<textarea disabled  v-if="scoringItem.score>=0" maxlength="2000" :id="index+'comment'+index1" name="commeInput" v-model="scoringItem.commeStr"
														  @input="updateCommon(index,index1)"  rows="4" class="form-control" style="width:100%;height: 100%;"
														  placeholder="请输入具体情形，长度不能超过2000字符" :title="scoringItem.commeStr" >
												</textarea>
													<%--<input disabled autocomplete="off" maxlength="200" type="text" name="commeStrTwoInput"--%>
														   <%--:id="index+'commentTwo'+index1"  v-model="scoringItem.commeStr"--%>
														   <%--class="from_gaozhi "  style="width:249px;outline:none;margin-right: 0px;" placeholder="（详细位置或某一处,仅限200字符之内）"--%>
														   <%--@input="updateCommon(index,index1)" >--%>
													<%--<input disabled autocomplete="off" maxlength="200" type="text" name="commeStrThreeInput"--%>
														   <%--:id="index+'commentThree'+index1"  v-model="scoringItem.commeStrThree" class="from_gaozhi "--%>
														   <%--style="width:250px;outline:none;margin-right: 0px;" placeholder="（具体问题,仅限200字符之内）" @input="updateCommon(index,index1)" >问题<br/>--%>
<%--&lt;%&ndash;													<textarea autocomplete="off" maxlength="2000" name="commeStrFourInput" ref="inputText" prop="commeStrFour"&ndash;%&gt;--%>
<%--&lt;%&ndash;															  :id="index+'commentFour'+index1"  v-model="scoringItem.commeStrFour"&ndash;%&gt;--%>
<%--&lt;%&ndash;															  placeholder="详情说明：（需对存在的问题进行详细说明）长度不能超过2000个字符" style=" width: 430px; height: 90px;outline: none;margin-top: 3px"&ndash;%&gt;--%>
<%--&lt;%&ndash;															  @input="updateCommon(index,index1)" >${{scoringItem.commeStrFour}}</textarea>&ndash;%&gt;--%>
													<%--<input disabled autocomplete="off" maxlength="2000" type="text" name="commeStrFour"--%>
														   <%--:id="index+'commentThree'+index1"  v-model="scoringItem.commeStrFour" style="width:100%; height: 100px;"--%>
														   <%--style="width:250px;outline:none;margin-right: 0px;" placeholder="（详情说明,仅限2000字符之内）" @input="updateCommon(index,index1)" ><br/>--%>
												</div>
											</div>

<%--												<textarea v-if="scoringItem.score>=0" maxlength="2000" :id="index+'comment'+index1" name="commeInput" v-model="scoringItem.commeStr"--%>
<%--														  @input="updateCommon(index,index1)"  rows="4" class="form-control" style="width:100%;"--%>
<%--														  placeholder="请输入具体情形，长度不能超过2000字符" :title="scoringItem.commeStr" >--%>
<%--												</textarea>--%>
<%--											<textarea v-else maxlength="2000" :id="index+'comment'+index1" name="commeInput" v-model="scoringItem.commeStr"--%>
<%--													  @input="updateCommon(index,index1)"  rows="4" class="form-control" style="width:100%;"--%>
<%--													  placeholder="请输入具体情形，长度不能超过2000字符" :title="scoringItem.commeStr" disabled>--%>
<%--												</textarea>--%>
											<div style="color:red; font-size: 12px;">{{scoringItem.validatorComment}}</div>
										</td>
									</tr>
								</table>
							</td>
							<!-- <td v-else >
                                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                    <tr>
                                        <td style="border-bottom:solid 1px #f7f7f7;">
                                            <div style="vertical-align:middle; margin:0 5px 5px 0;">{{scoringIndex.indexdesc}}</div>
                                        </td>
                                        <td style="border-bottom:solid 1px #f7f7f7;">
                                            <div :id="'expertIndex'+index"  class="form-group" style="width: 150px;float: right;">
                                                <div v-if="scoringIndex.isAdd==1" style="float:left; padding:5px 2px 7px 3px; color:red; font-size:20px;">+</div>
                                                <div v-if="scoringIndex.isAdd!=1" style="float:left; padding:0 0 3px 0; margin:0; color:red; font-size:26px;">-</div>
                                                <input name="scoreInput" type="text" v-model="scoringIndex.score" autocomplete="off"
                                                    @input="updateIndex(index,scoringIndex.indexscore)" :class="'checkitem'+index"
                                                    class="form-control inputDisabled"  placeholder="请输入初评得分">
                                                <div style="color:red;font-size: 5px;">{{scoringIndex.validatorMessage}}</div>
                                            </div>
                                        </td>
                                        <td>
                                        </td>
                                    </tr>
                                </table>
                            </td> -->

						</tr>
						<tr>
							<tr v-for="(scoringIndex, index) in scoringIndexList.expertHandlIndexScoreList" v-if="scoringIndex.isError==2"  style="background-color: #ECD9E0;">
								<td height="30" align="center" >{{index+1}}</td>

								<!-- 评查项目 -->
								<td align="center" >{{scoringIndex.indexname}}
								</td>

								<!-- 标准分 -->
								<!-- <td style="vertical-align:middle;">{{scoringIndex.indexscore}}</td> -->

								<!-- 评分细则 -->
								<td disabled v-if="scoringIndex.expertHandlItemScoreList != null && scoringIndex.expertHandlItemScoreList.length !=0 "  style="padding:0;">
									<table width="100%" border="0" cellspacing="0" cellpadding="0" style="padding:0;">
										<tr  v-for="(scoringItem, index1) in scoringIndex.expertHandlItemScoreList">
											<td style="background-color:#ECD9E0;border-bottom:solid 1px #ddd; border-right:solid 1px #ddd;vertical-align:middle;"  v-if="scoringItem.isError==1" >
												<div style="vertical-align:middle; margin:0 5px;" >{{scoringItem.itemname}}
													<p v-if="scoringItem.isError==1" style="text-align:center;width: 67px;line-height:24px;height: 24px;background: #DF1912;border-radius: 3px;" >
														<a href="#" style="font-size:14px;font-weight: 400;color: #ffffff"  v-on:click="showErrorMsg(scoringItem)">存在争议</a>
													</p>
												</div>
											</td>
											<td style="border-bottom:solid 1px #ddd; border-right:solid 1px #ddd;vertical-align:middle;"  v-if="scoringItem.isError !=1" >
												<div style="vertical-align:middle; margin:0 5px;" >{{scoringItem.itemname}}
													<p v-if="scoringItem.isError==1" style="text-align:center;width: 67px;line-height:24px;height: 24px;background: #DF1912;border-radius: 3px;" >
														<a href="#" style="font-size:14px;font-weight: 400;color: #ffffff"  v-on:click="showErrorMsg(scoringItem)">存在争议</a>
													</p>
												</div>
											</td>
											<td style="border-bottom:solid 1px #ddd; border-right:solid 1px #ddd; width:10%; vertical-align:middle;">
												<div  :id="index+'expertItem'+index1" style="float:left;">
													<div v-if="scoringItem.score !=null " style="float:right; padding:0 5px 0 10px;" >
															<input   type="radio" value="1" :name="index+'scoreRadio'+index1" v-model="scoringItem.score" @click="updateItem(index,index1,scoringItem.itemscore,scoringItem.temItemId)" >是
															<input   type="radio" value="0" :name="index+'scoreRadio'+index1" v-model="scoringItem.score" @click="updateItem(index,index1,scoringItem.itemscore,scoringItem.temItemId)" >否
													</div>
													<div v-else style="float:right; padding:0 5px 0 10px;">

															<input class="radoos" type="radio" value="1" :name="index+'scoreRadio'+index1" v-model="scoringItem.score" @click="updateItem(index,index1,scoringItem.itemscore,scoringItem.temItemId)" readonly>是
															<input class="radoos" type="radio" value="0" :name="index+'scoreRadio'+index1" v-model="scoringItem.score" @click="updateItem(index,index1,scoringItem.itemscore,scoringItem.temItemId)" readonly>否
													</div>
													<div style="color:red; font-size: 12px;">{{scoringItem.validatorMessage}}</div>
												</div>
											</td>
											<td style="border-bottom:solid 1px #ddd; width:30%;vertical-align:middle;padding:0px 5px 0px 5px;">
												<textarea v-if="scoringItem.score>=0" maxlength="2000" :id="index+'comment'+index1" name="commeInput" v-model="scoringItem.commeStr"
												@input="updateCommon(index,index1)"  rows="4" class="form-control" style="width:100%;"
												placeholder="请输入具体情形，长度不能超过2000字符" :title="scoringItem.commeStr" >
												</textarea>
												<textarea v-else maxlength="2000" :id="index+'comment'+index1" name="commeInput" v-model="scoringItem.commeStr"
												@input="updateCommon(index,index1)"  rows="4" class="form-control" style="width:100%;"
												placeholder="请输入具体情形，长度不能超过2000字符" :title="scoringItem.commeStr" disabled>
												</textarea>
												<div style="color:red; font-size: 12px;">{{scoringItem.validatorComment}}</div>
											</td>
										</tr>
									</table>
								</td>
								<!-- <td v-else >
									<table width="100%" border="0" cellspacing="0" cellpadding="0">
										<tr>
											<td style="border-bottom:solid 1px #f7f7f7;">
												<div style="vertical-align:middle; margin:0 5px 5px 0;">{{scoringIndex.indexdesc}}</div>
											</td>
											<td style="border-bottom:solid 1px #f7f7f7;">
												<div :id="'expertIndex'+index"  class="form-group" style="width: 150px;float: right;">
													<div v-if="scoringIndex.isAdd==1" style="float:left; padding:5px 2px 7px 3px; color:red; font-size:20px;">+</div>
													<div v-if="scoringIndex.isAdd!=1" style="float:left; padding:0 0 3px 0; margin:0; color:red; font-size:26px;">-</div>
													<input name="scoreInput" type="text" v-model="scoringIndex.score" autocomplete="off"
														@input="updateIndex(index,scoringIndex.indexscore)" :class="'checkitem'+index"
														class="form-control inputDisabled"  placeholder="请输入初评得分">
													<div style="color:red;font-size: 5px;">{{scoringIndex.validatorMessage}}</div>
												</div>
											</td>
											<td>
											</td>
										</tr>
									</table>
								</td> -->

							</tr>
							<tr>
				             <td colspan="2">合计得分</td>
				             <td colspan="2">任意一小项选择是，则扣全50分，每一小项都是必选项<input disabled id="jsScore" type="text" v-model="scoringIndexList.entityscore" class="form-control"></td>

				           </tr>
						<%--	<tr>
								<td height="30" colspan="9">
									<div  class="col-sm-12" style="padding-left: 30px;">
									<input id="fileMaterials" name="fileMaterials" type="checkbox" onchange="changeFileMaterials()"><span style="font-size: 16px;">&nbsp;案卷材料严重不全，导致无法全面评价案卷实体和程序问题</span></div>
									<div class="col-sm-12" style="padding-left: 30px;">
											<h4>具体情形：</h4>
										<p>①一般行政处罚类案卷：仅有立案审批表、现场检查（勘察）笔录、调查询问笔录、证据材料、案件调查报告、行政处罚事先（听证）告知书、行政处罚决定书中的三项或不足三项的；</p>
										<p>②按日计罚类案卷：仅有立案审批表、现场检查（勘察）笔录、调查询问笔录、证据材料、案件调查报告、责令改正违法行为决定书、行政处罚事先（听证）告知书、行政处罚听证通知书、听证笔录、原行政处罚决定书、
											按日连续处罚决定书、按日连续处罚后再次责令改正违法行为决定书中的三项或不足三项的；</p>
										<p>③查封扣押类案卷：仅有立案审批表、现场检查（勘察）笔录、调查询问笔录、证据材料、查封/扣押决定书、查封/扣押清单中的三项或不足三项的；</p>
										<p>④限产停产类案卷：仅有立案审批表、现场检查（勘察）笔录、调查询问笔录、证据材料、责令改正违法行为决定书、责令限制生产/停产整治事先（听证）告知书、责令限制生产/停产整治听证通知书、责令限制生产/停产整治决定书中的三项或不足三项的；</p>
										<p>⑤移送拘留类案卷：仅有立案审批表、现场检查（勘察）笔录、调查询问笔录、证据材料、案件调查报告、行政处罚事先（听证）告知书、行政处罚听证通知书、听证笔录、行政处罚决定书（或责改决定书）、环境违法适用行政拘留处罚案件移送书、环境违法适用行政拘留处罚案件移送材料清单中的三项或不足三项的；</p>
										<p>⑥环境污染犯罪类案卷：仅有立案审批表、现场检查（勘察）笔录、调查询问笔录、证据材料、案件调查报告、行政处罚事先（听证）告知书、行政处罚听证通知书、听证笔录、行政处罚决定书、移送涉嫌环境犯罪案件审批表、涉嫌环境犯罪案件移送书、涉嫌环境犯罪案件移送材料清单中的三项或不足三项的；</p>
									</div>
								</td>
							</tr>--%>
						<tr>
							<td height="30" colspan="9">
								<textarea maxlength="2000" v-model="scoringIndexList.problemRemark" rows="5" class="form-control" id="problem_remark" placeholder="疑难（争议）问题描述"></textarea>
							</td>
						</tr>
						<tr>
							<td height="30" colspan="9" style="line-height: 3.1">
								<div class="col-sm-12" style="padding-left:30px">
									<input id="yxdxanlituijian" name="yxdxanlituijian" type="checkbox" v-on:click="checkBoxClick('1')" style="margin-right: 10px">优秀案卷推荐
								</div><div class="col-sm-12" style="display: none;padding-left:30px" id ="yxdxAnLiTuiJianReviews1"  >
								<table>
									<c:forEach items="${tcDictionaryList}" var="item" varStatus="status">
										<tr>
											<input  type="checkbox" name="yxaj" value="${item.code}" >&nbsp;${item.name} &nbsp; &nbsp;
											<c:if test="${status.count%4==0}"><br></c:if>
										</tr>
									</c:forEach>
								</table>
								<textarea maxlength="2000" v-model="scoringIndexList.yxdxanlituijianreviews" rows="5" class="form-control" id="zjpy" placeholder="请输入专家评语，长度不能超过2000个字符"></textarea>
							</div>
							</td>
						</tr>
						<tr>
							<td height="30" colspan="9" style="line-height: 3.1">
								<div class="col-sm-12" style="padding-left:30px">

									<input id="ajpjYxdxanlituijian" name="ajpjYxdxanlituijian" type="checkbox"  v-on:click="ajpjCheckBoxClick()"  style="margin-right: 10px">较差案卷推荐
								</div><div class="col-sm-12" style="display: none;padding-left:30px" id ="ajpjYxdxAnLiTuiJianReviews1"  >
								<table>
									<c:forEach items="${jcajDictionaryList}" var="item" varStatus="status">
										<tr>
											<input  type="checkbox" name="jcaj" value="${item.code}">&nbsp;${item.name}&nbsp; &nbsp;
											<c:if test="${status.count%4==0}"><br/></c:if>
										</tr>
									</c:forEach>
								</table>
								<textarea maxlength="2000"  v-model="scoringIndexList.ajpjYxdxanlituijianreviews" rows="5" class="form-control" id="ajpj" placeholder="请输入专家评语，长度不能超过2000个字符"></textarea>

							</div>
							</td>
						</tr>
						<tr>
							<td height="30" colspan="9">
								<div class="col-sm-12" style="padding-left:30px">
									<input id="noTuiJian" name ="noTuiJian" type="checkbox"  v-on:click="noCheckBoxClick()"  style="margin-right: 10px" >不推荐为典型案例
								</div>
							</td>
						</tr>


						</tbody>
					</table>



				</form>
		 		<div class="submit">
		 			<!--   -->
		 			<button v-if="scoringIndexList.scoredstate=='3'||scoringIndexList.scoredstate=='4'" type="button" class="btn btn-primary" v-on:click="saveSubmit(scoringIndexList.id,0)" name="signup" value="Sign up" style="font-size:14px;width:126px;height:40px;color:#ffffff; padding: 0; margin-top:5px;">信息暂存</button>
	 				<button type="button" class="btn btn-danger" v-on:click="saveSubmit(scoringIndexList.id,1)" name="signup" value="Sign up" style="font-size:14px;width:126px;height:40px;color:#ffffff;  margin-top:5px;padding: 0">信息保存</button>
 				</div>
				<!--  附件查看 -->
				<div class="modal fade" id="view" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
					<div class="modal-dialog modal-lg">
						<div class="modal-content"></div>
					</div>
				</div>
			</div>
		</div>
</div>
</body>
</html>
