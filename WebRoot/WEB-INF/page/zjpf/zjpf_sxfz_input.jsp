<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
</head>
<script type="text/javascript">
		/* 	判断评分的状态scoredState为1 */
	 $(document).ready(function(){
		 business.listenEnter();
			 var scoredState= eval('${expertHandlFileList.scoredstate}');
			 var status = eval('${status}');
			 if(status =='1'){
				 $("#chickAnjuan").hide();
				 $("#submitBaoChong").hide();
				 $("#fileCodeInput").hide();
				 $("#queren").hide();
				// $("textarea").removeAttr("disabled");
				 var yxdxAnLiTuiJian = "${expertHandlFileList.yxdxanlituijian}";
					if(yxdxAnLiTuiJian == '1'){
						$("#yxdxanlituijian").attr("checked","checked");
						$("#yxdxanlituijian").val("1");
						$("#yxdxAnLiTuiJianReviews1").css("display","block");
						var yxdxanlituijianreviews = "${expertHandlFileList.yxdxanlituijianreviews}";
						yxdxanlituijianreviews  = yxdxanlituijianreviews.replaceAll('</br>','\n');
						$("#yxdxanlituijianreviews").html(yxdxanlituijianreviews);
					}
					var shishizhengjureviews = "${expertHandlFileList.shishizhengjureviews}";
					shishizhengjureviews  = shishizhengjureviews.replaceAll('</br>','\n');
					$("#shishizhengjureviews").html(shishizhengjureviews);
					
					var falushiyongreviews = "${expertHandlFileList.falushiyongreviews}";
					falushiyongreviews  = falushiyongreviews.replaceAll('</br>','\n');
					$("#falushiyongreviews").html(falushiyongreviews);
					
					var chengxuguizereviews = "${expertHandlFileList.chengxuguizereviews}";
					chengxuguizereviews  = chengxuguizereviews.replaceAll('</br>','\n');
					$("#chengxuguizereviews").html(chengxuguizereviews);
					
					var jiafenqingkuangreviews = "${expertHandlFileList.jiafenqingkuangreviews}";
					jiafenqingkuangreviews  = jiafenqingkuangreviews.replaceAll('</br>','\n');
					$("#jiafenqingkuangreviews").html(jiafenqingkuangreviews);
			
				/*  var expertreviews =  "${expertHandlFileList.expertreviews}"
					 expertreviews=	 expertreviews.replaceAll('</br>','\n');
					 $("#expertreviews").html(expertreviews); */
					/*  var yxdxanlituijianreviews = "${expertHandlFileList.yxdxanlituijianreviews}";
					 yxdxanlituijianreviews  = yxdxanlituijianreviews.replaceAll('</br>','\n');
					 $("#yxdxanlituijianreviews").html(yxdxanlituijianreviews); */
			 }
			 if(scoredState == '1'){
				 if(status !='1'){
					$("input").removeAttr("readonly");
					$("#submitBaoChong").removeAttr("style display");
					$("#chickAnjuan").hide();
					$("#yxdxanlituijian").removeAttr("disabled");
					$("textarea").removeAttr("disabled");
					$("#fileCodeInput").attr("disabled","disabled");
					$("#fileCodeInput").val('${ expertHandlFileList.filecode}');
					var yxdxAnLiTuiJian = "${expertHandlFileList.yxdxanlituijian}";
					if(yxdxAnLiTuiJian == '1'){
						$("#yxdxanlituijian").attr("checked","checked");
						$("#yxdxanlituijian").val("1")
						$("#yxdxAnLiTuiJianReviews1").css("display","block");
						var yxdxanlituijianreviews = "${expertHandlFileList.yxdxanlituijianreviews}";
						yxdxanlituijianreviews  = yxdxanlituijianreviews.replaceAll('</br>','\n');
						$("#yxdxanlituijianreviews").html(yxdxanlituijianreviews);
					}
						var shishizhengjureviews = "${expertHandlFileList.shishizhengjureviews}";
						shishizhengjureviews  = shishizhengjureviews.replaceAll('</br>','\n');
						$("#shishizhengjureviews").html(shishizhengjureviews);
						
						var falushiyongreviews = "${expertHandlFileList.falushiyongreviews}";
						falushiyongreviews  = falushiyongreviews.replaceAll('</br>','\n');
						$("#falushiyongreviews").html(falushiyongreviews);
						
						var chengxuguizereviews = "${expertHandlFileList.chengxuguizereviews}";
						chengxuguizereviews  = chengxuguizereviews.replaceAll('</br>','\n');
						$("#chengxuguizereviews").html(chengxuguizereviews);
						
						var jiafenqingkuangreviews = "${expertHandlFileList.jiafenqingkuangreviews}";
						jiafenqingkuangreviews  = jiafenqingkuangreviews.replaceAll('</br>','\n');
						$("#jiafenqingkuangreviews").html(jiafenqingkuangreviews);
					
			 }}
			 else{
				 $("#fileCodeInput").val("");
			 }
			});
		/* 	评分状态的scoredState为0 */
			function chickAnjuan(){
				var fileCodeInput = trim($("#fileCodeInput").val());
				fileCodeInput = fileCodeInput.replace(/\s+/g,"");
				var fileCode = trim($("#filecode").text());
				fileCode = fileCode.replace(/\s+/g,"");
				if( fileCodeInput == fileCode){
				swal("开始打分", "开始打分!", "success");
				$("input").removeAttr("readonly");
				$("#submitBaoChong").removeAttr("style display");
				$("#chickAnjuan").hide();
				$("#yxdxanlituijian").removeAttr("disabled");
				$("#fileCodeInput").attr("disabled","disabled");
				$("textarea").removeAttr("disabled");
				}else{
					swal("开始打分", "请输入正确的文件号！", "error");
				}
				}
			function trim(str) {
				  return str.replace(/(^\s+)|(\s+$)/g, "");
				}
			//动态加载评语
		
			function checkBoxClick(){
				
				if($("#yxdxAnLiTuiJianReviews1").css("display")=='none' ){//如果show是隐藏的
					$("#yxdxAnLiTuiJianReviews1").css("display","block"); //  yxdxanlituijianreviews show的display属性设置为block（显示）
					$("#yxdxanlituijian").val("1");
					}else{//如果show是显示的
						
					$("#inputyxdxanlituijian").html("<input name = 'yxdxanlituijian' type='hidden' value='0'>")
					$("#yxdxAnLiTuiJianReviews1").css("display","none");//show的display属性设置为none（隐藏）
					 
			}
			}
			
			//下载文件
			function xiaZaiAnJuan(){
			 var fileId = $("#fileId").val();
			  $.ajax({
				    type:"post",
				    url:WEBPATH+'/zjpf/xzcfExistFileUrl.do',
				    data:{fileId:fileId},           //注意数据用{}
				    success:function(data){  //成功
					 if("yes" == data){
							window.location.href="${pageContext.request.contextPath}/zjpf/zjpfAnJuandown.do?fileId="+fileId;
						    return false;
				         }else if("no" == data){
				            	  swal( "操作失败","该案卷不存在!", "error");
				            	  return false;
						}else if("suffixerror" ==data){
							  swal( "操作失败","该案卷上传数据格式有问题!", "error");
			            	  return false;
						}
				         }
				});
			}

	 $(document).ready(function(){
			//保存数据方法
			var pageNum =$("#pageNum").val();
			$('#submitBaoChong').click(function() {
		       	 var options = {
		           url: WEBPATH+'/zjpf/savezjpf.do?',
		           type: 'post',
		           success:function(data){
			           if(data.result=="error"){
			        	   if(data.data="1"){
			        	   swal("操作失败", "您输入数据格式不准确!", "error");
			               return false; 
			        	   }else{
			        		   swal("操作失败", "信息保存操作失败了!", "error");
				               return false; 
			        	   }
			           }else if(data.result=="success"){
			        	  swal({title: "保存成功",text: "",type:"success"});
			        	business.addMainContentParserHtml('zjpf/xzcfList.do','pageNum='+pageNum);
			        	return false;
			           }
		         	}
		       	 };
		       	$("#zjpfForm").data('formValidation').validate();
		       	var validate = $("#zjpfForm").data('formValidation').isValid();
		       	if(validate){
		       	 	$('#zjpfForm').ajaxSubmit(options);
		       	}
		   	});
			
			
			$("#zjpfForm").formValidation({
				   framework: 'bootstrap',
			        message: 'This value is not valid',
			        icon:{
				            valid: 'glyphicon glyphicon-ok',
				            invalid: 'glyphicon glyphicon-remove',
				            validating: 'glyphicon glyphicon-refresh'
			        },
			        fields: {
			        	
			         	"shishizhengjuscore": {
			                validators: {
			                    notEmpty: {
			                        message: '事实证据 得分不能为空！'
			                    },
			                    regexp: {
			                    	  regexp: /(?!^0\.0?0$)^([0-9]|[1-2][0-9])(\.[0-9]{1,2})?$|^(30|30\.0|30\.00|0\.0|0\.00)$/,
					                     message: '请输入0-30之间的整数或者包含2位小数!'
			                    }
			                }
			            },
			            "shishizhengjureviews":{
			            	 validators: {
				                    notEmpty: {
				                        message: '事实证据得分或减分理由不能为空！'
				                    },
				                    stringLength: {
				                        min: 1,
				                        max: 300,
				                        message: '1-300个字符'
				                    }
			            	 }
			            },
			         	"falushiyongscore": {
			                validators: {
			                    notEmpty: {
			                        message: '适用法律 得分不能为空！'
			                    },
			                    regexp: {
			                        regexp:  /(?!^0\.0?0$)^([0-9]|[1-2][0-9]|[3][0-4])(\.[0-9]{1,2})?$|^(35|35\.0|35\.00|0\.0|0\.00)$/,
			                        message: '请输入0-35之间的整数或者包含2位小数!'
			                    }
			                }
			            },
			            "falushiyongreviews":{
			            	 validators: {
				                    notEmpty: {
				                        message: '适用法律得分或减分理由不能为空！'
				                    },
				                    stringLength: {
				                        min: 1,
				                        max: 300,
				                        message: '1-300个字符'
				                    }
			            	 }
			            },
			         	"chengxuguizescore": {
			                validators: {
			                    notEmpty: {
			                        message: '程序规则 得分不能为空！'
			                    },
			                    regexp: {
			                    	 regexp:  /(?!^0\.0?0$)^([0-9]|[1-2][0-9]|[3][0-4])(\.[0-9]{1,2})?$|^(35|35\.0|35\.00|0\.0|0\.00)$/,
				                         message: '请输入0-35之间的整数或者包含2位小数！'
			                    }
			                }
			            },
			            "chengxuguizereviews":{
			            	 validators: {
				                    notEmpty: {
				                        message: '程序规则 得分或减分理由不能为空！'
				                    },
				                    stringLength: {
				                        min: 1,
				                        max: 300,
				                        message: '1-300个字符'
				                    }
			            	 }
			            },
			         	"jiafenqingkuang": {
			                validators: {
			                    notEmpty: {
			                        message: '加分情况 得分不能为空！'
			                    },
			                    regexp: {
			                        regexp: /(?!^0\.0?0$)^([0-9]|[1][0-9])(\.[0-9]{1,2})?$|^(20|20\.0|20\.00|0\.0|0\.00)$/,
			                        message: '请输入0-20之间的整数或者包含2位小数!'
			                    }
			                }
			            },
			            "jiafenqingkuangreviews":{
			            	 validators: {
				                    notEmpty: {
				                        message: '加分情况得分或减分理由不能为空！'
				                    },
				                    stringLength: {
				                        min: 1,
				                        max: 300,
				                        message: '1-300个字符'
				                    }
			            	 }
			            },
			            "yxdxanlituijianreviews":{
			            	//enabled:false,
			            	 validators: {
			            		 notEmpty: {
				                        message: '作为优秀典型案例推荐理由不能为空！'
				                    },
				                    stringLength: {
				                        min: 1,
				                        max: 500,
				                        message: '1-500个字符'
				                    }
			            	 }
			            }
			        }
			        
			});
	 });
		 $('#yxdxanlituijian').on('change', function(){
	      var isTrue = ($(this).val() == '1');
	      $('#zjpfForm').formValidation('enableFieldValidators','yxdxanlituijianreviews',isTrue);
	    /*   relationDis(this.id,'是'); */
	   });
	 
	 
	 //动态加载计算总分
	 $(document).ready(function(){
		 $("input[type='text']").change(function() {
			 var inputid = new Array();  
			  var inputArray=$("input[class='form-control input-sm']");
			 inputArray.each(//使用数组的循环函数 循环这个input数组  
			 function (){  
		             var input =$(this);//循环中的每一个input元素  
					inputid.push(input.val());      
			  })
			  var num = 0;

			  for(var i=0;i<inputid.length;i++){
				  var a = inputid[i];
				  if(!(a)==""){
					  if(isNaN(parseFloat(inputid[i].value))){
						  inputid[i].value = 0;
						  inputid[i].toString().split(".")
						  //精度问题
				      	  num = (Math.round((num + parseFloat(inputid[i]))*100))/100; 
					  }
				  }else{
				  }
		 		}
		 $("#expertfinalscore1").html("&nbsp;&nbsp;&nbsp;&nbsp;"+num);	
		 $("#expertfinalscore").val(num);	
		 }); 
	 });
</script>
<body>
<div class="center_weizhi">当前位置：专家评分 - 专家评审案卷 - 涉嫌犯罪移送案卷</div>
<div class="center">
<div class="center_list">
		<div class="dingwei">案卷或材料：（<span id ="filecode" style="color:#06C;font-size:16px;">${expertHandlFileList.filecode}</span>） 
	<c:if test ="${sessionScope.sa_session.sysStatus == '5' }">	
	 <button class="btn btn-success btn-xs" data-toggle="modal" data-target="#myModal" onclick="xiaZaiAnJuan()">下载</button>
	</c:if>
		</div>
        <div class="dingwei" id ="queren">移送案卷编号确认：<input type="text" style="width:200px; float:right;" value ="${files.filecode}"   id="fileCodeInput" placeholder="请输入移送案卷编号"> </div>
		 <div class="dingwei"><button class="btn btn-primary" data-toggle="modal" data-target="#myModal" id ="chickAnjuan"  onclick="chickAnjuan();">开始评分</button></div>
       	<form action="#" id ="zjpfForm" method="post">
        <table class="table table-bordered table-hover table-condensed">
         <thead>
           <tr>
             <td width="50" height="30" bgcolor="#efefef">序号</td>
             <td bgcolor="#efefef">分指标</td>
             <td width="100" bgcolor="#efefef">分指标分值</td>
             <td width="150" bgcolor="#efefef">初评得分</td>
             <td width="150" bgcolor="#efefef">评审依据</td>
           </tr>
         </thead>
         <tbody>
           <tr>
             <td height="30" align="center">1</td>
             <td>事实证据 </td>
             <td width="100">30</td>
             <td width="150">
             <div class="form-group">
                 
             <input  type="text"  readonly="readonly"  class="form-control input-sm" id="shishizhengju" name ="shishizhengjuscore" value="${expertHandlFileList.shishizhengjuscore }"  placeholder="请输入初评得分">
          		</div>
             </td>
             <td><div class="form-group">
               <textarea disabled="disabled"   style ="word-wrap: break-word; word-break: break-all;"  rows="3"  wrap ="hard" class="form-control" id="shishizhengjureviews" name ="shishizhengjureviews"  placeholder="事实证据得分或减分理由不能为空"></textarea>
             </div></td>
           </tr>
           <tr>
             <td height="30" align="center">2</td>
             <td>适用法律 </td>
             <td width="100">35</td>
             <td width="150">
             <div class="form-group">
                 
             <input type="text"  readonly="readonly" class="form-control input-sm" id="falvshiyong" name ="falushiyongscore" value="${expertHandlFileList.falushiyongscore }"  placeholder="请输入初评得分">
           		</div>
             </td>
             <td><div class="form-group">
               <textarea disabled="disabled"  style ="word-wrap: break-word; word-break: break-all;"  rows="3"  wrap ="hard" class="form-control" id="falushiyongreviews" name ="falushiyongreviews"  placeholder="法律适用得分或减分理由不能为空"></textarea>
             </div></td>
           </tr>
           <tr>
             <td height="30" align="center">3</td>
             <td>程序规则 </td>
             <td width="100">35</td>
             <td width="150">
             <div class="form-group">
                 
             <input type="text"  readonly="readonly"  class="form-control input-sm" id="chengxuguize" name ="chengxuguizescore" value="${expertHandlFileList.chengxuguizescore }"  placeholder="请输入初评得分">
           		</div>
             </td>
             <td><div class="form-group">
               <textarea disabled="disabled"   style ="word-wrap: break-word; word-break: break-all;"  rows="3"  wrap ="hard" class="form-control" id="chengxuguizereviews" name ="chengxuguizereviews"  placeholder="程序规则得分或减分理由不能为空"></textarea>
             </div></td>
           </tr>
            
           <tr>
             <td height="30" align="center">4</td>
             <td>加分情况 </td>
             <td width="100">20</td>
             <td width="150">
             <div class="form-group">
                 
             <input type="text"   readonly="readonly"  class="form-control input-sm" id="jiafenqingkuang" name ="jiafenqingkuang" value="${expertHandlFileList.jiafenqingkuang }"  placeholder="请输入初评得分">
                </div>
             </td>
             <td><div class="form-group">
               <textarea  disabled="disabled" style ="word-wrap: break-word; word-break: break-all;"  rows="3"  wrap ="hard" class="form-control" id="jiafenqingkuangreviews" name ="jiafenqingkuangreviews"  placeholder="加分情况得分或减分理由不能为空"></textarea>
             </div></td>
           </tr>
           <tr>
             <td height="30" colspan="2" align="center">小计</td>
             <td width="100">120</td>
             <td width="150"><span id ="expertfinalscore1">&nbsp;&nbsp;&nbsp;&nbsp;${expertHandlFileList.expertfinalscore}</span></td>
             <td>&nbsp;</td>
           </tr>
           <tr>
             <!-- <td height="30" colspan="4">专家评语
             <textarea  rows="5" class="form-control" id="expertreviews" name ="expertreviews" placeholder="请输入专家评语"></textarea></td>
            -->
             <td height="30" colspan="5">
             <input id = "yxdxanlituijian" disabled="disabled"   value="0" name = "yxdxanlituijian" type="checkbox"  onchange="checkBoxClick()" >是否可以作为优秀典型案例推荐
             <div class="form-group">
             <div class="col-sm-12" style="display: none;" id ="yxdxAnLiTuiJianReviews1"  >
             <textarea  style ="word-wrap: break-word; word-break: break-all;"  rows="5"  wrap ="hard" class="form-control" id="yxdxanlituijianreviews" name ="yxdxanlituijianreviews"  placeholder="请输入作为优秀典型案例推荐理由！"></textarea>
             </div>
             </div>
            </tr>
            <tr>
            	
            </tr>
            
         </tbody>
       </table>
          		<div id= "inputyxdxanlituijian"></div>
          <div><input id ="pageNum" type="hidden" value="${pageNum }"></div>
        	<div><input id ="zjpfId"  name = "id" type="hidden" value="${expertHandlFileList.id}"></div>
        	<div><input id ="fileId"  name = "fileid" type="hidden" value="${expertHandlFileList.fileid}"></div>
        	<div><input id="expertfinalscore" type="hidden" name ="expertfinalscore" value="${expertHandlFileList.expertfinalscore}" ></div>
 	 </form>
 
 	  <div class="submit"><c:choose> 
			 <c:when test = "${sessionScope.sa_session.sysStatus != 5}"> 
			 </c:when> 
			 <c:otherwise> 
			 	<a href="#"><button type="button" class="btn btn-primary" id="submitBaoChong" name="signup" value="Sign up" style="font-size:16px;width:150px; margin-top:5px; display:none">信息保存</button></a>
			 </c:otherwise> 
			</c:choose></div>
 	 		</div>
		<!-- <a href="#"><button type="button" class="btn btn-primary" id="submitBaoChong"  style="font-size:16px;width:150px; margin-top:5px; display:none">信息保存</button></a> -->
</div>

	
</body>
</html>