<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<html>
<head>

<script type="text/javascript">
    $(".left_menu").hide();
    $(document).ready(function(){

        //监听enter
        // business.listenEnter("searchButt");
        var pageBean ="${pageBean[0]}";
        var fileId;
        if (pageBean == null || pageBean ==""){
            fileId = '${pageBeanSt[0].fileId}';
        }else {
            fileId = '${pageBean[0].fileId}';
        }
        //导出execl表单
        $("#jmExcel").click(function(){
            window.location.href= WEBPATH+'/zjpf/downExpertScoreErrorExecl.do?type=0&fileId='+fileId;
        });
        //导出execl表单
        $("#stExcel").click(function(){
            window.location.href= WEBPATH+'/zjpf/downExpertScoreErrorExecl.do?type=1&fileId='+fileId;
        });
    })
</script>
</head>
<body>
<div class="center_weizhi" id ="top_title">当前位置：专家评分 - 异常信息</div>
<div class="center">
    <div class="center_list">
        <br/>
        <h4 class="panel-title" style="font-size: 25px;color: #999999;">
           案卷文书号：
            <c:if test="${not empty pageBean}">
                ${pageBean[0].fileCode}
            </c:if>
            <c:if test="${empty pageBean}">
                <c:if test="${not empty pageBeanSt}">
                    ${pageBeanSt[0].fileCode}
                </c:if>
            </c:if>
        </h4>
        <button    class="btn btn-primary"  style="font-size:16px;width:150px; margin-top:5px;margin-right: 10px;float: right;" onclick="macroMgr.onLevelOneMenuClick(null, 'zjpf.do')">返回</button>
        <br/>
            <div class="panel-group" id="accordion">
              <h4 class="panel-title" style="font-size: 20px;">
                  卷面部分<span style="color: red">（在该案卷评审过程中，您与另一位专家的评审意见不一致。以下为“存在争议”的评审项，请根据案卷的实际情况进行核对；确认信息后，在系统中提交即可。）</span>
                  <div style="width:260px;" class="btn-group">
                      &nbsp;&nbsp;&nbsp;&nbsp;  <a href ="#"><button type="button" id="jmExcel"  class="btn btn-danger">导出EXCEL</button></a>
                  </div>
              </h4>

            </div>
            <table class="table table-bordered table-hover table-condensed" style="margin-top:30px;width: 70%;margin-left: 150px;">
             <thead>
               <tr>
                 <td width="50" height="30" bgcolor="#efefef">序号</td>
                 <td width="150"  bgcolor="#efefef">存在争议项</td>
                 <td width="50" bgcolor="#efefef">${pageBean[0].expertNameA==sessionScope.sa_session.userName?pageBean[0].expertNameA:"专家A"}</td>
                 <td width="50" bgcolor="#efefef">${pageBean[0].expertNameB==sessionScope.sa_session.userName?pageBean[0].expertNameB:"专家B"}</td>
                 <td width="150" bgcolor="#efefef">备注</td>
               </tr>
             </thead>
             <tbody>
             <c:if test="${not empty pageBean}">
             <c:forEach  varStatus="id"  items="${pageBean}" var="expertList">
                <tr>
                    <td height="30" align="center" >${id.index + 1}</td>
                    <td>${expertList.indexName}</td>
                    <td>${expertList.expertSocreA}</td>
                    <td>${expertList.expertSocreB}</td>
                    <td>${expertList.remark}</td>
                </tr>
             </c:forEach>
             </c:if>
             </tbody>
           </table>
    </div>
    <div class="center_list">
        <div class="panel-group"  id="accordion1">
            <h4 class="panel-title" style="font-size: 20px;">
                实体部分<span style="color: red"></span>
            </h4>
        </div>
        <table class="table table-bordered table-hover table-condensed" style="margin-top:30px; width: 70%;margin-left: 150px;">
            <thead>
            <tr>
                <td width="50" height="30" bgcolor="#efefef">序号</td>
                <td width="100" bgcolor="#efefef">存在争议项</td>
                <td width="200" bgcolor="#efefef">具体指标（指实体部分）</td>
                <td width="50" bgcolor="#efefef">${pageBeanSt[0].expertNameA==sessionScope.sa_session.userName?pageBeanSt[0].expertNameA:"专家A"}</td>
                <td width="50" bgcolor="#efefef">${pageBeanSt[0].expertNameB==sessionScope.sa_session.userName?pageBeanSt[0].expertNameB:"专家B"}</td>
                <td width="150" bgcolor="#efefef">备注</td>
            </tr>
            </thead>
            <tbody>
            <c:if test="${not empty pageBeanSt}">
                <c:forEach  varStatus="id"  items="${pageBeanSt}" var="expertList">
                    <tr>
                        <td height="30" align="center" >${id.index + 1}</td>
                        <td>${expertList.indexName}</td>
                        <td>${expertList.itemName}</td>
                        <td>${expertList.expertSocreA ==1?"是":"否"}</td>
                        <td>${expertList.expertSocreB ==1?"是":"否"}</td>
                        <td>${expertList.remark}</td>
                    </tr>
                </c:forEach>
            </c:if>
            </tbody>
        </table>
    </div>
</div>
</body>
</html>