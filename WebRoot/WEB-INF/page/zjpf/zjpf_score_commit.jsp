<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
<style type="text/css">
	.modal-backdrop {
		position: relative;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		z-index: 1040;
		background-color: #000
	}
	.modal-backdrop.fade {
		filter: alpha(opacity=0);
		opacity: 0
	}
	.modal-backdrop.in {
		filter: alpha(opacity=50);
	}
	.pdfobject-container {
		width: 100%;
		height:580px;
		margin: 2em 0;
	}
	.textarea-success {
		border-top:green 1px solid;
		border-bottom:green 1px solid;
		border-left:green 1px solid;
     	border-right:green 1px solid;
	}
	.textarea-error {
		border-top:red 1px solid;
		border-bottom:red 1px solid;
		border-left:red 1px solid;
     	border-right:red 1px solid;
	}
    /*/deep/.xiala{*/
        /*margin-top: 10px !important;*/
    /*}*/
	.ceshi{
		color: #DF1912;
		font-weight: 400;
		font-size: 14px;
	}
	#indexName{
		height: 40px;
		width: 270px;
		background: #FFFFFF;
		border: 1px solid #DCDFE6;
		border-radius: 3px;
		margin-left: 15px;}
	.center_list{background-color: #ffffff;}
	.center{	position: absolute;left: 0px;top: 272px; width: 98%;margin: 0 20px;bottom: 35px;right: 5px;background-color: #ffffff;}
	.table>thead>tr>td{line-height: 3.1}
	.table-bordered>thead>tr>td{background-color: #F5F7FA;border:1px solid #EBEEF5;text-align: center}
	.panel-tool-close {
		display: none;
	}
	.noline-input{
		border: none;
		border-bottom: 1px solid black;
		outline: none;
	}
</style>
</head>
<script type="text/javascript">
	$(".left_menu").hide();
	var firScolltop=0;
	var oldValue='';
	business.listenEnter();
	var pageNum = $("#pageNum").val();
	var scoringIndexList = null;
	var temIndexId = 0;
	var expertFileId = $("#expertFileId").val();
	var mater = '${expertHandlFileList.fileMaterials}'
	if (mater == '1'){
		$("#fileMaterials").attr("checked",true);

	}
	$.ajax({
		cache : true,
		type : "GET",
		async : false,
		//api/test/detail/behaviour/74
		url: WEBPATH+"/zjpf/getCommitIndexList.do",
		data:{
			id:expertFileId,handType:0
		},
		error : function(request) {
			swal("错误!","请求异常！", "error");
		},
		success : function(data) {
		  if(data.result =='success'){
                scoringIndexList = data.data;
                // scoringIndexList.problemRemark = problemRemark;
                for(var i=0;i<data.data.expertHandlIndexScoreList.length;i++){
                    data.data.expertHandlIndexScoreList[i].className = 'class'+ data.data.expertHandlIndexScoreList[i].id;
                    var index = data.data.expertHandlIndexScoreList[i];
                    //先创建好select里面的option元素
                    var option=document.createElement("option");
                    //转换DOM对象为JQ对象,好用JQ里面提供的方法 给option的value赋值
                    $(option).val('class' + index.id);
                    $(option).text(index.indexname);
                    $("#indexName").append(option);
                }
                console.log(scoringIndexList);
            }

		  	if(data.result =='success'){
		  		scoringIndexList = data.data;
		  	}

		  	if(data.result =='success'){
		  		scoringIndexList = data.data;
				switch (scoringIndexList.filetype) {
					case "2":
						temIndexId = 118;
						break;
					case "0":
						temIndexId = 110;
						break;
					case "1":
						temIndexId = 111;
						break;
					case "3":
						temIndexId = 120;
						break;
					case "6":
						temIndexId = 115;
						break;
					case "7":
						temIndexId = 116;
						break;
				}
		  	}

		}
	});
    function change(value) {
		var mao;
		var pos;
		var poshigh;
		mao = $("." + value);
		pos = mao.offset().top;
		poshigh = mao.height();
		//alert(poshigh)
		var temp=pos+firScolltop;
		if(oldValue!=''){
			// alert("old:"+$("." + oldValue).offset().top);
			temp=temp-$("." + oldValue).offset().top;
		}
		// alert(pos);
		$("#scrollBarCenter").animate({scrollTop: temp - 210})
		// alert(mao.offset().top)
		firScolltop = temp;
		oldValue=value;
    }


    var xzcfVue = new Vue({
		el: '#CommitVue',
		data: {
			scoringIndexList:scoringIndexList,
			temIndexId:temIndexId,
		},
		mounted: function(){
			this.sumScore();
		//	setTimeout(this.saveTime,1000*60*30);//30分钟自动保存
		},
		computed:{
			totalScore:function(){
				var sum = 0;

				this.scoringIndexList.expertHandlIndexScoreList.forEach(function(item){
					//inCheckValue无需评查,修改为无需评查默认满分了
					// if(item.inCheckValue != 1) {
					// }
					if(item.indexname == null || item.indexname == "" /*|| item.indexname == '加分项'*/ || (this.scoringIndexList.closed==0? item.indexname == '结案审批表':false) || item.isOperator=='0'){
						sum += 0;
					}else{
						sum += item.indexscore;
					}
				});
				return sum;
			},
			inputScore:function(){
				var inpuSco = 0;
				this.scoringIndexList.expertHandlIndexScoreList.forEach(function(item){
					if(item.indexname == null || item.indexname == "" || item.isOperator=='0'){
						inpuSco += 0;
					}else if (item.resultScore!=''){
						inpuSco += item.resultScore;
					}

				});
				if (inpuSco == null || inpuSco == "" || inpuSco == 0){
					return 0.00;
				}else {
					return parseFloat(inpuSco).toFixed(2);;
				}
			}
		},
		methods: {

			/*严重不全的提交按钮 */
			validateForm: function (id) {
				var fileMaterialsNums = document.getElementById('fileMaterialsNums').value;
				var fileMaterialsDocs = document.getElementById('fileMaterialsDocs').value;
				var fileMaterialsSups = document.getElementById('fileMaterialsSups').value;
				var filesId = document.getElementById('filesId').value;

				console.log("严重不全,取filesId:", filesId,"之前传的:",id)

				if (fileMaterialsNums === '' || fileMaterialsDocs === '' || fileMaterialsSups === '') {
					// alert('请填写所有内容！');
					swal("操作失败!", "请填写所有内容", "error");
					return false;
				}

				$.ajax({
					type:"post",
					url:WEBPATH+'/zjpf/notGood.do',
					data:{
						id:filesId,
						fileMaterialsNums:fileMaterialsNums,
						fileMaterialsDocs:fileMaterialsDocs,
						fileMaterialsSups:fileMaterialsSups
					},
					success:function(data){  //成功
						$('#my_dialo').dialog("close");
						if("200" == data.code){
							// alert(222)
							<%--window.location.href="${pageContext.request.contextPath}/zjpf/zjpfList.do";--%>
							business.addMainContentParserHtml('zjpf/zjpfList.do','pageNum=${pageNum}');
							location.reload()
							//return false;
						}else if("400" == data.code){
							document.getElementById('fileMaterialsNums').value = "";
							document.getElementById('fileMaterialsDocs').value= "";
							document.getElementById('fileMaterialsSups').value= "";
							swal( "操作失败","该案卷不存在!", "error");
							return false;
						}else if("suffixerror" ==data){
							document.getElementById('fileMaterialsNums').value = "";
							document.getElementById('fileMaterialsDocs').value= "";
							document.getElementById('fileMaterialsSups').value= "";
							swal( "操作失败","该案卷上传数据格式有问题!", "error");
							return false;
						}
					}
				});
				$('#my_dialo').dialog("close");

				// 如果通过校验，可以继续执行其他操作
				return true;
			},

			showErrorMsg:function(obj){
				$.ajax({
					type:"post",
					url:WEBPATH+'/zjpf/getOtherExpertScore.do',
					data:{
						fileId:$("#fileId").val(),
						expertId:$("#expertId").val(),
						handlType:$("#handlType").val(),
						indexId:obj.indexid
					},
					success:function(data){  //成功
						if (data.result=="success") {
							// swal("争议详情(对方专家评分情况)", "一票否决："+(data.data.voteDownValue==0?"否":"是")+" \n无需评查："+(data.data.inCheckValue==0?"否":"是")+"\n得分："+data.data.resultScore+"\n评审依据:"+(!data.data.comment?"":data.data.comment), "info");
							//如果不为空,说明对面选择了一票否决或者无需评查,否则就展示对方的评分
							if (data.data.option != '' && data.data.option != null && data.data.option!=undefined){
								$('#commit_itemOption').text(data.data.option);
							}else {
								$('#commit_itemOption').text("评分值:"+data.data.expertSocre);
							}

							if (data.data.comme != '' && data.data.comme != null && data.data.comme!=undefined){
								$('#commit_itemComme').text(data.data.comme);
							}else {
								$('#commit_itemComme').text("无内容");
							}

							//主键ID
							$('#commit_error_id').val(data.data.id);
							//现在是A专家还是B砖家
							$('#commit_isAOrB').val(data.data.isAOrB);
							//打开查看异常的弹窗
							$( "#error_dialog_score_commit" ).dialog( "open" );
						}else{
							swal("信息", data.message, "error");
						}
					}
				});
			},
			//判断是否有总分，没有则相加求分
			sumScore:function(){
				var paperScore = this.scoringIndexList.paperscore;
				var firstScore = this.scoringIndexList.expertHandlIndexScoreList;
				if(paperScore==""|| paperScore==null){
					var bonus = 0;
					var denominatorScore = 0;
					var childScore = 0;
					// 扣分项
					var deduction = 0;
					if(firstScore != null&&firstScore.length>0) {
						for(var i = 0; i < firstScore.length; i++) {
							if(!isNaN(parseFloat(firstScore[i].resultScore))&& firstScore[i].indexname != '加分项'  && firstScore[i].isOperator!='0') {
								childScore += firstScore[i].resultScore;
							}
							if(/*firstScore[i].indexname != '加分项' &&*/ (this.scoringIndexList.closed==0? firstScore[i].indexname != '结案审批表':true) && firstScore[i].isOperator!='0') {
								denominatorScore += firstScore[i].indexscore;
							}
							if(firstScore[i].indexname == '加分项'){
								bonus = firstScore[i].resultScore;
							}
							if (firstScore[i].isOperator=='0'){
								deduction += firstScore[i].resultScore;
							}
						}
					}
					if (denominatorScore == 0){
						totalScore = 0.00 + bonus +deduction
					} else {
						var childCountScore = childScore+bonus;
						totalScore = 50 * (childCountScore / denominatorScore) +deduction;
					}
					this.scoringIndexList.paperscore = totalScore.toFixed(2);
				}
			},

			changeCheckBoxNegative:function(indexScore, itemScore) {
				var data = xzcfVue.scoringIndexList.expertHandlIndexScoreList[indexScore].expertHandlItemScoreList;
				var flag = data[itemScore].score;
				var checkitem = ".checkitem" + indexScore;
			  	var isInCheckid = '#isInCheck' + indexScore;

			  	if(flag) {//一票否决选中
			  		xzcfVue.scoringIndexList.expertHandlIndexScoreList[indexScore].expertHandlItemScoreList[itemScore].score = 1;
			  		if(data != null && data.length > 0) {
			  			for(var i = 0; i < data.length - 2; i++){
			  				data[i].score = '';
			  				$("#expertItem" + i + "" + indexScore).removeClass("has-error");
							$("#expertItem" + i + "" + indexScore).removeClass("has-success");
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[indexScore].expertHandlItemScoreList[i].validatorMessage = "";
			  			}
			  		} else {
			  			xzcfVue.scoringIndexList.expertHandlIndexScoreList[indexScore].score = '';
			  			xzcfVue.scoringIndexList.expertHandlIndexScoreList[indexScore].resultScore = '';
			  			$("#expertIndex" + indexScore).removeClass("has-error");
						$("#expertIndex" + indexScore).removeClass("has-success");
						xzcfVue.scoringIndexList.expertHandlIndexScoreList[indexScore].validatorMessage = "";
			  		}
		  			xzcfVue.scoringIndexList.expertHandlIndexScoreList[indexScore].resultScore = 0;
			  		$(checkitem).attr("disabled", "disabled");
			  		$(isInCheckid).attr("disabled", "disabled");

			  		var text = $("#comment" + indexScore).val().replace(/\s+/g,"");
			  		if(text == null || text == ''){
			  			$("#comment" + indexScore).removeClass("textarea-success");
				  		$("#comment" + indexScore).addClass("textarea-error");
						xzcfVue.scoringIndexList.expertHandlIndexScoreList[indexScore].validatorFlag = false;
						xzcfVue.scoringIndexList.expertHandlIndexScoreList[indexScore].validatorMessage = "此项评审依据必填";
					}
			  		xzcfVue.scoringIndexList.expertHandlIndexScoreList[indexScore].voteDownValue = 2;
			  	} else {
			  		xzcfVue.scoringIndexList.expertHandlIndexScoreList[indexScore].expertHandlItemScoreList[itemScore].score = 0;

			  		/*
			  		 *	2019二级指标为复选框, 指标项在集合中最后两个
			  		 *
			  		 *		先判断传入二级指标是否为最后一项
			  		 *			是:判断上一个是否选中
			  		 *			否:判断下一个是否选中
			  		 */
			  		var state = data[itemScore].score;
			  		if(itemScore == data.length - 1) {
			  			if(data[itemScore - 1].score == 1) {
			  				state = true;
			  			}
			  		} else {
			  			if(data[itemScore + 1].score == 1) {
			  				state = true;
			  			}
			  		}
			  		if(!state) {
				  		xzcfVue.scoringIndexList.expertHandlIndexScoreList[indexScore].score = '';
				  		xzcfVue.scoringIndexList.expertHandlIndexScoreList[indexScore].resultScore = '';
				  		if(data != null && data.length > 0) {
				  			for(var i = 0; i < data.length; i++){
								xzcfVue.scoringIndexList.expertHandlIndexScoreList[indexScore].expertHandlItemScoreList[i].validatorFlag = false;
				  			}
				  		} else {
				  			xzcfVue.scoringIndexList.expertHandlIndexScoreList[indexScore].validatorFlag = false;
				  		}
				  		$(checkitem).removeAttr("disabled");
				  		$(isInCheckid).removeAttr("disabled");

				  		$("#comment" + indexScore).removeClass("textarea-error");
				  		$("#comment" + indexScore).addClass("textarea-success");
						xzcfVue.scoringIndexList.expertHandlIndexScoreList[indexScore].validatorFlag = true;
						xzcfVue.scoringIndexList.expertHandlIndexScoreList[indexScore].validatorMessage = "";
						xzcfVue.scoringIndexList.expertHandlIndexScoreList[indexScore].voteDownValue = 0;
			  		}
			  	}
			 	// 总分计算
				var totalScore = 0;
				var childScore = 0;
				var denominatorScore = 0;
	 			var firstScore = xzcfVue.scoringIndexList.expertHandlIndexScoreList;
				var bonus = 0;
				// 扣分项
				var deduction = 0;
				if(firstScore != null&&firstScore.length>0) {
					for(var i = 0; i < firstScore.length; i++) {
						if(!isNaN(parseFloat(firstScore[i].resultScore))&&firstScore[i].indexname != '加分项' && firstScore[i].isOperator!='0') {
							childScore += firstScore[i].resultScore;
						}
						if(firstScore[i].indexname != '加分项' && (this.scoringIndexList.closed==0? firstScore[i].indexname != '结案审批表':true) && firstScore[i].isOperator!='0') {
							denominatorScore += firstScore[i].indexscore;
						}
						if(firstScore[i].indexname == '加分项'){
							bonus = firstScore[i].resultScore;
						}
						if (firstScore[i].isOperator=='0'){
							deduction += firstScore[i].resultScore;
						}
					}
				}
				if (denominatorScore == 0){
					totalScore = 0.00 + bonus +deduction
				} else {
					var childCountScore = childScore+bonus;
					totalScore = 50 * (childCountScore / denominatorScore) +deduction;
				}
				if (totalScore<0){
					totalScore = 0;
				}
				xzcfVue.scoringIndexList.paperscore = totalScore.toFixed(2);
			},
			changeCheckBoxCli:function(index, status) {//一票否决或无需评查复选框点击事件
				// 获取选中未选中的状态 stauts=1一票否决\status=2无需评查
				if(status == '1') {
					//一票否决
					var flag = xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].voteDownValue;
				  	var checkitem = ".checkitem" + index;
				  	var isInCheckid = '#isInCheck' + index;
				  	if(flag) {//一票否决选中
				  		var data = xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList;
				  		if(data != null && data.length > 0) {
				  			for(var i = 0; i < data.length; i++){
				  				data[i].score='';
				  				$("#expertItem" + i + "" + index).removeClass("has-error");
								$("#expertItem" + i + "" + index).removeClass("has-success");
								xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[i].validatorMessage = "";
				  			}
				  		} else {
				  			xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].score = '';
				  			xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore = '';
				  			$("#expertIndex"+index).removeClass("has-error");
							$("#expertIndex"+index).removeClass("has-success");
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].validatorMessage = "";
				  		}
			  			xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore = 0;
				  		$(checkitem).attr("disabled", "disabled");
				  		$(isInCheckid).attr("disabled", "disabled");

				  		var text = $("#comment" + index).val().replace(/\s+/g,"");
				  		if(text == null || text == ''){
				  			$("#comment"+index).removeClass("textarea-success");
					  		$("#comment"+index).addClass("textarea-error");
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].validatorFlag = false;
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].validatorMessage = "此项评审依据必填";
						}
				  	} else {//一票否决取消
				  		xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].score = '';
				  		xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore = '';
				  		var data = xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList;
				  		if(data!=null &&  data.length>0) {
				  			for(var i=0;i<data.length;i++){
								xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[i].validatorFlag = false;
				  			}
				  		} else {
				  			xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].validatorFlag = false;
				  		}
				  		$(checkitem).removeAttr("disabled");
				  		$(isInCheckid).removeAttr("disabled");

				  		$("#comment"+index).removeClass("textarea-error");
				  		$("#comment"+index).addClass("textarea-success");
						xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].validatorFlag = true;
						xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].validatorMessage = "";
				  	}
				} else {
					//无需评查
					var flag = xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].inCheckValue;
				  	var checkitem = ".checkitem"+index;
				  	var isVoteDownid = '#isVoteDown'+index;
				  	if(flag) {
						  //无需评查选中
				  		var data = xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList;
				  		if(data != null && data.length > 0) {
							var resultScore = 0;
				  			for(var i = 0; i < data.length; i++) {
								//选中无需评查后,默认满分
								resultScore = resultScore + data[i].itemscore
								data[i].score = data[i].itemscore;
						  		$("#expertItem" + i + "" + index).removeClass("has-error");
								$("#expertItem" + i + "" + index).removeClass("has-success");
								xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[i].validatorMessage = "";
				  			}
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore = resultScore;
				  		} else {
				  			xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].score = '';
				  			xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore = 0;
				  			$("#expertIndex"+index).removeClass("has-error");
							$("#expertIndex"+index).removeClass("has-success");
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].validatorMessage = "";
				  		}
			  			// xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore = 0;
				  		$(checkitem).attr("disabled","disabled");
				  		$(isVoteDownid).attr("disabled","disabled");
				  	} else {//无需评查取消
				  		var data = xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList;
				  		if(data!=null &&  data.length>0) {
				  			for(var i=0;i<data.length;i++) {
								data[i].score = '';
								xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[i].validatorFlag = false;
				  			}
				  		} else {
				  			xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].validatorFlag= false;
				  		}
				  		xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].score='';
				  		xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore='';
				  		$(checkitem).removeAttr("disabled");
				  		$(isVoteDownid).removeAttr("disabled");
				  	}
				}
				// 总分计算
				var totalScore = 0;
				var childScore = 0;
				var denominatorScore = 0;
	 			var firstScore = xzcfVue.scoringIndexList.expertHandlIndexScoreList;
				var bonus = 0;
				// 扣分项
				var deduction = 0;
				if(firstScore != null&&firstScore.length>0) {
					for(var i = 0; i < firstScore.length; i++) {
						if(!isNaN(parseFloat(firstScore[i].resultScore))&&firstScore[i].indexname != '加分项' && firstScore[i].isOperator!='0') {
							childScore += firstScore[i].resultScore;
						}
						if(firstScore[i].indexname != '加分项' && (this.scoringIndexList.closed==0? firstScore[i].indexname != '结案审批表':true) && firstScore[i].isOperator!='0') {
							denominatorScore += firstScore[i].indexscore;
						}
						if(firstScore[i].indexname == '加分项'){
							bonus = firstScore[i].resultScore;
						}
						if (firstScore[i].isOperator=='0'){
							deduction += firstScore[i].resultScore;
						}
					}
				}
				if (denominatorScore == 0){
					totalScore = 0.00 + bonus +deduction
				} else {
					var childCountScore = childScore+bonus;
					totalScore = 50 * (childCountScore / denominatorScore)+deduction;
				}
				if (totalScore<0){
					totalScore = 0;
				}
				xzcfVue.scoringIndexList.paperscore = totalScore.toFixed(2);
			},
			updateItem:function (index, itemIndex, itemScore, id) {//

				var num = 0;
			  	var childNum =0;
			  	var value = xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].score;
				if(value != '' && value != null){
					if(parseFloat(value) <=parseFloat(itemScore)){
						if(value.substring(value.length-1,value.length) =="."){
							$("#expertItem"+itemIndex+index).removeClass("has-success");
							$("#expertItem"+itemIndex+index).addClass("has-error");
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorFlag= false;
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorMessage="分值项不能为空，且在0-"+itemScore+"分之间，且最多精确到小数点后两位小数！";
							return false;
						}
						if(value.indexOf('-') !=-1){
							$("#expertItem"+itemIndex+index).removeClass("has-success");
							$("#expertItem"+itemIndex+index).addClass("has-error");
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorFlag= false;
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorMessage="分值项不能为负数";
							return false;
						}
			  		 	var re = /^-?\d+\.?\d{0,2}$/;
			    		if( re.test(value) ){   // 返回true
			    			$("#expertItem"+itemIndex+index).removeClass("has-error");
							$("#expertItem"+itemIndex+index).addClass("has-success");
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorFlag= true;
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorMessage="";
			    		}else{
			    			$("#expertItem"+itemIndex+index).removeClass("has-success");
							$("#expertItem"+itemIndex+index).addClass("has-error");
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorFlag= false;
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorMessage="分值项不能为空，且在0-"+itemScore+"分之间，且最多精确到小数点后两位小数！";
					   		return false;
			    		}
					 	var score = xzcfVue.scoringIndexList.expertHandlIndexScoreList[index];
					 	var indexScore = xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].indexscore;
						if(score != null){
							for(var i=0;i<score.expertHandlItemScoreList.length;i++){
								if(!isNaN(parseFloat(score.expertHandlItemScoreList[i].score))){
									if(score.expertHandlItemScoreList[i].isOperator ==1){
										childNum = (Math.round((childNum + parseFloat(score.expertHandlItemScoreList[i].score))*100))/100;
									} else {
										//减分项
										childNum = (Math.round((childNum - parseFloat(score.expertHandlItemScoreList[i].score))*100))/100;
									}

								}
							}
						}
						xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore= childNum;
						if(xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore>indexScore){
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore=indexScore;
						}
						// if( xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore<0){
						// 	xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore =0;
						// }
						// if(xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].isAdd ==1){
						// 	//加分项
						// 	xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore= childNum;
				  		// 	if(xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore>indexScore){
				  		// 		xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore=indexScore;
				  		// 	}
						// } else {
						// 	//减分项
				  		// 	xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore=
			  			// 	Math.round((xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].indexscore-childNum)*100)/100;
				  		//  	if( xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore<0){
				  		//  	 	xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore =0;
				  		//  	}
						// }
						// 总分计算
						var totalScore = 0;
						var childScore = 0;
						var denominatorScore = 0;
			 			var firstScore = xzcfVue.scoringIndexList.expertHandlIndexScoreList;
						var bonus = 0;
						// 扣分项
						var deduction = 0;
						if(firstScore != null&&firstScore.length>0) {
							for(var i = 0; i < firstScore.length; i++) {
								if(!isNaN(parseFloat(firstScore[i].resultScore))&& firstScore[i].indexname!='加分项' && firstScore[i].isOperator!='0') {
									childScore += firstScore[i].resultScore;
								}
								if(firstScore[i].indexname!='加分项' && (this.scoringIndexList.closed==0? firstScore[i].indexname != '结案审批表':true) && firstScore[i].isOperator!='0') {
									denominatorScore += firstScore[i].indexscore;
								}
								if(firstScore[i].indexname=='加分项'){
									bonus = firstScore[i].resultScore;
								}
								if (firstScore[i].isOperator=='0'){
									deduction += firstScore[i].resultScore;
								}
							}
						}
						if (denominatorScore == 0){
							totalScore = 0.00 + bonus +deduction
						} else {
							var childCountScore = childScore+bonus;
							totalScore = 50 * (childCountScore / denominatorScore)+deduction;
						}
						if (totalScore<0){
							totalScore = 0;
						}
						xzcfVue.scoringIndexList.paperscore = totalScore.toFixed(2);
				  		} else {
				  			$("#expertItem"+itemIndex+index).removeClass("has-success");
							$("#expertItem"+itemIndex+index).addClass("has-error");
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorFlag= false;
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorMessage="分值项不能为空，且在0-"+itemScore+"分之间，且最多精确到小数点后两位小数！";
					   		return false;
				  		}
					} else {
				  		$("#expertItem"+itemIndex+index).removeClass("has-success");
						$("#expertItem"+itemIndex+index).addClass("has-error");
						xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorFlag= false;
						xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorMessage="分值项不能为空，且在0-"+itemScore+"分之间，且最多精确到小数点后两位小数！";
				   		return false;
					}
				},
				updateCommon:function (index){
					var flag=  xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].voteDownValue;
					var text = $("#comment"+index).val().replace(/\s+/g,"");
					var indexname = xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].indexname;
					if(indexname == "责令改正违法行为决定书") {
						var itemScores = xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList;
						var item1 = itemScores[itemScores.length - 1].score;
						var item2 =  itemScores[itemScores.length - 2].score;
						if(item1 == 1 || item2 == 1) {
							flag = true;
						}
					}
					if(flag){
					   	if(text!=null && text!=''){
					 		$("#comment"+index).removeClass("textarea-error");
					  		$("#comment"+index).addClass("textarea-success");
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].validatorFlag= true;
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].validatorMessage="";
						}
					   	if(text==null || text==''){
				 			$("#comment"+index).removeClass("textarea-success");
					  		$("#comment"+index).addClass("textarea-error");
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].validatorFlag= false;
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].validatorMessage="此项评审依据必填";
						}
					}
				},
				saveTime:function(){
					if(this.scoringIndexList.scoredstate == 0 || this.scoringIndexList.scoredstate == 0){
						this.saveSubmit(this.scoringIndexList.id,'0');//暂存
					}else{
						this.saveSubmit(this.scoringIndexList.id,'1');//保存
					}
				},
				saveSubmit:function(id, status){// 0.暂存  1.保存

					/*逐项验证vue对象中的文本框*/
					if(xzcfVue.scoringIndexList != null) {
						var data = xzcfVue.scoringIndexList.expertHandlIndexScoreList;//一级指标
						for(var i = 0; i < data.length; i++){
							//一票否决
							if(data[i].voteDownValue != 2) {
								if((data[i].voteDownValue != 0 || data[i].voteDownValue != 1) && data[i].voteDownValue) {
									data[i].voteDownValue = 1;
								} else {
									data[i].voteDownValue = 0;
								}
							}

							//无需评查
							if((data[i].inCheckValue != 0 || data[i].inCheckValue != 1) && data[i].inCheckValue) {
								data[i].inCheckValue = 1;
							}else{
								data[i].inCheckValue = 0;
							}

							//评审依据的验证
							if(status == "1") {//保存的时候必验
								var flag=  data[i].voteDownValue;
							    var text = $("#comment"+i).val().replace(/\s+/g,"");
								//分
								var resultScore =  data[i].resultScore;
								//一票否决
								var inCheckValue =  data[i].inCheckValue;
								var isOperator =  data[i].isOperator;


								//扣分项扣分时,依据必填
								if (isOperator == '0' && resultScore!=0){
									if(text == null || text == ''){
										$("#comment"+i).removeClass("textarea-success");
										$("#comment"+i).addClass("textarea-error");
										$("#comment"+i).focus();
										data[i].validatorFlag= false;
										data[i].validatorMessage="此项评审依据必填";
										return false;
									}
								}

								if(resultScore!=null && resultScore ==0 && inCheckValue!=1 && data[i].isOperator!=0 && (this.scoringIndexList.closed===0? data[i].indexname !== "结案审批表":true)){
									if(text == null || text == ''){
										$("#comment"+i).removeClass("textarea-success");
										$("#comment"+i).addClass("textarea-error");
										$("#comment"+i).focus();
										data[i].validatorFlag= false;
										data[i].validatorMessage="此项评审依据必填";
										return false;
									}
								}else{
									$("#comment"+i).removeClass("textarea-error");
									data[i].validatorMessage="";
								}
							    if(flag) {
							    	if(text == null || text == ''){
							  			$("#comment"+i).removeClass("textarea-success");
								  		$("#comment"+i).addClass("textarea-error");
								  		$("#comment"+i).focus();
								  		data[i].validatorFlag= false;
								  		data[i].validatorMessage="此项评审依据必填";
								  		return false;
						  			}
							    }
							}

							//一级指标一票否决或无需评查，则跳出循环，无需校验二级指标
							if(data[i].voteDownValue == 2 || data[i].voteDownValue == 1 || data[i].inCheckValue == 1){continue;}

							/*二级指标验证*/
							if(data[i].expertHandlItemScoreList != null){
								for(var j=0;j<data[i].expertHandlItemScoreList.length;j++){
									var standScore = data[i].expertHandlItemScoreList[j].itemscore//标准分
									var score=data[i].expertHandlItemScoreList[j].score;//输入分

									/*
									*无论暂存或是保存，都需要验证的是格式
									*/
									if(score != null && score!=""){
										var re = /^-?\d+\.?\d{0,2}$/;
										if(standScore!=0&&(score>standScore || !(re.test(score)) )){
											xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].validatorFlag= false;
											xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].validatorMessage="分值项不能为空，且在0-"+xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].itemscore+"分之间，且最多精确到小数点后两位小数！";
											$("#expertItem"+xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].temItemId+""+i).removeClass("has-success");
											$("#expertItem"+xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].temItemId+""+i).addClass("has-error");
											$("#"+i+"scoreInput"+j).focus();
											return false;
										}
										if(score.toString().indexOf('-') !=-1){
											xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].validatorFlag= false;
											xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].validatorMessage="分值项不能为负数";
											$("#expertItem"+xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].temItemId+""+i).removeClass("has-success");
											$("#expertItem"+xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].temItemId+""+i).addClass("has-error");
											$("#"+i+"scoreInput"+j).focus();
											return false;
										}
									}

									/*
									*【保存】的时候验证必填项
									*/
									if(status == "1") {
										if(standScore!=0){//标准分不是0的是必填项
											if((score == null || score==="")){
												xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].validatorFlag= false;
												xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].validatorMessage="分值项不能为空，且在0-"+xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].itemscore+"分之间，且最多精确到小数点后两位小数！";
												$("#expertItem"+xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].temItemId+""+i).removeClass("has-success");
												$("#expertItem"+xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].temItemId+""+i).addClass("has-error");
												$("#"+i+"scoreInput"+j).focus();
												return false;
											}
										}else{//此操作是为了后台验证通过
											xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].validatorFlag= true;
											xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].validatorMessage="";
										}
									}

								}
							}//二级指标验证结束

						}//一级指标验证结束

					}//vue对象验证结束
					//存入优秀案卷code
					var code_array=new Array();
					$('input[name="yxaj"]:checked').each(function(){
						code_array.push($(this).val())
					});
					xzcfVue.scoringIndexList.ajpjYxdxanlituijianCode = code_array.join(',')

					//存入较差案卷code
					var jcajArr=new Array();
					$('input[name="jcaj"]:checked').each(function(){
						jcajArr.push($(this).val())
					});
					xzcfVue.scoringIndexList.jcajtuijianCode = jcajArr.join(',')

					/*开始提交保存*/
					$("#submitBtn").attr("disabled", "disabled");
					$("#submitTemporaryBtn").attr("disabled", "disabled");
			  		$("#submitBtn").text("提交中...");
			  		$.ajax({
			  			type:"post",
						url:WEBPATH+'/zjpf/saveExpertScore.do',
						data:{
							id:id,
							status:status,
							scoringIndex:JSON.stringify(xzcfVue.scoringIndexList)
						},
						success:function(data){  //成功
							if (data.result=="success") {
								swal({title: "保存成功",text: "",type:"success"});
								$("#submitBtn").attr("disabled", false);
								$("#submitTemporaryBtn").attr("disabled", false);
								$("#submitBtn").text("保存信息");
								if(status == "1") {
									business.addMainContentParserHtml('zjpf/entityCommitScore.do?id=${expertFileId}','pageNum=${pageNum}');
								}else{
									business.addMainContentParserHtml('zjpf/zjpfCommiteeScore.do?id='+id,'pageNum=${pageNum}');
								}
								return false;
							} else {
								if(data.code=="007"){
									swal("提示", "正在保存中……，请稍等片刻", "info");
								}else if(data.code=="000"){//登录信息失效
									swal({
								        title: "提示",
								        text: data.message,
								        type: "error",
								        confirmButtonText: "确定",
								        confirmButtonColor: "#ec6c62"
								    }, function() {
								        window.location.href=WEBPATH+"/index.do";
								    });
								}
								$("#submitBtn").attr("disabled", false);
								$("#submitTemporaryBtn").attr("disabled", false);
								$("#submitBtn").text("保存信息");
								return false;
							}
						}
			  		});

				}

		}//vue对象中method结束

	});//vue对象结束

	/*点击案卷严重不全*/
	function changeFileMaterials(){
		if ($("#fileMaterials").prop("checked")) {
			xzcfVue.scoringIndexList.entityscore =0;
			xzcfVue.scoringIndexList.fileMaterials = '1';
			//案卷严重不足的说明情况
			<%--document.getElementById("mySerious").value = '${expertHandlFileList.fileMaterialsMsg}';--%>
			// checkInput();
			$( "#my_dialo" ).dialog( "open" );
		}else {
			xzcfVue.sumScore();
			xzcfVue.scoringIndexList.fileMaterials = '0';
		}
	}
	$('#my_dialo').dialog({
		width : "900",
		height : "450",
		autoOpen : false,
		resizable : false,
		modal : true,
		closed: true
	});
	/*严重不全弹窗的取消按钮*/
	function Cancel() {
		$('#my_dialo').dialog("close");
		$('#fileMaterials').prop("checked",false);//取消勾选
		// checkInput();
	}


	//处理浮点型数据相减
	function handFloat(biaozhunfen,subScore){
		var r1,r2,m,n;
		try{r1=biaozhunfen.toString().split(".")[1].length}catch(e){r1=0};
		try{r2=subScore.toString().split(".")[1].length}catch(e){r2=0};
		m=Math.pow(10,Math.max(r1,r2));
		n = (r1 >= r2) ? r1 : r2;
		return ((biaozhunfen * m - subScore * m) / m).toFixed(n);
	}

	var a = xzcfVue.scoringIndexList.yxdxanlituijian;
	if(xzcfVue.scoringIndexList.yxdxanlituijian=="1"){
		//$('#yxdxanlituijian').attr('checked', true)

	     $("[name = yxdxanlituijian]:checkbox").attr("checked", true);
		$("#yxdxAnLiTuiJianReviews1").css("display","block");

		//优秀案卷选项回显
		var checkArray = $("input[name='yxaj']");
		var codes = xzcfVue.scoringIndexList.ajpjYxdxanlituijianCode;
		if(codes != null && codes != "" ){
			var myArray=codes.split(",");
			for (var i = 0; i < myArray.length; i++) {
				//获取所有复选框对象的value属性，然后，用checkArray[i]和他们匹配，如果有，则说明他应被选中
				$.each(checkArray, function (j, checkbox) {
					//获取复选框的value属性
					var checkValue=$(checkbox).val();
					console.log(j+"----"+checkValue)
					if (myArray[i] == checkValue) {
						$(checkbox).attr("checked", true);
					}
				})
			}
		}


		//禁止反面推荐
		$("#ajpjYxdxanlituijian").attr("disabled","disabled");
		$("#ajpjYxdxAnLiTuiJianReviews1").css("display", "none");
		//禁止不推荐为典型案例
		$("#noTuiJian").attr("checked", false);
	}

	var ajpja = xzcfVue.scoringIndexList.ajpjYxdxanlituijian;
	if(xzcfVue.scoringIndexList.ajpjYxdxanlituijian=="1"){
		//$('#ajpjYxdxanlituijian').attr('checked', true)

	    $("[name = ajpjYxdxanlituijian]:checkbox").attr("checked", true);
		$("#ajpjYxdxAnLiTuiJianReviews1").css("display","block");

		//较差案卷选项回显
		var checkArray = $("input[name='jcaj']");
		var codes = xzcfVue.scoringIndexList.jcajtuijianCode;
		if(codes != null && codes != "" ){
			var myArray=codes.split(",");
			for (var i = 0; i < myArray.length; i++) {
				//获取所有复选框对象的value属性，然后，用checkArray[i]和他们匹配，如果有，则说明他应被选中
				$.each(checkArray, function (j, checkbox) {
					//获取复选框的value属性
					var checkValue=$(checkbox).val();
					console.log(j+"----"+checkValue)
					if (myArray[i] == checkValue) {
						$(checkbox).attr("checked", true);
					}
				})
			}
		}


		//禁止优秀推荐
		$("#yxdxanlituijian").attr("disabled","disabled");
		$("#yxdxAnLiTuiJianReviews1").css("display", "none");
		//禁止不推荐为典型案例
		$("#noTuiJian").attr("checked", false);
	}
	// 不推荐为典型案例 回显
	if(xzcfVue.scoringIndexList.noTuiJian=="1"){
		$("#noTuiJian").attr("checked", true);
		//禁止优秀推荐
		$("#yxdxanlituijian").attr("disabled","disabled");
		$("#yxdxAnLiTuiJianReviews1").css("display", "none");
		//禁止较差推荐
		$("#ajpjYxdxanlituijian").attr("disabled","disabled");
		$("#ajpjYxdxAnLiTuiJianReviews1").css("display", "none");
	}

	function trim(str) {
		return str.replace(/(^\s+)|(\s+$)/g, "");
	}

	//下载文件
	function xiaZaiAnJuan(){
	  var fileId = $("#fileId").val();
	  $.ajax({
		    type:"post",
		    url:WEBPATH+'/zjpf/xzcfExistFileUrl.do',
		    data:{fileId:fileId},           //注意数据用{}
		    success:function(data){  //成功
			 	if("yes" == data){
					window.location.href="${pageContext.request.contextPath}/zjpf/zjpfAnJuandown.do?fileId="+fileId;
				    return false;
		         }else if("no" == data){
		            	  swal( "操作失败","该案卷不存在!", "error");
		            	  return false;
				}else if("suffixerror" ==data){
					  swal( "操作失败","该案卷上传数据格式有问题!", "error");
	            	  return false;
				}
		    }
	    });
	}

	$(document).ready(function(){
		var scoredstate = ${expertHandlFileList.scoredstate};
		if(scoredstate==1){
			$("input[name='scoreInput']").removeAttr("disabled");

			$("#zjpy").removeAttr("disabled");
			$("#ajpj").removeAttr("disabled");
			$("#submitBtn").removeAttr("style display");
			$("#fileCodeInput").attr("disabled","disabled");
			$("textarea").removeAttr("disabled");
			$("#fileCodeInput").val('${ expertHandlFileList.filecode}');
			$("#yxdxanlituijian").attr("disabled",false);
			$("#ajpjYxdxanlituijian").attr("disabled",false);
			/* 页面初始化加载设置隐藏 input输入框，当用户选择一票否决和无须评查  */
			if(xzcfVue.scoringIndexList != null && xzcfVue.scoringIndexList.expertHandlIndexScoreList != null ){
				for( var i=0;i< xzcfVue.scoringIndexList.expertHandlIndexScoreList.length;i++){
					if(xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].inCheckValue ==1 ||xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].voteDownValue ==1){
						var checkitem =".checkitem"+i;
						$(checkitem).attr("disabled","disabled");
						if(xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].inCheckValue ==1){
							//如果选了无须评查
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].resultScore=xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].resultScore;
							var isVoteDownid = '#isVoteDown'+i;
							$(isVoteDownid).attr("disabled","disabled");
						}
						if(xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].voteDownValue ==1){
							//若果选了一票否决
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].resultScore=0;
							var isInCheckid = '#isInCheck'+i;
							$(isInCheckid).attr("disabled","disabled");
						}
					}
				}
			}
		}else{
			//$("#look").attr("disabled","disalbed");
		}


		$("#problem_remarkA").attr("disabled","disabled")
		$("#problem_remarkB").attr("disabled","disabled")
	});


	 function loding(btn,itext){
		document.getElementById(btn).innerHTML = "加载.."
		document.getElementById(btn).disabled = "disabled"
	    setTimeout(function () {
			document.getElementById(btn).innerHTML = itext;
			document.getElementById(btn).removeAttribute("disabled");
	    },3000);
	 }
	 //零分案卷理由返回
	 /* function bcakList(){
		 $("#zeroScoreFile").modal("hide");
		 $('.modal-backdrop').remove();
		 business.addMainContentParserHtml('zjpf/zjpfList.do','pageNum=${pageNum}');
	 } */
	//系统初始化函数
	$(function(){
		/*查看争议异常的弹窗*/
		$('#error_dialog_score_commit').dialog({
			title: "查看争议",
			width : "900",
			height : "450",
			autoOpen : false,
			resizable : false,
			modal : true,
			closed: true
		});
		// 隐藏dialog右上角叉号关闭按钮
		$('.panel-tool-close').hide();
	});
	//关闭查看异常的窗口
	function commit_error_close(){
		var isAOrB = $("#commit_isAOrB").val();
		var id = $("#commit_error_id").val();
		$.ajax({
			type: "post",
			url: WEBPATH + '/zjpf/saveErrorIsAgree.do',
			data: {
				isAgree: 1,
				isAOrB: isAOrB,
				id: id
			},
			success: function (data) {
				console.log(data)

			}
		}).always(function() {
			$('#error_dialog_score_commit').dialog("close")
			//清空专家否决选项
			$('#commit_itemOption').val('');
			//清空专家否决描述
			$('#commit_itemComme').val('');
			$('#commit_isAOrB').val('');
			$('#commit_error_id').val('');
		});

	}

</script>
<body>
<%--查看异常信息--%>
<div id="error_dialog_score_commit" style="height: 450px; display: flex; flex-direction: column; justify-content: space-between;">
	<input type="hidden" id="commit_error_id">
	<input type="hidden" id="commit_isAOrB">
	<span class="glyphicon glyphicon-info-sign" aria-hidden="true" style="text-align: center;font-size: 50px;color: #337ab7;"></span>
	<div style="width: 85%;padding-left: 20%;; font-size: 20px">
		<span style="font-weight: bold; font-size: 20px">该项对方专家</span>
		<span style="font-size: 20px;color: #337ab7" id="commit_itemOption">xxx</span>
		<span style="font-weight: bold; font-size: 20px">，评审依据为：</span>
		<span style="font-size: 20px;color: #337ab7" id="commit_itemComme">xxx</span>

	</div>

	<div style="text-align: center; padding: 20px;">
		<button id="commit_error_close" class="my-btn-gray"  OnClick="commit_error_close()" style="background: #ECF5FF; margin-right: 50px;margin-top: 10px; outline: none; border: 1px solid #C6E2FF;border-radius: 3px;font-size: 18px;color:#409EFF; width: 80px; height: 40px">确认</button>
	</div>
</div>

	<div id="CommitVue">
		<div class="center_weizhi">当前位置：专家评分 - 委员评审案卷 - ${filetypeName}</div>
		<div style="position: absolute;top: 127px;width: 98%;left:0;margin: 0 20px;right: 5px;padding: 5px 0 0 19px;background-color: #ffffff">
			<button class="btn btn-danger" style='margin-top:10px;padding:9px 34px;margin-right:14px'  >卷面评查</button>
			<c:choose>
				<c:when test ="${expertHandlFileList.scoredstate == 0 or expertHandlFileList.scoredstate == 2 or expertHandlFileList.scoredstate == 7}">
					<button disabled class="btn btn-default" style='margin-top:10px;padding:9px 34px'>实体和程序评查</button>
				</c:when>
				<c:otherwise>
					<button class="btn btn-default" style='margin-top:10px;padding:9px 34px' onclick="macroMgr.onLevelTwoMenuClick(null, 'zjpf/entityCommitScore.do?id=${expertFileId}&pageNum=${pageNum}')">实体和程序评查</button>
				</c:otherwise>
			</c:choose>
			<button class="btn btn-primary"  style="font-size:16px;margin-top:10px;width:98px;height:40px;  padding:6px 34px;margin-right: 10px;float: right;"  onclick="macroMgr.onLevelOneMenuClick(null, 'zjpf.do')">返回</button>
<%--			<div>--%>
				<div style="color:#333333;font-size:16px; font-weight:600;margin-top:17px">案卷或材料：</div>
				<span style="color:#009CCD;font-size:14px; font-weight:400;" id ="filecode">
						{{scoringIndexList.filecode }} <span style="color:red;font-size:16px; font-weight:bold;"><c:choose><c:when test="${expertHandlFileList.closed==1}">（已结案）</c:when><c:when test="${expertHandlFileList.closed==0}">（未结案）</c:when> </c:choose></span>
						</span>
				<c:if test ="${sessionScope.sa_session.sysStatus == '5' }">
					<a v-if="scoringIndexList.fileurl != null && scoringIndexList.fileurl != ''" v-bind:href="scoringIndexList.downUrl" target="_Blank">
						<button id="download" class="btn btn-success btn-xs" data-toggle="modal" style="padding:5px 12px;margin-left:10px;" data-target="#myModal" style="margin: 0 10px">下载</button>
					</a>
					<span v-else style="color:red;font-size:14px; font-weight:400;">（无案卷文件）</span>
					<c:if test="${expertHandlFileList.suffix== 'pdf'}">
						<button id="look" class="btn btn-info btn-xs" data-toggle="modal" style="padding:5px 12px;margin-left:10px;margin-right:10px;" data-remote="${webpath}/jcpf/showFileModal.do?fastDFSUrl=+'${fastDFS}'+" data-target="#view" style="margin: 0 10px">预览</button>
					</c:if>
				</c:if>
				<select id="indexName" onchange="change(this.value)" style="margin-top: 10px" ></select>
				<%-- <c:if test ="${expertHandlFileList.scoredstate == 0}">
                    <button class="btn btn-info btn-xs" data-toggle="modal" data-target="#zeroScoreFile" id ="chickAnjuan">零分案卷</button>
                </c:if> --%>
<%--			</div>--%>
		</div>

		<div class="center" id="scrollBarCenter">
			<div class="center_list">
				<input id="pageNum"  type="hidden" value="${pageNum}" >
		 		<input id="expertFileId"  type="hidden" value="${expertFileId}" >
		 		<input id="fileId"  type="hidden" value="${expertHandlFileList.fileid}" >
				<input id="filesId" type="hidden" value="${filesId}">
				<input id="expertId"  type="hidden" value="${expertHandlFileList.expertId}" >
				<input id="handlType"  type="hidden" value="${expertHandlFileList.handlType}" >
		 		<div class="dingwei" style="padding:0px 5px 15px 0px">
					<div style="font-weight: 400; font-size: 14px">
						<p style="font-weight: 600; font-size: 16px">使用说明：</p>
						<p>1.本标准用于评查案卷卷面内容。</p>
						<p>2.可以根据证据类型和所发文书种类确定评查项目。</p>
						<p>3.卷面分=50*对应评查项目得分之和/参与评查项目标准分之和。</p>
						<p>4.内容完整、规范、正确的，得相应分值。不完整、不规范或者不正确的，不得分。</p>
						<p class="ceshi">5.提示：无需评查是指根据案情需要，无需对此项文书进行评查！</p>
<%--					<div class="dingwei">--%>
<%--                        <span style="font-weight: 400; font-size: 14px">案卷或材料：</span>--%>
<%--						<span style="color:#009CCDFF;font-size:14px; font-weight:400;" id ="filecode">--%>
<%--						{{scoringIndexList.filecode }} <span style="color:red;font-size:16px; font-weight:bold;"><c:choose><c:when test="${expertHandlFileList.closed==1}">（已结案）</c:when><c:when test="${expertHandlFileList.closed==0}">（未结案）</c:when> </c:choose></span>--%>
<%--						</span>--%>
<%--					<c:if test ="${sessionScope.sa_session.sysStatus == '5' }">	 --%>
<%--						<a v-if="scoringIndexList.fileurl != null && scoringIndexList.fileurl != ''" v-bind:href="scoringIndexList.downUrl" target="_Blank">--%>
<%--							<button id="download" class="btn btn-success btn-xs" data-toggle="modal" style="padding:5px 12px;margin-left:10px;" data-target="#myModal" style="margin: 0 10px">下载</button>--%>
<%--						</a>--%>
<%--						<span v-else style="color:red;font-size:16px; font-weight:bold;">（无案卷文件）</span>--%>
<%--						<c:if test="${expertHandlFileList.suffix== 'pdf'}">--%>
<%--							<button id="look" class="btn btn-info btn-xs" data-toggle="modal" style="padding:5px 12px;margin-left:10px;margin-right:10px;" data-remote="${webpath}/jcpf/showFileModal.do?fastDFSUrl=+'${fastDFS}'+" data-target="#view" style="margin: 0 10px">预览</button>--%>
<%--						</c:if>--%>
<%--					</c:if>--%>
<%--						<select id="indexName" onchange="change(this.value)" style="margin-top: 10px" ></select>--%>
<%--					&lt;%&ndash; <c:if test ="${expertHandlFileList.scoredstate == 0}">--%>
<%--						<button class="btn btn-info btn-xs" data-toggle="modal" data-target="#zeroScoreFile" id ="chickAnjuan">零分案卷</button>--%>
<%--					</c:if> &ndash;%&gt;--%>
					</div>

				</div>

		       	<form action="#" id ="zjpfForm" method="post">
		        	<table class="table table-bordered table-hover table-condensed">
						<thead>
							<tr>
								<td width="3%" height="30" bgcolor="#efefef">序号</td>
								<td width="7%" bgcolor="#efefef">评查项目</td>
								<td width="7%" bgcolor="#efefef">标准分</td>
								<!-- <td width="44%" bgcolor="#efefef"><span style="float: left;">评分细则</span><span style="float: right;margin-right: 70px;">得分值</span></td> -->
								 <td bgcolor="#efefef" style="text-align: center;">评分细则</td>
								 <td width="70px" bgcolor="#efefef">专家A评分值</td>
					             <td width="70px" bgcolor="#efefef">专家A评审依据</td>
					             <td width="70px" bgcolor="#efefef">专家B评分值</td>
					             <td width="70px" bgcolor="#efefef">专家B评审依据</td>
					             <td width="155px" bgcolor="#efefef">打分值</td>
								<td width="5%" bgcolor="#efefef">一票否决</td>
								<td width="5%" bgcolor="#efefef">无需评查</td>
								<td width="5%" bgcolor="#efefef">得分</td>
								<td width="15%" bgcolor="#efefef">评审依据</td>
							</tr>
						</thead>
						<tbody  class="form-group">
							<tr v-for="(scoringIndex, index) in scoringIndexList.expertHandlIndexScoreList" :class="scoringIndex.className" :style="scoringIndex.isError==1?'background:#ECD9E0':''">
								<td height="30" align="center" >{{index+1}}</td>

								<!-- 评查项目 -->
								<td :class="scoringIndex.className">
									{{scoringIndex.indexname}}
									<span v-if="scoringIndex.isOperator=='0'" style="color:red;">（扣分项）</span>
									<p v-if="scoringIndex.isError==1 && scoringIndex.isAgree==null" style="margin:0 auto;width: 67px;height: 24px;background: #DF1912;border-radius: 3px;">
										<a href="#" style="line-height: 24px;font-size:14px;font-weight: 400;color: #ffffff" v-on:click="showErrorMsg(scoringIndex)">存在争议</a>
									</p>
									<p v-if="scoringIndex.isError==1 && scoringIndex.isAgree!=null" style="margin:0 auto;width: 67px;line-height:24px;height: 24px;background: rgba(129,194,105,1);border-radius: 3px;" >
										<a href="#" style="font-size:14px;font-weight: 400;color: #ffffff"  v-on:click="showErrorMsg(scoringIndex)">查看争议</a>
									</p>
									<%--<p v-if="scoringIndex.isError==1" style="width: 67px;height: 24px;background: #DF1912;border-radius: 3px;text-align: center;">
										<a href="#" style="line-height: 22px;font-size:14px;font-weight: 400;color: #ffffff"  v-on:click="showErrorMsg(scoringIndex)">存在争议</a>
									</p>--%>
								</td>

								<!-- 标准分 -->
								<td style="vertical-align:middle;">{{scoringIndex.indexscore}}</td>

								<!-- 评分细则 -->
								<td colspan="6" v-if="scoringIndex.expertHandlItemScoreList != null && scoringIndex.expertHandlItemScoreList.length !=0" style="padding:5px;">
									<table width="100%" border="0" cellspacing="0" cellpadding="0" style="padding:0;">
										<tr v-for="(scoringItem, index1) in scoringIndex.expertHandlItemScoreList">
											<td style="border-bottom:solid 1px #ddd; border-right:solid 1px #ddd;vertical-align:middle;">
												<div style="vertical-align:middle; margin:0 5px 5px 0;">{{scoringItem.itemname}}</div>
											</td>

											<td style="width:70px; border-bottom:solid 1px #ddd; border-right:solid 1px #ddd; vertical-align:middle;align:center;">{{scoringItem.aexpertScore}}</td>
											<td :rowspan="scoringIndex.expertHandlItemScoreList.length" v-if="index1==0" style="width:70px; border-bottom:solid 1px #ddd; border-right:solid 1px #ddd; vertical-align:middle;">{{scoringIndex.aexpertReason}}</td>
                            				<td style="width:70px; border-bottom:solid 1px #ddd; border-right:solid 1px #ddd; vertical-align:middle;align:center;">{{scoringItem.bexpertScore}}</td>
                            				<td :rowspan="scoringIndex.expertHandlItemScoreList.length" v-if="index1==0" style="width:70px; border-bottom:solid 1px #ddd; border-right:solid 1px #ddd; vertical-align:middle;">{{scoringIndex.bexpertReason}}</td>

											<td style="border-bottom:solid 1px #ddd; width:155px;vertical-align:middle;">
												<div v-if="scoringItem.itemscore == 0" class="custom-checkbox text-center" >
													<input name="scoreInput" type="checkbox" :id="index+'scoreInput'+index1"
														@change="changeCheckBoxNegative(index, index1)" v-model="scoringItem.score"
														style="width:16px; height:16px; display: table-cell;vertical-align: middle; text-align: center;">
													<label :for="'checkbox'+index" class="checkbox-blue" checked></label>
												</div>
												<div v-else :id="'expertItem'+index1+index" style="margin:0 0 5px 0; float:left;">
													<div v-if="scoringItem.isOperator==1" style="float:left; padding:5px 2px 7px 3px; color:red; font-size:20px;">＋</div>
													<div v-if="scoringItem.isOperator!=1" style="float:left; padding:0 0 3px 0; margin:0; color:red; font-size:26px;">－</div>

													<div v-if="scoringIndex.voteDownValue == 1 || scoringIndex.voteDownValue == 2 || scoringIndex.inCheckValue == 1|| (scoringIndexList.closed == 0 ? scoringIndex.indexname == '结案审批表' : false)"
														style="float:right; padding-top:4px;">
														<input disabled name="scoreInput" @input="updateItem(index,index1,scoringItem.itemscore,scoringItem.temItemId)"
															v-model="scoringItem.score" type="text" :class="'checkitem'+index" autocomplete="off"
															class="form-control inputDisabled"
															   :placeholder="scoringIndex.isOperator === '0' ? '请填写扣除分数' : '请输入复评得分'"
															   style="width:125px;">
													</div>
													<div v-else style="float:right; padding-top:4px;">
														<input name="scoreInput" @input="updateItem(index,index1,scoringItem.itemscore,scoringItem.temItemId)"
															:id="index+'scoreInput'+index1" v-model="scoringItem.score" type="text" :class="'checkitem'+index" autocomplete="off"
															class="form-control inputDisabled"
															   :placeholder="scoringIndex.isOperator === '0' ? '请填写扣除分数' : '请输入复评得分'"
															   style="width:125px;">
													</div>

													<div style="color:red; font-size: 12px;">{{scoringItem.validatorMessage}}</div>
												</div>
											</td>
										</tr>
									</table>
								</td>

			             		<!-- 一票否决 -->
								<td style="vertical-align:middle;">
									<div class="custom-checkbox text-center" v-if="scoringIndex.isVoteDown == 1 && (scoringIndexList.closed == 0 ? scoringIndex.indexname != '结案审批表' : true)" >
										<input v-if="scoringIndex.inCheckValue != 1" name="scoreInput" type="checkbox" :id="'isVoteDown'+index"
											@change="changeCheckBoxCli(index,'1')" v-model="scoringIndex.voteDownValue"
											style="margin-left:18px;width:16px; height:16px; display: table-cell;vertical-align: middle; text-align: center;">
										<input v-else name="scoreInput" type="checkbox" :id="'isVoteDown'+index"
											disabled v-model="scoringIndex.voteDownValue"
											style="margin-left:18px;width:16px; height:16px; display: table-cell;vertical-align: middle; text-align: center;">
										<label :for="'checkbox'+index" class="checkbox-blue" checked>一票否决</label>
									</div>
								</td>

								<!-- 无需评查 -->
								<td style="vertical-align:middle;">
									<div class="custom-checkbox text-center" v-if="scoringIndex.isInCheck ==1">
										<input v-if="scoringIndex.voteDownValue != 1" name="scoreInput" type="checkbox" :id="'isInCheck'+index"
		 									@change="changeCheckBoxCli(index,'2')"  v-model="scoringIndex.inCheckValue"
											style="margin-left:18px;width:16px; height:16px;  display: table-cell;vertical-align: middle; text-align: center; ">
										<input v-else name="scoreInput" type="checkbox" :id="'isInCheck'+index"
		 									disabled  v-model="scoringIndex.inCheckValue"
											style="margin-left:18px;width:16px; height:16px;  display: table-cell;vertical-align: middle; text-align: center; ">
										<label for="checkbox1" class="checkbox-blue" checked>无需评查</label>
									</div>
								</td>

								<!-- 得分 -->
								<td style="vertical-align:middle;">
									<div style="width:120px;float: right;">
					           			<input type="text"  disabled="disabled" v-model="scoringIndex.resultScore"  class="form-control">
					           		</div>
								</td>

								<!-- 评审依据 -->
								<td >
									<textarea maxlength="1000" @input="updateCommon(index)" :id="'comment'+index" :rows="scoringIndex.expertHandlItemScoreList.length"
										class="form-control" v-model="scoringIndex.comment" placeholder="请输入评审依据，长度不能超过1000个字符" style="width:100%;">
									</textarea>
									<div style="color:red; font-size: 12px;">{{scoringIndex.validatorMessage}}</div>
								</td>
							</tr>

							<tr>
								<td rowspan="2"></td>
								<td rowspan="2"><strong>合计</strong></td>
								<td colspan="7"><strong>参与评查得分</strong></td>
								<td colspan="2"><strong>参与评查标准分</strong></td>
								<td colspan="2"><strong>卷面分</strong></td>
							</tr>
							<tr>
								<td colspan="7">{{inputScore}}</td>
								<td colspan="2">{{totalScore}}</td>
								<td colspan="2">
									<div style="width:120px;float: left;">
					           			<input type="text" disabled v-model="scoringIndexList.paperscore" class="form-control">
					           		</div>
								</td>
							</tr>

						</tbody>
					</table>
				</form>
				<%--严重不全--%>
				<tr>
					<td height="30" colspan="9">
						<div  class="col-sm-12" style="padding-left: 3px;">
							<input id="fileMaterials" name="fileMaterials" type="checkbox" onchange="changeFileMaterials()"><span style="font-size: 14px; color:DF1912">&nbsp;案卷材料严重不全，导致无法全面评价案卷实体和程序问题</span></div>
						<div class="col-sm-12" style="padding-left: 3px;line-height: 40px;">
							<h4>具体情形：</h4>
							<p>案卷中只有三项或不足三项，无相关决定性文书，导致无法全面评价案卷规范性、合法性。</p>

						</div>
					</td>
				</tr>
				<%--严重不全的弹窗--%>
				<div id="my_dialo" >
					<span style="font-size: 16px;color: #ffffff; display: inline-block;  visibility: hidden;letter-spacing: 1.5px;">空格</span>
					<span style="font-size: 16px;color:#333333;letter-spacing: 1.5px;">  案卷共</span>
					<input class="noline-input" type="text" id="fileMaterialsNums" placeholder="请输入总页数"style="font-size: 16px" value="${expertHandlFileList.fileMaterialsNums}">
					<span style="font-size: 16px;color:#333333;letter-spacing: 1.5px;">页，</span>
					<span style="font-size: 16px;color:#333333;letter-spacing: 1.5px;">只有</span>
					<input class="noline-input"  maxlength="200" id="fileMaterialsDocs" placeholder="请填写案卷中存在的文书"  style="width: 400px;height: 25px;;font-size: 16px"></input>
					<span style="margin-bottom: 10px"></span>
					<span style="font-size: 16px;color:#333333;letter-spacing: 1.5px;">文书，</br>
						导致无法全面评价案卷和实体程序问题。</span> </br>
					<span style="display: block; margin-bottom: 20px"></span>
					<span style="font-size: 16px;color:#333333;letter-spacing: 1.5px;">其他情况补充：</span></br>
					<textarea id="fileMaterialsSups" placeholder="其他情况补充,仅限2000字"  maxlength="2000" style="width: 600px;height: 125px;font-size: 16px;margin-top: 5px"></textarea></br>
					<%--<p id="warningMessage" style="color: red">最大字符200字</p>--%>
					<span style="font-size: 16px;color:#333333;letter-spacing: 1.5px;color: red">（提示：如确认该案卷为严重不全案卷，需填写以上信息，点击确定后该案卷直接显示已评状态且不可重新评查！）</span>
					<br>
					<div style="text-align: center">
						<button  class="my-btn-gray"  OnClick="Cancel()" style="background: #ECF5FF; margin-right: 50px;margin-top: 10px; outline: none; border: 1px solid #C6E2FF;border-radius: 3px;font-size: 18px;color:#409EFF; width: 80px; height: 40px">取消</button>
						<button  class="my-btn-blue" v-on:click="validateForm(${expertHandlFileList.id})" style="background: #009CCD; margin-top: 10px;outline: none; border: none; width: 80px; height: 40px;border: 1px solid #C6E2FF;border-radius: 3px;font-size: 18px;color:#ffffff;">
							确定
						</button>
					</div>
				</div>


		 		<div class="submit">
		 			<button v-if="scoringIndexList.scoredstate == 0 || scoringIndexList.scoredstate == 2" type="submit" class="btn btn-primary" id="submitTemporaryBtn" v-on:click="saveSubmit(scoringIndexList.id, '0')" style="font-size:14px;width:126px; height:40px;color:#ffffff;padding:0;margin-top:5px;">信息暂存</button>
	 				<button type="submit" class="btn btn-danger" id="submitBtn" v-on:click="saveSubmit(scoringIndexList.id, '1')" style="font-size:14px;width:126px; height:40px;color:#ffffff;padding:0;margin-top:5px;">信息保存</button>
 				</div>
				<!--  附件查看 -->
				<div class="modal fade" id="view" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
					<div class="modal-dialog modal-lg">
						<div class="modal-content"></div>
					</div>
				</div>

				<!-- 零分案卷（Modal） -->
				<!-- <div class="modal fade" id="zeroScoreFile" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true" data-backdrop="static" data-keyboard="false">
				    <div class="modal-dialog">
				        <div class="modal-content">
				            <div class="modal-header">
				                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
				                <h4 class="modal-title" id="myModalLabel">零分案卷</h4>
				            </div>
				            <div class="modal-body form-horizontal">
				                <div class="form-group" style="padding:2px;">
				                	<label class="col-lg-3 control-label">零分案卷原因</label>
				                    <div class="col-lg-8">
				                		<textarea maxlength="1000" @input="updateZeroScoreReviews()" id="zeroScoreFileReviews" v-model="scoringIndexList.zeroScoreFileReviews"
				                			class="form-control" rows="5" placeholder="请输入零分案卷原因"></textarea>
				                    </div>
				                </div>
				                <div class="form-group">
				                	<label class="col-lg-3 control-label"></label>
				                	<div id="zeroScoreText" class="col-lg-8 hide" style="color:red; font-size: 12px;">零分案卷原因不可为空！</div>
			                	</div>
				                <div class="form-group">
				                	<label class="col-lg-3 control-label"></label>
				                	<div class="col-lg-8">
				                		<span style="color:red;">* 提示：评为零分案卷后将不可更改！且长度不能超过1000个字符！</span>
				                	</div>
			                	</div>
				            </div>

				            <div class="modal-footer">
				                <button type="button" class="btn btn-primary" v-on:click="subZeroScoreReviews(scoringIndexList.id)">保存</button>
				                <button v-if="scoringIndexList.zeroScoreFileReviews != null && scoringIndexList.zeroScoreFileReviews != ''"
				                onclick="bcakList()" type="button" class="btn btn-default">返回</button>
				                <button v-else type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
				            </div>
				        </div>
				    </div>
				</div> -->
			</div>
		</div>
	</div>
</body>
</html>
