<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
<style type="text/css">
	.pdfobject-container {
		width: 100%;
		height:580px;
		margin: 2em 0;
	}
	.textarea-success {
		border-top:green 1px solid;
		border-bottom:green 1px solid; 
		border-left:green 1px solid;
     	border-right:green 1px solid;
	}
	.textarea-error {
		border-top:red 1px solid;
		border-bottom:red 1px solid; 
		border-left:red 1px solid;
     	border-right:red 1px solid;
	}
	.center_list{background-color: #ffffff;padding-left: 19px;margin: 0;}
	.table>thead>tr>td{line-height: 3.1}
	.table-bordered>thead>tr>td{background-color: #F5F7FA;border:1px solid #EBEEF5;text-align: center}
</style>
</head>
<script type="text/javascript">
	$(".left_menu").hide();

		business.listenEnter();
		var pageNum =$("#pageNum").val();
		var scoringIndexList =null;
		var expertFileId = $("#expertFileId").val();
		$(function () {
			var mater = '${expertHandlFileList.fileMaterials}'
			if (mater == '1'){
				$("#fileMaterials").attr("checked",true);
			}
			$.ajax({
				cache : true,
				type : "GET",
				async : false,
				url: WEBPATH+"/zjpf/getCommitEntityIndexs.do",
				data:{
					id:expertFileId,handType:1
				},
				error : function(request) {
					swal("错误!","请求异常！", "error");
				},
				success : function(data) {
					if(data.result =='success'){
						scoringIndexList=data.data;
						//console.log(scoringIndexList);
					}
				}
			});
		})

		
		var xzcfVue = new Vue({

			  el: '#CommitEnttity',
			  data: {
				  scoringIndexList:scoringIndexList,
			  },
			  mounted: function(){
					this.sumScore();
				//  setTimeout(this.saveTime,1000*60*30);//30分钟自动保存
				},
			  methods: {
				  sumScore:function(){//判断是否有总分，没有则相加求分

					  	var entityScore = this.scoringIndexList.entityscore
				  		var indexObj = this.scoringIndexList.expertHandlIndexScoreList;
				  		if(entityScore==""|| entityScore==null){
				  			var indexStr = "";
							if(indexObj != null&&indexObj.length>0){
						  		for(var i=0;i<indexObj.length;i++){
						  			if(!isNaN(parseFloat(indexObj[i].resultScore))){
						  				indexStr = indexStr+","+indexObj[i].resultScore;
						  			}
						  		}
						  	}
							if(indexStr.indexOf("1")!=-1){
								this.scoringIndexList.entityscore =0;
					  		}else{
					  			this.scoringIndexList.entityscore =50;
					  		}
				  		}
					},
				updateItem:function (index,itemIndex,itemScore,id){//

				  	var value = xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].score;
				  	if(value != '' && value != null){
				  		
				  		$("#"+index+"expertItem"+itemIndex).removeClass("has-error");	
				  		$("#"+index+"expertItem"+itemIndex).addClass("has-success");	
						xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorFlag= true;
						xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorMessage="";
						// Window.print("asad")
						
				  		if(value=="1"){
				  			// var comment = $("#"+index+"comment"+itemIndex).val().replace(/\s+/g,"");
				  			var comment = $("#"+index+"comment"+itemIndex).val();
					  		if(comment==null || comment==''){
					  			$("#"+index+"comment"+itemIndex).removeClass("textarea-success");	
						  		$("#"+index+"comment"+itemIndex).addClass("textarea-error");	  
								xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorFlag= false;
								xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorComment="评查结果若选择‘是’，具体情形必填";
					  		}
				  		}else{
				  			$("#"+index+"comment"+itemIndex).removeClass("textarea-error");	
				  			$("#"+index+"comment"+itemIndex).addClass("textarea-success");	 
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorFlag== true;
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorComment="";
				  		}
					  		 	
						var indexObj = xzcfVue.scoringIndexList.expertHandlIndexScoreList[index];
						var indexScore = xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].indexscore;
						var itemStr = "";
						if(indexObj != null){
				  			for(var i=0;i<indexObj.expertHandlItemScoreList.length;i++){
			  					if(!isNaN(parseFloat(indexObj.expertHandlItemScoreList[i].score))){
			  						itemStr = itemStr+","+indexObj.expertHandlItemScoreList[i].score;
			  					}
			  		 		}
				  		}
				  		//一级指标分
				  		if(itemStr.indexOf("1")!=-1){
				  			xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore= 1;
				  		}else{
				  			xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore= 0;
				  		}
				  		
				  		 	
				  		//实体总分技算

				  		var indexObj1 = xzcfVue.scoringIndexList.expertHandlIndexScoreList;
				  		var indexStr = "";
						if(indexObj1 != null){
					  		for(var i=0;i<indexObj1.length;i++){
					  			if(!isNaN(parseFloat(indexObj1[i].resultScore))){
					  				indexStr = indexStr+","+indexObj1[i].resultScore;
					  			}
					  		}
					  	}
						if(indexStr.indexOf("1")!=-1){
							xzcfVue.scoringIndexList.entityscore =0;
				  		}else{
				  			xzcfVue.scoringIndexList.entityscore =50;
				  		}
				  	}else{
				  		$("#"+index+"expertItem"+itemIndex).removeClass("has-success");	
				  		$("#"+index+"expertItem"+itemIndex).addClass("has-error");	  
						xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorFlag= false;
						xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorMessage="请选择评查结果！";
				   		return false;
				  	}
				},
				updateCommon:function (index,itemIndex){
					var radio = xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].score;
					if(radio != '' && radio != null){
						if(radio=="1"){
							// var comment = $("#"+index+"comment"+itemIndex).val().replace(/\s+/g,"");
							var comment = $("#"+index+"comment"+itemIndex).val();
							if(comment==null || comment==''){
								$("#"+index+"comment"+itemIndex).removeClass("textarea-success");	
								$("#"+index+"comment"+itemIndex).addClass("textarea-error");	  
								xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorFlag= false;
								xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorComment="评查结果若选择‘是’，具体情形必填";
					  		}else{
					  			$("#"+index+"comment"+itemIndex).removeClass("textarea-error");	
					  			$("#"+index+"comment"+itemIndex).addClass("textarea-success");	 
								xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorFlag= true;
								xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorComment="";
					  		}
					  	}else{
				  			$("#"+index+"comment"+itemIndex).removeClass("textarea-error");	
				  			$("#"+index+"comment"+itemIndex).addClass("textarea-success");	 
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorFlag= true;
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorComment="";
				  		}
				  	}else{
				  		$("#"+index+"comment"+itemIndex).removeClass("textarea-error");	
			  			$("#"+index+"comment"+itemIndex).addClass("textarea-success");	 
						xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorFlag= true;
						xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorComment="";
				  	}
				},
				  saveTime:function(){
					  if(this.scoringIndexList.scoredstate == 3 || this.scoringIndexList.scoredstate == 4){
						  this.saveSubmit(this.scoringIndexList.id,'0');//暂存
					  }else{
						  this.saveSubmit(this.scoringIndexList.id,'1');//保存
					  }
				  },
				saveSubmit:function(id,status){//0代表缓存，1代表保存
					if(status=='1'){
						if(xzcfVue.scoringIndexList!=null){
							var index = xzcfVue.scoringIndexList.expertHandlIndexScoreList;
							for(var i=0;i<index.length;i++){
								if(index[i].expertHandlItemScoreList != null){
									var item = index[i].expertHandlItemScoreList;
									for(var j=0;j<item.length;j++){
										var value=item[j].score;
										if(value ==="" || value == null){
											xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].validatorFlag= false;
											xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].validatorMessage="请选择评查结果";
											$("#"+i+"expertItem"+""+j).removeClass("has-success");	
											$("#"+i+"expertItem"+""+j).addClass("has-error");
											$("[name='"+i+"scoreRadio"+""+j+"']").focus();
											return false;
										}else{
											if(value=="1"){
												// var comment = $("#"+i+"comment"+j).val().replace(/\s+/g,"");
												var comment = $("#"+i+"comment"+j).val();
										  		if(comment==null || comment==''){
										  			$("#"+i+"comment"+j).removeClass("textarea-success");	
											  		$("#"+i+"comment"+j).addClass("textarea-error");
											  		$("#"+i+"comment"+j).focus();
													xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].validatorFlag= false;
													xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].validatorComment="评查结果若选择‘是’，具体情形必填";
													return false;
										  		}else{
										  			xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].validatorFlag= true;
										  		}
											}else{
												xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].validatorFlag= true;
											}
										}
									}
								}
							}
						}
					}
					//是否材料严重不全
					if ($("#fileMaterials").prop("checked")) {
						xzcfVue.scoringIndexList.fileMaterials = '1';
					}else {
						xzcfVue.scoringIndexList.fileMaterials = '0';
					}
					$.ajax({
						type:"post",
						url:WEBPATH+'/zjpf/saveEntityScore.do',
						data:{id:id,scoringIndex:JSON.stringify(xzcfVue.scoringIndexList),status:status},           //注意数据用{}
						success:function(data){//成功
							if(data.result=="success"){
								swal({title: "保存成功",text: "",type:"success"});
								if(status=='1'){
									business.addMainContentParserHtml('zjpf/chief_list.do','pageNum=${pageNum}');
							  	}else{
							  		business.addMainContentParserHtml('zjpf/entityChiefScore.do?id='+id,'pageNum=${pageNum}');
							  	}
						        return false;
							}else{
								if(data.code=="007"){
									swal("提示", "正在保存中……，请稍等片刻", "info");
								}else if(data.code=="000"){//登录信息失效
									swal({ 
								        title: "提示",  
								        text: data.message,  
								        type: "error", 
								        confirmButtonText: "确定", 
								        confirmButtonColor: "#ec6c62" 
								    }, function() { 
								        window.location.href=WEBPATH+"/index.do";
								    });
								}
								return false;
						    }
						}
					});
				}
			}
			  
			  
		});
		
		//下载文件
		function xiaZaiAnJuan(){
		  var fileId = $("#fileId").val();
		  $.ajax({
			    type:"post",
			    url:WEBPATH+'/zjpf/xzcfExistFileUrl.do',
			    data:{fileId:fileId},           //注意数据用{}
			    success:function(data){  //成功
				 	if("yes" == data){
						window.location.href="${pageContext.request.contextPath}/zjpf/zjpfAnJuandown.do?fileId="+fileId;
					    return false;
			         }else if("no" == data){
			            	  swal( "操作失败","该案卷不存在!", "error");
			            	  return false;
					}else if("suffixerror" ==data){
						  swal( "操作失败","该案卷上传数据格式有问题!", "error");
		            	  return false;
					}
			    }
		    });
		}
		
		 
		 function loding(btn,itext){
			document.getElementById(btn).innerHTML = "加载.."
			document.getElementById(btn).disabled = "disabled"
		    setTimeout(function () {
		      document.getElementById(btn).innerHTML = itext;
		      document.getElementById(btn).removeAttribute("disabled");
		    },3000);
		 }
</script>
<body>
	<div id="CommitEnttity">
		<div class="center_weizhi">当前位置：专家评分 - 首席专家评审案卷 - ${filetypeName}</div>
		<div class="center">
			<div class="center_list">
				<input id="pageNum"  type="hidden" value="${pageNum}" >
		 		<input id="expertFileId"  type="hidden" value="${expertFileId}" >
		 		<input id="fileId"  type="hidden" value="${expertHandlFileList.fileid}" >
				<button    class="btn btn-primary"  style="font-size:16px;width:98px; height:40px; padding:6px 34px;margin-right: 10px;float: right;" onclick="macroMgr.onLevelOneMenuClick(null, 'zjpf.do')">返回</button>
		 		<div class="dingwei">
					<button class="btn btn-default" onclick="macroMgr.onLevelTwoMenuClick(null, 'zjpf/zjpfChiefScore.do?id=${expertFileId}&pageNum=${pageNum}' )">卷面评查</button>
					<button class="btn btn-danger" >实体和程序评查</button>
					<p style="font-weight:800; font-size:16px">评审说明：</p>
					<p>1.出现下面任一项情形的属于错案，扣50分。</p>
					<p>2.与其中任一项表现情形相吻合的，在序号处打“√”（找不到合适的表述请在相应评查项目的“其他”项前打“√”）并在审核栏说明具体情形。</p>
					<div class="dingwei">案卷或材料：<span style="color:#009CCD;font-size:14px; font-weight:400;" id ="filecode">
						{{scoringIndexList.filecode }} 
						<span style="color:red;font-size:16px; font-weight:bold;"><c:choose><c:when test="${expertHandlFileList.closed==1}">（已结案）</c:when><c:when test="${expertHandlFileList.closed==0}">（未结案）</c:when> </c:choose></span>
						</span>
					<c:if test ="${sessionScope.sa_session.sysStatus == '5' }">	 
						<a v-if="scoringIndexList.fileurl != null && scoringIndexList.fileurl != ''" v-bind:href="scoringIndexList.downUrl" target="_Blank">
							<button id="download" class="btn btn-success btn-xs" data-toggle="modal" style="padding:5px 12px;margin-left:10px"  data-target="#myModal">下载</button>
						</a>
						<span v-else style="color:red;font-size:16px; font-weight:bold;">（无案卷文件）</span>
						
							<button id="look" class="btn btn-info btn-xs" data-toggle="modal" style="padding:5px 12px;margin-left:10px" data-remote="${webpath}/jcpf/showFileModal.do?fastDFSUrl=+'${fastDFS}'+" data-target="#view">预览</button>
						
					</c:if>
					</div>
				</div>
				
		       	<form action="#" id ="zjpfForm" method="post">
		        	<table class="table table-bordered table-hover table-condensed">
						<thead>
							<tr>
								<td width="3%" height="30" bgcolor="#efefef">序号</td>
								<td width="7%" bgcolor="#efefef">评查项目</td>
								<td bgcolor="#efefef">
								<table width="100%" border="0" cellspacing="0" cellpadding="0" style="padding:0;">
									<tr>
										<td style="border-right:solid 1px #ddd;">错案表现形式</td>
										<td style="width:60px;border-right:solid 1px #ddd;">委员A评查结果</td>
										<td style="width:120px;border-right:solid 1px #ddd;">委员A具体情形</td>
										<td style="width:60px;border-right:solid 1px #ddd;">委员B评查结果</td>
										<td style="width:120px;border-right:solid 1px #ddd;">委员B具体情形</td>
										<td style="border-right:solid 1px #ddd; width:10%; padding:0px 5px 0px 5px;">评查</td>
										<td style="width:30%;padding:0px 5px 0px 5px;">具体情形</td>
									</tr>
								</table>
								</td>
								<!-- <td width="15%" bgcolor="#efefef">具体情形</td> -->
							</tr>
						</thead>
						<tbody  class="form-group">
							<tr v-for="(scoringIndex, index) in scoringIndexList.expertHandlIndexScoreList">
								<td height="30" align="center" >{{index+1}}</td>
			             
								<!-- 评查项目 -->
								<td align="center">{{scoringIndex.indexname}}</td>
								
								<!-- 标准分 -->
								<!-- <td style="vertical-align:middle;">{{scoringIndex.indexscore}}</td> -->
								
								<!-- 评分细则 -->
								<td v-if="scoringIndex.expertHandlItemScoreList != null && scoringIndex.expertHandlItemScoreList.length !=0 "  style="padding:5px;">
									<table width="100%" border="0" cellspacing="0" cellpadding="0" style="padding:0;">
										<tr  v-for="(scoringItem, index1) in scoringIndex.expertHandlItemScoreList">   
											<td style="border-bottom:solid 1px #ddd; border-right:solid 1px #ddd;vertical-align:middle;">
												<div style="vertical-align:middle; margin:0 5px 5px 0;">{{scoringItem.itemname}}</div>
											</td>                      
											
											<td style="width:60px;border-bottom:solid 1px #ddd; border-right:solid 1px #ddd;vertical-align:middle;">
												{{scoringItem.aexpertScore==1 ? "是":"否" }}
											</td>
											<td style="width:120px;border-bottom:solid 1px #ddd; border-right:solid 1px #ddd;vertical-align:middle;">
												{{scoringItem.aexpertReason}}
											</td> 
											<td style="width:60px;border-bottom:solid 1px #ddd; border-right:solid 1px #ddd;vertical-align:middle;">
												{{scoringItem.bexpertScore==1 ? "是":"否" }}
											</td> 
											<td style="width:120px;border-bottom:solid 1px #ddd; border-right:solid 1px #ddd;vertical-align:middle;">
												{{scoringItem.bexpertReason}}
											</td>
											<td style="border-bottom:solid 1px #ddd; border-right:solid 1px #ddd; width:10%; vertical-align:middle;">
												<div  :id="index+'expertItem'+index1" style="float:left;">
													
													<div style="float:right; padding:0 5px 0 10px;">
															
															<input type="radio" value="1" :name="index+'scoreRadio'+index1" v-model="scoringItem.score" @click="updateItem(index,index1,scoringItem.itemscore,scoringItem.temItemId)">是
															<input type="radio" value="0" :name="index+'scoreRadio'+index1" v-model="scoringItem.score" @click="updateItem(index,index1,scoringItem.itemscore,scoringItem.temItemId)">否	
													</div>
													<div style="color:red; font-size: 12px;">{{scoringItem.validatorMessage}}</div>
												</div>
											</td>   
											<td style="border-bottom:solid 1px #ddd; width:30%;vertical-align:middle;padding:0px 5px 0px 5px;">
												<textarea maxlength="2000" :id="index+'comment'+index1" name="commeInput" v-model="scoringItem.commeStr"
												@input="updateCommon(index,index1)"  rows="4" class="form-control" style="width:100%;" 
												placeholder="请输入具体情形，长度不能超过2000个字符" :title="scoringItem.commeStr">
												</textarea>
												<div style="color:red; font-size: 12px;">{{scoringItem.validatorComment}}</div>
											</td>                          
										</tr>
									</table>
								</td>
								<!-- <td v-else >
									<table width="100%" border="0" cellspacing="0" cellpadding="0">
										<tr>
											<td style="border-bottom:solid 1px #f7f7f7;">
												<div style="vertical-align:middle; margin:0 5px 5px 0;">{{scoringIndex.indexdesc}}</div>
											</td>    
											<td style="border-bottom:solid 1px #f7f7f7;">
												<div :id="'expertIndex'+index"  class="form-group" style="width: 150px;float: right;">
													<div v-if="scoringIndex.isAdd==1" style="float:left; padding:5px 2px 7px 3px; color:red; font-size:20px;">+</div>
													<div v-if="scoringIndex.isAdd!=1" style="float:left; padding:0 0 3px 0; margin:0; color:red; font-size:26px;">-</div>
													<input name="scoreInput" type="text" v-model="scoringIndex.score" autocomplete="off"
														@input="updateIndex(index,scoringIndex.indexscore)" :class="'checkitem'+index"
														class="form-control inputDisabled"  placeholder="请输入初评得分">
													<div style="color:red;font-size: 5px;">{{scoringIndex.validatorMessage}}</div>
												</div>
											</td>
											<td>
											</td>
										</tr>
									</table>
								</td> -->
			             
							</tr>
							<tr>
				             <td colspan="2">合计得分</td>
				             <td colspan="2">任意一小项选择是，则扣全50分，每一小项都是必选项<input disabled type="text" v-model="scoringIndexList.entityscore" class="form-control"></td>
				             
				           </tr>
						</tbody>
					</table>
				</form>
		 		<div class="submit">
		 			<!--   -->
		 			<button v-if="scoringIndexList.zeroScoreFileReviews != 1" type="button" class="btn btn-primary" v-on:click="saveSubmit(scoringIndexList.id,0)" name="signup" value="Sign up" style="font-size:14px;width:126px;padding: 0;color:#ffffff;height:40px; margin-top:5px;">信息暂存</button>
	 				<button v-if="scoringIndexList.zeroScoreFileReviews != 1"  type="button" class="btn btn-danger" v-on:click="saveSubmit(scoringIndexList.id,1)" name="signup" value="Sign up" style="font-size:14px;width:126px;padding: 0;color:#ffffff;height:40px;margin-top:5px;">信息保存</button>
 				</div>
				<!--  附件查看 -->
				<div class="modal fade" id="view" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
					<div class="modal-dialog modal-lg">
						<div class="modal-content"></div>
					</div>
				</div>
			</div>
		</div>
	</div>
</body>
</html>