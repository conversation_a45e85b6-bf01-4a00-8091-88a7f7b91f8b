<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
<style type="text/css">
	.pdfobject-container {
		width: 100%;
		height:580px;
		margin: 2em 0;
	}
	.textarea-success {
		border-top:green 1px solid;
		border-bottom:green 1px solid; 
		border-left:green 1px solid;
     	border-right:green 1px solid;
	}
	.textarea-error {
		border-top:red 1px solid;
		border-bottom:red 1px solid; 
		border-left:red 1px solid;
     	border-right:red 1px solid;
	}
</style>
</head>
<script type="text/javascript">
		business.listenEnter();
		var pageNum =$("#pageNum").val();
		var scoringIndexList =null;
		var expertFileId = $("#expertFileId").val();
		$.ajax({
			cache : true,
			type : "GET",
			async : false,
			//api/test/detail/behaviour/74
			url: WEBPATH+"/zjpf/getIndexList.do",
			data:{
				id:expertFileId
			},
			error : function(request) {
				swal("错误!","请求异常！", "error");
			},
			success : function(data) {
			  	if(data.result =='success'){
			  		scoringIndexList=data.data;
			  		console.log(scoringIndexList);
			  	}
			} 
		});
		
		var xzcfVue = new Vue({
			  el: '#ExpertVue',
			  data: {
				  scoringIndexList:scoringIndexList,
			  },
			  methods: {
				  checkBoxClick:function(){//优秀典型案例推荐复选框点击事件
						if($("#yxdxAnLiTuiJianReviews1").css("display")=='none' ){//如果show是隐藏的
							$("#yxdxAnLiTuiJianReviews1").css("display","block"); //  yxdxanlituijianreviews show的display属性设置为block（显示）
							//$("#yxdxanlituijian").val("1");
							xzcfVue.scoringIndexList.yxdxanlituijian="1";
							}else{//如果show是显示的
							xzcfVue.scoringIndexList.yxdxanlituijianreviews="";
							xzcfVue.scoringIndexList.yxdxanlituijian="0";
							$("#yxdxAnLiTuiJianReviews1").css("display","none");//show的display属性设置为none（隐藏）
					}
				  },
				  ajpjCheckBoxClick:function(){//是否进行案卷评价复选框点击事件
						if($("#ajpjYxdxAnLiTuiJianReviews1").css("display")=='none' ){//如果show是隐藏的
							$("#ajpjYxdxAnLiTuiJianReviews1").css("display","block"); //  yxdxanlituijianreviews show的display属性设置为block（显示）
							//$("#ajpjYxdxanlituijian").val("1");
							xzcfVue.scoringIndexList.ajpjYxdxanlituijian="1";
							}else{//如果show是显示的
							xzcfVue.scoringIndexList.ajpjYxdxanlituijianreviews="";
							xzcfVue.scoringIndexList.ajpjYxdxanlituijian="0";
							$("#ajpjYxdxAnLiTuiJianReviews1").css("display","none");//show的display属性设置为none（隐藏）
					}
				  },
				  changeCheckBoxCli:function(index,status){//一票否决或无需评查复选框点击事件
					  //获取选中未选中的状态 stauts =1一票否决 status=2无需评查
					  if(status=='1'){
						  //一票否决
						var flag=  xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].voteDownValue;
					  	var checkitem =".checkitem"+index;
					  	var isInCheckid = '#isInCheck'+index;
					  	if(flag){//一票否决选中
					  		var data = xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList;
					  		if(data!=null &&  data.length>0){
					  			for(var i=0;i<data.length;i++){
					  				data[i].score='';
					  				$("#expertItem"+i+""+index).removeClass("has-error");	
									$("#expertItem"+i+""+index).removeClass("has-success");
									xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[i].validatorMessage="";
					  			}
					  		}else{
					  			xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].score='';
					  			xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore='';
					  			$("#expertIndex"+index).removeClass("has-error");	
								$("#expertIndex"+index).removeClass("has-success");	
								xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].validatorMessage="";
					  		}
				  			xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore=0;
					  		$(checkitem).attr("disabled","disabled");
					  		$(isInCheckid).attr("disabled","disabled");
					  		
					  		var text = $("#comment"+index).val();
					  		if(text==null || text==''){
					  			$("#comment"+index).removeClass("textarea-success");	
						  		$("#comment"+index).addClass("textarea-error");	  
								xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].validatorFlag= false;
								xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].validatorMessage="此项评审依据必填";
					  		}
					  	}else{//一票否决取消
					  		xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].score='';
					  		xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore='';
					  		var data = xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList;
					  		if(data!=null &&  data.length>0){
					  			for(var i=0;i<data.length;i++){
									xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[i].validatorFlag= false;
					  			}
					  		}else{
					  			xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].validatorFlag= false;
					  		}
					  		$(checkitem).removeAttr("disabled");
					  		$(isInCheckid).removeAttr("disabled");
					  		
					  		$("#comment"+index).removeClass("textarea-error");	
					  		$("#comment"+index).addClass("textarea-success");	  
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].validatorFlag= true;
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].validatorMessage="";
					  	}
					  	 //总分技算
					  	 var num = 0;
						 var childNum =0;
				  		 var score1 = xzcfVue.scoringIndexList.expertHandlIndexScoreList;
						  if(score1 != null){
					  			for(var i=0;i<score1.length;i++){
					  					  if(!isNaN(parseFloat(score1[i].resultScore))){
					  						num = (Math.round((num + parseFloat(score1[i].resultScore))*100))/100; 
					  					  }
					  		 		}
					  	 }
						xzcfVue.scoringIndexList.expertfinalscore =num;
					  }else{
						  //无需评查
						    var flag=  xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].inCheckValue;
						  	var checkitem =".checkitem"+index;
						  	var isVoteDownid = '#isVoteDown'+index;
						  	if(flag){//无需评查选中
						  		var data = xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList;
						  		if(data!=null &&  data.length>0){
						  			for(var i=0;i<data.length;i++){
						  				data[i].score='';
								  		$("#expertItem"+i+""+index).removeClass("has-error");	
										$("#expertItem"+i+""+index).removeClass("has-success");
										xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[i].validatorMessage="";
						  			}
						  		}else{
						  			xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].score='';
						  			xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore='';
						  			$("#expertIndex"+index).removeClass("has-error");	
									$("#expertIndex"+index).removeClass("has-success");	
									xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].validatorMessage="";
						  		}
						  	/* 	if(xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].isAdd ==1){
					  				//是否为加分项
					  				xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore=0;
					  			}else{
					  				//减分项
					  			} */
					  				xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore=xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].indexscore;
						  		$(checkitem).attr("disabled","disabled");
						  		$(isVoteDownid).attr("disabled","disabled");
						  	}else{//无需评查取消
						  		var data = xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList;
						  		if(data!=null &&  data.length>0){
						  			for(var i=0;i<data.length;i++){
										xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[i].validatorFlag= false;
						  			}
						  		}else{
						  			xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].validatorFlag= false;
						  		}
						  		xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].score='';
						  		xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore='';
						  		$(checkitem).removeAttr("disabled");
						  		$(isVoteDownid).removeAttr("disabled");
						  	}
						 	 //总分技算
						  	 var num = 0;
							 var childNum =0;
					  		 var score1 = xzcfVue.scoringIndexList.expertHandlIndexScoreList;
							  if(score1 != null){
						  		for(var i=0;i<score1.length;i++){
						  		  if(!isNaN(parseFloat(score1[i].resultScore))){
						  			num = (Math.round((num + parseFloat(score1[i].resultScore))*100))/100; 
						  		 	 }
						  		}
						  	 }
							xzcfVue.scoringIndexList.expertfinalscore =num;
					  }
				  },
				  updateItem:function (index,itemIndex,itemScore,id){//
					  var num = 0;
					  	var childNum =0;
					  	var value = xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].score;
					  	 if(value != '' && value != null){
					  		 	if(parseFloat(value) <=parseFloat(itemScore)){
					  		 		if(value.substring(value.length-1,value.length) =="."){
					  		 			$("#expertItem"+itemIndex+index).removeClass("has-success");	
										$("#expertItem"+itemIndex+index).addClass("has-error");	  
										xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorFlag= false;
										xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorMessage="分值项不能为空，且在0-"+itemScore+"分之间！";
								   		return false;
					  		 		}
						  		 	var re = /^-?\d+\.?\d{0,2}$/;
						    		if( re.test(value) ){   // 返回true
						    			$("#expertItem"+itemIndex+index).removeClass("has-error");	
										$("#expertItem"+itemIndex+index).addClass("has-success");	 
										xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorFlag= true;
										xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorMessage="";
						    		}else{
						    			$("#expertItem"+itemIndex+index).removeClass("has-success");	
										$("#expertItem"+itemIndex+index).addClass("has-error");	  
										xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorFlag= false;
										xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorMessage="分值项不能为空，且在0-"+itemScore+"分之间！";
								   		return false;
						    		}
						 	var score = xzcfVue.scoringIndexList.expertHandlIndexScoreList[index];
						 	var indexScore = xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].indexscore;
					  		if(score != null){
					  			for(var i=0;i<score.expertHandlItemScoreList.length;i++){
					  					  if(!isNaN(parseFloat(score.expertHandlItemScoreList[i].score))){
					  						childNum = (Math.round((childNum + parseFloat(score.expertHandlItemScoreList[i].score))*100))/100; 
					  					  }
					  		 		}
					  			}
					  		 if(xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].isAdd ==1){
					  			//加分项
					  			 xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore= childNum;
					  			if(xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore>indexScore){
					  				xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore=indexScore;
					  			}
					  		 }else{
					  			 //减分项
					  			xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore=
					  				Math.round((xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].indexscore-childNum)*100)/100;
					  		 	if( xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore<0){
					  		 	 	xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore =0;
					  		 	}
					  		 }
					  		 //总分技算
					  		 var score1 = xzcfVue.scoringIndexList.expertHandlIndexScoreList;
							  if(score1 != null){
						  			for(var i=0;i<score1.length;i++){
						  					  if(!isNaN(parseFloat(score1[i].resultScore))){
						  						num = (Math.round((num + parseFloat(score1[i].resultScore))*100))/100; 
						  					  }
						  		 		}
						  	 }
					  		xzcfVue.scoringIndexList.expertfinalscore =num;
					  		}else{
					  			$("#expertItem"+itemIndex+index).removeClass("has-success");	
								$("#expertItem"+itemIndex+index).addClass("has-error");	  
								xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorFlag= false;
								xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorMessage="分值项不能为空，且在0-"+itemScore+"分之间！";
						   		return false;
					  		}
					  	}else{
					  		$("#expertItem"+itemIndex+index).removeClass("has-success");	
							$("#expertItem"+itemIndex+index).addClass("has-error");	  
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorFlag= false;
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorMessage="分值项不能为空，且在0-"+itemScore+"分之间！";
					   		return false;
					  	}
				  },
				  updateIndex:function (index,indexScore){
					  var num = 0;
					  var value = xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].score;
					    if(value != '' && value != null){
					    	 		//eval("var re = /(?!^0\.0?0$)^([0-"+(parseInt(indexScore)-1)+"])(\.[0-9]{1,2})?$|^("+indexScore+"|"+indexScore+"\.0|"+indexScore+"\.00|0\.0|0\.00)$/;");   
						    	if(value.substring(value.length-1,value.length) =="."){
						    		$("#expertIndex"+index).removeClass("has-success");	
									$("#expertIndex"+index).addClass("has-error");	  
									xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].validatorFlag= false;
									xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].validatorMessage="分值项不能为空，且在0-"+indexScore+"分之间！";
							   		return false;
				  		 		}
					    	 		if(parseFloat(value) <=parseFloat(indexScore)){
							  		var re = /^-?\d+\.?\d{0,2}$/;
					    	 		if( re.test(value) ){   // 返回true
					    			$("#expertIndex"+index).removeClass("has-error");	
									$("#expertIndex"+index).addClass("has-success");	 
									xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].validatorFlag= true;
									xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].validatorMessage="";
					    		}else{
					    			$("#expertIndex"+index).removeClass("has-success");	
									$("#expertIndex"+index).addClass("has-error");	  
									xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].validatorFlag= false;
									xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].validatorMessage="分值项不能为空，且在0-"+indexScore+"分之间！";
							   		return false;
					    		}
					    		  var score = xzcfVue.scoringIndexList.expertHandlIndexScoreList;
								  if(xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].isAdd ==1){
									  //加分项
									  xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore=xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].score;
								  }else{
									  //减分项
									  xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore=parseFloat(xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].indexscore) -parseFloat(value);
									  //xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore=0;
								  }
								  if(score != null){
							  			for(var i=0;i<score.length;i++){
							  					  if(!isNaN(parseFloat(score[i].resultScore))){
							  						num = (Math.round((num + parseFloat(score[i].resultScore))*100))/100; 
							  					  }
							  		 		}
							  	 }
						  		xzcfVue.scoringIndexList.expertfinalscore =num;
					    		}else{
					    			$("#expertIndex"+index).removeClass("has-success");	
									$("#expertIndex"+index).addClass("has-error");	  
									xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].validatorFlag= false;
									xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].validatorMessage="分值项不能为空，且在0-"+indexScore+"分之间！";
							   		return false;
					    		}
					    }else{
					    	$("#expertIndex"+index).removeClass("has-success");	
							$("#expertIndex"+index).addClass("has-error");	  
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].validatorFlag= false;
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].validatorMessage="分值项不能为空，且在0-"+indexScore+"分之间！";
					   		return false;
					    }
				  },
				  updateCommon:function (index){
					    var flag=  xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].voteDownValue;
					    var text = $("#comment"+index).val();
					    if(flag){
					    	if(text!=null && text!=''){
					  			$("#comment"+index).removeClass("textarea-error");	
						  		$("#comment"+index).addClass("textarea-success");	  
								xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].validatorFlag= true;
								xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].validatorMessage="";
				  			}
					    	if(text==null || text==''){
					  			$("#comment"+index).removeClass("textarea-success");	
						  		$("#comment"+index).addClass("textarea-error");	  
								xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].validatorFlag= false;
								xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].validatorMessage="此项评审依据必填";
				  			}
					    }
				  },
				  saveSubmit:function(id){
					  var tuijian=document.getElementById("yxdxanlituijian").checked;
					  var pingjia=document.getElementById("ajpjYxdxanlituijian").checked;
					  if(tuijian==true){
						  if($("#zjpy").val()==null || $("#zjpy").val()==""){
							  swal("提示", "优秀典型案例推荐不能为空!", "error");
							  return false;
						  }; 
					  }
					  if(pingjia==true){
						  if($("#ajpj").val()==null || $("#ajpj").val()==""){
							  swal("提示", "案卷评价不能为空!", "error");
							  return false;
						  } 
					  }
						if(xzcfVue.scoringIndexList!=null){
							var data = xzcfVue.scoringIndexList.expertHandlIndexScoreList;
							for(var i=0;i<data.length;i++){
								if((data[i].voteDownValue !=0 || data[i].voteDownValue != 1) && data[i].voteDownValue){
									data[i].voteDownValue =1;
								}else{data[i].voteDownValue=0;}
								if((data[i].inCheckValue!= 0 || data[i].inCheckValue!=1) && data[i].inCheckValue){
									data[i].inCheckValue =1;
								}else{data[i].inCheckValue=0;}
								

								var flag=  data[i].voteDownValue;
							    var text = $("#comment"+i).val();
							    if(flag){
							    	if(text==null || text==''){
							  			$("#comment"+i).removeClass("textarea-success");	
								  		$("#comment"+i).addClass("textarea-error");	  
								  		data[i].validatorFlag= false;
								  		data[i].validatorMessage="此项评审依据必填";
								  		return false;
						  			}
							    }
								if(data[i].voteDownValue==1 || data[i].inCheckValue==1){continue;}
								if(data[i].expertHandlItemScoreList ==null&&(data[i].validatorFlag == null || !data[i].validatorFlag) ){
									xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].validatorFlag= false;
									xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].validatorMessage="分值项不能为空，且在0-"+xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].indexscore+"分之间！";
									$("#expertIndex"+i).removeClass("has-success");	
									$("#expertIndex"+i).addClass("has-error");	
									return false;
								}else{
									if(data[i].expertHandlItemScoreList != null){
										for(var j=0;j<data[i].expertHandlItemScoreList.length;j++){
											if(data[i].expertHandlItemScoreList[j].validatorFlag == null || !data[i].expertHandlItemScoreList[j].validatorFlag){
												xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].validatorFlag= false;
												xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].validatorMessage="分值项不能为空，且在0-"+xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].itemscore+"分之间！";
												$("#expertItem"+xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].temItemId+""+i).removeClass("has-success");	
												$("#expertItem"+xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].temItemId+""+i).addClass("has-error");
												return false;
											}
										}
									}
								}
							}
							
							//loding('submitBtn', '信息保存');
							$("#submitBtn").attr("disabled","disabled");
					  		$("#submitBtn").text("提交中...");
							 $.ajax({
							    type:"post",
							    url:WEBPATH+'/zjpf/saveExpertScore.do',
							    data:{id:id,
							    	scoringIndex:JSON.stringify(xzcfVue.scoringIndexList)},           //注意数据用{}
							    	success:function(data){  //成功
							    	if(data.result=="success"){
							    	  swal({title: "保存成功",text: "",type:"success"});
							    	  $("#submitBtn").attr("disabled","");
								  	  $("#submitBtn").text("保存信息");
								  	  business.addMainContentParserHtml('zjpf/xzcfList.do','pageNum=${pageNum}');
								  	business.addMainContentParserHtml('zjpf/xzcfList.do','pageNum=${pageNum}');
								  	  var consider = $("#consider").val();
								  	  if(consider==1){
								  		business.addMainContentParserHtml('zjpf/zjpf_list_commit.do','pageNum=${pageNum}');
								  	  }else{
								  		 business.addMainContentParserHtml('zjpf/xzcfList.do','pageNum=${pageNum}');
								  	  }
							          
							          return false;
							    	}else{
							    		$("#submitBtn").attr("disabled","");
								  		$("#submitBtn").text("保存信息");
							    		swal("提示", "信息保存操作失败了!", "error");
								         return false; 
							    	}
							      }
							});
						}
				  }
			 }
		});
		
		
		//处理浮点型数据相减
		function handFloat(biaozhunfen,subScore){
			var r1,r2,m,n;
			try{r1=biaozhunfen.toString().split(".")[1].length}catch(e){r1=0};
			try{r2=subScore.toString().split(".")[1].length}catch(e){r2=0};
			m=Math.pow(10,Math.max(r1,r2));
			n = (r1 >= r2) ? r1 : r2;
			return ((biaozhunfen * m - subScore * m) / m).toFixed(n);  
		}
		
		var a = xzcfVue.scoringIndexList.yxdxanlituijian;
		if(xzcfVue.scoringIndexList.yxdxanlituijian=="1"){
			//$('#yxdxanlituijian').attr('checked', true)
			
		     $("[name = yxdxanlituijian]:checkbox").attr("checked", true);
			$("#yxdxAnLiTuiJianReviews1").css("display","block"); 
		}
		
		
		var ajpja = xzcfVue.scoringIndexList.ajpjYxdxanlituijian;
		if(xzcfVue.scoringIndexList.ajpjYxdxanlituijian=="1"){
			//$('#ajpjYxdxanlituijian').attr('checked', true)
			
		     $("[name = ajpjYxdxanlituijian]:checkbox").attr("checked", true);
			$("#ajpjYxdxAnLiTuiJianReviews1").css("display","block"); 
		}
		
		/*点击开始评分之后才能评分并提交 */
	    function chickAnjuan(){
			var fileCodeInput = trim($("#fileCodeInput").val());
			fileCodeInput = fileCodeInput.replace(/\s+/g,"");
			var fileCode = trim($("#filecode").text());
			fileCode = fileCode.replace(/\s+/g,"");
			if( fileCodeInput == fileCode){
				swal("开始打分", "开始打分!", "success");
				$("input[name='scoreInput']").removeAttr("disabled");
				
				$("#yxdxanlituijian").attr("disabled",false)
				$("#ajpjYxdxanlituijian").attr("disabled",false)
				$("#submitBtn").removeAttr("style display");
				$("#chickAnjuan").hide();
				$("#fileCodeInput").attr("disabled","disabled");
				$("textarea").removeAttr("disabled");
				$("#download").removeAttr("disabled");
				$("#look").removeAttr("disabled");
			}else{
				swal("开始打分", "请输入正确的文件号！", "error");
			}
		}
	
		function trim(str) {
			  return str.replace(/(^\s+)|(\s+$)/g, "");
		}
		
		
		//下载文件
		function xiaZaiAnJuan(){
		  var fileId = $("#fileId").val();
		  $.ajax({
			    type:"post",
			    url:WEBPATH+'/zjpf/xzcfExistFileUrl.do',
			    data:{fileId:fileId},           //注意数据用{}
			    success:function(data){  //成功
				 	if("yes" == data){
						window.location.href="${pageContext.request.contextPath}/zjpf/zjpfAnJuandown.do?fileId="+fileId;
					    return false;
			         }else if("no" == data){
			            	  swal( "操作失败","该案卷不存在!", "error");
			            	  return false;
					}else if("suffixerror" ==data){
						  swal( "操作失败","该案卷上传数据格式有问题!", "error");
		            	  return false;
					}
			    }
		    });
		}
		
		 $(document).ready(function(){
			//保存数据方法
			var status = '${status}';
			if(status==1){//status为1时是查看，否则为编辑
				$("#chickAnjuan").hide();
				$("#fileCodeInput").attr("disabled","disabled");
				var scoredstate = ${expertHandlFileList.scoredstate};
				if(scoredstate==1){
					$("#fileCodeInput").val('${expertHandlFileList.filecode}');
				}else{
					/* $("#download").attr("disabled","disalbed"); */
					$("#look").attr("disabled","disalbed");//预览按钮在评分后才能点击
				}
			}else{
				var scoredstate = ${expertHandlFileList.scoredstate};
				if(scoredstate==1){
					$("input[name='scoreInput']").removeAttr("disabled");
					
					$("#zjpy").removeAttr("disabled");
					$("#ajpj").removeAttr("disabled");
					$("#submitBtn").removeAttr("style display");
					$("#chickAnjuan").hide();
					$("#fileCodeInput").attr("disabled","disabled");
					$("textarea").removeAttr("disabled");
					$("#fileCodeInput").val('${ expertHandlFileList.filecode}');
					$("#yxdxanlituijian").attr("disabled",false);
					$("#ajpjYxdxanlituijian").attr("disabled",false);
					
					/* 页面初始化加载设置隐藏 input输入框，当用户选择一票否决和无须评查  */
					if(xzcfVue.scoringIndexList != null && xzcfVue.scoringIndexList.expertHandlIndexScoreList != null ){
							for( var i=0;i< xzcfVue.scoringIndexList.expertHandlIndexScoreList.length;i++){
								if(xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].inCheckValue ==1 ||xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].voteDownValue ==1){
									var checkitem =".checkitem"+i;
									$(checkitem).attr("disabled","disabled");
									if(xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].inCheckValue ==1){
										//如果选了无须评查
										xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].resultScore=xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].indexscore;
										var isVoteDownid = '#isVoteDown'+i;
										$(isVoteDownid).attr("disabled","disabled");
									}
									if(xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].voteDownValue ==1){
										//若果选了一票否决
										xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].resultScore=0;
										var isInCheckid = '#isInCheck'+i;
										$(isInCheckid).attr("disabled","disabled");
									}
								}
							}
					}
				}else{
					/* $("#download").attr("disabled","disalbed"); */
					$("#look").attr("disabled","disalbed");
				}
			}
		 });
		 
		 function loding(btn,itext){
			document.getElementById(btn).innerHTML = "加载.."
			document.getElementById(btn).disabled = "disabled"
		    setTimeout(function () {
		      document.getElementById(btn).innerHTML = itext;
		      document.getElementById(btn).removeAttribute("disabled");
		    },3000);
		 }
</script>
<body>
<div id="ExpertVue">
<div class="center_weizhi">当前位置：专家评分 - ${typeName} - ${filetypeName}</div>
<div class="center">
<div class="center_list">
		<input id="consider"  type="hidden" value="${consider}" >
		<input id="pageNum"  type="hidden" value="${pageNum}" >
 		<input id="expertFileId"  type="hidden" value="${expertFileId}" >
 		<input id="fileId"  type="hidden" value="${expertHandlFileList.fileid}" >
		<div class="dingwei">案卷或材料：<span style="color:#23b7e5;font-size:16px; font-weight:bold;" id ="filecode">{{scoringIndexList.filecode }} </span>
		<c:if test ="${sessionScope.sa_session.sysStatus == '5' }">	 
			<a v-bind:href="scoringIndexList.downUrl" target="_Blank">
				<button id="download" class="btn btn-success btn-xs" data-toggle="modal" data-target="#myModal">下载</button>
			</a>
			<!-- <button class="btn btn-success btn-xs" onclick="javascript:window.location.href='${downUrl}'">下载</button> -->
			<c:if test="${expertHandlFileList.suffix== 'pdf'}">
				<button id="look" class="btn btn-info btn-xs" data-toggle="modal" data-remote="${webpath}/jcpf/showFileModal.do?fastDFSUrl=+'${fastDFS}'+" data-target="#view">预览</button>
			</c:if>
		</c:if>
		</div>
    	<div class="dingwei" id ="queren">案卷处罚决定书文号确认：<input type="text" class="form-control" style="width:200px; float:right;"  id="fileCodeInput" placeholder="请输入案卷处罚决定书文号" ></div>
		 <div class="dingwei"><button class="btn btn-primary" data-toggle="modal" data-target="#myModal" id ="chickAnjuan"  onclick="chickAnjuan();">开始评分</button></div>
       	<form action="#" id ="zjpfForm" method="post">
        <table class="table table-bordered table-hover table-condensed">
         <thead>
           <tr>
             <td width="3%" height="30" bgcolor="#efefef">序号</td>
             <td width="7%" bgcolor="#efefef">分指标</td>
             <td width="7%" bgcolor="#efefef">分指标分值</td>
             <td  width="17%"  bgcolor="#efefef">指标说明</td>
             <td width="44%" bgcolor="#efefef"> <span style="float: left;">判断标准</span><span style="float: right;margin-right: 70px;">扣分值</span></td>
             <td width="5%" bgcolor="#efefef">符合扣全分条件</td>
             <!-- <td width="3%" bgcolor="#efefef">无需评查</td> -->
             <td width="5%" bgcolor="#efefef">初评得分</td>
             <td width="15%" bgcolor="#efefef">评审依据</td>
           </tr>
         </thead>
         <tbody  class="form-group">
         	   <tr v-for="(scoringIndex, index) in scoringIndexList.expertHandlIndexScoreList">
	             <td height="30" align="center" >{{index+1}}</td>
	             
	             <!-- 分指标名称 -->
	             <td >{{scoringIndex.indexname}}</td>
	             
	             <!-- 指标说明 -->
	             <td >{{scoringIndex.indexscore}}</td>
	             
	             <!-- 分指标分值 -->
	             <td >{{scoringIndex.indexdesc}}</td>
	             
	             <!-- 判断标准 -->
	             <td v-if="scoringIndex.expertHandlItemScoreList != null && scoringIndex.expertHandlItemScoreList.length !=0 "  style="padding:0;">
                        <table width="100%" border="0" cellspacing="0" cellpadding="0" style="padding:0;">
                          <tr  v-for="(scoringItem, index1) in scoringIndex.expertHandlItemScoreList">   
                          	<td style="border-bottom:solid 1px #ddd; border-right:solid 1px #ddd;vertical-align:middle;">
                                 <div style="vertical-align:middle; margin:0 5px 5px 0;">
		            			{{scoringItem.itemname}}（{{scoringItem.itemscore}}分）
		            			</div>
                            </td>                      
                            <td style="border-bottom:solid 1px #ddd; width:155px;vertical-align:middle;">
                                 <div  :id="'expertItem'+index1+index" style="margin:0 0 5px 0; float:left;">
	                                 <div v-if="scoringIndex.isAdd==1" style="float:left; padding:5px 2px 7px 3px; color:red; font-size:20px;">＋</div>
	                                 <div v-if="scoringIndex.isAdd!=1" style="float:left; padding:0 0 3px 0; margin:0; color:red; font-size:26px;">－</div>
	                                 <div style="float:right; padding-top:4px;">
	                                 	<input disabled name="scoreInput" @input="updateItem(index,index1,scoringItem.itemscore,scoringItem.temItemId)"  
	                                       v-model="scoringItem.score" type="text" :class="'checkitem'+index" autocomplete="off"
	                                      class="form-control inputDisabled"   placeholder="请输入初评得分" style="width:125px;">
	                                 </div>
                                 	<div style="color:red; font-size: 12px;">{{scoringItem.validatorMessage}}</div>
                                 </div>
                            </td>                          
                          </tr>
                        </table>
		             </td>
		            <td v-else >
		             <table width="100%" border="0" cellspacing="0" cellpadding="0">
		             	<tr>
		             		<td style="border-bottom:solid 1px #f7f7f7;">
                                 <div style="vertical-align:middle; margin:0 5px 5px 0;">
		            				{{scoringIndex.indexdesc}}
		            			</div>
                            </td>    
                            <td style="border-bottom:solid 1px #f7f7f7;">
                            	<div :id="'expertIndex'+index"  class="form-group" style="width: 150px;float: right;">
	                            	<div v-if="scoringIndex.isAdd==1" style="float:left; padding:5px 2px 7px 3px; color:red; font-size:20px;">+</div>
		                            <div v-if="scoringIndex.isAdd!=1" style="float:left; padding:0 0 3px 0; margin:0; color:red; font-size:26px;">-</div>
				                		<input disabled name="scoreInput" type="text" v-model="scoringIndex.score" autocomplete="off"
				                		 @input="updateIndex(index,scoringIndex.indexscore)" :class="'checkitem'+index"
				               	  		 class="form-control inputDisabled"  placeholder="请输入初评得分">
			         			  	<div style="color:red;font-size: 5px;">{{scoringIndex.validatorMessage}}</div>
			         			 </div>
                            </td>
		             	</tr>
		             </table>
		         </td>
	           
	           		
	             <!-- 2017需求变更，去掉一票否决和无需评查 --> 	
	           	 <!-- 一票否决 -->
	           	 <td >
		              <span v-if="scoringIndex.isVoteDown ==1" >
		              		 <input name="scoreInput" disabled type="checkbox" :id="'isVoteDown'+index" 
		              		  @change="changeCheckBoxCli(index,'1')" v-model="scoringIndex.voteDownValue"
		              		  style="width:16px; height:16px; display: table-cell;vertical-align: middle; text-align: center;">
               				 <label :for="'checkbox'+index" class="checkbox-blue" checked></label>
		              </span>
		         </td> 
	           	 
	           	 <!-- 无需评查 -->
	           	 <!-- <td >
		            <span v-if="scoringIndex.isInCheck ==1">
		              		 <input name="scoreInput" disabled type="checkbox" :id="'isInCheck'+index"
		              		  @change="changeCheckBoxCli(index,'2')"  v-model="scoringIndex.inCheckValue"
		              		  style="width:16px; height:16px;  display: table-cell;vertical-align: middle; text-align: center; ">
               				 <label for="checkbox1" class="checkbox-blue" checked></label>
		              </span>
		         </td> -->
	           
	           	 <!-- 初评得分 -->
	             <td >
	             	<div style="width:120px;float: right;">
	           			<input type="text"  disabled="disabled" v-model="scoringIndex.resultScore" class="form-control">
	           		</div>
	             </td>
	           
	             <!-- 评审依据 -->
	             <td >
	           	   <textarea @input="updateCommon(index)" :id="'comment'+index" :rows="scoringIndex.expertHandlItemScoreList.length" disabled class="form-control" v-model="scoringIndex.comment" placeholder="请输入评审依据" style="width:100%;">
				   
				   </textarea>
				   <div style="color:red; font-size: 12px;">{{scoringIndex.validatorMessage}}</div>
	             </td>
	             
	           </tr>
	           <tr>
	           		<td></td>
	           		<td>合计</td>
	           		<td>100</td>
	           		<td></td>
	           		<td></td>
	           		<td></td>
	           		<td>
		           		<div style="width:120px;float: right;">
		           			<input type="text"  disabled="disabled" v-model="scoringIndexList.expertfinalscore" class="form-control">
		           		</div>
	           		</td>
	           		<td></td>
	           </tr>
	           <tr>
	           		<td height="30" colspan="9" style="line-height: 3.1">
	           		<input disabled id="yxdxanlituijian" 
	           		  name = "yxdxanlituijian" type="checkbox"  v-on:click="checkBoxClick()" >是否可以作为优秀典型案例推荐
             		 <div class="col-sm-12" style="display: none;" id ="yxdxAnLiTuiJianReviews1"  >
             			<textarea disabled v-model="scoringIndexList.yxdxanlituijianreviews" rows="5" class="form-control" id="zjpy" placeholder="请输入专家评语"></textarea>
             		 </div>
             		</td>
	           </tr>
	           <tr>
	           		<td height="30" colspan="9" style="line-height: 3.1">
	           		<input disabled id="ajpjYxdxanlituijian" 
	           		  name = "ajpjYxdxanlituijian" type="checkbox"  v-on:click="ajpjCheckBoxClick()" >是否进行案卷评价
             		 <div class="col-sm-12" style="display: none;" id ="ajpjYxdxAnLiTuiJianReviews1"  >
             			<textarea disabled v-model="scoringIndexList.ajpjYxdxanlituijianreviews" rows="5" class="form-control" id="ajpj" placeholder="请输入案卷评价"></textarea>
             		 </div>
             		</td>
	           </tr>
          	</tbody>
       </table>
 	</form>
 	
 	<!-- <button id="submitBtn" style="align:right;display:none" class="btn btn-primary" v-on:click="saveSubmit(scoringIndexList.id)" type="button" style="font-size:16px;width:150px; margin-top:5px;">信息保存</button> -->
 	<a style="float: right;" href="#"><button class="btn btn-primary" id="submitBtn" v-on:click="saveSubmit(scoringIndexList.id)" type="button" style="font-size:16px;width:150px; margin-top:5px;display:none">信息保存</button></a>	
	<!--  附件查看 -->
	<div class="modal fade" id="view" tabindex="-1" role="dialog"
		aria-labelledby="myModalLabel" aria-hidden="true">
		<div class="modal-dialog modal-lg">
			<div class="modal-content"></div>
		</div>
	</div>
</div>
</div>
</div>
</body>
</html>