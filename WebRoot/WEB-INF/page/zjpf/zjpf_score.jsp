<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>

<html>
<head>
    <style type="text/css">
        .card {
            background: #fff;
            margin-top: 20px;
            padding: 24px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            border: 1px solid #f0f0f0;
        }
        .modal-backdrop {
            position: relative;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            z-index: 1040;
            background-color: #000
        }

        .modal-backdrop.fade {
            filter: alpha(opacity=0);
            opacity: 0
        }

        .modal-backdrop.in {
            filter: alpha(opacity=50);
        }

        .tag {
            border: solid 1px #ddd;
            padding: 5px;
            margin: 5px 0;
            cursor: pointer;
            width: 300px; /* 根据需要调整宽度 */
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0, 0, 0, 0.7);
            padding: 60px;
        }

        .modal-content {
            background-color: #fff;
            margin: auto;
            padding: 20px;
            border: 1px solid #888;
            width: 80%;
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
        }

        .close:hover,
        .close:focus {
            color: black;
            text-decoration: none;
            cursor: pointer;
        }

        .noline-input {
            border: none;
            border-bottom: 1px solid black;
            outline: none;
        }

        .icon {
            margin: 10px 0; /* 每个图标之间的间距 */
            display: block; /* 确保图标在单独一行 */
            font-size: 15px; /* 增大图标的大小 */
            text-align: center; /* 使内容居中 */
            width: 10px;
            height: 10px;
            background-size: 10px;
        }

        .icon.pass {
            background-image: url("${webpath}/static/images/zjpf_pass.svg");
        }

        .icon.nopass {
            background-image: url("${webpath}/static/images/zjpf_nopass.svg");
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
        }

        .close:hover,
        .close:focus {
            color: black;
            text-decoration: none;
            cursor: pointer;
        }

        button#confirmButton {
            background-color: #007bff; /* 蓝色背景 */
            color: white; /* 白色文字 */
            display: block; /* 设置为块级元素，便于居中 */
            margin: 20px auto; /* 自动左右边距，居中对齐 */
            border: none; /* 去掉默认边框 */
            border-radius: 5px; /* 圆角效果 */
        }

        button#confirmButton:hover {
            background-color: #0056b3; /* 悬停时的颜色变化 */
        }

        #completionMessage h3 {
            margin-top: 20px; /* 图标与标题之间的间隔 */
            margin-bottom: 10px; /* 标题与内容之间的间隔 */
        }

        #completionMessage p {
            margin: 0; /* 清除段落的默认外边距 */
        }

        .jdmodal {
            display: none; /* 隐藏模态框 */
            position: fixed; /* 固定定位，以覆盖整个视口 */
            z-index: 1000; /* 确保模态框位于最上层 */
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0, 0, 0, 0.5); /* 半透明背景 */

        }

        .jdmodal-content {
            background-color: #fefefe;
            position: absolute; /* 使用绝对定位 */
            left: 50%; /* 左边距为50% */
            top: 50%; /* 顶边距为50% */
            transform: translate(-50%, -50%); /* 通过变换实现真正的居中 */
            padding: 20px;
            border: 1px solid #888;
            width: 800px;
            text-align: center;
            z-index: 1001; /* 确保模态内容位于模态背景之上 */
        }

        .progress {
            background-color: #f3f3f3;
            border: 1px solid #ccc;
            height: 20px;
            width: 100%;
            margin: 20px 0;
        }

        .progress-bar {
            background-color: #4caf50;
            height: 100%;
            width: 0;
            transition: width 0.1s; /* 动画效果 */
        }

        /* 完成消息样式 */
        #completionMessage {
            margin-top: 20px;
        }

        .custom-container {
            position: relative;
            text-align: left; /* 居中显示内容 */
        }

        .custom-icon-button {
            font-size: 24px; /* 调整图标大小 */
            cursor: pointer;
            background: none;
            border: none;
            color: #f39c12; /* 图标颜色 */
            transition: transform 0.2s; /* 添加动画效果 */
        }

        .custom-icon-button:hover {
            transform: scale(1.2); /* 鼠标悬停时放大 */
        }

        .custom-modal {
            display: none; /* 默认隐藏 */
            position: fixed;
            z-index: 1;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0, 0, 0, 0.7); /* 深色背景 */
        }

        .custom-modal-content {
            background-color: #ffffff;
            margin: 10% auto;
            padding: 30px;
            border-radius: 10px; /* 圆角边框 */
            width: 90%;
            max-width: 600px; /* 最大宽度 */
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3); /* 阴影效果 */
        }

        .custom-modal-content.new-modal {
            border-radius: 0;
            padding: 0;
        }

        .custom-modal-title {
            font-size: 24px; /* 标题字体大小 */
            margin-bottom: 20px; /* 标题下边距 */
            color: #333; /* 标题颜色 */
        }

        .custom-modal-list {
            list-style: none; /* 移除列表样式 */
            padding-left: 0; /* 移除左边距 */
        }

        .custom-modal-list li {
            margin-bottom: 10px; /* 列表项下边距 */
            line-height: 1.6; /* 行间距 */
            color: #555; /* 列表项颜色 */
        }

        .custom-close-button {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
        }

        .custom-close-button:hover {
            color: #333; /* 鼠标悬停时变色 */
            cursor: pointer;
        }

        .custom-highlight {
            color: red; /* 重点内容颜色 */
            font-weight: bold; /* 加粗重点内容 */
        }

        .pdfobject-container {
            width: 100%;
            height: 580px;
            margin: 2em 0;
        }

        .textarea-success {
            border-top: green 1px solid;
            border-bottom: green 1px solid;
            border-left: green 1px solid;
            border-right: green 1px solid;
        }

        .textarea-error {
            border-top: red 1px solid;
            border-bottom: red 1px solid;
            border-left: red 1px solid;
            border-right: red 1px solid;
        }

        #indexName {
            height: 40px;
            width: 270px;
            background: #FFFFFF;
            border: 1px solid #DCDFE6;
            border-radius: 3px;
            margin-left: 15px;
        }

        #my_dialo button[disabled] {
            opacity: 0.2; /* 设置按钮为半透明 */
            cursor: not-allowed; /* 设置鼠标指针为禁止 */
        }

        .panel-tool-close {
            display: none;
        }

        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
        }

        /*重构样式开始*/
        .zjpf_vue_content {
            position: relative;
        }

        .breadcrumb_wrap {
            position: absolute;
            top: 2px;
            left: 0;
            z-index: 99;
            display: flex;
            width: 98%;
            padding: 3px 0 6px 10px;
            font-family: "微软雅黑";
            font-size: 16px;
            color: #134b82;
            border-top-left-radius: 5px;
            border-top-right-radius: 5px;
        }

        .back_btn {
            color: rgb(66, 133, 244);
            cursor: pointer;
        }

        .records_title {
            display: flex;
            padding: 3px 0 6px 10px;
        }

        .score_main_content {
            position: relative;
            top: 0;
            bottom: 18px;
            z-index: 99;
            display: flex;
            width: 100%;
            height: calc(100vh - 136px);
        }

        .score_left_content {
            flex: 0 0 720px;
            padding: 0 10px;
            /*border: 1px solid #ccc;*/
            background: #fff;
            border-radius: 5px;
            transition: all 0.3s ease;
            width: 720px;
            /*min-width: 600px;*/
            /*max-width: 720px;*/
            background-color: #fff;
        }

        .score_left_content.collapsed {
            flex: 0 0 0;
            padding: 0;
            overflow: hidden;
            border: none;
            width: 0;
            min-width: 0;
            max-width: 0;
        }

        .score_right_content {
            flex: 1;
            /*border: 1px solid #ccc;*/
            padding: 0 10px;
            margin-left: 10px;
            background: #fff;
            border-radius: 5px;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            position: relative;
            background-color: #fff;
        }

        .toggle_btn.leftBtn {
            position: absolute;
            left: 711px;
            top: 50%;
            transform: translateY(-50%);
            width: 20px;
            height: 40px;
            background: #fff;
            border: 1px solid #ccc;
            border-right: none;
            border-radius: 4px 0 0 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1;
        }

        .toggle_btn.rightBtn {
            position: absolute;
            left: -9px;
            top: 50%;
            transform: translateY(-50%);
            width: 20px;
            height: 40px;
            background: #fff;
            border: 1px solid #ccc;
            border-right: none;
            border-radius: 4px 0 0 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1;
        }

        .toggle_btn i {
            font-size: 14px;
            color: #666;
        }

        .tab_wrap {
            display: flex;
            align-items: center;
            gap: 56px;
            background-color: #f4f6f9;
            margin: 0 -10px;
            height: 60px;
            position: relative;
        }

        .tab_item {
            height: 100%;
            text-align: center;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #73777a;
            width: 130px;
            font-size: 16px;

        .tab_item_text {
            height: 100%;
            display: flex;
            align-items: center;
        }

        }
        .tab_item.active {
            background-color: #ffffff;

        .tab_item_text {
            border-bottom: 2px solid #367bc0;
        }

        }
        .tab_header_btn_container {
            position: absolute;
            right: 18px;
            display: flex;
            gap: 16px;

        .tab_header_btn {
            width: 120px;
            height: 36px;
            padding: 0;
            color: #367bc0;
            outline: none;
            border-radius: 4px;
            border: 1px solid #367bc0;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            cursor: pointer;
        }

        }

        .tab_content {
            padding: 15px 10px;
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;

        .tab_content_show {
            display: flex;
            flex-direction: column;
            overflow: hidden;
            padding-top: 2px;
        }

        }
        /*左侧预览模块开始*/
        .pdfPre_toolbar {
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border: 1px solid #ccc;
            padding: 0 5px;
        }

        .pdfPre_toolbar i {
            cursor: pointer;
            padding: 5px;
            font-size: 16px;
        }

        .pdfPre_toolbar i.disabled {
            cursor: not-allowed;
            opacity: 0.5;
        }

        .pdf_canvas_container {
            position: relative;
            height: calc(100% - 80px);
            overflow: auto;
            width: 100%;
        }

        .page_container {
            width: 100%;
            height: 100%;
            /*display: flex;*/
            /*flex-direction: column;*/
            /*align-items: center;*/
            /*gap: 20px;*/
            padding: 0 20px;
            overflow: auto;
        }

        .page-wrapper {
            position: relative;
            width: 100%;
        }

        .el-loading-mask {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 2000;
            opacity: 1;
            transition: opacity 0.3s;
        }

        .el-loading-mask.hide {
            opacity: 0;
            pointer-events: none;
        }

        .el-loading-spinner {
            text-align: center;
        }

        .el-loading-text {
            color: #8d8d8d;
        }

        .circular {
            /* 控制整体大小 */
            width: 42px;
            height: 42px;
            /* 启用硬件加速 */
            transform: translateZ(0);
            /* 旋转动画 */
            animation: loading-rotate 2s linear infinite;
        }

        .path {
            /* 圆环样式 */
            stroke: #409EFF; /* Element UI 主色 */
            stroke-width: 3;
            stroke-linecap: round;
            /* 虚线动画 */
            animation: loading-dash 1.5s ease-in-out infinite;
        }

        @keyframes loading-rotate {
            100% {
                transform: rotate(360deg);
            }
        }

        @keyframes loading-dash {
            0% {
                stroke-dasharray: 1, 200;
                stroke-dashoffset: 0;
            }
            50% {
                stroke-dasharray: 90, 150;
                stroke-dashoffset: -40px;
            }
            100% {
                stroke-dasharray: 90, 150;
                stroke-dashoffset: -120px;
            }
        }

        /*左侧预览模块结束*/

        /*智能评查详情模块开始*/
        .ied_content {
            width: 100%;
            height: 615px;
            overflow-y: auto;
            display: flex;
            gap: 10px;
        }

        .ied_left_content {
            width: 80%;
        }

        .ied_table {
            width: 100%;
            border-collapse: collapse;
            border: 1px solid #d0d7e5;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            table-layout: fixed;
        }

        .ied_table th {
            background-color: #e0e6ed;
            padding: 12px 10px;
            text-align: left;
            font-weight: bold;
        }

        /* 调整列宽 */
        .ied_table th:nth-child(1),
        .ied_table td:nth-child(1) {
            width: 60%;
        }

        .ied_table th:nth-child(2),
        .ied_table td:nth-child(2) {
            width: 15%;
        }

        .ied_table th:nth-child(3),
        .ied_table td:nth-child(3) {
            width: 25%;
        }

        .ied_table td {
            padding: 12px 10px;
            border-bottom: 1px solid #d0d7e5;
            vertical-align: top;
        }

        .ied_table td:not(:last-child) {
            border-right: 1px solid #d0d7e5;
        }

        .title-row {
            font-weight: bold;
            background-color: #f5f7fa;
        }

        .title-row td {
            border-bottom: 1px solid #d0d7e5;
        }

        .ied_right_content {
            width: 20%;
        }

        /*智能评查详情模块结束*/

        /*评分明细模块开始*/
        .score_title {
            position: relative;
            display: flex;
            align-items: center;
            gap: 32px;
        }

        .reviewBtn_wrap {
            display: flex;

        .reviewBtnDisable {
            cursor: not-allowed;

        div {
            pointer-events: none;
            opacity: 0.5;
        }

        }
        }
        .reviewBtn_item {
            border: 1px solid #ccc;
            /*border-radius: 5px;*/
            cursor: pointer;
            height: 36px;
            display: flex;
            align-items: center;
        }

        /*.reviewBtn_item:nth-of-type(1) {*/
        /*    border-bottom-right-radius: 0px;*/
        /*    border-top-right-radius: 0px;*/
        /*    border-right: none;*/
        /*}*/

        /*.reviewBtn_item:nth-of-type(2) {*/
        /*    border-bottom-left-radius: 0px;*/
        /*    border-top-left-radius: 0px;*/
        /*    border-left: none;*/
        /*}*/

        .reviewBtn_item.active {
            color: #fff;
            background-color: #367bc0;
            border-color: #367bc0;
        }

        reviewBtn_item.disable {
            opacity: 0.45;
            pointer-events: none;
        }

        .result_container {
            display: flex;
            align-items: center;

        .result_title {
            color: #7f7f7f;
        }

        .result_text {
            font-size: 20px;
            color: #333333;
        }

        .result_sum_text {
            font-size: 20px;
            color: red;
        }

        }
        .instructions_tip {
            flex: 1;
            display: flex;
            justify-content: flex-end;
            gap: 20px;
            align-items: center;
            color: #4285f4;
        }

        .expand-btn-container {
            color: #367bc0;
            cursor: pointer;
            display: flex;
            gap: 8px;

        .expand-icon::before {
            content: '\25B6';
            font-size: 16px;
            color: #367bc0;
        }

        }
        .expand-btn-container.expand-hidden {

        .expand-icon {
            transform: rotate(180deg);
        }

        }
        .table-container {
            flex: 1;
            margin-top: 16px;
            display: flex;
            flex-direction: row;
            overflow: hidden;
            position: relative;

        .table-box {
            flex: 1;
            height: 100%;
            width: 100%;
            overflow: hidden;
            -webkit-overflow-scrolling: touch;
            position: relative;

        .table-head {
            background-color: #F9FAFE;
            border-top: 1px solid #e8e8e8;
            border-bottom: 1px solid #e8e8e8;
            color: #777;
            table-layout: fixed;
            position: absolute;
            top: 0;
            width: calc(100% - 6px);

        td {
            background-color: #F5F7FA;
            text-align: center;
            border: 1px solid #EBEEF5;
            height: 40px;
            line-height: 40px;
        }

        }
        .table-body-box {
            table-layout: fixed;
            -webkit-overflow-scrolling: touch;
            overflow: scroll;
            scroll-behavior: smooth;
            height: calc(100% - 41px);
            box-sizing: border-box;
            margin-top: 41px;

        .table-body {
            table-layout: fixed;
            width: 100%;
            background-color: white;
            text-align: center;
            margin: 0;

        .tr-hover {
            background-color: #f5f5f5;
        }

        }
        }
        .table-body-box::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }

        }
        .table-link {
            margin-left: 20px;
            width: 217px;
            overflow: auto;
            transition: all 0.3s ease;

        .link-container {
            display: flex;
            align-items: stretch;

        .link-line {
            position: relative;
            width: 3px;
            background-color: #e7ebf0;
            margin-right: 8px;
        }

        a {
            flex: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            cursor: pointer;
            padding: 11px 8px;
        }

        a:hover {
            background-color: #f5f7fa;
            color: #4285f4;
        }

        }
        .link-container.active {

        .link-line::before {
            content: "";
            height: 75%;
            width: 3px;
            background-color: #367bc0;
            border-radius: 2px;
            display: block;
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
        }

        a {
            color: #367bc0;
        }

        }
        }
        .table-link.table-link-hidden {
            width: 0;
        }

        .table-link::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }

        .table-nopass-container {
            width: 0;
            height: 100%;
            position: absolute;
            right: 0;
            border: 1px solid #ddd;

        .table-nopass-cover {
            width: 100%;
            height: 100%;
            background-color: #f5f5f5;
            position: absolute;
            opacity: 0.45;
        }

        .table-nopass-content {
            width: 0;
            height: 100%;
            position: absolute;
            right: 0;
            border: 1px solid #ddd;
            background-color: #ffffff;
            transition: all 0.3s ease;
            overflow: auto;
        }

        }
        .table-nopass-container.show {
            width: 100%;

        .table-nopass-content {
            width: 600px;
        }

        }
        }
        .score-footer {
            display: flex;
            padding-top: 16px;
            gap: 16px;
            justify-content: space-between;

        .score-warning-btn {
            height: 36px;
            padding: 0px 15px 0px 39px;
            border-radius: 4px;
            border: 1px solid red;
            background-color: #ffffff;
            box-sizing: border-box;
            color: red;
            text-align: left;
            cursor: pointer;
            white-space: nowrap;
            display: flex;
            align-items: center;
            position: relative;

        img {
            position: absolute;
            left: 12px;
        }

        }
        .score-warning-btn:hover {
            color: #4285f4;
            border: 1px solid #4285f4;
        }

        .score-warning-tips {
            flex: 1;
            display: flex;
            align-items: center;
        }

        }
        .score-submit {
            display: flex;
            justify-content: flex-end;
            gap: 32px;

        button {
            padding: 0 15px;
            height: 32px;
            line-height: 32px;
        }

        .submitTemporaryBtn {
            border: 1px solid red;
            background-color: red;
            box-sizing: border-box;
            color: #ffffff;
            width: 90px;
            height: 36px;
            border-radius: 4px;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 6px;
            cursor: pointer;
        }

        .submitBtn {
            border: 1px solid #367bc0;
            background-color: #367bc0;
            box-sizing: border-box;
            color: #ffffff;
            height: 36px;
            width: 90px;
            border-radius: 4px;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 6px;
            cursor: pointer;
        }

        .submitBtn::before {
            content: '\2713'; /* Unicode 字符，代表 √ */
            font-size: 16px;
            color: #ffffff;
        }

        }

        .new-modal {

        .new-modal-cancel {
            border: 1px solid red;
            background-color: red;
            box-sizing: border-box;
            color: #ffffff;
            height: 36px;
            width: 90px;
            border-radius: 4px;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 6px;
            cursor: pointer;
        }

        .new-modal-cancel::before {
            content: '\2716'; /* Unicode 字符，代表 √ */
            font-size: 16px;
            color: #ffffff;
        }

        .new-modal-submit {
            border: 1px solid #367bc0;
            background-color: #367bc0;
            box-sizing: border-box;
            color: #ffffff;
            height: 36px;
            width: 90px;
            border-radius: 4px;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 6px;
            cursor: pointer;
        }

        .new-modal-submit::before {
            content: '\2713'; /* Unicode 字符，代表 √ */
            font-size: 16px;
            color: #ffffff;
        }

        }

        .panel-body {
            height: auto !important;
            min-height: 500px !important;
        }

        .no-close .ui-dialog-titlebar-close {
            display: none;
        }

        .from_gaozhi {
            border-bottom-width: 1px;
            border-bottom-style: solid;
            border-bottom-color: #CCC;
            border-top-width: 1px;
            border-right-width: 1px;
            border-left-width: 1px;
            border-top-style: solid;
            border-right-style: solid;
            border-left-style: solid;
            border-top-color: #FFF;
            border-right-color: #FFF;
            border-left-color: #FFF;
            width: 100%;
            font-size: 14px;
        }

        #span1 {
            display: block;
            font-size: 18px;
            max-height: 100px;
            overflow: auto;
            font-weight: 400;
            color: #333333;
        }

        .panel-title {
            font-size: 18px;
            margin-left: 10px;
        }

        .error {
            border: 1px solid red;
        }

        .center_list {
            background: #ffffff;
            width: 100%;
            left: 0;
            margin: 0;
        }

        .panel-tool a {
            display: none;
        }

        select.bs-select-hidden, select.selectpicker {
            display: inline !important;
        }

        .ismycheck {
            margin: 0 10px !important;
        }

        /*评分明细模块结束*/
        /*智能识别结果模块开始*/
        /*.mark_content {*/
        /*	height: 650px;*/
        /*	overflow-y: scroll;*/
        /*}*/

        /* 基础重置和字体 */
        .markdown-body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif;
            font-size: 16px;
            line-height: 1.6;
            word-wrap: break-word;
            /*max-width: 800px;*/
            margin: 0 auto;
            padding: 20px;
            height: 850px;
            overflow-y: scroll;
        }

        /* 标题样式 */
        .markdown-body h1,
        .markdown-body h2,
        .markdown-body h3,
        .markdown-body h4,
        .markdown-body h5,
        .markdown-body h6 {
            margin-top: 24px;
            margin-bottom: 16px;
            font-weight: 600;
            line-height: 1.25;
        }

        .markdown-body h1 {
            font-size: 2em;
            border-bottom: 1px solid #eaecef;
            padding-bottom: 0.3em;
        }

        .markdown-body h2 {
            font-size: 1.5em;
            border-bottom: 1px solid #eaecef;
            padding-bottom: 0.3em;
        }

        /* 段落和文本 */
        .markdown-body p {
            margin-top: 0;
            margin-bottom: 16px;
        }

        .markdown-body strong {
            font-weight: 600;
        }

        .markdown-body em {
            font-style: italic;
        }

        /* 列表 */
        .markdown-body ul,
        .markdown-body ol {
            padding-left: 2em;
            margin-top: 0;
            margin-bottom: 16px;
        }

        .markdown-body li {
            margin-bottom: 0.25em;
        }

        /* 代码 */
        .markdown-body code {
            background-color: rgba(27, 31, 35, 0.05);
            border-radius: 3px;
            padding: 0.2em 0.4em;
            font-family: SFMono-Regular, Consolas, "Liberation Mono", Menlo, monospace;
            font-size: 85%;
        }

        .markdown-body pre {
            background-color: #f6f8fa;
            border-radius: 3px;
            padding: 16px;
            overflow: auto;
            line-height: 1.45;
        }

        .markdown-body pre code {
            background-color: transparent;
            padding: 0;
            border-radius: 0;
        }

        /* 表格 */
        .markdown-body table {
            border-collapse: collapse;
            margin-bottom: 16px;
            width: 100%;
        }

        .markdown-body th,
        .markdown-body td {
            padding: 6px 13px;
            border: 1px solid #dfe2e5;
        }

        .markdown-body th {
            font-weight: 600;
            background-color: #f6f8fa;
        }

        .markdown-body table tr > *:nth-child(1) {
            width: 10%;
        }

        /* 引用 */
        .markdown-body blockquote {
            padding: 0 1em;
            color: #6a737d;
            border-left: 0.25em solid #dfe2e5;
            margin: 0 0 16px 0;
        }

        /* 链接 */
        .markdown-body a {
            color: #0366d6;
            text-decoration: none;
        }

        .markdown-body a:hover {
            text-decoration: underline;
        }

        /* 图片 */
        .markdown-body img {
            max-width: 100%;
            box-sizing: initial;
            background-color: #fff;
        }

        /*智能识别结果模块结束*/
        /*重构样式结束*/
        /*抽屉样式开始*/
        .drawer_wrap {
            position: fixed;
            top: 0;
            right: 0;
            bottom: 0;
            width: 50%;
            background: #fff;
            box-shadow: -2px 0 8px rgba(0, 0, 0, 0.15);
            z-index: 1000;
            transform: translateX(100%);
            transition: transform 0.3s ease-in-out;
        }

        .drawer_wrap.show {
            transform: translateX(0);
        }

        .drawer_header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 24px;
            border-bottom: 1px solid #e8e8e8;
        }

        .drawer_title {
            display: flex;
            align-items: center;
            font-size: 16px;
            font-weight: 500;
            color: rgba(0, 0, 0, 0.85);
        }

        .pdf_file_name {
            color: rgba(0, 0, 0, 0.85);
            width: 610px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }

        .drawer_close {
            font-size: 16px;
            cursor: pointer;
            color: rgba(0, 0, 0, 0.45);
        }

        .drawer_close:hover {
            color: rgba(0, 0, 0, 0.75);
        }

        .drawer_content {
            padding: 24px;
            height: calc(100% - 57px);
            overflow-y: auto;
        }

        .drawer_mask {
            position: fixed;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            background: rgba(0, 0, 0, 0.45);
            z-index: 999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease-in-out;
        }

        .drawer_mask.show {
            opacity: 1;
            visibility: visible;
        }

        .drawer_tags_wrap {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
        }

        .drawer_tag_item {
            padding: 5px 8px;
            border-radius: 5px;
            background-color: #f4f6f9;
        }

        .drawer_desc_wrap {
            margin-top: 15px;
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .drawer_desc_item {
            display: flex;
            align-items: flex-start;
        }

        .drawer_desc_label {
            width: 100px;
            flex-shrink: 0;
            white-space: nowrap;
        }

        .drawer_desc_value {
            flex: 1;
            word-break: break-all;
            text-align: justify;
            padding-left: 10px;
            color: #8d8d8d;
        }

        .drawer_line_wrap {
            margin-top: 20px;
            position: relative;
        }

        /*.time_line_item {*/
        /*	line-height: 2;*/
        /*	font-size: 16px;*/
        /*}*/
        /*.time_line_item::before {*/
        /*	display: inline-block;*/
        /*	content: '';*/
        /*	width: 8px;*/
        /*	height: 8px;*/
        /*	background-color: #000;*/
        /*	border-radius: 50%;*/
        /*	margin-right: 8px;*/
        /*}*/

        .timeline-item {
            position: relative;
            padding-left: 26px;
            margin-bottom: 10px;
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            left: 6px;
            top: 23px;
            bottom: -10px;
            width: 1px;
            border-left: 1px dashed #e8e8e8;
            background-color: transparent;
        }

        .timeline-item:last-child::before {
            display: none;
        }

        .timeline-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .timeline-dot {
            position: absolute;
            left: 0;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #fff;
            border: 2px solid #1890ff;
        }

        .timeline-title {
            display: flex;
            align-items: center;
        }

        .timeline-title .title {
            font-size: 16px;
            font-weight: bold;
            color: #367bc0;
            margin-right: 12px;
        }

        .timeline-title .time {
            color: #999;
            font-size: 14px;
        }

        .timeline-content {
            background-color: #f8f9fa;
            border-radius: 4px;
            padding: 12px;
        }

        .detail-item {
            display: flex;
            margin-bottom: 8px;
        }

        .detail-item:last-child {
            margin-bottom: 0;
        }

        .page-wrapper {
            position: relative;
            margin-bottom: 20px;
        }

        .pdf-highlight-active {
            pointer-events: none; /* 防止高亮层拦截事件 */
            box-sizing: border-box;
        }

        /* PDF 高亮样式 */
        .text-layer {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            pointer-events: none; /* 允许点击穿透到下层 */
            /*transform: none !important;*/
        }

        .pdf-highlight-active {
            pointer-events: none;
            box-sizing: border-box;
        }

        .pdf-highlight {
            position: absolute;
            z-index: 10;
            pointer-events: none;
        }

        /* Markdown 内容样式 */
        .detail-item {
            padding: 8px;
            margin: 4px 0;
            border-left: 3px solid transparent;
        }

        .clickable-item, .clickable-cell {
            cursor: pointer;
        }

        .clickable-item:hover, .clickable-cell:hover {
            background-color: #f5f5f5;
        }

        .highlight-layer {
            position: absolute;
            top: 0;
            left: 0;
            pointer-events: none;
            z-index: 10;
        }

        .pdf-highlight {
            position: absolute;
            background-color: red;
            border: 2px solid red;
            border-radius: 3px;
            pointer-events: none;
            transition: all 0.2s ease;
        }

        .text-layer > span {
            color: transparent;
            position: absolute;
            white-space: pre;
            cursor: text;
            transform-origin: 0% 0%;
        }

        .text-layer .highlight {
            background-color: rgba(255, 255, 0, 0.3);
            border-radius: 4px;
        }

        .text-layer .highlight.applied {
            background-color: rgba(255, 0, 0, 0.3);
        }

        .detail-time {
            color: #333;
            font-size: 14px;
            margin-right: 12px;
            min-width: 100px;
        }

        .detail-desc {
            color: #333;
            font-size: 14px;
        }

        /*抽屉样式结束*/

    </style>
    <%--	<link rel="stylesheet" href="styles.css" />--%>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css"/>
    <!-- Font Awesome -->
</head>
<script type="text/javascript">
    $(".frame_left").hide();
    var pageNum = $("#pageNum").val();
    var scoringIndexList = null;
    var expertFileId = $("#expertFileId").val();
    var problemRemark = $("#problemRemark").val();
    var pdfFileName = null;
    var pdfDownloadUrl = '';
    var pdfUrl = '${fastDFS}';
    var scoredstate = $("#scoredstate").val();
    //加分项表单数据
    var scoringPlusesList = null;

    console.log('获取加分项数据，scoredstate=',scoredstate,',expertFileId=', expertFileId);
    //获取加分项数据
    $.ajax({
        cache: true,
        type: "GET",
        async: false,
        url: WEBPATH + '/zjpf/getPlusesData.do',
        data: {
            id: expertFileId
        },
        success: (res) => {
            console.log('获取加分项数据成功', res);
            if (res.code === '200' && res.result === 'success') {
                if (res.data && res.data.error) {
                    console.error('获取加分项数据错误：', res.data.error);
                    layer.msg('获取加分项数据错误：' + res.data.error);
                } else {
                    this.scoringPlusesList = res.data;

                }
            } else {
                console.error('获取加分项数据失败：', res.message);
                layer.msg('获取加分项数据失败：' + res.message);
            }
        },
        error: (err) => {
            console.error('获取加分项数据失败', err);
            swal("错误!", "获取加分项数据失败！", "error");
        }
    });

    //获取卷面数据
    // $.ajax({
    //     cache: true,
    //     type: "GET",
    //     async: false,
    //     //api/test/detail/behaviour/74
    //     url: WEBPATH + "/zjpf/getIndexList.do",
    //     data: {
    //         id: expertFileId
    //     },
    //     error: function (request) {
    //         swal("错误!", "请求异常！", "error");
    //     },
    //     success: function (data) {
    //         if (data.result == 'success') {
    //             scoringIndexList = data.data;
    //             scoringIndexList.problemRemark = problemRemark;
    //             pdfFileName = data.data.filecode
    //             pdfDownloadUrl = data.data.downUrl
    //             for (var i = 0; i < data.data.expertHandlIndexScoreList.length; i++) {
    //                 data.data.expertHandlIndexScoreList[i].indexname = data.data.expertHandlIndexScoreList[i].indexname + "(" + data.data.expertHandlIndexScoreList[i].indexscore + "分)";
    //                 data.data.expertHandlIndexScoreList[i].className = 'class' + data.data.expertHandlIndexScoreList[i].id;
    //                 var index = data.data.expertHandlIndexScoreList[i];
    //                 //先创建好select里面的option元素
    //                 var option = document.createElement("option");
    //                 //转换DOM对象为JQ对象,好用JQ里面提供的方法 给option的value赋值
    //                 $(option).val('class' + index.id);
    //                 $(option).text(index.indexname);
    //                 $("#indexName").append(option);
    //             }
    //             console.log("页面数据", scoringIndexList);
    //         }
    //     }
    // });

    //多选下拉框所在的div
    var selecteddiv = document.getElementById("selectdiv");
    var problemRemark = $("#problemRemark").val();

    //鼠标是否在【多选下拉框div】上面（如果在div上面，需要控制鼠标的点击事件，不让div隐藏；否则要让该div隐藏）
    var indiv = false;

    //选中的股票代码（需要传到后台的参数）
    var selectedlist = [];
    //选中的股票名称（展示在前台给业务员看的）
    var selectednamelist = [];

    //系统初始化函数
    $(function () {
        $('#my_dialog').dialog({
            width: "900",
            autoOpen: false,
            resizable: false,
            modal: true,
            closed: true
        });
        $(".disNo").removeAttr("disabled");
        /*查看争议异常的弹窗*/
        $('#error_dialog').dialog({
            title: "查看争议",
            width: "900",
            height: "450",
            autoOpen: false,
            resizable: false,
            modal: true,
            closed: true
        });
    });

    //关闭查看异常的窗口
    function entity_error_close() {
        $('#error_dialog').dialog("close")
        //清空具体描述的单选框
        $('#caseInfo').val('');
        //清空专家否决选项
        $('#itemOption_entity').val('');
        //清空专家否决描述
        $('#itemComme_entity').val('');
        $('#isAOrB_entity').val('');
        $('#entity_error_id').val('');
        $('#error-message').text('');
        document.getElementById('isAgree_entity_0').disabled = false;
        document.getElementById('isAgree_entity_1').disabled = false;
        document.getElementById('caseInfo').readOnly = false;
        //清空是否认同单选框
        $('input[name="isAgree_entity"]').prop('checked', false);
    }

    //异常窗口的 确认提交 按钮
    function error_submit() {
        //是否认可:0不认可,1认可
        var isAgree = $('input[name=isAgree_entity]:checked').val();
        //是否认可的具体情形
        var caseInfo = $("#caseInfo").val();
        var isAOrB = $("#isAOrB_entity").val();
        var id = $("#entity_error_id").val();

        console.log("是否认可:", isAgree, ",描述:", caseInfo)
        if (isAgree == null || isAgree == undefined) {
            swal("信息", "【是否认同】不能为空", "error");
            return false;
        }

        //调保存接口,将是否认可和具体情形存到库里
        $.ajax({
            type: "post",
            url: WEBPATH + '/zjpf/saveErrorIsAgree.do',
            data: {
                isAgree: isAgree,
                caseInfo: caseInfo,
                isAOrB: isAOrB,
                id: id
            },
            success: function (data) {
                console.log(data)
                if (data.result == "success") {
                    entity_error_close();
                } else {
                    swal("信息", data.message, "error");
                }
            }
        });
    }

    // 鼠标点击事件，如果点击在 selectedbutton，或者是在多选框div中的点击事件，不作处理。其他情况的点击事件，将多选空div隐藏
    document.onclick = function (event) {
        if (event.target.id == "commeStrThree" || indiv) {
            return;
        }
        selecteddiv.style.display = "none";
        document.getElementById("fuzzysearchdiv").style.display = "none";
    };

    //点击selectButton，展示多选框
    function myclick(target) {
        $("#selectdiv").css("display", "block");
        if (target) {
            $(target).parent().find('.doc-item-list').css("display", "block");
        }
    }

    //鼠标进入多选框的div【selectdiv】
    function mousein(target) {
        indiv = true;
        fn()
        $(target).css("display", "block");
    }

    //鼠标离开多选框的div【selectdiv】
    function mouseout(target) {
        indiv = false;
        fn()
        $(target).css("display", "none");
    }

    function fn() {
        if (indiv) {
            $("#selectdiv").css("display", "block");
        } else {
            $("#selectdiv").css("display", "none");
        }
    }

    //checkbox的点击事件
    function mycheck(obj) {
        console.log("obj", obj)
        var dataValue = obj.getAttribute('data');
        $("#commeStrThree").val("");
        if (obj.checked) {
            // selectedlist.push(obj.value);
            selectedlist.push(dataValue);
            selectednamelist.push(obj.nextSibling.nodeValue);
        } else {
            for (var i = 0; i < selectedlist.length; i++) {
                // if(selectedlist[i] == obj.value){
                if (selectedlist[i] == dataValue) {
                    selectedlist.splice(i, 1);
                    selectednamelist.splice(i, 1);
                }
            }
        }
        // console.log(selectedlist,selectednamelist)
        document.getElementById("commeStrThree").value = selectednamelist;
        obj.parentNode.parentNode.parentNode.querySelector('.commeStrThree').value = selectedlist
        // $('input[name="selectButton"]').val(selectednamelist);
    }

    var mater = '${expertHandlFileList.fileMaterials}'
    if (mater == '1') {
        $("#fileMaterials").attr("checked", true);
    }
    business.listenEnter();
    var scoringEntityList = null;
    //获取实体数据
    $.ajax({
        cache: true,
        type: "GET",
        async: false,
        url: WEBPATH + "/zjpf/getEntityIndexs.do",
        data: {
            id: expertFileId
        },
        error: function (request) {
            swal("错误!", "请求异常！", "error");
        },
        success: function (data) {
            if (data.result == 'success') {
                scoringEntityList = data.data;
                scoringEntityList.problemRemark = problemRemark;
                selecteddiv.innerHTML = "";
                if (scoringEntityList.indexOneList && scoringEntityList.indexOneList.length > 0) {
                    for (var i = 0; i < scoringEntityList.indexOneList.length; i++) {
                        var tmpdiv = document.createElement("div");
                        var tmpinput = document.createElement("input");
                        var str = scoringEntityList.indexOneList[i];
                        tmpinput.setAttribute("name", "mycheckbox");
                        tmpinput.setAttribute("class", "ismycheck");
                        tmpinput.setAttribute("type", "checkbox");
                        tmpinput.setAttribute("onclick", "mycheck(this)");
                        tmpinput.setAttribute("value", str);
                        tmpinput.setAttribute("data", str);
                        var tmptext = document.createTextNode(scoringEntityList.indexOneList[i], scoringEntityList.indexOneList[i].length);
                        tmpdiv.appendChild(tmpinput);
                        tmpdiv.appendChild(tmptext);
                        selecteddiv.appendChild(tmpdiv);
                    }
                }
            }
        }
    });

    function showInfoModal(type) {
        if (type != 'entity') {
            const infoModal = document.getElementById("infoModal");
            infoModal.style.display = "block";
        } else {
            const infoModal = document.getElementById("infoEntityModal");
            infoModal.style.display = "block";
        }
    }

    function closeInfoModal(type) {
        if (type != 'entity') {
            const infoModal = document.getElementById("infoModal");
            infoModal.style.display = "none";
        } else {
            const infoModal = document.getElementById("infoEntityModal");
            infoModal.style.display = "none";
        }
    }

    // 点击模态框外部也能关闭模态框
    window.onclick = function (event) {
        const infoModal = document.getElementById("infoModal");
        const infoEntityModal = document.getElementById("infoEntityModal");
        if (event.target === infoModal || event.target === infoEntityModal) {
            infoModal.style.display = "none";
            infoEntityModal.style.display = "none";
        }
    };

    // 显示模态框
    function showModal(content) {
        const modal = document.getElementById("AiModal");
        const modalText = document.getElementById("fullText");
        modalText.innerHTML = content.innerHTML; // 将标签内容放入模态框，使用 innerHTML
        modal.style.display = "block"; // 显示模态框
    }

    // 关闭模态框
    function closeModal() {
        const modal = document.getElementById("AiModal");
        modal.style.display = "none"; // 隐藏模态框
    }

    // 点击疑似问题
    // function handleSuspectedIssue(e, data) {
    // 	console.log('e',e)
    // 	console.log('data',data)
    // }
</script>
<script type="text/javascript">
    let myVue = new Vue({
        el: "#zjpfVueTemplate",
        components: {
            // "pdf-preview": pdfPreviewComponent,
            // "ied-component": iedComponent
        },
        data: {
            tabCurrentIndex: "1",
            tabList: [
                // {
                // 	id: "0",
                // 	tabName: "智能评查详情",
                // },
                {
                    id: "1",
                    tabName: "评分明细",
                },
                {
                    id: "2",
                    tabName: "智能识别结果",
                },
            ],
            // 根据scoredstate设置默认显示的评查类型
            // 当scoredstate==0时，默认显示实体评查(entity)
            // 当scoredstate==10时，显示加分项(pluses)
            // 其他情况保持原有逻辑
            reviewBtnCurrent: (scoredstate == '0' || scoredstate == '3' || scoredstate == '4') ? 'entity' :
                             (scoredstate == '10') ? 'pluses' : 'rollSurface',
            reviewBtnList: [
                {
                    id: "rollSurface",
                    btnName: "规范性评查",
                },
                {
                    id: "entity",
                    btnName: "合法性评查",
                },
                {
                    id: "pluses",
                    btnName: "加分项",
                },
            ],
            pdfFileName: pdfFileName,
            // PDF 文档相关状态
            pdfDoc: null,      // PDF 文档对象
            pdfCurrentPage: 1,    // 当前页码
            pdfTotalPages: 0,     // 总页数
            pdfCurrentScale: 1.1, // 当前缩放比例
            pdfUrl: pdfUrl,
            isPdfViewLoading: false,  // 加载状态
            pdfLoadingText: '加载中...', // 加载提示文本
            processingStatus: '处理数据中...',
            showZoomLevel: false,    // 是否显示缩放比例提示
            isScrollingProgrammatically: false, // 是否为程序触发的滚动
            pdfScrollTimeout: null,                 // 滚动节流定时器
            scaleOptions: [
                {value: 0.5, label: '50%'},
                {value: 0.75, label: '75%'},
                {value: 1, label: '100%'},
                {value: 1.1, label: '110%'},
                {value: 1.25, label: '125%'},
                {value: 1.5, label: '150%'},
                {value: 2, label: '200%'}
            ],
            // 智能识别结果
            markdownText: '',
            // 智能评查详情数据
            intelligentEvaluationDetailsList: [
                {
                    title: "1.立案审批表",
                    scoreNote: "(0.5分)",
                    detail: [
                        {
                            label: "文书页码",
                            value: "该文书在材料第一页共4页具体以人工核对为准",
                        },
                        {
                            label: "案件来源",
                            value: "涉嫌使用排放不合格的非道路移动机械案",
                        },
                    ],
                },
            ],
            drawerVisible: false,
            // 案卷文号
            recordsNumber: '济环罚字【2024】GC第033号',
            recordsTags: [
                // "0" waring; "1" dangerous
                {
                    id: '0',
                    type: '0',
                    tagName: '一般处罚'
                },
                {
                    id: '1',
                    type: '1',
                    tagName: '法律适用错误'
                },
                {
                    id: '2',
                    type: '1',
                    tagName: '重大合法性问题'
                }

            ],
            // 案卷描述
            recordsDescList: [
                {
                    key: 'lawObjectName',
                    label: '当事人名称',
                    value: ''
                },
                {
                    key: 'litigantManType',
                    label: '当事人类型',
                    value: ''
                },
                {
                    key: 'caseIntro',
                    label: '处罚案由',
                    value: ''
                },
                {
                    key: 'punishSubjectName',
                    label: '执法主体',
                    value: ''
                },
                {
                    key: 'caseIntro',
                    label: '违法行为',
                    value: ''
                },
                {
                    key: 'lawTerm',
                    label: '违法条款',
                    value: ''
                },
                {
                    key: 'lawResp',
                    label: '处罚依据',
                    value: ''
                },
                {
                    key: 'punishType',
                    label: '处罚种类',
                    value: ''
                },
                {
                    key: 'lawFacts',
                    label: '违法事实证据',
                    value: ''
                }
            ],
            // 案卷详情时间轴
            recordsTimeLineList: [],
            timeAxisData: null,
            testNumber: 5,
            scoringIndexList: scoringIndexList,
            scoringEntityList: scoringEntityList,
            scoringPlusesList: scoringPlusesList,
            totalAiScore: 0,
            scrollLeft: 0,
            scrollLeftEntity: 0,
            scrollTop: 0,
            scrollTopEntity: 0,
            activeId: "",
            activeIdEntity: "",
            expandStatus: true,
            expandEntityStatus: true,
            hoverId: '',
            suspectedIssueTitle: '',
            nopassDrawItem: {},
            textContentItems: [], // 显式初始化
            currentEditIndex: null,
            currentEditItemIndex: null,
            nopassDrawItem: {
                itemname:'',
                commeStrOne: '',
                commeStrTwo: '',
                commeStrThree: '',
                commeStrFive: '',
                commeStrSix: '',
                commeStrEight: ''
            }
        },
        mounted() {
            //计算总分
            this.sumScore();
            //ai智能识别赋值
            // this.aiInfoColor();//20250709优秀案卷需求注释
            this.initAi();
            this.sumScoreEntity();
            this.initAiEntity();
            this.initData()
            this.initPdfPreview()
            // // 初始化加分项默认选择"不符合"
            // this.initPlusesDefault();
            const container = this.$refs['pdfViewContainer']
            if (container) {
                container.addEventListener('scroll', this.handlePdfPreScroll)
            }
        },
        computed: {
            //总分
            totalScore: function () {
                var sum = 0;
                this.scoringIndexList?.expertHandlIndexScoreList?.forEach(function (item) {
                    //inCheckValue无需评查,修改为无需评查默认满分了
                    // if(item.inCheckValue != 1) {
                    // }

                    if (item.indexname == null || item.indexname == "" || item.indexname == '加分项' || (this.scoringIndexList.closed == 0 ? item.indexname.includes('结案审批表') : false) || item.isOperator == '0') {
                        sum += 0;
                    } else {
                        sum += item.indexscore;
                    }
                });
                return sum;
            },
            //输入分数
            inputScore: function () {

                var inpuSco = 0;
                this.scoringIndexList?.expertHandlIndexScoreList?.forEach(function (item) {
                    if (item.indexname == null || item.indexname == "" /*|| item.indexname == '加分项'*/ || item.isOperator == '0') {
                        inpuSco += 0;
                    } else if (item.resultScore != '') {
                        inpuSco += item.resultScore;
                    }

                });
                if (inpuSco == null || inpuSco == "" || inpuSco == 0) {
                    return 0.00;
                } else {
                    return parseFloat(inpuSco).toFixed(2);
                }
            },
            //实体程序评查-存在问题数
            nopassNum: function () {
                let count = 0;
                this.scoringEntityList?.expertHandlIndexScoreList?.forEach(function (item) {
                    if (item.expertHandlItemScoreList && item.expertHandlItemScoreList.length > 0) {
                        item.expertHandlItemScoreList.forEach(function (v) {
                            if (v.score == 1) {
                                count++;
                            }
                        })
                    }
                });
                return count;
            },
            //实体程序评查-不存在问题数
            passNum: function () {
                let count = 0;
                this.scoringEntityList?.expertHandlIndexScoreList?.forEach(function (item) {
                    if (item.expertHandlItemScoreList && item.expertHandlItemScoreList.length > 0) {
                        item.expertHandlItemScoreList.forEach(function (v) {
                            if (v.score == 0) {
                                count++;
                            }
                        })
                    }
                });
                return count;
            },
            //实体程序评查-未评查
            pendingNum: function () {
                let count = 0;
                this.scoringEntityList?.expertHandlIndexScoreList?.forEach(function (item) {
                    if (item.expertHandlItemScoreList && item.expertHandlItemScoreList.length > 0) {
                        item.expertHandlItemScoreList.forEach(function (v) {
                            if (![0, 1, '0', '1'].includes(v.score)) {
                                count++;
                            }
                        })
                    }
                });
                return count;
            },
            //实体程序评查-总分
            totalScoreEntity: function () {
                let count = 50;
                if (this.pendingNum != 0 || this.nopassNum != 0) {
                    count = 0;
                }
                return count;
            },
            //加分项-选择数量
            plusesSelectedNum: function () {
                let count = 0;
                if (this.scoringPlusesList && this.scoringPlusesList.expertHandlIndexScoreList) {
                    this.scoringPlusesList.expertHandlIndexScoreList.forEach(function (item) {
                        if (item.expertHandlItemScoreList && item.expertHandlItemScoreList.length > 0) {
                            item.expertHandlItemScoreList.forEach(function (v) {
                                if (v.score == "1") {
                                    count++;
                                }
                            })
                        }
                    });
                }
                return count;
            },
            //加分项-总分数
            plusesTotalScore: function () {
                let totalScore = 0;
                if (this.scoringPlusesList && this.scoringPlusesList.expertHandlIndexScoreList) {
                    // 遍历每个大项
                    this.scoringPlusesList.expertHandlIndexScoreList.forEach(function (item, index) {
                        let majorItemScore = 0; // 当前大项得分
                        if (item.expertHandlItemScoreList && item.expertHandlItemScoreList.length > 0) {
                            // 计算当前大项下所有选择"符合"的小项数量
                            item.expertHandlItemScoreList.forEach(function (v) {
                                if (v.score == "1") {
                                    majorItemScore++;
                                }
                            });
                        }
                        // 每个大项最多5分
                        majorItemScore = Math.min(majorItemScore, 5);
                        totalScore += majorItemScore;

                        // 调试信息
                        if (majorItemScore > 0) {
                            console.log(`加分项大项${index + 1}得分: ${majorItemScore}分`);
                        }
                    });
                }
                // 总分最高10分限制
                totalScore = Math.min(totalScore, 10);
                console.log(`加分项总分: ${totalScore}分`);
                return totalScore;
            },
            //案卷总分数计算 - paperScore + entityScore + plusesScore
            totalCaseScore: function () {
                // 获取规范性评查分数(卷面分)
                let paperScore = parseFloat(this.scoringIndexList?.paperscore) || 0;

                // 获取合法性评查分数(实体分)
                let entityScore = parseFloat(this.scoringEntityList?.entityscore) || 0;

                // 获取加分项分数
                let plusesScore = this.plusesTotalScore || 0;

                // 计算总分
                let totalScore = paperScore + entityScore + plusesScore;

                console.log(`案卷总分数计算: ${paperScore}(卷面分) + ${entityScore}(实体分) + ${plusesScore}(加分项) = ${totalScore.toFixed(2)}`);
                return totalScore.toFixed(2);
            }
        },
        beforeDestroy() {
            const container = this.$refs['pdfViewContainer']
            if (container) {
                container.removeEventListener('scroll', this.handlePdfPreScroll)
            }
        },
        methods: {
            // 初始化查询数据
            initData() {
                this.activeId = this.scoringIndexList?.expertHandlIndexScoreList ? this.scoringIndexList.expertHandlIndexScoreList[0].id : ""
                this.activeIdEntity = this.scoringEntityList?.expertHandlIndexScoreList ? this.scoringEntityList.expertHandlIndexScoreList[0].id : ""
            },
            // 初始化加分项默认选择"不符合"
            // initPlusesDefault() {
            //     console.log('初始化加分项默认选择"不符合"');
            //     // 当页面是加分项时，为所有未设置值的评分项设置默认值为"不符合"
            //     if (this.reviewBtnCurrent === 'pluses') {
            //         if (this.scoringEntityList && this.scoringEntityList.expertHandlIndexScoreList) {
            //             console.log('加分项列表数量：', this.scoringEntityList.expertHandlIndexScoreList.length);
            //             try {
            //                 this.scoringEntityList.expertHandlIndexScoreList.forEach((scoringIndex, index) => {
            //                     if (scoringIndex.expertHandlItemScoreList && scoringIndex.expertHandlItemScoreList.length > 0) {
            //                         scoringIndex.expertHandlItemScoreList.forEach((scoringItem, index1) => {
            //                             if (scoringItem.score === null || scoringItem.score === undefined || scoringItem.score === '') {
            //                                 console.log('设置加分项默认值为"不符合"', index, index1);
            //                                 scoringItem.score = "0"; // 设置为"不符合"
            //                                 try {
            //                                     // 调用更新方法
            //                                     this.updateItemEntity(index, index1, scoringItem.itemscore, scoringItem.temItemId);
            //                                 } catch (err) {
            //                                     console.error('调用updateItemEntity方法出错', err);
            //                                 }
            //                             }
            //                         });
            //                     }
            //                 });
            //             } catch (err) {
            //                 console.error('初始化加分项默认值出错', err);
            //             }
            //         } else {
            //             console.warn('没有找到加分项列表或列表为空');
            //         }
            //     } else {
            //         console.log('当前不是加分项页面，不需要初始化默认值');
            //     }
            // },
            handlePreview() {
                this.drawerVisible = true;
                $.ajax({
                    cache: true,
                    type: 'GET',
                    async: false,
                    url: WEBPATH + '/caseView/queryCaseOverviewByFileId.do',
                    data: {
                        id: $("#fileId").val()
                    },
                    error: function (error) {
                        swal('错误，请求异常！')
                    },
                    success: (data) => {
                        if (data.code === 'success') {
                            this.recordsDescList.forEach(item => {
                                if (data.data[item.key]) {
                                    item.value = data.data[item.key]
                                }
                            })
                            const parseData = JSON.parse(data.data.timeAxis)
                            if (!parseData.length) return
                            parseData.sort((a, b) => a.sort - b.sort)
                            parseData.forEach(item => {
                                if (item.child && !item.child.length) {
                                    item.child.sort((a, b) => a.sort - b.sort)
                                }
                            })
                            parseData.forEach(item => {
                                if (item.child.length > 0) {
                                    if (item.child[0].text === '未识别具体日期') {
                                        item['titleTime'] = ''
                                    } else {
                                        const firstArr = item.child[0].text.split('、')[0]
                                        const dateStr = firstArr.substring(0, firstArr.indexOf('日') + 1)
                                        item['titleTime'] = dateStr
                                    }

                                } else {
                                    item['titleTime'] = ''
                                }
                            })
                            this.recordsTimeLineList = parseData
                        }
                    }

                })

            },
            handleCloseDrawer() {
                this.drawerVisible = false;
            },
            // tab切换
            handleClickTap(tabIndex) {
                this.tabCurrentIndex = tabIndex;
                if (tabIndex === "0") {
                    // 初始化智能评查详情页面
                    this.initintelligentEvaluationDetailRender();
                }
                if (tabIndex === '2') {
                    this.loadMarkdown()
                }
            },
            // 卷面/实体 评查切换
            handleCLickReviewBtn(id) {
                this.reviewBtnCurrent = id;
            },

            // 智能评查详情页面渲染
            initintelligentEvaluationDetailRender() {
                const tbody = document.querySelector(".ied_table tbody");
            },
            async initPdfPreview() {
                await this.loadPdf()
            },
            async loadPdf() {
                this.isPdfViewLoading = true;
                this.pdfLoadingText = '加载中...';
                try {
                    const loadingTask = pdfjsLib.getDocument(this.pdfUrl)
                    <%--const loadingTask = pdfjsLib.getDocument('${webpath}/static/pdf/test.pdf')--%>
                    loadingTask.onProgress = (progress) => {
                        const percent = Math.round((progress.loaded / progress.total) * 100)
                        this.pdfLoadingText = `加载中...${percent}%`
                    }
                    this.pdfDoc = await loadingTask.promise
                    this.pdfTotalPages = this.pdfDoc.numPages
                    console.log('pdfTotalPages', this.pdfTotalPages)
                    await this.renderAllPages()
                } catch (error) {
                    console.error('PDF加载失败', error)
                } finally {
                    this.isPdfViewLoading = false
                }
            },
            // 渲染单个pdf页面
            async renderPage(pageNumber) {
                try {
                    const page = await this.pdfDoc.getPage(pageNumber);
                    const scale = this.pdfCurrentScale
                    const viewport = page.getViewport({scale})
                    let id = 'canvas-' + pageNumber
                    const canvas = document.getElementById(id)
                    const context = canvas.getContext('2d')
                    canvas.width = viewport.width
                    canvas.height = viewport.height
                    await page.render(
                        {
                            canvasContext: context,
                            viewport: viewport
                        }
                    ).promise


                } catch (error) {
                    console.error(`Error rendering page ${pageNumber}:`, error)
                }
            },
            // 渲染所有pdf页面
            async renderAllPages() {
                this.isPdfViewLoading = true
                try {
                    for (let i = 1; i <= this.pdfTotalPages; i++) {
                        let percentage = Math.round((i / this.pdfTotalPages) * 100)
                        this.pdfLoadingText = '加载中...' + percentage + '%'
                        await this.renderPage(i)
                    }
                } finally {
                    this.isPdfViewLoading = false
                }
            },
            // 滚动到指定页面
            scrollToPage(pageNumber) {
                this.isScrollingProgrammatically = true
                const id = 'page-' + pageNumber
                const pageElement = document.getElementById(id)
                if (pageElement) {
                    pageElement.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    })
                    setTimeout(() => {
                        this.isScrollingProgrammatically = false
                    }, 1000)
                }
            },
            /**
             * 上一页
             */
            prevPage() {
                if (this.pdfCurrentPage > 1) {
                    this.pdfCurrentPage--
                    this.scrollToPage(this.pdfCurrentPage)
                }
            },

            /**
             * 下一页
             */
            nextPage() {
                if (this.pdfCurrentPage < this.pdfTotalPages) {
                    this.pdfCurrentPage++
                    this.scrollToPage(this.pdfCurrentPage)
                }
            },
            // 页码变动
            handlePageInput() {
                if (!this.pdfDoc || this.isPdfViewLoading) return;

                // 确保输入的值在有效范围内
                const newPage = Math.max(1, Math.min(parseInt(this.pdfCurrentPage) || 1, this.pdfTotalPages))
                this.pdfCurrentPage = newPage;

                // 滚动到目标页面
                this.scrollToPage(newPage);
            },
            /**
             * 监听滚动事件，更新当前页码
             */
            handlePdfPreScroll() {
                if (this.isScrollingProgrammatically) return;

                clearTimeout(this.pdfScrollTimeout);
                this.pdfScrollTimeout = setTimeout(() => {
                    const container = this.$refs['pdfViewContainer'];
                    if (!container) return;

                    const containerRect = container.getBoundingClientRect();
                    const containerTop = containerRect.top;
                    const containerHeight = containerRect.height;
                    const containerCenter = containerTop + containerHeight / 2;

                    // 获取所有页面元素
                    const pages = container.querySelectorAll('.page-wrapper');
                    let visiblePage = 1;
                    let minDistance = Infinity;

                    // 遍历所有页面，找到最接近容器中心的页面
                    pages.forEach((page, index) => {
                        const pageRect = page.getBoundingClientRect();
                        const pageCenter = pageRect.top + pageRect.height / 2;

                        // 计算页面中心到容器中心的距离
                        const distance = Math.abs(pageCenter - containerCenter);
                        if (distance < minDistance) {
                            minDistance = distance;
                            visiblePage = index + 1;
                        }
                    });

                    // 更新页码
                    if (visiblePage !== this.pdfCurrentPage) {
                        this.pdfCurrentPage = visiblePage;
                    }
                }, 100);
            },
            /**
             * 放大
             */
            zoomIn() {
                if (this.pdfCurrentScale < 2) {
                    const currentScaleOptionIndex = this.scaleOptions.findIndex(item => item.value === this.pdfCurrentScale)
                    // 排除最后一项
                    if (currentScaleOptionIndex !== this.scaleOptions.length - 1) {
                        this.pdfCurrentScale = this.scaleOptions[currentScaleOptionIndex + 1].value
                    }
                    this.renderAllPages();
                }
            },

            /**
             * 缩小
             */
            zoomOut() {
                if (this.pdfCurrentScale > 0.5) {
                    const currentScaleOptionIndex = this.scaleOptions.findIndex(item => item.value === this.pdfCurrentScale)
                    // 排除第一项
                    if (currentScaleOptionIndex !== 0) {
                        this.pdfCurrentScale = this.scaleOptions[currentScaleOptionIndex - 1].value
                    }
                    this.renderAllPages();
                }
            },
            // 下载
            async handlePdfFileDownload() {
                if (!this.pdfUrl) {
                    swal('案卷加载异常，无法下载！')
                    return
                }

                try {
                    const response = await fetch(this.pdfUrl, {
                        method: 'GET',
                        headers: {
                            // 如果需要token或者其他header，可以在这里添加
                        }
                    });
                    if (!response.ok) {
                        throw new Error('文件下载失败');
                    }
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);

                    const link = document.createElement('a');
                    link.href = url;
                    link.download = this.pdfFileName || 'download.pdf';
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);

                    window.URL.revokeObjectURL(url); // 释放内存

                } catch (error) {
                    swal('下载失败：' + error.message);
                }
            },


            /**
             * 标签背景颜色
             */
            tagColorObject(tag) {
                if (!tag || !tag.type) return
                const colorMap = {
                    "0": "#367bc0",
                    "1": "#ff4d4f"
                }
                return {
                    color: colorMap[tag.type]
                }
            },
            /**
             * 智能识别结果渲染
             */
            loadMarkdown() {
                const that = this
                $.ajax({
                    cache: true,
                    type: 'GET',
                    async: true,
                    url: WEBPATH + '/ocr/zjpf/getDocMarkdwonInfo.do',
                    data: {
                        id: $("#fileId").val()
                    },
                    error: function (error) {
                        swal('请求错误！')
                    },
                    success: function (data) {
                        if (data.result === 'success') {
                            that.$nextTick(() => {
                                console.log(data.data, '11111')
                                const details = data.data
                                const container = document.getElementById('markdown_content')
                                container.className = 'markdown-body'
                                container.innerHTML = ''
                                details.forEach(detail => {
                                    const detailDiv = document.createElement('div')
                                    detailDiv.className = 'detail-item'
                                    detailDiv.innerHTML = marked.parse(detail.textContent || '')

                                    // 添加data属性便于后续查找
                                    detailDiv.dataset.pageId = detail.pageId;
                                    detailDiv.dataset.position = JSON.stringify(detail.position);

                                    if (detail.cells && Array.isArray(detail.cells)) {
                                        const textElements = detailDiv.querySelectorAll('td');
                                        textElements.forEach(element => {
                                            const matchingCell = detail.cells.find(cell =>
                                                element.textContent.trim() === cell.textContent.trim()
                                            );

                                            if (matchingCell) {
                                                element.style.cursor = 'pointer';
                                                element.classList.add('clickable-cell');

                                                element.addEventListener('click', e => {
                                                    e.stopPropagation();
                                                    that.handleItemClick(detail, matchingCell);
                                                });
                                            }
                                        });
                                        // detail.cells.forEach(cell => {
                                        // const textElements = detailDiv.querySelectorAll('td');

                                        // textElements.forEach(element => {
                                        // 	if(element.textContent.trim() === cell.textContent.trim()) {
                                        // 		element.style.cursor = 'pointer';
                                        // 		element.classList.add('clickable-cell')
                                        //
                                        // 		element.addEventListener('click', e => {
                                        // 			e.stopPropagation()
                                        // 			// 移除之前所有active状态
                                        // 			document.querySelectorAll('.detail-item.active').forEach(item => {
                                        // 				item.classList.remove('active');
                                        // 			});
                                        // 			// 给当前父元素添加active状态
                                        // 			const parentDetail = e.target.closest('.detail-item');
                                        // 			if(parentDetail) {
                                        // 				parentDetail.classList.add('active');
                                        // 			}
                                        // 			// 高亮对应PDF内容
                                        // 			that.highlightText(detail.pageId, detail.position);
                                        //
                                        // 			// 对于表格单元格的特殊处理
                                        // 			element.addEventListener('click', e => {
                                        // 				e.stopPropagation();
                                        //
                                        // 				// 移除之前所有active状态
                                        // 				document.querySelectorAll('.detail-item.active').forEach(item => {
                                        // 					item.classList.remove('active');
                                        // 				});
                                        //
                                        // 				// 给当前父元素添加active状态
                                        // 				const parentDetail = e.target.closest('.detail-item');
                                        // 				if (parentDetail) {
                                        // 					parentDetail.classList.add('active');
                                        // 				}
                                        // 			})
                                        // 				that.scrollToKeyword(detail['pageId'],cell.position)
                                        // 		})
                                        // 	}
                                        // });

                                        // })
                                    } else {
                                        detailDiv.style.cursor = 'pointer';
                                        detailDiv.classList.add('clickable-item')
                                        detailDiv.addEventListener('click', () => {
                                            that.handleItemClick(detail);
                                            // 	// 高亮对应位置
                                            // 	if (detail.position) {
                                            // 		that.highlightText(detail.pageId, detail.position);
                                            // 	}
                                            //
                                            // 	// 如果是表格单元格
                                            // 	if (detail.cells) {
                                            // 		detail.cells.forEach(cell => {
                                            // 			if (cell.position) {
                                            // 				that.highlightText(detail.pageId, cell.position);
                                            // 			}
                                            // 		});
                                            // 	}
                                            //
                                            // 	// 移除之前所有active状态
                                            // 	document.querySelectorAll('.detail-item.active').forEach(item => {
                                            // 		item.classList.remove('active');
                                            // 	});
                                            //
                                            // 	// 给当前元素添加active状态
                                            // 	detailDiv.classList.add('active');
                                            //
                                            // 	that.scrollToKeyword(detail['pageId'], detail.position);
                                        })
                                    }
                                    container.appendChild(detailDiv);
                                })
                            })
                        }
                    }
                })

                <%--$.getJSON('${webpath}/static/pdf/data.json', function(data) {--%>
                <%--	that.$nextTick(() => {--%>
                <%--		const details = data.result.detail--%>
                <%--		const container = document.getElementById('markdown_content')--%>
                <%--		container.className = 'markdown-body'--%>
                <%--		container.innerHTML = ''--%>
                <%--		details.forEach(detail => {--%>
                <%--			const detailDiv = document.createElement('div')--%>
                <%--			detailDiv.className = 'detail-item'--%>
                <%--			// detailDiv.innerHTML = detail.text;--%>
                <%--			detailDiv.innerHTML = marked.parse(detail.text)--%>
                <%--			if(detail.cells && Array.isArray(detail.cells)) {--%>
                <%--				detail.cells.forEach(cell => {--%>
                <%--					const textElements = detailDiv.querySelectorAll('td');--%>
                <%--					textElements.forEach(element => {--%>
                <%--						if(element.textContent.trim() === cell.text.trim()) {--%>
                <%--							element.style.cursor = 'pointer'--%>
                <%--							element.classList.add('clickable-cell')--%>
                <%--							element.addEventListener('click', e => {--%>
                <%--								e.stopPropagation()--%>
                <%--								that.scrollToKeyword(detail['page_id'],cell.position)--%>
                <%--							})--%>
                <%--						}--%>
                <%--					})--%>
                <%--				})--%>
                <%--			}else {--%>
                <%--				detailDiv.addEventListener('click', () => {--%>
                <%--					that.scrollToKeyword(detail['page_id'],detail.position)--%>
                <%--				})--%>
                <%--			}--%>
                <%--			container.appendChild(detailDiv);--%>
                <%--		})--%>
                <%--	})--%>

                <%--}).fail(function(jqxhr, textStatus, error) {--%>
                <%--	console.error("获取JSON失败",error);--%>
                <%--});--%>
            },
            // 提取公共点击处理逻辑
            handleItemClick(detail, cell = null) {
                // 1. 更新激活状态
                document.querySelectorAll('.detail-item.active').forEach(item => {
                    item.classList.remove('active');
                });

                // 找到最近的detail-item父元素
                const activeElement = event?.target.closest('.detail-item') ||
                    document.querySelector(`[data-page-id="${detail.pageId}"]`);
                if (activeElement) {
                    activeElement.classList.add('active');
                }

                // 2. 获取并验证位置信息
                let positionData;
                try {
                    positionData = this.parsePositionData(cell?.position || detail.position);
                    if (!positionData || positionData.length < 4) {
                        throw new Error('无效的位置数据');
                    }
                } catch (error) {
                    console.error('位置数据处理失败:', error);
                    return;
                }

                // 4. 执行高亮和滚动
                this.scrollToKeyword(detail.pageId, positionData);
            },
            parsePositionData(position) {
                if (!position) throw new Error('位置数据为空');

                // 处理字符串格式 "186,346,816,344,815,371,185,372"
                if (typeof position === 'string') {
                    return position.split(',').map(Number);
                }
                // 处理数组格式
                else if (Array.isArray(position)) {
                    return position;
                }
                // 处理对象格式 {x, y, width, height}
                else if (position.x !== undefined) {
                    return [
                        position.x,
                        position.y,
                        position.x + position.width,
                        position.y + position.height
                    ];
                }

                throw new Error('未知的位置数据格式');
            },

            async scrollToKeyword(pageId, positionData, ocrDPI = 144) {
                if (!pageId || !positionData || !Array.isArray(positionData) || positionData.length !== 8) {
                    console.error('无效参数:', {pageId, positionData});
                    return;
                }

                try {
                    // 1. 验证PDF文档
                    if (!this.pdfDoc) {
                        console.error('PDF文档未加载');
                        return;
                    }
                    this.isScrollingProgrammatically = true;
                    this.pdfCurrentPage = parseInt(pageId);
                    await this.scrollToPage(pageId);

                    // 获取PDF页面
                    const page = await this.pdfDoc.getPage(pageId);
                    const pdfPageSize = page.getViewport({scale: 1.0}); // 使用1.0获取原始尺寸
                    const currentViewport = page.getViewport({scale: this.pdfCurrentScale});

                    // 2. 精确坐标转换
                    const convertedCoords = this.convertOcrToPdfCoords(positionData, pdfPageSize.height, ocrDPI);

                    // 3. 获取Canvas元素
                    const canvasIds = 'canvas-' + pageId
                    const canvas = document.getElementById(canvasIds);
                    console.log('Canvas尺寸:', canvas);
                    if (!canvas) {
                        console.error('Canvas元素未找到');
                        return;
                    }
                    // 6. 关键修正：使用Canvas的width/height属性而非clientWidth/clientHeight
                    const canvasWidth = canvas.width;
                    const canvasHeight = canvas.height;
                    const canvasRect = canvas.getBoundingClientRect();

                    // 4. 计算实际缩放比例
                    const scaleX = canvasWidth / pdfPageSize.width;
                    const scaleY = canvasHeight / pdfPageSize.height;

                    // 5. 计算边界框（处理四边形区域）
                    const [x1, y1, x2, y2, x3, y3, x4, y4] = convertedCoords;
                    const left = Math.min(x1, x2, x3, x4) * scaleX;
                    const right = Math.max(x1, x2, x3, x4) * scaleX;
                    const top = Math.max(y1, y2, y3, y4); // 注意这里是Math.min
                    const bottom = Math.min(y1, y2, y3, y4); // 注意这里是Math.max

                    // 6. 计算显示位置（转换为浏览器坐标系）
                    const displayLeft = left;
                    const displayTop = (pdfPageSize.height - top) * scaleY; // 不再需要canvasRect.height - top
                    const displayWidth = right - left;
                    const displayHeight = (top - bottom) * scaleY;
                    // // 5. 计算显示位置
                    // const displayLefts = left + 'px';
                    // const displayTops = top+ 'px'; // 转换为浏览器坐标系
                    // const displayWidths = right - left + 'px';
                    // const displayHeights =  bottom - top + 'px';
                    let textLayerIds = 'text-layer-' + pageId
                    const textLayer = document.getElementById(textLayerIds);
                    console.log('textLayer尺寸:', textLayer);
                    if (!textLayer) {
                        console.error('Text layer未找到');
                        return;
                    }
                    const displayLefts = displayLeft + 'px';
                    const displayTops = displayTop + 'px';// 不再需要canvasRect.height - top
                    const displayWidths = displayWidth + 'px';
                    const displayHeights = displayHeight + 'px';
                    // 6. 清除所有现有高亮
                    this.clearAllHighlights();
                    const highlight = document.createElement('div');
                    highlight.className = 'pdf-highlight-active';

                    Object.assign(highlight.style, {
                        position: 'absolute',
                        left: displayLefts,
                        top: displayTops,
                        width: displayWidths,
                        height: displayHeights,
                        backgroundColor: '#FFF581', // 改为红色便于调试
                        border: '2px solid #F90F03',
                        zIndex: '10',
                        opacity: '0.5',
                    });

                    textLayer.appendChild(highlight);
                    // 调试日志
                    console.log('最终坐标转换结果:', {
                        originalOcrCoords: positionData,
                        convertedPdfCoords: convertedCoords,
                        pdfPageSize: {
                            width: pdfPageSize.width,
                            height: pdfPageSize.height
                        },
                        canvasSize: {
                            logical: {width: canvasWidth, height: canvasHeight},
                            rendered: {width: canvasRect.width, height: canvasRect.height}
                        },
                        displayPosition: {
                            left: displayLeft,
                            top: displayTop,
                            width: displayWidth,
                            height: displayHeight
                        },
                        currentViewportScale: this.pdfCurrentScale
                    });

                } catch (error) {
                    console.error('高亮定位失败:', error);
                }
            },

            convertOcrToPdfCoords(ocrCoords, pdfHeight, ocrDPI = 300) {  // 1. 将OCR像素坐标转换为PDF点单位（72点=1英寸）
                // 1. 将OCR像素坐标转换为PDF点单位（72点=1英寸）
                const ptPerPixel = 72 / ocrDPI;

                // 2. 转换坐标并正确处理PDF坐标系（原点在左下角）
                return [
                    ocrCoords[0] * ptPerPixel,          // x1 (保持)
                    pdfHeight - (ocrCoords[1] * ptPerPixel), // y1 (翻转)
                    ocrCoords[2] * ptPerPixel,          // x2 (保持)
                    pdfHeight - (ocrCoords[3] * ptPerPixel), // y2 (翻转)
                    ocrCoords[4] * ptPerPixel,          // x3 (保持)
                    pdfHeight - (ocrCoords[5] * ptPerPixel), // y3 (翻转)
                    ocrCoords[6] * ptPerPixel,          // x4 (保持)
                    pdfHeight - (ocrCoords[7] * ptPerPixel)  // y4 (翻转)
                ];
            },

            // 新增方法：清除所有高亮
            clearAllHighlights() {
                // 查找所有高亮元素并移除
                const highlights = document.querySelectorAll('.pdf-highlight-active');
                highlights.forEach(highlight => highlight.remove());
            },

            // pt 转 px
            convertPtToPx(ptValue, viewport, page) {
                // PDF 的 1pt = (72 DPI)，转换为渲染后的 px
                return ptValue * (viewport.width / page.getViewport({scale: 1.0}).width);
            },
            /**
             * 处理缩放比例变化
             */
            handleScaleChange() {
                if (!this.pdfDoc || this.isPdfViewLoading) return;
                this.renderAllPages();
            },

            /**
             * 表格滚动事件
             */
            tableScroll(e) {
                this.scrollLeft = -e.target.scrollLeft;
                this.scrollTop = e.target.scrollTop;
            },
            tableScrollEntity(e) {
                this.scrollLeftEntity = -e.target.scrollLeft;
                this.scrollTopEntity = e.target.scrollTop;
            },

            /**
             * 右侧锚点跳转
             */
            tableLink(id) {
                const anchorElement = document.getElementById(id)
                // 如果对应id的锚点存在，就跳转到锚点
                if (anchorElement) {
                    anchorElement.scrollIntoView()
                }
                this.activeId = id
            },
            tableLinkEntity(id) {
                const anchorElement = document.getElementById(id)
                // 如果对应id的锚点存在，就跳转到锚点
                if (anchorElement) {
                    anchorElement.scrollIntoView()
                }
                this.activeIdEntity = id
            },
            /**
             * 点击中间表格内容右侧案卷目录定位
             */
            tableToLink(id) {
                this.activeId = id
                const anchorElement = document.getElementById(id + 'link')
                // 如果对应id的锚点存在，就跳转到锚点
                if (anchorElement) {
                    anchorElement.scrollIntoView()
                }
            },
            tableToLinkEntity(id) {
                this.activeIdEntity = id
                const anchorElement = document.getElementById(id + 'link')
                // 如果对应id的锚点存在，就跳转到锚点
                if (anchorElement) {
                    anchorElement.scrollIntoView()
                }
            },
            /**
             * 展开收缩案卷目录
             */
            expandEvents: function () {
                this.expandStatus = !this.expandStatus;
            },
            expandEntityEvents: function () {
                this.expandEntityStatus = !this.expandEntityStatus;
            },
            initAi: function () {
                if (this.scoringIndexList != null) {
                    var isAiView = this.scoringIndexList.isAiView;
                    if (isAiView == "1" && document.getElementById('aistartRecognition')) {
                        document.getElementById('aistartRecognition').disabled = true;
                        // this.aiInfoColor();
                    }
                }
            },
            startRecognition: function () {
                document.getElementById("jdmodal").style.display = "block";
                this.startProgress();
            },

            closejdModal: function () {
                document.getElementById("jdmodal").style.display = "none";
            },
            confirmRecognition: function () {
                // alert("确认识别已完成！最终分数：" + currentScore.toFixed(1));

                this.closejdModal();


                // this.aiInfoColor();

                this.updateAiView();
                document.getElementById('aistartRecognition').disabled = true;

            },
            updateAiView: function () {
                $.ajax({
                    type: "post",
                    url: WEBPATH + '/zjpf/updateAiView.do',
                    data: {id: expertFileId},           //注意数据用{}
                    success: function (data) {  //成功
                    }
                });
            },
            startProgress: function () {
                let finalScore = 50; // 最终分数
                let currentScore = 0; // 初始化当前分数

                let progress = 0;
                const progressBar = document.getElementById("progressBar");
                const status = document.getElementById("jdstatus");
                const confirmButton = document.getElementById("confirmButton");
                // const scoreDisplay = document.getElementById("jdscore");
                const completionMessage = document.getElementById("completionMessage");
                const modalTitle = document.getElementById("modalTitle");

                const totalDuration = 15000; // 总时间 15 秒
                const totalSteps = 50; // 进度条分为 50 步
                const stepDuration = totalDuration / totalSteps; // 每步的持续时间
                const incrementPerStep = 100 / totalSteps; // 每步进度增加的百分比

                const interval = setInterval(function () {
                    if (progress < 100) {
                        // 增加固定的进度增量
                        progress += incrementPerStep;

                        // 确保进度不超过100%
                        if (progress > 100) {
                            progress = 100;
                        }

                        progressBar.style.width = progress + "%";
                        status.innerText = "进度：" + Math.round(progress) + "%";

                        // 随机增加分数
                        const randomScoreIncrement = Math.random() < 0.5 ? 0.5 : 1; // 50%概率增加0.5或1
                        if (currentScore < finalScore) {
                            currentScore += randomScoreIncrement;
                            currentScore = Math.min(currentScore, finalScore); // 确保不超过最终分数
                        }
                        // scoreDisplay.innerText = "卷面分数：" + currentScore.toFixed(1); // 保留一位小数
                    }

                    if (progress >= 100) {
                        clearInterval(interval);
                        status.innerText = "已完成识别";
                        // scoreDisplay.innerText = "卷面分数：" + currentScore.toFixed(1); // 显示最终分数

                        document.getElementById("modalTitle").style.display = "none"; // 隐藏标题
                        progressBar.parentNode.style.display = "none"; // 隐藏进度条
                        // scoreDisplay.style.display = "none"; // 隐藏分数
                        status.style.display = "none"; // 隐藏状态

                        // 显示完成信息
                        completionMessage.style.display = "block"; // 显示完成信息
                        confirmButton.style.display = "block"; // 显示确认按钮
                    }
                }, stepDuration) // 每步持续时间
            },
            //ai评查内容赋值
            aiInfoColor: function () {
                // const finalScore = 50; // 假设这是最终卷面分
                // if (scoringIndexList != null && scoringIndexList.expertHandlIndexScoreList != null) {
                //     console.log("处理卷面AI数据:", scoringIndexList.expertHandlIndexScoreList);
                //     var keywords = ["未提到", "没有提到", "没有明确提到", "未提供", "没有提供", "无法确定", "没有提及", "未在", "未明确提及", "没有明确提及", "没有直接提及", "未提及", "没有包含", "没有提及", "也没有", "无法", "没有包含", "不包含", "并未包含", "没有找到", "没有描述"];
                //     var expertHandlIndexScoreList = scoringIndexList.expertHandlIndexScoreList;
                //     //循环大项
                //     var smartScore = 0; // 假设这是智能识别分数
                //     var chidAiScore = 0;
                //     for (let i = 0; i < expertHandlIndexScoreList.length; i++) {
                //
                //         console.log("大项:", expertHandlIndexScoreList[i], this.scoringIndexList)
                //         //未结案的结案审批表大项不处理
                //         if (this.scoringIndexList.closed == 0 ? !expertHandlIndexScoreList[i].indexname.includes("结案审批表") : true) {
                //             var expertHandlItemScoreList = expertHandlIndexScoreList[i].expertHandlItemScoreList;
                //             //循环大项里面的小项
                //             for (let j = 0; j < expertHandlItemScoreList.length; j++) {
                //                 var itemScore = expertHandlItemScoreList[j];
                //                 console.log(i, j, "卷面isAi:", itemScore.isAi, itemScore.isAi == null, "itemScore:", itemScore)
                //                 //如果不是ai评查项,需要展示文字: 该项人工评查
                //                 if (itemScore != null && (itemScore.isAi == null || itemScore.isAi == '0')) {
                //                     var manualInfo = 'aiInfo' + i + j;
                //                     console.log("该项人工评查:", manualInfo)
                //                     var spanInfo = document.getElementById(manualInfo);
                //
                //                     if (spanInfo) {
                //                         var newSpan = document.createElement("span");
                //                         newSpan.innerHTML = "该项人工评查";
                //                         spanInfo.appendChild(newSpan);
                //                     } else {
                //                         console.error("Element does not exist: ", manualInfo);
                //                     }
                //                 }
                //
                //                 //判断小项不为空,并取出ai内容
                //                 if (itemScore != null && itemScore.aiInfo != null) {
                //                     var aiInfo = itemScore.aiInfo;
                //                     var isAi = itemScore.isAi;
                //                     var oneitemScore = itemScore.itemscore;
                //                     if (isAi == '1') {
                //                         chidAiScore += oneitemScore
                //                     }
                //
                //                     var formattedString = itemScore.htmlaiInfo;
                //                     var aiInfoId = 'aiInfo' + i + j;
                //
                //                     var aiStatus = itemScore.aiStatus;
                //                     var aiStatusId = 'aiStatus' + i + j;
                //
                //                     if (aiStatus !== null && aiStatus !== undefined) {
                //                         const statusArray = aiStatus.split(','); // 切分字符串
                //                         const iconContainer = document.getElementById(aiStatusId); // 获取展示图标的容器
                //
                //                         // 清空容器
                //                         if (iconContainer) {
                //                             iconContainer.innerHTML = '';
                //                         }
                //
                //                         // 图标映射
                //                         const iconMap = {
                //                             '1': '<icon class="icon pass"></icon>', // 对号
                //                             '2': '<icon class="icon nopass"></icon>',   // 叉号
                //                             '3': '<span class="icon" style="color: orange;font-size: 20px;font-weight:normal ;">&#33; </span>', // 感叹号
                //                         };
                //
                //                         // 遍历状态数组
                //                         statusArray.forEach(status => {
                //                             const iconHTML = iconMap[status.trim()]; // 获取对应的图标 HTML
                //                             if (iconHTML && iconContainer) {
                //                                 iconContainer.innerHTML += iconHTML; // 将图标添加到容器中
                //                             }
                //                         });
                //
                //                     }
                //
                //
                //                     if (formattedString !== null && formattedString !== undefined) {
                //
                //                         // document.getElementById(aiInfoId).innerHTML = formattedString;
                //                         const lines = formattedString.split("\r\n");
                //                         var tagsContainer = document.getElementById(aiInfoId);
                //                         // 清空容器
                //                         if (tagsContainer) {
                //                             tagsContainer.innerHTML = '';
                //                         }
                //
                //                         lines.forEach((line, index) => {
                //
                //                             const tag = document.createElement("span");
                //                             tag.innerHTML = line; // 使用 innerHTML 渲染HTML内容
                //                             if (tagsContainer) {
                //                                 tagsContainer.appendChild(tag);
                //                                 if (index < lines.length - 1) {
                //                                     tagsContainer.appendChild(document.createElement("br"));
                //                                 }
                //                             }
                //                         });
                //
                //
                //                     } else {
                //                         console.log("这里是ai智能评查内容:", i, j, aiInfo)
                //
                //                         // 使用 "\r\n" 切分字符串
                //                         var aiInfoParts = aiInfo.split("\r\n");
                //                         // 获取 aiInfo 所对应的 div
                //                         var aiInfoId = 'aiInfo' + i + j;
                //                         var aiInfoDiv = document.getElementById(aiInfoId);
                //
                //                         aiInfoParts.forEach(function (part, index) { // 遍历每一部分
                //                             if (part) { // 排除空的部分
                //                                 // 创建新的 span 元素,并将字符串赋值给 span 的 innerHTML
                //                                 var newSpan = document.createElement("span");
                //                                 newSpan.innerHTML = part;
                //
                //                                 // 检测是否包含关键词，如果包含则设置文字颜色为红色
                //                                 keywords.forEach(function (keyword) {
                //                                     if (part.includes(keyword)) newSpan.style.color = 'red';
                //                                 });
                //                                 //检查元素是否存在
                //                                 if (aiInfoDiv) {
                //                                     aiInfoDiv.appendChild(newSpan); // 将 span 添加到 div 中
                //                                     if (index < aiInfoParts.length - 1) {
                //                                         aiInfoDiv.appendChild(document.createElement("br"));  // 添加一个新的换行符到 div 中
                //                                     }
                //                                 } else {
                //                                     console.log('没有找到对应的div: aiInfo' + i + j);
                //                                 }
                //
                //                             }
                //                         });
                //
                //                     }
                //
                //                     // 智能评分赋值
                //                     var aiScore = itemScore.aiScore;
                //                     smartScore = smartScore + aiScore;
                //
                //                     if (aiScore != null) {
                //                         var aiScoreId = 'aiScore' + i + j;
                //
                //                         $("#" + aiScoreId).val(aiScore);
                //                     }
                //                 }
                //             }
                //         }
                //
                //     }
                //
                //
                // }
                //
                // document.getElementById('smartScore').textContent = (50 * (smartScore / chidAiScore)).toFixed(2);
                // //document.getElementById('totalAiScore').textContent = (50 * (smartScore / chidAiScore)).toFixed(2)  ;
                // this.totalAiScore = (50 * (smartScore / chidAiScore)).toFixed(2);
                // document.getElementById('finalScore').textContent = scoringIndexList.paperscore;

            },

            //存在争议 按钮
            showErrorMsg: function (obj) {
                $.ajax({
                    type: "post",
                    url: WEBPATH + '/zjpf/getOtherExpertScore.do',
                    data: {
                        fileId: $("#fileId").val(),
                        expertId: $("#expertId").val(),
                        handlType: $("#handlType").val(),
                        indexId: obj.indexid
                    },
                    success: function (data) {  //成功
                        if (data.result == "success") {
                            // swal("争议详情(对方专家评分情况)", "一票否决："+(data.data.voteDownValue==0?"否":"是")+" \n无需评查："+(data.data.inCheckValue==0?"否":"是")+"\n得分："+data.data.resultScore+"\n评审依据:"+(!data.data.comment?"":data.data.comment), "info");

                            //如果不为空,说明对面选择了一票否决或者无需评查,否则就展示对方的评分
                            if (data.data.option != '' && data.data.option != null && data.data.option != undefined) {
                                $('#itemOption').text(data.data.option);
                            } else {
                                $('#itemOption').text("评分值:" + data.data.expertSocre);
                            }

                            if (data.data.comme != '' && data.data.comme != null && data.data.comme != undefined) {
                                $('#itemComme').text(data.data.comme);
                            } else {
                                $('#itemComme').text("无内容");
                            }

                            //主键ID
                            $('#error_id').val(data.data.id);
                            //现在是A专家还是B砖家
                            $('#isAOrB').val(data.data.isAOrB);
                            //打开查看异常的弹窗
                            $("#error_dialog_score").dialog("open");
                        } else {
                            swal("信息", data.message, "error");
                        }
                    }
                });
            },
            validateForm: function (id) {
                var fileMaterialsNums = document.getElementById('fileMaterialsNums').value;
                var fileMaterialsDocs = document.getElementById('fileMaterialsDocs').value;
                var fileMaterialsSups = document.getElementById('fileMaterialsSups').value;

                if (fileMaterialsNums === '' || fileMaterialsDocs === '' || fileMaterialsSups === '') {
                    // alert('请填写所有内容！');
                    swal("操作失败!", "请填写所有内容", "error");
                    return false;
                }

                $.ajax({
                    type: "post",
                    url: WEBPATH + '/zjpf/notGood.do',
                    data: {
                        id: id,
                        fileMaterialsNums: fileMaterialsNums,
                        fileMaterialsDocs: fileMaterialsDocs,
                        fileMaterialsSups: fileMaterialsSups
                    },
                    success: function (data) {  //成功
                        //$('#my_dialo').dialog("close");
                        $("#my_dialo").css("display", "none");
                        if ("200" == data.code) {
                            // alert(222)
                            <%--window.location.href="${pageContext.request.contextPath}/zjpf/zjpfList.do";--%>
                            business.addMainContentParserHtml('zjpf/zjpfList.do', 'pageNum=${pageNum}');
                            location.reload()
                            //return false;
                        } else if ("400" == data.code) {
                            document.getElementById('fileMaterialsNums').value = "";
                            document.getElementById('fileMaterialsDocs').value = "";
                            document.getElementById('fileMaterialsSups').value = "";
                            swal("操作失败", "该案卷不存在!", "error");
                            return false;
                        } else if ("suffixerror" == data) {
                            document.getElementById('fileMaterialsNums').value = "";
                            document.getElementById('fileMaterialsDocs').value = "";
                            document.getElementById('fileMaterialsSups').value = "";
                            swal("操作失败", "该案卷上传数据格式有问题!", "error");
                            return false;
                        }
                    }
                });
                //$('#my_dialo').dialog("close");
                $("#my_dialo").css("display", "none");


                // 如果通过校验，可以继续执行其他操作
                return true;
            },
            //判断是否有总分，没有则相加求分
            sumScore: function () {
                 var paperScore = this.scoringIndexList.paperscore;
                var firstScore = this.scoringIndexList.expertHandlIndexScoreList;
                console.log("页面加载完成,执行计算分数,paperScore:",paperScore,"firstScore:",firstScore)

                if (paperScore == "" || paperScore == null) {
                    var totalScore = 0;
                    var childScore = 0;
                    var denominatorScore = 0;
                    var bonus = this.plusesTotalScore || 0; // 使用加分项计算方法获取加分项分数
                    // 扣分项
                    var deduction = 0;
                    if (firstScore != null && firstScore.length > 0) {
                        for (var i = 0; i < firstScore.length; i++) {
                            if (!isNaN(parseFloat(firstScore[i].resultScore)) && firstScore[i].indexname != '加分项' && firstScore[i].isOperator != '0') {
                                childScore += firstScore[i].resultScore;
                            }
                            if (/*!firstScore[i].inCheckValue&&*/ firstScore[i].indexname != '加分项' && (this.scoringIndexList.closed == 0 ? !firstScore[i].indexname.includes("结案审批表") : true) && firstScore[i].isOperator != '0') {
                                denominatorScore += firstScore[i].indexscore;
                            }
                            if (firstScore[i].indexname == '加分项') {
                                bonus = firstScore[i].resultScore;
                            }
                            if (firstScore[i].isOperator == '0') {
                                deduction += firstScore[i].resultScore;
                            }
                        }
                    }
                    if (denominatorScore == 0) {
                        totalScore = 0.00 + bonus + deduction
                    } else {
                        var childCountScore = childScore + bonus;
                        totalScore = 50 * (childCountScore / denominatorScore) + deduction;
                    }
                    this.scoringIndexList.paperscore = totalScore.toFixed(2);
                    console.log("页面加载完成,执行计算分数,计算后的分数,totalScore:",totalScore,"this.scoringIndexList.paperscore:",this.scoringIndexList.paperscore)
                    document.getElementById('finalScore').textContent = this.scoringIndexList.paperscore;
                }else{
                    document.getElementById('finalScore').textContent = this.scoringIndexList.paperscore;
                }
            },
            //小指标的复选框选择事件
            changeCheckBoxNegative: function (indexScore, itemScore) {

                //该二级指标所在的一级指标下的所有二级指标
                var data = myVue.scoringIndexList.expertHandlIndexScoreList[indexScore].expertHandlItemScoreList;
                var flag = data[itemScore].score;
                var checkitem = ".checkitem" + indexScore;
                var isInCheckid = '#isInCheck' + indexScore;
                if (flag) {//一票否决选中
                    myVue.scoringIndexList.expertHandlIndexScoreList[indexScore].expertHandlItemScoreList[itemScore].score = 1;
                    if (data != null && data.length > 0) {
                        for (var i = 0; i < data.length - 2; i++) {//
                            data[i].score = '';
                            $("#expertItem" + i + "" + indexScore).removeClass("has-error");
                            $("#expertItem" + i + "" + indexScore).removeClass("has-success");
                            myVue.scoringIndexList.expertHandlIndexScoreList[indexScore].expertHandlItemScoreList[i].validatorMessage = "";
                        }
                    } else {
                        myVue.scoringIndexList.expertHandlIndexScoreList[indexScore].score = '';
                        myVue.scoringIndexList.expertHandlIndexScoreList[indexScore].resultScore = '';
                        $("#expertIndex" + indexScore).removeClass("has-error");
                        $("#expertIndex" + indexScore).removeClass("has-success");
                        myVue.scoringIndexList.expertHandlIndexScoreList[indexScore].validatorMessage = "";
                    }
                    myVue.scoringIndexList.expertHandlIndexScoreList[indexScore].resultScore = 0;
                    $(checkitem).attr("disabled", "disabled");
                    $(isInCheckid).attr("disabled", "disabled");

                    var text = $("#comment" + indexScore).val().replace(/\s+/g, "");
                    if (text == null || text == '') {
                        $("#comment" + indexScore).removeClass("textarea-success");
                        $("#comment" + indexScore).addClass("textarea-error");
                        myVue.scoringIndexList.expertHandlIndexScoreList[indexScore].validatorFlag = false;
                        myVue.scoringIndexList.expertHandlIndexScoreList[indexScore].validatorMessage = "此项评审依据必填";
                    }
                    myVue.scoringIndexList.expertHandlIndexScoreList[indexScore].voteDownValue = 2;
                } else {//一票否决取消
                    myVue.scoringIndexList.expertHandlIndexScoreList[indexScore].expertHandlItemScoreList[itemScore].score = 0;

                    /*
                     *	2019二级指标为复选框, 指标项在集合中最后两个
                     *
                     *		先判断传入二级指标是否为最后一项
                     *			是:判断上一个是否选中
                     *			否:判断下一个是否选中
                     */
                    var state = data[itemScore].score;
                    if (itemScore == data.length - 1) {
                        if (data[itemScore - 1].score == 1) {
                            state = true;
                        }
                    } else {
                        if (data[itemScore + 1].score == 1) {
                            state = true;
                        }
                    }
                    if (!state) {
                        myVue.scoringIndexList.expertHandlIndexScoreList[indexScore].score = '';
                        myVue.scoringIndexList.expertHandlIndexScoreList[indexScore].resultScore = '';
                        if (data != null && data.length > 0) {
                            for (var i = 0; i < data.length; i++) {
                                myVue.scoringIndexList.expertHandlIndexScoreList[indexScore].expertHandlItemScoreList[i].validatorFlag = false;
                            }
                        } else {
                            myVue.scoringIndexList.expertHandlIndexScoreList[indexScore].validatorFlag = false;
                        }
                        $(checkitem).removeAttr("disabled");
                        $(isInCheckid).removeAttr("disabled");

                        $("#comment" + indexScore).removeClass("textarea-error");
                        $("#comment" + indexScore).addClass("textarea-success");
                        myVue.scoringIndexList.expertHandlIndexScoreList[indexScore].validatorFlag = true;
                        myVue.scoringIndexList.expertHandlIndexScoreList[indexScore].validatorMessage = "";
                        myVue.scoringIndexList.expertHandlIndexScoreList[indexScore].voteDownValue = 0;
                    }
                }
                // 总分计算
                var totalScore = 0;
                var childScore = 0;
                var denominatorScore = 0;
                var firstScore = myVue.scoringIndexList.expertHandlIndexScoreList;
                var bonus = 0;
                // 扣分项
                var deduction = 0;
                if (firstScore != null && firstScore.length > 0) {
                    for (var i = 0; i < firstScore.length; i++) {
                        if (!isNaN(parseFloat(firstScore[i].resultScore)) && firstScore[i].indexname != '加分项' && firstScore[i].isOperator != '0') {
                            childScore += firstScore[i].resultScore;
                        }
                        if (/*!firstScore[i].inCheckValue&&*/ firstScore[i].indexname != '加分项' && (this.scoringIndexList.closed == 0 ? !firstScore[i].indexname.includes("结案审批表") : true) && firstScore[i].isOperator != '0') {
                            denominatorScore += firstScore[i].indexscore;
                        }
                        if (firstScore[i].indexname == '加分项') {
                            bonus = firstScore[i].resultScore;
                        }
                        if (firstScore[i].isOperator == '0') {
                            deduction += firstScore[i].resultScore;
                        }
                    }
                }
                if (denominatorScore == 0) {
                    totalScore = 0.00 + bonus + deduction
                } else {
                    var childCountScore = childScore + bonus;
                    totalScore = 50 * (childCountScore / denominatorScore) + deduction;
                }
                if (totalScore < 0) {
                    totalScore = 0;
                }
                myVue.scoringIndexList.paperscore = totalScore.toFixed(2);
                document.getElementById('finalScore').textContent = myVue.scoringIndexList.paperscore;
            },

            //一票否决或无需评查复选框点击事件
            changeCheckBoxCli: function (index, status) {

                // 获取选中未选中的状态 stauts=1一票否决\status=2无需评查
                if (status == '1') {
                    //一票否决
                    var flag = myVue.scoringIndexList.expertHandlIndexScoreList[index].voteDownValue;
                    var checkitem = ".checkitem" + index;
                    var isInCheckid = '#isInCheck' + index;
                    if (flag) {//一票否决选中
                        var data = myVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList;
                        if (data != null && data.length > 0) {
                            for (var i = 0; i < data.length; i++) {
                                data[i].score = '';
                                $("#expertItem" + i + "" + index).removeClass("has-error");
                                $("#expertItem" + i + "" + index).removeClass("has-success");
                                myVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[i].validatorMessage = "";
                            }
                        } else {
                            myVue.scoringIndexList.expertHandlIndexScoreList[index].score = '';
                            myVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore = '';
                            $("#expertIndex" + index).removeClass("has-error");
                            $("#expertIndex" + index).removeClass("has-success");
                            myVue.scoringIndexList.expertHandlIndexScoreList[index].validatorMessage = "";
                        }
                        myVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore = 0;
                        $(checkitem).attr("disabled", "disabled");
                        $(isInCheckid).attr("disabled", "disabled");

                        var text = $("#comment" + index).val().replace(/\s+/g, "");
                        if (text == null || text == '') {
                            $("#comment" + index).removeClass("textarea-success");
                            $("#comment" + index).addClass("textarea-error");
                            myVue.scoringIndexList.expertHandlIndexScoreList[index].validatorFlag = false;
                            myVue.scoringIndexList.expertHandlIndexScoreList[index].validatorMessage = "此项评审依据必填";
                        }
                    } else {//一票否决取消
                        myVue.scoringIndexList.expertHandlIndexScoreList[index].score = '';
                        myVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore = '';
                        var data = myVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList;
                        if (data != null && data.length > 0) {
                            for (var i = 0; i < data.length; i++) {
                                myVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[i].validatorFlag = false;
                            }
                        } else {
                            myVue.scoringIndexList.expertHandlIndexScoreList[index].validatorFlag = false;
                        }
                        $(checkitem).removeAttr("disabled");
                        $(isInCheckid).removeAttr("disabled");

                        $("#comment" + index).removeClass("textarea-error");
                        $("#comment" + index).addClass("textarea-success");
                        myVue.scoringIndexList.expertHandlIndexScoreList[index].validatorFlag = true;
                        myVue.scoringIndexList.expertHandlIndexScoreList[index].validatorMessage = "";
                    }
                } else {
                    //无需评查
                    var flag = myVue.scoringIndexList.expertHandlIndexScoreList[index].inCheckValue;
                    var checkitem = ".checkitem" + index;
                    var isVoteDownid = '#isVoteDown' + index;
                    if (flag) {
                        //无需评查选中
                        var data = myVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList;
                        if (data != null && data.length > 0) {
                            var resultScore = 0;
                            for (var i = 0; i < data.length; i++) {
                                //选中无需评查后,默认满分
                                resultScore = resultScore + data[i].itemscore
                                data[i].score = data[i].itemscore;
                                $("#expertItem" + i + "" + index).removeClass("has-error");
                                $("#expertItem" + i + "" + index).removeClass("has-success");
                                myVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[i].validatorMessage = "";
                            }
                            myVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore = resultScore;
                        } else {
                            myVue.scoringIndexList.expertHandlIndexScoreList[index].score = '';
                            myVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore = 0;
                            $("#expertIndex" + index).removeClass("has-error");
                            $("#expertIndex" + index).removeClass("has-success");
                            myVue.scoringIndexList.expertHandlIndexScoreList[index].validatorMessage = "";
                        }
                        // myVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore = 0;
                        $(checkitem).attr("disabled", "disabled");
                        $(isVoteDownid).attr("disabled", "disabled");
                    } else {
                        //无需评查取消
                        var data = myVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList;
                        if (data != null && data.length > 0) {
                            for (var i = 0; i < data.length; i++) {
                                data[i].score = '';
                                myVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[i].validatorFlag = false;
                            }
                        } else {
                            myVue.scoringIndexList.expertHandlIndexScoreList[index].validatorFlag = false;
                        }
                        myVue.scoringIndexList.expertHandlIndexScoreList[index].score = '';
                        myVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore = '';
                        $(checkitem).removeAttr("disabled");
                        $(isVoteDownid).removeAttr("disabled");
                    }
                }

                // 总分计算
                var totalScore = 0;
                var childScore = 0;
                var denominatorScore = 0;
                var firstScore = myVue.scoringIndexList.expertHandlIndexScoreList;
                var bonus = myVue.plusesTotalScore || 0; // 使用加分项计算方法获取加分项分数
                // 扣分项
                var deduction = 0;
                if (firstScore != null && firstScore.length > 0) {
                    for (var i = 0; i < firstScore.length; i++) {
                        if (!isNaN(parseFloat(firstScore[i].resultScore)) && firstScore[i].indexname != '加分项' && firstScore[i].isOperator != '0') {
                            childScore += firstScore[i].resultScore;
                        }
                        if (/*!firstScore[i].inCheckValue&&*/ firstScore[i].indexname != '加分项' && (this.scoringIndexList.closed == 0 ? !firstScore[i].indexname.includes("结案审批表") : true) && firstScore[i].isOperator != '0') {
                            denominatorScore += firstScore[i].indexscore;
                        }
                        if (firstScore[i].indexname == '加分项') {
                            bonus = firstScore[i].resultScore;
                        }
                        if (firstScore[i].isOperator == '0') {
                            deduction += firstScore[i].resultScore;
                        }
                    }
                }
                if (denominatorScore == 0) {
                    totalScore = 0.00 + bonus + deduction
                } else {
                    var childCountScore = childScore + bonus;
                    totalScore = 50 * (childCountScore / denominatorScore) + deduction;
                }
                if (totalScore < 0) {
                    totalScore = 0;
                }

                myVue.scoringIndexList.paperscore = totalScore.toFixed(2);
                document.getElementById('finalScore').textContent = myVue.scoringIndexList.paperscore
            },
            //初评得分
            updateItem: function (index, itemIndex, itemScore, id) {//

                var num = 0;
                var childNum = 0;
                var value = myVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].score;
                if (value != '' && value != null) {

                    console.log("初评得分:", value)
                    if (parseFloat(value) <= parseFloat(itemScore)) {
                        if (value.substring(value.length - 1, value.length) == ".") {
                            $("#expertItem" + itemIndex + index).removeClass("has-success");
                            $("#expertItem" + itemIndex + index).addClass("has-error");
                            myVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorFlag = false;
                            myVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorMessage = "分值项不能为空，在0-" + itemScore + "分之间，且最多精确到小数点后两位小数！";
                            return false;
                        }
                        if (value.indexOf('-') != -1) {
                            $("#expertItem" + itemIndex + index).removeClass("has-success");
                            $("#expertItem" + itemIndex + index).addClass("has-error");
                            myVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorFlag = false;
                            myVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorMessage = "分值项不能为负数";
                            return false;
                        }
                        var re = /^-?\d+\.?\d{0,2}$/;
                        if (re.test(value)) {   // 返回true
                            $("#expertItem" + itemIndex + index).removeClass("has-error");
                            $("#expertItem" + itemIndex + index).addClass("has-success");
                            myVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorFlag = true;
                            myVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorMessage = "";
                        } else {
                            $("#expertItem" + itemIndex + index).removeClass("has-success");
                            $("#expertItem" + itemIndex + index).addClass("has-error");
                            myVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorFlag = false;
                            myVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorMessage = "分值项不能为空，在0-" + itemScore + "分之间，且最多精确到小数点后两位小数！";
                            return false;
                        }
                        var score = myVue.scoringIndexList.expertHandlIndexScoreList[index];
                        var indexScore = myVue.scoringIndexList.expertHandlIndexScoreList[index].indexscore;
                        if (score != null) {
                            for (var i = 0; i < score.expertHandlItemScoreList.length; i++) {
                                if (!isNaN(parseFloat(score.expertHandlItemScoreList[i].score))) {
                                    if (score.expertHandlItemScoreList[i].isOperator == 1) {
                                        childNum = (Math.round((childNum + parseFloat(score.expertHandlItemScoreList[i].score)) * 100)) / 100;
                                    } else {
                                        //减分项
                                        childNum = (Math.round((childNum - parseFloat(score.expertHandlItemScoreList[i].score)) * 100)) / 100;
                                    }

                                }
                            }
                        }
                        myVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore = childNum;

                        if (myVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore > indexScore) {
                            myVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore = indexScore;
                        }
                        // if( myVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore<0){
                        // 	myVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore =0;
                        // }
                        // if(myVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].isOperator ==1){
                        //     //加分项
                        //     myVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore= childNum;
                        //     if(myVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore>indexScore){
                        //         myVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore=indexScore;
                        //     }
                        // } else {
                        //     //减分项
                        //     myVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore=
                        //     Math.round((myVue.scoringIndexList.expertHandlIndexScoreList[index].indexscore-childNum)*100)/100;
                        // 	myVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore=
                        // 			Math.round((myVue.scoringIndexList.expertHandlIndexScoreList[index].indexscore-childNum)*100)/100;
                        //     if( myVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore<0){
                        //         myVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore =0;
                        //     }
                        // };
                        // 总分计算
                        var totalScore = 0;
                        var childScore = 0;
                        var denominatorScore = 0;
                        var firstScore = myVue.scoringIndexList.expertHandlIndexScoreList;
                        // if(firstScore != null) {
                        // 	for(var i = 0; i < firstScore.length; i++) {
                        // 		if(!isNaN(parseFloat(firstScore[i].resultScore))) {
                        // 			childScore += firstScore[i].resultScore;
                        // 		}
                        // 		if(!firstScore[i].inCheckValue) {
                        // 			denominatorScore += firstScore[i].indexscore;
                        // 		}
                        // 	}
                        // }
                        // totalScore = (childScore / denominatorScore) * 50;
                        var bonus = myVue.plusesTotalScore || 0; // 使用加分项计算方法获取加分项分数
                        // 扣分项
                        var deduction = 0;
                        if (firstScore != null && firstScore.length > 0) {
                            //评分大项循环    isCircle='0'是减分项
                            for (var i = 0; i < firstScore.length; i++) {
                                if (!isNaN(parseFloat(firstScore[i].resultScore)) && firstScore[i].indexname != '加分项' && firstScore[i].isOperator != '0') {
                                    childScore += firstScore[i].resultScore;
                                }
                                if (/*!firstScore[i].inCheckValue &&*/ firstScore[i].indexname != '加分项' && (this.scoringIndexList.closed == 0 ? !firstScore[i].indexname.includes("结案审批表") : true) && firstScore[i].isOperator != '0') {
                                    denominatorScore += firstScore[i].indexscore;
                                }
                                if (firstScore[i].indexname == '加分项') {
                                    bonus = firstScore[i].resultScore;
                                }
                                if (firstScore[i].isOperator == '0') {
                                    deduction += firstScore[i].resultScore;
                                }
                            }
                        }
                        if (denominatorScore == 0) {
                            totalScore = 0.00 + bonus + deduction
                        } else {
                            var childCountScore = childScore + bonus;
                            totalScore = 50 * (childCountScore / denominatorScore) + deduction;
                        }
                        if (totalScore < 0) {
                            totalScore = 0;
                        }
                        myVue.scoringIndexList.paperscore = totalScore.toFixed(2);
                        document.getElementById('finalScore').textContent = myVue.scoringIndexList.paperscore;
                    } else {
                        $("#expertItem" + itemIndex + index).removeClass("has-success");
                        $("#expertItem" + itemIndex + index).addClass("has-error");
                        myVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorFlag = false;
                        myVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorMessage = "分值项不能为空，在0-" + itemScore + "分之间，且最多精确到小数点后两位小数！";
                        return false;
                    }
                } else {
                    $("#expertItem" + itemIndex + index).removeClass("has-success");
                    $("#expertItem" + itemIndex + index).addClass("has-error");
                    myVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorFlag = false;
                    myVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorMessage = "分值项不能为空，在0-" + itemScore + "分之间，且最多精确到小数点后两位小数！";
                    return false;
                }
            },
            //评审依据输入框
            updateCommon: function (index) {
                var flag = myVue.scoringIndexList.expertHandlIndexScoreList[index].voteDownValue;
                var text = $("#comment" + index).val().replace(/\s+/g, "");
                var indexname = myVue.scoringIndexList.expertHandlIndexScoreList[index].indexname;
                if (indexname == "责令改正违法行为决定书") {
                    var itemScores = myVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList;
                    var item1 = itemScores[itemScores.length - 1].score;
                    var item2 = itemScores[itemScores.length - 2].score;
                    if (item1 == 1 || item2 == 1) {
                        flag = true;
                    }
                }
                if (flag) {
                    if (text != null && text != '') {
                        $("#comment" + index).removeClass("textarea-error");
                        $("#comment" + index).addClass("textarea-success");
                        myVue.scoringIndexList.expertHandlIndexScoreList[index].validatorFlag = true;
                        myVue.scoringIndexList.expertHandlIndexScoreList[index].validatorMessage = "";
                    }
                    if (text == null || text == '') {
                        $("#comment" + index).removeClass("textarea-success");
                        $("#comment" + index).addClass("textarea-error");
                        myVue.scoringIndexList.expertHandlIndexScoreList[index].validatorFlag = false;
                        myVue.scoringIndexList.expertHandlIndexScoreList[index].validatorMessage = "此项评审依据必填";
                    }
                }
            },
            mycheckEntity: function (item) {
                const str = myVue.nopassDrawItem?.commeStrThree || '';
                const arr = str.split(',');
                if (arr.indexOf(item) != -1) {
                    myVue.nopassDrawItem.commeStrThree = arr.filter(v => v != item).join(',');
                } else {
                    myVue.nopassDrawItem.commeStrThree = str.length ? (str + ',' + item) : item;
                }
            },

            saveTime: function () {
                if (this.scoringIndexList.scoredstate == 0 || this.scoringIndexList.scoredstate == 2) {
                    this.saveSubmit(this.scoringIndexList.id, '0');//暂存
                } else {
                    this.saveSubmit(this.scoringIndexList.id, '1');//保存
                }
            },
            //保存 0.暂存  1.保存
            saveSubmit: function (id, status) {
                //var tuijian = document.getElementById("yxdxanlituijian").checked;
                //var dictionarys = document.getElementById("dictionary");
                //var dictionarys = $("#dictionarys").val();

                var isfileMaterials = $("#fileMaterials").prop('checked');
                if (isfileMaterials == true) {
                    swal({title: "提示", text: "正常评审的案卷请取消勾选材料严重不全！", type: "error"});
                    return false;
                }
                /*逐项验证vue对象中的文本框*/
                if (myVue.scoringIndexList != null) {
                    var data = myVue.scoringIndexList.expertHandlIndexScoreList;//一级指标
                    for (var i = 0; i < data.length; i++) {
                        if (data[i].indexname.includes("结案审批表")) {
                            console.log("结案审批表", data[i])
                        }
                        //一票否决
                        if (data[i].voteDownValue != 2) {
                            if ((data[i].voteDownValue != 0 || data[i].voteDownValue != 1) && data[i].voteDownValue) {
                                data[i].voteDownValue = 1;
                            } else {
                                data[i].voteDownValue = 0;
                            }
                        }
                        //无需评查
                        if ((data[i].inCheckValue != 0 || data[i].inCheckValue != 1) && data[i].inCheckValue) {
                            data[i].inCheckValue = 1;
                        } else {
                            data[i].inCheckValue = 0;
                        }
                        //评审依据的验证  //保存的时候必验
                        if (status == "1") {
                            var flag = data[i].voteDownValue;
                            var text = $("#comment" + i).val().replace(/\s+/g, "");
                            var resultScore = data[i].resultScore;
                            var inCheckValue = data[i].inCheckValue;
                            var isOperator = data[i].isOperator;

                            //扣分项扣分时,依据必填
                            if (isOperator == '0' && resultScore != 0) {
                                if (text == null || text == '') {
                                    $("#comment" + i).removeClass("textarea-success");
                                    $("#comment" + i).addClass("textarea-error");
                                    $("#comment" + i).focus();
                                    data[i].validatorFlag = false;
                                    data[i].validatorMessage = "此项评审依据必填";
                                    return false;
                                }
                            }

                            if (resultScore != null && resultScore == 0 && inCheckValue != 1 && data[i].indexname != '加分项'
                                && !data[i].indexname.includes('各类文书基本要素')
                                && !data[i].indexname.includes('现场检查（勘察）笔录/调查询问笔录基本要素')
                                && (this.scoringIndexList.closed === 0 ? !data[i].indexname.includes('结案审批表') : true)) {
                                if (text == null || text == '') {
                                    $("#comment" + i).removeClass("textarea-success");
                                    $("#comment" + i).addClass("textarea-error");
                                    $("#comment" + i).focus();
                                    data[i].validatorFlag = false;
                                    data[i].validatorMessage = "此项评审依据必填";
                                    return false;
                                }
                            } else {
                                $("#comment" + i).removeClass("textarea-error");
                                data[i].validatorMessage = "";
                            }

                            if (flag) {
                                if (text == null || text == '') {

                                    $("#comment" + i).removeClass("textarea-success");
                                    $("#comment" + i).addClass("textarea-error");
                                    $("#comment" + i).focus();
                                    data[i].validatorFlag = false;
                                    data[i].validatorMessage = "此项评审依据必填";
                                    return false;
                                }
                            }
                        }
                        //一级指标一票否决或无需评查，则跳出循环，无需校验二级指标
                        if (data[i].voteDownValue == 2 || data[i].voteDownValue == 1 || data[i].inCheckValue == 1) {
                            continue;
                        }

                        if (data[i].indexname.includes("结案审批表")) {
                            console.log("结案审批表", data[i])
                        }
                        if (data[i].expertHandlItemScoreList == null && (data[i].validatorFlag == null || !data[i].validatorFlag)) {
                            //此种判断应该不会走，一级指标分无需操作
                            myVue.scoringIndexList.expertHandlIndexScoreList[i].validatorFlag = false;
                            myVue.scoringIndexList.expertHandlIndexScoreList[i].validatorMessage = "分值项不能为空，在0-" + myVue.scoringIndexList.expertHandlIndexScoreList[i].indexscore + "分之间，且最多精确到小数点后两位小数！";
                            $("#expertIndex" + i).removeClass("has-success");
                            $("#expertIndex" + i).addClass("has-error");
                            $("#expertIndex" + i).focus();
                            return false;
                        } else {//判断二级指标验证是否通过
                            if (data[i].expertHandlItemScoreList != null) {
                                for (var j = 0; j < data[i].expertHandlItemScoreList.length; j++) {
                                    var standScore = data[i].expertHandlItemScoreList[j].itemscore//标准分
                                    var score = data[i].expertHandlItemScoreList[j].score;//输入分

                                    /*
                                    *无论暂存或是保存，都需要验证的是格式
                                    */
                                    if (score != null && score != "") {
                                        var re = /^-?\d+\.?\d{0,2}$/;
                                        if (standScore != 0 && (score > standScore || !(re.test(score)))) {
                                            myVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].validatorFlag = false;
                                            myVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].validatorMessage = "分值项不能为空，在0-" + myVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].itemscore + "分之间，且最多精确到小数点后两位小数！";
                                            $("#expertItem" + myVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].temItemId + "" + i).removeClass("has-success");
                                            $("#expertItem" + myVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].temItemId + "" + i).addClass("has-error");
                                            $("#" + i + "scoreInput" + j).focus();
                                            return false;
                                        }
                                        if (score.toString().indexOf('-') != -1) {
                                            myVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].validatorFlag = false;
                                            myVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].validatorMessage = "分值项不能为负数";
                                            $("#expertItem" + myVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].temItemId + "" + i).removeClass("has-success");
                                            $("#expertItem" + myVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].temItemId + "" + i).addClass("has-error");
                                            $("#" + i + "scoreInput" + j).focus();
                                            return false;
                                        }
                                    }

                                    /*
                                    *【保存】的时候验证必填项
                                    */
                                    if (status == "1") {
                                        if (standScore != 0) {//标准分不是0的是必填项
                                            if ((score == null || score === "")) {
                                                myVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].validatorFlag = false;
                                                myVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].validatorMessage = "分值项不能为空，在0-" + myVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].itemscore + "分之间，且最多精确到小数点后两位小数！";
                                                $("#expertItem" + myVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].temItemId + "" + i).removeClass("has-success");
                                                $("#expertItem" + myVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].temItemId + "" + i).addClass("has-error");
                                                $("#" + i + "scoreInput" + j).focus();
                                                return false;
                                            }
                                        } else {//此操作是为了后台验证通过
                                            myVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].validatorFlag = true;
                                            myVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].validatorMessage = "";
                                        }
                                    }


                                }
                            }
                        }
                        //}
                    }

                    $("#submitBtn").attr("disabled", "disabled");
                    $("#submitTemporaryBtn").attr("disabled", "disabled");
                    $("#submitBtn").text("提交中...");
                    $.ajax({
                        type: "post",
                        url: WEBPATH + '/zjpf/saveExpertScore.do',
                        data: {
                            id: id,
                            status: status,
                            scoringIndex: JSON.stringify(myVue.scoringIndexList)
                        },           //注意数据用{}
                        success: function (data) {  //成功

                            if (data.result == "success") {
                                swal({title: "保存成功", text: "", type: "success"});
                                $("#submitBtn").attr("disabled", false);
                                $("#submitTemporaryBtn").attr("disabled", false);
                                $("#submitBtn").text("保存");
                                if (status == "1") {
                                    //business.addMainContentParserHtml('zjpf/entityAScore.do?id=${expertFileId}','pageNum=${pageNum}');
                                    //myVue.reviewBtnCurrent = "entity";
                                    business.addMainContentParserHtml('zjpf/zjpfScore.do?id=' + id, 'pageNum=${pageNum}');
                                } else {
                                    business.addMainContentParserHtml('zjpf/zjpfScore.do?id=' + id, 'pageNum=${pageNum}');
                                }
                                return false;
                            } else {
                                if (data.code == "007") {
                                    swal("提示", "正在保存中……，请稍等片刻", "info");
                                } else if (data.code == "000") {//登录信息失效
                                    swal({
                                        title: "提示",
                                        text: data.message,
                                        type: "error",
                                        confirmButtonText: "确定",
                                        confirmButtonColor: "#ec6c62"
                                    }, function () {
                                        window.location.href = WEBPATH + "/index.do";
                                    });
                                }
                                $("#submitBtn").attr("disabled", false);
                                $("#submitTemporaryBtn").attr("disabled", false);
                                $("#submitBtn").text("保存");

                                return false;
                            }
                        }
                    });
                }
            },
            //接下来是实体程序评查
            initAiEntity: function () {
                if (this.scoringEntityList != null) {
                    var isEntityAiView = this.scoringEntityList.isEntityAiView;
                    // this.entityiAInfoColor();//20250709优秀案卷需求注释
                }
            },
            startEntitytion: function () {
                document.getElementById("jdmodal").style.display = "block";
                this.startEntityProgress();
            },
            closejdModal: function () {
                document.getElementById("jdmodal").style.display = "none";
            },
            confirmEntityRecognition: function () {
                this.closejdModal();
                // this.entityiAInfoColor();//20250709优秀案卷需求注释
                this.updateAiEntityView();
            },
            updateAiEntityView: function () {
                $.ajax({
                    type: "post",
                    url: WEBPATH + '/zjpf/updateEntityAiView.do',
                    data: {id: expertFileId},           //注意数据用{}
                    success: function (data) {  //成功
                    }
                });
            },
            startEntityProgress: function () {

                let progress = 0;
                const progressBar = document.getElementById("progressBar");
                const status = document.getElementById("jdstatus");
                const confirmButton = document.getElementById("confirmButton");

                const completionMessage = document.getElementById("completionMessage");
                const modalTitle = document.getElementById("modalTitle");

                const totalDuration = 15000; // 总时间 15 秒
                const totalSteps = 50; // 进度条分为 50 步
                const stepDuration = totalDuration / totalSteps; // 每步的持续时间
                const incrementPerStep = 100 / totalSteps; // 每步进度增加的百分比

                const interval = setInterval(function () {
                    if (progress < 100) {
                        // 增加固定的进度增量
                        progress += incrementPerStep;
                        // 确保进度不超过100%
                        if (progress > 100) {
                            progress = 100;
                        }
                        progressBar.style.width = progress + "%";
                        status.innerText = "进度：" + Math.round(progress) + "%";
                    }

                    if (progress >= 100) {
                        clearInterval(interval);
                        status.innerText = "已完成识别";
                        document.getElementById("modalTitle").style.display = "none"; // 隐藏标题
                        progressBar.parentNode.style.display = "none"; // 隐藏进度条
                        status.style.display = "none"; // 隐藏状态
                        // 显示完成信息
                        completionMessage.style.display = "block"; // 显示完成信息
                        confirmButton.style.display = "block"; // 显示确认按钮
                    }
                }, stepDuration) // 每步持续时间
            },
            // 疑似问题点击弹窗
            handleSuspectedIssue: function () {
                console.log('测试')
            },
            //ai评查内容赋值
            entityiAInfoColor: function () {
                <%--if (scoringEntityList != null && scoringEntityList.expertHandlIndexScoreList != null) {--%>
                <%--    console.log("处理AI数据:", scoringEntityList.expertHandlIndexScoreList);--%>
                <%--    var keywords = ["未提到", "没有提到", "未提供", "没有提供", "无法确定", "没有提及", "未在", "未明确提及", "没有明确提及", "未提及", "没有包含", "没有明确标注", "没有找到", "疑似存在问题"];--%>
                <%--    var expertHandlIndexScoreList = scoringEntityList.expertHandlIndexScoreList;--%>
                <%--    //循环大项--%>
                <%--    for (let i = 0; i < expertHandlIndexScoreList.length; i++) {--%>
                <%--        var expertHandlItemScoreList = expertHandlIndexScoreList[i].expertHandlItemScoreList;--%>
                <%--        //循环大项里面的小项--%>
                <%--        for (let j = 0; j < expertHandlItemScoreList.length; j++) {--%>
                <%--            var itemScore = expertHandlItemScoreList[j];--%>
                <%--            //判断小项不为空,并取出ai内容--%>
                <%--            if (itemScore != null && itemScore.aiInfo != null) {--%>
                <%--                console.log("ai评查项")--%>
                <%--                //var aiInfo = itemScore.aiInfo;--%>
                <%--                var formattedString = itemScore.htmlaiInfo;--%>
                <%--                var aiInfoId = 'aiInfoEntity' + i + j;--%>
                <%--                //var aiStatusId = 'aiStatus'+i+j;--%>
                <%--                let suspected_issue_id;--%>
                <%--                if (formattedString !== null && formattedString !== undefined) {--%>
                <%--                    var tagsContainer = document.getElementById(aiInfoId);--%>
                <%--                    tagsContainer.innerHTML = '';--%>
                <%--                    const tag = document.createElement("span");--%>

                <%--                    if (formattedString.includes('暂未发现问题')) {--%>
                <%--                        tag.innerHTML = "<span style='color: green; font-weight: bold;'>暂未发现问题</span> \n<br/>";--%>
                <%--                    } else if (formattedString.includes('疑似存在问题')) {--%>
                <%--                        suspected_issue_id = aiInfoId + 'suspected_issue'--%>
                <%--                        let iconSrc = "${webpath}/static/image2024/ai.png";--%>
                <%--                        let iconStyle = 'width:20px;height:20px;vertical-align: sub;margin-right:5px;cursor:pointer;'--%>
                <%--                        let iconHtm = '<img id="' + suspected_issue_id +--%>
                <%--                            '" src="' + iconSrc +--%>
                <%--                            '" style="' + iconStyle +--%>
                <%--                            '" data-idx="' + j +--%>
                <%--                            '" data-index="' + i + '">' + // 使用变量--%>
                <%--                            '</img>';--%>
                <%--                        // let iconHtm = '<i id="' + suspected_issue_id + '" class="' + iconClassName + '" style="' + iconStyle + '" ><i>'--%>
                <%--                        tag.innerHTML = iconHtm + formattedString--%>
                <%--                    } else {--%>
                <%--                        tag.innerHTML = "<span style='color: green; font-weight: bold;'>暂未发现问题</span> \n<br/>";--%>
                <%--                    }--%>
                <%--                    tagsContainer.appendChild(tag);--%>
                <%--                    const iconDom = document.getElementById(suspected_issue_id)--%>
                <%--                    if (iconDom) {--%>
                <%--                        let idx = iconDom.dataset.idx--%>
                <%--                        let index = iconDom.dataset.index--%>
                <%--                        let tdItem = expertHandlIndexScoreList[index].expertHandlItemScoreList[idx]--%>

                <%--                        iconDom.addEventListener('click', () => {--%>
                <%--                            openSuspectedIssueDialog()--%>
                <%--                            const regex = /疑似存在问题<\/span>\s*<span[^>]*>（(.*?)）<\/span>/;--%>
                <%--                            const match = tdItem['htmlaiInfo'].match(regex);--%>
                <%--                            let quesInfoMsg;--%>
                <%--                            if (match) {--%>
                <%--                                quesInfoMsg = match[1]--%>
                <%--                            }--%>

                <%--                            $.ajax({--%>
                <%--                                cache: true,--%>
                <%--                                type: 'GET',--%>
                <%--                                async: false,--%>
                <%--                                url: WEBPATH + '/ocr/zjpf/getResponseInfo.do',--%>
                <%--                                data: {--%>
                <%--                                    itemId: tdItem.itemid,--%>
                <%--                                    quesInfo: quesInfoMsg--%>
                <%--                                },--%>
                <%--                                error: function (error) {--%>
                <%--                                    swal('错误，请求异常！')--%>
                <%--                                },--%>
                <%--                                success: (data) => {--%>
                <%--                                    this.suspectedIssueTitle = quesInfoMsg--%>
                <%--                                    const el = document.getElementById('issue_stream_content')--%>
                <%--                                    const responseInfo = data.responseInfo || "小蓝还在学习中。暂无法给出有效整改建议";--%>

                <%--                                    if (el) {--%>
                <%--                                        this.optimizedTypeWriter(el, responseInfo, 50)--%>
                <%--                                    }--%>

                <%--                                }--%>
                <%--                            })--%>


                <%--                        })--%>
                <%--                    }--%>
                <%--                    tagsContainer.appendChild(document.createElement("br"));--%>
                <%--                }--%>
                <%--            }--%>
                <%--            //如果不是ai评查项,需要展示文字: 该项人工评查--%>
                <%--            if (itemScore != null && (itemScore.isAi == null || itemScore.isAi == '0')) {--%>
                <%--                var manualInfo = 'aiInfoEntity' + i + j;--%>
                <%--                console.log("该项人工评查:", manualInfo)--%>
                <%--                var spanInfo = document.getElementById(manualInfo);--%>

                <%--                if (spanInfo) {--%>
                <%--                    var newSpan = document.createElement("span");--%>
                <%--                    newSpan.innerHTML = "该项人工评查";--%>
                <%--                    spanInfo.appendChild(newSpan);--%>
                <%--                } else {--%>
                <%--                    console.error("Element does not exist: ", manualInfo);--%>
                <%--                }--%>
                <%--            }--%>
                <%--        }--%>
                <%--    }--%>
                <%--}--%>
            },
            optimizedTypeWriter: function (element, text, speed = 50) {
                if (!element || !text) {
                    element.textContent = '';
                    return
                }
                const startTime = performance.now();
                const length = text.length;
                element.textContent = '';

                function frame(time) {
                    const elapsed = time - startTime;
                    const charsToShow = Math.min(length, Math.floor(elapsed / speed));

                    element.textContent = text.substring(0, charsToShow);

                    if (charsToShow < length) {
                        requestAnimationFrame(frame);
                    }
                }

                requestAnimationFrame(frame);
            },
            showErrorMsgEntity: function (obj) {

                document.getElementById('isAgree_entity_0').disabled = false;
                document.getElementById('isAgree_entity_1').disabled = false;
                $.ajax({
                    type: "post",
                    url: WEBPATH + '/zjpf/getOtherExpertScore.do',
                    data: {
                        fileId: $("#fileId").val(),
                        expertId: $("#expertId").val(),
                        handlType: $("#handlType").val(),
                        indexId: obj.indexId,
                        itemid: obj.itemid
                    },
                    success: function (data) {  //成功
                        if (data.result == "success") {
                            console.log(data.data)
                            if (data.data.option == '0') {
                                $('#itemOption_entity').text("未否决");
                            } else if (data.data.option == '1') {
                                $('#itemOption_entity').text("已否决");
                            } else {
                                $('#itemOption_entity').text("无内容");
                            }

                            if (data.data.comme == null) {
                                $('#itemComme_entity').text("无内容");
                            } else {
                                $('#itemComme_entity').text(data.data.comme);
                            }
                            //是否认同  回显赋值
                            if (data.data.isAgree != null && data.data.isAgree != undefined) {
                                console.log("是否认同回显值:", data.data.isAgree)
                                var test = 'isAgree_entity_' + data.data.isAgree;
                                document.getElementById(test).checked = true;

                            } else {
                                document.getElementById('caseInfo').readOnly = false;
                                document.getElementById('isAgree_entity_0').disabled = false;
                                document.getElementById('isAgree_entity_1').disabled = false;
                            }
                            //是否认同的描述  回显赋值
                            if (data.data.caseInfo != null && data.data.caseInfo != undefined) {
                                $('#caseInfo').val(data.data.caseInfo);
                            }
                            //主键ID
                            $('#entity_error_id').val(data.data.id);
                            //现在是A专家还是B砖家
                            $('#isAOrB_entity').val(data.data.isAOrB);
                            //打开查看异常的弹窗
                            $("#error_dialog").dialog("open");

                        } else {
                            swal("信息", data.message, "error");
                        }
                    }
                });
            },
            checkBoxClick: function (textId) {//优秀典型案例推荐复选框点击事件
                if ($("#yxdxAnLiTuiJianReviews1").css("display") == 'none') {//如果show是隐藏的
                    $("#yxdxAnLiTuiJianReviews1").css("display", "block"); //  yxdxanlituijianreviews show的display属性设置为block（显示）
                    //$("#yxdxanlituijian").val("1");
                    myVue.scoringEntityList.yxdxanlituijian = "1";

                    //禁止反面推荐
                    $("#ajpjYxdxanlituijian").attr("disabled", "disabled");
                    $("#ajpjYxdxAnLiTuiJianReviews1").css("display", "none");
                    $('input:checkbox[name=jcaj]').attr('checked', false);
                    $('#ajpj').val("")

                    myVue.scoringEntityList.ajpjYxdxanlituijianreviews = "";
                    myVue.scoringEntityList.ajpjYxdxanlituijian = "0";
                    //禁止不推荐为典型案例
                    $("#noTuiJian").attr("disabled", "disabled");
                    myVue.scoringEntityList.noTuiJian = "0";
                } else {//如果show是显示的
                    myVue.scoringEntityList.yxdxanlituijianreviews = "";
                    myVue.scoringEntityList.yxdxanlituijian = "0";
                    $("#yxdxAnLiTuiJianReviews1").css("display", "none");//show的display属性设置为none（隐藏）

                    //放开反面推荐
                    $("#ajpjYxdxanlituijian").removeAttr("disabled");
                    //放开不推荐为典型案例
                    $("#noTuiJian").removeAttr("disabled");
                }
            },
            ajpjCheckBoxClick: function () {//是否进行案卷评价复选框点击事件
                if ($("#ajpjYxdxAnLiTuiJianReviews1").css("display") == 'none') {//如果show是隐藏的
                    $("#ajpjYxdxAnLiTuiJianReviews1").css("display", "block"); //  yxdxanlituijianreviews show的display属性设置为block（显示）
                    //$("#ajpjYxdxanlituijian").val("1");
                    myVue.scoringEntityList.ajpjYxdxanlituijian = "1";

                    //禁止优秀推荐
                    $("#yxdxanlituijian").attr("disabled", "disabled");
                    $("#yxdxAnLiTuiJianReviews1").css("display", "none");
                    $('input:checkbox[name=yxaj]').attr('checked', false);
                    $('#zjpy').val("")

                    myVue.scoringEntityList.yxdxanlituijianreviews = "";
                    myVue.scoringEntityList.yxdxanlituijian = "0";
                    //禁止不推荐为典型案例
                    $("#noTuiJian").attr("disabled", "disabled");
                    myVue.scoringEntityList.noTuiJian = "0";
                } else {//如果show是显示的
                    myVue.scoringEntityList.ajpjYxdxanlituijianreviews = "";
                    myVue.scoringEntityList.ajpjYxdxanlituijian = "0";
                    $("#ajpjYxdxAnLiTuiJianReviews1").css("display", "none");//show的display属性设置为none（隐藏）

                    //放开优秀推荐
                    $("#yxdxanlituijian").removeAttr("disabled");
                    //放开不推荐为典型案例
                    $("#noTuiJian").removeAttr("disabled");
                }
            },
            noCheckBoxClick: function () {
                var isChecked = $("#noTuiJian").prop('checked');
                if (isChecked) {
                    myVue.scoringEntityList.noTuiJian = "1";
                    //禁止优秀推荐
                    $("#yxdxanlituijian").attr("disabled", "disabled");
                    $("#yxdxAnLiTuiJianReviews1").css("display", "none");
                    myVue.scoringEntityList.yxdxanlituijianreviews = "";
                    myVue.scoringEntityList.yxdxanlituijian = "0";
                    $('input:checkbox[name=yxaj]').attr('checked', false);
                    $('#zjpy').val("")
                    //禁止反面推荐
                    $("#ajpjYxdxanlituijian").attr("disabled", "disabled");
                    $("#ajpjYxdxAnLiTuiJianReviews1").css("display", "none");
                    myVue.scoringEntityList.ajpjYxdxanlituijianreviews = "";
                    myVue.scoringEntityList.ajpjYxdxanlituijian = "0";
                    $('input:checkbox[name=jcaj]').attr('checked', false);
                    $('#ajpj').val("")
                } else {

                    myVue.scoringEntityList.noTuiJian = "0";
                    //放开反面推荐
                    $("#ajpjYxdxanlituijian").removeAttr("disabled");
                    //放开优秀推荐
                    $("#yxdxanlituijian").removeAttr("disabled");
                }

            },
            sumScoreEntity: function () {//判断是否有总分，没有则相加求分
                var entityScore = this.scoringEntityList.entityscore
                var indexObj = this.scoringEntityList.expertHandlIndexScoreList;

                if (entityScore == "" || entityScore == null) {
                    var indexStr = "";
                    if (indexObj != null && indexObj.length > 0) {
                        for (var i = 0; i < indexObj.length; i++) {
                            //整个保存完的逻辑
                            if (indexObj[i].resultScore != null) {
                                if (!isNaN(parseFloat(indexObj[i].resultScore))) {
                                    indexStr = indexStr + "," + indexObj[i].resultScore;
                                }
                            } else {
                                //弹框完成整个保存的逻辑
                                var itemScore = indexObj[i].expertHandlItemScoreList;
                                for (var j = 0; j < itemScore.length; j++) {
                                    indexStr = indexStr + "," + itemScore[j].score;
                                }
                            }

                        }
                    }

                    if (indexStr.indexOf("1") != -1) {
                        this.scoringEntityList.entityscore = 0;
                    } else if (indexStr.indexOf("1") == -1) {
                        this.scoringEntityList.entityscore = 50;
                    } else {
                        this.scoringEntityList.entityscore = 0;
                    }
                }
            },
            updateItemEntity: function (index, itemIndex, itemScore, id) {
                var value = myVue.scoringEntityList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].score;
                if (value != '' && value != null) {

                    $("#" + index + "expertItem" + itemIndex).removeClass("has-error");
                    $("#" + index + "expertItem" + itemIndex).addClass("has-success");
                    myVue.scoringEntityList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorFlag = true;
                    myVue.scoringEntityList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorMessage = "";

                    if (value == "1") {
                        // myVue.nopassDrawItem.commeStr = "";
                        // var comment = $("#"+index+"comment"+itemIndex).val();
                        // $("#"+index+"comment"+itemIndex).removeClass("textarea-success");
                        // //$("#"+index+"comment"+itemIndex).addClass("textarea-error");
                        // if(comment==null||comment==''){
                        // 	myVue.scoringEntityList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorFlag= false;
                        // 	myVue.scoringEntityList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorComment="评查结果若选择‘存在问题’，具体情形必填";
                        // 	myVue.nopassDrawItem = myVue.scoringEntityList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex];
                        // }
                        //
                        // $("#nopassDraw").addClass("show");
                        myVue.currentEditIndex = index;
                        myVue.currentEditItemIndex = itemIndex;
                        // 初始化弹框数据（新增逻辑）
                        var currentComment = myVue.scoringEntityList.expertHandlIndexScoreList[index]
                            .expertHandlItemScoreList[itemIndex].commeStr;
                        if (currentComment) {
                            myVue.parseCommentToFields(currentComment);
                        } else {
                            // 重置各字段
                            myVue.nopassDrawItem = {
                                commeStrOne: '',
                                commeStrTwo: '',
                                commeStrThree: '',
                                commeStrFive: '',
                                commeStrSix: '',
                                commeStrEight: ''
                            };
                        }

                        $("#nopassDraw").addClass("show");

                    } else {
                        myVue.scoringEntityList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].commeStr = "";
                        $("#" + index + "comment" + itemIndex).removeClass("textarea-error");
                        //$("#"+index+"comment"+itemIndex).addClass("textarea-success");
                        myVue.scoringEntityList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorFlag == true;
                        myVue.scoringEntityList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorComment = "";
                        $("#nopassDraw").removeClass("show");
                    }
                    myVue.nopassDrawItem.itemname = '';
                    myVue.nopassDrawItem.itemname = myVue.scoringEntityList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].itemname;

                    var indexObj = myVue.scoringEntityList.expertHandlIndexScoreList[index];
                    var indexScore = myVue.scoringEntityList.expertHandlIndexScoreList[index].indexscore;
                    var itemStr = "";
                    if (indexObj != null) {
                        for (var i = 0; i < indexObj.expertHandlItemScoreList.length; i++) {
                            if (!isNaN(parseFloat(indexObj.expertHandlItemScoreList[i].score))) {
                                itemStr = itemStr + "," + indexObj.expertHandlItemScoreList[i].score;
                            }
                        }
                    }
                    //一级指标分
                    if (itemStr.indexOf("1") != -1) {
                        myVue.scoringEntityList.expertHandlIndexScoreList[index].resultScore = 1;
                    } else {
                        myVue.scoringEntityList.expertHandlIndexScoreList[index].resultScore = 0;
                    }
                    //实体总分技算
                    var indexObj1 = myVue.scoringEntityList.expertHandlIndexScoreList;
                    var indexStr = "";
                    if (indexObj1 != null) {
                        for (var i = 0; i < indexObj1.length; i++) {
                            if (!isNaN(parseFloat(indexObj1[i].resultScore))) {
                                indexStr = indexStr + "," + indexObj1[i].resultScore;
                            }
                        }
                    }
                    if (indexStr.indexOf("1") != -1) {
                        myVue.scoringEntityList.entityscore = 0;
                    } else {
                        myVue.scoringEntityList.entityscore = 50;
                    }
                } else {
                    $("#" + index + "expertItem" + itemIndex).removeClass("has-success");
                    $("#" + index + "expertItem" + itemIndex).addClass("has-error");
                    myVue.scoringEntityList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorFlag = false;
                    myVue.scoringEntityList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorMessage = "请选择评查结果！";
                    return false;
                }
            },
            // 在Vue实例的methods中添加
            parseCommentToFields: function (comment) {
                if (!comment) {
                    this.nopassDrawItem = {
                        commeStrOne: '',
                        commeStrTwo: '',
                        commeStrThree: '',
                        commeStrFive: '',
                        commeStrSix: '',
                        commeStrEight: ''
                    };
                    return;
                }

                // 更健壮的解析逻辑
                let commeStrOne = '', commeStrTwo = '', commeStrThree = '',
                    commeStrFive = '', commeStrSix = '', commeStrEight = '';

                // 尝试解析格式化的内容
                const parts = comment.split('因此认定该案卷错误。');
                if (parts.length > 0) {
                    const mainPart = parts[0];

                    // 解析第一部分
                    const part1Match = mainPart.match(/该案卷在(.*?)处存在/);
                    if (part1Match) commeStrOne = part1Match[1].trim();

                    // 解析第二部分
                    const part2Match = mainPart.match(/处存在(.*?)问题,详见/);
                    if (part2Match) commeStrTwo = part2Match[1].trim();

                    // 解析文书部分
                    const part3Match = mainPart.match(/详见(.*?)文书,/);
                    if (part3Match) commeStrThree = part3Match[1].trim();

                    // 解析页码部分
                    const part4Match = mainPart.match(/文书,(.*?)页,/);
                    if (part4Match) commeStrFive = part4Match[1].trim();

                    // 解析错误描述
                    const part5Match = mainPart.match(/页,(.*?)错误,/);
                    if (part5Match) commeStrSix = part5Match[1].trim();
                }

                // 解析详情说明
                const detailMatch = comment.match(/详情说明：(.*)/);
                if (detailMatch) commeStrEight = detailMatch[1].trim();

                this.nopassDrawItem = {
                    commeStrOne,
                    commeStrTwo,
                    commeStrThree,
                    commeStrFive,
                    commeStrSix,
                    commeStrEight
                };
            },

            updateCommonEntity: function (index, itemIndex) {
                var radio = myVue.scoringEntityList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].score;
                if (radio != '' && radio != null) {
                    if (radio == "1") {
                        // var comment = $("#"+index+"comment"+itemIndex).val().replace(/\s+/g,"");
                        var comment = $("#" + index + "comment" + itemIndex).val();
                        if (comment) {
                            $("#" + index + "comment" + itemIndex).removeClass("textarea-success");
                            $("#" + index + "comment" + itemIndex).addClass("textarea-error");
                            myVue.scoringEntityList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorFlag = false;
                            myVue.scoringEntityList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorComment = "评查结果若选择‘是’，具体情形必填";
                        } else {
                            $("#" + index + "comment" + itemIndex).removeClass("textarea-error");
                            $("#" + index + "comment" + itemIndex).addClass("textarea-success");
                            myVue.scoringEntityList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorFlag = true;
                            myVue.scoringEntityList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorComment = "";
                        }
                    } else {
                        $("#" + index + "comment" + itemIndex).removeClass("textarea-error");
                        $("#" + index + "comment" + itemIndex).addClass("textarea-success");
                        myVue.scoringEntityList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorFlag = true;
                        myVue.scoringEntityList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorComment = "";
                    }
                } else {
                    $("#" + index + "comment" + itemIndex).removeClass("textarea-error");
                    $("#" + index + "comment" + itemIndex).addClass("textarea-success");
                    myVue.scoringEntityList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorFlag = true;
                    myVue.scoringEntityList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorComment = "";
                }
            },
            // 加分项专用的输入验证函数
            updateCommonPluses: function (index, itemIndex) {
                // 清除之前的错误样式
                var textareaId = index + 'pointsComment' + itemIndex;
                var textareaElement = document.getElementById(textareaId);
                if (textareaElement) {
                    textareaElement.style.borderColor = '';
                    textareaElement.style.boxShadow = '';
                }

                // 实时验证：如果选择了"符合"且有内容，清除验证消息
                if (myVue.scoringPlusesList && myVue.scoringPlusesList.expertHandlIndexScoreList &&
                    myVue.scoringPlusesList.expertHandlIndexScoreList[index] &&
                    myVue.scoringPlusesList.expertHandlIndexScoreList[index].expertHandlItemScoreList &&
                    myVue.scoringPlusesList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex]) {

                    var scoringItem = myVue.scoringPlusesList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex];
                    var commeStr = scoringItem.commeStr;

                    // 如果选择了"符合"且填写了具体说明，清除验证消息
                    if (scoringItem.score == "1") {
                        if (commeStr && commeStr.trim() !== ""){
                            scoringItem.validatorMessage = "";
                            $("#" + textareaId).removeClass("textarea-error");
                        }else {
                            scoringItem.validatorMessage = "此项具体说明必填";
                            $("#" + textareaId).addClass("textarea-error");
                        }
                    }
                }
            },
            // 加分项单选按钮change事件处理函数
            handlePlusesRadioChange: function (index, itemIndex) {
                if (myVue.scoringPlusesList && myVue.scoringPlusesList.expertHandlIndexScoreList &&
                    myVue.scoringPlusesList.expertHandlIndexScoreList[index] &&
                    myVue.scoringPlusesList.expertHandlIndexScoreList[index].expertHandlItemScoreList &&
                    myVue.scoringPlusesList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex]) {

                    var scoringItem = myVue.scoringPlusesList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex];
                    var commeStr = scoringItem.commeStr;
                    var textareaId = index + 'pointsComment' + itemIndex;
                    var textareaElement = document.getElementById(textareaId);

                    if (scoringItem.score == "1") {
                        // 选择"符合"时，检查具体说明是否填写
                        if (!commeStr || commeStr.trim() === "") {
                            // 显示红色提示
                            scoringItem.validatorMessage = "此项具体说明必填";
                            // 添加错误样式
                            if (textareaElement) {
                                textareaElement.style.borderColor = '#d9534f';
                                textareaElement.style.boxShadow = '0 0 0 0.2rem rgba(217, 83, 79, 0.25)';
                                // $("#" + textareaId).removeClass("textarea-success");
                                $("#" + textareaId).addClass("textarea-error");
                            }
                        } else {
                            // 已填写具体说明，清除提示
                            scoringItem.validatorMessage = "";
                            if (textareaElement) {
                                textareaElement.style.borderColor = '';
                                textareaElement.style.boxShadow = '';
                                $("#" + textareaId).removeClass("textarea-error");
                                // $("#" + textareaId).addClass("textarea-success");
                            }
                        }
                    } else {
                        // 选择"不符合"时，清除验证消息和错误样式
                        scoringItem.validatorMessage = "";
                        if (textareaElement) {
                            textareaElement.style.borderColor = '';
                            textareaElement.style.boxShadow = '';
                            $("#" + textareaId).removeClass("textarea-error");
                            // $("#" + textareaId).addClass("textarea-success");
                        }
                    }
                }
            },
            closeNopassDraw: function () {
                $("#nopassDraw").removeClass("show");
            },
            saveNopassDraw: function () {
                if (myVue.nopassDrawItem.commeStrOne == "" || myVue.nopassDrawItem.commeStrTwo == "" || myVue.nopassDrawItem.commeStrThree == "" || myVue.nopassDrawItem.commeStrFive == "" || myVue.nopassDrawItem.commeStrSix == "" || myVue.nopassDrawItem.commeStrEight == "") {
                    swal("提示信息", "相关输入框信息必填！", "error");
                } else {

                    // 组合具体情形内容
                    var comment = [
                        "该案卷在" + this.nopassDrawItem.commeStrOne + "处存在",
                        this.nopassDrawItem.commeStrTwo + "问题,详见",
                        this.nopassDrawItem.commeStrThree + "文书,",
                        this.nopassDrawItem.commeStrFive + "页,",
                        this.nopassDrawItem.commeStrSix + "错误,",
                        "因此认定该案卷错误。",
                        "详情说明：" + this.nopassDrawItem.commeStrEight
                    ].join(" ");

                    // 回填到原始项
                    var index = this.nopassDrawItem.index;
                    var itemIndex = this.nopassDrawItem.itemIndex;
                    this.scoringEntityList.expertHandlIndexScoreList[this.currentEditIndex].expertHandlItemScoreList[this.currentEditItemIndex].commeStr = comment;
                    // 关闭弹框
                    this.closeNopassDraw();
                    // let commeStr = "该案卷在" + myVue.nopassDrawItem.commeStrOne + "处存在" + myVue.nopassDrawItem.commeStrTwo + "问题，详见" + myVue.nopassDrawItem.commeStrThree + "文书，" + myVue.nopassDrawItem.commeStrFive + "页，" + myVue.nopassDrawItem.commeStrSix + "错误,因此认定该案卷错误。详情说明:" + myVue.nopassDrawItem.commeStrEight;
                    // myVue.nopassDrawItem.commeStr = commeStr;
                    // $("#nopassDraw").removeClass("show");
                    // 保存到后端
                    // $.ajax({
                    // 	type: "post",
                    // 	url: WEBPATH + '/zjpf/svaeIndexEntity.do',
                    // 	data: {
                    // 		id: myVue.nopassDrawItem.expertindexid,
                    // 		score: myVue.nopassDrawItem.score,
                    // 		commeStr: commeStr,
                    // 		normId: myVue.nopassDrawItem.normId,
                    // 		itemId: myVue.nopassDrawItem.itemid,
                    // 		commeStrOne: myVue.nopassDrawItem.commeStrOne,
                    // 		commeStrTwo: myVue.nopassDrawItem.commeStrTwo,
                    // 		commeStrThree: myVue.nopassDrawItem.commeStrThree,
                    // 		commeStrFive: myVue.nopassDrawItem.commeStrFive,
                    // 		commeStrSix: myVue.nopassDrawItem.commeStrSix,
                    // 		commeStrEight: myVue.nopassDrawItem.commeStrEight
                    // 	},
                    // 	success: function (data) {
                    // 		if (data.code == "200") {
                    // 			// 更新本地数据
                    // 			myVue.nopassDrawItem.commeStr = commeStr;
                    // 			myVue.nopassDrawItem.validatorFlag = true;
                    // 			myVue.nopassDrawItem.validatorComment = "";
                    //
                    // 			// 关闭模态框
                    // 			$("#nopassDraw").removeClass("show");
                    // 		} else {
                    // 			swal("提示信息", "保存失败！", "error");
                    // 		}
                    // 	}
                    // });
                }
            },
            saveSubmitPluses: function (id, status) {//0代表缓存，1代表保存
                // 验证是否推荐优秀案卷字段是否已选择
                var ajpjYxdxanlituijianCode = myVue.scoringPlusesList.ajpjYxdxanlituijianCode;
                if (ajpjYxdxanlituijianCode === null || ajpjYxdxanlituijianCode === undefined || ajpjYxdxanlituijianCode === '') {
                    swal("提示信息", "请选择是否推荐优秀案卷!", "error");
                    return false;
                }

                // 数据验证：检查是否有未选择的加分项
                if (myVue.scoringPlusesList != null) {
                    var hasUnselected = false;
                    var hasEmptyComment = false;
                    var emptyCommentItems = []; // 收集需要填写具体说明的项目
                    var indexList = myVue.scoringPlusesList.expertHandlIndexScoreList;

                    for (var i = 0; i < indexList.length; i++) {
                        if (indexList[i].expertHandlItemScoreList != null) {
                            var itemList = indexList[i].expertHandlItemScoreList;
                            for (var j = 0; j < itemList.length; j++) {
                                var value = itemList[j].score;
                                var commeStr = itemList[j].commeStr;

                                // 检查是否选择了符合/不符合
                                if (value === "" || value == null) {
                                    swal("保存失败", "请完成所有加分项的评查!", "error");
                                    hasUnselected = true;
                                    return false;
                                }

                                // 检查选择"符合"时具体说明是否必填
                                if (value == "1" && (commeStr === "" || commeStr == null || commeStr.trim() === "")) {
                                    hasEmptyComment = true;
                                    emptyCommentItems.push({
                                        indexName: indexList[i].indexname,
                                        itemName: itemList[j].itemname,
                                        indexIndex: i,
                                        itemIndex: j
                                    });

                                    // 为对应的textarea添加错误样式
                                    var textareaId = i + 'pointsComment' + j;
                                    var textareaElement = document.getElementById(textareaId);
                                    if (textareaElement) {
                                        textareaElement.style.borderColor = '#d9534f';
                                        textareaElement.style.boxShadow = '0 0 0 0.2rem rgba(217, 83, 79, 0.25)';
                                        $("#" + textareaId).addClass("textarea-error");
                                    }
                                }
                            }
                        }
                    }

                    if (hasUnselected) {
                        return false;
                    }

                    // 如果有选择"符合"但未填写具体说明的项目，提示用户并滚动定位
                    if (hasEmptyComment) {
                        // 滚动定位到第一个未填写的具体说明字段
                        var firstEmptyItem = emptyCommentItems[0];
                        var firstTextareaId = firstEmptyItem.indexIndex + 'pointsComment' + firstEmptyItem.itemIndex;
                        var firstTextareaElement = document.getElementById(firstTextareaId);
                        if (firstTextareaElement) {
                            firstTextareaElement.focus();
                            // 滚动到该元素位置
                            firstTextareaElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        }

                        // 设置验证消息
                        for (var k = 0; k < emptyCommentItems.length; k++) {
                            var item = emptyCommentItems[k];
                            if (myVue.scoringPlusesList.expertHandlIndexScoreList[item.indexIndex] &&
                                myVue.scoringPlusesList.expertHandlIndexScoreList[item.indexIndex].expertHandlItemScoreList &&
                                myVue.scoringPlusesList.expertHandlIndexScoreList[item.indexIndex].expertHandlItemScoreList[item.itemIndex]) {
                                myVue.scoringPlusesList.expertHandlIndexScoreList[item.indexIndex].expertHandlItemScoreList[item.itemIndex].validatorMessage = "此项具体说明必填";
                            }
                        }
                        return false;
                    }
                }

                // 发送AJAX请求保存加分项数据
                $.ajax({
                    type: "post",
                    url: WEBPATH + '/zjpf/savePlusesScore.do',
                    data: {
                        id: id,
                        scoringIndex: JSON.stringify(myVue.scoringPlusesList),
                        status: status,
                        plusesScore: myVue.plusesTotalScore  // 传递前端计算好的加分项得分
                    },
                    success: function (data) {
                        if (data.result == "success") {
                            swal({title: "保存成功", text: "", type: "success"});
                            if (status == '1') {
                                // 正式保存后返回列表页
                                business.addMainContentParserHtml('zjpf/zjpfList.do');
                            } else {
                                // 暂存后刷新当前页面
                                business.addMainContentParserHtml('zjpf/zjpfScore.do?id=' + id, 'pageNum=${pageNum}');
                            }
                            return false;
                        } else {
                            swal("操作失败", data.message, "error");
                        }
                    },
                    error: function (data) {
                        swal("操作失败", "网络异常，请稍后重试", "error");
                    }
                });
            },
            saveSubmitEntity: function (id, status) {//0代表缓存，1代表保存
                //保存之前,先判断有没有没确认的异常,如果有没确认的异常,需要弹窗提示, 先确认完异常
                if (status == '1') {
                    // var isYXchecked = $("#yxdxanlituijian").prop('checked');
                    // var isJCchecked = $("#ajpjYxdxanlituijian").prop('checked');
                    // var isNOchecked = $("#noTuiJian").prop('checked');
                    // if (isYXchecked == false && isJCchecked == false && isNOchecked == false) {
                    //     swal({title: "提示", text: "请选择案件状态！", type: "error"});
                    //     return false;
                    // }

                    var checked_array = new Array();
                    if (myVue.scoringEntityList.yxdxanlituijian == "1") {
                        var flag = false;
                        var flagtext = false;
                        $('input[name="yxaj"]:checked').each(function () {
                            checked_array.push($(this).val())
                            if (checked_array.indexOf("1000") != -1) {
                                var yxFileStr = $("#zjpy").val().replace(/\s+/g, "");
                                if (yxFileStr == null || yxFileStr == "") {

                                    flag = true;
                                }
                                if (yxFileStr.length > 2000) {
                                    flagtext = true;
                                }
                            }
                        });
                        if (flag) {
                            swal({title: "提示", text: "优秀典型案例推荐不能为空", type: "error"});
                            return false;
                        }
                        if (flagtext) {
                            swal({title: "提示", text: "案卷评价长度不能超过2000个字符", type: "error"});
                            return false;
                        }
                    }
                    //优秀典型案例选中 下边的子项必填一个
                    if (myVue.scoringEntityList.yxdxanlituijian == "1") {
                        var checked = $("input[name='yxaj']:checked");//获取复选框被选中状态
                        if (!(checked && checked.length > 0)) {//判断复选框是否被选中
                            swal({title: "提示", text: "优秀典型案例子项不能为空", type: "error"});
                            return false;
                        }
                    }
                    //反面典型案例
                    var flags = false;
                    var flagss = false;
                    var ajpjYxdxanlituijianreviews = myVue.scoringEntityList.ajpjYxdxanlituijianreviews;
                    if (myVue.scoringEntityList.ajpjYxdxanlituijian == "1") {
                        var checked_array = new Array();
                        $('input[name="jcaj"]:checked').each(function () {
                            checked_array.push($(this).val())
                            if (checked_array.indexOf("1000") != -1) {
                                if (ajpjYxdxanlituijianreviews == null || ajpjYxdxanlituijianreviews == "") {
                                    flags = true;
                                } else {
                                    if (ajpjYxdxanlituijianreviews.length > 2000) {
                                        flagss = true;
                                    }
                                }
                            }
                        });
                        if (flags) {
                            swal({title: "提示", text: "较差案例推荐不能为空", type: "error"});
                            return false;
                        }
                        if (flagss) {
                            swal({title: "提示", text: "案卷评价长度不能超过2000个字符", type: "info"});
                            return false;
                        }
                    }
                    //反面典型案例选中 下边的子项必填一个
                    if (myVue.scoringEntityList.ajpjYxdxanlituijian == "1") {
                        var checked = $("input[name='jcaj']:checked");//获取复选框被选中状态
                        if (!(checked && checked.length > 0)) {//判断复选框是否被选中
                            swal({title: "提示", text: "较差案例推荐子项不能为空", type: "error"});
                            return false;
                        }
                    }

                    if (myVue.scoringEntityList != null) {
                        var falg = false;
                        var index = myVue.scoringEntityList.expertHandlIndexScoreList;
                        for (var i = 0; i < index.length; i++) {
                            if (index[i].expertHandlItemScoreList != null) {
                                var item = index[i].expertHandlItemScoreList;
                                for (var j = 0; j < item.length; j++) {
                                    var value = item[j].score;
                                    var content = item[j].commeStr || "";
                                    if (value == '1' && content == "") {
                                        swal("操作失败", "评查结果若选择‘存在问题’，具体情形必填!", "error");
                                        falg = true
                                        return false;
                                    }
                                }
                            }
                        }
                        if (falg) {
                            return false;
                        }
                    }
                }


                if (myVue.scoringEntityList != null) {
                    var falg = false;
                    var index = myVue.scoringEntityList.expertHandlIndexScoreList;
                    for (var i = 0; i < index.length; i++) {
                        if (index[i].expertHandlItemScoreList != null) {
                            var item = index[i].expertHandlItemScoreList;
                            for (var j = 0; j < item.length; j++) {
                                var value = item[j].score;
                                if (value === "" || value == null) {
                                    myVue.scoringEntityList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].validatorFlag = false;
                                    myVue.scoringEntityList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].validatorMessage = "请选择评查结果";
                                    $("#" + i + "expertItem" + "" + j).removeClass("has-success");
                                    $("#" + i + "expertItem" + "" + j).addClass("has-error");
                                    $("[name='" + i + "scoreRadio" + "" + j + "']").focus();
                                    swal("操作失败", "请先开始评查!", "error");
                                    falg = true
                                    return false;
                                }
                            }
                        }
                    }
                    if (falg) {
                        return false;
                    }
                }

                //存入优秀案卷code
                var code_array = new Array();
                $('input[name="yxaj"]:checked').each(function () {
                    code_array.push($(this).val())
                });
                if (code_array.length == 0) {

                }
                myVue.scoringEntityList.ajpjYxdxanlituijianCode = code_array.join(',')

                //存入较差案卷code
                var jcajArr = new Array();
                $('input[name="jcaj"]:checked').each(function () {
                    jcajArr.push($(this).val())
                });
                myVue.scoringEntityList.jcajtuijianCode = jcajArr.join(',')

                $.ajax({
                    type: "post",
                    url: WEBPATH + '/zjpf/saveEntityScore.do',
                    data: {id: id, scoringIndex: JSON.stringify(myVue.scoringEntityList), status: status},           //注意数据用{}
                    success: function (data) {//成功
                        if (data.result == "success") {
                            swal({title: "保存成功", text: "", type: "success"});
                            if (status == '1') {
                                // business.addMainContentParserHtml('zjpf/zjpfList.do');
                                business.addMainContentParserHtml('zjpf/zjpfScore.do?id=' + id, 'pageNum=${pageNum}');

                            } else {
                                business.addMainContentParserHtml('zjpf/zjpfScore.do?id=' + id, 'pageNum=${pageNum}');

                            }
                            return false;
                        } else {
                            if (data.code == "007") {
                                swal("提示", "正在保存中……，请稍等片刻", "info");
                            } else if (data.code == "000") {//登录信息失效
                                swal({
                                    title: "提示",
                                    text: data.message,
                                    type: "error",
                                    confirmButtonText: "确定",
                                    confirmButtonColor: "#ec6c62"
                                }, function () {
                                    window.location.href = WEBPATH + "/index.do";
                                });
                            } else if (data.code == "400") {
                                swal({title: "提示", text: data.message, type: "error"});
                            } else if (data.code == "500") {
                                //实体评查,输入框不能为空,前端不会处理,直接在后端判断的---20230824
                                swal({title: data.data, text: data.message, type: "error"});
                            } else if (data.code == "5000") {
                                swal({title: data.data, text: data.message, type: "error"});
                            }
                            return false;
                        }
                    }
                });
            }
        }
    });

    // $('#my_dialo').dialog({
    // 	width : "900",
    // 	height : "450",
    // 	autoOpen : false,
    // 	resizable : false,
    // 	modal : true,
    // 	closed: true
    // });
    function Cancel() {
        //$('#my_dialo').dialog("close");
        $("#my_dialo").css("display", "none");
        myVue.sumScore();
        myVue.scoringIndexList.fileMaterials = '0';
    }

    function changeFileMaterials() {
        myVue.scoringIndexList.entityscore = 0;
        myVue.scoringIndexList.fileMaterials = '1';
        //$( "#my_dialo" ).dialog( "open" );
        $("#my_dialo").css("display", "block");
    }

    // $('#entity_footer_dialog').dialog({
    // 	title: "疑难（争议）问题描述及案卷推荐",
    // 	width : "900",
    // 	autoOpen : false,
    // 	resizable : false,
    // 	modal : true,
    // 	closed: true
    // });
    function confirmEntityDialo() {
        //$('#entity_footer_dialog').dialog("close");
        $("#entity_footer_dialog").css("display", "none");
    }

    function openEntityDialo() {
        //$( "#entity_footer_dialog" ).dialog( "open" );
        $("#entity_footer_dialog").css("display", "block");
    }
    function confirmPlusesDialo() {
        // 验证是否选择了推荐选项
        var ajpjYxdxanlituijianCode = myVue.scoringPlusesList.ajpjYxdxanlituijianCode;
        if (ajpjYxdxanlituijianCode === null || ajpjYxdxanlituijianCode === undefined || ajpjYxdxanlituijianCode === '') {
            // 显示错误提示
            $("#ajpjYxdxanlituijianCode_error").show();
            return false;
        } else {
            // 隐藏错误提示
            $("#ajpjYxdxanlituijianCode_error").hide();
        }

        $("#pluses_footer_dialog").css("display", "none");
    }

    function openPlusesDialo() {
        $("#pluses_footer_dialog").css("display", "block");
    }

    // $('#suspected_issue_dialog').closest('.panel').find('.panel-tool-close').css('display','block');
    function openSuspectedIssueDialog() {
        $('#suspected_issue_dialog').css('display', 'block')
    }

    function closeSuspectedIssueDialog() {
        $('#suspected_issue_dialog').css('display', 'none')
    }

    var a = myVue.scoringEntityList.yxdxanlituijian;
    $(function () {
        if (myVue.scoringEntityList.yxdxanlituijian == "1") {
            //$('#yxdxanlituijian').attr('checked', true)

            $("[name = yxdxanlituijian]:checkbox").attr("checked", true);
            $("#yxdxAnLiTuiJianReviews1").css("display", "block");

            //优秀案卷选项回显
            var checkArray = $("input[name='yxaj']");
            var codes = myVue.scoringEntityList.ajpjYxdxanlituijianCode;
            if (codes != null && codes != "") {
                var myArray = codes.split(",");
                for (var i = 0; i < myArray.length; i++) {
                    //获取所有复选框对象的value属性，然后，用checkArray[i]和他们匹配，如果有，则说明他应被选中
                    $.each(checkArray, function (j, checkbox) {
                        //获取复选框的value属性
                        var checkValue = $(checkbox).val();
                        console.log(j + "----" + checkValue)
                        if (myArray[i] == checkValue) {
                            $(checkbox).attr("checked", true);
                        }
                    })
                }
            }


            //禁止较差推荐
            $("#ajpjYxdxanlituijian").attr("disabled", "disabled");
            $("#ajpjYxdxAnLiTuiJianReviews1").css("display", "none");
            //禁止不推荐为典型案例
            $("#noTuiJian").attr("disabled", "disabled");
        }


        var ajpja = myVue.scoringEntityList.ajpjYxdxanlituijian;
        if (myVue.scoringEntityList.ajpjYxdxanlituijian == "1") {
            //$('#ajpjYxdxanlituijian').attr('checked', true)

            $("[name = ajpjYxdxanlituijian]:checkbox").attr("checked", true);
            $("#ajpjYxdxAnLiTuiJianReviews1").css("display", "block");

            //较差案卷选项回显
            var checkArray = $("input[name='jcaj']");
            var codes = myVue.scoringEntityList.jcajtuijianCode;
            if (codes != null && codes != "") {
                var myArray = codes.split(",");
                for (var i = 0; i < myArray.length; i++) {
                    //获取所有复选框对象的value属性，然后，用checkArray[i]和他们匹配，如果有，则说明他应被选中
                    $.each(checkArray, function (j, checkbox) {
                        //获取复选框的value属性
                        var checkValue = $(checkbox).val();
                        console.log(j + "----" + checkValue)
                        if (myArray[i] == checkValue) {
                            $(checkbox).attr("checked", true);
                        }
                    })
                }
            }


            //禁止优秀推荐
            $("#yxdxanlituijian").attr("disabled", "disabled");
            $("#yxdxAnLiTuiJianReviews1").css("display", "none");
            //禁止不推荐为典型案例
            $("#noTuiJian").attr("disabled", "disabled");
        }

        // 不推荐为典型案例 回显
        console.log("不推荐为典型案例 回显", myVue.scoringEntityList)
        if (myVue.scoringEntityList.noTuiJian == "1") {
            $("[name = noTuiJian]:checkbox").attr("checked", true);
            $("#noTuiJian").attr("checked", true);
            //禁止优秀推荐
            $("#yxdxanlituijian").attr("disabled", "disabled");
            $("#yxdxAnLiTuiJianReviews1").css("display", "none");
            //禁止较差推荐
            $("#ajpjYxdxanlituijian").attr("disabled", "disabled");
            $("#ajpjYxdxAnLiTuiJianReviews1").css("display", "none");
        }
    })

    //-----------------------------------------------------------------------------------------------------

    //开始评查
    var index = 0;
    var normId = 0;
    var itemId = 0;
    var commeStr0 = "";
    var commeStr3 = "";
    var commeStr2 = "";
    var commeStr3 = "";
    var commeStr4 = "";
    var scoress = ""
    var newCommeStr = ""

    function startComment() {
        $('#my_dialog').dialog({closed: false})
        $("#my_dialog").dialog("open");
        $("#textare").val("");
        $(".disNo").removeAttr("disabled");
        $("#topBnt").attr("disabled")

        $.ajax({
            type: "post",
            url: WEBPATH + '/zjpf/selectEntity.do',
            data: {id: expertFileId, index: index},
            success: function (data) {  //成功
                if (data.result == 'success') {


                    var itemData = data.data;
                    //console.log("-----------",itemData)
                    indexId = itemData.indexId;
                    itemId = itemData.itemid;
                    normId = itemData.normId;
                    // console.log("1",normId)
                    // var selectDev = $("input[name='mycheckbox']");
                    // // selectedlist = itemData.commeStr.split(',');
                    // 	//获取所有复选框对象的value属性，然后，用checkArray[i]和他们匹配，如果有，则说明他应被选中
                    // 	$.each(selectDev,function(j,checkbox){
                    // 		for(var i=0;i<arr.length;i++){
                    //
                    // 			//获取复选框的value属性
                    // 		var checkValue=$(checkbox).val().split(',');
                    // 		console.log(arr[i],$(checkbox).val(),'checkValue')
                    // 		// checkValue.map(item =>{
                    // 			if(arr[i]==$(checkbox).val()) {
                    // 				$(checkbox).attr("checked",true);
                    // 			}
                    // 		// })
                    // 		}
                    // 	})
                    commeStr0 = itemData.commeStr;
                    // newCommeStr= itemData.commeStr;
                    commeStr1 = itemData.commeStrOne;
                    commeStr2 = itemData.commeStrTwo;
                    commeStr3 = itemData.commeStrThree;
                    // commeStr4 = itemData.commeStrFour;
                    commeStr5 = itemData.commeStrFive;
                    commeStr6 = itemData.commeStrSix;
                    // commeStr7 = itemData.commeStrSeven;
                    commeStr8 = itemData.commeStrEight;


                    scoress = itemData.score;
                    if (scoress == null) {
                        $('#topBnts').hide()
                    } else {
                        $('#topBnts').show()
                    }
                    // console.log("itemData.score",itemData.score == parseFloat("1"))
                    $('#my_dialog').dialog({title: itemData.sort + "." + itemData.indexName})
                    $("#span1").html(itemData.itemname)
                    //console.log("11111",itemData.commeStr)
                    if (itemData.score == parseFloat("1")) {
                        $("#score1").prop("checked", true);
                        $('#selectdiv').hide()
                        //console.log("commeStrThree",itemData.commeStrThree,"commeStrTwo",itemData.commeStrTwo,"commeStrThree",itemData.commeStrThree)
                        $("#commeStrOne").val(itemData.commeStrOne == "" ? "" : itemData.commeStrOne);
                        $("#commeStrTwo").val(itemData.commeStrTwo == "" ? "" : itemData.commeStrTwo);
                        $("#commeStrThree").val(itemData.commeStrThree == "" ? "" : itemData.commeStrThree);
                        // $("#commeStrFour").val(itemData.commeStrFour ==""? "":itemData.commeStrFour);

                        $("#commeStrFive").val(itemData.commeStrFive == "" ? "" : itemData.commeStrFive);
                        $("#commeStrSix").val(itemData.commeStrSix == "" ? "" : itemData.commeStrSix);
                        // $("#commeStrSeven").val(itemData.commeStrSeven== ""? "":itemData.commeStrSeven);
                        $("#commeStrEight").val(itemData.commeStrEight == "" ? "" : itemData.commeStrEight);
                        // $("#score1").prop("checked", true);
                        // $(".ismycheck").val(itemData.commeStrThree);
                        $("#commeStrDiv").show();
                        $("#textare").hide();
                    }
                    if (itemData.score == parseFloat("0")) {
                        $("#score0").prop("checked", true);
                        $("#textare").show();
                        $("#commeStrDiv").hide();
                        if (itemData.commeStr != "") {
                            $("#textare").val(itemData.commeStr);
                        } else {
                            $("#textare").val("");
                        }
                    }
                    if (itemData.score == null) {
                        $("#commeStrDiv").hide();
                        $("#textare").hide();
                    }
                    if (commeStr3 != "" && commeStr2 != "" && commeStr3 != "") {
                        $("#commeStrOne").val(commeStr1);
                        $("#commeStrTwo").val(commeStr2);
                        $("#commeStrThree").val(commeStr3);
                        // $("#commeStrFour").val(commeStr4);
                        $("#commeStrFive").val(commeStr5);
                        $("#commeStrSix").val(commeStr6);
                        // $("#commeStrSeven").val(commeStr7);
                        $("#commeStrEight").val(commeStr8);


                    }
                }
            }
        });

    };

    //下一项
    function confirm() {

        $(".disNo").removeAttr("disabled");
        var score = $("input[name='score']:checked").val();
        // $("input[name='mycheckbox']:checked").prop("checked", false);
        var commeStr = $("#textare").val();//否的情况
        var commeStrOne = $("#commeStrOne").val();//是的情况
        var commeStrTwo = $("#commeStrTwo").val();//是的情况
        var commeStrThree = $("#commeStrThree").val();//是的情况
        // var commeStrFour=$("#commeStrFour").val();//是的情况
        var commeStrFive = $("#commeStrFive").val();//是的情况
        var commeStrSix = $("#commeStrSix").val();//是的情况
        // var	commeStrSeven=$("#commeStrSeven").val();//是的情况
        var commeStrEight = $("#commeStrEight").val();//是的情况


        if (score == null) {
            swal("提示信息", "请先选是否！", "error");
            return false;
        }

        if (score == '1' && (commeStrOne == "" || commeStrTwo == "" || commeStrThree == "" || commeStrFive == "" || commeStrSix == "" || commeStrEight == "")) {

            if (commeStrOne == "") {
                document.getElementById('commeStrOne').classList.add('error');
            } else {
                document.getElementById('commeStrOne').classList.remove('error');
            }
            if (commeStrTwo == "") {
                document.getElementById('commeStrTwo').classList.add('error');
            } else {
                document.getElementById('commeStrTwo').classList.remove('error');
            }
            if (commeStrThree == "") {
                document.getElementById('commeStrThree').classList.add('error');
            } else {
                document.getElementById('commeStrThree').classList.remove('error');
            }
            if (commeStrFive == "") {
                document.getElementById('commeStrFive').classList.add('error');
            } else {
                document.getElementById('commeStrFive').classList.remove('error');
            }
            if (commeStrSix == "") {
                document.getElementById('commeStrSix').classList.add('error');
            } else {
                document.getElementById('commeStrSix').classList.remove('error');
            }
            // if(commeStrSeven == ""){
            //     document.getElementById('commeStrSeven').classList.add('error');
            // }else {
            //     document.getElementById('commeStrSeven').classList.remove('error');
            // }
            if (commeStrEight == "") {
                document.getElementById('commeStrEight').classList.add('error');
            } else {
                document.getElementById('commeStrEight').classList.remove('error');
            }
            swal("提示信息", "相关输入框信息必填！", "error");

        } else {
            if (score == 1) {
                //是 拼接
                commeStr = "该案卷在" + commeStrOne + "处存在" + commeStrTwo + "问题，详见" + commeStrThree + "文书，" + commeStrFive + "页，" + commeStrSix + "错误,因此认定该案卷错误。详情说明:" + commeStrEight;

            }
            //保存大项小项
            $.ajax({
                type: "post",
                url: WEBPATH + '/zjpf/svaeIndexEntity.do',
                data: {
                    id: expertFileId,
                    score: score,
                    commeStr: commeStr,
                    normId: normId,
                    itemId: itemId,
                    commeStrOne: commeStrOne,
                    commeStrTwo: commeStrTwo,
                    commeStrThree: commeStrThree,
                    commeStrFour: '',
                    commeStrFive: commeStrFive,
                    commeStrSix: commeStrSix,
                    commeStrSeven: '',
                    commeStrEight: commeStrEight
                },
                success: function (data) {  //成功
                    //business.addMainContentParserHtml(WEBPATH+'/zjpf/entityAScore.do?id='+expertFileId+'&pageNum=1');
                    //window.location.Reload()


                    //window.history.back();

                    if (data.result == 'success') {
                        $.ajax({
                            type: "post",
                            url: WEBPATH + '/zjpf/selectEntity.do',
                            data: {id: expertFileId, index: index + 1},
                            success: function (data) {  //成功
                                if (data.result == 'success') {
                                    $("#topBnt").removeAttr("disabled")
                                    clearInput();
                                    $("#textare").val("");
                                    var itemData = data.data;
                                    indexId = itemData.indexId;
                                    itemId = itemData.itemid;
                                    normId = itemData.normId;
                                    index = index + 1;
                                    commeStr0 = itemData.commeStr;
                                    commeStr1 = itemData.commeStrOne;
                                    commeStr2 = itemData.commeStrTwo;
                                    commeStr3 = itemData.commeStrThree;
                                    // commeStr4 = itemData.commeStrFour;

                                    commeStr5 = itemData.commeStrFive;
                                    commeStr6 = itemData.commeStrSix;
                                    // commeStr7 = itemData.commeStrSeven;
                                    commeStr8 = itemData.commeStrEight;
                                    scoress = itemData.score;
                                    if (scoress == null) {
                                        $('#topBnts').hide()
                                    } else {
                                        $('#topBnts').show()
                                    }
                                    $('#my_dialog').dialog({title: itemData.sort + "." + itemData.indexName})
                                    $("#span1").html(itemData.itemname)
                                    // console.log("123123123",itemData.commeStr)
                                    if (itemData.score == parseFloat("1")) {
                                        $("#score1").prop("checked", true);

                                        $("#textare").hide();
                                        $("#commeStrOne").val(itemData.commeStrOne == "" ? "" : itemData.commeStrOne);
                                        $("#commeStrTwo").val(itemData.commeStrTwo == "" ? "" : itemData.commeStrTwo);
                                        $("#commeStrThree").val(itemData.commeStrThree == "" ? "" : itemData.commeStrThree);
                                        // $("#commeStrFour").val(itemData.commeStrFour ==""? "":itemData.commeStrFour);

                                        $("#commeStrFive").val(itemData.commeStrFive == "" ? "" : itemData.commeStrFive);
                                        $("#commeStrSix").val(itemData.commeStrSix == "" ? "" : itemData.commeStrSix);
                                        // $("#commeStrSeven").val(itemData.commeStrSeven== ""? "":itemData.commeStrSeven);
                                        $("#commeStrEight").val(itemData.commeStrEight == "" ? "" : itemData.commeStrEight);

                                        $("#commeStrDiv").show();
                                    }
                                    if (itemData.score == parseFloat("0")) {

                                        $("#score0").prop("checked", true);
                                        $("#textare").show();
                                        $("#commeStrDiv").hide();
                                        if (itemData.commeStr != "") {
                                            $("#textare").val(itemData.commeStr);
                                        } else {
                                            $("#textare").val("");
                                        }
                                    }

                                    if (itemData.score == null) {
                                        $("#commeStrDiv").hide();
                                        $("#textare").hide();
                                    }
                                    if (commeStr3 != "" && commeStr2 != "" && commeStr3 != "") {
                                        $("#commeStrOne").val(commeStr1);
                                        $("#commeStrTwo").val(commeStr2);
                                        $("#commeStrThree").val(commeStr3);
                                        // $("#commeStrFour").val(commeStr4);
                                        $("#commeStrFive").val(commeStr5);
                                        $("#commeStrSix").val(commeStr6);
                                        // $("#commeStrSeven").val(commeStr7);
                                        $("#commeStrEight").val(commeStr8);
                                    }
                                }
                                if (data.result == "null") {
                                    //关闭弹框
                                    $('#my_dialog').dialog("close");
                                    //刷新页面
                                    business.addMainContentParserHtml(WEBPATH + '/zjpf/entityAScore.do?id=' + expertFileId + '&pageNum=1');
                                }
                            }
                        });

                    }
                }
            });
            $("input[name='score']:checked").prop("checked", false);


        }

    };

    function confirms() {
        $(".disNo").removeAttr("disabled");
        var score = $("input[name='score']:checked").val();
        // $("input[name='mycheckbox']:checked").prop("checked", false);
        var commeStr = $("#textare").val();//否的情况
        var commeStrOne = $("#commeStrOne").val();//是的情况
        var commeStrTwo = $("#commeStrTwo").val();//是的情况
        var commeStrThree = $("#commeStrThree").val();//是的情况
        // var commeStrFour = $("#commeStrFour").val();//是的情况
        var commeStrFive = $("#commeStrFive").val();//是的情况
        var commeStrSix = $("#commeStrSix").val();//是的情况
        // var	commeStrSeven=$("#commeStrSeven").val();//是的情况
        var commeStrEight = $("#commeStrEight").val();//是的情况

        if (score == null) {
            swal("提示信息", "请先选是否！", "error");
            return false;
        }
        if (score == '1' && (commeStrOne == "" || commeStrTwo == "" || commeStrThree == "" || commeStrFive == "" || commeStrSix == "" || commeStrEight == "")) {
            if (commeStrOne == "") {
                document.getElementById('commeStrOne').classList.add('error');
            } else {
                document.getElementById('commeStrOne').classList.remove('error');
            }
            if (commeStrTwo == "") {
                document.getElementById('commeStrTwo').classList.add('error');
            } else {
                document.getElementById('commeStrTwo').classList.remove('error');
            }
            if (commeStrThree == "") {
                document.getElementById('commeStrThree').classList.add('error');
            } else {
                document.getElementById('commeStrThree').classList.remove('error');
            }
            if (commeStrFive == "") {
                document.getElementById('commeStrFive').classList.add('error');
            } else {
                document.getElementById('commeStrFive').classList.remove('error');
            }
            if (commeStrSix == "") {
                document.getElementById('commeStrSix').classList.add('error');
            } else {
                document.getElementById('commeStrSix').classList.remove('error');
            }
            // if(commeStrSeven == ""){
            //     document.getElementById('commeStrSeven').classList.add('error');
            // }else {
            //     document.getElementById('commeStrSeven').classList.remove('error');
            // }
            if (commeStrEight == "") {
                document.getElementById('commeStrEight').classList.add('error');
            } else {
                document.getElementById('commeStrEight').classList.remove('error');
            }
            swal("提示信息", "相关输入框信息必填！", "error");
        } else {
            if (score == 1) {
                commeStr = "该案卷在" + commeStrOne + "处存在" + commeStrTwo + "问题，详见" + commeStrThree + "文书，" + commeStrFive + "页，" + commeStrSix + "错误,因此认定该案卷错误。详情说明:" + commeStrEight;
            }
        }
        //保存大项小项
        $.ajax({
            type: "post",
            url: WEBPATH + '/zjpf/svaeIndexEntity.do',
            data: {
                id: expertFileId,
                score: score,
                commeStr: commeStr,
                normId: normId,
                itemId: itemId,
                commeStrOne: commeStrOne,
                commeStrTwo: commeStrTwo,
                commeStrThree: commeStrThree,
                // commeStrFour: commeStrFour,
                commeStrFive: commeStrFive,
                commeStrSix: commeStrSix,
                // commeStrSeven: commeStrSeven,
                commeStrEight: commeStrEight,
            },
            success: function (data) {  //成功
                if (data.result == 'success') {
                    //关闭弹框
                    $('#my_dialog').dialog("close");
                    //刷新页面
                    business.addMainContentParserHtml(WEBPATH + '/zjpf/entityAScore.do?id=' + expertFileId + '&pageNum=1');
                }
            }
        });
    }

    //上一项
    function CancelEntity() {

        // if(selectednamelist){
        // 	$("input[name='mycheckbox']:checked").prop("checked", true);
        // 	$("#commeStrThree").val(selectednamelist);
        // }else{
        // 	$("input[name='mycheckbox']:checked").prop("checked", false);
        // 	$("#commeStrThree").val('');
        // }
        $(".disNo").removeAttr("disabled");
        $.ajax({
            type: "post",
            url: WEBPATH + '/zjpf/selectEntity.do',
            data: {id: expertFileId, index: index - 1},
            success: function (data) {
                //成功
                if (data.result == 'success') {
                    var itemData = data.data;
                    index = index - 1;
                    indexId = itemData.indexId;
                    itemId = itemData.itemid;
                    normId = itemData.normId;
                    commeStr0 = itemData.commeStr;
                    commeStr1 = itemData.commeStrOne;
                    commeStr2 = itemData.commeStrTwo;
                    commeStr3 = itemData.commeStrThree;
                    // commeStr4 = itemData.commeStrFour;
                    commeStr5 = itemData.commeStrFive;
                    commeStr6 = itemData.commeStrSix;
                    // commeStr7 = itemData.commeStrSeven;
                    commeStr8 = itemData.commeStrEight;

                    scoress = itemData.score;
                    //console.log("indexId",indexId,"itemId",itemId,"normId",normId)
                    $('#my_dialog').dialog({title: itemData.sort + "." + itemData.indexName})
                    $("#span1").html(itemData.itemname)

                    if (itemData.score == parseFloat("1")) {
                        $("#score1").prop("checked", true);
                        $("#textare").hide();
                        $("#commeStrDiv").show();

                        $("#commeStrOne").val(itemData.commeStrOne == "" ? "" : itemData.commeStrOne);
                        $("#commeStrTwo").val(itemData.commeStrTwo == "" ? "" : itemData.commeStrTwo);
                        $("#commeStrThree").val(itemData.commeStrThree == "" ? "" : itemData.commeStrThree);
                        // $("#commeStrFour").val(itemData.commeStrFour ==""? "":itemData.commeStrFour);

                        $("#commeStrFive").val(itemData.commeStrFive == "" ? "" : itemData.commeStrFive);
                        $("#commeStrSix").val(itemData.commeStrSix == "" ? "" : itemData.commeStrSix);
                        // $("#commeStrSeven").val(itemData.commeStrSeven== ""? "":itemData.commeStrSeven);
                        $("#commeStrEight").val(itemData.commeStrEight == "" ? "" : itemData.commeStrEight);


                        // $("#ismycheck").val(itemData.commeStrThree == ""? "":itemData.commeStrThree);
                        // $("input[name='mycheckbox']:checked").prop("checked", true);
                    }
                    if (itemData.score == parseFloat("0")) {
                        $("#textare").show();
                        $("#commeStrDiv").hide();
                        $("#score0").prop("checked", true);
                        // $("input[name='mycheckbox']:checked").prop("checked", true);


                        if (itemData.commeStr != "" && itemData.commeStr != null) {
                            $("#textare").val(itemData.commeStr);
                        } else {
                            //清空否的文本框
                            $("#textare").val("");
                        }
                    }
                    if (itemData.score == null) {
                        $("#commeStrDiv").hide();
                        $("#textare").hide();
                    }

                    if (itemData.score != parseFloat("0") && itemData.score != parseFloat("1")) {
                        if (commeStr1 != "" && commeStr2 != "" && commeStr3 != "") {
                            $("#commeStrOne").val(commeStr1);
                            $("#commeStrTwo").val(commeStr2);
                            $("#commeStrThree").val(commeStr3);
                            // $("#commeStrFour").val(commeStr4);
                            $("#commeStrFive").val(commeStr5);
                            $("#commeStrSix").val(commeStr6);
                            // $("#commeStrSeven").val(commeStr7);
                            $("#commeStrEight").val(commeStr8);

                        }
                        //没有值 就清空
                        $("input[name='score']:checked").prop("checked", false);
                        $("input[name='mycheckbox']:checked").prop("checked", false);
                    }

                }
            }
        });
    };

    function updataRadio(type) {
        debugger
        console.log("传参是否值:----" + type)
        if (type == '1') {
            debugger
            if ((commeStr3 !== null && commeStr2 !== null && commeStr3 !== null) || (commeStr3 !== "" && commeStr2 !== "" && commeStr3 !== "")) {
                $("#commeStrOne").val(commeStr1);
                $("#commeStrTwo").val(commeStr2);
                $("#commeStrThree").val(commeStr3);
                // $("#commeStrFour").val(commeStr4);
                $("#commeStrFive").val(commeStr5);
                $("#commeStrSix").val(commeStr6);
                // $("#commeStrSeven").val(commeStr7);
                $("#commeStrEight").val(commeStr8);
            }
            //切换radio 清空内容
            if (scoress == "") {
                clearInput()
            }
            $("#commeStrDiv").show();
            $("#textare").hide();
        } else {
            //切换radio 清空内容
            if (commeStr0 == null || commeStr0 == "") {
                $("#textare").val("");
            }
            $("#commeStrDiv").hide();
            $("#textare").attr("style", "display:block;width:750px;height:270px;resize:none;");
            confirm();
        }
    }

    //点击下一项时，清空输入框
    function clearInput() {
        $("#commeStrOne").val("");
        $("#commeStrTwo").val("");
        $("#commeStrThree").val("");
        // $("#commeStrFour").val("");
        $("#commeStrFive").val("");
        $("#commeStrSix").val("");
        // $("#commeStrSeven").val("");
        $("#commeStrEight").val("");
        $(".ismycheck").val("");
        $("input[name='mycheckbox']:checked").prop("checked", false);

        // var selectedlist = [];
        //选中的股票名称（展示在前台给业务员看的）
        selectednamelist = [];
    }

    //设置不可点击 后显示蓝色不置灰
    //$(".radoos").css("pointer-events","none");
    //设置单选按钮不可点击
    //$(".radoos").attr("disabled","disabled");


    $('#caseInfo').on('input', function () {
        var text = $(this).val();
        var length = text.length;
        if (length > 499) {
            $(this).val(text.substring(0, 500));
            $('#error-message-500').text('(限制500字)');
        } else {
            $('#error-message-500').text('');

        }
    });
</script>

<body>
<%--重构布局开始--%>
<div id="zjpfVueTemplate" class="zjpf_vue_content">
    <%--面包屑导航--%>
    <div class="breadcrumb_wrap">
        <span class="back_btn" @click="macroMgr.onLevelOneMenuClick(null, 'zjpf.do')"><&nbsp;返回</span>
        <span>&nbsp;|&nbsp;</span>
        <span>专家评分/专家评审案卷/${filetypeName}/评分</span>
    </div>
    <%--案件编号--%>
    <%--	<div class="records_title">--%>
    <%--		<div>--%>
    <%--			<span id="pdfPreFileName">案卷文号：{{ pdfFileName }}</span>--%>
    <%--			<span style="margin-left: 10px; color: #4285f4; cursor: pointer"--%>
    <%--				  @click="handlePdfFileDownload"--%>
    <%--			>下载</span--%>
    <%--			>--%>
    <%--		</div>--%>
    <%--		<div style="margin-left: 20px">--%>
    <%--			<span>专家姓名：专家A</span>--%>
    <%--		</div>--%>
    <%--	</div>--%>
    <%--主体内容--%>
    <div class="score_main_content">
        <input id="pageNum" type="hidden" value="${pageNum}">
        <input id="expertFileId" type="hidden" value="${expertFileId}">
        <input id="fileId" type="hidden" value="${expertHandlFileList.fileid}">
        <input id="expertId" type="hidden" value="${expertHandlFileList.expertId}">
        <input id="handlType" type="hidden" value="${expertHandlFileList.handlType}">
        <input id="problemRemark" type="hidden" value="${expertHandlFileList.problemRemark}">
        <input id="scoredstate" type="hidden" value="${expertHandlFileList.scoredstate}">
        <div class="toggle_btn leftBtn" onclick="toggleLeftContent()">
            <i class="fas fa-chevron-left"></i>
        </div>
        <%-- 左侧预览--%>
        <div class="score_left_content">
            <div style="display: flex;align-items: center;height: 40px;">
                <span style="font-size: 16px;font-weight: 600">案卷预览</span>
                <span style="font-size: 14px;color: #8d8d8d;margin-left: 20px">案卷文号：</span>
                <span :title="pdfFileName"
                      style="max-width: 500px;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;color: rgba(0, 0, 0, 0.85);">{{ pdfFileName }}</span>
            </div>
            <div style="position: relative;">
                <!--工具栏-->
                <div class="pdfPre_toolbar">
                    <i class="fas fa-chevron-left"
                       :class="{ disabled: isPdfViewLoading ||  pdfCurrentPage <= 1 }"
                       @click="prevPage"></i>
                    <div style="display: flex; align-items: center; gap: 5px;">
                        <input type="number"
                               v-model.number="pdfCurrentPage"
                               :min="1"
                               :max="pdfTotalPages"
                               :disabled="isPdfViewLoading"
                               @change="handlePageInput"
                               style="width: 50px; text-align: center;"/>
                        <span>/ {{ pdfTotalPages }}</span>
                    </div>
                    <i class="fas fa-chevron-right"
                       :class="{ disabled: isPdfViewLoading || pdfCurrentPage >= pdfTotalPages }"
                       @click="nextPage"></i>
                    <i class="fas fa-search-plus"
                       :class="{ disabled: !pdfDoc || isPdfViewLoading ||  pdfCurrentScale === 2.0 }"
                       @click="!isPdfViewLoading && pdfDoc && zoomIn()"></i>
                    <i class="fas fa-search-minus"
                       :class="{ disabled: !pdfDoc || isPdfViewLoading || pdfCurrentScale === 0.5 }"
                       @click="!isPdfViewLoading && pdfDoc && zoomOut()"></i>
                    <span style="color: #4285f4; cursor: pointer" @click="handlePdfFileDownload">
						下载
					</span>
                    <select v-model="pdfCurrentScale"
                            :disabled="!pdfDoc || isPdfViewLoading"
                            @change="!isPdfViewLoading && pdfDoc && handleScaleChange()">
                        <option v-for="scale in scaleOptions" :key="scale.value" :value="scale.value">{{ scale.label
                            }}
                        </option>
                    </select>

                </div>
                <!--渲染容器-->
                <div class="pdf_canvas_container">
                    <div ref="pdfViewContainer" class="page_container">
                        <div
                                v-for="pageNum in pdfTotalPages"
                                :key="pageNum"
                                :id="'page-' + pageNum"
                                class="page-wrapper"
                                ref="pageRefs"
                        >
                            <!-- 每页的 canvas 元素 -->
                            <canvas
                                    :id="'canvas-' + pageNum"
                            ></canvas>
                            <%--						<!-- 高亮叠加层 -->--%>
                            <div :id="'text-layer-' + pageNum" class="text-layer"></div>

                        </div>
                    </div>
                    <!-- 加载状态遮罩层 -->
                    <%--					<div class="el-loading-mask" :class="{ hide: !isPdfViewLoading }">--%>
                    <%--						<div class="el-loading-spinner">--%>
                    <%--							<!-- 加载动画 SVG -->--%>
                    <%--							<svg class="circular" viewBox="25 25 50 50">--%>
                    <%--								<circle class="path" cx="50" cy="50" r="20" fill="none"/>--%>
                    <%--							</svg>--%>
                    <%--							<!-- 加载状态文本 -->--%>
                    <%--							<p class="el-loading-text">{{ pdfLoadingText }}</p>--%>
                    <%--						</div>--%>
                    <%--					</div>--%>
                </div>
            </div>

        </div>
        <%-- 右侧识别--%>
        <div class="score_right_content">
            <%--			<div class="toggle_btn" onclick="toggleLeftContent()">--%>
            <%--				<i class="fas fa-chevron-left"></i>--%>
            <%--			</div>--%>
            <div class="tab_wrap">
                <div style="display: flex; align-items: center; height: 60px">
                    <div
                            v-for="(tab,index) in tabList"
                            :key="tab.id"
                            class="tab_item"
                            :class="{active: tab.id === tabCurrentIndex}"
                            @click="handleClickTap(tab.id)"
                    >
                        <div class="tab_item_text">{{ tab.tabName }}</div>
                    </div>
                </div>
                <div class="tab_header_btn_container">
<%--                    <div class="tab_header_btn preview_btn" @click="handlePreview"><img--%>
<%--                            src="${webpath}/static/images/zjpf_preview.png"/>案卷概览--%>
<%--                    </div>--%>

                    <div v-show="tabCurrentIndex === '1' && reviewBtnCurrent=='rollSurface'"
                         class="tab_header_btn use_tips_btn" onclick="showInfoModal('rollSurface')"><img
                            src="${webpath}/static/images/zjpf_tips.png"/>使用说明
                    </div>
                    <div v-show="tabCurrentIndex === '1' && reviewBtnCurrent=='entity'"
                         class="tab_header_btn use_tips_btn" onclick="showInfoModal('entity')"><img
                            src="${webpath}/static/images/zjpf_tips.png"/>使用说明
                    </div>
                </div>
            </div>
            <div class="tab_content">
                <%--智能评查详情--%>
                <div v-if="tabCurrentIndex === '0'">
                    <%--基本要素评查--%>
                    <%--						<ied-component></ied-component>--%>
                    <%--					<div class="ied_content">--%>
                    <%--						<div class="ied_left_content">--%>
                    <%--							&lt;%&ndash;左侧表格&ndash;%&gt;--%>
                    <%--							<table class="ied_table">--%>
                    <%--								<thead>--%>
                    <%--								<tr>--%>
                    <%--									<th>检查项目</th>--%>
                    <%--									<th>分数更正</th>--%>
                    <%--									<th>问题标注</th>--%>
                    <%--								</tr>--%>
                    <%--								</thead>--%>
                    <%--								<tbody></tbody>--%>
                    <%--							</table>--%>
                    <%--						</div>--%>
                    <%--						&lt;%&ndash;右侧目录索引&ndash;%&gt;--%>
                    <%--						<div class="ied_right_content">索引</div>--%>
                    <%--					</div>--%>

                    <%--合法性评查--%>
                </div>
                <%--评分明细--%>

                    <%--卷面评分页面--%>
                    <%--<div v-show="tabCurrentIndex === '1' && reviewBtnCurrent == 'rollSurface'" class="tab_content_show">
                    <div class="score_title">
                        <div class="reviewBtn_wrap" style="min-width: 130px;">
&lt;%&ndash;                            <div class="reviewBtn_item" :class="{'active': reviewBtnCurrent === 'rollSurface'}">&ndash;%&gt;
&lt;%&ndash;                                <div style="padding: 6px 12px;" @click="handleCLickReviewBtn('rollSurface')">规范性评查</div>&ndash;%&gt;
&lt;%&ndash;                            </div>&ndash;%&gt;
                            <div class="reviewBtn_item" :class="{'active': reviewBtnCurrent === 'entity', 'reviewBtnDisable': ${(expertHandlFileList.fileMaterials == 1) ? 'true' : 'false'}}">
                                <div style="padding: 6px 12px;" @click="handleCLickReviewBtn('entity')">合法性评查</div>
                            </div>
                            <div class="reviewBtn_item" :class="{'active': reviewBtnCurrent === 'pluses', 'reviewBtnDisable': ${(expertHandlFileList.scoredstate == 0 || expertHandlFileList.scoredstate == 2 || expertHandlFileList.fileMaterials == 1) ? 'true' : 'false'}}">
                                <div style="padding: 6px 12px;" @click="handleCLickReviewBtn('pluses')">加分项</div>
                            </div>
                        </div>
&lt;%&ndash;                        <div class="result_container">&ndash;%&gt;
&lt;%&ndash;                            <span class="result_title">参与评查得分：</span>&ndash;%&gt;
&lt;%&ndash;                            <span class="result_text">{{ inputScore }}</span>&ndash;%&gt;
&lt;%&ndash;                        </div>&ndash;%&gt;
&lt;%&ndash;                        <div class="result_container">&ndash;%&gt;
&lt;%&ndash;                            <span class="result_title">参与评查标准分：</span>&ndash;%&gt;
&lt;%&ndash;                            <span class="result_text">{{ totalScore }}</span>&ndash;%&gt;
&lt;%&ndash;                        </div>&ndash;%&gt;



&lt;%&ndash;                        <div class="result_container">&ndash;%&gt;
&lt;%&ndash;                            <span class="result_title">智能识别得分：</span>&ndash;%&gt;
&lt;%&ndash;                            <span id="smartScore" class="result_text">{{ scoringIndexList.paperscore }}</span>&ndash;%&gt;
&lt;%&ndash;                        </div>&ndash;%&gt;

&lt;%&ndash;                        <div class="result_container">&ndash;%&gt;
&lt;%&ndash;                            <span class="result_title">最终卷面分：</span>&ndash;%&gt;
&lt;%&ndash;                            <span id="finalScore" class="result_sum_text"></span>&ndash;%&gt;
&lt;%&ndash;                        </div>&ndash;%&gt;

                        <div class="instructions_tip">
                            <div id="infoModal" class="custom-modal">
                                <div class="custom-modal-content">
                                    <span class="custom-close-button"
                                          onclick="closeInfoModal('rollSurface')">&times;</span>
                                    <h2 class="custom-modal-title">使用说明：</h2>
                                    <ol class="custom-modal-list">
                                        <li>1.本标准用于评查案卷卷面内容。</li>
                                        <li>2.可以根据证据类型和所发文书种类确定评查项目。</li>
                                        <li>3.卷面分 = 50 * 对应评查项目得分之和 / 参与评查标准分之和 - 基本要素扣分值之和。</li>
                                        <li>4.内容完整、规范、正确的，得相应分值。不完整、不规范或者不正确的，不得分。</li>
                                        <li><span class="custom-highlight">5.提示：无需评查是指根据案情需要，无需对此项文书进行评查！</span></li>
                                    </ol>
                                </div>
                            </div>
                            &lt;%&ndash;<div>
								<i class="fas fa-brain"></i>
								<span>开启智能评查</span>
							</div>&ndash;%&gt;
                            <div class="expand-btn-container" :class="expandStatus?'':'expand-hidden'"
                                 @click="expandEvents">
                                <div class="expand-icon"></div>
                                案卷目录
                            </div>
                        </div>
                    </div>
                    <div class="table-container">
                        &lt;%&ndash;卷面评查表格&ndash;%&gt;
                        <div class="table-box">
                            <form action="#" id="zjpfForm" method="post">
                                <table class="table-head" :style="{ left: scrollLeft }">
                                    <colgroup>
                                        <col style="width:60px">
                                        <col style="width:100px">
                                        <col style="width:228px">
                                        <col style="width:138px">
&lt;%&ndash;                                        <col style="width:118px">&ndash;%&gt;
&lt;%&ndash;                                        <col style="width:332px">&ndash;%&gt;
                                        <col style="width:80px">
                                        <col style="width:80px">
                                        <col style="width:118px">
                                        <col style="width:200px">
                                    </colgroup>
                                    <thead>
                                    <tr>
                                        <td height="30">序号</td>
                                        <td>评查项目</td>
                                        <td>评分细则</td>
                                        <td>得分值</td>
&lt;%&ndash;                                        <td>智能评分</td>&ndash;%&gt;
&lt;%&ndash;                                        <td>识别结果及内容</td>&ndash;%&gt;
                                        <td>一票否决</td>
                                        <td>无需评查</td>
                                        <td>得分</td>
                                        <td>评审依据</td>
                                    </tr>
                                    </thead>
                                </table>
                                <div class="table-body-box" @scroll="tableScroll($event)">
                                    <table class="table-body table table-bordered table-condensed">
                                        <colgroup>
                                            <col style="width:60px">
                                            <col style="width:100px">
                                            <col style="width:228px">
                                            <col style="width:138px">
&lt;%&ndash;                                            <col style="width:118px">&ndash;%&gt;
&lt;%&ndash;                                            <col style="width:332px">&ndash;%&gt;
                                            <col style="width:80px">
                                            <col style="width:80px">
                                            <col style="width:118px">
                                            <col style="width:200px">
                                        </colgroup>
                                        <tbody class="form-group">
                                        <template
                                                v-for="(scoringIndex, index) in scoringIndexList.expertHandlIndexScoreList">
                                            <tr v-for="(scoringItem, index1) in scoringIndex.expertHandlItemScoreList"
                                                :class="hoverId==scoringIndex.id?'tr-hover':''"
                                                :style="scoringIndex.isError==1?'background:#ECD9E0':''"
                                                @mouseover="hoverId=scoringIndex.id;" @mouseleave="hoverId=''"
                                                @click="tableToLink(scoringIndex.id)">
                                                <td :rowspan="scoringIndex?.expertHandlItemScoreList?.length || 1"
                                                    v-if="index1==0" height="30" align="center" :id="scoringIndex.id">
                                                    {{index+1}}
                                                </td>

                                                <!-- 评查项目 -->
                                                <td :rowspan="scoringIndex?.expertHandlItemScoreList?.length || 1"
                                                    v-if="index1==0 && scoringIndex.indexname=='结案表'"
                                                    :class="scoringIndex.className"
                                                    style="text-align:center;padding: 6px 20px">
                                                    {{scoringIndex.indexname}}
                                                    <span v-if="scoringIndexList.closed==1"
                                                          style="color:red;font-size:16px; font-weight:bold;">（已结案）</span>
                                                    <span v-if="scoringIndexList.closed==0"
                                                          style="color:red;font-size:16px; font-weight:bold;">（未结案）</span>

                                                    <p v-if="scoringIndex.isError==1"
                                                       style="margin:0 auto;width: 67px;height: 24px;background: #DF1912;border-radius: 3px;">
                                                        <a href="#"
                                                           style="font-size:14px;line-height: 24px;color: #ffffff"
                                                           v-on:click="showErrorMsg(scoringIndex)">存在争议</a>
                                                    </p>
                                                </td>
                                                <td :rowspan="scoringIndex?.expertHandlItemScoreList?.length || 1"
                                                    v-if="index1==0" style="text-align:center;padding: 6px 20px" v-else>
                                                    {{scoringIndex.indexname}}<span v-if="scoringIndex.isOperator=='0'"
                                                                                    style="color:red;">（扣分项）</span>
                                                    <p v-if="scoringIndex.isError==1 && scoringIndex.isAgree==null"
                                                       style="margin:0 auto;width: 67px;height: 24px;background: #DF1912;border-radius: 3px;">
                                                        <a href="#"
                                                           style="line-height: 24px;font-size:14px;color: #ffffff"
                                                           v-on:click="showErrorMsg(scoringIndex)">存在争议</a>
                                                    </p>
                                                    <p v-if="scoringIndex.isError==1 && scoringIndex.isAgree!=null"
                                                       style="margin:0 auto;width: 67px;line-height:24px;height: 24px;background: rgba(129,194,105,1);border-radius: 3px;">
                                                        <a href="#" style="font-size:14px;color: #ffffff"
                                                           v-on:click="showErrorMsg(scoringIndex)">查看争议</a>
                                                    </p>
                                                </td>

                                                <!-- 评分细则 -->
                                                <td style="border-bottom:solid 1px #ddd; border-right:solid 1px #ddd;vertical-align:middle;padding:6px 20px;">
                                                    <div style="vertical-align:middle;text-align:left">
                                                        {{scoringItem.itemname}}
                                                    </div>
                                                </td>
                                                <td style="border-bottom:solid 1px #ddd; border-right:solid 1px #ddd;vertical-align:middle;padding: 6px 20px">
                                                    <div v-if="scoringItem.itemscore == 0"
                                                         class="custom-checkbox text-center"
                                                         style="display: flex;align-items: center;justify-content: center">
                                                        <input name="scoreInput" type="checkbox"
                                                               :id="index+'scoreInput'+index1"
                                                               @change="changeCheckBoxNegative(index, index1)"
                                                               v-model="scoringItem.score"
                                                               style="width:16px; height:16px; display: table-cell;vertical-align: middle; text-align: center;">
                                                        <label :for="'checkbox'+index" class="checkbox-blue"
                                                               checked></label>
                                                    </div>
                                                    <div v-else :id="'expertItem'+index1+index"
                                                         style="display: flex;align-items: center;justify-content: center;flex-wrap: wrap">
                                                        <div style="display: flex;align-items: center;justify-content: center">
                                                            <div v-if="scoringItem.isOperator=='1'"
                                                                 style="color:red; font-size:20px;">＋
                                                            </div>
                                                            <div v-if="scoringItem.isOperator!='1'"
                                                                 style="margin:0; color:red; font-size:26px;">－
                                                            </div>

                                                            <div v-if="scoringIndex.voteDownValue == 1 || scoringIndex.voteDownValue == 2 || scoringIndex.inCheckValue == 1 || (scoringIndexList.closed == 0 ? scoringIndex.indexname.includes('结案审批表')  : false)">
                                                                <input disabled name="scoreInput"
                                                                       @input="updateItem(index,index1,scoringItem.itemscore,scoringItem.temItemId)"
                                                                       type="text" :class="'checkitem'+index"
                                                                       autocomplete="off"
                                                                       v-model="(scoringIndexList.closed == 0 && scoringIndex.indexname.includes('结案审批表')) ? 0 : scoringItem.score"
                                                                       class="form-control inputDisabled"
                                                                       :placeholder="scoringIndex.isOperator === '0' ? '请填写扣除分数' : '请输入初评得分'"
                                                                       style="width:80px">
                                                            </div>
                                                            <div v-else>
                                                                <input name="scoreInput"
                                                                       @input="updateItem(index,index1,scoringItem.itemscore,scoringItem.temItemId)"
                                                                       :id="index+'scoreInput'+index1" type="text"
                                                                       :class="'checkitem'+index" autocomplete="off"
                                                                       v-model="scoringItem.score"
                                                                       class="form-control inputDisabled"
                                                                       :placeholder="scoringIndex.isOperator === '0' ? '请填写扣除分数' : '请输入初评得分'"
                                                                       style="width:80px">
                                                            </div>
                                                        </div>
                                                        <div style="color:red; font-size: 12px;">
                                                            {{scoringItem.validatorMessage}}
                                                        </div>
                                                    </div>
                                                </td>
                                                &lt;%&ndash;智能评查得分&ndash;%&gt;
&lt;%&ndash;                                                <td style="border-bottom:solid 1px #ddd; border-right:solid 1px #ddd;vertical-align:middle;padding: 6px 20px;">&ndash;%&gt;
&lt;%&ndash;                                                    <div style="display: flex;align-items: center;justify-content: center">&ndash;%&gt;
&lt;%&ndash;                                                        <input type="text" style="width: 80px" disabled="disabled"&ndash;%&gt;
&lt;%&ndash;                                                               class="form-control" :id="'aiScore'+index+index1">&ndash;%&gt;
&lt;%&ndash;                                                    </div>&ndash;%&gt;
&lt;%&ndash;                                                </td>&ndash;%&gt;
                                                &lt;%&ndash;识别结果及内容&ndash;%&gt;
&lt;%&ndash;                                                <td onclick="showModal(this)"&ndash;%&gt;
&lt;%&ndash;                                                    style="border-right:solid 1px #ddd;border-bottom:solid 1px #ddd;  border-left:solid 1px #ddd;vertical-align:middle;white-space: nowrap; overflow: hidden; text-overflow: ellipsis;padding: 6px 20px;">&ndash;%&gt;
&lt;%&ndash;                                                    <div style="display: flex">&ndash;%&gt;
&lt;%&ndash;                                                        <div style="vertical-align:middle; margin:0"&ndash;%&gt;
&lt;%&ndash;                                                             :id="'aiStatus'+index+index1">&ndash;%&gt;
&lt;%&ndash;                                                        </div>&ndash;%&gt;
&lt;%&ndash;                                                        <div style="vertical-align:middle; margin:5px 0 0 5px;flex: 1;line-height: 20px;overflow: hidden;text-overflow: ellipsis;text-align: left"&ndash;%&gt;
&lt;%&ndash;                                                             :id="'aiInfo'+index+index1">&ndash;%&gt;
&lt;%&ndash;                                                        </div>&ndash;%&gt;
&lt;%&ndash;                                                    </div>&ndash;%&gt;
&lt;%&ndash;                                                </td>&ndash;%&gt;

                                                <!-- 一票否决 -->
                                                <td :rowspan="scoringIndex?.expertHandlItemScoreList?.length || 1"
                                                    v-if="index1==0" style="vertical-align:middle;">
                                                    <div class="custom-checkbox text-center"
                                                         v-if="(scoringIndex.isVoteDown == 1) && (scoringIndexList.closed == 0 ? !scoringIndex.indexname.includes('结案审批表')  : true)">
                                                        <input v-if="scoringIndex.inCheckValue != 1" name="scoreInput"
                                                               type="checkbox" :id="'isVoteDown'+index"
                                                               @change="changeCheckBoxCli(index,'1')"
                                                               v-model="scoringIndex.voteDownValue"
                                                               style="width:16px; height:16px; display: table-cell;vertical-align: middle; margin-left:18px;text-align: center;">
                                                        <input v-else name="scoreInput" type="checkbox"
                                                               :id="'isVoteDown'+index"
                                                               disabled v-model="scoringIndex.voteDownValue"
                                                               style="width:16px; height:16px; display: table-cell;vertical-align: middle; margin-left:18px;text-align: center;">
                                                        <label :for="'checkbox'+index" class="checkbox-blue"
                                                               checked></label>
                                                    </div>
                                                </td>

                                                <!-- 无需评查 -->
                                                <td :rowspan="scoringIndex?.expertHandlItemScoreList?.length || 1"
                                                    v-if="index1==0" style="vertical-align:middle;">
                                                    <div class="custom-checkbox text-center"
                                                         v-if="scoringIndex.isInCheck ==1">
                                                        <input v-if="scoringIndex.voteDownValue != 1" name="scoreInput"
                                                               type="checkbox" :id="'isInCheck'+index"
                                                               @change="changeCheckBoxCli(index,'2')"
                                                               v-model="scoringIndex.inCheckValue"
                                                               style="margin-left:18px;width:16px; height:16px;  display: table-cell;vertical-align: middle; text-align: center; ">
                                                        <input v-else name="scoreInput" type="checkbox"
                                                               :id="'isInCheck'+index"
                                                               disabled v-model="scoringIndex.inCheckValue"
                                                               style="margin-left:18px;width:16px; height:16px;  display: table-cell;vertical-align: middle; text-align: center; ">
                                                        <label for="checkbox1" class="checkbox-blue" checked></label>
                                                    </div>
                                                </td>

                                                <!-- 得分 -->
                                                <td :rowspan="scoringIndex?.expertHandlItemScoreList?.length || 1"
                                                    v-if="index1==0" style="vertical-align:middle;padding: 6px 20px">
                                                    <div style="display: flex;align-items: center;justify-content: center">
                                                        <input type="text" disabled="disabled"
                                                               v-model="(scoringIndexList.closed == 0 && scoringIndex.indexname.includes('结案审批表')) ? 0 : scoringIndex.resultScore"
                                                               style="width: 80px"
                                                               class="form-control">
                                                    </div>
                                                </td>

                                                <!-- 评审依据 -->
                                                <td :rowspan="scoringIndex?.expertHandlItemScoreList?.length || 1"
                                                    v-if="index1==0" style="padding: 6px 20px">
															<textarea maxlength="1000" @input="updateCommon(index)"
                                                                      :id="'comment'+index"
                                                                      :rows="scoringIndex.expertHandlItemScoreList.length"
                                                                      class="form-control"
                                                                      v-model="scoringIndex.comment"
                                                                      placeholder="请输入评审依据，长度不能超过1000个字符"
                                                                      style="width:100%;min-height: 60px;">
															</textarea>
                                                    <div style="color:red; font-size: 12px;">
                                                        {{scoringIndex.validatorMessage}}
                                                    </div>
                                                </td>
                                            </tr>
                                        </template>

                                        &lt;%&ndash;<template v-for="(scoringIndex, index) in scoringIndexList.expertHandlIndexScoreList" :class="scoringIndex.className" v-if="scoringIndex.isError==2" >
                                            <tr v-for="(scoringItem, index1) in scoringIndex.expertHandlItemScoreList" style="background-color: #ECD9E0" @click="activeId=scoringIndex.id">
                                                <td :rowspan="scoringIndex?.expertHandlItemScoreList?.length || 1" height="30" align="center" >{{index+1}}</td>

                                                <!-- 评查项目 -->
                                                <td :rowspan="scoringIndex?.expertHandlItemScoreList?.length || 1" v-if="scoringIndex.indexname=='结案表'" :class="scoringIndex.className" style="text-align:center;">
                                                    {{scoringIndex.indexname}}
                                                    <span v-if="scoringIndexList.closed==1" style="color:red;font-size:16px; font-weight:bold;">（已结案）</span>
                                                    <span v-if="scoringIndexList.closed==0" style="color:red;font-size:16px; font-weight:bold;">（未结案）</span>

                                                    <p v-if="scoringIndex.isError==1" style="margin:0 auto;width: 67px;height: 24px;background: #DF1912;border-radius: 3px;">
                                                        <a href="#" style="font-size:14px;line-height: 24px;color: #ffffff"  v-on:click="showErrorMsg(scoringIndex)">存在争议</a>
                                                    </p>
                                                </td>
                                                <td :rowspan="scoringIndex?.expertHandlItemScoreList?.length || 1" style="text-align:center;" v-else>
                                                    {{scoringIndex.indexname}}<span v-if="scoringIndex.isOperator=='0'" style="color:red;">（扣分项）</span>
                                                    <p v-if="scoringIndex.isError==1" style="margin:0 auto;width: 67px;height: 24px;background: #DF1912;border-radius: 3px;">
                                                        <a href="#" style="line-height: 24px;font-size:14px;color: #ffffff"    v-on:click="showErrorMsg(scoringIndex)">存在争议</a>
                                                    </p>
                                                </td>

                                                <!-- 标准分 -->
                                                <td :rowspan="scoringIndex?.expertHandlItemScoreList?.length || 1" style="display: flex;align-items: center;justify-content: center">{{scoringIndex.indexscore}}</td>

                                                <!-- 评分细则 -->

                                                <td style="border-bottom:solid 1px #ddd; border-right:solid 1px #ddd;vertical-align:middle;">
                                                    <div style="vertical-align:middle; margin:0 5px 5px 0;">{{scoringItem.itemname}}</div>
                                                </td>
                                                <td style="border-bottom:solid 1px #ddd; width:155px;vertical-align:middle;">
                                                    <div v-if="scoringItem.itemscore == 0" class="custom-checkbox text-center" >
                                                        <input name="scoreInput" type="checkbox" :id="index+'scoreInput'+index1"
                                                            @change="changeCheckBoxNegative(index, index1)" v-model="scoringItem.score"
                                                            style="width:16px; height:16px; display: table-cell;vertical-align: middle; text-align: center;">
                                                        <label :for="'checkbox'+index" class="checkbox-blue" checked></label>
                                                    </div>
                                                    <div v-else :id="'expertItem'+index1+index" style="margin:0 0 5px 0;">
                                                        <div v-if="scoringItem.isOperator==1" style="float:left; padding:5px 2px 7px 3px; color:#00982d; font-size:20px;">＋</div>
                                                        <div v-if="scoringItem.isOperator!=1" style="float:left; padding:0 0 3px 0; margin:0; color:#ff4d4f; font-size:26px;">－</div>

                                                        <div v-if="scoringIndex.voteDownValue == 1 || scoringIndex.voteDownValue == 2 || scoringIndex.inCheckValue == 1|| (scoringIndexList.closed == 0 ? scoringIndex.indexname.includes('结案审批表') : false)"
                                                            style="float:right; padding-top:4px;">
                                                            <input disabled name="scoreInput" @input="updateItem(index,index1,scoringItem.itemscore,scoringItem.temItemId)"
                                                                 type="text" :class="'checkitem'+index" autocomplete="off"  v-model="scoringItem.score"
                                                                class="form-control inputDisabled"
                                                                   :placeholder="scoringIndex.isOperator === '0' ? '请填写扣除分数' : '请输入初评得分'"
                                                                   style="width:80px;">
                                                        </div>
                                                        <div v-else style="float:right; padding-top:4px;">
                                                            <input name="scoreInput" @input="updateItem(index,index1,scoringItem.itemscore,scoringItem.temItemId)"
                                                                :id="index+'scoreInput'+index1"  type="text" :class="'checkitem'+index" autocomplete="off"  v-model="scoringItem.score"
                                                                class="form-control inputDisabled"
                                                                   :placeholder="scoringIndex.isOperator === '0' ? '请填写扣除分数' : '请输入初评得分'"
                                                                   style="width:80px;">
                                                        </div>

                                                        <div style="color:red; font-size: 12px;">{{scoringItem.validatorMessage}}</div>
                                                    </div>
                                                </td>

                                                 <!-- 一票否决 -->
                                                <td :rowspan="scoringIndex?.expertHandlItemScoreList?.length || 1" style="vertical-align:middle;">
                                                    <div class="custom-checkbox text-center" v-if="scoringIndex.isVoteDown == 1 && (scoringIndexList.closed == 0 ? !scoringIndex.indexname.includes('结案审批表')  : true)" >
                                                        <input v-if="scoringIndex.inCheckValue != 1" name="scoreInput" type="checkbox" :id="'isVoteDown'+index"
                                                            @change="changeCheckBoxCli(index,'1')" v-model="scoringIndex.voteDownValue"
                                                            style="width:16px; height:16px; display: table-cell;vertical-align: middle; margin-left:18px;text-align: center;">
                                                        <input v-else name="scoreInput" type="checkbox" :id="'isVoteDown'+index"
                                                            disabled v-model="scoringIndex.voteDownValue"
                                                            style="width:16px; height:16px; display: table-cell;vertical-align: middle; margin-left:18px;text-align: center;">
                                                        <label :for="'checkbox'+index" class="checkbox-blue" checked></label>
                                                    </div>
                                                </td>

                                                <!-- 无需评查 -->
                                                <td :rowspan="scoringIndex?.expertHandlItemScoreList?.length || 1" style="vertical-align:middle;">
                                                    <div class="custom-checkbox text-center" v-if="scoringIndex.isInCheck ==1">
                                                        <input v-if="scoringIndex.voteDownValue != 1" name="scoreInput" type="checkbox" :id="'isInCheck'+index"
                                                             @change="changeCheckBoxCli(index,'2')"  v-model="scoringIndex.inCheckValue"
                                                            style="margin-left:18px;width:16px; height:16px;  display: table-cell;vertical-align: middle; text-align: center; ">
                                                        <input v-else name="scoreInput" type="checkbox" :id="'isInCheck'+index"
                                                             disabled  v-model="scoringIndex.inCheckValue"
                                                            style="margin-left:18px;width:16px; height:16px;  display: table-cell;vertical-align: middle; text-align: center; ">
                                                        <label for="checkbox1" class="checkbox-blue" checked></label>
                                                    </div>
                                                </td>

                                                <!-- 得分 -->
                                                <td :rowspan="scoringIndex?.expertHandlItemScoreList?.length || 1" style="vertical-align:middle;">
                                                    <div style="display: flex;align-items: center;justify-content: center">
                                                           <input type="text" style="width: 80px" disabled="disabled" v-model="scoringIndex.resultScore" class="form-control">
                                                       </div>
                                                </td>

                                                <!-- 评审依据 -->
                                                <td :rowspan="scoringIndex?.expertHandlItemScoreList?.length || 1">
                                                    <textarea maxlength="1000" @input="updateCommon(index)" :id="'comment'+index" :rows="scoringIndex.expertHandlItemScoreList.length"
                                                        class="form-control" v-model="scoringIndex.comment" placeholder="请输入评审依据，长度不能超过1000个字符" style="width:100%;min-height: 60px;">
                                                    </textarea>
                                                    <div style="color:red; font-size: 12px;">{{scoringIndex.validatorMessage}}</div>
                                                </td>
                                            </tr>
                                        </template>&ndash;%&gt;
                                        </tbody>
                                    </table>
                                </div>
                            </form>

                        </div>
                        &lt;%&ndash;列表锚点&ndash;%&gt;
                        <div class="table-link" :class="expandStatus?'':'table-link-hidden'">
                            <div v-for="(scoringIndex, index) in scoringIndexList.expertHandlIndexScoreList"
                                 class="link-container" :class="scoringIndex.id==activeId?'active':''"
                                 :id="scoringIndex.id+'link'">
                                <div class="link-line"></div>
                                <a v-if="scoringIndex.indexname=='结案表'" @click.prevent="tableLink(scoringIndex.id)">
                                    {{scoringIndex.indexname}}
                                    <span v-if="scoringIndexList.closed==1"
                                          style="color:red;font-size:16px; font-weight:bold;">（已结案）</span>
                                    <span v-if="scoringIndexList.closed==0"
                                          style="color:red;font-size:16px; font-weight:bold;">（未结案）</span>
                                </a>
                                <a v-else @click.prevent="tableLink(scoringIndex.id)">
                                    {{scoringIndex.indexname}}<span v-if="scoringIndex.isOperator=='0'"
                                                                    style="color:red;">（扣分项）</span>
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="score-footer">
                        <div class="score-warning-btn" onclick="changeFileMaterials()"><img
                                src="${webpath}/static/images/zjpf_warning.svg"/>案卷材料严重不全
                        </div>
                        <div class="score-warning-tips">
                            案卷材料严重不全，导致无法全面评价案卷实体和程序问题。具体情形：案卷中只有三项或不足三项，无相关决定性文书，导致无法全面评价案卷规范性、合法性。
                        </div>
                        <div class="score-submit">
                            &lt;%&ndash;							<div v-if="scoringIndexList.scoredstate == 0 || scoringIndexList.scoredstate == 2" class="submitTemporaryBtn" id="submitTemporaryBtn" v-on:click="saveSubmit(scoringIndexList.id, '0')">暂存</div>&ndash;%&gt;
                            <div class="submitBtn" id="submitBtn" v-on:click="saveSubmit(scoringIndexList.id, '1')">保存
                            </div>
                        </div>
                    </div>
                </div>--%>
                <%--实体评分页面--%>
                <div v-show="tabCurrentIndex === '1' && reviewBtnCurrent == 'entity'" class="tab_content_show">
                    <div class="score_title">
                        <div class="reviewBtn_wrap" style="min-width: 130px;">
<%--                            <div class="reviewBtn_item" :class="{'active': reviewBtnCurrent === 'rollSurface'}">--%>
<%--                                <div style="padding: 6px 12px;" @click="handleCLickReviewBtn('rollSurface')">规范性评查</div>--%>
<%--                            </div>--%>
<%--                            <div class="reviewBtn_item" :class="{'active': reviewBtnCurrent === 'entity', 'reviewBtnDisable': ${(expertHandlFileList.scoredstate == 0 || expertHandlFileList.scoredstate == 2 || expertHandlFileList.fileMaterials == 1) ? 'true' : 'false'}}">--%>
                            <div class="reviewBtn_item" :class="{'active': reviewBtnCurrent === 'entity'}">
                                <div style="padding: 6px 12px;" @click="handleCLickReviewBtn('entity')">合法性评查</div>
                            </div>
                            <div class="reviewBtn_item" :class="{'active': reviewBtnCurrent === 'pluses', 'reviewBtnDisable': ${(expertHandlFileList.scoredstate == 0 || expertHandlFileList.scoredstate == 2 || expertHandlFileList.fileMaterials == 1) ? 'true' : 'false'}}">
                                <div style="padding: 6px 12px;" @click="handleCLickReviewBtn('pluses')">加分项</div>
                            </div>
                        </div>
                        <div class="result_container">
                            <span class="result_title">存在问题：</span>
                            <span class="result_text">{{ nopassNum }}项</span>
                        </div>
                        <div class="result_container">
                            <span class="result_title">不存在问题：</span>
                            <span class="result_text">{{ passNum }}项</span>
                        </div>
                        <div class="result_container">
                            <span class="result_title">未评查：</span>
                            <span class="result_text">{{ pendingNum }}项</span>
                        </div>
<%--                        <div class="result_container">--%>
<%--                            <span class="result_title">合计得分：</span>--%>
<%--                            <span class="result_sum_text">{{ totalScoreEntity }}</span>--%>
<%--                            <input disabled id="jsScore" type="text" v-model="scoringEntityList.entityscore"--%>
<%--                                   class="form-control" style="display: none">--%>
<%--                        </div>--%>


                        <%--						<button class="btn btn-info"  onclick="startComment()" style="width: 120px;height: 40px;padding:6px 34px">开始评查</button>--%>
                        <div class="instructions_tip">
                            <div id="infoEntityModal" class="custom-modal">
                                <div class="custom-modal-content">
                                    <span class="custom-close-button" onclick="closeInfoModal('entity')">&times;</span>
                                    <h2 class="custom-modal-title">使用说明：</h2>
                                    <ol class="custom-modal-list">
                                        <li>1.任意一小项选择“存在问题”，则扣全50分；每一小项都是必选项</li>
                                        <li>2.评审说明：其中任一项表现情形相吻合的，请在具体选项处进行选择“存在问题”或者“不存在问题”；如果选择“存在问题”请在审核栏说明具体情形。</li>
                                    </ol>
                                </div>
                            </div>
                            <div class="expand-btn-container" :class="expandEntityStatus?'':'expand-hidden'"
                                 @click="expandEntityEvents">
                                <div class="expand-icon"></div>
                                案卷目录
                            </div>
                        </div>
                    </div>

                    <%--查看异常信息--%>
                    <div id="error_dialog"
                         style="height: 450px; display: flex; flex-direction: column; justify-content: space-between;">
                        <input type="hidden" id="entity_error_id">
                        <input type="hidden" id="isAOrB_entity">
                        <span class="glyphicon glyphicon-info-sign" aria-hidden="true"
                              style="text-align: center;font-size: 50px;color: #337ab7;"></span>
                        <div style="width: 85%;padding-left: 20%;; font-size: 20px">
                            <span style="font-weight: bold; font-size: 20px">该项对方专家</span>
                            <span style="font-size: 20px;color: #337ab7" id="itemOption_entity">xxx</span>
                            <span style="font-weight: bold; font-size: 20px">，评审依据为：</span>
                            <span style="font-size: 20px;color: #337ab7" id="itemComme_entity">xxx</span>

                        </div>

                        <div style="padding-left: 20%; font-size: 20px">
                            <label style="font-weight: bold; font-size: 20px">是否认同:</label>
                            <input class="form-check-input" type="radio" name="isAgree_entity" id="isAgree_entity_0"
                                   value="0"><span style="margin-right: 48px;color:#333333;font-size:18px;">不认同</span>
                            <input class="form-check-input" type="radio" name="isAgree_entity" id="isAgree_entity_1"
                                   value="1"><span style="color:#333333;font-size:18px;"> 认同</span>
                        </div>
                        <div style="padding-left: 20%;">
                            <textarea style="width: 80%;" id="caseInfo" placeholder="请输入具体情形"></textarea>
                            <span id="error-message-500" style="color: red;"></span>

                        </div>

                        <div style="text-align: center; padding: 20px;">
                            <button id="entity_error_close" class="my-btn-gray" OnClick="entity_error_close()"
                                    style="background: #ECF5FF; margin-right: 50px;margin-top: 10px; outline: none; border: 1px solid #C6E2FF;border-radius: 3px;font-size: 18px;color:#409EFF; width: 80px; height: 40px">
                                取消
                            </button>
                            <button id="error_submit" class="my-btn-blue" OnClick="error_submit()"
                                    style="background: #009CCD; margin-top: 10px;outline: none; border: none; width: 80px; height: 40px;border: 1px solid #C6E2FF;border-radius: 3px;font-size: 18px;color:#ffffff;">
                                确认
                            </button>
                        </div>
                    </div>
                    <%------------------------实体弹框开始------------------------------------------------%>
                    <div id="my_dialog">

                        <p style="text-align: left;margin-left: 25px;margin-right:30px">
                            <span id="span1"></span>
                        </p>

                        <p style="text-align: left;margin-left: 35px; margin-top: 28px">
                            <input disabled type="radio" id="score1" class="disNo" value="1" name="score"
                                   onclick="updataRadio(1)"/><span
                                style="margin-right: 48px;color:#333333;font-size:18px;"> 存在问题</span>
                            <input disabled type="radio" id="score0" class="disNo" value="0" name="score"
                                   onclick="updataRadio(0)"/><span style="color:#333333;font-size:18px;"> 不存在问题</span>
                        </p>

                        <p style="text-align:center;">
                            <%--<textarea  id="textare" name="commeStr" style="resize:none; width: 700px; height: 150px" placeholder="请输入具体情形，长度不能超过2000字符" ></textarea>--%>
                        <div style="height: 270px;width: 750px;border:1px solid #0006;margin-left: 25px;margin-top:10px"
                             class="form-group">
                            <textarea maxlength="2000" class="disNo" id="textare" name="commeStr"
                                      style=" width: 750px; height: 270px;display: none;"
                                      placeholder="请输入具体情形，长度不能超过2000字符"></textarea>
                            <div id="commeStrDiv"
                                 style="height: 270px;line-height:37px;width: 750px;margin-left: 15px;display: none">
                                <span style="font-size: 16px;color: #ffffff; display: inline-block;  visibility: hidden;letter-spacing: 1.5px;">空格</span>
                                <span style="font-size: 16px;color:#333333;letter-spacing: 1.5px;">该案卷在</span>
                                <input autocomplete="off" maxlength="200" type="text" id="commeStrOne"
                                       class="from_gaozhi disNo"
                                       style="width:240px;outline:none;margin-right: 0px;font-size: 16px;color: #333333;"
                                       placeholder="仅限200字符之内"><span
                                    style="font-size: 16px;color: #333333;letter-spacing: 1.5px;">处存在</span>
                                <input autocomplete="off" maxlength="200" type="text" id="commeStrTwo"
                                       class="from_gaozhi disNo"
                                       style="width:240px;outline:none;margin-right: 0px;font-size: 16px;color: #333333;"
                                       placeholder="仅限200字符之内"><span
                                    style="font-size: 16px;color: #333333;letter-spacing: 1.5px;">问题,详见</span>
                                <input type="text" maxlength="200" id="commeStrThree" class='from_gaozhi disNo'
                                       onclick="myclick();" readonly style="width:200px;height:30px;">

                                <div id="fuzzysearchdiv"
                                     style="display:none;width:200px;z-index:3;position:absolute;height:20px;"
                                     onMouseOver="mousein()" onMouseOut="mouseout()">
                                </div>
                                <div id="selectdiv"
                                     style="display:none;top:230px;border:1px solid #A9A9A9;width:200px;z-index:2;position:absolute;overflow-y :scroll;margin-left:50px;height:270px;background-color:white;"
                                     onMouseOver="mousein()" onMouseOut="mouseout()">
                                </div>

                                <span style="font-size: 16px;color: #333333;letter-spacing: 1.5px;">文书,</span>
                                <input autocomplete="off" maxlength="200" type="text" id="commeStrFive"
                                       class="from_gaozhi disNo"
                                       style="width:100px;outline:none;margin-right: 0px;font-size: 16px;color: #333333;"
                                       placeholder="分别第XX页"><span
                                    style="font-size: 16px;color: #333333;letter-spacing: 1.5px;">页,</span>
                                <input autocomplete="off" maxlength="200" type="text" id="commeStrSix"
                                       class="from_gaozhi disNo"
                                       style="width:280px;outline:none;margin-right: 0px;font-size: 16px;color: #333333;"
                                       placeholder="仅限200字符之内"><span
                                    style="font-size: 16px;color: #333333;letter-spacing: 1.5px;">错误,</span>
                                <br/>
                                <span style="font-size: 16px;color: #333333;letter-spacing: 1.5px;">因此认定该案卷错误。</span>
                                <br/>
                                <span style="font-size: 16px;color:#333333;letter-spacing: 1.5px;">详情说明：</span>
                                <textarea autocomplete="off" maxlength="2000" class="disNo" id="commeStrEight"
                                          placeholder="需对存在的问题进行详细说明，仅限2000字"
                                          style=" width: 720px; height: 80px;outline: none;font-size:16px;color: #333333;"></textarea>
                            </div>
                        </div>
                        </p>

                        <div style="text-align: center">
                            <button id="topBnt" class="my-btn-gray" OnClick="CancelEntity()"
                                    style="background: #ECF5FF; margin-right: 50px;margin-top: 10px; outline: none; border: 1px solid #C6E2FF;border-radius: 3px;font-size: 18px;color:#409EFF; width: 100px; height: 40px">
                                上一项
                            </button>
                            <button class="my-btn-blue" OnClick="confirm()"
                                    style="background: #009CCD; margin-top: 10px;outline: none; border: none; width: 100px; height: 40px;border: 1px solid #C6E2FF;border-radius: 3px;font-size: 18px;color:#ffffff;">
                                下一项
                            </button>
                        </div>
                    </div>
                    <%--	------------------------实体弹框结束-----------------------------------------------%>

                    <%--				<button  id="aistartEntity" v-on:click="startEntitytion()" class="btn btn-primary"  style="font-size:14px;margin-top:10px;width:98px;height:40px;  padding:6px 6px;margin-right: 10px;float: right;">开始智能评查</button>--%>
                    <div id="jdmodal" class="jdmodal" style="display: none;">
                        <div class="jdmodal-content">

                            <span class="close" id="closeButton" v-on:click="closejdModal()">&times;</span>
                            <h2 id="modalTitle">识别进度</h2>
                            <div class="progress">
                                <div id="progressBar" class="progress-bar"></div>
                            </div>
                            <div id="jdstatus">进度：0%</div>

                            <div id="completionMessage" style="display: none;"> <!-- 完成消息区域 -->
                                <i class="fas fa-check-circle" style="font-size: 50px; color: green;"></i> <!-- 完成图标 -->
                                <h3>已完成识别</h3>
                                <p>请注意智能识别内容，仅供参考，具体需以实际核对结果为准。</p>
                            </div>
                            <button id="confirmButton" style="display: none;" v-on:click="confirmEntityRecognition()"
                                    onclick="confirmEntityRecognition()">确认
                            </button>
                        </div>
                    </div>

                    <div class="table-container">
                        <div class="table-box">
                            <form action="#" id="zjpfEntityForm" method="post">
                                <table class="table-head" :style="{ left: scrollLeftEntity }">
                                    <colgroup>
                                        <col style="min-width: 60px;width:4.3%">
                                        <col style="min-width: 100px;width:5.7%">
                                        <col style="min-width: 1192px;width:90%">
                                    </colgroup>
                                    <thead>
                                    <tr>
                                        <td>序号</td>
                                        <td>评查项目</td>
                                        <td>
                                            <table width="100%" border="0" cellspacing="0" cellpadding="0"
                                                   style="padding:0;">
                                                <colgroup>
                                                    <col style="width: 300px;">
                                                    <col style="width: 100px;">
                                                    <col style="width: 150px;">
                                                    <col style="min-width: 512px;">
                                                </colgroup>
                                                <tr>
                                                    <td style="border-right:solid 1px #ddd;text-align: center">
                                                        重大问题案卷表现形式
                                                    </td>
<%--                                                    <td style="border-right:solid 1px #ddd;text-align: center;padding:0px 5px 0px 5px;">--%>
<%--                                                        智能评查识别内容--%>
<%--                                                    </td>--%>
                                                    <td style="border-right:solid 1px #ddd; text-align: center;padding:0px 5px 0px 5px;">
                                                        评查
                                                    </td>
                                                    <td style="text-align: center;padding:0px 5px 0px 5px;">具体情形</td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    </thead>
                                </table>
                                <div class="table-body-box" @scroll="tableScrollEntity($event)">
                                    <table class="table-body table table-bordered table-condensed">
                                        <colgroup>
                                            <col style="min-width: 60px;width:4.3%">
                                            <col style="min-width: 100px;width:5.7%">
                                            <col style="min-width: 1192px;width:90%">
                                        </colgroup>
                                        <tbody class="form-group">
                                        <tr v-for="(scoringIndex, index) in scoringEntityList.expertHandlIndexScoreList"
                                            v-if="scoringIndex.isError!=2" :id="scoringIndex.id"
                                            :class="hoverId==scoringIndex.id?'tr-hover':''"
                                            @mouseover="hoverId=scoringIndex.id;" @mouseleave="hoverId=''"
                                            @click="tableToLinkEntity(scoringIndex.id)">
                                            <td height="30" align="center">{{index+1}}</td>

                                            <!-- 评查项目 -->
                                            <td align="center" style="padding: 6px 20px;">{{scoringIndex.indexname}}
                                            </td>

                                            <!-- 评分细则 -->
                                            <td disabled
                                                v-if="scoringIndex.expertHandlItemScoreList != null && scoringIndex.expertHandlItemScoreList.length !=0 "
                                                style="padding:0;">
                                                <table width="100%" border="0" cellspacing="0" cellpadding="0"
                                                       style="padding:0;">
                                                    <colgroup>
                                                        <col style="width: 300px;">
                                                        <col style="width: 100px;">
                                                        <col style="width: 150px;">
                                                        <col style="min-width: 512px;">
                                                    </colgroup>
                                                    <tr v-for="(scoringItem, index1) in scoringIndex.expertHandlItemScoreList">
                                                        <td style="background-color:#ECD9E0;border-bottom:solid 1px #ddd; border-right:solid 1px #ddd;vertical-align:middle;padding: 6px 20px;"
                                                            v-if="scoringItem.isError==1">
                                                            <div style="vertical-align:middle;text-align: left;">
                                                                {{scoringItem.itemname}}
                                                                <p v-if="scoringItem.isError==1 && scoringItem.isAgree==null"
                                                                   style="text-align:center;width: 67px;line-height:24px;height: 24px;background: #DF1912;border-radius: 3px;">
                                                                    <a href="#" style="font-size:14px;color: #ffffff"
                                                                       v-on:click="showErrorMsgEntity(scoringItem)">存在争议</a>
                                                                </p>
                                                                <p v-if="scoringItem.isError==1 && scoringItem.isAgree!=null"
                                                                   style="text-align:center;width: 67px;line-height:24px;height: 24px;background: rgba(129,194,105,1);border-radius: 3px;">
                                                                    <a href="#" style="font-size:14px;color: #ffffff"
                                                                       v-on:click="showErrorMsgEntity(scoringItem)">查看争议</a>
                                                                </p>
                                                            </div>
                                                        </td>
                                                        <td style="border-bottom:solid 1px #ddd; border-right:solid 1px #ddd;vertical-align:middle;padding: 6px 20px"
                                                            v-if="scoringItem.isError !=1">
                                                            <div style="vertical-align:middle;text-align: left;">
                                                                {{scoringItem.itemname}}
                                                                <p v-if="scoringItem.isError==1"
                                                                   style="text-align:center;width: 67px;line-height:24px;height: 24px;background: #DF1912;border-radius: 3px;">
                                                                    <a href="#" style="font-size:14px;color: #ffffff"
                                                                       v-on:click="showErrorMsgEntity(scoringItem)">存在争议</a>
                                                                </p>
                                                            </div>
                                                        </td>
                                                        <%--实体智能评查内容--%>
<%--                                                        <td style="border-bottom:solid 1px #ddd; border-right:solid 1px #ddd;vertical-align:middle;padding: 6px 20px;">--%>
<%--                                                            <div style="vertical-align:middle;"--%>
<%--                                                                 :id="'aiInfoEntity'+index+index1">--%>
<%--                                                                &lt;%&ndash;												{{scoringItem.aiInfo}}&ndash;%&gt;--%>

<%--                                                            </div>--%>
<%--                                                        </td>--%>

                                                        <td align="center"
                                                            style="border-bottom:solid 1px #ddd; border-right:solid 1px #ddd; vertical-align:middle;padding: 6px 20px;">
                                                            <div :id="index+'expertItem'+index1" style="float:left;">
                                                                <div v-if="scoringItem.score !=null "
                                                                     style="float:right;text-align: left">
                                                                    <input type="radio" value="1"
                                                                           :name="index+'scoreRadio'+index1"
                                                                           v-model="scoringItem.score"
                                                                           @click="updateItemEntity(index,index1,scoringItem.itemscore,scoringItem.temItemId)">存在问题
                                                                    <input type="radio" value="0" style="margin-left: 10px;"
                                                                           :name="index+'scoreRadio'+index1"
                                                                           v-model="scoringItem.score"
                                                                           @click="updateItemEntity(index,index1,scoringItem.itemscore,scoringItem.temItemId)">不存在问题
                                                                </div>
                                                                <div v-else style="float:right;text-align: left">

                                                                    <input class="radoos" type="radio" value="1"
                                                                           :name="index+'scoreRadio'+index1"
                                                                           v-model="scoringItem.score"
                                                                           @click="updateItemEntity(index,index1,scoringItem.itemscore,scoringItem.temItemId)">存在问题
                                                                    <input class="radoos" type="radio" value="0" style="margin-left: 10px;"
                                                                           :name="index+'scoreRadio'+index1"
                                                                           v-model="scoringItem.score"
                                                                           @click="updateItemEntity(index,index1,scoringItem.itemscore,scoringItem.temItemId)">不存在问题
                                                                </div>
                                                                <div style="color:red; font-size: 12px;">
                                                                    {{scoringItem.validatorMessage}}
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td align="center"
                                                            style="border-bottom:solid 1px #ddd;vertical-align:middle;padding: 6px 20px;">
                                                            <div style="font-size: 14px">
																	<textarea v-if="scoringItem.score != 1"
                                                                              maxlength="2000"
                                                                              :id="index+'comment'+index1"
                                                                              name="commeInput"
                                                                              v-model="scoringItem.commeStr"
                                                                              @input="updateCommonEntity(index,index1)"
                                                                              rows="4" class="form-control"
                                                                              style="width: 100%;height: 100px;"
                                                                              placeholder="请输入具体情形，长度不能超过2000字符"
                                                                              :title="scoringItem.commeStr">
																	</textarea>
                                                                <div v-show="scoringItem.score == 1"
                                                                     :id="index+'commeStrDiv'+index1"
                                                                     style="line-height: 20px">
                                                                    <template v-if="scoringItem.score>=0">
                                                                        <%--<div class="score-nopass-container" style="position: relative;text-align: left;background-color:#ffffff;">
                                                                            <div style="display: flex;line-height: 24px">
                                                                                <span style="color: #ffffff; display: inline-block; visibility: hidden;letter-spacing: 1.5px;">空格</span>
                                                                                <span style="color:#333333;letter-spacing: 1.5px;">该案卷在</span>
                                                                                <input autocomplete="off" maxlength="200" type="text" class="from_gaozhi disNo" v-model="scoringItem.commeStrOne" style="flex:1;outline:none;margin-right: 0px;color: #333333;" placeholder="仅限200字符之内">
                                                                                <span style="color: #333333;letter-spacing: 1.5px;">处存在</span>
                                                                </div>
                                                                            <div style="display: flex;line-height: 24px">
                                                                                <input autocomplete="off" maxlength="200" type="text" class="from_gaozhi disNo" v-model="scoringItem.commeStrTwo" style="flex:1;outline:none;margin-right: 0px;color: #333333;" placeholder="仅限200字符之内">
                                                                                <span style="color: #333333;letter-spacing: 1.5px;">问题,详见</span>
                                                                            </div>
                                                                            <div style="display: flex;line-height: 24px">
                                                                                <input type="text" maxlength="200" class='from_gaozhi disNo commeStrThree' v-model="scoringItem.commeStrThree" onclick="myclick(this);" readonly style="flex:1;">

                                                                                <div class="doc-item-list" style="display:none;top: 72px;left: 0px;border:1px solid #A9A9A9;width:200px;z-index:2;position:absolute;overflow-y :scroll;height:270px;background-color:white;"
                                                                                     onMouseOver="mousein(this)" onMouseOut="mouseout(this)">
                                                                                    <div v-for="indexOneItem in scoringEntityList.indexOneList">
                                                                                        <input name="mycheckbox" class="ismycheck" type="checkbox" onclick="mycheck(this)" value="" :data="indexOneItem">{{indexOneItem}}
                                                                                    </div>
                                                                                </div>
                                                                                <span style="color: #333333;letter-spacing: 1.5px;">文书,</span>
                                                                                <input autocomplete="off" maxlength="200" type="text" class="from_gaozhi disNo" v-model="scoringItem.commeStrFive" style="width:30%;outline:none;margin-right: 0px;color: #333333;" placeholder="分别第XX页">
                                                                                <span style="color: #333333;letter-spacing: 1.5px;">页,</span>
                                                                            </div>
                                                                            <div style="display: flex;line-height: 24px">
                                                                                <input autocomplete="off" maxlength="200" type="text" class="from_gaozhi disNo" v-model="scoringItem.commeStrSix" style="flex:1;outline:none;margin-right: 0px;color: #333333;" placeholder="仅限200字符之内">
                                                                                <span style="color: #333333;letter-spacing: 1.5px;">错误,</span>
                                                                            </div>
                                                                            <div style="display: flex;line-height: 24px"><span style="color: #333333;letter-spacing: 1.5px;">因此认定该案卷错误。</span></div>

                                                                            <div style="display: flex;line-height: 24px"><span style="color:#333333;letter-spacing: 1.5px;">详情说明：</span></div>
                                                                            <textarea autocomplete="off" maxlength="2000" class="form-control disNo" v-model="scoringItem.commeStrEight" placeholder="需对存在的问题进行详细说明，仅限2000字" style="width:100%;height: 80px;"></textarea>
                                                                        </div>--%>
                                                                        <textarea maxlength="2000"
                                                                                  :id="index+'comment'+index1"
                                                                                  name="commeInput"
                                                                                  v-model="scoringItem.commeStr"
                                                                                  readonly
                                                                                  @click="updateItemEntity(index,index1,scoringItem.itemscore,scoringItem.temItemId)"
                                                                                  @input="updateCommonEntity(index,index1)"
                                                                                  rows="4" class="form-control"
                                                                                  style="width:100%;height: 100%;"
                                                                                  placeholder="请输入具体情形，长度不能超过2000字符"
                                                                                  :title="scoringItem.commeStr">
																			</textarea>
                                                                    </template>
                                                                </div>
                                                            </div>
                                                            <div style="color:red; font-size: 12px;">
                                                                {{scoringItem.validatorComment}}
                                                            </div>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
                                        <tr>
                                        <tr v-for="(scoringIndex, index) in scoringEntityList.expertHandlIndexScoreList"
                                            v-if="scoringIndex.isError==2" style="background-color: #ECD9E0;">
                                            <td height="30" align="center">{{index+1}}</td>

                                            <!-- 评查项目 -->
                                            <td align="center">{{scoringIndex.indexname}}
                                            </td>

                                            <!-- 评分细则 -->
                                            <td disabled
                                                v-if="scoringIndex.expertHandlItemScoreList != null && scoringIndex.expertHandlItemScoreList.length !=0 "
                                                style="padding:0;">
                                                <table width="100%" border="0" cellspacing="0" cellpadding="0"
                                                       style="padding:0;">
                                                    <colgroup>
                                                        <col style="width: 300px;">
                                                        <col style="width: 100px;">
                                                        <col style="width: 150px;">
                                                        <col style="min-width: 512px;">
                                                    </colgroup>
                                                    <tr v-for="(scoringItem, index1) in scoringIndex.expertHandlItemScoreList">
                                                        <td style="background-color:#ECD9E0;border-bottom:solid 1px #ddd; border-right:solid 1px #ddd;vertical-align:middle;"
                                                            v-if="scoringItem.isError==1">
                                                            <div style="vertical-align:middle; margin:0 5px;">
                                                                {{scoringItem.itemname}}
                                                                <p v-if="scoringItem.isError==1"
                                                                   style="text-align:center;width: 67px;line-height:24px;height: 24px;background: #DF1912;border-radius: 3px;">
                                                                    <a href="#" style="font-size:14px;color: #ffffff"
                                                                       v-on:click="showErrorMsgEntity(scoringItem)">存在争议</a>
                                                                </p>
                                                            </div>
                                                        </td>
                                                        <td style="border-bottom:solid 1px #ddd; border-right:solid 1px #ddd;vertical-align:middle;"
                                                            v-if="scoringItem.isError !=1">
                                                            <div style="vertical-align:middle; margin:0 5px;">
                                                                {{scoringItem.itemname}}
                                                                <p v-if="scoringItem.isError==1"
                                                                   style="text-align:center;width: 67px;line-height:24px;height: 24px;background: #DF1912;border-radius: 3px;">
                                                                    <a href="#" style="font-size:14px;color: #ffffff"
                                                                       v-on:click="showErrorMsgEntity(scoringItem)">存在争议</a>
                                                                </p>
                                                            </div>
                                                        </td>
                                                        <td style="border-bottom:solid 1px #ddd; border-right:solid 1px #ddd; vertical-align:middle;">
                                                            <div :id="index+'expertItem'+index1" style="float:left;">
                                                                <div v-if="scoringItem.score !=null "
                                                                     style="float:right; padding:0 5px 0 10px;">
                                                                    <input type="radio" value="1"
                                                                           :name="index+'scoreRadio'+index1"
                                                                           v-model="scoringItem.score"
                                                                           @click="updateItemEntity(index,index1,scoringItem.itemscore,scoringItem.temItemId)">是
                                                                    <input type="radio" value="0"
                                                                           :name="index+'scoreRadio'+index1"
                                                                           v-model="scoringItem.score"
                                                                           @click="updateItemEntity(index,index1,scoringItem.itemscore,scoringItem.temItemId)">否
                                                                </div>
                                                                <div v-else style="float:right; padding:0 5px 0 10px;">

                                                                    <input class="radoos" type="radio" value="1"
                                                                           :name="index+'scoreRadio'+index1"
                                                                           v-model="scoringItem.score"
                                                                           @click="updateItemEntity(index,index1,scoringItem.itemscore,scoringItem.temItemId)"
                                                                           readonly>是
                                                                    <input class="radoos" type="radio" value="0"
                                                                           :name="index+'scoreRadio'+index1"
                                                                           v-model="scoringItem.score"
                                                                           @click="updateItemEntity(index,index1,scoringItem.itemscore,scoringItem.temItemId)"
                                                                           readonly>否
                                                                </div>
                                                                <div style="color:red; font-size: 12px;">
                                                                    {{scoringItem.validatorMessage}}
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td style="border-bottom:solid 1px #ddd;vertical-align:middle;padding:0px 5px 0px 5px;">
													<textarea v-if="scoringItem.score>=0" maxlength="2000"
                                                              :id="index+'comment'+index1" name="commeInput"
                                                              v-model="scoringItem.commeStr"
                                                              @input="updateCommonEntity(index,index1)" rows="4"
                                                              class="form-control" style="width:100%;"
                                                              placeholder="请输入具体情形，长度不能超过2000字符"
                                                              :title="scoringItem.commeStr">
													</textarea>
                                                            <textarea v-else maxlength="2000"
                                                                      :id="index+'comment'+index1" name="commeInput"
                                                                      v-model="scoringItem.commeStr"
                                                                      @input="updateCommonEntity(index,index1)" rows="4"
                                                                      class="form-control" style="width:100%;"
                                                                      placeholder="请输入具体情形，长度不能超过2000字符"
                                                                      :title="scoringItem.commeStr" disabled>
													</textarea>
                                                            <div style="color:red; font-size: 12px;">
                                                                {{scoringItem.validatorComment}}
                                                            </div>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </form>
                        </div>
                        <%--列表锚点--%>
                        <div class="table-link" :class="expandEntityStatus?'':'table-link-hidden'">
                            <div v-for="(scoringIndex, index) in scoringEntityList.expertHandlIndexScoreList"
                                 v-if="scoringIndex.isError!=2" class="link-container"
                                 :class="scoringIndex.id==activeIdEntity?'active':''" :id="scoringIndex.id+'link'">
                                <div class="link-line"></div>
                                <a @click.prevent="tableLinkEntity(scoringIndex.id)">
                                    {{scoringIndex.indexname}}
                                </a>
                            </div>
                        </div>
                        <%--具体情形-存在问题--%>
                        <div class="table-nopass-container" id="nopassDraw">
                            <div class="table-nopass-cover"></div>
                            <div class="table-nopass-content">
                                <div class="score-nopass-container"
                                     style="position: relative;text-align: left;background-color:#ffffff;padding:16px">
                                    <div style="font-size: 20px;font-weight: 700;">具体情形</div>
                                    <hr style="margin: 8px 0; border: none; border-top: 1px solid #367bc0;" />
                                    <div style="padding-top: 20px;padding-left: 30px;padding-right: 30px;" class="card">
                                        评查指标：{{nopassDrawItem.itemname}}
                                    </div>
                                    <div class="card">
                                    <div style="padding-top: 20px;padding-left: 30px;padding-right: 30px;" >
                                        <div style="display: flex;line-height: 24px;margin-bottom:6px;">
                                            <span style="color: #ffffff; display: inline-block; visibility: hidden;letter-spacing: 1.5px;">空格</span>
                                            <span style="color:#333333;letter-spacing: 1.5px;">该案卷在</span>
                                            <input autocomplete="off" maxlength="200" type="text" class="from_gaozhi disNo"
                                                   v-model="nopassDrawItem.commeStrOne"
                                                   style="flex:1;outline:none;margin-right: 0px;color: #333333;"
                                                   placeholder="仅限200字符之内">
                                            <span style="color: #333333;letter-spacing: 1.5px;">处存在</span>
                    </div>
                                        <div style="display: flex;line-height: 24px;margin-bottom:6px;">
                                            <input autocomplete="off" maxlength="200" type="text" class="from_gaozhi disNo"
                                                   v-model="nopassDrawItem.commeStrTwo"
                                                   style="flex:1;outline:none;margin-right: 0px;color: #333333;"
                                                   placeholder="仅限200字符之内">
                                            <span style="color: #333333;letter-spacing: 1.5px;">问题,详见</span>
                        </div>
                                        <div style="display: flex;line-height: 24px;margin-bottom:6px;position: relative">
                                            <input type="text" maxlength="200" class='from_gaozhi disNo commeStrThree'
                                                   v-model="nopassDrawItem.commeStrThree" onclick="myclick(this);" readonly
                                                   style="flex:1;">

                                            <div class="doc-item-list"
                                                 style="display:none;top: 24px;left: 0px;border:1px solid #A9A9A9;width:200px;z-index:2;position:absolute;overflow-y :scroll;height:270px;background-color:white;"
                                                 onMouseOver="mousein(this)" onMouseOut="mouseout(this)">
                                                <div v-for="indexOneItem in scoringEntityList.indexOneList">
                                                    <input name="mycheckbox" class="ismycheck" type="checkbox"
                                                           @click="mycheckEntity(indexOneItem)" value=""
                                                           :data="indexOneItem"
                                                           :checked="(nopassDrawItem?.commeStrThree ?? '').split(',').indexOf(indexOneItem) !== -1">{{indexOneItem}}
                        </div>
                            </div>
                                            <span style="color: #333333;letter-spacing: 1.5px;">文书,</span>
                                            <input autocomplete="off" maxlength="200" type="text" class="from_gaozhi disNo"
                                                   v-model="nopassDrawItem.commeStrFive"
                                                   style="width:30%;outline:none;margin-right: 0px;color: #333333;"
                                                   placeholder="分别第XX页">
                                            <span style="color: #333333;letter-spacing: 1.5px;">页,</span>
                        </div>
                                        <div style="display: flex;line-height: 24px;margin-bottom:6px;">
                                            <input autocomplete="off" maxlength="200" type="text" class="from_gaozhi disNo"
                                                   v-model="nopassDrawItem.commeStrSix"
                                                   style="flex:1;outline:none;margin-right: 0px;color: #333333;"
                                                   placeholder="仅限200字符之内">
                                            <span style="color: #333333;letter-spacing: 1.5px;">错误,</span>
                                        </div>
                                        <div style="display: flex;line-height: 24px;margin-bottom:6px;"><span
                                                style="color: #333333;letter-spacing: 1.5px;">因此认定该案卷错误。</span></div>
                                    </div><%--中间--%>
                                    <div style="padding-top: 30px;padding-left: 30px;padding-right: 30px;" >
                                        <div style="display: flex;line-height: 24px;"><span
                                                style="color:#333333;letter-spacing: 1.5px;">详情说明：</span></div>
                                        <div style="display: flex;line-height: 24px;margin-bottom:6px;"><textarea
                                                autocomplete="off" maxlength="2000" class="form-control disNo"
                                                v-model="nopassDrawItem.commeStrEight" placeholder="需对存在的问题进行详细说明，仅限2000字"
                                                style="width:100%;height: 80px;"></textarea></div>
                                    </div><%--最后--%>
                                        <div style="padding: 40px"></div>
                                    </div>
                                    <div style="display: flex;justify-content: center;width: 100%;margin-top: 16px;gap:16px;">
                                        <div @click="closeNopassDraw"
                                             style="border: 1px solid #ddd;background-color: #fff;box-sizing: border-box;height: 36px;width: 90px;border-radius: 4px;display: flex;justify-content: center;align-items: center;cursor: pointer;">
                                            取消
                                        </div>
                                        <div @click="saveNopassDraw"
                                             style="border: 1px solid #367bc0;background-color: #367bc0;box-sizing: border-box;color: #ffffff;height: 36px;width: 90px;border-radius: 4px;display: flex;justify-content: center;align-items: center;cursor: pointer;">
                                            确定
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="score-footer" style="justify-content: flex-end;">
<%--                        <div class="score-warning-btn" onclick="openEntityDialo()"><img--%>
<%--                                src="${webpath}/static/images/zjpf_warning.svg"/>疑难（争议）问题描述及案卷推荐--%>
<%--                        </div>--%>
                        <div class="score-submit" >
                            <%--							<div v-if="scoringEntityList.scoredstate=='3'||scoringEntityList.scoredstate=='4'" class="submitTemporaryBtn" id="submitTemporaryBtnEntity" v-on:click="saveSubmitEntity(scoringEntityList.id,0)">暂存</div>--%>
                            <div class="submitBtn" id="submitBtnEntity"
                                 v-on:click="saveSubmitEntity(scoringEntityList.id,1)">保存
                            </div>
                        </div>
                    </div>

                    <!--  附件查看 -->
                    <div class="modal fade" id="view" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
                         aria-hidden="true">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content"></div>
                        </div>
                    </div>
                </div>
                <%--加分项评分页面--%>
                <div v-show="tabCurrentIndex === '1' && reviewBtnCurrent=='pluses'" class="tab_content_show">
                    <div class="score_title">
                        <div class="reviewBtn_wrap" style="min-width: 130px;">
<%--                            <div class="reviewBtn_item" :class="{'active': reviewBtnCurrent === 'rollSurface'}">--%>
<%--                                <div style="padding: 6px 12px;" @click="handleCLickReviewBtn('rollSurface')">规范性评查</div>--%>
<%--                            </div>--%>
<%--                            <div class="reviewBtn_item" :class="{'active': reviewBtnCurrent === 'entity', 'reviewBtnDisable': ${(expertHandlFileList.scoredstate == 0 || expertHandlFileList.scoredstate == 2 || expertHandlFileList.fileMaterials == 1) ? 'true' : 'false'}}">--%>
                            <div class="reviewBtn_item" :class="{'active': reviewBtnCurrent === 'entity'}">
                                <div style="padding: 6px 12px;" @click="handleCLickReviewBtn('entity')">合法性评查</div>
                            </div>
                            <div class="reviewBtn_item" :class="{'active': reviewBtnCurrent === 'pluses', 'reviewBtnDisable': ${(expertHandlFileList.scoredstate == 0 || expertHandlFileList.scoredstate == 2 || expertHandlFileList.fileMaterials == 1) ? 'true' : 'false'}}">
                                <div style="padding: 6px 12px;" @click="handleCLickReviewBtn('pluses')">加分项</div>
                            </div>
                        </div>
                        <div class="result_container">
                            <span class="result_title">共选择：</span>
                            <span class="result_text">{{ plusesSelectedNum }}项</span>
                        </div>
<%--                        <div class="result_container">--%>
<%--                            <span class="result_title">加分分数：</span>--%>
<%--                            <span class="result_text">{{ plusesTotalScore }}分</span>--%>
<%--                        </div>--%>
<%--                        <div class="result_container">--%>
<%--                            <span class="result_title">案卷总分数：</span>--%>
<%--                            <span class="result_sum_text">{{ totalCaseScore }}</span>--%>
<%--                            <input disabled id="jsScore" type="text" v-model="scoringEntityList.entityscore"--%>
<%--                                   class="form-control" style="display: none">--%>
<%--                        </div>--%>


                        <div class="instructions_tip">
<%--                            <div id="infoEntityModal" class="custom-modal">--%>
<%--                                <div class="custom-modal-content">--%>
<%--                                    <span class="custom-close-button" onclick="closeInfoModal('entity')">&times;</span>--%>
<%--                                    <h2 class="custom-modal-title">使用说明：</h2>--%>
<%--                                    <ol class="custom-modal-list">--%>
<%--                                        <li>1.任意一小项选择“存在问题”，则扣全50分；每一小项都是必选项</li>--%>
<%--                                        <li>2.评审说明：其中任一项表现情形相吻合的，请在具体选项处进行选择“存在问题”或者“不存在问题”；如果选择“存在问题”请在审核栏说明具体情形。</li>--%>
<%--                                    </ol>--%>
<%--                                </div>--%>
<%--                            </div>--%>
<%--                            <div class="expand-btn-container" :class="expandEntityStatus?'':'expand-hidden'"--%>
<%--                                 @click="expandEntityEvents">--%>
<%--                                <div class="expand-icon"></div>--%>
<%--                                案卷目录--%>
<%--                            </div>--%>
                    </div>
                                </div>
                    <div class="table-container">
                        <div class="table-box">
                            <form action="#" id="zjpfPlusesForm" method="post">
                                <table class="table-head" :style="{ left: scrollLeftEntity }">
                                    <colgroup>
                                        <col style="min-width: 60px;width:4.3%">
                                        <col style="min-width: 100px;width:5.7%">
                                        <col style="min-width: 1192px;width:90%">
                                    </colgroup>
                                    <thead>
                                    <tr>
                                        <td>序号</td>
                                        <td>评查项目</td>
                                        <td>
                                            <table width="100%" border="0" cellspacing="0" cellpadding="0"
                                                   style="padding:0;">
                                                <colgroup>
                                                    <col style="width: 300px;">
                                                    <col style="width: 100px;">
                                                    <col style="width: 150px;">
                                                    <col style="min-width: 512px;">
                                                </colgroup>
                                                <tr>
                                                    <td style="border-right:solid 1px #ddd;text-align: center">
                                                        加分项评分标准
                                                    </td>
                                                    <td style="border-right:solid 1px #ddd; text-align: center;padding:0px 5px 0px 5px;">
                                                        符合情况
                                                    </td>
                                                    <td style="text-align: center;padding:0px 5px 0px 5px;">具体说明</td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    </thead>
                                </table>
                                <div class="table-body-box" @scroll="tableScrollEntity($event)">
                                    <table class="table-body table table-bordered table-condensed">
                                        <colgroup>
                                            <col style="min-width: 60px;width:4.3%">
                                            <col style="min-width: 100px;width:5.7%">
                                            <col style="min-width: 1192px;width:90%">
                                        </colgroup>
                                        <tbody class="form-group">
                                        <tr v-for="(scoringIndex, index) in scoringPlusesList.expertHandlIndexScoreList"
                                                                                     :id="scoringIndex.id"
                                                                                    :class="hoverId==scoringIndex.id?'tr-hover':''"
                                                                                    @mouseover="hoverId=scoringIndex.id;" @mouseleave="hoverId=''"
                                                                                                                @click="tableToLinkEntity(scoringIndex.id)">
                                            <td height="30" align="center">{{index+1}}</td>

                                            <!-- 评查项目 -->
                                            <td align="center" style="padding: 6px 20px;">{{scoringIndex.indexname}}
                                            </td>

                                            <!-- 评分细则 -->
                                            <td disabled
                                                v-if="scoringIndex.expertHandlItemScoreList != null && scoringIndex.expertHandlItemScoreList.length !=0 "
                                                style="padding:0;">
                                                <table width="100%" border="0" cellspacing="0" cellpadding="0"
                                                       style="padding:0;">
                                                    <colgroup>
                                                        <col style="width: 300px;">
                                                        <col style="width: 100px;">
                                                        <col style="width: 150px;">
                                                        <col style="min-width: 512px;">
                                                    </colgroup>
                                                    <tr v-for="(scoringItem, index1) in scoringIndex.expertHandlItemScoreList">
                                                        <td style="border-bottom:solid 1px #ddd; border-right:solid 1px #ddd;vertical-align:middle;padding: 6px 20px;">
                                                            <div style="vertical-align:middle;text-align: left;">
                                                                {{scoringItem.itemname}}
                                            </div>
                                                        </td>

                                                        <td align="center"
                                                            style="border-bottom:solid 1px #ddd; border-right:solid 1px #ddd; vertical-align:middle;padding: 6px 20px;">
                                                            <div :id="index+'expertItemPluses'+index1" style="display: flex; justify-content: center;">
                                                                <div v-if="scoringItem.score !=null " style="float:right;text-align: left">
                                                                    <input type="radio" value="1"
                                                                           :name="index+'scoreRadio'+index1"
                                                                           v-model="scoringItem.score"
                                                                           @change="handlePlusesRadioChange(index, index1)">符合
                                                                    <input type="radio" value="0" style="margin-left: 20px;"
                                                                           :name="index+'scoreRadio'+index1"
                                                                           v-model="scoringItem.score"
                                                                           @change="handlePlusesRadioChange(index, index1)">不符合
                                                            </div>
                                                                <div v-else style="float:right;text-align: left">
                                                                    <input class="radoos" type="radio" value="1"
                                                                           :name="index+'scoreRadio'+index1"
                                                                           v-model="scoringItem.score"
                                                                           @change="handlePlusesRadioChange(index, index1)">符合
                                                                    <input class="radoos" type="radio" value="0"  style="margin-left: 20px;"
                                                                           :name="index+'scoreRadio'+index1"
                                                                           v-model="scoringItem.score"
                                                                           @change="handlePlusesRadioChange(index, index1)">不符合
                                                        </div>
<%--                                                                <div style="color:red; font-size: 12px;">--%>
<%--                                                                    {{scoringItem.validatorMessage}}--%>
<%--                                                                </div>--%>
                                                                        </div>
                                                        </td>
                                                        <td align="center"
                                                            style="border-bottom:solid 1px #ddd;vertical-align:middle;padding: 6px 20px;">
                                                            <div style="font-size: 14px">
																	<textarea
                                                                              maxlength="2000"
                                                                              :id="index+'pointsComment'+index1"
                                                                              name="commeInput"
                                                                              v-model="scoringItem.commeStr"
                                                                              @input="updateCommonPluses(index,index1)"
                                                                              rows="4" class="form-control"
                                                                              style="width: 100%;height: 100px;"
                                                                              placeholder="请输入具体情形，长度不能超过2000字符"
                                                                              :title="scoringItem.commeStr">
																	</textarea>
                                                                <div style="color:red; font-size: 12px;">
                                                                    {{scoringItem.validatorMessage}}
                                                                    </div>
<%--                                                                <div v-show="scoringItem.score == 1"--%>
<%--                                                                     :id="index+'commeStrDiv'+index1"--%>
<%--                                                                     style="line-height: 20px">--%>
<%--                                                                    <template v-if="scoringItem.score>=0">--%>
<%--                                                                        <textarea maxlength="2000"--%>
<%--                                                                                  :id="index+'comment'+index1"--%>
<%--                                                                                  name="commeInput"--%>
<%--                                                                                  v-model="scoringItem.commeStr"--%>
<%--                                                                                  readonly--%>
<%--                                                                                  @click="updateItemEntity(index,index1,scoringItem.itemscore,scoringItem.temItemId)"--%>
<%--                                                                                  @input="updateCommonEntity(index,index1)"--%>
<%--                                                                                  rows="4" class="form-control"--%>
<%--                                                                                  style="width:100%;height: 100%;"--%>
<%--                                                                                  placeholder="请输入具体情形，长度不能超过2000字符"--%>
<%--                                                                                  :title="scoringItem.commeStr">--%>
<%--																			</textarea>--%>
<%--                                                                    </template>--%>
<%--                                                                </div>--%>
                                                                                    </div>
                                                            <div style="color:red; font-size: 12px;">
                                                                {{scoringItem.validatorComment}}
                                                                                </div>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                                                                                </div>
                            </form>
                                                                                            </div>
                        <%--列表锚点--%>
<%--                        <div class="table-link" :class="expandEntityStatus?'':'table-link-hidden'">--%>
<%--                            <div v-for="(scoringIndex, index) in scoringEntityList.expertHandlIndexScoreList"--%>
<%--                                 v-if="scoringIndex.isError!=2" class="link-container"--%>
<%--                                 :class="scoringIndex.id==activeIdEntity?'active':''" :id="scoringIndex.id+'link'">--%>
<%--                                <div class="link-line"></div>--%>
<%--                                <a @click.prevent="tableLinkEntity(scoringIndex.id)">--%>
<%--                                    {{scoringIndex.indexname}}--%>
<%--                                </a>--%>
<%--                            </div>--%>
<%--                        </div>--%>

                                                                                                            </div>
                    <div class="score-footer" style="justify-content: flex-end;">
                        <div class="score-warning-btn" onclick="openPlusesDialo()">
                            <img src="${webpath}/static/images/zjpf_warning.svg"/>是否推荐为优秀案卷
                                                                                                        </div>
                        <div class="score-submit" >
                            <%--							<div v-if="scoringEntityList.scoredstate=='3'||scoringEntityList.scoredstate=='4'" class="submitTemporaryBtn" id="submitTemporaryBtnEntity" v-on:click="saveSubmitEntity(scoringEntityList.id,0)">暂存</div>--%>
                            <div class="submitBtn" id="submitBtnPluses"
                                 v-on:click="saveSubmitPluses(scoringPlusesList.id, '1')">保存
                                                                                                                        </div>
                                                                                                                    </div>
                                                                                                                                    </div>
                                                                                                                                </div>
                <%--智能识别结果--%>
                <div v-if="tabCurrentIndex === '2'">
                    <div ref="markdownContent" id="markdown_content" class="mark_content"></div>
                                                                                                                                                </div>
                                                                                                                                            </div>
                                                                                                                                                            </div>
                                                                                                                                                        </div>
    <!-- 抽屉组件 -->
    <div class="drawer_mask" :class="{ show: drawerVisible }" @click="handleCloseDrawer"></div>
    <div class="drawer_wrap" :class="{ show: drawerVisible }">
        <div class="drawer_header">
            <div class="drawer_title">
                <span style="font-size: 18px;">案卷概览</span>
                <span style="font-size: 14px;color: #8d8d8d;margin-left: 20px">案卷文号：</span>
                <span class="pdf_file_name" :title="pdfFileName">{{ pdfFileName }}</span>
                                                                                                                                                                        </div>
            <div class="drawer_close" @click="handleCloseDrawer">
                <i class="fas fa-times" style="font-size: 16px"></i>
                                                                                                                                                                    </div>
                                                                                                                                                                                    </div>
        <div class="drawer_content">
            <!-- 标签-->
            <div class="drawer_tags_wrap">
                <span v-for="tag in recordsTags" :key="tag.id" :style="tagColorObject(tag)" class="drawer_tag_item">{{ tag.tagName }}</span>
                                                                                                                                                                                </div>
            <%--案卷描述--%>
            <div class="drawer_desc_wrap">
                <div v-for="(desc,index) in recordsDescList" :key="index" class="drawer_desc_item">
                    <span class="drawer_desc_label">{{ desc.label }}:</span>
                    <span class="drawer_desc_value">{{ desc.value }}</span>
                                                                                                                                                                                                </div>
                                                                                                                                                                                            </div>
            <%--时间轴--%>
            <div class="drawer_line_wrap">
                <%--				<ul>--%>
                <%--					<li v-for="(value,key) in timeAxisData" class="time_line_item">--%>
                <%--						<span>{{ key }}:</span>--%>
                <%--						<span>{{ value }}</span>--%>
                <%--					</li>--%>
                <%--				</ul>--%>
                <div v-for="(item, index) in recordsTimeLineList" :key="index" class="timeline-item">
                    <div class="timeline-header">
                        <div class="timeline-dot"></div>
                        <div class="timeline-title">
                            <span class="title">{{ item.text }}</span>
                            <span class="time">{{ item.titleTime }}</span>
                                                                                                                                                                                                            </div>
                                                                                                                                                                                                        </div>
                    <div class="timeline-content">
                        <div v-for="(detail, dIndex) in item.child" :key="dIndex" class="detail-item">
                            <span class="detail-time">{{ detail.text }}</span>
                            <span class="detail-desc">{{ detail.documentName }}</span>
                                                                                                                                                                                                                        </div>
                                                                                                                                                                                                                    </div>
                                                                                                                                                                                                                                    </div>
                                                                                                                                                                                                                                </div>
                                                                                                                                                                                                                                                </div>
                                                                                                                                                                                                                                            </div>
    <div id="AiModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <p id="fullText"></p>
                                                                                                                                                                                                                                                            </div>
                                                                                                                                                                                                                                                        </div>
    <div id="my_dialo" class="modal">
        <div class="custom-modal-content new-modal" style="max-width: 895px;">
            <div style="font-size: 20px;font-weight: 700;padding: 24px;border-bottom: 1px solid #ddd">案卷材料严重不全</div>
            <div style="padding: 40px">
                <span style="font-size: 16px;color: #ffffff; display: inline-block;  visibility: hidden;letter-spacing: 1.5px;">空格</span>
                <span style="font-size: 16px;color:#333333;letter-spacing: 1.5px;">  案卷共</span>
                <input class="noline-input" type="text" id="fileMaterialsNums" placeholder="请输入总页数"
                       style="font-size: 16px" value="${expertHandlFileList.fileMaterialsNums}">
                <span style="font-size: 16px;color:#333333;letter-spacing: 1.5px;">页，</span>
                <span style="font-size: 16px;color:#333333;letter-spacing: 1.5px;">只有</span>
                <input class="noline-input" maxlength="200" id="fileMaterialsDocs" placeholder="请填写案卷中存在的文书"
                       style="width: 400px;height: 25px;;font-size: 16px"></input>
                <span style="font-size: 16px;color:#333333;letter-spacing: 1.5px;">文书，导致无法全面评价案卷和实体程序问题。</span>
                <span style="display: block; margin-bottom: 20px"></span>
                <span style="font-size: 16px;color:#333333;letter-spacing: 1.5px;">其他情况补充：</span></br>
                <textarea id="fileMaterialsSups" placeholder="其他情况补充,仅限2000字" maxlength="2000"
                          style="width: 100%;height: 125px;font-size: 16px;margin-top: 5px;resize:none;border: 1px solid #ddd;border-radius: 4px"></textarea>
                <span style="font-size: 16px;color:#333333;letter-spacing: 1.5px;color: red">（提示：如确认该案卷为严重不全案卷，需填写以上信息，点击确定后该案卷直接显示已评状态且不可重新评查！）</span>
                <br>
                <div style="display: flex;margin-top: 16px;justify-content: center;gap: 16px;">
                    <div class="new-modal-cancel" OnClick="Cancel()">取消</div>
                    <div class="new-modal-submit" v-on:click="validateForm(${expertHandlFileList.id})">确定</div>
                                                                                                                                                                                                                                                                        </div>
                                                                                                                                                                                                                                                                    </div>
        </div>
    </div>
    <div id="entity_footer_dialog" class="modal">
        <div class="custom-modal-content new-modal" style="max-width: 895px;">
            <div style="font-size: 20px;font-weight: 700;padding: 24px;border-bottom: 1px solid #ddd">疑难（争议）问题描述及案卷推荐
            </div>
            <div style="padding: 40px">
                <div>
                    <div class="footer_title">疑难（争议）问题描述</div>
                    <div class="footer_content" style="margin-top: 12px;display: flex"><textarea maxlength="2000"
                                                                                                 v-model="scoringEntityList.problemRemark"
                                                                                                 rows="3"
                                                                                                 class="form-control"
                                                                                                 id="problem_remark"
                                                                                                 placeholder="请输入"
                                                                                                 style="resize: none"></textarea>
                    </div>
                </div>
                <div style="margin-top: 16px">
                    <div class="footer_title">案卷推荐</div>
                    <div class="footer_content">
                        <div>
                            <input id="yxdxanlituijian" name="yxdxanlituijian" type="checkbox"
                                   v-on:click="checkBoxClick('1')">&nbsp;优秀案卷推荐&nbsp; &nbsp;
                            <input id="ajpjYxdxanlituijian" name="ajpjYxdxanlituijian" type="checkbox"
                                   v-on:click="ajpjCheckBoxClick()">&nbsp;较差案卷推荐&nbsp; &nbsp;
                            <input id="noTuiJian" name="noTuiJian" type="checkbox" v-on:click="noCheckBoxClick()">&nbsp;不推荐为典型案例&nbsp;
                            &nbsp;
                        </div>
                        <div style="display: none;" id="yxdxAnLiTuiJianReviews1">
                            <c:forEach items="${tcDictionaryList}" var="item" varStatus="status">
									<span style="white-space: nowrap">
										<input type="checkbox" name="yxaj" value="${item.code}">&nbsp;${item.name} &nbsp; &nbsp;
									</span>
                            </c:forEach>
                            <textarea maxlength="2000" v-model="scoringEntityList.yxdxanlituijianreviews" id="zjpy"
                                      placeholder="请输入专家评语，长度不能超过2000个字符"
                                      style="width: 100%;height: 125px;margin-top: 8px;padding:6px 12px;resize:none;border: 1px solid #ccc;border-radius: 4px"></textarea>
                        </div>
                        <div style="display: none;" id="ajpjYxdxAnLiTuiJianReviews1">
                            <c:forEach items="${jcajDictionaryList}" var="item" varStatus="status">
									<span style="white-space: nowrap">
										<input type="checkbox" name="jcaj" value="${item.code}">&nbsp;${item.name}&nbsp; &nbsp;
									</span>
                            </c:forEach>
                            <textarea maxlength="2000" v-model="scoringEntityList.ajpjYxdxanlituijianreviews" id="ajpj"
                                      placeholder="请输入专家评语，长度不能超过2000个字符"
                                      style="width: 100%;height: 125px;margin-top: 8px;padding:6px 12px;resize:none;border: 1px solid #ccc;border-radius: 4px"></textarea>
                        </div>
                    </div>
                </div>
                <div style="display: flex;margin-top: 16px;justify-content: center;gap: 16px;">
                    <div class="new-modal-submit" onClick="confirmEntityDialo()">确定</div>
                </div>
            </div>
        </div>
    </div>
        <div id="pluses_footer_dialog" class="modal">
            <div class="custom-modal-content new-modal" style="max-width: 895px;">
                <div style="font-size: 20px;font-weight: 700;padding: 24px;border-bottom: 1px solid #ddd">是否推荐优秀案卷
                </div>
                <div style="padding: 40px">
                    <div style="margin-top: 16px">
                        <div class="footer_title">是否推荐优秀案卷 <span style="color: red;">*</span></div>
                        <div class="footer_content" style="margin-top: 12px;">
                            <div>
                                <input id="ajpjYxdxanlituijianCode_yes" name="ajpjYxdxanlituijianCode" type="radio" value="1"
                                       v-model="scoringPlusesList.ajpjYxdxanlituijianCode">&nbsp;是&nbsp; &nbsp;
                                <input id="ajpjYxdxanlituijianCode_no" name="ajpjYxdxanlituijianCode" type="radio" value="0"
                                       v-model="scoringPlusesList.ajpjYxdxanlituijianCode">&nbsp;否&nbsp; &nbsp;
                            </div>
                            <div id="ajpjYxdxanlituijianCode_error" style="color: red; font-size: 12px; margin-top: 5px; display: none;">请选择是否推荐优秀案卷</div>
                        </div>
                    </div>
                    <div style="margin-top: 16px">
                        <div class="footer_title">理由</div>
                        <div class="footer_content" style="margin-top: 12px;display: flex">
                            <textarea maxlength="2000"
                                     v-model="scoringPlusesList.problemRemark"
                                     rows="3"
                                     class="form-control"
                                     id="problem_remark"
                                     placeholder="请输入理由，长度不能超过2000个字符"
                                     style="resize: none"></textarea>
                        </div>
                    </div>
                    <div style="display: flex;margin-top: 16px;justify-content: center;gap: 16px;">
                        <div class="new-modal-submit" onClick="confirmPlusesDialo()">确定</div>
                    </div>
                </div>
            </div>
        </div>
    <div id="suspected_issue_dialog" class="modal">
        <div class="custom-modal-content new-modal" style="width: 720px;">
            <div style="display: flex;justify-content: space-between;font-size: 20px;font-weight: 700;padding: 24px;border-bottom: 1px solid #ddd">
                <span>案卷小助手</span>
                <i class="fas fa-times" style="cursor: pointer" @click="closeSuspectedIssueDialog"></i>
            </div>
            <div style="padding: 25px 30px;height: 400px;overflow-y:auto ">
                <div id="suspected_issue_title" style="display: flex;align-items: center">
                    <img src="${webpath}/static/image2024/ai.png" alt=""
                         style="align-self: flex-start;width: 26px;height: 26px;margin-right: 16px">
                    <span style="display: flex;align-items: center;min-height:36px;padding: 10px 15px;background-color: #f4f6f9;border-radius: 10px">疑似存在问题: {{ suspectedIssueTitle }}。</span>
                </div>
                <div style="display: flex;align-items: center;justify-content: flex-end;margin-top: 30px">
                    <span style="height: 36px;line-height: 36px;margin-right: 16px;padding: 0 15px;background-color: #f4f6f9;border-radius: 10px">整改建议。</span>
                    <img src="${webpath}/static/image2024/user.png" alt="">
                </div>
                <div style="display: flex;align-items: center;margin-top: 30px">
                    <img src="${webpath}/static/image2024/ai.png" alt=""
                         style="align-self: flex-start;width: 26px;height: 26px;margin-right: 16px">
                    <div id="issue_stream_content"
                         style="display: flex;align-items: center;min-height:36px;padding: 10px 15px;background-color: #f4f6f9;border-radius: 10px"></div>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>

<script>
    function toggleLeftContent() {
        const leftContent = document.querySelector('.score_left_content');
        const toggleBtnWrap = document.querySelector('.toggle_btn');
        const toggleBtn = document.querySelector('.toggle_btn i');
        leftContent.classList.toggle('collapsed');
        toggleBtnWrap.classList.toggle('leftBtn')
        toggleBtnWrap.classList.toggle('rightBtn')
        toggleBtn.classList.toggle('fa-chevron-left');
        toggleBtn.classList.toggle('fa-chevron-right');

    }
</script>


