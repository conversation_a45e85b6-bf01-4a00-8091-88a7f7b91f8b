<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
</head>
<script type="text/javascript">
		/* 	判断评分的状态scoredState为1 */
		 $(document).ready(function(){
			 business.listenEnter();
			 var scoredState= eval('${expertHandlFileList.scoredstate}');
			 var status = eval('${status}');
			 if(status =='1'){
				 $("#chickAnjuan").hide();
				 $("#submitBaoChong").hide();
				 $("#fileCodeInput").hide();
				 $("#queren").hide();
				 var expertreviews =  "${expertHandlFileList.expertreviews}"
					 expertreviews=	 expertreviews.replaceAll('</br>','\n');
					$("#expertreviews").html(expertreviews);
			 }
			 if(scoredState == '1'){
				 if(status !='1'){
					$("input").removeAttr("readonly");
					$("#submitBaoChong").removeAttr("style display");
					$("#chickAnjuan").hide();
					$("#fileCodeInput").attr("disabled","disabled");
					$("#fileCodeInput").val('${ expertHandlFileList.filecode}');
					 var expertreviews =  "${expertHandlFileList.expertreviews}"
						 expertreviews=	 expertreviews.replaceAll('</br>','\n');
						$("#expertreviews").html(expertreviews);
			 }else{
				 $("#fileCodeInput").val("");
			 }
				 }
			});
		/* 	评分状态的scoredState为0 */
			function chickAnjuan(){
				var fileCodeInput = trim($("#fileCodeInput").val());
				fileCodeInput = fileCodeInput.replace(/\s+/g,"");
				var fileCode = trim($("#fileCode").text());
				fileCode = fileCode.replace(/\s+/g,"");
				if( fileCodeInput == fileCode){
				swal("开始打分", "开始打分!", "success");
				//$("input").removeAttr("readonly");
				$("input").removeAttr("readonly");
				$("#fileCodeInput").attr("disabled","disabled");
				$("#submitBaoChong").removeAttr("style display");
				$("#chickAnjuan").hide();
				
				}else{
					swal("开始打分", "请输入正确的案卷号！", "error");
				}
			}
			function trim(str) {
				  return str.replace(/(^\s+)|(\s+$)/g, "");
				}
			
			//下载文件
			function xiaZaiAnJuan(){
			 var fileId = $("#fileId").val();
			  $.ajax({
				    type:"post",
				    url:WEBPATH+'/zjpf/xzcfExistFileUrl.do',
				    data:{fileId:fileId},           //注意数据用{}
				    success:function(data){  //成功
					 if("yes" == data){
							window.location.href="${pageContext.request.contextPath}/zjpf/zjpfAnJuandown.do?fileId="+fileId;
						    return false;
				         }else if("no" == data){
				            	  swal( "操作失败","该案卷不存在!", "error");
				            	  return false;
						}else if("suffixerror" ==data){
							  swal( "操作失败","该案卷上传数据格式有问题!", "error");
			            	  return false;
						}
				         }
				});
			}

	 $(document).ready(function(){
		 var pageNum = $("#pageNum").val();
			//保存数据方法
			$('#submitBaoChong').click(function() {
		       	 var options = {
		           url: WEBPATH+'/savezjpf.do',
		           type: 'post',
		           success:function(data){
			           if(data.result=="error"){
			        	   if(data.data="1"){
				        	   swal("操作失败", "您输入数据格式不准确!", "error");
				               return false; 
				        	   }else{
				        		   swal("操作失败", "信息保存操作失败了!", "error");
					               return false; 
				        	   }
			           }else if(data.result=="success"){
			        	  swal({title: "保存成功",text: "",type:"success"});
			        	business.addMainContentParserHtml('xzcfList.do','pageNum='+pageNum);
			        	return false;
			           }
		         	}
		       	 };
		       	$("#zjpfForm").data('formValidation').validate();
		       	var validate = $("#zjpfForm").data('formValidation').isValid();
		       	if(validate){
		       	 	$('#zjpfForm').ajaxSubmit(options);
		       	}
		   	});
			$("#zjpfForm").formValidation({
				   framework: 'bootstrap',
			        message: 'This value is not valid',
			        icon:{
				            valid: 'glyphicon glyphicon-ok',
				            invalid: 'glyphicon glyphicon-remove',
				            validating: 'glyphicon glyphicon-refresh'
			        },
			        fields: {
			        	"zhutizhigescore": {
			                validators: {
			                    notEmpty: {
			                        message: '主体资格得分不能为空！'
			                    },
			                    regexp: {
			                        regexp:  /(?!^0\.0?0$)^[0-9](\.[0-9]{1,2})?$|^(10|10\.0|10\.00|0\.0|0\.00)$/,
			                        message: '请输入0-10之间的整数或者包含2位小数!'
			                    }
			                }
			            },
			         	"shishizhengjuscore": {
			                validators: {
			                    notEmpty: {
			                        message: '事实证据 得分不能为空！'
			                    },
			                    regexp: {
			                        regexp: /(?!^0\.0?0$)^[0-9](\.[0-9]{1,2})?$|^(10|10\.0|10\.00|0\.0|0\.00)$/,
			                        message: '请输入0-10之间的整数或者包含2位小数!'
			                    }
			                }
			            },
			         	"falushiyongscore": {
			                validators: {
			                    notEmpty: {
			                        message: '法律适用 得分不能为空！'
			                    },
			                    regexp: {
			                        regexp:  /(?!^0\.0?0$)^([0-9]|[1][0-9])(\.[0-9]{1,2})?$|^(20|20\.0|20\.00|0\.0|0\.00)$/,
			                        message: '请输入0-20之间的整数或者包含2位小数!'
			                    }
			                }
			            },
			         	"chengxuguizescore": {
			                validators: {
			                    notEmpty: {
			                        message: '程序规则 得分不能为空！'
			                    },
			                    regexp: {
			                        regexp: /(?!^0\.0?0$)^([0-9]|[1][0-9]|[2][0-9])(\.[0-9]{1,2})?$|^(30|30\.0|30\.00|0\.0|0\.00)$/,
			                        message: '请输入0-30之间的整数或者包含2位小数!'
			                    }
			                }
			            },
			         	"jiafenqingkuang": {
			                validators: {
			                    notEmpty: {
			                        message: '加分情况 得分不能为空！'
			                    },
			                    regexp: {
			                        regexp: /(?!^0\.0?0$)^([0-9]|[1][0-9]|[2][0-9])(\.[0-9]{1,2})?$|^(30|30\.0|30\.00|0\.0|0\.00)$/,
			                        message: '请输入0-30之间的整数或者包含2位小数!'
			                    }
			                }
			            }
			        }
			});
	 });
	 
	 
	 //动态加载计算总分
	 $(document).ready(function(){
		 $("input[type='text']").change(function() {
			 var inputid = new Array();  
			  var inputArray=$("input[class='form-control input-sm']");
			 inputArray.each(//使用数组的循环函数 循环这个input数组  
			 function (){  
		             var input =$(this);//循环中的每一个input元素  
					inputid.push(input.val());      
			  })
			  var num = 0;

			  for(var i=0;i<inputid.length;i++){
				  var a = inputid[i];
				  if(!(a)==""){
					  if(isNaN(parseFloat(inputid[i].value))){
						  inputid[i].value = 0;
						  inputid[i].toString().split(".")
						  //精度问题
				      	  num = (Math.round((num + parseFloat(inputid[i]))*100))/100; 
					  }
				  }else{
				  }
		 		}
		 $("#expertfinalscore1").html("&nbsp;&nbsp;&nbsp;&nbsp;"+num);	
		 $("#expertfinalscore").val(num);	
		 }); 
	 });
</script>
<body>
<div class="center_weizhi">当前位置：专家评分 - 专家评审案卷 - 行政处罚案卷</div>
<div class="center">
<div class="center_list">
	<div class="dingwei">案卷或材料：（<span id ="filecode" style="color:#06C;font-size:16px;">${expertHandlFileList.filecode}</span>）
	
	<c:if test ="${sessionScope.sa_session.sysStatus == '5' }">	
	 <button class="btn btn-success btn-xs" data-toggle="modal" data-target="#myModal" onclick="xiaZaiAnJuan()">下载</button>
	</c:if>
	 </div>
     <div class="dingwei" id ="queren">案卷处罚决定书文号确认：<input type="text" value ="${files.filecode}" style="width:200px; float:right;"  id="fileCodeInput" placeholder="请输入案卷处罚决定书文号"> </div>
		 <div class="dingwei"><button class="btn btn-primary" data-toggle="modal" data-target="#myModal" id ="chickAnjuan"  onclick="chickAnjuan();">开始评分</button></div>
       	<form action="#" id ="zjpfForm" method="post">
        <table class="table table-bordered table-hover table-condensed">
         <thead>
           <tr>
             <td width="50" height="30" bgcolor="#efefef">序号</td>
             <td bgcolor="#efefef">分指标</td>
             <td width="150" bgcolor="#efefef">分指标分值</td>
             <td width="150" bgcolor="#efefef">初评得分</td>
           </tr>
         </thead>
         <tbody>
           <tr>
             <td height="30" align="center">1</td>
             <td>主体资格 </td>
             <td>10</td>
             <td>
             <div class="form-group">
                 <div class="col-sm-12">
             <input type="text"  readonly="readonly"  class="form-control input-sm" id="zhutizhige" name ="zhutizhigescore" value="${expertHandlFileList.zhutizhigescore }" placeholder="请输入初评得分">
           		</div>
           		</div>
             </td>
           </tr>
           <tr>
             <td height="30" align="center">2</td>
             <td>事实证据 </td>
             <td>10</td>
             <td>
             <div class="form-group">
                 <div class="col-sm-12">
             <input  type="text"  readonly="readonly"  class="form-control input-sm" id="shishizhengju" name ="shishizhengjuscore" value="${expertHandlFileList.shishizhengjuscore }"  placeholder="请输入初评得分">
          		</div>
          		</div>
             </td>
           </tr>
           <tr>
             <td height="30" align="center">3</td>
             <td>法律适用 </td>
             <td>20</td>
             <td>
             <div class="form-group">
                 <div class="col-sm-12">
             <input type="text"  readonly="readonly" class="form-control input-sm" id="falvshiyong" name ="falushiyongscore" value="${expertHandlFileList.falushiyongscore }"  placeholder="请输入初评得分">
           		</div>
           		</div>
             </td>
           </tr>
           <tr>
             <td height="30" align="center">4</td>
             <td>程序规则 </td>
             <td>30</td>
             <td>
             <div class="form-group">
                 <div class="col-sm-12">
             <input type="text"  readonly="readonly"  class="form-control input-sm" id="chengxuguize" name ="chengxuguizescore" value="${expertHandlFileList.chengxuguizescore }"  placeholder="请输入初评得分">
           		</div>
           		</div>
             </td>
           </tr>
           <tr>
             <td height="30" align="center">5</td>
             <td>加分情况 </td>
             <td>30</td>
             <td>
             <div class="form-group">
                 <div class="col-sm-12">
             <input type="text"   readonly="readonly"  class="form-control input-sm" id="jiafenqingkuang" name ="jiafenqingkuang" value="${expertHandlFileList.jiafenqingkuang }"  placeholder="请输入初评得分">
          		</div>
          		</div>
             </td>
           </tr>
           <tr>
             <td height="30" colspan="2" align="center">小计</td>
             <td>100</td>
             <td><span id ="expertfinalscore1">&nbsp;&nbsp;&nbsp;&nbsp;${expertHandlFileList.expertfinalscore}</span></td>
           </tr>
           <tr>
             <td height="30" colspan="4">专家评语
             <textarea  rows="5" class="form-control" id="expertreviews" name ="expertreviews"  placeholder="请输入专家评语"></textarea></td>
           </tr>
         </tbody>
       </table>
         <div><input id ="pageNum" type="hidden" value="${pageNum }"></div>
        	<div><input id ="zjpfId"  name = "id" type="hidden" value="${expertHandlFileList.id}"></div>
        	<div><input id ="fileId"  name = "fileid" type="hidden" value="${expertHandlFileList.fileid}"></div>
        	<div><input id="expertfinalscore" type="hidden" name ="expertfinalscore" value="${expertHandlFileList.expertfinalscore}" ></div>
   </form>
   <!-- 	 <a href="#"><button  id="submitBaoChong" type="button" class="btn btn-primary" name="signup" value="Sign up" style="font-size:16px;width:150px; margin-top:5px; display:none"  >信息保存</button></a> -->
</div>
		 <div class="submit"><c:choose> 
			 <c:when test = "${sessionScope.sa_session.sysStatus != 5}"> 
			 </c:when> 
			 <c:otherwise> 
			 	<a href="#"><button type="button" class="btn btn-primary" id="submitBaoChong" name="signup" value="Sign up" style="font-size:16px;width:150px; margin-top:5px; display:none">信息保存</button></a>
			 </c:otherwise> 
			</c:choose></div>
</div>
</body>
</html>