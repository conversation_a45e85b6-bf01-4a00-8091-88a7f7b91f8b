<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<html>
<head>
<script type="text/javascript">
$(document).ready(function(){
	$(".left_menu").hide();
	business.listenEnter("searchButt");
	$("#searchButt").click(function(){
		business.addMainContentParserHtml("zjpf/zjpf_list_commit.do",$("#searchForm").serialize());
	});
	$("#scoredState").change(function(){
		business.addMainContentParserHtml("zjpf/zjpf_list_commit.do",$("#searchForm").serialize());
	});
	$("#entityState").change(function(){
		business.addMainContentParserHtml("zjpf/zjpf_list_commit.do",$("#searchForm").serialize());
	});


	$("#scoredState").find("option[value='${scoredState}']").attr("selected",'selected');
	$("#entityState").find("option[value='${entityState}']").attr("selected",'selected');
});


$(document).ready(function(){

	var curentPage = eval('${pageBean.pageNum}');
	var totalPage = eval('${pageBean.pages}');
	if(totalPage>0){
		var options = {
			bootstrapMajorVersion: 3,
		    currentPage: curentPage,
		    totalPages: totalPage,
		    numberOfPages: 5,
		    itemTexts: function (type, page, current) {
		            switch (type) {
		            case "first":
		                return "首页";
		            case "prev":
		                return "&laquo;";
		            case "next":
		                return "&raquo;";
		            case "last":
		                return "尾页";
		            case "page":
		                return page;
		            }
		    },
		    onPageClicked: function (event, originalEvent, type, page) {
            	business.addMainContentParserHtml(WEBPATH+'/zjpf/zjpf_list_commit.do?pageNum='+page,$("#searchForm").serialize());
            }
    	};
    	$('#pageCon').bootstrapPaginator(options);
	}


});
//有评审中的，请先完成评审中的
function startScore(id){
	$.ajax({
		url: WEBPATH+"/zjpf/checkExpert.do",
		type:'post',
		async:false,
		data:{experHandID:id},
		success:function(data){
			if(data.result=="success"){
				if(data.data=="1"){
					macroMgr.onLevelTwoMenuClick(null, "zjpf/zjpfCommiteeScore.do?id="+id+"&pageNum=${pageNum}");
				}else if(data.data=="0"){
					swal("提示", "有未完成的案卷，请先完成上一份案卷的评查", "info");
				}else if(data.data=="3"){
					//有异常也允许评案卷
					<%--macroMgr.onLevelTwoMenuClick(null, "zjpf/zjpfScore.do?id="+id+"&pageNum=${pageNum}");--%>
					swal("提示", "有异常的案卷,请先检查异常的评查", "info");
				}else{
					swal("异常", "用户信息获取失败", "error");
				}
            }else{
            	if(data.code=="000"){
            		swal({
				        title: "提示",
				        text: data.message,
				        type: "error",
				        confirmButtonText: "确定",
				        confirmButtonColor: "#ec6c62"
				    }, function() {
				        window.location.href=WEBPATH+"/index.do";
				    });
            	}else{
            		swal("提示", "后台出现未知异常", "error");
            	}
            }
		},
		error:function(){
			 swal("error", "请求错误", "error");
		}
	})
}

function checkExpertFile(expertID,fileID){
	if(expertID==null||expertID == ''){
		return ;
	}
	$.ajax({
		data:{expertID:expertID,fileID:fileID},
		dataType:"JSON",
		Type:"post",
		url:WEBPATH+'/zjpf/getExpertHanderID.do',
		success:function(data){
			if(data.id !=null && data.id!=''){
				business.addMainContentParserHtml(WEBPATH+'/zjpf/zjpfScoreView.do?consider=1&status=1&id='+data.id);//跳转到第一轮专家打分的详情
			}else{
				swal("操作失败!", data.text, "error");
			}
		}
	});
}

function errorState(fileId){
	macroMgr.onLevelTwoMenuClick(null, "zjpf/errorState.do?fileId="+fileId);
}
</script>
</head>
<body>
<div class="center_weizhi">当前位置：专家评分 - 委员评审案卷 - 委员评审列表</div>
<div class="center">
<div class="center_list">
    <div class="panel-group" id="accordion">
          <h4 class="panel-title">
            <!---信息填报
            <div class="btn-group" style="margin-right:20px;float:left;">
               <a href="CementEnterprises_input.html"><button type="button" class="btn btn-success" style="font-size:16px;">企业信息录入</button></a>
            </div>--->


          <form role="form" method="get" id ="searchForm" name ="form">
              <select id ="scoredState" class="form-control" style="width:150px;margin-right:5px;" name="scoredState">
                 <option value=''>请选择评分状态</option>
                 <option value = '1'>已评</option>
                 <option value = '0'>全部未评</option>
                 <option value = '7'>实体未评</option>
              </select>
<%--			  <select id ="entityState" class="form-control" style="width:150px;margin-right:5px;" name="entityState">--%>
<%--				  <option value=''>请选择评审类型</option>--%>
<%--				  <option value = '0'>卷面和实体</option>--%>
<%--				  <option value = '1'>只评实体</option>--%>
<%--			  </select>--%>
            <!---搜索--->
            <div style="width:260px;" class="btn-group">
                  <div class="row">
                     <div class="col-lg-6">
                        <div class="input-group">
                           <input type="text" placeholder="案卷文号关键字" class="form-control" name ="fileCode" value="${fileCode }" style="width:200px;">
                           <span class="input-group-btn">
                              <button id="searchButt"  class="btn btn-success" type="button">
                                 快速搜索
                              </button>
                           </span>
                        </div>
                     </div>
                  </div>
            </div>
        </form>
          </h4>
    </div>
        <table class="table table-bordered table-hover table-condensed">
         <thead>
           <tr>
             <td width="50" height="30" bgcolor="#efefef">序号</td>
             <td bgcolor="#efefef">案卷文号</td>
             <td bgcolor="#efefef">案卷类型</td>
             <td width="150" bgcolor="#efefef">专家姓名</td>
             <td width="150" bgcolor="#efefef">初评得分</td>
             <td width="150" bgcolor="#efefef">专家姓名</td>
             <td width="150" bgcolor="#efefef">初评得分</td>
             <td width="150" bgcolor="#efefef">委员姓名</td>
             <td width="150" bgcolor="#efefef">复评得分</td>
<%--		     <td width="150" bgcolor="#efefef">评审类型</td>--%>
             <td width="60" bgcolor="#efefef">状态</td>
             <td width="100" bgcolor="#efefef">操作</td>
           </tr>
         </thead>
<%--         <tbody>--%>

          <!-- function linkExpert(fileType,fileId,expertId,fileCodeCurrent,pageNum) -->
           <c:forEach var="list" items="${pageBean.list}" varStatus="index">
<%--			   <input type="hidden" placeholder="案卷文号关键字" class="form-control" name ="fileCode" value="${list.caseCheckState }" style="width:200px;">--%>
<%--		  <c:choose>
		  	<c:when test="${list.scoredState == '5' }">
			<tbody style="background-color: #f0d8e0">
			</c:when>
			<c:otherwise>
			<tbody>
			</c:otherwise>
			</c:choose>--%>

			<c:choose>
			<c:when test="${list.errorState == '1'}">
			<tbody style="background-color: #f0d8e0">
			</c:when>
			<c:otherwise>
			<tbody>
			</c:otherwise>
			</c:choose>
			   <c:choose>
				   <c:when test="${list.scoredState == '5' }">
<%--					   <input type="hidden" placeholder="案卷文号关键字" class="form-control" name ="fileCode" value="${list.scoredState }" style="width:200px;">--%>
					   <tr style="background-color: #f0d8e0">
				   </c:when>
<%--				   <c:when test="${list.errorState == '1'}">--%>
<%--				       <tr style="background-color: #f0d8e0">--%>
<%--				   </c:when>--%>
<%--				   <c:when test="${list.caseCheckState == '3'}">--%>
<%--					   <tr style="background-color: #3f91c28a;border: white">--%>
<%--				   </c:when>--%>
<%--				   <c:when test="${list.caseCheckState == '1'}">--%>
<%--					   <tr style="background-color: #ffc76d8f;border: white">--%>
<%--				   </c:when>--%>
<%--				   <c:when test="${list.caseCheckState == '2'}">--%>
<%--					   <tr>--%>
<%--				   </c:when>--%>
<%--				   <c:otherwise>--%>
<%--					   <tr>--%>
<%--				   </c:otherwise>--%>
			   </c:choose>

           		<td>${index.index+1}</td>
           		<td>${list.fileCode}</td>
					<c:if test="${list.fileType == '0' }">
		           <td>行政处罚案卷</td>
		           </c:if>
		           <c:if test="${list.fileType == '1' }">
		           <td>按日计罚案卷</td>
		           </c:if>
		           <c:if test="${list.fileType == '2' }">
		           <td>移送行政拘留案卷</td>
		           </c:if>
		           <c:if test="${list.fileType == '3' }">
		           <td>涉嫌环境污染犯罪</td>
		           </c:if>
		           <c:if test="${list.fileType == '4' }">
		           <td>申请法院强制执行案卷</td>
		           </c:if>
		           <c:if test="${list.fileType == '5' }">
		           <td>发现问题的污染源现场监督检查稽查案卷</td>
		           </c:if>
		           <c:if test="${list.fileType == '6' }">
		           <td>查封扣押案卷</td>
		           </c:if>
		           <c:if test="${list.fileType == '7' }">
		           <td>限产停产案卷</td>
		           </c:if>
					   <c:if test="${list.fileType == '9' }">
						   <td>不予处罚案卷</td>
					   </c:if>
<%--           		<td>${list.expertAName}</td>--%>
           		<td>专家A</td>
           		<td onclick="checkExpertFile('${list.expertAID}','${list.fID}')">${list.expertAScore}</td>
<%--           		<td>${list.expertBName}</td>--%>
           		<td>专家B</td>
           		<td onclick="checkExpertFile('${list.expertBID}','${list.fID}')">${list.expertBScore}</td>
           		<td>${list.expertName}</td>
           		<td>${list.expertFinalScore}</td>
<%--			   <td>--%>
<%--				   <c:if test="${list.entityState == '1'}">只评实体</c:if>--%>
<%--				   <c:if test="${list.entityState != '1'}">卷面和实体</c:if>--%>
<%--			   </td>--%>
           		<c:choose>
					<c:when test="${list.scoredState == '0' && list.caseCheckState == '1' }">
						<td><button class="btn btn-xs" style="background-color: #467bdeb5; color: #ffffff;">只评卷面</button></td>
					</c:when>
					<c:when test="${list.scoredState == '0' && list.caseCheckState == '2' }">
						<td><button class="btn btn-xs" style="background-color: #5de0bd; color: #ffffff;">只评实体</button></td>
					</c:when>
					<c:when test="${list.scoredState == '0' && list.caseCheckState == '3' }">
						<td><button class="btn btn-xs" style="background-color: #019ccdc2; color: #ffffff;">卷面实体都评</button></td>
					</c:when>
         			<c:when test="${list.scoredState == '0' && list.caseCheckState != '1' && list.caseCheckState != '2' && list.caseCheckState != '3'}">
						<td><button class="btn btn-info btn-xs" >未评</button></td>
					</c:when>
					<c:when test="${list.scoredState == '7' }">
						<td><button class="btn btn-primary btn-xs" style="background-color: #5acde2;border: white">未评只评实体</button></td>
					</c:when>

<%--					<c:if test="${list.caseCheckState == '1'  }">--%>
<%--						<td><button class="btn btn-primary btn-xs" style="background-color: #5acde2;border: white">未评只评卷面</button></td>--%>
<%--					</c:if>--%>

<%--					<c:when test="${list.caseCheckState == '1' }">--%>
<%--						<td><button class="btn btn-primary btn-xs" style="background-color: #5acde2;border: white">未评只评卷面</button></td>--%>
<%--					</c:when>--%>
<%--					<c:when test="${list.scoredState == '10'}">--%>
<%--						<td align="center"><button class="btn btn-primary btn-xs" style="background-color: #C9302C;border: white">未评只评卷面</button></td>--%>
<%--					</c:when>--%>
					<c:when test="${list.scoredState == '5' }">
						<td><button class="btn btn-danger btn-xs" style="background-color: #C9302C;" onclick="errorState('${list.fID}')">异常</button></td>
					</c:when>

					<c:when test="${list.scoredState == '1' }">
						<td><button class="btn btn-success btn-xs" >已评</button></td>
					</c:when>
					<c:otherwise>
						<td><button class="btn btn-warning btn-xs" >评审中</button></td>
					</c:otherwise>
				</c:choose>
				<td align="center">
               		<c:if test ="${sessionScope.sa_session.sysStatus == '5' and list.considerState != '1'  }">
               			<c:if test="${list.scoredState == '1' }">
         					<a href="javascript:void(0);" onclick="macroMgr.onLevelTwoMenuClick(null, 'zjpf/zjpfCommiteeScore.do?id=${list.eID}&pageNum=${pageNum}' )"><button class="btn btn-danger btn-xs" >评分</button></a>
         		    	</c:if>
         				<c:if test="${list.scoredState != '1' }">
         					<a href="javascript:void(0);" onclick="startScore('${list.eID}')"><button class="btn btn-danger btn-xs" >评分</button></a>
         		   	  	</c:if>
         		    </c:if>

               		<c:if test ="${list.scoredState != '0' && list.scoredState != '7' }">
               			<a href="javascript:void(0);" onclick="macroMgr.onLevelTwoMenuClick(null, 'zjpf/zjpfCommitView.do?id=${list.eID}&status=1' )"><button class="btn btn-warning btn-xs" >查看</button></a>
		          	</c:if>
               	</td>



           		<%-- <c:choose>
           			<c:when test="${list.scoredState == '1' }">
           			<td><button class="btn btn-success btn-xs" data-toggle="modal" data-target="#myModal">已评</button></td>
           			<td><a ><button onclick="saveScore('${list.eID}',1)" class="btn btn-primary btn-xs" >查看</button></a>
           			    <a ><button onclick="saveScore('${list.eID}','')" class="btn btn-primary btn-xs" >评分</button></a></td>
           		</c:when>
          			<c:otherwise>
          			 	<td><button class="btn btn-warning btn-xs" data-toggle="modal" data-target="#myModal">未评</button></td>
          			 	<td><a ><button onclick="saveScore('${list.eID}',1)" class="btn btn-primary btn-xs" >查看</button></a>
          				    <a ><button onclick="saveScore('${list.eID}','')" class="btn btn-primary btn-xs" >评分</button></a>
          				    <a  href="javascript:void(0);" onclick="macroMgr.onLevelTwoMenuClick(null, 'zjpf/zjpfScore.do?id=${list.eID}&pageNum=${pageNum}' )"><button class="btn btn-danger btn-xs" >评分</button></a>
          				    </td>
           		</c:otherwise>
           		</c:choose> --%>

           </tr>
           </c:forEach>

         </tbody>       </table>
    </div>
     <div class="page">
    <span style="padding:12px; float:left; color:#0099cc;">共${pageBean.total}条记录</span>
        <ul class="pagination" id="pageCon">
        </ul>
    </div>
</div>
</body>
</html>
