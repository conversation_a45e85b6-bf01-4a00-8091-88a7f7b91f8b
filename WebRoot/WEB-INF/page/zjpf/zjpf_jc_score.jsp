<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
	<meta http-equiv="pragma" content="no-cache">
	<meta http-equiv="cache-control" content="no-cache">
	<meta http-equiv="expires" content="0"> 
<head>
<style>
/*
PDFObject appends the classname "pdfobject-container" to the target element.
This enables you to style the element differently depending on whether the embed was successful.
In this example, a successful embed will result in a large box.
A failed embed will not have dimensions specified, so you don't see an oddly large empty box.
*/
.pdfobject-container {
	width: 100%;
	height:580px;
	margin: 2em 0;
}
.textarea-success {
	border-top:green 1px solid;
	border-bottom:green 1px solid; 
	border-left:green 1px solid;
    	border-right:green 1px solid;
}
.textarea-error {
	border-top:red 1px solid;
	border-bottom:red 1px solid; 
	border-left:red 1px solid;
    	border-right:red 1px solid;
}
.center{	position: absolute;left: 0px;top: 237px; width: 98%;margin: 0 20px;bottom: 35px;right: 5px;
	}
</style>
<script type="text/javascript">
	$(".left_menu").hide();
/* 	判断评分的状态scoredState为1 */
$(document).ready(function(){
	var scoredState= eval('${expertHandlFileList.scoredstate}');
	var status = eval('${status}');
	//查看
	
	if(scoredState == '1'){
		if(status !='1'){
			$("#submitBtn").show();
			$("#jcpj").removeAttr("disabled");
			$("#yxdxanlituijian").attr("disabled",false);
			//$("#chickAnjuan").hide();
			/*$("#fileCodeInput").attr("disabled","disabled");
			$("#fileCodeInput").val('${ expertHandlFileList.filecode}');*/
			$("#fileCodeInput").hide();
			 $("#queren").hide();
		 	$(".inputDisabled").removeAttr("readonly");
			//扣分理由
		 	$("textarea").removeAttr("disabled");
	 	}
	}else{
		 //$(".inputDisabled").attr("readonly","readonly");
		 //$("input:checkbox").attr("disabled",true);
	}
});

/* 评分状态的scoredState为0 */
/* function chickAnjuan(){//开始评分
	var fileCodeInput = trim($("#fileCodeInput").val());
	var fileCode = trim($("#fileCode").text());
	if( fileCodeInput == fileCode){
		swal("开始打分", "开始打分!", "success");
		//隐藏开始打分按钮
		$("#chickAnjuan").hide();
		//移除打分div只读状态
		$(".inputDisabled").removeAttr("readonly");
		
		$("#xiaZaiFuJian").removeAttr("disabled");
		$("#xiaZai").removeAttr("disabled");
		//案卷处罚书 设为不可用
		$("#fileCodeInput").attr("disabled","disabled");	
		//案卷评价设为可用
		$("#yxdxanlituijian").attr("disabled",false)
		//显示保存按钮
		$("[id^='submitBtn']").show();
		//扣分理由
		$("textarea").removeAttr("disabled");
		$("input:checkbox").removeAttr("disabled");
	}else{
		swal("开始打分", "请输入正确的案卷号！", "error");
	}
} */

function trim(str) {
	return str.replace(/(^\s+)|(\s+$)/g, "");
}

//下载文件
function xiaZaiAnJuan(){
	//var fileCode = $("#fileCode").html();
	var fileid  = $("#fileId").val();
 	$.ajax({
	    type:"post",
	    url:WEBPATH+'/jcpf/jcpfExistFileUrl.do',
	    data:{fileId:fileid },           //注意数据用{}
	    success:function(data){  //成功
		 if("yes" == data){
				window.location.href="${pageContext.request.contextPath}/jcpf/jcpfAnJuandown.do?fileid="+fileid;
			    return false;
	         }else if("no" == data){
	            	  swal( "操作失败","该案卷不存在!", "error");
	            	  return false;
			}else if("suffixerror" ==data){
				  swal( "操作失败","该案卷上传数据格式有问题!", "error");
            	  return false;
			}
	         }
	});
 }
 
function loding(btn,itext){
	document.getElementById(btn).innerHTML = "加载.."
	document.getElementById(btn).disabled = "disabled"
    setTimeout(function () {
      document.getElementById(btn).innerHTML = itext;
      document.getElementById(btn).removeAttribute("disabled");
    },3000);
}	

$(document).ready(function(){
	//保存数据方法
	var pageNum = $("#pageNum").val();
});
</script>

</head>
<div id='zjpfJCVue'>
<div   class="center_weizhi">当前位置：：专家评分 - 专家评审案卷  - ${filetypeName }</div>
<div class="center"  id="scrollBarCenter">
<div class="center_list" id="anjuanxinxi">
	<input id="expertFileId"  type="hidden" value="${expertFileId }" >
	<input id="fileId" type="hidden" :value="scoringIndexList.fileid">
	<button    class="btn btn-primary"  style="font-size:16px;width:150px; margin-top:5px;margin-right: 10px;float: right;" onclick="macroMgr.onLevelOneMenuClick(null, 'zjpf.do')">返回</button>
	<div class="dingwei">案卷或材料：<span style="color:#009CCD;font-size:14px; font-weight:400;" id ="fileCode">{{scoringIndexList.filecode }} </span>
	<c:if test ="${sessionScope.sa_session.sysStatus == '5' }">	
		<a v-bind:href="scoringIndexList.downUrl" target="_Blank">
			<button class="btn btn-success btn-xs" id="xiaZai" data-toggle="modal" data-target="#myModal">下载</button>
		</a>

		<a  target="_Blank">
			<button id="look" class="btn btn-info btn-xs" data-toggle="modal" data-remote="${webpath}/jcpf/showFileModal.do?fastDFSUrl=+'${fastDFS}'+" data-target="#view">预览</button>
		</a>

	</c:if>
	</div>
	<select id="indexName" onchange="change(this.value)"></select>
    <!-- <div class="dingwei" id ="queren">案卷处罚决定书文号确认：<input type="text" class="form-control" style="width:200px; float:right;"  id="fileCodeInput" placeholder="请输入案卷处罚决定书文号" ></div>
    <div class="dingwei"><button id ="chickAnjuan" class="btn btn-primary" data-toggle="modal"  onclick="chickAnjuan()" data-target="#myModal">开始评分</button></div> -->
 	   <table class="table table-striped table-bordered table-condensed" id="tableOuter" >
      <thead>
           <tr>
             <td width="50" height="30" bgcolor="#efefef">序号</td>
             <td width="100" bgcolor="#efefef">分指标</td>
             <td width="90" bgcolor="#efefef">分指标分值</td>
             <td width="200" bgcolor="#efefef">指标说明</td>
             <td bgcolor="#efefef"> <span style="float: left;">判断标准</span><span style="float: right;margin-right: 120px;">分值</span></td>
             <td bgcolor="#efefef" width="70">一票否决</td>
             <td bgcolor="#efefef" width="70">无需评查</td>
             <td bgcolor="#efefef" width="80">得分</td>
             <td width="15%" bgcolor="#efefef">扣分理由</td>
           </tr>
         </thead>
         	<tbody  class="form-group">
			<tr v-for="(scoringIndex, index) in scoringIndexList.expertHandlIndexScoreList"  :class="scoringIndex.className" v-if="scoringIndex.isError!=1">
				<td height="30" align="center" style="vertical-align:middle;">{{index+1}}</td>
				<td style="vertical-align:middle;" :class="scoringIndex.className">{{scoringIndex.indexname}}
					<span v-if="scoringIndex.isError==1" >
							<a href="#" style="width:60px; background-color: red"   v-on:click="showErrorMsg(scoringIndex)">存在争议</a>
						</span>
				</td>
				<td style="vertical-align:middle;">{{scoringIndex.indexscore}}</td>
				<td style="vertical-align:middle; width:200px;">{{scoringIndex.indexdesc}}</td>
				<td v-if="scoringIndex.expertHandlItemScoreList != null && scoringIndex.expertHandlItemScoreList.length !=0 "  style="padding:0;">
					<table width="100%" border="0" cellspacing="0" cellpadding="0" style="padding:0;">
						<tr  v-for="(scoringItem, index1) in scoringIndex.expertHandlItemScoreList">
							<td style="border-bottom:solid 1px #ddd; border-right:solid 1px #ddd;vertical-align:middle;">
								<div style="vertical-align:middle; margin:0 5px 5px 0;">
									{{scoringItem.itemname}}<!-- （{{scoringItem.itemscore}}分） -->
								</div>
								<span style="color:red" v-if="scoringItem.isInCheck==1 && scoringIndex.inCheckValue!=1 && scoringIndex.voteDownValue!=1">
		            			&nbsp;&nbsp;是否无需评查：
                                			<input type="checkbox" id="" v-model="scoringItem.inCheckValue"
												   @change="wuxupingcha2(index,index1)"
												   style="width:16px; height:16px;  display: table-cell;vertical-align: middle; text-align: center; ">
                                		</span>
							</td>
							<td style="border-bottom:solid 1px #ddd; width:155px;vertical-align:middle;">
								<div  :id="index+'expertItem'+index1" style="margin:0 0 5px 0; float:left;">
									<div v-if="scoringIndex.isAdd==1" style="float:left; padding:5px 2px 7px 3px; color:red; font-size:20px;">＋</div>
									<div v-if="scoringIndex.isAdd!=1" style="float:left; padding:0 0 3px 0; margin:0; color:red; font-size:26px;">－</div>
									<div style="float:right; padding-top:4px;">
										<input @input="updateItem(index,index1,scoringItem.itemscore,scoringItem.temItemId)"
											   v-model="scoringItem.score" type="text" :class="'checkitem'+index" :id="index+'scoreInput'+index1" :name = "index+'item'+index1"
											   class="form-control inputDisabled"   placeholder="请输入初评得分" style="width:125px;">
									</div>
									<div style="color:red; font-size: 12px;">{{scoringItem.validatorMessage}}
									</div>
								</div>
							</td>
						</tr>
					</table>
				</td>
				<td v-else >
					<table width="100%" border="0" cellspacing="0" cellpadding="0">
						<tr>
							<td style="border-bottom:solid 1px #f7f7f7;">
								<div style="vertical-align:middle; margin:0 5px 5px 0;">
									{{scoringIndex.indexdesc}}
								</div>
							</td>
							<td style="border-bottom:solid 1px #f7f7f7;">
								<div :id="'expertIndex'+index"  class="form-group" style="width: 150px;float: right;">
									<div v-if="scoringIndex.isAdd==1" style="float:left; padding:5px 2px 7px 3px; color:red; font-size:20px;">+</div>
									<div v-if="scoringIndex.isAdd!=1" style="float:left; padding:0 0 3px 0; margin:0; color:red; font-size:26px;">-</div>
									<input type="text" v-model="scoringIndex.score" :id="index+'scoreInput'+index1"
										   @input="updateIndex(index,scoringIndex.indexscore)" :class="'checkitem'+index"
										   class="form-control inputDisabled"  placeholder="请输入初评得分">
									<div style="color:red;font-size: 5px;">{{scoringIndex.validatorMessage}}</div>
								</div>
							</td>
						</tr>
					</table>
				</td>
				<!-- 一票否决 -->
				<td style="vertical-align:middle; text-align:center;">
		              <span v-if="scoringIndex.isVoteDown ==1" >
		              		 <input type="checkbox" :id="'checkbox'+index"
									@change="changeCheckBoxCli(index,'1')" v-model="scoringIndex.voteDownValue"
									style="width:16px; height:16px; display: table-cell;vertical-align: middle; text-align: center;">
               				 <label :for="'checkbox'+index" class="checkbox-blue" checked></label>
		              </span>
				</td>
				<!-- 无需评查 -->
				<td style="vertical-align:middle;text-align:center;">
		            <span v-if="scoringIndex.isInCheck ==1">
		              		 <input type="checkbox" id="checkbox1" v-model="scoringIndex.inCheckValue"
									@change="wuxupingcha(index)"
									style="width:16px; height:16px;  display: table-cell;vertical-align: middle; text-align: center; ">
               				 <label for="checkbox1" class="checkbox-blue" checked></label>
		              </span>
				</td>
				<td style="vertical-align:middle;text-align:center;">
					<input type="text" id="111" disabled="disabled" v-model="scoringIndex.resultScore" style="background-color:transparent; width:80px; border:0px;vertical-align:middle;text-align:center;">
					<!-- <input type="text" id="222" v-if="scoringIndex.calculScore!=null && scoringIndex.calculScore!=''" disabled="disabled" v-model="scoringIndex.calculScore" style="background-color:transparent; width:80px; border:0px;vertical-align:middle;text-align:center;"> -->
				</td>

				<!-- 扣分理由 -->
				<td >
	           	   <textarea maxlength="1000" @input="updateCommon(index)" :id="'comment'+index" :rows="scoringIndex.expertHandlItemScoreList.length" class="form-control" v-model="scoringIndex.comment" placeholder="请输入扣分理由" style="width:100%;">

				   </textarea>
					<div style="color:red; font-size: 12px;">{{scoringIndex.validatorMessage}}</div>
				</td>

			</tr >
			<tr v-for="(scoringIndex, index) in scoringIndexList.expertHandlIndexScoreList"  :class="scoringIndex.className" v-if="scoringIndex.isError==1" style="background-color: #ECD9E0">
				<td height="30" align="center" style="vertical-align:middle;">{{index+1}}</td>
				<td style="vertical-align:middle;" :class="scoringIndex.className">{{scoringIndex.indexname}}
					<span v-if="scoringIndex.isError==1" >
							<a href="#" style="width:60px; background-color: red"   v-on:click="showErrorMsg(scoringIndex)">存在争议</a>
						</span>
				</td>
				<td style="vertical-align:middle;">{{scoringIndex.indexscore}}</td>
				<td style="vertical-align:middle; width:200px;">{{scoringIndex.indexdesc}}</td>
				<td v-if="scoringIndex.expertHandlItemScoreList != null && scoringIndex.expertHandlItemScoreList.length !=0 "  style="padding:0;">
					<table width="100%" border="0" cellspacing="0" cellpadding="0" style="padding:0;">
						<tr  v-for="(scoringItem, index1) in scoringIndex.expertHandlItemScoreList">
							<td style="border-bottom:solid 1px #ddd; border-right:solid 1px #ddd;vertical-align:middle;">
								<div style="vertical-align:middle; margin:0 5px 5px 0;">
									{{scoringItem.itemname}}<!-- （{{scoringItem.itemscore}}分） -->
								</div>
								<span style="color:red" v-if="scoringItem.isInCheck==1 && scoringIndex.inCheckValue!=1 && scoringIndex.voteDownValue!=1">
		            			&nbsp;&nbsp;是否无需评查：
                                			<input type="checkbox" id="" v-model="scoringItem.inCheckValue"
												   @change="wuxupingcha2(index,index1)"
												   style="width:16px; height:16px;  display: table-cell;vertical-align: middle; text-align: center; ">
                                		</span>
							</td>
							<td style="border-bottom:solid 1px #ddd; width:155px;vertical-align:middle;">
								<div  :id="index+'expertItem'+index1" style="margin:0 0 5px 0; float:left;">
									<div v-if="scoringIndex.isAdd==1" style="float:left; padding:5px 2px 7px 3px; color:red; font-size:20px;">＋</div>
									<div v-if="scoringIndex.isAdd!=1" style="float:left; padding:0 0 3px 0; margin:0; color:red; font-size:26px;">－</div>
									<div style="float:right; padding-top:4px;">
										<input @input="updateItem(index,index1,scoringItem.itemscore,scoringItem.temItemId)"
											   v-model="scoringItem.score" type="text" :class="'checkitem'+index" :id="index+'scoreInput'+index1" :name = "index+'item'+index1"
											   class="form-control inputDisabled"   placeholder="请输入初评得分" style="width:125px;">
									</div>
									<div style="color:red; font-size: 12px;">{{scoringItem.validatorMessage}}
									</div>
								</div>
							</td>
						</tr>
					</table>
				</td>
				<td v-else >
					<table width="100%" border="0" cellspacing="0" cellpadding="0">
						<tr>
							<td style="border-bottom:solid 1px #f7f7f7;">
								<div style="vertical-align:middle; margin:0 5px 5px 0;">
									{{scoringIndex.indexdesc}}
								</div>
							</td>
							<td style="border-bottom:solid 1px #f7f7f7;">
								<div :id="'expertIndex'+index"  class="form-group" style="width: 150px;float: right;">
									<div v-if="scoringIndex.isAdd==1" style="float:left; padding:5px 2px 7px 3px; color:red; font-size:20px;">+</div>
									<div v-if="scoringIndex.isAdd!=1" style="float:left; padding:0 0 3px 0; margin:0; color:red; font-size:26px;">-</div>
									<input type="text" v-model="scoringIndex.score" :id="index+'scoreInput'+index1"
										   @input="updateIndex(index,scoringIndex.indexscore)" :class="'checkitem'+index"
										   class="form-control inputDisabled"  placeholder="请输入初评得分">
									<div style="color:red;font-size: 5px;">{{scoringIndex.validatorMessage}}</div>
								</div>
							</td>
						</tr>
					</table>
				</td>
				<!-- 一票否决 -->
				<td style="vertical-align:middle; text-align:center;">
		              <span v-if="scoringIndex.isVoteDown ==1" >
		              		 <input type="checkbox" :id="'checkbox'+index"
									@change="changeCheckBoxCli(index,'1')" v-model="scoringIndex.voteDownValue"
									style="width:16px; height:16px; display: table-cell;vertical-align: middle; text-align: center;">
               				 <label :for="'checkbox'+index" class="checkbox-blue" checked></label>
		              </span>
				</td>
				<!-- 无需评查 -->
				<td style="vertical-align:middle;text-align:center;">
		            <span v-if="scoringIndex.isInCheck ==1">
		              		 <input type="checkbox" id="checkbox1" v-model="scoringIndex.inCheckValue"
									@change="wuxupingcha(index)"
									style="width:16px; height:16px;  display: table-cell;vertical-align: middle; text-align: center; ">
               				 <label for="checkbox1" class="checkbox-blue" checked></label>
		              </span>
				</td>
				<td style="vertical-align:middle;text-align:center;">
					<input type="text" id="111" disabled="disabled" v-model="scoringIndex.resultScore" style="background-color:transparent; width:80px; border:0px;vertical-align:middle;text-align:center;">
					<!-- <input type="text" id="222" v-if="scoringIndex.calculScore!=null && scoringIndex.calculScore!=''" disabled="disabled" v-model="scoringIndex.calculScore" style="background-color:transparent; width:80px; border:0px;vertical-align:middle;text-align:center;"> -->
				</td>

				<!-- 扣分理由 -->
				<td >
	           	   <textarea maxlength="1000" @input="updateCommon(index)" :id="'comment'+index" :rows="scoringIndex.expertHandlItemScoreList.length" class="form-control" v-model="scoringIndex.comment" placeholder="请输入扣分理由" style="width:100%;">

				   </textarea>
					<div style="color:red; font-size: 12px;">{{scoringIndex.validatorMessage}}</div>
				</td>

			</tr >
          		 <tr v-for="(scoringIndex, index) in scoringIndexList.expertHandlIndexScoreList"  :class="scoringIndex.className" v-if="scoringIndex.isError!=1">
		             <td height="30" align="center" style="vertical-align:middle;">{{index+1}}</td>
		             <td style="vertical-align:middle;" :class="scoringIndex.className">{{scoringIndex.indexname}}
<%--						 <span v-if="scoringIndex.isError==1" >--%>
<%--							<a href="#" style="width:60px; background-color: red"   v-on:click="showErrorMsg(scoringIndex)">存在争议</a>--%>
<%--						</span>--%>
						 <p v-if="scoringIndex.isError==1" style="margin:0 auto;width: 67px;height: 24px;background: #DF1912;border-radius: 3px;">
							 <a href="#" style="font-size:14px;line-height: 24px;font-weight: 400;color: #ffffff" v-on:click="showErrorMsg(scoringIndex)">存在争议</a>
						 </p>
					 </td>
		             <td style="vertical-align:middle;">{{scoringIndex.indexscore}}</td>
		             <td style="vertical-align:middle; width:200px;">{{scoringIndex.indexdesc}}</td>
		             <td v-if="scoringIndex.expertHandlItemScoreList != null && scoringIndex.expertHandlItemScoreList.length !=0 "  style="padding:0;">
                        <table width="100%" border="0" cellspacing="0" cellpadding="0" style="padding:0;">
                          <tr  v-for="(scoringItem, index1) in scoringIndex.expertHandlItemScoreList">   
                          	<td style="border-bottom:solid 1px #ddd; border-right:solid 1px #ddd;vertical-align:middle;">
                                 <div style="vertical-align:middle; margin:0 5px 5px 0;">
		            			{{scoringItem.itemname}}<!-- （{{scoringItem.itemscore}}分） -->
		            			</div>
		            		<span style="color:red" v-if="scoringItem.isInCheck==1 && scoringIndex.inCheckValue!=1 && scoringIndex.voteDownValue!=1">
		            			&nbsp;&nbsp;是否无需评查：
                                			<input type="checkbox" id="" v-model="scoringItem.inCheckValue"
					              		  @change="wuxupingcha2(index,index1)" 
					              		  style="width:16px; height:16px;  display: table-cell;vertical-align: middle; text-align: center; ">
                                		</span>	
                            </td>                      
                            <td style="border-bottom:solid 1px #ddd; width:155px;vertical-align:middle;">
                                 <div  :id="index+'expertItem'+index1" style="margin:0 0 5px 0; float:left;">
	                                 <div v-if="scoringIndex.isAdd==1" style="float:left; padding:5px 2px 7px 3px; color:red; font-size:20px;">＋</div>
	                                 <div v-if="scoringIndex.isAdd!=1" style="float:left; padding:0 0 3px 0; margin:0; color:red; font-size:26px;">－</div>
	                                 <div style="float:right; padding-top:4px;">
	                                 	<input @input="updateItem(index,index1,scoringItem.itemscore,scoringItem.temItemId)"  
	                                       v-model="scoringItem.score" type="text" :class="'checkitem'+index" :id="index+'scoreInput'+index1" :name = "index+'item'+index1"
	                                      class="form-control inputDisabled"   placeholder="请输入初评得分" style="width:125px;">
	                                 </div>
                                 	<div style="color:red; font-size: 12px;">{{scoringItem.validatorMessage}}
                                 	</div>
                                 </div>
                            </td>                          
                          </tr>
                        </table>
		             </td>
		            <td v-else >
		             <table width="100%" border="0" cellspacing="0" cellpadding="0">
		             	<tr>
		             		<td style="border-bottom:solid 1px #f7f7f7;">
                                 <div style="vertical-align:middle; margin:0 5px 5px 0;">
		            				{{scoringIndex.indexdesc}}
		            			</div>
                            </td>    
                            <td style="border-bottom:solid 1px #f7f7f7;">
                            	<div :id="'expertIndex'+index"  class="form-group" style="width: 150px;float: right;">
	                            	<div v-if="scoringIndex.isAdd==1" style="float:left; padding:5px 2px 7px 3px; color:red; font-size:20px;">+</div>
		                            <div v-if="scoringIndex.isAdd!=1" style="float:left; padding:0 0 3px 0; margin:0; color:red; font-size:26px;">-</div>
				                		<input type="text" v-model="scoringIndex.score" :id="index+'scoreInput'+index1"
				                		 @input="updateIndex(index,scoringIndex.indexscore)" :class="'checkitem'+index"
				               	  		 class="form-control inputDisabled"  placeholder="请输入初评得分">
			         			  	<div style="color:red;font-size: 5px;">{{scoringIndex.validatorMessage}}</div>
			         			 </div>
                            </td>
		             	</tr>
		             </table>
		           </td>
		           <!-- 一票否决 -->
		            <td style="vertical-align:middle; text-align:center;">
		              <span v-if="scoringIndex.isVoteDown ==1" >
		              		 <input type="checkbox" :id="'checkbox'+index" 
		              		  @change="changeCheckBoxCli(index,'1')" v-model="scoringIndex.voteDownValue"
		              		  style="width:16px; height:16px; display: table-cell;vertical-align: middle; text-align: center;">
               				 <label :for="'checkbox'+index" class="checkbox-blue" checked></label>
		              </span>
		           </td>
		           <!-- 无需评查 -->
		            <td style="vertical-align:middle;text-align:center;">
		            <span v-if="scoringIndex.isInCheck ==1">
		              		 <input type="checkbox" id="checkbox1" v-model="scoringIndex.inCheckValue"
		              		  @change="wuxupingcha(index)" 
		              		  style="width:16px; height:16px;  display: table-cell;vertical-align: middle; text-align: center; ">
               				 <label for="checkbox1" class="checkbox-blue" checked></label>
		              </span>
		           </td>
		           <td style="vertical-align:middle;text-align:center;">
		           	  <input type="text" id="111" disabled="disabled" v-model="scoringIndex.resultScore" style="background-color:transparent; width:80px; border:0px;vertical-align:middle;text-align:center;">
		           	  <!-- <input type="text" id="222" v-if="scoringIndex.calculScore!=null && scoringIndex.calculScore!=''" disabled="disabled" v-model="scoringIndex.calculScore" style="background-color:transparent; width:80px; border:0px;vertical-align:middle;text-align:center;"> -->
		           </td>
		           
		           <!-- 扣分理由 -->
	             <td >																   
	           	   <textarea maxlength="1000" @input="updateCommon(index)" :id="'comment'+index" :rows="scoringIndex.expertHandlItemScoreList.length" class="form-control" v-model="scoringIndex.comment" placeholder="请输入扣分理由" style="width:100%;">
				   
				   </textarea>
				   <div style="color:red; font-size: 12px;">{{scoringIndex.validatorMessage}}</div>
	             </td>
		           
		    	</tr>
		           <tr>
		           		<td></td>
		           		<td align="center"><strong>合计</strong></td>
		           		<td>103</td>
		           		<td></td>
		           		<td><strong></strong></td>
		           		<td></td>
		           		<td align="center"><strong>最终得分</strong></td>
		           		<td style="vertical-align:middle;text-align:center;">{{scoringIndexList.expertfinalscore}}</td>
		           		<td></td>
		           </tr>
				 <tr>
					 <td height="30" colspan="9">
						 <textarea maxlength="2000" v-model="scoringIndexList.problemRemark" rows="5" class="form-control" id="problem_remark" placeholder="疑难（争议）问题描述"></textarea>
					 </td>
				 </tr>
	           <tr>
           		<td height="30" colspan="9">
           		<input id="yxdxanlituijian" name = "yxdxanlituijian" type="checkbox"  v-on:click="jcpjCheckBoxClick()" >优秀案卷推荐
            		 <div class="col-sm-12" style="display: none;" id ="yxdxAnLiTuiJianReviews1"  >
						 <table>
							 <c:forEach items="${tcDictionaryList}" var="item" varStatus="status">
								 <tr>
									 <input  type="checkbox" name="yxaj" value="${item.code}" >&nbsp;${item.name} &nbsp; &nbsp;
									 <c:if test="${status.count%4==0}"><br></c:if>
								 </tr>
							 </c:forEach>

						 </table>
            			<textarea maxlength="2000" v-model="scoringIndexList.yxdxanlituijianreviews" rows="5" class="form-control" id="yxaj" placeholder="请输入专家评语，长度不能超过2000个字符"></textarea>
            		 </div>
            	</td>
	           </tr>
	           <tr>
           		<td height="30" colspan="9">
           		<input id="ajpjYxdxanlituijian" name = "ajpjYxdxanlituijian" type="checkbox"  v-on:click="ajpjCheckBoxClick()" >较差案卷推荐
            		 <div class="col-sm-12" style="display: none;" id ="ajpjYxdxAnLiTuiJianReviews1"  >
						 <table>
							 <c:forEach items="${jcajDictionaryList}" var="item" varStatus="status">
								 <tr>
									 <input  type="checkbox" name="jcaj" value="${item.code}" >&nbsp;${item.name}&nbsp; &nbsp;
									 <c:if test="${status.count%4==0}"><br/></c:if>
								 </tr>
							 </c:forEach>
						 </table>
            			<textarea maxlength="2000" v-model="scoringIndexList.ajpjYxdxanlituijianreviews" rows="5" class="form-control" id="jcpj" placeholder="请输入专家评语，长度不能超过2000个字符"></textarea>
            		 </div>
            	</td>
	           </tr>
				 <tr>
					 <td height="30" colspan="9">
						 <input id="noTuiJian" name ="noTuiJian" type="checkbox"  v-on:click="noCheckBoxClick()" >不推荐为典型案例
					 </td>
				 </tr>
          	</tbody>
       </table>
       
       	<div class="submit">
       	<button v-if="scoringIndexList.scoredstate == 0 || scoringIndexList.scoredstate == 2" type="submit" class="btn btn-primary" id="submitBtnTemp" v-on:click="saveSubmit(scoringIndexList.id, 0)" style="font-size:14px;width:126px; height:40px;color:#ffffff;margin-top:5px;padding:0">信息暂存</button>
       	<button class="btn btn-danger" id="submitBtn" v-on:click="saveSubmit(scoringIndexList.id,1)" type="button" style="font-size:14px;width:126px; height:40px;color:#ffffff;margin-top:5px;">信息保存</button>
       	</div>
         <div><input id ="pageNum" type="hidden" value="${pageNum }"></div>
 		<div class="submit">
 			<c:choose> 
 			 <c:when test = "${sessionScope.sa_session.sysStatus != 5}"> 
			 </c:when> 
			 <c:otherwise> 
			<!-- 	<a href="#"><button type="button" class="btn btn-primary" id="saveSubmit" name="signup" value="Sign up" style="font-size:16px;width:150px; margin-top:5px; display:none">信息保存</button></a>
			 --></c:otherwise> 
			</c:choose></div>
 	</div>
</div>
</div>
<script >
	var firScolltop=0;
	var oldValue='';
	var scoringIndexList =null;
	var expertFileId = $("#expertFileId").val();
	if(expertFileId != null){
		$.ajax({
			cache : true,
			type : "GET",
			async : false,
			//api/test/detail/behaviour/74
			url: WEBPATH+"/zjpf/getIndexList.do",
			data:{
				id:expertFileId,
			},
			error : function(request) {
				swal("错误!","请求异常！", "error");
			},
			success : function(data) {
                if(data.result =='success'){
                    scoringIndexList = data.data;
                    // scoringIndexList.problemRemark = problemRemark;
                    for(var i=0;i<data.data.expertHandlIndexScoreList.length;i++){
                        data.data.expertHandlIndexScoreList[i].className = 'class'+ data.data.expertHandlIndexScoreList[i].id;
                        var index = data.data.expertHandlIndexScoreList[i];
                        //先创建好select里面的option元素
                        var option=document.createElement("option");
                        //转换DOM对象为JQ对象,好用JQ里面提供的方法 给option的value赋值
                        $(option).val('class' + index.id);
                        $(option).text(index.indexname);
                        $("#indexName").append(option);
                    }
                    console.log(scoringIndexList);
                }
			} 
		});			
	}
    function change(value) {
		var mao;
		var pos;
		var poshigh;
		mao = $("." + value);
		pos = mao.offset().top;
		poshigh = mao.height();
		//alert(poshigh)
		var temp=pos+firScolltop;
		if(oldValue!=''){
			// alert("old:"+$("." + oldValue).offset().top);
			temp=temp-$("." + oldValue).offset().top;
		}
		// alert(pos);
		$("#scrollBarCenter").animate({scrollTop: temp - 210})
		// alert(mao.offset().top)
		firScolltop = temp;
		oldValue=value;
    }
	var veiwBtnVue = new Vue({
		el:'#viewBtn',
		data:{
			datas:'<button class="btn btn-info btn-xs" id="xiaZaiFuJian" v-on:click="showPdfClick()">预览</button>',
		},

	});
	var xzcfVue = new Vue({
		  el: '#zjpfJCVue',
		  data: {
			  scoringIndexList:scoringIndexList,
		  },
		  mounted: function(){
			  this.sumScore();
		  },
		  methods: {
			  showErrorMsg:function(obj){
				  $.ajax({
					  type:"post",
					  url:WEBPATH+'/zjpf/getOtherExpertScore.do',
					  data:{
						  fileId:$("#fileId").val(),
						  expertId:$("#expertId").val(),
						  handlType:$("#handlType").val(),
						  indexId:obj.indexid
					  },
					  success:function(data){  //成功
						  if (data.result=="success") {
							  swal("争议详情(对方专家评分情况)", "一票否决："+(data.data.voteDownValue==0?"否":"是")+" \n无需评查："+(data.data.inCheckValue==0?"否":"是")+"\n得分："+data.data.resultScore+"\n扣分理由:"+(!data.data.comment?"":data.data.comment), "info");
						  }else{
							  swal("信息", data.message, "error");
						  }
					  }
				  });
			  },
			  sumScore:function(){//判断是否有总分，没有则相加求分
				  	var resulScore = this.scoringIndexList.expertfinalscore;
				  	var score1 = this.scoringIndexList.expertHandlIndexScoreList;
				  	if(resulScore==""|| resulScore==null){
				  		if(score1 != null && score1.length>0){
				  			var num = 0;
					  		for(var i=0;i<score1.length;i++){
					  			if(!isNaN(parseFloat(score1[i].resultScore))){
					  				num = (Math.round((num + parseFloat(score1[i].resultScore))*100))/100;
					  		 	}
					  		}
					  		
							this.scoringIndexList.inputScore = num.toFixed(2);
							this.scoringIndexList.expertfinalscore = num.toFixed(2);
				  		}
				  	}
				  	
			  },
			  jcpjCheckBoxClick:function(){
					if($("#yxdxAnLiTuiJianReviews1").css("display")=='none' ){//如果show是隐藏的
						$("#yxdxAnLiTuiJianReviews1").css("display","block"); //  yxdxanlituijianreviews show的display属性设置为block（显示）
						//$("#yxdxanlituijian").val("1");
						xzcfVue.scoringIndexList.yxdxanlituijian="1";
						
						//禁止反面推荐
						$("#ajpjYxdxanlituijian").attr("disabled","disabled");
						$("#ajpjYxdxAnLiTuiJianReviews1").css("display", "none");
						xzcfVue.scoringIndexList.ajpjYxdxanlituijianreviews="";
						xzcfVue.scoringIndexList.ajpjYxdxanlituijian="0";
                        $('input:checkbox[name=jcaj]').attr('checked',false);
                        $('#jcpj').val("")
						//禁止不推荐为典型案例
						$("#noTuiJian").attr("disabled","disabled");
						xzcfVue.scoringIndexList.noTuiJian="0";
					}else{//如果show是显示的
						xzcfVue.scoringIndexList.yxdxanlituijianreviews="";
						xzcfVue.scoringIndexList.yxdxanlituijian="0";
						$("#yxdxAnLiTuiJianReviews1").css("display","none");//show的display属性设置为none（隐藏）
						
						//放开反面推荐
						$("#ajpjYxdxanlituijian").removeAttr("disabled");
						//放开不推荐为典型案例
						$("#noTuiJian").removeAttr("disabled");
					}
			  },
			  ajpjCheckBoxClick:function(){
					if($("#ajpjYxdxAnLiTuiJianReviews1").css("display")=='none' ){//如果show是隐藏的
						$("#ajpjYxdxAnLiTuiJianReviews1").css("display","block"); //  yxdxanlituijianreviews show的display属性设置为block（显示）
						//$("#ajpjYxdxanlituijian").val("1");
						xzcfVue.scoringIndexList.ajpjYxdxanlituijian="1";
						
						//禁止优秀推荐
						$("#yxdxanlituijian").attr("disabled","disabled");
						$("#yxdxAnLiTuiJianReviews1").css("display", "none");
						xzcfVue.scoringIndexList.yxdxanlituijianreviews="";
						xzcfVue.scoringIndexList.yxdxanlituijian="0";
						$('input:checkbox[name=yxaj]').attr('checked',false);
						$('#yxaj').val("")
						//禁止不推荐为典型案例
						$("#noTuiJian").attr("disabled","disabled");
						xzcfVue.scoringIndexList.noTuiJian="0";
					}else{//如果show是显示的
						xzcfVue.scoringIndexList.ajpjYxdxanlituijianreviews="";
						xzcfVue.scoringIndexList.ajpjYxdxanlituijian="0";
						$("#ajpjYxdxAnLiTuiJianReviews1").css("display","none");//show的display属性设置为none（隐藏）
						
						//放开优秀推荐
						$("#yxdxanlituijian").removeAttr("disabled");
						//放开不推荐为典型案例
						$("#noTuiJian").removeAttr("disabled");
					}
			  },
			  noCheckBoxClick:function (){
				  var isChecked = $("#noTuiJian").prop('checked');
				  if(isChecked){
					  xzcfVue.scoringIndexList.noTuiJian="1";
					  //禁止优秀推荐
					  $("#yxdxanlituijian").attr("disabled","disabled");
					  $("#yxdxAnLiTuiJianReviews1").css("display", "none");
					  xzcfVue.scoringIndexList.yxdxanlituijianreviews="";
					  xzcfVue.scoringIndexList.yxdxanlituijian="0";
					  $('input:checkbox[name=yxaj]').attr('checked',false);
					  $('#yxaj').val("")

					  //禁止反面推荐
					  $("#ajpjYxdxanlituijian").attr("disabled","disabled");
					  $("#ajpjYxdxAnLiTuiJianReviews1").css("display", "none");
					  xzcfVue.scoringIndexList.ajpjYxdxanlituijianreviews="";
					  xzcfVue.scoringIndexList.ajpjYxdxanlituijian="0";
					  $('input:checkbox[name=jcaj]').attr('checked',false);
					  $('#jcpj').val("")
				  }else {

					  xzcfVue.scoringIndexList.noTuiJian="0";
					  //放开反面推荐
					  $("#ajpjYxdxanlituijian").removeAttr("disabled");
					  //放开优秀推荐
					  $("#yxdxanlituijian").removeAttr("disabled");
				  }

			  },
			  changeCheckBoxCli:function(index,status){
				  //获取选中未选中的状态 stauts =1一票否决 status=2无需评查
				  if(status=='1'){
					  //一票否决
					var flag=  xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].voteDownValue;
					
					if(xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].inCheckValue == 1){
						xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].inCheckValue = 0;
						var data = xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList;
				  		if(data!=null &&  data.length>0){
				  			for(var i=0;i<data.length;i++){
								xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[i].validatorFlag= false;
				  			}
				  		}else{
				  			xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].validatorFlag= false;
				  		}
				  		xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].score='';
				  		xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore='';
				  		$(checkitem).removeAttr("disabled");
				  		
					}
					 
				  	var checkitem =".checkitem"+index;
				  	if(flag){//选择一票否决
				  		var data = xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList;
				  		if(data!=null &&  data.length>0){
				  			for(var i=0;i<data.length;i++){
				  				data[i].score='';
				  				$("#"+index+"expertItem"+i).removeClass("has-error");	
								$("#"+index+"expertItem"+i).removeClass("has-success");
								xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[i].validatorMessage="";
				  			}
				  		}else{
				  			xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].score='';
				  			xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore='';
				  			$("#expertIndex"+index).removeClass("has-error");	
							$("#expertIndex"+index).removeClass("has-success");	
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].validatorMessage="";
				  		}
			  				xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore=0;
			  				xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].calculScore='';
				  		/* 	if(xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].isAdd ==1){
			  				//是否为加分项
			  			}else{
			  				//减分项
			  				//xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore=xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].indexscore;
			  			} */
				  		$(checkitem).attr("disabled","disabled");
			  			
			  			
				  		var text = $("#comment"+index).val().replace(/\s+/g,"");
				  		if(text==null || text==''){
				  			$("#comment"+index).removeClass("textarea-success");	
					  		$("#comment"+index).addClass("textarea-error");	  
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].validatorFlag= false;
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].validatorMessage="选择一票否决，此项扣分理由必填";
				  		}
				  		
				  		//恢复小指标无需评查
				  		/* var wxpcScoreLittle = 0;
				  		var data = xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList;
				  		if(data!=null &&  data.length>0){
				  			for(var i=0;i<data.length;i++){
				  				data[i].score='';
						  		$("#"+index+"expertItem"+i).removeClass("has-error");	
								$("#"+index+"expertItem"+i).removeClass("has-success");
								xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[i].validatorMessage="";
								
								if(data[i].inCheckValue==1){//如果小指标勾选了无需评查，给它还原
									data[i].inCheckValue = 0;
									wxpcScoreLittle += data[i].itemscore;
								}
				  			}
				  		} */
				  		
				  	}else{//取消一票否决
				  		xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].score='';
				  		xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore='';
				  		var data = xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList;
				  		if(data!=null &&  data.length>0){
				  			for(var i=0;i<data.length;i++){
								xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[i].validatorFlag= false;
				  			}
				  		}else{
				  			xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].validatorFlag= false;
				  		}
				  		$(checkitem).removeAttr("disabled");
				  		
				  		/**/
				  		var text = $("#comment"+index).val();
				  		if(text==null || text==''){
				  			$("#comment"+index).removeClass("textarea-error");	
					  		$("#comment"+index).addClass("textarea-success");	  
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].validatorFlag= false;
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].validatorMessage="";
				  		}
				  		
				  	}
				  	 //总分技算
				  	 var num = 0;
					 var childNum =0;
			  		 var score1 = xzcfVue.scoringIndexList.expertHandlIndexScoreList;
					 if(score1 != null){
			  			for(var i=0;i<score1.length;i++){
		  					  if(!isNaN(parseFloat(score1[i].resultScore))){
		  						num = (Math.round((num + parseFloat(score1[i].resultScore))*100))/100; 
		  					  }
		  		 		}
				  	 }
					xzcfVue.scoringIndexList.inputScore =num;
					xzcfVue.scoringIndexList.expertfinalscore=num;
				  }
			  },
			  wuxupingcha:function(index){//大指标无需评查
				  var flag=  xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].inCheckValue;//是否勾选
				  	var checkitem =".checkitem"+index;
				    var wxpcScoreLittle = 0;
				  	if(flag){//勾选
				  		
				  		if(xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].voteDownValue == 1){
				  			xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].voteDownValue = 0;
				  			xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].score='';
					  		xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore='';
					  		var data = xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList;
					  		if(data!=null &&  data.length>0){
					  			for(var i=0;i<data.length;i++){
									xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[i].validatorFlag= false;
					  			}
					  		}else{
					  			xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].validatorFlag= false;
					  		}
						}
				  		
				  		var data = xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList;
				  		if(data!=null &&  data.length>0){
				  			for(var i=0;i<data.length;i++){
				  				data[i].score='';
						  		$("#"+index+"expertItem"+i).removeClass("has-error");	
								$("#"+index+"expertItem"+i).removeClass("has-success");
								xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[i].validatorMessage="";
								
								if(data[i].inCheckValue){//如果小指标勾选了无需评查，给它还原
									data[i].inCheckValue = '';
									wxpcScoreLittle += data[i].itemscore;
								}
				  			}
				  		}
			  			xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore = '';
			  			xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].calculScore = '';
			  			//xzcfVue.scoringIndexList.wxpjScore += xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].indexscore - wxpcScoreLittle;
			  			
				  		$(checkitem).attr("disabled","disabled");
				  	}else{//取消
				  		var data = xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList;
				  		if(data!=null &&  data.length>0){
				  			for(var i=0;i<data.length;i++){
								xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[i].validatorFlag= false;
				  			}
				  		}else{
				  			xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].validatorFlag= false;
				  		}
				  		xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].score='';
				  		xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore='';
				  		$(checkitem).removeAttr("disabled");
				  		
				  		//xzcfVue.scoringIndexList.wxpjScore -= xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].indexscore;
				  	}
				  	
				 	//计算最终总得分
		  			var num = 0;
					var childNum =0;
			  		var score1 = xzcfVue.scoringIndexList.expertHandlIndexScoreList;
					if(score1 != null){
				  		for(var i=0;i<score1.length;i++){
				  			if(!isNaN(parseFloat(score1[i].resultScore))){
				  				num = (Math.round((num + parseFloat(score1[i].resultScore))*100))/100;
				  		 	}
				  		}
				  	}
					xzcfVue.scoringIndexList.inputScore = num.toFixed(2);
					xzcfVue.scoringIndexList.expertfinalscore = num.toFixed(2);
			  },
			  wuxupingcha2:function(index,index1){//小指标无需评查
					//无需评查
					//console.log(xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[index1]);
					  var flag=  xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[index1].inCheckValue;//是否勾选
					  
					  	var checkitem ="input[name='"+index+"item"+index1+"']";
					  	if(flag){//勾选
					  		xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[index1].inCheckValue = 1;
					  		var data = xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[index1];
					  		$("#"+index+"expertItem"+index1).removeClass("has-error");	
							$("#"+index+"expertItem"+index1).removeClass("has-success");
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[index1].validatorFlag=1;	
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[index1].validatorMessage="";	
				  			$(checkitem).attr("disabled","disabled");
					  		
				  			xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[index1].score = '';
				  			//xzcfVue.scoringIndexList.wxpjScore += xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[index1].itemscore;
				  			
				  			var indexFinalScore;
				  			var a = xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList;
				  			var indexSum;//输入的分数和
				  			var wxpcSum=0;//勾选的无需评查分值和
				  			for(var i=0;i<a.length;i++){//计算大指标下所有小指标输入的分数和，以及勾选的无需评查分值和
						  		if(!isNaN(parseFloat(a[i].score))){
						  			if(isNaN(parseFloat(indexSum))){
						  				indexSum = (Math.round((parseFloat(a[i].score))*100))/100;
						  			}else{
						  				indexSum = (Math.round((indexSum + parseFloat(a[i].score))*100))/100;
						  			}
						  		}
				  				if(a[i].inCheckValue){
				  					wxpcSum += a[i].itemscore;
				  				}
						  	}
				  			var indexScore = xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].indexscore;
				  			if (indexSum!=null){
				  				//计算指标最终得分
					  			indexFinalScore = indexSum*(indexScore/(indexScore-wxpcSum));
					  			xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore = indexSum;
					  			xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].calculScore = indexFinalScore.toFixed(2);
					  			
					  			var num = 0;
								var childNum =0;
						  		var score1 = xzcfVue.scoringIndexList.expertHandlIndexScoreList;
								if(score1 != null){
							  		for(var i=0;i<score1.length;i++){
							  			if(!isNaN(parseFloat(score1[i].resultScore))){
							  				num = (Math.round((num + parseFloat(score1[i].resultScore))*100))/100; 
							  		 	}
							  		}
							  	}
								xzcfVue.scoringIndexList.inputScore = num.toFixed(2); 
								xzcfVue.scoringIndexList.expertfinalscore = num.toFixed(2);
				  			}else{
				  				xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore = '';
					  			xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].calculScore = '';
				  			}
					  	}else{//取消
					  		xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[index1].inCheckValue = 0;
					  		var data = xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList;
					  		if(data!=null &&  data.length>0){
					  			for(var i=0;i<data.length;i++){
									xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[index1].validatorFlag= false;
					  			}
					  		}else{
					  			xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].validatorFlag= false;
					  		}
					  		xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].score='';
					  		xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore='';
					  		$(checkitem).removeAttr("disabled");
					  		
					  		//xzcfVue.scoringIndexList.wxpjScore -= xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[index1].itemscore;
					  		
					  		var indexFinalScore;
				  			var a = xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList;
				  			var indexSum;//输入的分数和
				  			var wxpcSum=0;//勾选的无需评查分值和
				  			for(var i=0;i<a.length;i++){
						  		if(!isNaN(parseFloat(a[i].score))){
					  				if(isNaN(parseFloat(indexSum))){
					  					indexSum = (Math.round((parseFloat(a[i].score))*100))/100;
					  				}else{
					  					indexSum = (Math.round((indexSum + parseFloat(a[i].score))*100))/100;
					  				}
						  		}
						  		if(a[i].inCheckValue){
				  					wxpcSum += a[i].itemscore;
				  				}
						  	}
				  			var indexScore = xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].indexscore;
				  			
				  			if (indexSum!=null){//別的小指标打分了
				  				//计算大指标最终得分
				  				//计算该大指标下无需评查的小指标总分
				  				
			  					indexFinalScore = indexSum*(indexScore/(indexScore-wxpcSum))
					  			
					  			xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore = indexSum;
				  				if(wxpcSum==0){
				  					xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].calculScore = '';
				  				}else{
				  					xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].calculScore = indexFinalScore;
				  				}
					  			
					  			
					  			//计算最终总得分
					  			var num = 0;
								var childNum =0;
						  		var score1 = xzcfVue.scoringIndexList.expertHandlIndexScoreList;
								if(score1 != null){
							  		for(var i=0;i<score1.length;i++){
							  			if(!isNaN(parseFloat(score1[i].resultScore))){
							  				num = (Math.round((num + parseFloat(score1[i].resultScore))*100))/100;
							  		 	}
							  		}
							  	}
								xzcfVue.scoringIndexList.inputScore = num.toFixed(2);
								xzcfVue.scoringIndexList.expertfinalscore = num.toFixed(2);
				  				
				  			}else{//别的小指标没打分
				  				
				  			}
					  	}
				  },
			  showPdfClick:function(){
				//pdf cha kan
				  var options = {
							remote:WEBPATH+'/jcpf/showFileModal.do?fastDFSUrl="'+xzcfVue.scoringIndexList.fileurl+'"'
						  };
						$('#view').modal(options);
			  },
			  getView:function(){
				  //判断url是不是pdf
				  if(xzcfVue.scoringIndexList.fileurl != null ){
					  var url = (xzcfVue.scoringIndexList.fileurl).split(".");
					  if(url[url.length-1]=='pdf' || url[url.length-1]=='PDF'){
						  $("#viewBtn").show();
					  }else{
						  $("#viewBtn").hide();
					  }
				  }
			  },
			  updateItem:function (index,itemIndex,itemScore){
			    	var num = 0;
				  	var childNum =0;
				  	var value = xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].score;
				  	xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].score=value;
				  	 if(value != '' && value != null){
			  		 	if(parseFloat(value) <= parseFloat(itemScore) && parseFloat(value)>=0){
			  		 		if(value.substring(value.length-1,value.length) =="."){
			  		 			$("#"+index+"expertItem"+itemIndex).removeClass("has-success");	
								$("#"+index+"expertItem"+itemIndex).addClass("has-error");
								$("#"+index+"scoreInput"+itemIndex).focus();
								xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorFlag= false;
								xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorMessage="分值项不能为空，且在0-"+itemScore+"分之间，最多两位小数！";
						   		return false;
			  		 		}
				  		 	var re = /^-?\d+\.?\d{0,2}$/;
				    		if( re.test(value) ){   // 返回true
				    			$("#"+index+"expertItem"+itemIndex).removeClass("has-error");	
								$("#"+index+"expertItem"+itemIndex).addClass("has-success");	 
								xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorFlag= true;
								xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorMessage="";
				    		}else{
				    			$("#"+index+"expertItem"+itemIndex).removeClass("has-success");	
								$("#"+index+"expertItem"+itemIndex).addClass("has-error");
								$("#"+index+"scoreInput"+itemIndex).focus();
								xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorFlag= false;
								xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorMessage="分值项不能为空，且在0-"+itemScore+"分之间，最多两位小数！";
						   		return false;
					    	}
				    		
				    		var indexSum;//输入的分数和
				  			var wxpcSum=0;//勾选的无需评查分值和
				  			var a = xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList;
				  			for(var i=0;i<a.length;i++){//计算大指标下所有小指标输入的分数和，以及勾选的无需评查分值和
						  		if(!isNaN(parseFloat(a[i].score))){
						  			if(isNaN(parseFloat(indexSum))){
						  				indexSum = (Math.round((parseFloat(a[i].score))*100))/100;
						  			}else{
						  				indexSum = (Math.round((indexSum + parseFloat(a[i].score))*100))/100;
						  			}
						  		}
				  				if(a[i].inCheckValue){
				  					wxpcSum += a[i].itemscore;
				  				}
						  	}
				  			
							var indexScore = xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].indexscore;
				  			
				  			if (indexSum!=null){
				  				//计算指标最终得分
				  				var isAdd = xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].isAdd ==1;
				  				if(wxpcSum==0){
						  			if(isAdd){
										//加分项
						  				xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore = indexSum;
							  			xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].calculScore = '';
								  	}else{
										//减分项
									   	xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore = (parseFloat(indexScore) - parseFloat(indexSum))< 0 ? 0 : parseFloat(indexScore) - parseFloat(indexSum);
							  			xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].calculScore = '';
								  	}
				  				}else{
				  					if(isAdd){
										//加分项
					  					indexFinalScore = indexSum*(indexScore/(indexScore-wxpcSum));
							  			xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore = indexSum;
							  			xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].calculScore = indexFinalScore.toFixed(2);
								  	}else{
										//减分项
								  		indexSum = indexScore - wxpcSum - indexSum;
					  					indexFinalScore = indexSum*(indexScore/(indexScore-wxpcSum));
							  			xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].resultScore = indexSum;
							  			xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].calculScore = indexFinalScore.toFixed(2);
								  	}
				  				}
				  				
					  			
					  			
					  			var num = 0;
								var childNum =0;
						  		var score1 = xzcfVue.scoringIndexList.expertHandlIndexScoreList;
								if(score1 != null){
							  		for(var i=0;i<score1.length;i++){
							  			if(!isNaN(parseFloat(score1[i].resultScore))){
							  				num = (Math.round((num + parseFloat(score1[i].resultScore))*100))/100; 
							  		 	}
							  		}
							  	}
								xzcfVue.scoringIndexList.inputScore = num.toFixed(2);
								xzcfVue.scoringIndexList.expertfinalscore = num.toFixed(2);
				  			}
				  		}else{
				  			$("#"+index+"expertItem"+itemIndex).removeClass("has-success");
							$("#"+index+"expertItem"+itemIndex).addClass("has-error");
							$("#"+index+"scoreInput"+itemIndex).focus();
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorFlag= false;
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorMessage="分值项不能为空，且在0-"+itemScore+"分之间，最多两位小数！";
					   		return false;
				  		}
				  	}else{
				  		$("#"+index+"expertItem"+itemIndex).removeClass("has-success");
						$("#"+index+"expertItem"+itemIndex).addClass("has-error");
						$("#"+index+"scoreInput"+itemIndex).focus();
						xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorFlag= false;
						xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].expertHandlItemScoreList[itemIndex].validatorMessage="分值项不能为空，且在0-"+itemScore+"分之间，最多两位小数！";
				   		return false;
				  	}
					  		 	
			  },
			  updateCommon:function (index){
				    var flag=  xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].voteDownValue;
				    var text = $("#comment"+index).val().replace(/\s+/g,"");
				    
				    if(text!=null && text!=''){
				    	var length = text.length;
					    if(length>1000){
				  			$("#comment"+index).removeClass("textarea-success");	
					  		$("#comment"+index).addClass("textarea-error");	  
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].validatorFlag= 3;
							xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].validatorMessage="长度不能大于1000";
			  			}
					    
				 		$("#comment"+index).removeClass("textarea-error");	
				  		$("#comment"+index).addClass("textarea-success");	  
						xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].validatorFlag= true;
						xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].validatorMessage="";
					}else{
			 			$("#comment"+index).removeClass("textarea-success");	
				  		$("#comment"+index).addClass("textarea-error");	  
						xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].validatorFlag= false;
						xzcfVue.scoringIndexList.expertHandlIndexScoreList[index].validatorMessage="此项评审依据必填";
					}
			  },
			  //保存
			  saveSubmit:function(id, status){// 0.暂存  1.保存
			  		if(status == "1") {
						var  isYXchecked=$("#yxdxanlituijian").prop('checked');
						var  isJCchecked=$("#ajpjYxdxanlituijian").prop('checked');
						var  isNOchecked=$("#noTuiJian").prop('checked');
						if(isYXchecked == false && isJCchecked == false && isNOchecked == false ){
							swal({title: "提示",text: "请选择案件状态！",type:"error"});
							return false;
						}
						//优秀典型案例
						var yxdxanlituijianreviews = xzcfVue.scoringIndexList.yxdxanlituijianreviews;
						if(xzcfVue.scoringIndexList.yxdxanlituijian=="1"){
							var falg=false;
							var checkArray = $("input[name='yxaj']");
							$.each(checkArray, function (j, checkbox) {
								//获取复选框的value属性
								var checkValue = $(checkbox).val();
								if(checkValue.indexOf("1000") != -1) { //其他选项时输入框为必填
									if (yxdxanlituijianreviews == null || yxdxanlituijianreviews == '') {
										falg = true;
									}
								}
							});
							if(falg){
								swal({title: "提示", text: "请输入案卷评价", type: "info"});
								return false;
							}
						}else{
							if(yxdxanlituijianreviews.length>2000){
								swal({title: "提示",text: "案卷评价长度不能超过2000个字符",type:"info"});
								return false;
							}
						}
						//优秀典型案例选中 下边的子项必填一个
						if(xzcfVue.scoringIndexList.yxdxanlituijian=="1"){
							var checked=$("input[name='yxaj']:checked");//获取复选框被选中状态
							if(!(checked&&checked.length>0)){//判断复选框是否被选中
								swal({title: "提示",text: "优秀典型案例子项不能为空",type:"error"});
								return false;
							}
						}


						//反面典型案例
						var flags = false;
						var flagss = false;
						var ajpjYxdxanlituijianreviews = xzcfVue.scoringIndexList.ajpjYxdxanlituijianreviews;
						if(xzcfVue.scoringIndexList.ajpjYxdxanlituijian=="1"){
							var checked_array=new Array();
							$('input[name="jcaj"]:checked').each(function(){
								checked_array.push($(this).val())
								if(checked_array.indexOf("1000") != -1){
									if(ajpjYxdxanlituijianreviews==null || ajpjYxdxanlituijianreviews==""){
										flags = true;
									}else{
										if(ajpjYxdxanlituijianreviews.length>2000){
											flags = true;
										}
									}
								}
							});
							if (flags){
								swal({title: "提示",text: "较差案例推荐不能为空",type:"error"});
								return false;
							}
							if (flagss){
								swal({title: "提示",text: "案卷评价长度不能超过2000个字符",type:"info"});
								return false;
							}
						}
						//反面典型案例选中 下边的子项必填一个
						if(xzcfVue.scoringIndexList.ajpjYxdxanlituijian=="1"){
							var checked=$("input[name='jcaj']:checked");//获取复选框被选中状态
							if(!(checked&&checked.length>0)){//判断复选框是否被选中
								swal({title: "提示",text: "较差案例推荐子项不能为空",type:"error"});
								return false;
							}
						}
					}

				  
					if(xzcfVue.scoringIndexList!=null){
						var data = xzcfVue.scoringIndexList.expertHandlIndexScoreList;
						for(var i=0;i<data.length;i++){
							if((data[i].voteDownValue !=0 || data[i].voteDownValue != 1) && data[i].voteDownValue){//把true和false置为1,0
								data[i].voteDownValue =1;
							}else{
								data[i].voteDownValue=0;
							}
							if((data[i].inCheckValue!= 0 || data[i].inCheckValue!=1) && data[i].inCheckValue){//把true和false置为1,0
								data[i].inCheckValue =1;
							}else{
								data[i].inCheckValue=0;
							}
							
							if(status == "1") {
								var flag=  data[i].voteDownValue;
							    var text = $("#comment"+i).val().replace(/\s+/g,"");
							    if(flag) {
							    	if(text == null || text == ''){
							  			$("#comment"+i).removeClass("textarea-success");	
								  		$("#comment"+i).addClass("textarea-error");	
								  		$("#comment"+i).focus();
								  		data[i].validatorFlag= false;
								  		data[i].validatorMessage="此项评审依据必填";
								  		return false;
						  			}
							    }
							}
							
							/* if(data[i].validatorFlag== 3){//验证指标评审依据长度
							  	return false;
							} */
							
							if(data[i].inCheckValue==1 || data[i].voteDownValue == 1){//如果勾选了则不需要验证
								continue;
							}
							
							if(data[i].expertHandlItemScoreList ==null&&(data[i].validatorFlag == null || !data[i].validatorFlag) ){
								//此种判断应该不会走，一级指标分无需操作
								xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].validatorFlag= false;
								xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].validatorMessage="分值项不能为空，且在0-"+xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].indexscore+"分之间，最多两位小数！";
								$("#expertIndex"+i).removeClass("has-success");	
								$("#expertIndex"+i).addClass("has-error");
								$("#expertIndex"+i).focus();
								return false;
							}else{//判断二级指标验证是否通过
								if(data[i].expertHandlItemScoreList != null){
									for(var j=0;j<data[i].expertHandlItemScoreList.length;j++){
										var standScore = data[i].expertHandlItemScoreList[j].itemscore//标准分
										var score=data[i].expertHandlItemScoreList[j].score;//输入分
										
										/*
										*无论暂存或是保存，都需要验证的是格式
										*/
										if(score != null && score!=""){
											var re = /^-?\d+\.?\d{0,2}$/;
											if(score>standScore || !(re.test(score))){
												xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].validatorFlag= false;
												xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].validatorMessage="分值项不能为空，且在0-"+xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].itemscore+"分之间！";
												$("#expertItem"+xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].temItemId+""+i).removeClass("has-success");	
												$("#expertItem"+xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].temItemId+""+i).addClass("has-error");
												$("#"+i+"scoreInput"+j).focus();
												return false;
											}
										}
										
										if(status == "1") {
											if((score == null || score==="")){
												xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].validatorFlag= false;
												xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].validatorMessage="分值项不能为空，且在0-"+xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].itemscore+"分之间！";
												$("#expertItem"+xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].temItemId+""+i).removeClass("has-success");	
												$("#expertItem"+xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].temItemId+""+i).addClass("has-error");
												$("#"+i+"scoreInput"+j).focus();
												return false;
											}
										}
										
											/* if(data[i].expertHandlItemScoreList[j].validatorFlag == null || !data[i].expertHandlItemScoreList[j].validatorFlag){
												xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].validatorFlag= false;
												xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].validatorMessage="分值项不能为空，且在0-"+xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].itemscore+"分之间，最多两位小数！";
												$("#expertItem"+xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].temItemId+""+i).removeClass("has-success");	
												$("#expertItem"+xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList[j].temItemId+""+i).addClass("has-error");
												return false;
										} */
									}
								}
							}
						}
						//loding('submitBtn', '信息保存');
						
						//无需评审公式换算：最终得分 = 打分合计 *（满分/（满分-无需评审总分））
						/* if(xzcfVue.scoringIndexList.wxpjScore!=0){
							var expertfinalscore = xzcfVue.scoringIndexList.inputScore*(100/(100-xzcfVue.scoringIndexList.wxpjScore));
							xzcfVue.scoringIndexList.expertfinalscore = expertfinalscore.toFixed(2);
						}else{ */
						xzcfVue.scoringIndexList.expertfinalscore = xzcfVue.scoringIndexList.inputScore;
						//存入优秀案卷code
						var code_array=new Array();
						$('input[name="yxaj"]:checked').each(function(){
							code_array.push($(this).val())
						});
						xzcfVue.scoringIndexList.ajpjYxdxanlituijianCode = code_array.join(',')

						//存入较差案卷code
						var jcajArr=new Array();
						$('input[name="jcaj"]:checked').each(function(){
							jcajArr.push($(this).val())
						});
						xzcfVue.scoringIndexList.jcajtuijianCode = jcajArr.join(',')
						//}
						$("[id^='submitBtn']").attr("disabled", "disabled");
						$("[id^='submitBtn']").text("提交中...");
				  		
						$.ajax({
						    type:"post",
						    url:WEBPATH+'/zjpf/saveJCExpertScore.do',
						    data:{
						    	id:id,
						    	status:status,
						    	scoringIndex:JSON.stringify(xzcfVue.scoringIndexList)},//注意数据用{}
						    success:function(data){  //成功
						    	if(data.result=="success"){
						    	  swal({title: "保存成功",text: "",type:"success"});
						    	  if(status == "1"){
						    		  business.addMainContentParserHtml('zjpf/zjpfList.do','pageNum=${pageNum}');
						    	  }else{
						    		  business.addMainContentParserHtml('zjpf/zjpfScore.do?id='+id,'pageNum=${pageNum}');
						    	  }
						          
						          return false;
						    	}else{
						    		if(data.code=="007"){
										swal("提示", "正在保存中……，请稍等片刻", "info");
									}else if(data.code=="000"){//登录信息失效
										swal({ 
									        title: "提示",  
									        text: data.message,  
									        type: "error", 
									        confirmButtonText: "确定", 
									        confirmButtonColor: "#ec6c62" 
									    }, function() { 
									        window.location.href=WEBPATH+"/index.do";
									    });
									}
						    		$("[id^='submitBtn']").removeAttr("disabled");
						    		$("#submitBtnTemp").text("信息暂存");
									$("#submitBtn").text("信息保存");
							    	return false; 
						    	}
						      }
						});
					}
			  }

		 }
	});
	
	//优秀推荐
	//var yxtj = xzcfVue.scoringIndexList.yxdxanlituijian;
	if(xzcfVue.scoringIndexList.yxdxanlituijian=="1"){
		//$('#yxdxanlituijian').attr('checked', true)
		
	    $("[name = yxdxanlituijian]:checkbox").attr("checked", true);
		$("#yxdxAnLiTuiJianReviews1").css("display","block");

        //优秀案卷选项回显
		var checkArray = $("input[name='yxaj']");
		var codes = xzcfVue.scoringIndexList.ajpjYxdxanlituijianCode;
		if(codes != null && codes != "" ){
			var myArray=codes.split(",");
			for (var i = 0; i < myArray.length; i++) {
				//获取所有复选框对象的value属性，然后，用checkArray[i]和他们匹配，如果有，则说明他应被选中
				$.each(checkArray, function (j, checkbox) {
					//获取复选框的value属性
					var checkValue=$(checkbox).val();
					console.log(j+"----"+checkValue)
					if (myArray[i] == checkValue) {
						$(checkbox).attr("checked", true);
					}
				})
			}
		}



		//禁止反面推荐
		$("#ajpjYxdxanlituijian").attr("disabled","disabled");
		$("#ajpjYxdxAnLiTuiJianReviews1").css("display", "none");
		//禁止不推荐为典型案例
		$("#noTuiJian").attr("checked", false);
	}
	
	//反面推荐
	//var ajpja = xzcfVue.scoringIndexList.ajpjYxdxanlituijian;
	if(xzcfVue.scoringIndexList.ajpjYxdxanlituijian=="1"){
		//$('#ajpjYxdxanlituijian').attr('checked', true)
		
	    $("[name = ajpjYxdxanlituijian]:checkbox").attr("checked", true);
		$("#ajpjYxdxAnLiTuiJianReviews1").css("display","block");

		//较差案卷选项回显
		var checkArray = $("input[name='jcaj']");
		var codes = xzcfVue.scoringIndexList.jcajtuijianCode;
		if(codes != null && codes != "" ){
			var myArray=codes.split(",");
			for (var i = 0; i < myArray.length; i++) {
				//获取所有复选框对象的value属性，然后，用checkArray[i]和他们匹配，如果有，则说明他应被选中
				$.each(checkArray, function (j, checkbox) {
					//获取复选框的value属性
					var checkValue=$(checkbox).val();
					console.log(j+"----"+checkValue)
					if (myArray[i] == checkValue) {
						$(checkbox).attr("checked", true);
					}
				})
			}
		}
		
		//禁止优秀推荐
		$("#yxdxanlituijian").attr("disabled","disabled");
		$("#yxdxAnLiTuiJianReviews1").css("display", "none");
		//禁止不推荐为典型案例
		$("#noTuiJian").attr("checked", false);
	}

	// 不推荐为典型案例
	if(xzcfVue.scoringIndexList.noTuiJian=="1"){
		$("#noTuiJian").attr("checked", true);
		//禁止优秀推荐
		$("#yxdxanlituijian").attr("disabled","disabled");
		$("#yxdxAnLiTuiJianReviews1").css("display", "none");
		//禁止较差推荐
		$("#ajpjYxdxanlituijian").attr("disabled","disabled");
		$("#ajpjYxdxAnLiTuiJianReviews1").css("display", "none");
	}
	
	/* 页面初始化加载设置隐藏 input输入框，当用户选择一票否决和无须评查  */
	if(xzcfVue.scoringIndexList != null && xzcfVue.scoringIndexList.expertHandlIndexScoreList != null ){
			for( var i=0;i< xzcfVue.scoringIndexList.expertHandlIndexScoreList.length;i++){
				if(xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].inCheckValue ==1 ||xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].voteDownValue ==1){
					var checkitem =".checkitem"+i;
					$(checkitem).attr("disabled","disabled");
					/* if(xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].inCheckValue ==1){
						//无须评查
						xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].resultScore=xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].indexscore;
					} */
					if(xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].voteDownValue ==1){
						//无须评查
						xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].resultScore=0;
					}
				}
				var items = xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList;
				for (var j = 0; j < items.length; j++) {
					//console.log(items[j].inCheckValue);
					if(items[j].inCheckValue){
						var checkitem ="input[name='"+i+"item"+j+"']";
				  		$(checkitem).attr("disabled","disabled");	
					}
				}
			}
	}
	xzcfVue.getView();
	var statusTemp = eval('${status}');
	if(statusTemp =='1'){
		$("#xiaZaiFuJian").removeAttr("disabled");
		$("#xiaZai").removeAttr("disabled");
	}
	var scoredStateTemp= eval('${expertHandlFileList.scoredstate}');
	 
	if(scoredStateTemp == '1'){
		 if(statusTemp !='1'){
			 $("#xiaZaiFuJian").removeAttr("disabled");
			 $("#xiaZai").removeAttr("disabled");
	 	}
	}
	 
	function keepTwoDecimal(num) {
		var result = parseFloat(num);
		if (isNaN(result)) {
		 	alert('传递参数错误，请检查！');
			return false;
		}
		result = Math.round(num * 100) / 100;
		return result;
	}
		//四舍五入保留2位小数（不够位数，则用0替补）
	function keepTwoDecimalFull(num) {
		var result = parseFloat(num);
		if (isNaN(result)) {
			alert('传递参数错误，请检查！');
			return false;
		}
		result = Math.round(num * 100) / 100;
		var s_x = result.toString();
		var pos_decimal = s_x.indexOf('.');
		if (pos_decimal < 0) {
			pos_decimal = s_x.length;
			s_x += '.';
		}
		while (s_x.length <= pos_decimal + 2) {
		 	s_x += '0';
		}
		return s_x;
	}

</script>
	<!-- 附件预览  -->
	<div class="modal fade" id="view" tabindex="-1" role="dialog"
		aria-labelledby="myModalLabel" aria-hidden="true">
		<div class="modal-dialog modal-lg">
			<div class="modal-content">
			</div>
		</div>
	</div>
</body>
</html>
