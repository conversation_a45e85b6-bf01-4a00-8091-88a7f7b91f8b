<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
	<meta http-equiv="pragma" content="no-cache">
	<meta http-equiv="cache-control" content="no-cache">
	<meta http-equiv="expires" content="0"> 
<head>
<style>
/*
PDFObject appends the classname "pdfobject-container" to the target element.
This enables you to style the element differently depending on whether the embed was successful.
In this example, a successful embed will result in a large box.
A failed embed will not have dimensions specified, so you don't see an oddly large empty box.
*/
.pdfobject-container {
	width: 100%;
	height:580px;
	margin: 2em 0;
}
.textarea-success {
	border-top:green 1px solid;
	border-bottom:green 1px solid; 
	border-left:green 1px solid;
    	border-right:green 1px solid;
}
.textarea-error {
	border-top:red 1px solid;
	border-bottom:red 1px solid; 
	border-left:red 1px solid;
    	border-right:red 1px solid;
}
.center{	position: absolute;left: 0px;top: 237px; width: 98%;margin: 0 auto;bottom: 35px;right: 5px;
	}
</style>
<script type="text/javascript">
	$(".left_menu").hide();

/* 	判断评分的状态scoredState为1 */
$(document).ready(function(){
	//移除打分div只读状态
	$(".inputDisabled").removeAttr("readonly");
	
	$("#xiaZaiFuJian").removeAttr("disabled");
	$("#xiaZai").removeAttr("disabled");
	//案卷处罚书 设为不可用
	$("#fileCodeInput").attr("disabled","disabled");	
	//案卷评价设为可用
	$("#yxdxanlituijian").attr("disabled",false)
	//显示保存按钮
	$("#submitBtn").show();
	//扣分理由
	$("textarea").removeAttr("disabled");
	$("input:checkbox").removeAttr("disabled");
});


function trim(str) {
	return str.replace(/(^\s+)|(\s+$)/g, "");
}

//下载文件
function xiaZaiAnJuan(){
	//var fileCode = $("#fileCode").html();
	var fileid  = $("#fileId").val();
 	$.ajax({
	    type:"post",
	    url:WEBPATH+'/jcpf/jcpfExistFileUrl.do',
	    data:{fileId:fileid },           //注意数据用{}
	    success:function(data){  //成功
		 if("yes" == data){
				window.location.href="${pageContext.request.contextPath}/jcpf/jcpfAnJuandown.do?fileid="+fileid;
			    return false;
	         }else if("no" == data){
	            	  swal( "操作失败","该案卷不存在!", "error");
	            	  return false;
			}else if("suffixerror" ==data){
				  swal( "操作失败","该案卷上传数据格式有问题!", "error");
            	  return false;
			}
	         }
	});
 }
 
function loding(btn,itext){
	document.getElementById(btn).innerHTML = "加载.."
	document.getElementById(btn).disabled = "disabled"
    setTimeout(function () {
      document.getElementById(btn).innerHTML = itext;
      document.getElementById(btn).removeAttribute("disabled");
    },3000);
}	

$(document).ready(function(){
	//保存数据方法
	var pageNum = $("#pageNum").val();
	
	$(":input[type='checkbox']").attr("disabled","disabled");
	$(":input[type='text'], textarea").attr("disabled","disabled");
});
</script>

</head>
<div id='zjpfJCVue'>
<div   class="center_weizhi">当前位置：：专家评分 - 专家评审案卷  - ${filetypeName }</div>
<div class="center"  id="scrollBarCenter">
<div class="center_list" id="anjuanxinxi">
	<input id="expertFileId"  type="hidden" value="${expertFileId }" >
	<input id="fileId" type="hidden" :value="scoringIndexList.fileid">
	<button    class="btn btn-primary"  style="font-size:16px;width:150px; margin-top:5px;margin-right: 10px;float: right;" onclick="macroMgr.onLevelOneMenuClick(null, 'zjpf.do')">返回</button>

	<div class="dingwei">案卷或材料：<span style="color:#009CCD;font-size:14px; font-weight:400;" id ="fileCode">{{scoringIndexList.filecode }} </span>
	<c:if test ="${sessionScope.sa_session.sysStatus == '5' }">	
		<a v-bind:href="scoringIndexList.downUrl" target="_Blank">
			<button class="btn btn-success btn-xs" disabled="true" id="xiaZai" data-toggle="modal" data-target="#myModal">下载</button>
		</a>	
		<span id="viewBtn" v-html='datas' ></span>

	</c:if>
	</div>
	<select id="indexName" onchange="change(this.value)"></select>
 	   <table class="table table-striped table-bordered table-condensed" id="tableOuter" >
      <thead>
           <tr>
             <td width="50" height="30" bgcolor="#efefef">序号</td>
             <td width="100" bgcolor="#efefef">分指标</td>
             <td width="90" bgcolor="#efefef">分指标分值</td>
             <td width="200" bgcolor="#efefef">指标说明</td>
             <td bgcolor="#efefef"> <span style="float: left;">判断标准</span><span style="float: right;margin-right: 120px;">分值</span></td>
             <td bgcolor="#efefef" width="70">一票否决</td>
             <td bgcolor="#efefef" width="70">无需评查</td>
             <td bgcolor="#efefef" width="80">得分</td>
             <td width="15%" bgcolor="#efefef">扣分理由</td>
           </tr>
         </thead>
         	<tbody  class="form-group">
          		 <tr v-for="(scoringIndex, index) in scoringIndexList.expertHandlIndexScoreList"  :class="scoringIndex.className">
		             <td height="30" align="center" style="vertical-align:middle;">{{index+1}}</td>
		             <td style="vertical-align:middle;"  :class="scoringIndex.className">{{scoringIndex.indexname}}</td>
		             <td style="vertical-align:middle;">{{scoringIndex.indexscore}}</td>
		             <td style="vertical-align:middle; width:200px;">{{scoringIndex.indexdesc}}</td>
		             <td v-if="scoringIndex.expertHandlItemScoreList != null && scoringIndex.expertHandlItemScoreList.length !=0 "  style="padding:0;">
                        <table width="100%" border="0" cellspacing="0" cellpadding="0" style="padding:0;">
                          <tr  v-for="(scoringItem, index1) in scoringIndex.expertHandlItemScoreList"  :class="scoringIndex.className">
                          	<td style="border-bottom:solid 1px #ddd; border-right:solid 1px #ddd;vertical-align:middle;">
                                 <div style="vertical-align:middle; margin:0 5px 5px 0;">
		            			{{scoringItem.itemname}}<!-- （{{scoringItem.itemscore}}分） -->
		            			</div>
		            		<span style="color:red" v-if="scoringItem.isInCheck==1 && scoringIndex.inCheckValue!=1 && scoringIndex.voteDownValue!=1">
		            			&nbsp;&nbsp;是否无需评查：
                                			<input type="checkbox" id="" v-model="scoringItem.inCheckValue"
					              		  @change="wuxupingcha2(index,index1)" 
					              		  style="width:16px; height:16px;  display: table-cell;vertical-align: middle; text-align: center; ">
                                		</span>	
                            </td>                      
                            <td style="border-bottom:solid 1px #ddd; width:155px;vertical-align:middle;">
                                 <div  :id="index+'expertItem'+index1" style="margin:0 0 5px 0; float:left;">
	                                 <div v-if="scoringIndex.isAdd==1" style="float:left; padding:5px 2px 7px 3px; color:red; font-size:20px;">＋</div>
	                                 <div v-if="scoringIndex.isAdd!=1" style="float:left; padding:0 0 3px 0; margin:0; color:red; font-size:26px;">－</div>
	                                 <div style="float:right; padding-top:4px;">
	                                 	<input @input="updateItem(index,index1,scoringItem.itemscore,scoringItem.temItemId)"  
	                                       v-model="scoringItem.score" type="text" :class="'checkitem'+index" :name = "index+'item'+index1"
	                                      class="form-control inputDisabled"   placeholder="请输入初评得分" style="width:125px;">
	                                 </div>
                                 	<div style="color:red; font-size: 12px;">{{scoringItem.validatorMessage}}
                                 	</div>
                                 </div>
                            </td>                          
                          </tr>
                        </table>
		             </td>
		            <td v-else >
		             <table width="100%" border="0" cellspacing="0" cellpadding="0">
		             	<tr>
		             		<td style="border-bottom:solid 1px #f7f7f7;">
                                 <div style="vertical-align:middle; margin:0 5px 5px 0;">
		            				{{scoringIndex.indexdesc}}
		            			</div>
                            </td>    
                            <td style="border-bottom:solid 1px #f7f7f7;">
                            	<div :id="'expertIndex'+index"  class="form-group" style="width: 150px;float: right;">
	                            	<div v-if="scoringIndex.isAdd==1" style="float:left; padding:5px 2px 7px 3px; color:red; font-size:20px;">+</div>
		                            <div v-if="scoringIndex.isAdd!=1" style="float:left; padding:0 0 3px 0; margin:0; color:red; font-size:26px;">-</div>
				                		<input   type="text"    v-model="scoringIndex.score" 
				                		 @input="updateIndex(index,scoringIndex.indexscore)" :class="'checkitem'+index"
				               	  		 class="form-control inputDisabled"  placeholder="请输入初评得分">
			         			  	<div style="color:red;font-size: 5px;">{{scoringIndex.validatorMessage}}</div>
			         			 </div>
                            </td>
		             	</tr>
		             </table>
		           </td>
		           <!-- 一票否决 -->
		            <td style="vertical-align:middle; text-align:center;">
		              <span v-if="scoringIndex.isVoteDown ==1" >
		              		 <input type="checkbox" :id="'checkbox'+index" 
		              		  @change="changeCheckBoxCli(index,'1')" v-model="scoringIndex.voteDownValue"
		              		  style="width:16px; height:16px; display: table-cell;vertical-align: middle; text-align: center;">
               				 <label :for="'checkbox'+index" class="checkbox-blue" checked></label>
		              </span>
		           </td>
		           <!-- 无需评查 -->
		            <td style="vertical-align:middle;text-align:center;">
		            <span v-if="scoringIndex.isInCheck ==1">
		              		 <input type="checkbox" id="checkbox1" v-model="scoringIndex.inCheckValue"
		              		  @change="wuxupingcha(index)" 
		              		  style="width:16px; height:16px;  display: table-cell;vertical-align: middle; text-align: center; ">
               				 <label for="checkbox1" class="checkbox-blue" checked></label>
		              </span>
		           </td>
		           <td style="vertical-align:middle;text-align:center;">
		           	  <input type="text" id="111" v-if="scoringIndex.calculScore==null || scoringIndex.calculScore==''" disabled="disabled" v-model="scoringIndex.resultScore" style="background-color:transparent; width:80px; border:0px;vertical-align:middle;text-align:center;">
		           	  <!-- <input type="text" id="222" v-if="scoringIndex.calculScore!=null && scoringIndex.calculScore!=''" disabled="disabled" v-model="scoringIndex.calculScore" style="background-color:transparent; width:80px; border:0px;vertical-align:middle;text-align:center;"> -->
		           </td>
		           
		           <!-- 扣分理由 -->
	             <td >																   
	           	   <textarea maxlength="1000" @input="updateCommon(index)" :id="'comment'+index" :rows="scoringIndex.expertHandlItemScoreList.length" disabled class="form-control" v-model="scoringIndex.comment" placeholder="请输入扣分理由" style="width:100%;">
				   
				   </textarea>
				   <div style="color:red; font-size: 12px;">{{scoringIndex.validatorMessage}}</div>
	             </td>
		           
		    	</tr>
		           <tr>
		           		<td></td>
		           		<td align="center"><strong>合计</strong></td>
		           		<td>103</td>
		           		<td></td>
		           		<td><strong></strong></td>
		           		<td></td>
		           		<td align="center"><strong>最终得分</strong></td>
		           		<td style="vertical-align:middle;text-align:center;">{{scoringIndexList.expertfinalscore}}</td>
		           		<td></td>
		           </tr>
				 <tr>
					 <td height="30" colspan="9">
						 <textarea maxlength="2000" value="${expertHandlFileList.problemRemark}" rows="5" class="form-control" id="problem_remark" placeholder="疑难（争议）问题描述">${expertHandlFileList.problemRemark}</textarea>
					 </td>
				 </tr>
	           <tr>
           		<td height="30" colspan="9" style="line-height: 3.1">
					<div class="col-sm-12" style="padding-left:30px">
           		    <input id="yxdxanlituijian" name = "yxdxanlituijian" type="checkbox"  v-on:click="jcpjCheckBoxClick()" style="margin-right: 10px">优秀案卷推荐
            		 </div>
					<div class="col-sm-12" style="display: none;padding-left:30px" id ="yxdxAnLiTuiJianReviews1"  >
						 <table>
							 <c:forEach items="${tcDictionaryList}" var="item" varStatus="status">
								 <tr>
									 <input  type="checkbox" name="yxaj" value="${item.code}" >&nbsp;${item.name} &nbsp; &nbsp;
									 <c:if test="${status.count%4==0}"><br></c:if>
								 </tr>
							 </c:forEach>
						 </table>
            			<textarea disabled maxlength="2000" v-model="scoringIndexList.yxdxanlituijianreviews" rows="5" class="form-control" id="jcpj" placeholder="请输入专家评语，长度不能超过2000个字符"></textarea>
            		 </div>
            	</td>
	           </tr>
	           <tr>
           		<td height="30" colspan="9">
					<div class="col-sm-12" style="padding-left:30px">
           		    <input id="ajpjYxdxanlituijian" name = "ajpjYxdxanlituijian" type="checkbox"  v-on:click="ajpjCheckBoxClick()" style="margin-right: 10px">较差案卷推荐
				    </div>
					<div class="col-sm-12" style="display: none;padding-left:30px" id ="ajpjYxdxAnLiTuiJianReviews1"  >
						 <table>
							 <c:forEach items="${jcajDictionaryList}" var="item" varStatus="status">
								 <tr>
									 <input  type="checkbox" name="jcaj" value="${item.code}" >&nbsp;${item.name}&nbsp; &nbsp;
									 <c:if test="${status.count%4==0}"><br/></c:if>
								 </tr>
							 </c:forEach>
						 </table>
            			<textarea disabled maxlength="2000" v-model="scoringIndexList.ajpjYxdxanlituijianreviews" rows="5" class="form-control" id="jcpj" placeholder="请输入专家评语，长度不能超过2000个字符"></textarea>
            		 </div>
            	</td>
	           </tr>
				 <tr>
					 <td height="30" colspan="9">
						 <div class="col-sm-12" style="padding-left:30px">
						 <input id="noTuiJian" name ="noTuiJian" type="checkbox"  v-on:click="noCheckBoxClick()" style="margin-right: 10px">不推荐为典型案例
						 </div>
					 </td>
				 </tr>
          	</tbody>
       </table>
       	<a href="#">
       	</a>
         <div><input id ="pageNum" type="hidden" value="${pageNum }"></div>
 		<div class="submit">
 			<c:choose> 
 			 <c:when test = "${sessionScope.sa_session.sysStatus != 5}"> 
			 </c:when> 
			 <c:otherwise> 
			<!-- 	<a href="#"><button type="button" class="btn btn-primary" id="saveSubmit" name="signup" value="Sign up" style="font-size:16px;width:150px; margin-top:5px; display:none">信息保存</button></a>
			 --></c:otherwise> 
			</c:choose></div>
 	</div>
</div>
</div>
<script >
	var firScolltop=0;
	var oldValue='';
	var scoringIndexList =null;
	var expertFileId = $("#expertFileId").val();
	if(expertFileId != null){
		$.ajax({
			cache : true,
			type : "GET",
			async : false,
			//api/test/detail/behaviour/74
			url: WEBPATH+"/zjpf/getIndexList.do",
			data:{
				id:expertFileId,
			},
			error : function(request) {
				swal("错误!","请求异常！", "error");
			},
			success : function(data) {
		  		if(data.result =='success'){
                    if(data.result =='success'){
                        scoringIndexList = data.data;
                        // scoringIndexList.problemRemark = problemRemark;
                        for(var i=0;i<data.data.expertHandlIndexScoreList.length;i++){
                            data.data.expertHandlIndexScoreList[i].className = 'class'+ data.data.expertHandlIndexScoreList[i].id;
                            var index = data.data.expertHandlIndexScoreList[i];
                            //先创建好select里面的option元素
                            var option=document.createElement("option");
                            //转换DOM对象为JQ对象,好用JQ里面提供的方法 给option的value赋值
                            $(option).val('class' + index.id);
                            $(option).text(index.indexname);
                            $("#indexName").append(option);
                        }
                        console.log(scoringIndexList);
                    }
		  		}
			} 
		});			
	}
    function change(value) {
		var mao;
		var pos;
		var poshigh;
		mao = $("." + value);
		pos = mao.offset().top;
		poshigh = mao.height();
		//alert(poshigh)
		var temp=pos+firScolltop;
		if(oldValue!=''){
			// alert("old:"+$("." + oldValue).offset().top);
			temp=temp-$("." + oldValue).offset().top;
		}
		// alert(pos);
		$("#scrollBarCenter").animate({scrollTop: temp - 210})
		// alert(mao.offset().top)
		firScolltop = temp;
		oldValue=value;
    }
	var veiwBtnVue = new Vue({
		el:'#viewBtn',
		data:{
			datas:'<button class="btn btn-info btn-xs" id="xiaZaiFuJian" disabled="true" v-on:click="showPdfClick()">预览</button>',
		},
		
	});
	var xzcfVue = new Vue({
		  el: '#zjpfJCVue',
		  data: {
			  scoringIndexList:scoringIndexList,
		  },
		 mounted: function(){
		  },
		  methods: {
		 }
	});
	
	//优秀推荐
	var yxtj = xzcfVue.scoringIndexList.yxdxanlituijian;
	if(xzcfVue.scoringIndexList.yxdxanlituijian=="1"){
		$('#yxdxanlituijian').attr('checked', true)

	    $("[name = yxdxanlituijian]:checkbox").attr("checked", true);
		$("#yxdxAnLiTuiJianReviews1").css("display","block");

         //优秀案卷选项回显
		var checkArray = $("input[name='yxaj']");
		var codes = xzcfVue.scoringIndexList.ajpjYxdxanlituijianCode;
		if(codes != null && codes != "" ){
			var myArray=codes.split(",");
			for (var i = 0; i < myArray.length; i++) {
				//获取所有复选框对象的value属性，然后，用checkArray[i]和他们匹配，如果有，则说明他应被选中
				$.each(checkArray, function (j, checkbox) {
					//获取复选框的value属性
					var checkValue=$(checkbox).val();
					console.log(j+"----"+checkValue)
					if (myArray[i] == checkValue) {
						$(checkbox).attr("checked", true);
					}
				})
			}
		}



		//禁止反面推荐
		$("#ajpjYxdxanlituijian").attr("disabled","disabled");
		$("#ajpjYxdxAnLiTuiJianReviews1").css("display", "none");
		//禁止不推荐为典型案例
		$("#noTuiJian").attr("disabled","disabled");
	}
	
	//反面推荐
	var ajpja = xzcfVue.scoringIndexList.ajpjYxdxanlituijian;
	if(xzcfVue.scoringIndexList.ajpjYxdxanlituijian=="1"){
		//$('#ajpjYxdxanlituijian').attr('checked', true)
		
	    $("[name = ajpjYxdxanlituijian]:checkbox").attr("checked", true);
		$("#ajpjYxdxAnLiTuiJianReviews1").css("display","block");

		//较差案卷选项回显
		var checkArray = $("input[name='jcaj']");
		var codes = xzcfVue.scoringIndexList.jcajtuijianCode;
		if(codes != null && codes != "" ){
			var myArray=codes.split(",");
			for (var i = 0; i < myArray.length; i++) {
				//获取所有复选框对象的value属性，然后，用checkArray[i]和他们匹配，如果有，则说明他应被选中
				$.each(checkArray, function (j, checkbox) {
					//获取复选框的value属性
					var checkValue=$(checkbox).val();
					console.log(j+"----"+checkValue)
					if (myArray[i] == checkValue) {
						$(checkbox).attr("checked", true);
					}
				})
			}
		}
		
		//禁止优秀推荐
		$("#yxdxanlituijian").attr("disabled","disabled");
		$("#yxdxAnLiTuiJianReviews1").css("display", "none");
		//禁止不推荐为典型案例
		$("#noTuiJian").attr("disabled","disabled");
	}

	// 不推荐为典型案例 回显
	if(xzcfVue.scoringIndexList.noTuiJian=="1"){
		$("#noTuiJian").attr("checked", true);
		//禁止优秀推荐
		$("#yxdxanlituijian").attr("disabled","disabled");
		$("#yxdxAnLiTuiJianReviews1").css("display", "none");
		//禁止较差推荐
		$("#ajpjYxdxanlituijian").attr("disabled","disabled");
		$("#ajpjYxdxAnLiTuiJianReviews1").css("display", "none");
	}
	
	/* 页面初始化加载设置隐藏 input输入框，当用户选择一票否决和无须评查  */
	if(xzcfVue.scoringIndexList != null && xzcfVue.scoringIndexList.expertHandlIndexScoreList != null ){
			for( var i=0;i< xzcfVue.scoringIndexList.expertHandlIndexScoreList.length;i++){
				if(xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].inCheckValue ==1 ||xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].voteDownValue ==1){
					var checkitem =".checkitem"+i;
					$(checkitem).attr("disabled","disabled");
					
					if(xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].voteDownValue ==1){
						//无须评查
						xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].resultScore=0;
					}
				}
				var items = xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].expertHandlItemScoreList;
				for (var j = 0; j < items.length; j++) {
					//console.log(items[j].inCheckValue);
					if(items[j].inCheckValue){
						var checkitem ="input[name='"+i+"item"+j+"']";
				  		$(checkitem).attr("disabled","disabled");	
					}
				}
			}
	}
	 
	function keepTwoDecimal(num) {
		var result = parseFloat(num);
		if (isNaN(result)) {
		 	alert('传递参数错误，请检查！');
			return false;
		}
		result = Math.round(num * 100) / 100;
		return result;
	}
		//四舍五入保留2位小数（不够位数，则用0替补）
	function keepTwoDecimalFull(num) {
		var result = parseFloat(num);
		if (isNaN(result)) {
			alert('传递参数错误，请检查！');
			return false;
		}
		result = Math.round(num * 100) / 100;
		var s_x = result.toString();
		var pos_decimal = s_x.indexOf('.');
		if (pos_decimal < 0) {
			pos_decimal = s_x.length;
			s_x += '.';
		}
		while (s_x.length <= pos_decimal + 2) {
		 	s_x += '0';
		}
		return s_x;
	}

</script>
	<!-- 附件预览  -->
	<div class="modal fade" id="view" tabindex="-1" role="dialog"
		aria-labelledby="myModalLabel" aria-hidden="true">
		<div class="modal-dialog modal-lg">
			<div class="modal-content">
			</div>
		</div>
	</div>
</body>
</html>
