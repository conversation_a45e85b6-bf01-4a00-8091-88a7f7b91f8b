<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<link rel="stylesheet" href="${webpath }/static/css/caiji.css">
<script type="text/javascript" src="${webpath }/static/js/echarts.min.js"></script>
<script type="text/javascript" src="${webpath }/static/js/caiji.js"></script>

<script type="text/javascript">

$(document).ready(function(){
	var areacode="${tarea.code }";//行政区划编码
	var arealevel="${tarea.arealevel}";
	if(typeof(areacode)!="undefined"){
		if(typeof(arealevel)!="undefined"&&arealevel!="3"){
			if(areacode.substring(2,4)!="00"){//行政区已具体到市级加载县级
				business.cascadedful(areacode.substring(0,4)+"00",'country',areacode);
			}
		}
	};
	
});

var getAreaCode = function(){
	var areaCode='';
	
	var pro = $("#province").val();
	var city = $("#city").val();
	var country = $("#country").val();
	
	if(country!=""){
		areaCode = country;
	}else{
		if(city!=""){
			areaCode = city;
		}else{
			if(pro!=""){
				areaCode = pro;
			}
		}
	}
	return areaCode;
}


    var vm = new Vue({
        el:'#vueT',
        data:{
        	numObj:{}
        },
        mounted:function(){
        	this.initData();
        },
        methods:{
        	initData:function(){
        		var that =this;
        		var areacode = getAreaCode();
        		if(areacode==""||areacode.substring(4,6)=="00"){
        			$("#main_5,#main_6,#main_7").show();
        		}else{
        			$("#main_5,#main_6,#main_7").hide();
        		}
        		
        		$.ajax({
        			cache : true,
        			type : "post",
        			url: WEBPATH+"/totalSbNum.do",
        			data:{
        				areaCode:areacode
        			},
        			error : function(request) {
        				swal({title:"错误!",text:"请求异常！",type:"error",confirmButtonColor: "#d9534f"});
        			},
        			success : function(data) {
        			  	if(data.result =='success'){
        			  		//console.log(data.data);
        			  		//概要统计
        			  		that.numObj=data.data.baseNum;
        			  		//各类型案件对比图
        			  		var data1 = data.data.typeNum;
        			  		fileType_bar(data1);
        			  		//集体、个人案卷上报情况
        			  		var data2 = data.data.useNum;
        			  		useType_pie(data2);
        			  		
        			  		if(areacode==""||areacode.substring(4,6)=="00"){
        			  			//上报案件数量==区划
            			  		var data3 = data.data.ajslArea;
            			  		fileNumArea(data3);
            			  		//参与集体数量==区划
            			  		var data4 = data.data.jtArea;
            			  		cjJtNumArea(data4);
            			  		//参与个人数量==区划
            			  		var data5 = data.data.grArea;
            			  		cjGrNumArea(data5);
        	        		}
        			  	}else{
        			  		swal({title:"提示",text:"统计数据异常！",type:"error",confirmButtonColor: "#d9534f"});
        			  	}
        			} 
        		});
        		
        		
        	}
        }
    });

</script>
<div id="vueT">
		<div class="w" style="box-shadow: 0px 0px 20px #d9e3ed;">
			<div class="form">
            	<div class="choose clearfix" style="margin-top: 22px;">
	               	<select class="form-control" id="province" name="province_code" onchange="business.cascadedful($('#province').val(),'city');">
	               		<c:if test="${fn:length(provinceList)>1 }">
	               		<option value="">-省(市)-</option>
	               		</c:if>
                    	<c:forEach items="${provinceList }" var="prolist">
	               			<option value="${prolist.code }" <c:if test="${fn:substring(areacode,0,2).concat('0000')==prolist.code }">selected</c:if> >${prolist.name }</option>
	               		</c:forEach>
                    </select>
             	</div>
             	<div class="choose clearfix" style="margin-top: 22px;">
             		<select class="form-control" id="city" name="city_code" onchange="business.cascadedful($('#city').val(),'country');">
                        <c:if test="${tarea.arealevel!=3 && tarea.arealevel!=2}">
                        	<option value="">-地(市)-</option>
                        </c:if>
                        <c:forEach items="${cityList }" var="citylist">
		               		<option value="${citylist.code }" <c:if test="${fn:substring(areacode,0,4).concat('00')==citylist.code }">selected</c:if> >${citylist.name }</option>
		               	</c:forEach>
                	</select>
	            </div>
	            <div class="choose clearfix" style="margin-top: 22px;">
	            	<select class="form-control" id="country" name="country_code">
                    	<c:if test="${tarea.arealevel!=3}">
                        	<option value="">-区(县)-</option>
                    	</c:if>
                    	<c:forEach var="county" items="${countyList }" >
	                 		<option value="${county.code }" <c:if test="${fn:substring(areacode,0,6)==county.code }">selected</c:if> >${county.name }</option>
	                	</c:forEach>
					</select>
		            <input type="hidden" name="belongAreaId"/>
             	</div>
             	<div class="choose clearfix">
             		<button class="btn btn-danger"  v-on:click="initData" style="margin-top: 12px; background: #3F91C2; border-color: #3F91C2">查询</button>
             	</div>
        	</div>

        </div>
        <%--<hr color="white">--%>
        <div class="content" id="content" style="margin-top: 45px">
            <h3>概况</h3>
            <div class="fn_l fl" style="margin-left: 200px;">
                <div class="content_tab">
                    <p>上报案卷总数</p>
                    <h6>{{numObj.sbAjNum}}</h6>
                </div>
                <div class="content_tab">
                    <p>参与集体</p>
                    <h6>{{numObj.cyJtNum}}</h6>
                </div>
                <div class="content_tab">
                    <p>参与个人</p>
                    <h6>{{numObj.cyGrNum}}</h6>
                </div>
                <%--<div class="content_tab">
                    <p>重点排污单位数量</p>
                    <h6> {{numObj.zdPwNum}} </h6>
                </div>--%>
            </div>
            <!--<div class="fn_l fr">
                <div class="content_tab">
                    <p>重点排污单位数量</p>
                    <h6> {{numObj.zdPwNum}} </h6>
                </div>
            </div>-->
        </div>
</div>

        <div class="echarts">
            <h3>案卷上报情况</h3>
            <!-- <h6 class="echarts_title">案卷上报情况</h6> -->
            <div class="col-sm-6">
            	<div class="main_1" style="width:100%; height:350px; float: left; box-shadow: 0px 0px 20px #d9e3ed;
"></div>
            </div>            
            <div class="col-sm-6">
            	<div class="main_2" style="width:100%; height:350px; float:right; box-shadow: 0px 0px 20px #d9e3ed;
"></div>
            </div>
            
        </div>

        <div class="echarts">
            <div id="main_5" class="main_5" style="width:1200px; height:350px; float: left; box-shadow: 0px 0px 20px #d9e3ed;
">
            </div>
        </div>
        <div class="echarts">
            <div  id="main_6" class="main_6" style="width:1200px; height:350px; float: left; box-shadow: 0px 0px 20px #d9e3ed;
">
            </div>
        </div>
        <div class="echarts">
            <div  id="main_7" class="main_7" style="width:1200px; height:350px; float: left; margin-bottom: 50px; box-shadow: 0px 0px 20px #d9e3ed;
">
            </div>
        </div>

