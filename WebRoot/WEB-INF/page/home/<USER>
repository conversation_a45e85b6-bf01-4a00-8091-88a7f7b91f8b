<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>

<style>
html {
    overflow: auto;
}
.frame_bottom {
    position: fixed;
}
</style>
<link rel="stylesheet" href="${webpath }/static/css/caiji.css">
<script type="text/javascript">
	var WEBPATH = '${webpath}';
</script>
<script type="text/javascript">

$(function() {
		
	//如果密码没有重置
	$.ajax({
		type: "post",
		url: WEBPATH+"/sys/checkpwd.do",
		async:false,
		success: function (data) {
			if(data.result=="success"){
				var IfUpdatePwd = data.data;
				if(IfUpdatePwd==0){
	       			var options2 = {remote:encodeURI(WEBPATH+'/sys/changePWD.do')};
	       			$('#toResetPwd2').modal(options2);
	       			$('#toResetPwd2').modal({backdrop: 'static', keyboard: false});
	       		}  
			}else{
	        	swal("服务异常，数据请求失败!", "", "warning");
			}
		},
		error: function(){
			swal("网络异常，请求数据失败!", "", "error");
		}
	});
});

//默认显示第1个统计
$(document).ready(function(){
	//绑定主菜单单击方法，设置样式
	$(".clearfix li").bind("click",function(){
			$(".clearfix li a").removeClass("active");
			$(this).children(0).addClass("active");
	});
	//触发点击事件
	$(".clearfix li a:first").click();
	
});
</script>


<div class="center_home_weizhi">当前位置：系统首页</div>
	<div class="w">
        <div class="item" style="margin-top: 60px;">
            <ul class="clearfix">
            	<c:if test="${sessionScope.sa_session.userTypeCode=='1' || sessionScope.sa_session.userTypeCode=='4' }">
                <li><a href="javascript:void(0);" onclick="macroMgr.onLevelTwoMenuClick(null, 'xxcj_index.do')">信息采集</a></li>
                </c:if>
               <%-- <c:if test="${(sessionScope.sa_session.userTypeCode=='1' && sessionScope.sa_session.arealevel=='1')||sessionScope.sa_session.userTypeCode=='4'}">
                <li><a href="javascript:void(0);" onclick="macroMgr.onLevelTwoMenuClick(null, 'snpb_index.do')">省内评比</a></li>
                </c:if>--%>
                <c:if test="${sessionScope.sa_session.userTypeCode=='2' || sessionScope.sa_session.userTypeCode=='4' }">
                <li><a href="javascript:void(0);" onclick="macroMgr.onLevelTwoMenuClick(null, 'ajpf_index.do')">案卷评分</a></li>
                </c:if>
                <!--<li><a href="javascript:void(0);" onclick="macroMgr.onLevelTwoMenuClick(null, 'hzjd_index.do')">汇总阶段</a></li>
                <li><a href="javascript:void(0);" onclick="macroMgr.onLevelTwoMenuClick(null, 'jggs_index.do')">结果公示</a></li>
                <li><a href="javascript:void(0);" onclick="macroMgr.onLevelTwoMenuClick(null, 'total_index.do')">统计分析</a></li> -->
            </ul>
        </div>
	    <div id="main_content" style="padding: 50px 0;">
	    
	
		</div>
    </div>
	
	<!-- 强制修改密码模态框 -->
	<div class="modal fade" id="toResetPwd2"tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true" data-backdrop="static" data-keyboard="false">
		<div class="modal-dialog">
			<div class="modal-content">

			</div>
		</div>
    </div> 

