<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<link rel="stylesheet" href="${webpath }/static/css/caiji.css">
<script type="text/javascript" src="${webpath }/static/js/echarts.min.js"></script>
<script type="text/javascript" src="${webpath }/static/js/ajpf.js"></script>
<script type="text/javascript">
	//统计互动
    var vm = new Vue({
        el:'#vueT',
        data:{
        	numObj:{}
        },
        mounted:function(){
        	this.initData();
        },
        methods:{
        	initData:function(){
        		$.ajax({
        			cache : true,
        			type : "post",
        			url: WEBPATH+"/fileSourceStatistics.do",
        			data:{
        				areaCode:"00000000"
        			},
        			error : function(request) {
        				swal({title:"错误!",text:"请求异常！",type:"error",confirmButtonColor: "#d9534f"});
        			},
        			success : function(data) {
        			  	if(data.result =='success'){
        			  		var curLoginUser = data.data.curLoginUser;
        			  		var userTypeCode = curLoginUser.userTypeCode;
        			  		var userLevel = curLoginUser.level;
        			  		if(userTypeCode == 4) {
        			  			$("#sysDivPie").css("display","block");
        			  			// 饼图1--参评案卷
            			  		var data1 = data.data.participateFiles;
            			  		cpaj_pie(data1);
            			  		// 饼图2--已评案卷
            			  		var data2 = data.data.evaluatedFiles;
            			  		ypaj_pie(data2);
            			  		// 饼图3--推送合议案卷
            			  		var data3 = data.data.pushCollegialFiles;
            			  		tshyaj_pie(data3);
        			  		}
        			  		if(userTypeCode == 2 && userLevel!=6) {
        			  			$("#expertDivPie").css("display","block");
        			  			// 饼图4--本账号案卷总数
            			  		var data4 = data.data.thisAccountFiles;
            			  		bzhajzs_pie(data4);
            			  		if(userLevel!=2){
            			  		// 饼图3--推送合议案卷
                			  		var data5 = data.data.thisAccountEvaluatedFiles;
                			  		bzhypaj_pie(data5);
            			  		}
    							
        			  		}
        			  		
        			  	}else{
        			  		swal({title:"提示",text:"统计数据异常！",type:"error",confirmButtonColor: "#d9534f"});
        			  	}
        			} 
        		});
        	}
        }
    });

</script>
<div id="sysDivPie" class="echarts clearfix" style="display:none;">
	<div class="main_1" style="width:400px; height:350px; float: left;"></div>
	<div class="main_2" style="width:390px; height:350px; float: left;"></div>
	<div class="main_3" style="width:400px; height:350px; float: left;"></div>
</div>
<div id="expertDivPie" class="echarts clearfix" style="display:none; background-color: #fff; padding: 20px 0 65px 0;">
	<div class="main_4" style="width:400px; height:350px; float: left; margin-left: 150px"></div>
        <%--<div class="main_4" style="width:500px; height:500px; float: left;"></div>--%>

        <div class="main_5" style="width:400px; height:350px; float: left; margin-left: 100px"></div>
</div>
