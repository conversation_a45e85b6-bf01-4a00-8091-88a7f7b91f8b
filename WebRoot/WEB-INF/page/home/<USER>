<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<link rel="stylesheet" href="${webpath }/static/css/caiji.css">
<script type="text/javascript" src="${webpath }/static/js/echarts.min.js"></script>
<script type="text/javascript" src="${webpath }/static/js/snpb.js"></script>
<script type="text/javascript">
	//初始化加载行政区划代码
	$(document).ready(function(){
		var areacode="${tarea.code }";//行政区划编码
		var arealevel="${tarea.arealevel}";
		if(typeof(areacode)!="undefined"){
			if(typeof(arealevel)!="undefined"&&arealevel!="3"){
				if(areacode.substring(2,4)!="00"){//行政区已具体到市级加载县级
					business.cascadedful(areacode.substring(0,4)+"0000",'country',areacode);
				}
			}
		};
		
	});

	//获取行政区划编码
	var getAreaCode = function(){
		var areaCode='';
		
		var pro = $("#province").val();
		/* var city = $("#city").val();
		var country = $("#country").val();
		
		if(country!=""){
			areaCode = country;
		}else{
			if(city!=""){
				areaCode = city;
			}else{ */
				if(pro!=""){
					areaCode = pro;
				}
		/* 	}
		} */
		return areaCode;
	}

	//统计互动
    var vm = new Vue({
        el:'#vueT',
        data:{
        	numObj:{},
        	tabData:{}
        },
        mounted:function(){
        	this.initData();
        },
        methods:{
        	initData:function(){
        		var that =this;
        		var areacode = getAreaCode();
        		if(areacode=="00000000"){
        			$("#main_3,#main_5,#main_6,#main_7").show();
        			$("#tableDiv,#geren").hide();
        		}else{
        			$("#main_3,#main_5,#main_6,#main_7").hide();
        			$("#tableDiv,#geren").show();
        		}
        		
        		$.ajax({
        			cache : true,
        			type : "post",
        			url: WEBPATH+"/totalTJNum.do",
        			data:{
        				areaCode:areacode
        			},
        			error : function(request) {
        				swal({title:"错误!",text:"请求异常！",type:"error",confirmButtonColor: "#d9534f"});
        			},
        			success : function(data) {
        			  	if(data.result =='success'){
        			  		console.log(data.data);
        			  		//概要统计
        			  		that.numObj=data.data.baseNumTJ;
        			  		
        			  		//饼图1--集体、个人案卷使用情况
        			  		var data1 = data.data.useNumTJ;
        			  		useType_pie(data1);
        			  		//饼图2--案卷上报方式统计情况
        			  		var data2 = data.data.reportNumTJ;
        			  		reportType_pie(data2);
        			  		
        			  		
        			  		//柱图--各类案卷数量情况
        			  		var data4 = data.data.typeNumTJ;
        			  		fileType_bar(data4);
        			  		
        			  		if(areacode=="00000000"){
        			  			
        			  			//饼图3--省内开展大练兵情况
            			  		var data3 = data.data.kzdlbNumTJ;
            			  		kzDlb_pie(data3);
        			  			
        			  			//上报案件数量==区划
            			  		var data5 = data.data.ajslArea;
            			  		fileNumArea(data5);
            			  		//参与集体数量==区划
            			  		var data6 = data.data.jtArea;
            			  		cjJtNumArea(data6);
            			  		//参与个人数量==区划
            			  		var data7 = data.data.grArea;
            			  		cjGrNumArea(data7);
        	        		}else{
        	        			//列表
        	        			that.tabData = data.data.tabData;
        	        			//推荐个人参与处理的案件数量
        	        			var data8 = data.data.grInCase;
        	        			tjGerenBar(data8);
        	        		}
        			  	}else{
        			  		swal({title:"提示",text:"统计数据异常！",type:"error",confirmButtonColor: "#d9534f"});
        			  	}
        			} 
        		});
        		
        		
        	}
        }
    });

</script>
	<div id="vueT">
		<div class="form">
			<input id="province" type="hidden" value="${tarea.code }"/>
			<%-- <div class="choose clearfix" style="margin-top: 22px;">
				<select class="form-control" id="province" name="province_code" ><!-- onchange="business.cascadedful($('#province').val(),'city');" -->
            		<c:if test="${fn:length(provinceList)>1 }">
            		<option value="">-省(市)-</option>
            		</c:if>
                	<c:forEach items="${provinceList }" var="prolist">
            			<option value="${prolist.code }" <c:if test="${fn:substring(areacode,0,2).concat('000000')==prolist.code }">selected</c:if> >${prolist.name }</option>
            		</c:forEach>
                </select>
			</div> --%>
			<%-- <div class="choose clearfix">
				<select class="form-control" id="city" name="city_code" onchange="business.cascadedful($('#city').val(),'country');">
                       <c:if test="${tarea.arealevel!=3 && tarea.arealevel!=2}">
                       	<option value="">-地(市)-</option>
                       </c:if>
                       <c:forEach items="${cityList }" var="citylist">
	               		<option value="${citylist.code }" <c:if test="${fn:substring(areacode,0,4).concat('0000')==citylist.code }">selected</c:if> >${citylist.name }</option>
	               	   </c:forEach>
               	</select>
			</div>
			<div class="choose clearfix">
				<select class="form-control" id="country" name="country_code">
                   	<c:if test="${tarea.arealevel!=3}">
                       	<option value="">-区(县)-</option>
                   	</c:if>
                   	<c:forEach var="county" items="${countyList }" >
                 		<option value="${county.code }" <c:if test="${fn:substring(areacode,0,6).concat('00')==county.code }">selected</c:if> >${county.name }</option>
                	</c:forEach>
				</select>
			</div> --%>
			<!-- <div class="choose clearfix">
           		<button class="btn btn-danger"  v-on:click="initData" style="margin-top: 12px;">查询</button>
           	</div> -->
		</div>
		<hr>
        <div class="content">
            <div class="fn_l fl">
				<div class="snpb_tab">
					<p>推荐集体数</p>
					<h6>{{numObj.tjjtNum}}</h6>
				</div>
				<div class="snpb_tab">
					<p>推荐个人数</p>
					<h6>{{numObj.tjgrNum}}</h6>
				</div>
				<div class="snpb_tab">
					<p>参评案卷数</p>
					<h6>{{numObj.cxwjNum}}</h6>
				</div>
				<div class="snpb_tab">
					<p>新推荐集体</p>
					<h6>{{numObj.jtNewNum}}</h6>
				</div>
				<div class="snpb_tab">
					<p>新推荐个人</p>
					<h6>{{numObj.grNewNum}}</h6>
				</div>
				<div class="snpb_tab">
					<p>参加过未入选集体</p>
					<h6>{{numObj.jtOldNum}}</h6>
				</div>
				<div class="snpb_tab">
					<p>参加过未入选个人</p>
					<h6>{{numObj.grOldNum}}</h6>
				</div>
			</div>
        </div>


      	<div class="row" style="margin-top: 20px;" id="tableDiv">
			<div class="col-lg-6">
				<div style="text-align: center;font-size: 20px;padding: 10px 0;"><img src="${webpath }/static/img/jiti.png" style="padding: 0 10px;">推荐集体</div>
				<table class="table table-striped table-bordered table-hover no-margin">
				  <thead>
					  <tr>
						  <th class="text-center" style="width:70px;">级别</th>
						  <th class="text-center">行政区</th>
						  <th class="text-center">机构名称</th>
					  </tr>
				  </thead>
				  <tbody>
					  <tr v-for="(item, index) in tabData.uListCit">
						  <td class="text-center" v-if="index==0" :rowspan="tabData.uListCit.length" style="vertical-align: middle">市级</td>
						  <td class="text-center">
						  {{item.areaName_pro}}
						  <img src="${webpath }/static/img/tishi-1.png" title="新参加" v-if="item.isJoinLast==null" style="height: 20px;position: absolute;padding: 0 10px;">
						  <img src="${webpath }/static/img/tishi-2.png" title="参与过未入选" v-if="item.isJoinLast=='1'" style="height: 20px;position: absolute;padding: 0 10px;">
						  </td>
						  <td class="text-center">{{item.companyName}}</td>
					  </tr>  
					  <tr v-for="(item, index) in tabData.uListCou">
						  <td class="text-center" v-if="index==0" :rowspan="tabData.uListCou.length" style="vertical-align: middle">县级</td>
						  <td class="text-center">
						  {{item.areaName_pro}}
						  <img src="${webpath }/static/img/tishi-1.png" title="新参加" v-if="item.isJoinLast==null" style="height: 20px;position: absolute;padding: 0 10px;">
						  <img src="${webpath }/static/img/tishi-2.png" title="参与过未入选" v-if="item.isJoinLast=='1'" style="height: 20px;position: absolute;padding: 0 10px;">
						  </td>
						  <td class="text-center">{{item.companyName}}</td>
					  </tr>  
				  </tbody>
			  </table>
			</div>
			<div class="col-lg-6">
				<div style="text-align: center;font-size: 20px;padding: 10px 0;"><img src="${webpath }/static/img/geren.png" style="padding: 0 10px;">推荐个人</div>
				<table class="table table-striped table-bordered table-hover no-margin">
				  <thead>
					  <tr>
						  <th class="text-center" style="width:70px;">级别</th>
						  <th class="text-center">行政区</th>
						  <th class="text-center">人员姓名</th>
					  </tr>
				  </thead>
				  <tbody>
				  	  <tr v-for="(item, index) in tabData.pListPro">
						  <td class="text-center" v-if="index==0" :rowspan="tabData.pListPro.length" style="vertical-align: middle">省级</td>
						  <td class="text-center">{{item.areaName_pro}}</td>
						  <td class="text-center">
						  {{item.name_pro}}
						  <img src="${webpath }/static/img/tishi-1.png" title="新参加" v-if="item.isJoinLast==null" style="height: 20px;position: absolute;padding: 0 10px;">
						  <img src="${webpath }/static/img/tishi-2.png" title="参与过未入选" v-if="item.isJoinLast=='1'" style="height: 20px;position: absolute;padding: 0 10px;">
						  </td>
					  </tr>  
					  <tr v-for="(item, index) in tabData.pListCit">
						  <td class="text-center" v-if="index==0" :rowspan="tabData.pListCit.length" style="vertical-align: middle">市级</td>
						  <td class="text-center">{{item.areaName_pro}}</td>
						  <td class="text-center">
						  {{item.name_pro}}
						  <img src="${webpath }/static/img/tishi-1.png" title="新参加" v-if="item.isJoinLast==null" style="height: 20px;position: absolute;padding: 0 10px;">
						  <img src="${webpath }/static/img/tishi-2.png" title="参与过未入选" v-if="item.isJoinLast=='1'" style="height: 20px;position: absolute;padding: 0 10px;">
						  </td>
					  </tr>  
					  
					  <tr v-for="(item, index) in tabData.pListCou">
						  <td class="text-center" v-if="index==0" :rowspan="tabData.pListCou.length" style="vertical-align: middle">县级</td>
						  <td class="text-center">{{item.areaName_pro}}</td>
						  <td class="text-center">
						  {{item.name_pro}}
						  <img src="${webpath }/static/img/tishi-1.png" title="新参加" v-if="item.isJoinLast==null" style="height: 20px;position: absolute;padding: 0 10px;">
						  <img src="${webpath }/static/img/tishi-2.png" title="参与过未入选" v-if="item.isJoinLast=='1'" style="height: 20px;position: absolute;padding: 0 10px;">
						  </td>
					  </tr>  
				  </tbody>
			  </table>
			</div>
       	</div>
</div>       	
        <div class="echarts clearfix">
			<div class="main_1" style="width:450px; height:350px; float: left;">
			</div>
			<div class="main_2" style="width:350px; height:350px; float: left;">
			</div>
			<div class="main_3" id="main_3" style="width:350px; height:350px; float: left;">
			</div>
		</div>
		<div class="echarts clearfix">
			<div id="geren" style="width: 100%;height: 350px;"></div>
		</div>
		<div class="echarts clearfix">
			<div class="main_4" style="width:1200px; height:350px; float: left;">
			</div>
		</div>
		<div class="echarts clearfix">
			<div class="main_5" id="main_5" style="width:1200px; height:350px; float: left;">
			</div>
		</div>
		<div class="echarts clearfix">
			<div class="main_6" id="main_6" style="width:1200px; height:350px; float: left;">
			</div>
		</div>
		<div class="echarts clearfix">
			<div class="main_7" id="main_7" style="width:1200px; height:350px; float: left;">
			</div>
		</div>

