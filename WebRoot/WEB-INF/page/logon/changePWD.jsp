<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>


<!--框架内容 开始-->
<html lang="en">
<head>
<script type="text/javascript">
		      var WEBPATH='${webpath}';
		</script>
 <script type="text/javascript" src="${webpath }/static/js/jquery.min.js"></script>
		<script type="text/javascript" src="${webpath }/static/js/bootstrap.min.js"></script>
	    <script type="text/javascript" src="${webpath }/static/js/formValidation.js"></script>
		<script type="text/javascript" src="${webpath }/static/js/framework/bootstrap.js"></script>
		<script type="text/javascript" src="${webpath }/static/js/jquery.form.js"></script>
		<script type="text/javascript" src="${webpath }/static/js/language/zh_CN.js"></script>
		<script type="text/javascript" src="${webpath }/static/js/jquery-ui.min.js"></script>
		<script type="text/javascript" src="${webpath }/static/js/fileinput.min.js"></script>
		<script type="text/javascript" src="${webpath }/static/js/bootstrap-multiselect.js"></script>
		<script type="text/javascript" src="${webpath }/static/js/typeahead.bundle.js"></script>
		<script type="text/javascript" src="${webpath }/static/js/handlebars.js"></script>
		<script type="text/javascript" src="${webpath }/static/js/locales/bootstrap-fileinput/zh.js" ></script>
			
		<script type="text/javascript" src="${webpath }/static/js/scriptbreaker-multiple-accordion-1.js"></script>
		<script type="text/javascript" src="${webpath }/static/js/data.js"></script>

		<script type="text/javascript" src="${webpath }/static/js/framework/bootstrap-paginator.js"></script>

		<script type="text/javascript" src="${webpath}/static/easyui/jquery.easyui.min.js"></script>
		<script type="text/javascript" src="${webpath}/static/easyui/easyui-lang-zh_CN.js"></script>
		<script type="text/javascript" src="${webpath }/static/js/jquery.pwstabs-1.1.3.min.js"></script>
		
		<script type="text/javascript" src="${webpath }/static/js/bootstrap-datetimepicker.js"></script>
			
</head>


<div class="modal-header">
	<div style="float: right; margin-top: -5px;">
		
	</div>
	<h4 class="modal-title" id="myModalLabel">修改密码</h4>

</div>
<div class="modal-body">


	<div class="smart-widget-body form-horizontal">
		<form id="passForm">
			<div class="form-group">
			<label class="col-lg-9 control-label"><i style="color: red">请修改密码，要求长度8-20位，必须包含数字、字母和@</i></label></div>
			
			<sec:authentication property="principal" var="authentication" />
			<input type="hidden" name="id" id="id" value="${authentication.id}" />
			<div class="form-group">
				<label for="原密码" class="col-lg-3 control-label">原密码</label>
				<div class="col-lg-6">
					<input type="password" class="form-control" name="oldPass"
						placeholder="原密码">
				</div>
			</div>
			<div class="form-group">
				<label for="新密码" class="col-lg-3 control-label">新密码</label>
				<div class="col-lg-6">
					<input type="password" class="form-control" id="password"
						name="password" placeholder="新密码">
				</div>
			</div>
			<div class="form-group">
				<label for="新密码确认" class="col-lg-3 control-label">新密码确认</label>
				<div class="col-lg-6">
					<input type="password" class="form-control" name="repassword"
						placeholder="新密码确认">
				</div>
			</div>
		</form>
		<div class="form-group">
			<label class="col-lg-3 control-label"></label>
			<div class="col-lg-6">
				<button id="resetPwdBtn" type="button" onclick="reset()"
					class="btn btn-info no-shadow" tabindex="-1">保存</button>
			</div>
		</div>

	</div>
	<div class="clearfix"></div>
</div>


<script type="text/javascript">
	$(document).ready(function() {
		$('#toResetPwd').on('hide.bs.modal', function() {
			$(this).removeData("bs.modal");
		});
		business.listenEnter('resetPwdBtn');

		$("#passForm").formValidation({

			framework : 'bootstrap',
			message : 'This value is not valid',
			icon : {
				valid : 'glyphicon glyphicon-ok',
				invalid : 'glyphicon glyphicon-remove',
				validating : 'glyphicon glyphicon-refresh'
			},
			fields : {
				oldPass : {
					message : '原密码不正确',
					//Using the verbose option is solution for this approach. 
					//Setting verbose: false will stop validations when there is one failure validator.
					verbose : false,
					validators : {
						notEmpty : {
							message : '原密码为必填项'
						},
						stringLength : {
							min : 8,
							max : 20,
							message : '原密码为8-20个字符'
						},
						/* regexp : {
							regexp : /^[a-zA-Z0-9]+$/,
							message : '原密码由字母、数字组成'
						}, */
						//远程异步校验
						remote : {
							url : WEBPATH + '/sys/checkOldPass.do',
							type : 'POST',
							data : {
								oldPass : $(this).val()
							}

						}
					}

				},
				password : {
					validators : {
						notEmpty : {
							message : '新密码不能为空'
						},
						different : {
							field : 'oldPass',
							message : '新密码不能和原密码相同'
						},
						/* stringLength : {
							min : 6,
							max : 20,
							message : '新密码为6-20个字符'
						}, */
						regexp : {
							regexp: /^(?=.*[0-9])(?=.*[a-zA-Z])(?=.*[@]).{8,20}$/,
							message : "密码长度为8-20个字符，由字母、数字和@组成"
						}
					}
				},
				repassword : {
					validators : {
						notEmpty : {
							message : '新密码确认为必填项'
						},
						/* stringLength : {
							min : 6,
							max : 20,
							message : '新密码为6-20个字符'
						}, */
						regexp : {
							regexp: /^(?=.*[0-9])(?=.*[a-zA-Z])(?=.*[@]).{8,20}$/,
							message : "新密码为8-20个字符，由字母、数字和@组成"
						},
						identical : {
							field : 'password',
							message : '新密码与新密码确认不相同'
						}
					}
				}

			}
		});

	});
	
	//重置密码
	function reset(){

	    var validate = $("#passForm").data('formValidation').isValid();
	    //校验通过:null，false  ,true
	    if(validate){
	        	
	    	business.ajaxPostJson(WEBPATH+"/sys/resetPass.do", {password:$("#password").val()}, function(ret){
	    		
	    		if(ret.type=="error"){
	                 swal("修改密码失败!", "", "error");
	                 return false;
	             }else if(ret.type=="success"){
	                swal("修改密码成功！", "", "success");
	                $("#passForm").data('formValidation').resetForm();
	                $("#passForm")[0].reset();
	                window.location.href = WEBPATH + "/login.do";
	             }
	    	});
	    }else if(validate==null){
	      //某些表单未填写
	      
	        $("#passForm").data('formValidation').validate();
	    }
	   
	}
</script>
</html>
