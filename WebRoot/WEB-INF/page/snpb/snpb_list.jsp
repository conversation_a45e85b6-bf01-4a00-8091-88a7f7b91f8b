<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<html>
<head>
 <script type="text/javascript">
//提交
$('#submitBtn').click(function(){
	var unitList=$("#unitListSize").val();
	if(unitList > 0){
		
		
		
			$.ajax({
				url: WEBPATH+"/snpb/reportCountry.do",
				type:'post',
				success:function(data){
					if(data.result=="error"){
						swal("提交失败!", "", "error");
		                return false;
		             }else if(data.result=="success"){
		             	swal({
						    	title: "提交成功!",
						        type: "success",
						        closeOnConfirm: true,
						        confirmButtonText: "确定",
						        confirmButtonColor: "#d9534f",
						        
					    	}, function() {
						   business.addMainContentParserHtml('snpb/snpb_list.do','');
						});
		               return false;
		             }	
				},
				error:function(){
					 swal("OMG", "请求错误", "error");
				}
			})
			$('#report').modal('hide');
		
	}else{
		 swal("提交失败", "报送时必须推荐省级数据！", "error");
		 $('#report').modal('hide');
	}
});

function baosong(){
    // swal({title: "报送成功",text: "",type:"success"});
	/*var b = 0;
	$.ajax({
		url: WEBPATH+"/snpb/checkFile.do",
		type:'post',
		async:false,
		success:function(data){
			if(data.result=="error"){
				swal("提交失败!", "", "error");
                return false;
             }else if(data.result=="success"){
             	if(data.data=="1"){
             		b=1;
             	}else{
             		swal("提示", "请省级环保部门上传活动总结报告、自评报告！", "info");
             	}
             }	
		},
		error:function(){
			 swal("error", "请求错误", "error");
		}*/
	// })
	//
	// if(b==1){
		$("#report").modal();
	// }


}
</script>
</head>
<body>
<div class="center_weizhi">当前位置：推荐报送- 审核报送 - 报送国家</div>
<div class="center">
<div class="center_list">
<%-- <h4 style="color:#F00;">--%>
<%--          提示：此功能暂未开通。预计开通时间：2020年12月1日。报送时间暂定：2020年12月1日至12月4日。 disabled="disabled"--%>
<%-- </h4>--%>
</div>
      <input type="hidden" id="reportState" value="${reportState}">
<%--	  	<c:if test="${sysInitConfig.code == 1}">--%>
<%--		  <c:if test="${reportState !='1'}">--%>
<%--		    <button id="btn" type="button" class="btn btn-danger" style="width:120px;" onclick="baosong()" disabled="disabled">报送</button>--%>
<%--	      </c:if>--%>
<%--	    </c:if>--%>
       <h4 style="font-weight:bold">先进集体</h4>

       <input type="hidden" class="form-control" name="unitListSize" id="unitListSize" value="${unitListSize}">
        <table class="table table-bordered table-hover table-condensed">
         <thead>
           <tr>
             <th width="50" height="30" bgcolor="#efefef">序号</th>
             <th width="150" bgcolor="#efefef">参选单位</th>
             <%--<th width="130"bgcolor="#efefef">重点排污单位数量</th>--%>
<%--             <th width="200" bgcolor="#efefef"> </th>--%>
<%--             <th width="100" bgcolor="#efefef"> </th>--%>
<%--             <th width="300" bgcolor="#efefef"> </th>--%>
             <%--<th width="150"bgcolor="#efefef">排污单位证明材料</th>--%>
<%--             <th bgcolor="#efefef">操作</th>--%>
           </tr>
         </thead>
         <tbody>
          <c:forEach  var="selectionUnitList" items="${selectionUnitList}" varStatus="status">
         	<tr>
             <td height="30" align="center">${status.index+1}</td>
             <td>${selectionUnitList.areanamePro}</td>
             <%--<td>${selectionUnitList.polluternumber}</td>--%>
             <td>${selectionUnitList.deedevidencename}</td>
                <td>${selectionUnitList.peitaoNo}</td>
                <td>${selectionUnitList.jichaNo}</td>
             <%--<td>${selectionUnitList.polluterevidencename}</td>--%>
<%--             --%>
<%--             <td> --%>
<%--             <c:if test="${sysInitConfig.code == 1}">--%>
<%--				<c:if test="${reportState !='1'}">--%>
<%--					<c:if test="${selectionUnitList.areatypePro =='1'}">--%>
<%--						<button  id="btn2" class="btn btn-danger btn-xs" onclick="macroMgr.onLevelTwoMenuClick(null, 'snpb/provinceUnitUpdate.do?id=${selectionUnitList.id}')">编辑</button>--%>
<%--					</c:if>--%>
<%--					<c:if test="${selectionUnitList.areatypePro =='2' || selectionUnitList.areatypePro =='3'}">   --%>
<%--						<button  id="btn2" class="btn btn-danger btn-xs" onclick="macroMgr.onLevelTwoMenuClick(null, 'snpb/cityUnitUpdate.do?id=${selectionUnitList.id}')">编辑</button>--%>
<%--					</c:if>  --%>
<%--&lt;%&ndash;					<c:if test="${selectionUnitList.areatypePro !=1}">&ndash;%&gt;--%>
<%--&lt;%&ndash;						<button id="btn3" class="btn btn-danger btn-xs" onclick="macroMgr.onLevelTwoMenuClick(null, 'snpb/snpb_caseUpdate.do?areacodePro=${selectionUnitList.areacodePro}&areanamePro=${selectionUnitList.areanamePro}&areatypePro=${selectionUnitList.areatypePro}')">案卷修改</button>&ndash;%&gt;--%>
<%--&lt;%&ndash;					</c:if>&ndash;%&gt;--%>
<%--				</c:if>--%>
<%--			</c:if>				--%>
<%--            </td>--%>
              
              
           </tr>
           </c:forEach>
         </tbody>
       </table>
       
       <h4 style="font-weight:bold">先进个人</h4>
        <input type="hidden" class="form-control" name="personalList" id="personalList" value="${personalListSize}">
       <table class="table table-bordered table-hover table-condensed">
         <thead>
           <tr>
             <th width="40" height="30" bgcolor="#efefef">序号</th>
             <th width="100" bgcolor="#efefef">所属行政区</th>
             <th width="50" bgcolor="#efefef">姓名</th>
             <th width="50" bgcolor="#efefef">性别</th>
             <th width="50" bgcolor="#efefef">职务</th>
             <th width="100" bgcolor="#efefef">所在单位名称</th>
             <th width="80" bgcolor="#efefef">个人案卷号</th>
             <th width="60" bgcolor="#efefef">环保工作年限</th>
             <th width="150" bgcolor="#efefef">身份证号码</th>
             <th width="100" bgcolor="#efefef">联系电话</th>
             <th width="60" bgcolor="#efefef">学历</th>
             <th width="100" bgcolor="#efefef">编制性质</th>
             <%--<th width="80" bgcolor="#efefef">参与调查处理案件数量</th>--%>
             <th width="50" bgcolor="#efefef">操作</th>
           </tr>
         </thead>
         <tbody>
          <c:forEach  var="selectionPersonalList" items="${selectionPersonalList}" varStatus="status">
	          <tr>
	             <td height="30" align="center">${status.index+1}</td>
	             <td>${selectionPersonalList.areanamePro}</td>
	             <td>${selectionPersonalList.namePro}</td>
	             <td>${selectionPersonalList.sexPro}</td>
	             <td>${selectionPersonalList.jobPro}</td>
	             <td>${selectionPersonalList.unitnamePro}</td>
	             <td>${selectionPersonalList.fileCodePro}</td>
	             <td>${selectionPersonalList.workYearPro}</td>
	             <td>${selectionPersonalList.cardidPro}</td>
	             <td>${selectionPersonalList.phonePro}</td>
	             <td>
		             <c:if test="${selectionPersonalList.educationcodePro=='01'}">初中及以下</c:if>
		             <c:if test="${selectionPersonalList.educationcodePro=='02'}">中专</c:if>
		             <c:if test="${selectionPersonalList.educationcodePro=='03'}">高中</c:if>
		             <c:if test="${selectionPersonalList.educationcodePro=='04'}">大专</c:if>
		             <c:if test="${selectionPersonalList.educationcodePro=='05'}">本科</c:if>
		             <c:if test="${selectionPersonalList.educationcodePro=='06'}">硕士</c:if>
		             <c:if test="${selectionPersonalList.educationcodePro=='07'}">博士</c:if>
		             <c:if test="${selectionPersonalList.educationcodePro=='08'}">其他</c:if>
	             </td>
	             <td>
		              <c:if test="${selectionPersonalList.orgpropcodePro=='01'}">事业编制</c:if>
		             <c:if test="${selectionPersonalList.orgpropcodePro=='02'}">参照公务员管理事业编制</c:if>
		             <c:if test="${selectionPersonalList.orgpropcodePro=='03'}">行政编制</c:if>
		             <c:if test="${selectionPersonalList.orgpropcodePro=='04'}">企业编制</c:if>
		             <c:if test="${selectionPersonalList.orgpropcodePro=='05'}">其他</c:if>
	             </td>
	             <%--<td>${selectionPersonalList.handlcasenumPro}</td>--%>
	             <td>
	             	<c:if test="${sysInitConfig.code == 1}">
		            	<c:if test="${reportState !='1'}">
		             		<button   class="btn btn-danger btn-xs" onclick="macroMgr.onLevelTwoMenuClick(null, 'snpb/personalUpdate.do?id=${selectionPersonalList.id}&areacodePro=${selectionPersonalList.areacodePro}&areatypePro=${selectionPersonalList.areatypePro}')">编辑</button>
		             	</c:if>
	             	</c:if>
	             </td>
	           </tr>
	       </c:forEach>
         </tbody>
       </table>
        
          <div style="float:right;">
          	<c:if test="${sysInitConfig.code == 1}"> 
	            <c:if test="${reportState !='1'}">
	              <button id="btn1" type="button" class="btn btn-danger" style="width:120px;" data-toggle="modal" onclick="baosong()" >报送</button>
	            </c:if>
            </c:if>
           </div>
       
</div>
</div>
<!-- 提交（Modal） -->
<div class="modal fade" id="report" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title">提示</h4>
            </div>
            <div class="modal-body form-horizontal text-center" style="height:200px; font-size:18px; padding:80px 0; color:red;">
            	本单位还有剩余推荐名额${finalTotalNum}个单位和${finalTotalPer}名个人。提交后参选单位将无法更改，是否确认报送？
            </div>
            <div class="modal-footer"><button id="submitBtn" name="submitBtn" type="button" class="btn btn-danger">确定</button>
                <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

</body>
</html>