<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
<script type="text/javascript">
var RecommendId;
function deleteRecommend(id){
	 swal({
        title: "您确定执行此操作吗？",
        text: "您确定要删除当前人员吗？",
        type: "warning",
        showCancelButton: true,
        closeOnConfirm: false,
        confirmButtonText: "是的，我要删除",
        confirmButtonColor: "#d9534f"
    }, function(){
        $.ajax({
            url: WEBPATH+"/recom/deleteRecommendPersonal.do",
            type: "POST",
            data:{"id":id}
        }).done(function(data) {
            if(data.result=="success"){
            	swal("操作成功!", "已成功删除！", "success");
            	macroMgr.onLevelTwoMenuClick(null, 'recom/RecommendPersonal.do');
            }else{
                swal("OMG", "删除失败了!", "error");
            }
            
        }).error(function(data) {
            swal("OMG", "所选行政区为空,无法保存", "error");
        });
    });
}
//此事件由个人推荐页面的“增加参选个人”按钮触发
//加载的是本省级所有已上报个人信息和本省级的所有市级信息
function selectCity(){
	//已上报个人信息
	$("#zpdf").html("");
	$("#ranking").html("");
	if($("#list").val() < 10){
		$("#myModal").modal("show")
	$.ajax({
		url: WEBPATH+"/recom/selectPersonals.do",
		type:'post',
		success:function(data){
			var res = data;
			var option;
			var option1 = "<option value='0'>"+'请选择人员'+"</option>";
			$.each(res,function(i,n){//循环，i为下标从0开始，n为集合中对应的第i个对象
				
				option += "<option value='"+n.id+"'>"+n.namePro+"</option>"
			});
			$("#three").html(option1+option);//将循环拼接的字符串插入第二个下拉列表
			$("#three").show();
		},
		error:function(){
			 swal("OMG", "请求错误", "error");
		}
	});
	//本省级的所有市级信息
	$.ajax({
		url: WEBPATH+"/recom/selectCityList.do",
		type:'post',
		success:function(data){
			var res = data;
			var option;
			var option1 = "<option value='0'>"+'请选择行政区'+"</option>";
			$.each(res,function(i,n){//循环，i为下标从0开始，n为集合中对应的第i个对象
				
				option += "<option value='"+n.code+"'>"+n.name+"</option>"
			});
			$("#first").html(option1+option);//将循环拼接的字符串插入第二个下拉列表
			$("#first").show();
		},
		error:function(){
			 swal("OMG", "请求错误", "error");
		}
	})
	}else{
		swal("不可增加", "候选个人10个已满", "error");
	}
}
//通过所选的市级code加载本市下的所有县级信息和本市已上报人员信息
function selectCountry(code){
	$("#cardidPro").val("");
	$("#zpdf").html("");
	$("#ranking").html("");
	//通过本市code查询本市已上报个人信息
	$.ajax({
		url: WEBPATH+"/recom/selectPersonals.do",
		type:'post',
		data:{"areacodePro":code},
		success:function(data){
			var res = data;
			var option;
			var option1 = "<option value='0'>"+'请选择人员'+"</option>";
			$.each(res,function(i,n){//循环，i为下标从0开始，n为集合中对应的第i个对象
				
				option += "<option value='"+n.id+"'>"+n.namePro+"</option>"
			});
			$("#three").html();
			$("#three").html(option1+option);//将循环拼接的字符串插入第二个下拉列表
			$("#three").show();
		},
		error:function(){
			 swal("OMG", "请求错误", "error");
		}
	});
	//通过本市code查询本市下的所有县级
	$.ajax({
		url: WEBPATH+"/recom/selectCountryList.do",
		type:'post',
		data:{"code":code},
		success:function(data){
			var res = data;
			var option;
			var option1 = "<option value='0'>"+'请选择行政区'+"</option>";
			$.each(res,function(i,n){//循环，i为下标从0开始，n为集合中对应的第i个对象
				
				option += "<option value='"+n.code+"'>"+n.name+"</option>"
			});
			$("#second").html(option1+option);//将循环拼接的字符串插入第二个下拉列表
			$("#second").show();
		},
		error:function(){
			 swal("OMG", "请求错误", "error");
		}
	})
}
//根据所选县级的code查询本县已上报的所有个人
function selectPersonal(code){
	$("#cardidPro").val("");
	$("#zpdf").html("");
	$("#ranking").html("");
	$.ajax({
		url: WEBPATH+"/recom/selectPersonals.do",
		type:'post',
		data:{"areacodePro":code},
		success:function(data){
			var res = data;
			var option;
			var option1 = "<option value='0'>"+'请选择人员'+"</option>";
			$.each(res,function(i,n){//循环，i为下标从0开始，n为集合中对应的第i个对象
				
				option += "<option value='"+n.id+"'>"+n.namePro+"</option>"
			});
			$("#three").html();
			$("#three").html(option1+option);//将循环拼接的字符串插入第二个下拉列表
			$("#three").show();
		},
		error:function(){
			 swal("OMG", "请求错误", "error");
		}
	});
}
//通过所选择个人的id加载个人的基本信息
function selectInformation(id){
	$("#cardidPro").val("");
	$("#zpdf").html("");
	$("#ranking").html("");
	if(id!=0){
	$.ajax({
		url: WEBPATH+"/recom/selectInformationById.do",
		type:'post',
		data:{"id":id},
		success:function(data){
			//var aa='<input type="hidden" class="form-control" name="isrecommend" id="isrecommend" value="+'data.id'+">';
			//$("#recommend").html(aa);
			RecommendId=data.id;
			$("#cardidPro").val(data.cardidPro);
			$("#zpdf").html(data.altogetherscorePro);
			$("#ranking").html(data.provincerankingPro);
			
		},
		error:function(){
			
		}
	})
	}	
}
//推荐保存使用
$('#savebtn').click(function(){
	if(RecommendId != null && RecommendId != "" && RecommendId !="undefined"){
	$.ajax({
		url: WEBPATH+"/recom/updateRecommendPersonalById.do",
		type:'post',
		data:{"id":RecommendId},
		success:function(data){
			if(data.result=="error"){
				swal("推荐失败!", "", "error");
                return false;
             }else if(data.result=="success"){
             	swal({
				    	title: "推荐成功!",
				        type: "success",
				        closeOnConfirm: true,
				        confirmButtonText: "确定",
				        confirmButtonColor: "#d9534f"
			    	}, function() {
			    		RecommendId=null;
				    	business.addMainContentParserHtml('recom/RecommendPersonal.do','');
				});
               return false;
             }	
		},
		error:function(){
			 swal("OMG", "请求错误", "error");
		}
	})
	}else{
		swal("推荐失败", "请选择要推荐的个人再点击确定", "error");
	}
	$('#myModal').modal('hide');
	});
//提交
$('#submitBtn').click(function(){
	var aa=$("#list").val();
	if(aa <= 10){
	$.ajax({
		url: WEBPATH+"/recom/submitRecommendPersonal.do",
		type:'post',
		//data:{"id":RecommendId},
		success:function(data){
			if(data.result=="error"){
				swal("提交失败!", "", "error");
                return false;
             }else if(data.result=="success"){
             	swal({
				    	title: "提交成功!",
				        type: "success",
				        closeOnConfirm: true,
				        confirmButtonText: "确定",
				        confirmButtonColor: "#d9534f"
			    	}, function() {
			    		//RecommendId=null;
				    	business.addMainContentParserHtml('recom/RecommendPersonal.do','');
				});
               return false;
             }	
		},
		error:function(){
			 swal("OMG", "请求错误", "error");
		}
	
	})
// 	}else{
// 		swal("推荐失败", "请选择要推荐的市级再点击确定", "error");
// 	}
	$('#tijiao').modal('hide');
}else{
	 swal("提交失败", "推荐个人不能超过10人", "error");
	 $('#tijiao').modal('hide');
}
	});
</script>

</head>
<body>
<div class="center_weizhi">当前位置：推荐报送 - 集体和个人推荐 - 候选个人推荐</div>
<div class="center">
	<div class="center_list">
    <div class="panel-group" id="accordion">
          
          
            <!---搜索--->
            <div style="width:100%px;" class="btn-group">
            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.electPerState !=1 && provinceReportUser.reportstate !=1}">
              <c:choose>
   				<c:when test="${provinceReportUser.isDevInternalEva == 1}"> 
   				  	<c:choose>
   				   		<c:when test="${provinceElectionPersonals[0].provincerankingPro !=null }">
   				   			<button class="btn btn-danger" onclick="selectCity()" type="button" style="border-radius: 4px;" data-toggle="modal" > 增加参选个人</button>
              				<button type="button" class="btn btn-danger" style="width:180px; border-radius: 4px; margin-left: 20px" data-toggle="modal" data-target="#tijiao">提交</button>
   				   		</c:when>
   				  		<c:otherwise> 

              				<h4 style="color:#F00;" >请确认排完名之后再推荐</h4>

   				  		</c:otherwise>
   				  	</c:choose>
   				</c:when>
   				<c:otherwise> 
   					<button class="btn btn-danger" onclick="selectCity()" type="button" style="border-radius: 4px;" data-toggle="modal" > 增加参选个人</button>
              		<button type="button" class="btn btn-danger" style="width:180px; border-radius: 4px; margin-left: 20px" data-toggle="modal" data-target="#tijiao">提交</button>
   				</c:otherwise>
			</c:choose> 
             </c:if>
             <c:if test="${ provinceReportUser.electPerState ==1 }">
             <h4 style="color:#F00;" >候选个人已提交完毕，提交候选个人信息如列表所示：</h4>
             </c:if>
            </div>            
    </div>
    	<input type="hidden" class="form-control" name="list" id="list" value="${listSize}">
        <table class="table table-bordered table-hover table-condensed">
         <thead>
           <tr>
             <th width="50" height="30" bgcolor="#efefef">序号</th>
             <th bgcolor="#efefef" style="width:300px;">所属省</th>
             <th bgcolor="#efefef" style="width:300px;">所属地市</th>
             <th bgcolor="#efefef" style="width:300px;">所属区县</th>
             <th bgcolor="#efefef" style="width:300px;">个人姓名</th>
             <th bgcolor="#efefef" >身份证号</th>
             <th bgcolor="#efefef" style="width:100px;">操作</th>
           </tr>
         </thead>
         <tbody>
           <c:forEach  var="ProvinceElectionPersonals" items="${ProvinceElectionPersonals}" varStatus="status">
         		 <tr>
	             <td height="30" align="center">${status.index+1}</td>
	             <td>${ProvinceElectionPersonals.provincePro}</td>
	             <td>${ProvinceElectionPersonals.cityPro}</td>
	             <td>${ProvinceElectionPersonals.countryPro}</td>
	             <td>${ProvinceElectionPersonals.namePro}</td>
	             <td>${ProvinceElectionPersonals.cardidPro}</td>
	             <td style="text-align: center;">
	             	<c:if test="${sysInitConfig.code =='1' && provinceReportUser.electPerState != 1 && provinceReportUser.reportstate !=1}">
	             		<a href="#"><button class="btn btn-danger btn-xs" onclick="deleteRecommend('${ProvinceElectionPersonals.id}')">删除</button></a> 
				 	</c:if>	             
				 </td>
	             
	           </tr>
          	</c:forEach>
         </tbody>
       </table>
    </div>
<!--     <div class="page"> -->
<!--         <ul class="pagination"> -->
<!--             <li><a href="#">&laquo;</a></li> -->
<!--             <li class="active"><a href="#">1</a></li> -->
<!--             <li><a href="#">2</a></li> -->
<!--             <li><a href="#">3</a></li> -->
<!--             <li><a href="#">4</a></li> -->
<!--             <li><a href="#">5</a></li> -->
<!--             <li><a href="#">&raquo;</a></li> -->
<!--         </ul> -->
<!--     </div> -->
</div>
<!-- 新增县级参选单位（Modal） -->
<div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title">新增参选个人</h4>
            </div>
            <div class="modal-body form-horizontal" style="font-size:16px;">
            	<div class="form-group" style="padding:10px;">
                	<label class="col-lg-3 control-label">省份</label>
                    <div class="col-lg-8" style="margin-top:7px;">
                	${areaUser.userName}
                    </div>
                </div>
            	<div class="form-group" style="padding:10px;">
                	<label class="col-lg-3 control-label">市级行政区</label>
                    <div class="col-lg-8">
                		<select id="first" class="form-control" onChange="selectCountry(this.value)" >
                             
                          </select>
                    </div>
                </div>
                <div class="form-group" style="padding:10px;">
                	<label class="col-lg-3 control-label">县级行政区</label>
                    <div class="col-lg-8">
                		<select id="second" class="form-control" onChange="selectPersonal(this.value)">
                            
                          </select>
                    </div>
                </div>
                <div class="form-group" style="padding:10px;">
                	<label class="col-lg-3 control-label">个人姓名</label>
                    <div class="col-lg-8">
                		<select id="three" class="form-control" onChange="selectInformation(this.value)">
                             
                          </select>
                    </div>
                </div>
               
                <div class="form-group" style="padding:10px;">
                	<label class="col-lg-3 control-label">身份证号</label>
                    <div class="col-lg-8">
                		<input id="cardidPro" name="cardidPro" type="text" class="form-control" placeholder="自动获取" readonly>
                    </div>
                </div>
                
                <c:if test="${provinceReportUser.isDevInternalEva == 1}">
                <div class="form-group" style="padding:10px;">
                	<label class="col-lg-3 control-label">自评得分</label>
                    <div  id="zpdf" class="col-lg-8" style="margin-top:7px;">
                	
                    </div>
                </div>
                </c:if>
                <c:if test="${provinceReportUser.isDevInternalEva == 1}">
                <div class="form-group" style="padding:10px;">
                	<label class="col-lg-3 control-label">排名</label>
                    <div id="ranking" class="col-lg-8" style="margin-top:7px;">
                	
                    </div>
                </div>
                </c:if>
            </div>
            <div class="modal-footer"><button id="savebtn" name="savebtn" type="button" class="btn btn-danger">确定</button>
                <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
<!-- 提交（Modal） -->
<div class="modal fade" id="tijiao" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title">提示</h4>
            </div>
            <div class="modal-body form-horizontal text-center" style="height:200px; font-size:18px; padding:80px 0; color:red;">
            	提交后参选个人将无法更改，是否确认提交？
            </div>
            <div class="modal-footer"><button id="submitBtn" name="submitBtn" type="button" class="btn btn-danger">确定</button>
                <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
<script language="JavaScript">

$(document).ready(function() {
	$(".topnav").accordion({
		accordion:false,
		speed: 500,
		closedSign: '[+]',
		openedSign: '[-]'
	});
});

</script>
</body>
</html>