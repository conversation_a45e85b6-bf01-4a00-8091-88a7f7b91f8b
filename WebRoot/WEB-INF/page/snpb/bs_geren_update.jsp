<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
<script type="text/javascript">
//学历选择
function educationChange(){
	 var v = $("#educationcode").find("option:selected").text();
	 $("#educationname").val(v);
}
//编码性质选择
function orgpropChange(){
	 var v = $("#orgpropcode").find("option:selected").text();
	 $("#orgpropname").val(v);
}

//根据身份证号异步验证该人员去年是否参与大练兵
$("#cardID").blur(function(){
    $.ajax({
        type: "POST",
        url: "${webpath}/xxcj/selectByIdCard.do",
        data:{cardID:this.value},
        async:false,
        success: function(data){
            if(data.result=="success"){
                //姓名
                $("#namePro").val(data.data.xingMing);
                //性别
                var sex = data.data.sex;
                $("#sexPro").val(sex);
                //职务
                $("#jobPro").val(data.data.zhiWu);
                //单位名称
                $("#unitnamePro").val(data.data.danWeiMingCheng);
                //计算工作年限
                var beginDate = new Date(data.data.gongZuoShiJian);
                var endDate = new Date();
                beginDate.setDate(beginDate.getDate()-1);
                var diffYear = endDate.getFullYear() - beginDate.getFullYear();
                $("#workyearnumPro").val(int(diffYear));
                //学历
                var xueLi = data.data.xueLi;
                $("#educationcode").val(xueLi);
                var xueLiName = $("#educationcode").find("option:selected").text();
                // alert(xueLiName)
                $("#educationname").val(xueLiName);
                //联系电话
                $("#phonePro").val(data.data.lianXiDianHua);
            }else{
                swal({title:"提示",text:data.message,type:"info",confirmButtonColor: "#d9534f"});
            }
        }
    });
	$.ajax({
		   type: "POST",
		   url: "${webpath}/xxcj/checkIDWithLast.do",
		   data:{cardID:this.value},
		   async:false,
		   success: function(data){
			   if(data.result=="success"){
				   swal({title:"提示",text:data.message,type:"info",confirmButtonColor: "#d9534f"});
				   if(data.data=="0"){
					   $("#isjoinlast").val("1");
			       }else if(data.data=="1"){
			    	   $("#isjoinlast").val("2");
			       }
			   }
		   }
	});
})

var areaChange=function(){
	 $("#handlcasenumtext").text("");
    $("#handlcasenum").val("");
	 $(".multiselect-data-ajax").val(null).trigger("change");
}

var resetSelect1=function(){
	 $("#filecodexxcf1").val("");
	 $("#belongareacodexxcf1").val("");
	 $("#xxcf1oldid").val("");
	 $("#filesimpledescPro").val("");
	 $("#filedetiaildescPro").val("");
	 $("#fileid1").val("");
	 $("#typeaheadxxcf1").val(null).trigger("change");
	 
	 $('#saveXxcjPersonForm').data('formValidation').resetForm(); 
}
var resetSelect2=function(){
	 $("#filecodexxcf2").val("");
	 $("#belongareacodexxcf2").val("");
	 $("#xxcf2oldid").val("");
	 $("#onefilesimpledescPro").val("");
	 $("#onefiledetiaildescPro").val("");
	 $("#fileid2").val("");
	 $("#typeaheadxxcf2").val(null).trigger("change");
	 
	 $('#saveXxcjPersonForm').data('formValidation').resetForm(); 
}

function butChange(index){
	var butVal = $("#reButton"+index).attr("value");
	var butStyle = $("#reButton"+index).attr("style");
	if(butStyle!=""&&typeof(butStyle)!="undefined"){
		$("#reButton"+index).attr("style","");
    	$("#uploadTr"+index).attr("style","display:none");
    	$("#filetext"+index).attr("style","");
	}else{
		if(butVal=="重新上传"){
			$("#reButton"+index).attr("value","返回");
			$("#uploadTr"+index).attr("style","");
	    	$("#filetext"+index).attr("style","display:none");
		}else if(butVal=="返回"){
			$("#reButton"+index).attr("value","重新上传");
			$("#uploadTr"+index).attr("style","display:none");
	    	$("#filetext"+index).attr("style","");
		}
	}
}

$(document).ready(function(){
	 //个人事迹材料  .个人廉洁执法相关证明材料
	  $("#file1").fileinput({
  uploadUrl: WEBPATH+'/pdffileupload.do', // you must set a valid URL here else you will get an error
  allowedFileExtensions : ['doc','docx'],
  language:'zh',
  browseClass:'btn btn-danger',//按钮样式
  //overwriteInitial: true,
  minFileCount: 1,
  maxFileCount: 1,
  minFileSize:1,
  maxFileSize:1024,
  enctype: 'multipart/form-data',        
  dropZoneTitle:"可拖拽文件到此处...",
  initialPreviewShowDelete: false,
  msgFilesTooMany:'选择上传的文件数量({n}) 超过允许的最大数值{m}！',
  msgZoomModalHeading:'文件预览',
  msgInvalidFileExtension:'不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
  msgNoFilesSelected:'请选择文件',
  msgValidationError:'文件类型不正确或文件过大',
  initialPreviewFileType:'pdf',
  browseLabel:"选择文件",
  removeLabel:'删除',
  removeTitle:'删除文件',
  uploadLabel:'上传',
  uploadTitle:'上传文件',
  cancelLabel: '取消',
  cancelTitle: '取消上传',
  showPreview:false,
  autoReplace:true,
  slugCallback: function(filename) {
      return filename.replace('(', '_').replace(']', '_');
  }
	}).on('filepreupload', function(event, data, previewId, index) { //文件开始上传操作
		
	   $("#saveXxcjPersonBtn").prop('disabled', true);
	
	}).on('fileuploaded', function(event, data, previewId, index) {//文件上传完成执行操作
		
		if(event.currentTarget.id=="file1"){
			$("#personalmaterialPro").val(data.response.fileRealName);
		  	$("#personalmaterialurlPro").val(data.response.url);
		  	$("#filetext1").text(data.response.fileRealName);
		  	butChange('1');
		}
		$("#saveXxcjPersonBtn").prop('disabled',false);
	})
	$("#reButton1").click(function(){
		butChange('1');
    	
    });
	
	/* $("#file2").fileinput({
	      uploadUrl: WEBPATH+'/pdffileupload.do', // you must set a valid URL here else you will get an error
	      allowedFileExtensions : ['pdf'],
	      language:'zh',
	      browseClass:'btn btn-danger',//按钮样式
	      //overwriteInitial: true,
	      minFileCount: 1,
	      maxFileCount: 1,
	      minFileSize:1,
	      maxFileSize:51200,
	      enctype: 'multipart/form-data',        
	      dropZoneTitle:"可拖拽文件到此处...",
	      initialPreviewShowDelete: false,
	      msgFilesTooMany:'选择上传的文件数量({n}) 超过允许的最大数值{m}！',
	      msgZoomModalHeading:'文件预览',
	      msgInvalidFileExtension:'不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
	      msgNoFilesSelected:'请选择文件',
	      msgValidationError:'文件类型不正确',
	      initialPreviewFileType:'pdf',
	      browseLabel:"选择文件",
	      removeLabel:'删除',
	      removeTitle:'删除文件',
	      uploadLabel:'上传',
	      uploadTitle:'上传文件',
	      cancelLabel: '取消',
	      cancelTitle: '取消上传',
	      showPreview:false,
	      autoReplace:true,
	      slugCallback: function(filename) {
	          return filename.replace('(', '_').replace(']', '_');
	      }
		}).on('filepreupload', function(event, data, previewId, index) { //文件开始上传操作
			
		   $("#saveXxcjPersonBtn").prop('disabled', true);
		
		}).on('fileuploaded', function(event, data, previewId, index) {//文件上传完成执行操作
			
			if(event.currentTarget.id=="file2"){
				$("#perhonestfilenamePro").val(data.response.fileRealName);
			  	$("#perhonestfileurlPro").val(data.response.url);
			  	$("#filetext2").text(data.response.fileRealName);
			  	butChange('2');
			}
		
			$("#saveXxcjPersonBtn").prop('disabled',false);
		})
		$("#reButton2").click(function(){
			butChange('2');
    	});*/
	
	 //个人照片上传
	 $("#file3").fileinput({
  uploadUrl: WEBPATH+'/pdffileupload.do', // you must set a valid URL here else you will get an error
  allowedFileExtensions : ['JPG','PNG','BMP'],
  language:'zh',
  browseClass:'btn btn-danger',//按钮样式
  //overwriteInitial: true,
  minFileCount: 1,
  maxFileCount: 1,
  minFileSize:1,
  maxFileSize:5120,
  enctype: 'multipart/form-data',        
  dropZoneTitle:"可拖拽文件到此处...",
  initialPreviewShowDelete: false,
  msgFilesTooMany:'选择上传的文件数量({n}) 超过允许的最大数值{m}！',
  msgZoomModalHeading:'文件预览',
  msgInvalidFileExtension:'不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
  msgNoFilesSelected:'请选择文件',
  msgValidationError:'文件类型不正确或文件过大',
  initialPreviewFileType:'pdf',
  browseLabel:"选择文件",
  removeLabel:'删除',
  removeTitle:'删除文件',
  uploadLabel:'上传',
  uploadTitle:'上传文件',
  cancelLabel: '取消',
  cancelTitle: '取消上传',
  showPreview:false,
  autoReplace:true,
  slugCallback: function(filename) {
      return filename.replace('(', '_').replace(']', '_');
  }
	}).on('filepreupload', function(event, data, previewId, index) { //文件开始上传操作
		
	   $("#saveXxcjPersonBtn").prop('disabled', true);
	
	}).on('fileuploaded', function(event, data, previewId, index) {//文件上传完成执行操作
		
		if(event.currentTarget.id=="file3"){
			$("#personalphotouploadPro").val(data.response.fileRealName);
		  	$("#personalphotouploadurlPro").val(data.response.url);
		  	$("#filetext3").text(data.response.fileRealName);
		  	butChange('3');
		}
		$("#saveXxcjPersonBtn").prop('disabled',false);
	})
	$("#reButton3").click(function(){
		butChange('3');
    });
    // 日常监督执法工作
    $("#file4").fileinput({
        uploadUrl: WEBPATH+'/pdffileupload.do', // you must set a valid URL here else you will get an error
        allowedFileExtensions : ['rar'],
        language:'zh',
        browseClass:'btn btn-danger',//按钮样式
        //overwriteInitial: true,
        minFileCount: 1,
        maxFileCount: 1,
        minFileSize:1,
        maxFileSize:53500,
        enctype: 'multipart/form-data',
        dropZoneTitle:"可拖拽文件到此处...",
        initialPreviewShowDelete: false,
        msgFilesTooMany:'选择上传的文件数量({n}) 超过允许的最大数值{m}！',
        msgZoomModalHeading:'文件预览',
        msgInvalidFileExtension:'不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
        msgNoFilesSelected:'请选择文件',
        msgValidationError:'文件类型不正确或文件过大',
        initialPreviewFileType:'rar',
        browseLabel:"选择文件",
        removeLabel:'删除',
        removeTitle:'删除文件',
        uploadLabel:'上传',
        uploadTitle:'上传文件',
        cancelLabel: '取消',
        cancelTitle: '取消上传',
        showPreview:false,
        autoReplace:true,
        slugCallback: function(filename) {
            return filename.replace('(', '_').replace(']', '_');
        }
    }).on('filepreupload', function(event, data, previewId, index) { //文件开始上传操作
        $("#saveXxcjPersonBtn").prop('disabled', true);
    }).on('fileuploaded', function(event, data, previewId, index) {//文件上传完成执行操作
        if(event.currentTarget.id=="file4"){
            $("#dailySupervisionWorkPro").val(data.response.fileRealName);
            $("#dailySupervisionWorkUrlPro").val(data.response.url);
            $("#filetext4").text(data.response.fileRealName);
            butChange('4');
        }
        $("#saveXxcjPersonBtn").prop('disabled',false);
    })

    $("#reButton4").click(function(){
        butChange('4');
    });



    //初始化加载select2
		$("#typeaheadxxcf1").select2({
			language:'zh-CN',
			  data:[{id:'${xxcjProFiles1.oldidPro}'+'${xxcjProFiles1.filetypePro}',text:'${xxcjProFiles1.filecodePro}'}],
			  allowClear:true
			}).val(['${xxcjProFiles1.oldidPro}'+'${xxcjProFiles1.filetypePro}']).trigger("change").select2({	
			  language:'zh-CN',
			  ajax: {
			    url: "${webpath}/xxcj/selectAnjuanByAreacode2020.do",
			    dataType: 'json',
			    delay: 500,
			    type:'POST',
			    data: function (params) {
			    	var a=$("#areatypePro").val()
			    	var subAreaCode=$("#areacodePro").val().substring(0,2);
		 		      var areacode= $("#areacodePro").val();
		 		     if($("#areatypePro").val()=="2"){
		 		    	if(subAreaCode=='11'||subAreaCode=='12'||subAreaCode=='55'||subAreaCode=='31'||subAreaCode=='66'){//直辖市下面的区,相当于别的县
					    	  areacode=areacode.substring(0,6);
					      }else{
					    	  areacode=areacode.substring(0,4);
					      }
			    	}else if($("#areatypePro").val()=="1"){
			    		 areacode=areacode.substring(0,2);
			    	}else if($("#areatypePro").val()=="3"){
			    		 areacode=areacode.substring(0,6);
			    	}
			      return {
			        query: params.term,
			        filetype:$("#filetype").val(),
			        qtype:0,
			        areacode:areacode,
			        isClosed:0
			      };
			    },
			    processResults: function (data, params) {
			      return {
			        results: data
			      };
			    },
			    cache: true
			  },
			  escapeMarkup: function (markup) 
			  { 
				  return markup; 
			  },
			  templateResult:function (result) {
			        return result.text;
		      },
			  templateSelection:function (result) {
			        return result.text;
		      },
			  minimumInputLength:3,
			  theme:'bootstrap',
			  placeholder:'请选择决定文书号/移送编号',
			  allowClear:true
			});
        
	    	 //绑定选择select事件
			$('#typeaheadxxcf1').on('select2:select', function (evt) {
				var res = $("#typeaheadxxcf1").select2("data");
				$("#filecodexxcf1").val(res[0].text);
				$("#belongareacodexxcf1").val(res[0].areacode);
				$("#xxcf1oldid").val(res[0].oldid);
				$("#fileid1").val("");
			});
			//绑定取消选择select事件
			$('#typeaheadxxcf1').on('select2:unselecting', function (evt) {
				$("#filecodexxcf1").val("");
				$("#belongareacodexxcf1").val("");
				$("#xxcf1oldid").val("");
			}); 

			/*//初始化加载案件二select2
			$("#typeaheadxxcf2").select2({
				 language:'zh-CN',
				  data:[{id:'${xxcjProFiles2.oldidPro}'+'${xxcjProFiles2.filetypePro}',text:'${xxcjProFiles2.filecodePro}'}],
				  allowClear:true
				}).val(['${xxcjProFiles2.oldidPro}'+'${xxcjProFiles2.filetypePro}']).trigger("change").select2({	
				  language:'zh-CN',
				  ajax: {
					  url: "${webpath}/xxcj/selectAnjuanByAreacode2020.do",
				    dataType: 'json',
				    delay: 500,
				    type:'POST',
				    data: function (params) {
				    	if($("#areatypePro").val()==2){
				    		 var subAreaCode='${subAreaCode}';
						      var areacode="";
						      if(subAreaCode=='11'||subAreaCode=='12'||subAreaCode=='55'||subAreaCode=='31'||subAreaCode=='66'){//直辖市下面的区,相当于别的县
						    	  areacode=$("#areacodePro").val().substring(0,6);
						      }else{
						    	  areacode=$("#areacodePro").val().substring(0,4);
						      }
				    	}else if($("#areatypePro").val()==1){
				    		 areacode=$("#areacodePro").val().substring(0,2);
				    	}else if($("#areatypePro").val()==3){
				    		 areacode=$("#areacodePro").val().substring(0,6);
				    	}
				      return {
				        query: params.term,
				        filetype:$("#filetype2").val(),
				        qtype:0,
				        areacode:areacode,
				        isClosed:0
				      };
				    },
				    processResults: function (data, params) {
				      return {
				        results: data
				      };
				    },
				    cache: true
				  },
				  escapeMarkup: function (markup) 
				  { 
					  return markup; 
				  },
				  templateResult:function (result) {
				        return result.text;
			      },
				  templateSelection:function (result) {
				        return result.text;
			      },
				  minimumInputLength:3,
				  theme:'bootstrap',
				  placeholder:'请选决定文书号/移送编号',
				  allowClear:true
				});
			//绑定选择select事件
			$('#typeaheadxxcf2').on('select2:select', function (evt) {
				var res = $("#typeaheadxxcf2").select2("data");
				$("#filecodexxcf2").val(res[0].text);
				$("#belongareacodexxcf2").val(res[0].areacode);
				$("#xxcf2oldid").val(res[0].oldid);
				$("#xxcf2oldid").val(res[0].oldid);
				$("#fileid2").val("");
			});
			//绑定取消选择select事件
			$('#typeaheadxxcf2').on('select2:unselecting', function (evt) {
				$("#filecodexxcf2").val("");
				$("#belongareacodexxcf2").val("");
				$("#xxcf2oldid").val("");
			});
	*/
         
	//表单验证
		$("#saveXxcjPersonForm").formValidation({
	      framework: 'bootstrap',
	      message: 'This value is not valid',
	      icon:{
		            valid: 'glyphicon glyphicon-ok',
		            invalid: 'glyphicon glyphicon-remove',
		            validating: 'glyphicon glyphicon-refresh'
	      },
	      fields: {
	      		"files[0].filetypePro": {
	              validators: {
	                  notEmpty: {
	                      message: '请选择案卷一类型'
	                  }
	              }
	          },
	         /* "files[0].filesimpledescPro": {
	              validators: {
	            	  notEmpty: {
	                      message: '请填写案卷一案件调查情况材料详版说明'
	                  },
	            	  stringLength: {
	                      min: 1,
	                      max: 300,
	                      message: '1-300个字符'
	                  },
	                  regexp:{
	                         message:'不允许输入非法的字符<、>',
	                         regexp:/^(?!.*(\<|\>))/
	                    }
	              }
	          },
	          "files[0].filedetiaildescPro": {
	              validators: {
	            	  notEmpty: {
	                      message: '请填写案卷一案件调查情况材料详版说明'
	                  },
	                  stringLength: {
	                      min: 1,
	                      max: 2000,
	                      message: '1-2000个字符'
	                  },
	                  regexp:{
	                         message:'不允许输入非法的字符<、>',
	                         regexp:/^(?!.*(\<|\>))/
	                    }
	              }
	          },
	          "files[1].filesimpledescPro": {
	        	  enabled:false,
	              validators: {
	            	  notEmpty: {
	                      message: '请填写案卷二案件调查情况材料简版说明'
	                  },	
	            	  stringLength: {
	                      min: 1,
	                      max: 300,
	                      message: '1-300个字符'
	                  },
	                  regexp:{
	                         message:'不允许输入非法的字符<、>',
	                         regexp:/^(?!.*(\<|\>))/
	                    }
	              }
	          },*/
	          /*"files[1].filedetiaildescPro": {
	        	  enabled:false,
 	              validators: {
	            	  notEmpty: {
	                      message: '请填写案卷二案件调查情况材料详版说明'
	                  },
	            	  stringLength: {
	                      min: 1,
	                      max: 2000,
	                      message: '1-2000个字符'
	                  },
	                  regexp:{
	                         message:'不允许输入非法的字符<、>',
	                         regexp:/^(?!.*(\<|\>))/
	                    }
	              }
	          },*/
	          "proElectionPersonal.namePro": {
	              validators: {
	                  notEmpty: {
	                      message: '请输入姓名'
	                  },
	                  stringLength: {
	                      min: 1,
	                      max: 15,
	                      message: '1-15个字符'
	                  },
	                  regexp:{
	                         message:'不允许输入非法的字符<、>',
	                         regexp:/^(?!.*(\<|\>))/
	                    }
	              }
	          },
	          "proElectionPersonal.sexPro": {
	              validators: {
	                  notEmpty: {
	                      message: '请选择性别'
	                  }
	              }
	          },
	          "proElectionPersonal.jobPro": {
	              validators: {
	                  notEmpty: {
	                      message: '请输入职务'
	                  },
	                  stringLength: {
	                      min: 1,
	                      max: 30,
	                      message: '1-30个字符'
	                  },
	                  regexp:{
	                         message:'不允许输入非法的字符<、>',
	                         regexp:/^(?!.*(\<|\>))/
	                    }
	              }
	          },
	          "proElectionPersonal.unitnamePro": {
	               validators: {
	                   notEmpty: {
	                       message: '请输入单位名称'
	                   },
			           stringLength: {
			               min: 1,
			               max: 50,
			               message: '1-50个字符'
			           },
		                  regexp:{
		                         message:'不允许输入非法的字符<、>',
		                         regexp:/^(?!.*(\<|\>))/
		                    }
	               }
	           },
	           "proElectionPersonal.workyearnumPro": {
	        	   validators: {
	            	   notEmpty: {
	                       message: '请输入工作年限'
	                   },
	                   regexp:{
	                      	message:'请输入最多一位小数的有效数字',
	                      	regexp:/^[0-9]+(.[0-9]{1,1})?$/i
	                   },
		                  regexp:{
		                         message:'不允许输入非法的字符<、>',
		                         regexp:/^(?!.*(\<|\>))/
		                    }
	               }
	           },
	           "proElectionPersonal.educationcodePro": {
	               validators: {
	                   notEmpty: {
	                       message: '请选择学历'
	                   }
	               }
	           },
	           "proElectionPersonal.orgpropcodePro": {
	               validators: {
	                   notEmpty: {
	                       message: '请选择编制性质'
	                   }
	               }
	           },
	          "proElectionPersonal.cardidPro": {
	              validators: {
	                  notEmpty: {
	                      message: '请输入身份证号码'
	                  },
	                  regexp:{
	                     	message:'请输入正确的身份证号码',
	                     	regexp:/^\d{17}(\d|x)$/i
	                  },
	                  remote: {
	               	   dataType:'json',
	               	   type:'POST',
	               	   delay:500,
	                      url: '${webpath}/xxcj/checkIdCardUpdate2018.do',
	                      data: function(validator, $field, value) {
	                          return {
	                              idcard:validator.getFieldElements('proElectionPersonal.cardidPro').val(),
	                          	  id:'${ProPerson.id }'
	                          };
	                      },
	               	   message:'身份证号码已经存在，不允许重复录入'
	                  }
	              }
	          },
	          "proElectionPersonal.phonePro": {
	              validators: {
	                  notEmpty: {
	                      message: '请输入联系电话'
	                  },
	                  phone:{
	                     	message:'请输入正确的手机号码',
	                     	country:'CN'
	                  },
	                  regexp:{
	                         message:'不允许输入非法的字符<、>',
	                         regexp:/^(?!.*(\<|\>))/
	                    }
	              }
	          },
	          /*"proElectionPersonal.handlcasecodesPro": {
	              validators: {
	                  notEmpty: {
	                      message: '请输入参与调查处理案件案卷号'
	                  },
	                  stringLength: {
	                      min: 1,
	                      max: 30,
	                      message: '1-30个字符'
	                  }
	              }
	          }*/
		   }
		});          
        
		//准备ID
		var extract_preselected_ids = function(data){
	        var preselected_ids = [];
	        $.each(data,function(index,obj){
	        	preselected_ids.push(obj.id);
	    	});
	        return preselected_ids;
	    };
		/*//初始化加载select2
		$(".multiselect-data-ajax").select2({
			 language:'zh-CN',
			  data:eval("${ProPerson.handlcasecodesPro}"),
			  allowClear:true
			}).val(extract_preselected_ids(eval("${ProPerson.handlcasecodesPro}"))).trigger("change").select2({	
			  language:'zh-CN',
			  ajax: {
			    url: "${webpath}/xxcj/selectAnjuanByAreacodeAll.do",
			    dataType: 'json',
			    delay: 500,
			    type:'POST',
			    data: function (params) {
			    	if($("#areatypePro").val()==2){
			    		 var subAreaCode='${subAreaCode}';
					      var areacode="";
					      if(subAreaCode=='11'||subAreaCode=='12'||subAreaCode=='55'||subAreaCode=='31'||subAreaCode=='66'){//直辖市下面的区,相当于别的县
					    	  areacode=$("#areacodePro").val().substring(0,6);
					      }else{
					    	  areacode=$("#areacodePro").val().substring(0,4);
					      }
			    	}else if($("#areatypePro").val()==1){
			    		 areacode=$("#areacodePro").val().substring(0,2);
			    	}else if($("#areatypePro").val()==3){
			    		 areacode=$("#areacodePro").val().substring(0,6);
			    	}
			      return {
			        query: params.term,
			        areacode:areacode
			      };
			    },
			    processResults: function (data, params) {
			      return {
			        results: data,
			      };
			    },
			    cache: true
			  },
			  escapeMarkup: function (markup) 
			  { 
				  return markup; 
			  }, 
			  templateResult:function (result) {
			        return result.text;
		      },
			  templateSelection:function (result) {
			        return result.text;
		      },
			  minimumInputLength:3,
			  placeholder:'请选择参与调查处理案件的决定书文号或移送编号',
			  allowClear:true
			});
			//绑定选择select事件
			$('.multiselect-data-ajax').on('select2:select', function (evt) {
				var res = $(".multiselect-data-ajax").select2("data");
				var value= [];
				$.each(res,function(index,obj){
					var objValue={id:obj.id,text:obj.text}
					value.push(objValue);
				});
				$("#handlcasecodesPro").val(JSON.stringify(value).replace(/\"/g, "'"));
				$("#handlcasenumtext").text(res.length);
		     	$("#handlcasenum").val(res.length);
			});
			//绑定取消选择select事件
			$('.multiselect-data-ajax').on('select2:unselect', function (evt) {
				var res = $(".multiselect-data-ajax").select2("data");
				var value= [];
				$.each(res,function(index,obj){
					var objValue={id:obj.id,text:obj.text}
					value.push(objValue);
				});
				$("#handlcasecodesPro").val(JSON.stringify(value).replace(/\"/g, "'"));
				$("#handlcasenumtext").text(res.length);
		     	$("#handlcasenum").val(res.length);
			});*/
			$("#saveXxcjPersonForm").data('formValidation').validate();
});
	 
//保存数据方法
$('#saveXxcjPersonBtn').click(function() {
	if(($("#filetype").val()!="") &&  ($("#filecodexxcf1").val()=="")){
		swal({title:"操作失败", text:"案卷一的案卷文号不能为空!", type:"error",confirmButtonColor: "#d9534f"});
	}else{
		/*if($("#filetype2").val()!="" &&  ($("#filecodexxcf2").val()=="")){
				swal({title:"操作失败", text:"案卷二的案卷文号不能为空!", type:"error",confirmButtonColor: "#d9534f"});
			}else{
				if($("#filetype2").val()!=""){
					$('#saveXxcjPersonForm').formValidation('enableFieldValidators','files[1].filesimpledescPro',true);
					$('#saveXxcjPersonForm').formValidation('enableFieldValidators','files[1].filedetiaildescPro',true);
				}*/
 	 var options = {
     url: WEBPATH+'/xxcj/updateBSPersonPro.do',
     type: 'post',
     success:function(data){
           if(data.result=="error"){
        	   swal({title:"操作失败", text:data.message, type:"error",confirmButtonColor: "#d9534f"});
               return false;
           }else if(data.result=="success"){
        	   business.addMainContentParserHtml('snpb/snpb_list.do',null);
        	  swal({title: "保存成功",text: "",type:"success",confirmButtonColor: "#d9534f"});
             return false;
           }else{
        	   swal({title: "系统异常",text:"请刷新菜单或重新登录！",type:"warning",confirmButtonColor: "#d9534f"});
           }
   	}
 	 };
 	$("#saveXxcjPersonForm").data('formValidation').validate();
	var validate = $("#saveXxcjPersonForm").data('formValidation').isValid();
	if(validate){
		//学历选择
		 var v = $("#educationcode").find("option:selected").text();
		 $("#educationname").val(v);
	//编码性质选择
		 var v = $("#orgpropcode").find("option:selected").text();
		 $("#orgpropname").val(v);
		
		var personalmaterialPro=$("#personalmaterialPro").val();
 		var personalmaterialurlPro=$("#personalmaterialurlPro").val();
 		
 		// var perhonestfilenamePro=$("#perhonestfilenamePro").val();
 		// var perhonestfileurlPro=$("#perhonestfileurlPro").val();
 		
 		var personalphotouploadPro=$("#personalphotouploadPro").val();
 		var personalphotouploadurlPro=$("#personalphotouploadurlPro").val();
 		
		if((personalmaterialPro==""||personalmaterialurlPro=="")||/*(perhonestfilenamePro==""||perhonestfileurlPro=="")||*/(personalphotouploadPro==""||personalphotouploadurlPro=="")){
		 	swal({title:"提示", text:"请选择上传文件", type:"error",confirmButtonColor: "#d9534f"});
		}else{
			/*if($("#filecodexxcf1").val()==$("#filecodexxcf2").val() &&
			   $("#filetype").val()==$("#filetype2").val() && 
			   $("#xxcf1oldid").val()==$("#xxcf2oldid").val()){
				swal({title:"提示", text:"案卷一和案卷二的案卷文号不能相同", type:"error",confirmButtonColor: "#d9534f"});
			}else{*/
 		var anjuan=$("#typeaheadxxcf1").val();
		if(anjuan!=""&&anjuan!=null){
			var res = $(".multiselect-data-ajax").select2("data");
			/*if(res.length>0){*/
				$.ajax({
    			   type: "POST",
    			   url: "${webpath}/xxcj/selectPersonAnjuanCount.do",
    			   //data:{areacode:'${areaUser.areaCode}'.substring(0,2),type:$("#filetype").val()},
    			   async:false,
    			   success: function(data){
    				   if(data.result=="error"){
    					   swal({title:"操作失败", text:data.message, type:"error",confirmButtonColor: "#d9534f"});
    		               return false;
    			       }else if(data.result=="success"){
    			          if(data.data>=150){
    			        	swal({title: "操作失败",text: "个人信息上报数量已经达到上限",type:"error",confirmButtonColor: "#d9534f"});
    			          }else{
    			        	  $('#saveXxcjPersonForm').ajaxSubmit(options);
    			          }
    			       }
    			   }
    			});
			/*}else{
				swal({title:"提示", text:"请选择参与调查处理案件的决定书文号或移送编号", type:"error",confirmButtonColor: "#d9534f"});
			}*/
		}else{
			swal({title:"提示", text:"请选择案卷", type:"error",confirmButtonColor: "#d9534f"});
		}	
		/*}*/
		}
	}
	/*}*/
		
	}
	});

function down() {
    var name = "突出表现候选个人-日常监督执法工作";
    var path = "${webpath}/xxcj/xcclExcel.do";
    window.location.href = path + "?name="+name;
}

</script>
</head>
<body>
<div class="center_weizhi"><span style="float: left;">当前位置：推荐报送- 审核报送 - 报送国家  </span><span style="float: right;padding-right: 10px;">
<a href="javascript:void(0);" onclick="macroMgr.onLevelTwoMenuClick(null, 'snpb/snpb_list.do')">
<i class="fa fa-chevron-left"></i>返回</a>
</span></div>

<div class="center">
<div class="center_list">
	<form id="saveXxcjPersonForm" method="post">
  <table  class="table_input">
    <tbody>
    	<tr>
			<td></td>
			<td style="text-align: left; color: #F00;">重要提醒：上传相关附件材料命名规则：个人姓名+材料名称。例如张三个人事迹材料，张三廉洁执法证明材料。</td>
		</tr>
      <tr>
        <td width="200">所属行政区</td>
        <td style="text-align:left;">
        	<div class="form-group">
                 <div class="col-sm-12">
                 ${ProPerson.areanamePro}
                 <input type="hidden"  id="areatypePro" name="proElectionPersonal.areatypePro" value="${areatypePro}">
                  <input type="hidden" id="areacodePro" name="proElectionPersonal.areacodePro" value="${ProPerson.areacodePro}">
                  <input type="hidden" name="proElectionPersonal.id" value="${ProPerson.id}">
            	     
            	     <input type="hidden" id="educationname" name="proElectionPersonal.educationPro" value="${proElectionPersonal.educationPro}">
        			 <input type="hidden" id="orgpropname" name="proElectionPersonal.orgpropPro" value="${proElectionPersonal.orgpropPro}">
          </div>
          </div>
          </td>
      </tr>
 
      <tr>
           	<td colspan="2">
           	<div class="shangbao_panel">
                <div class="shangbao_titlt">案卷一</div>
                <table style="width:100%;" class="table_input">
                    <tr>
                    <td width="224">案卷类型<i class="ace-icon fa fa-asterisk" style="color:red"></i></td>
                    <td>
                    <div class="form-group">
                 		<div class="col-sm-12">
                    	<select name="files[0].filetypePro" class="form-control" id="filetype"  onchange="resetSelect1();">
                         <option value="">请选择案卷类型</option>
		                 <option value="0" <c:if test="${xxcjProFiles1.filetypePro==0}">selected</c:if> >一般行政处罚</option>
				         <option value="1" <c:if test="${xxcjProFiles1.filetypePro==1}">selected</c:if> >按日计罚</option>
				         <option value="6" <c:if test="${xxcjProFiles1.filetypePro==6}">selected</c:if> >查封扣押</option>
				         <option value="7" <c:if test="${xxcjProFiles1.filetypePro==7}">selected</c:if> >限产停产</option>
				         <option value="2" <c:if test="${xxcjProFiles1.filetypePro==2}">selected</c:if> >移送行政拘留</option>
				         <option value="3" <c:if test="${xxcjProFiles1.filetypePro==3}">selected</c:if> >涉嫌犯罪移送</option>
                   	 </select>
                   	  </div>
		             </div>
                   	 </td>
                  </tr>
                  <tr>
                    <td>案卷文号<i class="ace-icon fa fa-asterisk" style="color:red"></i></td>
                    <td style="text-align: left;">
		             	<div class="form-group">
		             	<div class="col-sm-12">
			                 <select id="typeaheadxxcf1" class="form-control" >
							 </select>
				                 <input type="hidden"name="files[0].belongareacodePro" id="belongareacodexxcf1" value="${xxcjProFiles1.belongareacodePro}"/>
				                 <input type="hidden" name="files[0].filecodePro" id="filecodexxcf1" value="${xxcjProFiles1.filecodePro}"/>
				                 <input type="hidden" name="files[0].oldidPro" id="xxcf1oldid" value="${xxcjProFiles1.oldidPro}"/>
				                 <input type="hidden" name="files[0].id" id="fileid1" value="${xxcjProFiles1.id }">
			                 </div>
		             	</div>
		             </td>
                  </tr>
                  <%--<tr>
                    <td>案件调查情况材料简版说明<i class="ace-icon fa fa-asterisk" style="color:red"></i></td>
                    <td>
                     <div class="form-group">
                 		<div class="col-sm-12">
                    		<textarea rows="6" class="form-control"  id="filesimpledescPro" name="files[0].filesimpledescPro"  placeholder="请输入案卷材料简版说明">${ProPersonAndFiles[0].filesimpledescPro}</textarea>
                    		<span style="float:left;color: red;">提示：说明中应包括本案件所涉及的违法行为，采取的处罚措施及执行结果等内容。</span>
                    		 </div>
		             </div>
                    </td>
                  </tr>
                  <tr>
                    <td>案件调查情况材料详版说明<i class="ace-icon fa fa-asterisk" style="color:red"></i></td>
                    <td>
                    <div class="form-group">
                 		<div class="col-sm-12">
                    		<textarea rows="12" class="form-control"   id="filedetiaildescPro" name="files[0].filedetiaildescPro" placeholder="请输入案卷材料详版说明">${ProPersonAndFiles[0].filedetiaildescPro}</textarea>
                     		<span style="float:left;color: red;">提示：说明中应包括本案件所涉及的违法行为，采取的处罚措施及执行结果等内容。</span>
                     	</div>
		             </div>
                    </td>
                  </tr>--%>
                </table>
            </div>
            </td>
	   </tr>
      <%--<tr>
           	<td colspan="2">
           	<div class="shangbao_panel">
                <div class="shangbao_titlt">案卷二</div>
                <table style="width:100%;" class="table_input">
                    <tr>
                    <td width="224">案卷类型</td>
                    <td>
                    <div class="form-group">
                 		<div class="col-sm-12">
		                    <select name="files[1].filetypePro" class="form-control" id="filetype2"  onchange="resetSelect2();">
		                      <option value="">请选择案卷类型</option>
		                   		 <option value="0" <c:if test="${xxcjProFiles2.filetypePro==0}">selected</c:if> >一般行政处罚</option>
						         <option value="1" <c:if test="${xxcjProFiles2.filetypePro==1}">selected</c:if> >按日计罚</option>
						         <option value="6" <c:if test="${xxcjProFiles2.filetypePro==6}">selected</c:if> >查封扣押</option>
						         <option value="7" <c:if test="${xxcjProFiles2.filetypePro==7}">selected</c:if> >限产停产</option>
						         <option value="2" <c:if test="${xxcjProFiles2.filetypePro==2}">selected</c:if> >移送行政拘留</option>
						         <option value="3" <c:if test="${xxcjProFiles2.filetypePro==3}">selected</c:if> >涉嫌犯罪移送</option>
						         
						     </select>
				           </div>
		             </div>
				     </td>
                  </tr>
                 <tr>
                    <td>案卷文号</td>
                    <td style="text-align: left;">
		             	<div class="form-group">
		             	<div class="col-sm-12">
		                 <select id="typeaheadxxcf2" class="form-control" >
						 </select>
		                 	<input type="hidden"name="files[1].belongareacodePro" id="belongareacodexxcf2" value="${xxcjProFiles2.belongareacodePro}"/>
		                 	<input type="hidden" name="files[1].filecodePro" id="filecodexxcf2" value="${xxcjProFiles2.filecodePro}"/>
		                 	<input type="hidden" name="files[1].oldidPro" id="xxcf2oldid" value="${xxcjProFiles2.oldidPro}"/>
		                  	<input type="hidden" name="files[1].id" id="fileid2" value="${xxcjProFiles2.id }">
		                  </div>
		            </div>
		            </td>
                  </tr>
                  <tr>
                    <td>案件调查情况材料简版说明</td>
                    <td>
                    <div class="form-group">
                 		<div class="col-sm-12">
                    		<textarea rows="6" class="form-control" id="onefilesimpledescPro" placeholder="请输入案卷材料简版说明" name="files[1].filesimpledescPro">${ProPersonAndFiles[1].filesimpledescPro}</textarea>
                    	 	<span style="float:left;color: red;">提示：说明中应包括本案件所涉及的违法行为，采取的处罚措施及执行结果等内容。</span>
                    	 </div>
	                </div>
                    </td>
                  </tr>
                  <tr>
                    <td>案件调查情况材料详版说明</td>
                    <td>
                    <div class="form-group">
                 		<div class="col-sm-12">
                    		<textarea rows="12" class="form-control" id="onefiledetiaildescPro" placeholder="请输入案卷材料详版说明" name="files[1].filedetiaildescPro">${ProPersonAndFiles[1].filedetiaildescPro}</textarea>
                     		<span style="float:left;color: red;">提示：说明中应包括本案件所涉及的违法行为，采取的处罚措施及执行结果等内容。</span>
                     	</div>
	                </div>
                    </td>
                  </tr>
                </table>
            </div>
            </td>--%>
        <tr>
            <td>身份证号码<i class="ace-icon fa fa-asterisk" style="color:red"></i></td>
            <td>
                <div class="form-group">
                    <div class="col-sm-12">
                        <input type="text" class="form-control" id="cardID" name="proElectionPersonal.cardidPro" placeholder="请输入身份证号码"  value="${ProPerson.cardidPro}">
                        <input type="hidden" id="isjoinlast" name="isjoinlast"/>
                    </div>
                </div>
            </td>
        </tr>
	   </tr>
           <tr>
             <td>姓名<i class="ace-icon fa fa-asterisk" style="color:red"></i></td>
             <td>
             <div class="form-group">
                 <div class="col-sm-12">
             <input type="text" class="form-control" name="proElectionPersonal.namePro" id="namePro" placeholder="请输入姓名" value="${ProPerson.namePro}">
             </div>
             </div>
             </td>
           </tr>
           <tr>
             <td>性别<i class="ace-icon fa fa-asterisk" style="color:red"></i></td>
             <td>
             <div class="form-group">
                 <div class="col-sm-12">
             <select name="proElectionPersonal.sexPro" class="form-control" id="sexPro">
               <option value="">请选择性别</option>
              <option <c:if test="${ProPerson.sexPro=='男'}">selected</c:if>>男</option>
               <option <c:if test="${ProPerson.sexPro=='女'}">selected</c:if>>女</option>
             </select>
             </div>
             </div>
             </td>
           </tr>
           <tr>
             <td>职务<i class="ace-icon fa fa-asterisk" style="color:red"></i></td>
             <td>
             <div class="form-group">
                 <div class="col-sm-12">
             <input type="text" class="form-control" name="proElectionPersonal.jobPro" id="jobPro" placeholder="请输入职务"  value="${ProPerson.jobPro}">
             </div>
             </div>
             </td>
           </tr>
            <tr>
             <td>所在单位名称<i class="ace-icon fa fa-asterisk" style="color:red"></i></td>
             <td>
             <div class="form-group">
                 <div class="col-sm-12">
             <input type="text" class="form-control" name="proElectionPersonal.unitnamePro" id="unitnamePro" placeholder="请输入所在单位名称"   value="${ProPerson.unitnamePro}">
             </div>
             </div>
             </td>
           </tr>
           
           <tr>
             <td>环保工作年限<i class="ace-icon fa fa-asterisk" style="color:red"></i></td>
             <td>
             <div class="form-group">
                 <div class="col-sm-12">
             <input type="text" class="form-control" name="proElectionPersonal.workyearnumPro" id="workyearnumPro" placeholder="请输入环保工作年限"   value="${ProPerson.workYearPro}">
             </div>
             </div>
             </td>
           </tr>

            <tr>
             <td>学历<i class="ace-icon fa fa-asterisk" style="color:red"></i></td>
             <td>
             <div class="form-group">
                 <div class="col-sm-12">
             <select id="educationcode" name="proElectionPersonal.educationcodePro" class="form-control" onchange="educationChange()">
             	<option value="" >请选择学历</option>
                <option value="01"  <c:if test="${ProPerson.educationcodePro==01}">selected</c:if> >初中及以下</option>
		        <option value="02"  <c:if test="${ProPerson.educationcodePro==02}">selected</c:if> >中专</option>
		        <option value="03"  <c:if test="${ProPerson.educationcodePro==03}">selected</c:if> >高中</option>
		        <option value="04"  <c:if test="${ProPerson.educationcodePro==04}">selected</c:if> >大专</option>
		        <option value="05"  <c:if test="${ProPerson.educationcodePro==05}">selected</c:if> >本科</option>
		        <option value="06"  <c:if test="${ProPerson.educationcodePro==06}">selected</c:if> >硕士</option>
		        <option value="07"  <c:if test="${ProPerson.educationcodePro==07}">selected</c:if> >博士</option>
		        <option value="08"  <c:if test="${ProPerson.educationcodePro==08}">selected</c:if> >其他</option>
             </select>
             </div>
             </div>
             </td>
           </tr>
            
           <tr>
             <td>编制性质<i class="ace-icon fa fa-asterisk" style="color:red"></i></td>
             <td>
             <div class="form-group">
                 <div class="col-sm-12">
             <select id="orgpropcode" name="proElectionPersonal.orgpropcodePro" class="form-control" onchange="orgpropChange()">
             	<option value="">请选择</option>
		        <option value="01"  <c:if test="${ProPerson.orgpropcodePro==01}">selected</c:if> >事业编制</option>
		        <option value="02"  <c:if test="${ProPerson.orgpropcodePro==02}">selected</c:if> >参照公务员管理事业编制</option>
		        <option value="03"  <c:if test="${ProPerson.orgpropcodePro==03}">selected</c:if> >行政编制</option>
		        <option value="04"  <c:if test="${ProPerson.orgpropcodePro==04}">selected</c:if> >企业编制</option>
		        <option value="05"  <c:if test="${ProPerson.orgpropcodePro==05}">selected</c:if> >其他</option>
             </select>
             </div>
             </div>
             </td>
           </tr>
           <tr>
             <td>联系电话<i class="ace-icon fa fa-asterisk" style="color:red"></i></td>
             <td>
             <div class="form-group">
                 <div class="col-sm-12">
             <input type="text" class="form-control" name="proElectionPersonal.phonePro" placeholder="请输入联系电话"  value="${ProPerson.phonePro}">
             </div>
             </div>
             </td>
           </tr>
           <%-- <tr>
             <td>参与调查处理案件案卷号<i class="ace-icon fa fa-asterisk" style="color:red"></i></td>
             <td>
             <div class="form-group">
                 <div class="col-sm-12">
                 	<select class="multiselect-data-ajax form-control" multiple="multiple">
					</select>
					<input type="hidden" name="proElectionPersonal.handlcasecodesPro" id="handlcasecodesPro" value="${ProPerson.handlcasecodesPro}"/>
             </div>
             </div>
             </td>
           </tr>
           <tr>
             <td>参与调查处理案件数量<i class="ace-icon fa fa-asterisk" style="color:red"></i></td>
             <td style="text-align:left;">
             <div class="form-group">
                 <div class="col-sm-12">
		             <span id="handlcasenumtext" >${ProPerson.handlcasenumPro}</span>
		             <input type="hidden" class="form-control" name="proElectionPersonal.handlcasenumPro" id="handlcasenum"  value="${ProPerson.handlcasenumPro}"/>
              	 </div>
             </div>    
             </td>
           </tr>--%>
				           <tr>
				             <td>个人事迹材料<i class="ace-icon fa fa-asterisk" style="color:red"></i></td>
				             <td style="text-align:left;">
				             		<div class="form-group">
				                 <div class="col-sm-12">
				                 <span id="filetext1">
				                <c:choose>
							        	<c:when test="${ProPerson.personalmaterialPro!='' && ProPerson.personalmaterialurlPro!=null }">
							        		<div style="float:left;margin-right:8px;padding-top:4px;">
							        			${ProPerson.personalmaterialPro}
							        		</div>
							        	</c:when>
							        </c:choose>
							      	</span>
							    	<span id="uploadTr1" <c:if test="${ProPerson.personalmaterialPro!='' && ProPerson.personalmaterialurlPro!=null }">style='display:none'</c:if>>
							        	<input id="file1" type="file" name="file1" class="file-loading" value="2"> 
									    <span style="color: red;">提示：要求500字以内，请上传word文件，大小不超过1M；</span>
									    <input type="hidden" name="proElectionPersonal.personalmaterialPro" id="personalmaterialPro"  value="${ProPerson.personalmaterialPro}"/>
			               				<input type="hidden" name="proElectionPersonal.personalmaterialurlPro" id=personalmaterialurlPro  value="${ProPerson.personalmaterialurlPro}"/>
							        </span> 
							         </div></div>
				                 	</td>
				                 	<td>
							         <c:if test="${ProPerson.personalmaterialPro!='' && ProPerson.personalmaterialurlPro!=null }">
							        		<input id="reButton1" class="btn btn-danger btn-xs" type="button" value="重新上传"/>
							        </c:if>
				                 	</td>
				                 	</tr>
           		    		<tr>
					             <td>个人照片上传<i class="ace-icon fa fa-asterisk" style="color:red"></i></td>
					             <td style="text-align:left;">
					             		<div class="form-group">
					                 <div class="col-sm-12">
					                 <span id="filetext3">
					                   <c:choose>
								        	<c:when test="${ProPerson.personalphotouploadPro!='' && ProPerson.personalphotouploadurlPro!=null }">
								        		<div style="float:left;margin-right:8px;padding-top:4px;">
								        			${ProPerson.personalphotouploadPro}
								        		</div>
								        	</c:when>
								        </c:choose>
								        </span>
								        <span id="uploadTr3" <c:if test="${ProPerson.personalphotouploadPro!='' && ProPerson.personalphotouploadurlPro!=null }">style='display:none'</c:if>>
									        <input id="file3" type="file" name="file3" class="file-loading" value="2"> 
											<span style="color: red;">提示：请上传2寸，浅色背景的照片，可上传JPG、PNG、BMP格式的附件，大小不超过5M；</span>
											<input type="hidden" name="proElectionPersonal.personalphotouploadPro" id="personalphotouploadPro"  value="${ProPerson.personalphotouploadPro}"/>
						                    <input type="hidden" name="proElectionPersonal.personalphotouploadurlPro" id="personalphotouploadurlPro"  value="${ProPerson.personalphotouploadurlPro}"/>
			           					</span>
								        </div></div>
								        </td>
								        <td>
								         <c:if test="${ProPerson.personalphotouploadPro!='' && ProPerson.personalphotouploadurlPro!=null }">
											<input id="reButton3" class="btn btn-danger btn-xs" type="button" value="重新上传"/>
										 </c:if>
					                 	</td>
					                 	</tr>
					                 
			                 <%-- <tr id="uploadTr2" <c:if test="${ProPerson.personalphotouploadPro!='' && ProPerson.personalphotouploadurlPro!=null }">style='display:none'</c:if>>
									        <td>&nbsp;</td>
									        <td style="text-align:left;">
										        <input id="file3" type="file" name="file3" class="file-loading" value="2"> 
					               				<span style="color: red;">提示：请上传2寸，浅色背景的照片，可上传JPG、PNG、BMP格式的附件，大小不超过5M；</span>
												<input type="hidden" name="proElectionPersonal.personalphotouploadPro" id="personalphotouploadPro"  value="${ProPerson.personalphotouploadPro}"/>
							                    <input type="hidden" name="proElectionPersonal.personalphotouploadurlPro" id="personalphotouploadurlPro"  value="${ProPerson.personalphotouploadurlPro}"/>
			                      			</td>
			           		 </tr> --%>
        <tr>
            <td>日常监督执法工作</td>
            <td style="text-align:left;">
                <div class="form-group">
                    <div class="col-sm-12">
                                                <span id="filetext4">
                                                    <c:choose>
                                                        <c:when test="${ProPerson.dailySupervisionWorkPro!='' && ProPerson.dailySupervisionWorkPro!=null }">
                                                            <div style="float:left;margin-right:8px;padding-top:4px;">
                                                                    ${ProPerson.dailySupervisionWorkPro}
                                                            </div>
                                                        </c:when>
                                                    </c:choose>
                                                </span>
                        <span id="uploadTr4" <c:if test="${ProPerson.dailySupervisionWorkPro!='' && ProPerson.dailySupervisionWorkPro!=null }">style='display:none'</c:if>>
                                        <input id="file4" type="file" name="file4" class="file-loading" value="2">
                                        <span style="color: red;">提示：文件命名规则参考个人事迹材料样式，如有相关信息，请上传rar文件，大小不超过50M。包括：Excel文件（<a style="color: blue;" href="#" onclick="down()">模板下载</a>）和证明材料；如无可不上传。</span>
                                        <input type="hidden" name="proElectionPersonal.dailySupervisionWorkPro" id="dailySupervisionWorkPro"  value="${ProPerson.dailySupervisionWorkPro}"/>
                                        <input type="hidden" name="proElectionPersonal.dailySupervisionWorkUrlPro" id="dailySupervisionWorkUrlPro"  value="${ProPerson.dailySupervisionWorkUrlPro}"/>
                                    </span>
                    </div>
                </div>
            </td>
            <td>
                <c:if test="${ProPerson.dailySupervisionWorkPro!='' && ProPerson.dailySupervisionWorkPro!=null }">
                    <input id="reButton4" class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                </c:if>
            </td>
        </tr><%--
			           		 <tr>
					             <td>个人廉洁执法相关证明材料<i class="ace-icon fa fa-asterisk" style="color:red"></i></td>
					             <td style="text-align:left;">
					             		<div class="form-group">
					                 <div class="col-sm-12">
					                 <span id="filetext2">
					                <c:choose>
								        	<c:when test="${ProPerson.perhonestfilenamePro!='' && ProPerson.perhonestfileurlPro!=null }">
								        		<div style="float:left;margin-right:8px;padding-top:4px;">
								        			${ProPerson.perhonestfilenamePro}
								        		</div>
								        	</c:when>
								        </c:choose>
								        </span>
								        <span id="uploadTr2" <c:if test="${ProPerson.perhonestfilenamePro!='' && ProPerson.perhonestfileurlPro!=null }">style='display:none'</c:if>>
										        <input id="file2" type="file" name="file2" class="file-loading" value="2"> 
	               								<span style="color: red;">提示：可上传PDF格式的附件，大小不超过50M；</span>
												 <input type="hidden" name="proElectionPersonal.perhonestfilenamePro" id="perhonestfilenamePro"  value="${ProPerson.perhonestfilenamePro}"/>
               									 <input type="hidden" name="proElectionPersonal.perhonestfileurlPro" id="perhonestfileurlPro"  value="${ProPerson.perhonestfileurlPro}"/>
			           		 			</span>
								        </div></div>
								        </td>
								        <td>
								         <c:if test="${ProPerson.perhonestfilenamePro!='' && ProPerson.perhonestfileurlPro!=null }">
											<input id="reButton2" class="btn btn-danger btn-xs" type="button" value="重新上传"/>
										 </c:if>
					                 	</td>
					                 	</tr>--%>
					                 
			                 <%-- <tr id="uploadTr3" <c:if test="${ProPerson.perhonestfilenamePro!='' && ProPerson.perhonestfileurlPro!=null }">style='display:none'</c:if>>
									        <td>&nbsp;</td>
									        <td style="text-align:left;">
										        <input id="file2" type="file" name="file2" class="file-loading" value="2"> 
	               								<span style="color: red;">提示：可上传PDF格式的附件，大小不超过50M；</span>
												 <input type="hidden" name="proElectionPersonal.perhonestfilenamePro" id="perhonestfilenamePro"  value="${ProPerson.perhonestfilenamePro}"/>
               									 <input type="hidden" name="proElectionPersonal.perhonestfileurlPro" id="perhonestfileurlPro"  value="${ProPerson.perhonestfileurlPro}"/>
			                      			</td>
			           		 </tr> --%>
      <tr>
        <td align="center">&nbsp;</td>
        <td style="text-align:left;"><a href="#">
          <button type="button" class="btn btn-danger" id="saveXxcjPersonBtn" style="font-size:16px;width:150px; margin-top:5px;">信息保存</button>
        </a></td>
      </tr>
    </tbody>
  </table>
  </form>
</div>
</div>
</body>
</html>
      