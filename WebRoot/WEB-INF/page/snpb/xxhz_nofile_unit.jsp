<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
</head>
<body>
<div class="center_weizhi">当前位置：信息汇总- 未上报案卷单位列表</div>
<div class="center">
<div class="center_list">
    <div class="panel-group" id="accordion">
    	<!-- <button type="button" class="btn btn-danger" id ="viewExcel" >导出EXCEL</button> --> 
    </div>
        <table class="table table-bordered table-hover table-condensed">
         <thead>
         <tr>
             <th width="40" height="30" bgcolor="#efefef">序号</th>
             <th bgcolor="#efefef">省</th>
             <th bgcolor="#efefef">市级总数</th>
             <th bgcolor="#efefef">未上报案卷市级行政区数</th>
             <th bgcolor="#efefef">县级总数</th>
             <th bgcolor="#efefef">未上报案卷县级行政区数</th>
             <th bgcolor="#efefef">操作</th>
           </tr>
         </thead>
         <tbody>
            <c:forEach items="${unitList}" var="item" varStatus="status">
	           <tr>
	             <td height="30" align="center">${status.index+1}<input id="id${status.index+1}" type="hidden"  value="${item.id}"></td>
	             <td>${item.province}</td>
	             <td>${item.allCity}</td>
	             <td <c:if test="${item.allCity==item.noCity}">style='color:red;'</c:if>>${item.noCity}</td>
	             <td>
	             	<c:choose>
	             		<c:when test="${item.areacode=='11' or item.areacode=='12' or item.areacode=='31' or item.areacode=='55'}">
	             			-
	             		</c:when>
	             		<c:otherwise>
	             			${item.allCountry}
	             		</c:otherwise>
	             	</c:choose>
	             </td>
	             <td <c:if test="${item.allCountry!='0'&&item.allCountry==item.noCountry}">style='color:red;'</c:if>>
	             	<c:choose>
	             		<c:when test="${item.areacode=='11' or item.areacode=='12' or item.areacode=='31' or item.areacode=='55'}">
	             			-
	             		</c:when>
	             		<c:otherwise>
	             			${item.noCountry}
	             		</c:otherwise>
	             	</c:choose>
	             </td>
	             <td>
	             	<button class="btn btn-danger btn-xs" style="width:100px;" onclick="macroMgr.onLevelTwoMenuClick(null, 'provinceScore/goNoFilesUnitList.do?subAreaCode=${item.areacode}' )">查看</button>
				 </td>
	           </tr>
           </c:forEach>
         </tbody>
       </table>
    </div>
</div>
<script type="text/javascript">




</script>
</body>
</html>
