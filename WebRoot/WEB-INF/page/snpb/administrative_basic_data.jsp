<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>

<script type="text/javascript">
	//分页
	$(document).ready(function(){
		business.listenEnter("searchButt");
		var curentPage = eval('${provinceSelectionUnit.pageNum}');
		var totalPage = eval('${provinceSelectionUnit.pages}');
		if(totalPage>0){
			var options = {
				bootstrapMajorVersion: 3,
			    currentPage: curentPage,
			    totalPages: totalPage,
			    numberOfPages: 5,
			    itemTexts: function (type, page, current) {
			            switch (type) {
			            case "first":
			                return "首页";
			            case "prev":
			                return "&laquo;";
			            case "next":
			                return "&raquo;";
			            case "last":
			                return "尾页";
			            case "page":
			                return page;
			            }
			    },
 			    onPageClicked: function (event, originalEvent, type, page) {
	            	business.addMainContentParserHtml(WEBPATH+'/recom/basicDataList.do?pageNum='+page,$("#searchForm").serialize() );
	            }
	    	};
	    	$('#pageCon').bootstrapPaginator(options);
    	}
	});


	$(document).ready(function(){
        /*$("#areatypePro option[value='1']").prop("selected",true);
        business.addMainContentParserHtml("recom/basicDataList.do",$("#searchForm").one().serialize());
        alert($("#searchForm").one().serialize())*/

		//快速查询
		$("#areatypePro").change(function(){
		    $("#flag").val("");
			business.addMainContentParserHtml("recom/basicDataList.do",$("#searchForm").serialize());
		});
		$("#areacodePro").change(function(){
            $("#flag").val("");
			business.addMainContentParserHtml("recom/basicDataList.do",$("#searchForm").serialize());
		});

	});
		//}
	//导出excel
	$("#viewExcel").click(function(){
		var areatypePro = $("#areatypePro").val();	
		var areacodePro = $("#areacodePro").val();
		
		if(typeof(areatypePro) == "undefined"){
			areatypePro="";
		}
		if(typeof(areacodePro) == "undefined"){
			areacodePro="";
		}
		window.location.href= WEBPATH+'/recom/exportUnitExcle.do?areatypePro='+areatypePro+'&areacodePro='+areacodePro;
	});
		
	//});
	function downloadFile(url,fileName){
		if(url!=null&&url!=""){
			window.location.href="${webpath}/filedownload.do?url="+url+"&fileName="+fileName;
		}else{
			swal({title: "提示",text: "您下载的附件不存在！",type:"info"});
		}
	}
	
</script>
<div class="center_weizhi">当前位置：推荐报送 - 信息汇总 - 行政区基础数据列表</div>
<!--框架内容 开始-->

<div class="center">
<div class="center_list">
    <div class="panel-group" id="accordion">
          <!---快速查询--->
          <form id="searchForm" name="searchForm" role="form">
         	<c:choose>
         	<c:when test="${sessionScope.sa_session.loginid == 'changneng'||sessionScope.sa_session.loginid == 'sysAdmin' }">
<%-- 				<c:if test="${areaUser.areaCode == '45000000' }"> --%>
              <select id="areacodePro" name="areacodePro" class="form-control" style="width:150px;margin-right:5px;">
               	 <option value="" <c:if test="${areatypePro==''}">selected</c:if>>请选择省份</option>
                 <option value="11000000" <c:if test="${areacodePro=='11000000'}">selected</c:if>>北京市</option>
                 <option value="12000000" <c:if test="${areacodePro=='12000000'}">selected</c:if>>天津市</option>
                 <option value="13000000" <c:if test="${areacodePro=='13000000'}">selected</c:if>>河北省</option>
                 <option value="14000000" <c:if test="${areacodePro=='14000000'}">selected</c:if>>山西省</option>
                 <option value="15000000" <c:if test="${areacodePro=='15000000'}">selected</c:if>>内蒙古自治区</option>
                 <option value="16000000" <c:if test="${areacodePro=='16000000'}">selected</c:if>>河南省</option>
                 <option value="21000000" <c:if test="${areacodePro=='21000000'}">selected</c:if>>辽宁省</option>
                 <option value="22000000" <c:if test="${areacodePro=='22000000'}">selected</c:if>>吉林省</option>
                 <option value="23000000" <c:if test="${areacodePro=='23000000'}">selected</c:if>>黑龙江省</option>
                 <option value="31000000" <c:if test="${areacodePro=='31000000'}">selected</c:if>>上海市</option>
                 <option value="32000000" <c:if test="${areacodePro=='32000000'}">selected</c:if>>江苏省</option>
                 <option value="33000000" <c:if test="${areacodePro=='33000000'}">selected</c:if>>浙江省</option>
                 <option value="34000000" <c:if test="${areacodePro=='34000000'}">selected</c:if>>安徽省</option>
                 <option value="35000000" <c:if test="${areacodePro=='35000000'}">selected</c:if>>福建省</option>
                 <option value="36000000" <c:if test="${areacodePro=='36000000'}">selected</c:if>>江西省</option>
                 <option value="37000000" <c:if test="${areacodePro=='37000000'}">selected</c:if>>山东省</option>
                 <option value="42000000" <c:if test="${areacodePro=='42000000'}">selected</c:if>>湖北省</option>
                 <option value="43000000" <c:if test="${areacodePro=='43000000'}">selected</c:if>>湖南省</option>
                 <option value="44000000" <c:if test="${areacodePro=='44000000'}">selected</c:if>>广东省</option>
                 <option value="45000000" <c:if test="${areacodePro=='45000000'}">selected</c:if>>广西壮族自治区</option>
                 <option value="46000000" <c:if test="${areacodePro=='46000000'}">selected</c:if>>海南省</option>
                 <option value="51000000" <c:if test="${areacodePro=='51000000'}">selected</c:if>>四川省</option>
                 <option value="52000000" <c:if test="${areacodePro=='52000000'}">selected</c:if>>贵州省</option>
                 <option value="53000000" <c:if test="${areacodePro=='53000000'}">selected</c:if>>云南省</option>
                 <option value="54000000" <c:if test="${areacodePro=='54000000'}">selected</c:if>>西藏自治区</option>
                 <option value="55000000" <c:if test="${areacodePro=='55000000'}">selected</c:if>>重庆市</option>
                 <option value="61000000" <c:if test="${areacodePro=='61000000'}">selected</c:if>>陕西省</option>
                 <option value="62000000" <c:if test="${areacodePro=='62000000'}">selected</c:if>>甘肃省</option>
                 <option value="63000000" <c:if test="${areacodePro=='63000000'}">selected</c:if>>青海省</option>
                 <option value="64000000" <c:if test="${areacodePro=='64000000'}">selected</c:if>>宁夏回族自治区</option>
                 <option value="65000000" <c:if test="${areacodePro=='65000000'}">selected</c:if>>新疆维吾尔自治区</option>
                 <option value="66000000" <c:if test="${areacodePro=='66000000'}">selected</c:if>>新疆建设兵团</option>
               </select>
<%--                </c:if> --%>
             </c:when>
             <c:otherwise>
                 <select id="areatypePro" name="areatypePro" class="form-control" style="width:150px;margin-right:5px;">
                 <option value="" <c:if test="${areatypePro==''}">selected</c:if>>请选择查询类型</option>
                 <option value="1" <c:if test="${areatypePro=='1'}">selected</c:if>>省级</option>
                 <option value="2" <c:if test="${areatypePro=='2'}">selected</c:if>>市级</option>
                 <option value="3" <c:if test="${areatypePro=='3'}">selected</c:if>>县级</option>
              </select>
                 <input type="hidden" name="flag" id="flag">
             </c:otherwise>
             </c:choose>
        </form>
          
            <!---导出--->
            <div style="width:260px;" class="btn-group">
<!--             <button id ="search" class="btn btn-success" type="button">快速搜索</button> -->
            <a href="#"><button type="button" class="btn btn-danger" id ="viewExcel">导出EXCEL</button></a>
            </div>            
    </div>
    
    <table class="table table-bordered table-hover table-condensed">
      <thead>
        <tr>
            <th height="30" bgcolor="#efefef">行政区</th>
          <%--<th width="210" bgcolor="#efefef">行政区域内重点排污企业数量</th>--%>
          <%--<th bgcolor="#efefef">全省重点排污单位总数</th>--%>
          <%--<th bgcolor="#efefef">考核奖惩制度</th>--%>
          <%--<th bgcolor="#efefef">执法人员人身安全保障制度</th>--%>
          <%--<th bgcolor="#efefef">尽职照单免责和失职照单问责制度</th>--%>
            <th bgcolor="#efefef">执法公众满意度</th>
            <th bgcolor="#efefef">活动总结</th>
<%--            <th bgcolor="#efefef">考核奖惩制度制定及执行证明材料</th>--%>
<%--            <th bgcolor="#efefef">执法人员人身安全保障制度制定及执行证明材料</th>--%>
<%--            <th bgcolor="#efefef">尽职照单免责和失职照单问责制度制定及执行证明材料</th>--%>
<%--            <th bgcolor="#efefef">创新措施证明材料下载</th>--%>
        </tr>
      </thead>
      <tbody>
      	<c:forEach items="${provinceSelectionUnit.list}" var="Units" varStatus="sta">
        <tr>
          <td height="30">${Units.areanamePro}</td>
          <%--
          <td>
          		${Units.polluternumber }
          </td>
          <td>
       			${Units.provincepolluternumber }
          </td>--%>
          <td>
              <a href="javascript:void(0)" onclick="downloadFile('${Units.lawSatisficingFileName}','${Units.lawgzmydName}')">
                      ${Units.lawgzmydName}
              </a>
          </td>
          <td>
              <a href="javascript:void(0)" onclick="downloadFile('${Units.activityreporturl}','${Units.activityreportname}')">
                      ${Units.activityreportname}
              </a>
          </td>
         <%-- <td>
          	<a href="javascript:void(0)" onclick="downloadFile('${Units.assessmentsystemurl}','${Units.assessmentsystemname}')">
          		${Units.assessmentsystemname }
          	</a>
          </td>
          <td>
          	<a href="javascript:void(0)" onclick="downloadFile('${Units.lawenforcnetworkurl}','${Units.lawenforcnetworkname}')">
          		${Units.lawenforcnetworkname }
          	</a>
          </td>
          <td>
          	<a href="javascript:void(0)" onclick="downloadFile('${Units.accountabilityurl}','${Units.accountabilityname}')">
          		${Units.accountabilityname }
          	</a>
          </td>
            <td>
          	<a href="javascript:void(0)" onclick="downloadFile('${Units.initiativesUrl}','${Units.initiativesName}')">
          		${Units.initiativesName}
          	</a>
          </td>--%>
        </tr>
        </c:forEach>
      </tbody>
    </table>
</div>
    <div class="page">
        <span style="padding:12px; float:left; color:#0099cc;">共${provinceSelectionUnit.total}条记录</span>
        <ul class="pagination" id="pageCon">
            
        </ul>
    </div>
</div>




 
 
