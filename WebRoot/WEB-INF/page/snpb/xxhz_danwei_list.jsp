<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
</head>
<body>
<div class="center_weizhi">当前位置：信息汇总- 参选单位列表</div>
<div class="center">
<div class="center_list">
    <div class="panel-group" id="accordion">
    	<button type="button" class="btn btn-danger" id ="viewExcel" >导出EXCEL</button> 
    </div>
        <table class="table table-bordered table-hover table-condensed">
         <thead>
         <tr>
             <th width="40" height="30" bgcolor="#efefef">序号</th>
             <th bgcolor="#efefef">省</th>
             <th bgcolor="#efefef">地市</th>
             <th bgcolor="#efefef">区县</th>

             <th bgcolor="#efefef">典型案例采纳情况支撑材料</th>
             <th bgcolor="#efefef">制度创新与执法任务支撑材料</th>
			 <th bgcolor="#efefef">发现问题能力支撑材料</th>
             <th bgcolor="#efefef">执法公众满意度支撑材料</th>
             <th bgcolor="#efefef">专项行动表现支撑材料</th>
             <th bgcolor="#efefef">专项行动-查处弄虚作假违法犯罪支撑材料</th>
           </tr>
         </thead>
         <tbody>
            <c:forEach items="${provinceSelectionUnit.list}" var="item" varStatus="status">
	           <tr>
	             <td height="30" align="center">${status.index+1}<input id="id${status.index+1}" type="hidden"  value="${item.id}"></td>
	             <td>${item.provincePro}</td>
	             <td>${item.cityPro}</td>
	             <td>${item.countryPro}</td>
	             <%--<td>
	             	&lt;%&ndash; <c:if test="${ not empty item.polluterevidenceurl}">
	             		<button class="btn btn-danger btn-xs" onclick="javascript:window.location.href='${item.polluterevidenceDownUrl}'">              下载</button>
	             	</c:if> &ndash;%&gt;
	             	<a href="javascript:void(0)" onclick="downloadFile('${item.polluterevidenceurl}','${item.polluterevidencename}')">
		             	<c:choose>
					 		<c:when test="${fn:length(item.polluterevidencename) >15 }">
					 			${fn:substring(item.polluterevidencename,0,15)}...
					 		</c:when>
					 		<c:otherwise>
					 			${item.polluterevidencename}
					 		</c:otherwise>
					 	</c:choose>
	             	</a>
				 </td>--%>
	             <td>
	             	<%-- <c:if test="${ not empty item.deedevidenceurl}">
	             		<button class="btn btn-danger btn-xs" onclick="javascript:window.location.href='${item.deedevidenceDownUrl}'">下载</button>
	             	</c:if> --%>
	             	<a href="javascript:void(0)" onclick="downloadFile('${item.typicalCaseAcceptUrl}','${item.typicalCaseAcceptName}')">
	             		<c:choose>
					 		<c:when test="${fn:length(item.typicalCaseAcceptName) >8 }">
					 			${fn:substring(item.typicalCaseAcceptName,0,8)}...
					 		</c:when>
					 		<c:otherwise>
					 			${item.typicalCaseAcceptName}
					 		</c:otherwise>
					 	</c:choose>
	             	</a>
				 </td>
				 <td>
				 	<%-- <c:if test="${ not empty item.deedpictureurl}">
	             		<button class="btn btn-danger btn-xs" onclick="javascript:window.location.href='${item.deedpictureDownUrl}'">下载</button>
	             	</c:if> --%>
	             	<a href="javascript:void(0)" onclick="downloadFile('${item.zdcxyzfrwzcclUrl}','${item.zdcxyzfrwzcclName}')">
	             		<c:choose>
					 		<c:when test="${fn:length(item.zdcxyzfrwzcclName) >8 }">
					 			${fn:substring(item.zdcxyzfrwzcclName,0,8)}...
					 		</c:when>
					 		<c:otherwise>
					 			${item.zdcxyzfrwzcclName}
					 		</c:otherwise>
					 	</c:choose>
	             	</a>
	             </td>

				   <td>
						   <%-- <c:if test="${ not empty item.deedpictureurl}">
                               <button class="btn btn-danger btn-xs" onclick="javascript:window.location.href='${item.deedpictureDownUrl}'">下载</button>
                           </c:if> --%>
					   <a href="javascript:void(0)" onclick="downloadFile('${item.findQuestionFileUrl}','${item.findQuestionFileName}')">
						   <c:choose>
							   <c:when test="${fn:length(item.findQuestionFileName) >8 }">
								   ${fn:substring(item.findQuestionFileName,0,8)}...
							   </c:when>
							   <c:otherwise>
								   ${item.findQuestionFileName}
							   </c:otherwise>
						   </c:choose>
					   </a>
				   </td>

	             <td>
	             	<%-- <c:if test="${ not empty item.publicityinformationurl}">
	             		<button class="btn btn-danger btn-xs" onclick="javascript:window.location.href='${item.publicityinformationDownUrl}'">下载</button>
	             	</c:if> --%>
	             	<a href="javascript:void(0)" onclick="downloadFile('${item.lawgzmydUrl}','${item.lawgzmydName}')">
	             		<c:choose>
					 		<c:when test="${fn:length(item.lawgzmydName) >8 }">
					 			${fn:substring(item.lawgzmydName,0,8)}...
					 		</c:when>
					 		<c:otherwise>
					 			${item.lawgzmydName}
					 		</c:otherwise>
					 	</c:choose>
	             	</a>
	             </td>
	             <td>
	             	<%-- <c:if test="${ not empty item.publicityinformationurl}">
	             		<button class="btn btn-danger btn-xs" onclick="javascript:window.location.href='${item.activityreportDownUrl}'">下载</button>
	             	</c:if> --%>
	             	<a href="javascript:void(0)" onclick="downloadFile('${item.citySpecialActionShowUrl}','${item.citySpecialActionShowName}')">
	             		<c:choose>
					 		<c:when test="${fn:length(item.citySpecialActionShowName) >8 }">
					 			${fn:substring(item.citySpecialActionShowName,0,8)}...
					 		</c:when>
					 		<c:otherwise>
					 			${item.citySpecialActionShowName}
					 		</c:otherwise>
					 	</c:choose>
	             	</a>
	             </td>
	             <td>
<%--	             	&lt;%&ndash; <c:if test="${ not empty item.publicityinformationurl}">--%>
<%--	             		<button class="btn btn-danger btn-xs" onclick="javascript:window.location.href='${item.selfevaluationreportDownUrl}'">下载</button>--%>
<%--	             	</c:if> &ndash;%&gt;--%>
	             	<a href="javascript:void(0)" onclick="downloadFile('${item.ccnxzjwffzCaseFileUrl}','${item.ccnxzjwffzCaseFileName}')">
	             		<c:choose>
					 		<c:when test="${fn:length(item.ccnxzjwffzCaseFileName) >8 }">
					 			${fn:substring(item.ccnxzjwffzCaseFileName,0,8)}...
					 		</c:when>
					 		<c:otherwise>
					 			${item.ccnxzjwffzCaseFileName}
					 		</c:otherwise>
					 	</c:choose>
	             	</a>
	             </td>
	            <%-- <td>
	             	&lt;%&ndash; <c:if test="${ not empty item.jiliurl}">
	             		<button class="btn btn-danger btn-xs" onclick="javascript:window.location.href='${item.jiliDownUrl}'">             下载</button>
	             	</c:if> &ndash;%&gt;
	             	<a href="javascript:void(0)" onclick="downloadFile('${item.jiliurl}','${item.jiliname}')">
	             		<c:choose>
					 		<c:when test="${fn:length(item.jiliname) >8 }">
					 			${fn:substring(item.jiliname,0,8)}...
					 		</c:when>
					 		<c:otherwise>
					 			${item.jiliname}
					 		</c:otherwise>
					 	</c:choose>
	             	</a>
				 </td>--%>
	           </tr>
           </c:forEach>
         </tbody>
       </table>
       <input type="hidden" value="${areaType}" id ="areaType">
    </div>
     <!--列表翻页 开始-->
    <div class="page">
    	<span style="padding:12px; float:left; color:#0099cc;">共${provinceSelectionUnit.total}条记录</span>
        <ul class="pagination" id="pageCon">
            
        </ul>
    </div>
    <!--列表翻页 结束-->
</div>
<script type="text/javascript">

function downloadFile(url,fileName){
	if(url!=null&&url!=""){
		window.location.href="${webpath}/filedownload.do?url="+url+"&fileName="+fileName;
	}else{
		swal({title: "提示",text: "您下载的附件不存在！",type:"info"});
	}
}

$("#viewExcel").click(function(){
	window.location.href= WEBPATH+'/provinceScore/downDanWeiExcel.do?areacode=${areacode}';
});

//分页
$(document).ready(function(){
	var curentPage = eval('${provinceSelectionUnit.pageNum}');
	var totalPage = eval('${provinceSelectionUnit.pages}');
	if(totalPage>0){
		var options = {
			bootstrapMajorVersion: 3,
		    currentPage: curentPage,
		    totalPages: totalPage,
		    numberOfPages: 5,
		    itemTexts: function (type, page, current) {
		            switch (type) {
		            case "first":
		                return "首页";
		            case "prev":
		                return "&laquo;";
		            case "next":
		                return "&raquo;";
		            case "last":
		                return "尾页";
		            case "page":
		                return page;
		            }
		    },
		    onPageClicked: function (event, originalEvent, type, page) {
            	business.addMainContentParserHtml(WEBPATH+'/provinceScore/goUnitList.do?pageNum='+page+'&areacode=${areacode}',$("#searchForm").serialize());
            }
    	};
    	$('#pageCon').bootstrapPaginator(options);
   	}
});

</script>
</body>
</html>
