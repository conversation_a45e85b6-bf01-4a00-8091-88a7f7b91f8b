<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
<script type="text/javascript">
//保存数据方法
$('#saveYBXZCFBtn').click(function() {
	var validate = false;
	$("#ybxzcfForm").data('formValidation').validate();
	validate = $("#ybxzcfForm").data('formValidation').isValid();
	var id=$("#ybxzcfId").val();
	if(id!=null && id!=""){
		url=WEBPATH+"/snpb/xxcjybxzcfAddOrUpdate.do?id="+id	
	}else{
		url=WEBPATH+"/snpb/xxcjybxzcfAddOrUpdate.do"
	}
	if(validate){
		var options = {
			       url: url,
			       type: 'post',
			       success:function(data){
			           if(data.result=="error"){
			        	   swal({title:"操作失败",text:data.message, type:"error",confirmButtonColor: "#d9534f"});
			               return false;
			           }else if(data.result=="success"){
			        	  swal({title: "操作成功",text: "",type:"success",confirmButtonColor: "#d9534f"});
			        	  business.addMainContentParserHtml('snpb/snpb_caseUpdate.do?areacodePro=${areacodePro}&areanamePro=${joinunitPro}&areatypePro=${areatypePro}',null);
			           }else{
			        	   swal({title: "系统异常",text:"请刷新菜单或重新登录！",type:"warning",confirmButtonColor: "#d9534f"});
			           }
			           return false;
			     	},error:function(){
			     		swal({title:"服务异常,保存失败!", text:"", type:"error",confirmButtonColor: "#d9534f"});
			     	}
		};
		if($("#typeaheadxxcf1").val()==""){
			swal({title:"操作失败", text:"案卷文号不能为空!", type:"error",confirmButtonColor: "#d9534f"});
		}else{
			$("#ybxzcfForm").ajaxSubmit(options);
		}
	}else if(validate==null){
		//表单未填写
		$("#ybxzcfForm").data('formValidation').validate();
   
    }

});	


$(document).ready(function(){
	
//表单验证
//表单校验
$("#ybxzcfForm").formValidation({
    message: 'This value is not valid',
    icon:{
            valid: 'glyphicon glyphicon-ok',
            invalid: 'glyphicon glyphicon-remove',
            validating: 'glyphicon glyphicon-refresh'
    },
    fields: {
    	"filetypePro": {
            validators: {
            	notEmpty: {
                    message: '请选择案卷类型'
                }
            }
        },
        /*"filesimpledescPro": {
            validators: {
           	 notEmpty: {
                 message: '请填写案卷材料简版说明'
             },
            	stringLength: {
                    min: 1,
                    max: 300,
                    message: '请输入1-300个字符'
                },
                regexp:{
                    message:'不允许输入非法的字符<、>',
                    regexp:/^(?!.*(\<|\>))/
               }
            }
        },
        "filedetiaildescPro": {
            validators: {
            	notEmpty: {
                    message: '请填写案卷材料详版说明'
                },
            	stringLength: {
                    min: 1,
                    max: 2000,
                    message: '请输入1-2000个字符'
                },
                regexp:{
                    message:'不允许输入非法的字符<、>',
                    regexp:/^(?!.*(\<|\>))/
               }
            }
        },*/
		"ispublicPro": {
            validators: {
                notEmpty: {
                    message: '请选择处罚情况是否在门户网站公开'
                }
            }
        },
        "publicaddressPro": {
        	enabled:false,
            validators: {
            	notEmpty: {
                    message: '请输入信息公开网址'
                },
                regexp: {
	                    regexp: 
	                    	/^([hH][tT]{2}[pP]:\/\/|[hH][tT]{2}[pP][sS]:\/\/)(?!.*(\<|\>))/,
                    message: '请输入正确网址'
	                }
            }
        }
        
    }
});


	clicks($("#ispublicPro_Id").val());
});
//关联验证
$("#ispublicPro_Id").on('change', function(){
	clicks($(this).val());
});

function clicks(val){
    if(val == '1'){
    	$('#ybxzcfForm').formValidation('enableFieldValidators','publicaddressPro',true);
    	$('#publicaddressPro').prop('disabled', false);
    }else if(val == '0'){
    	$('#ybxzcfForm').formValidation('enableFieldValidators','publicaddressPro',false);
    	$('#publicaddressPro').prop('disabled', true);
    	$('#publicaddressPro').val("");
    }
}

// function clicks(){
//     if($("#ispublicPro_Id").val() == '1'){
    
//     	$('#publicaddressPro').prop('disabled', false);
//     }else if($("#ispublicPro_Id").val() == '0'){
    
//     	$('#publicaddressPro').prop('disabled', true);
//     	$('#publicaddressPro').val("")
//     }
// }
var resetSelect=function(){
	 $("#filecodexxcf1").val("");
	 $("#belongareacodexxcf1").val("");
	 $("#xxcf1oldid").val("");
	 $("#typeaheadxxcf1").val(null).trigger("change");
	 
	 $('#saveXxcjPersonForm').data('formValidation').resetForm(); 
}
//初始化加载案件一select2
$("#typeaheadxxcf1").select2({
	  language:'zh-CN',
	  data:[{id:'${xxcjProFilesList.oldidPro}'+'${xxcjProFilesList.filetypePro}',text:'${xxcjProFilesList.filecodePro}'}],
	  allowClear:true
	}).val(['${xxcjProFilesList.oldidPro}'+'${xxcjProFilesList.filetypePro}']).trigger("change").select2({
	  language:'zh-CN',
	  ajax: {
	    url: "${webpath}/xxcj/selectAnjuanByAreacode2020.do",
	    dataType: 'json',
	    delay: 500,
	    type:'POST',
	    data: function (params) {
	    	/* if($("#areatypePro").val()==2){
	    		 var subAreaCode='${subAreaCode}';
			      var areacode="";
			      if(subAreaCode=='11'||subAreaCode=='12'||subAreaCode=='55'||subAreaCode=='31'||subAreaCode=='66'){//直辖市下面的区,相当于别的县
			    	  areacode=$("#areacodePro").val().substring(0,6);
			      }else{
			    	  areacode=$("#areacodePro").val().substring(0,4);
			      }
	    	}else if($("#areatypePro").val()==1){
	    		 areacode=$("#areacodePro").val().substring(0,2);
	    	}else if($("#areatypePro").val()==3){
	    		 areacode=$("#areacodePro").val().substring(0,6);
	    	} */
	    	var areacode=$("#areacodePro").val();
	      return {
	    	query: params.term,
	        filetype:$("#filetype").val(),
	        qtype:1,
	        areacode:areacode,
	        isClosed:1
	      };
	    },
	    processResults: function (data, params) {
	      return {
	        results: data
	      };
	    },
	    cache: true
	  },
	  escapeMarkup: function (markup) 
	  { 
		  return markup; 
	  },
	  templateResult:function (result) {
	        return result.text;
     },
	  templateSelection:function (result) {
	        return result.text;
     },
	  minimumInputLength:3,
	  theme:'bootstrap',
	  placeholder:'请选择决定文书号/移送编号',
	  allowClear:true
	});
	//绑定选择select事件
	$('#typeaheadxxcf1').on('select2:select', function (evt) {
		var res = $("#typeaheadxxcf1").select2("data");
		$("#filecodexxcf1").val(res[0].text);
		$("#belongareacodexxcf1").val(res[0].areacode);
		$("#xxcf1oldid").val(res[0].oldid);
	});
	//绑定取消选择select事件
	$('#typeaheadxxcf1').on('select2:unselecting', function (evt) {
		$("#filecodexxcf1").val("");
		$("#belongareacodexxcf1").val("");
		$("#xxcf1oldid").val("");
	});
</script>
</head>
<body>
<div class="center_weizhi"><span style="float: left;">当前位置：推荐报送- 审核报送 - 报送国家</span><span style="float: right;padding-right: 10px;"><a href="javascript:void(0);" onclick="macroMgr.onLevelTwoMenuClick(null, 'snpb/snpb_list.do')">
<i class="fa fa-chevron-left"></i>返回</a></span></div>

<div class="center">
<div class="frame_center">
<form id="ybxzcfForm" method="post" class="form-horizontal">
<input type="hidden" class="form-control" id="ybxzcfId" name="ybxzcfId" value="${xxcjProFilesList.id }">
<input type="hidden" id="areacodePro" name="areacodePro" value="${areacodePro}">
  <input type="hidden" id="areatypePro" name="areatypePro" value="${areatypePro}">
        <table  class="table_input">
         <tbody>
           <tr>
             <td width="300">参选单位</td>
             	<c:choose>
           			<c:when test="${xxcjProFilesList.id==null}">
           			  <td style="text-align:left;">${joinunitPro}</td>
           			</c:when>
           			<c:otherwise>
           			   <td style="text-align:left;">${xxcjProFilesList.joinunitPro}</td>
           			</c:otherwise>
           		</c:choose>
             
           </tr>
           
           <tr>
           	<td colspan="2">
           	<div class="shangbao_panel">
                <div class="shangbao_titlt">行政处罚案卷</div>
                <table style="width:100%;" class="table_input">
                	<tr>
                    <td width="224">案卷类型<i class="ace-icon fa fa-asterisk" style="color:red"></i></td>
                    <td>
                    <div class="form-group">
		             	<div class="col-sm-12">
                    <select name="filetypePro" class="form-control" id="filetype" onchange="resetSelect();">
		                 <option value="0" <c:if test="${xxcjProFilesList.filetypePro==0}">selected</c:if> >一般行政处罚</option>
                    </select>
                     </div>
		             	</div>
                    </td>
                  </tr>
                  <tr>
                    <td>案卷文号<i class="ace-icon fa fa-asterisk" style="color:red"></i></td>
                    <td style="text-align: left;">
		             	<div class="form-group">
		             	<div class="col-sm-12">
			                 <select id="typeaheadxxcf1" class="form-control" >
							 </select>
			                 <input type="hidden"name="belongareacodePro" id="belongareacodexxcf1" value="${xxcjProFilesList.belongareacodePro}"/>
			                 <input type="hidden" name="filecodePro" id="filecodexxcf1" value="${xxcjProFilesList.filecodePro}"/>
			                 <input type="hidden" name="oldidPro" id="xxcf1oldid" value="${xxcjProFilesList.oldidPro}"/>
			                 <input type="hidden" name="id" value="${xxcjProFilesList.id }"/>
			                 </div>
		             	</div>
		             </td>
                  </tr>
                   <%--<tr>
                     <td>案卷材料简版说明<i class="ace-icon fa fa-asterisk" style="color:red"></i></td>
                     <td><div class="form-group"> <div class="col-sm-12"><textarea  rows="6" class="form-control" id="filesimpledescPro_Id" name="filesimpledescPro" placeholder="请输入案卷材料简版说明">${xxcjProFilesList.filesimpledescPro }</textarea></div> </div></td>
                   </tr>
                   <tr>
                     <td>案卷材料详版说明<i class="ace-icon fa fa-asterisk" style="color:red"></i></td>
                     <td><div class="form-group"> <div class="col-sm-12"><textarea  rows="12" class="form-control" id="filedetiaildescPro_Id" name="filedetiaildescPro" placeholder="请输入案卷材料详版说明">${xxcjProFilesList.filedetiaildescPro }</textarea></div> </div></td>
                   </tr>--%>
                   <tr>
                     <td width="224">处罚情况是否在门户网站公开<i class="ace-icon fa fa-asterisk" style="color:red"></i></td>
                     <td>
                     <div class="form-group">
				                 <div class="col-sm-12">
			                     <select id="ispublicPro_Id" name="ispublicPro" class="form-control">
			                       <option value="">请选择</option>
			                       <option value="1" <c:if test="${xxcjProFilesList.ispublicPro==1}">selected</c:if> >是</option>
			                       <option value="0" <c:if test="${xxcjProFilesList.ispublicPro==0}">selected</c:if> >否</option>
			                     </select>
			                     </div>
			                </div>
                     </td>
                   </tr>
                   <tr>
                     <td>信息公开网址</td>
                     <td><div class="form-group"> <div class="col-sm-12"><input type="text" class="form-control" id="publicaddressPro" name="publicaddressPro" placeholder="请输入信息公开网址" value="${xxcjProFilesList.publicaddressPro }"></div></div></td>
                   </tr>
                </table>
            </div>
            </td>
           </tr>           
           <tr>
             <td align="center">&nbsp;</td>
             <td style="text-align:left;"><a href="#"><button id="saveYBXZCFBtn" type="button" class="btn btn-danger" style="font-size:16px;width:150px; margin-top:5px;">信息保存</button></a></td>
           </tr>
         </tbody>
       </table>
       </form>
    </div>
    
</div>

</body>
</html>
