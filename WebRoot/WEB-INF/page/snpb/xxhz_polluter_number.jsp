<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
</head>
<body>
<div class="center_weizhi">当前位置：推荐报送- 信息汇总 - 重点排污单位数量列表</div>
<div class="center">
<div class="center_list">
    <div class="panel-group" id="accordion">
            <!---信息填报
            <div class="btn-group" style="margin-right:20px;float:left;">
               <a href="CementEnterprises_input.html"><button type="button" class="btn btn-success" style="font-size:16px;">企业信息录入</button></a>
            </div>--->        
              
           <!---快速查询--->
        <form id= "searchForm"  role="form">
        	<c:choose>
         	<c:when test="${sessionScope.sa_session.loginid == 'changneng'||sessionScope.sa_session.loginid == 'sysAdmin' }">
<%-- 				<c:if test="${areaUser.areaCode == '45000000' }"> --%>
              <select id="areacodePro" name="areacodePro" class="form-control" style="width:150px;margin-right:5px;">
				 <option value="" <c:if test="${areatypePro==''}">selected</c:if>>请选择省份</option>
                 <option value="11000000" <c:if test="${areacodePro=='11000000'}">selected</c:if>>北京市</option>
                 <option value="12000000" <c:if test="${areacodePro=='12000000'}">selected</c:if>>天津市</option>
                 <option value="13000000" <c:if test="${areacodePro=='13000000'}">selected</c:if>>河北省</option>
                 <option value="14000000" <c:if test="${areacodePro=='14000000'}">selected</c:if>>山西省</option>
                 <option value="15000000" <c:if test="${areacodePro=='15000000'}">selected</c:if>>内蒙古自治区</option>
                 <option value="16000000" <c:if test="${areacodePro=='16000000'}">selected</c:if>>河南省</option>
                 <option value="21000000" <c:if test="${areacodePro=='21000000'}">selected</c:if>>辽宁省</option>
                 <option value="22000000" <c:if test="${areacodePro=='22000000'}">selected</c:if>>吉林省</option>
                 <option value="23000000" <c:if test="${areacodePro=='23000000'}">selected</c:if>>黑龙江省</option>
                 <option value="31000000" <c:if test="${areacodePro=='31000000'}">selected</c:if>>上海市</option>
                 <option value="32000000" <c:if test="${areacodePro=='32000000'}">selected</c:if>>江苏省</option>
                 <option value="33000000" <c:if test="${areacodePro=='33000000'}">selected</c:if>>浙江省</option>
                 <option value="34000000" <c:if test="${areacodePro=='34000000'}">selected</c:if>>安徽省</option>
                 <option value="35000000" <c:if test="${areacodePro=='35000000'}">selected</c:if>>福建省</option>
                 <option value="36000000" <c:if test="${areacodePro=='36000000'}">selected</c:if>>江西省</option>
                 <option value="37000000" <c:if test="${areacodePro=='37000000'}">selected</c:if>>山东省</option>
                 <option value="42000000" <c:if test="${areacodePro=='42000000'}">selected</c:if>>湖北省</option>
                 <option value="43000000" <c:if test="${areacodePro=='43000000'}">selected</c:if>>湖南省</option>
                 <option value="44000000" <c:if test="${areacodePro=='44000000'}">selected</c:if>>广东省</option>
                 <option value="45000000" <c:if test="${areacodePro=='45000000'}">selected</c:if>>广西壮族自治区</option>
                 <option value="46000000" <c:if test="${areacodePro=='46000000'}">selected</c:if>>海南省</option>
                 <option value="51000000" <c:if test="${areacodePro=='51000000'}">selected</c:if>>四川省</option>
                 <option value="52000000" <c:if test="${areacodePro=='52000000'}">selected</c:if>>贵州省</option>
                 <option value="53000000" <c:if test="${areacodePro=='53000000'}">selected</c:if>>云南省</option>
                 <option value="54000000" <c:if test="${areacodePro=='54000000'}">selected</c:if>>西藏自治区</option>
                 <option value="55000000" <c:if test="${areacodePro=='55000000'}">selected</c:if>>重庆市</option>
                 <option value="61000000" <c:if test="${areacodePro=='61000000'}">selected</c:if>>陕西省</option>
                 <option value="62000000" <c:if test="${areacodePro=='62000000'}">selected</c:if>>甘肃省</option>
                 <option value="63000000" <c:if test="${areacodePro=='63000000'}">selected</c:if>>青海省</option>
                 <option value="64000000" <c:if test="${areacodePro=='64000000'}">selected</c:if>>宁夏回族自治区</option>
                 <option value="65000000" <c:if test="${areacodePro=='65000000'}">selected</c:if>>新疆维吾尔自治区</option>
                 <option value="66000000" <c:if test="${areacodePro=='66000000'}">selected</c:if>>新疆建设兵团</option>
               </select>
<%--                </c:if> --%>
             </c:when>
             <c:otherwise>
              <select class="form-control" style="width:150px;margin-right:5px;"  name="areatypePro" id="areatypePro">
                 <option value="" <c:if test="${areatypePro==''}">selected</c:if>>请选择查询类型</option>
                 <option value="1" <c:if test="${areatypePro=='1' }">selected</c:if> >省级</option>
                 <option value="2" <c:if test="${areatypePro=='2' }">selected</c:if> >市级</option>
                 <option value="3" <c:if test="${areatypePro=='3' }">selected</c:if> >县级</option>
              </select>
             </c:otherwise>
             </c:choose>
        </form>
            <!---搜索--->
            
            <div style="width:260px;" class="btn-group">
                      <a href ="#"><button type="button" id ="viewExcel" class="btn btn-danger">导出EXCEL</button></a>  
            </div>            
    </div>
        <table class="table table-bordered table-hover table-condensed" style="table-layout:fixed;">
         <thead>
           <tr>
             <th height="30" bgcolor="#efefef" style="vertical-align:middle;width:50px;">序号</th>
             <th bgcolor="#efefef"style="vertical-align:middle;" >省</th>
             <th bgcolor="#efefef" style="vertical-align:middle;">地市</th>
             <th bgcolor="#efefef" style="vertical-align:middle;">区县</th>
             <th bgcolor="#efefef" style="vertical-align:middle;">行政区域内重点排污企业数量</th>
             <th bgcolor="#efefef" style="vertical-align:middle;">公开方式</th>
             <th bgcolor="#efefef" style="vertical-align:middle;">公开网址</th>
             <th bgcolor="#efefef" style="vertical-align:middle;">其他公开描述</th>
             <th bgcolor="#efefef" style="vertical-align:middle;">重点排污企业证明材料下载</th>
           </tr>
         </thead>
         <tbody>
               <c:forEach items="${provinceSelectionUnit.list}" var="item" varStatus="status">
	           <tr>
	             <td height="30" style="vertical-align:middle;"align="center">${status.index+1}</td>
	             <td style="vertical-align:middle;">${item.provincePro}</td>
	             <td style="vertical-align:middle;">${item.cityPro}</td>
	             <td style="vertical-align:middle;">${item.countryPro}</td>
	             <td style="vertical-align:middle;">${item.polluternumber}</td><!-- 行政区域内国控企业数量 -->
	             <td style="vertical-align:middle;">
	             	<c:if test="${item.publicityway=='1'}">
	             		网站公开
	             	</c:if>
	             	<c:if test="${item.publicityway=='2'}">
	             		其他公开方式
	             	</c:if>
	             </td><!-- 公开方式 -->
	             <td style="width:200px; word-break:break-all;">${item.website}</td><!-- 公开网址 -->
	             <td>${item.publicitywaydescribe}</td><!-- 其他公开描述 -->
	             <td>
	             	<a href="javascript:void(0)" onclick="downloadFile('${item.polluterevidenceurl}',' ${item.polluterevidencename}')">
	             		<c:choose>
					 		<c:when test="${fn:length(item.polluterevidencename) >15 }">
					 			${fn:substring(item.polluterevidencename,0,15)}...
					 		</c:when>
					 		<c:otherwise>
					 			${item.polluterevidencename}
					 		</c:otherwise>
					 	</c:choose>
	             	</a> 
	             </td>
	             <%-- <td style="vertical-align:middle;">
	             	<c:if test="${item.polluterevidencename!=null and item.polluterevidencename!=''}" > 
	             		<button class="btn btn-success btn-xs" onclick="downloadFile('${item.polluterevidenceurl}',' ${item.polluterevidencename}')">下载</button>
	             	</c:if>
	             </td> --%>
	           </tr>
           </c:forEach> 
         </tbody>
       </table>
    </div>
 	<div class="page">
    	<span style="padding:12px; float:left; color:#0099cc;">共${provinceSelectionUnit.total}条记录</span>
        <ul class="pagination" id="pageCon">
            
        </ul>
    </div>
</div>
<script >

$("#viewExcel").click(function(){
	var areatypePro = $("#areatypePro").val();
	var areacodePro = $("#areacodePro").val();
	if(areatypePro == null){
		window.location.href= WEBPATH+'/recom/exportExcle.do?areacodePro='+areacodePro+'&areatypePro='+null;
	}else{
		window.location.href= WEBPATH+'/recom/exportExcle.do?areatypePro='+areatypePro+'&areacodePro='+null;
	}
});
//搜索条件
$(document).ready(function(){
	$("#areatypePro").change(function(){
		business.addMainContentParserHtml("recom/polluterDataList.do",$("#searchForm").serialize());
	});
	$("#areacodePro").change(function(){
		business.addMainContentParserHtml("recom/polluterDataList.do",$("#searchForm").serialize());
	});
});
//分页
$(document).ready(function(){
	var curentPage = eval('${provinceSelectionUnit.pageNum}');
	var totalPage = eval('${provinceSelectionUnit.pages}');
	var areatypePro = eval('${areatypePro}');
	if(totalPage>0){
		var options = {
			bootstrapMajorVersion: 3,
		    currentPage: curentPage,
		    totalPages: totalPage,
		    numberOfPages: 5,
		    itemTexts: function (type, page, current) {
		            switch (type) {
		            case "first":
		                return "首页";
		            case "prev":
		                return "&laquo;";
		            case "next":
		                return "&raquo;";
		            case "last":
		                return "尾页";
		            case "page":
		                return page;
		            }
		    },
		    onPageClicked: function (event, originalEvent, type, page) {
            	business.addMainContentParserHtml(WEBPATH+'/recom/polluterDataList.do?pageNum='+page,$("#searchForm").serialize());
            }
    	};
    	$('#pageCon').bootstrapPaginator(options);
   	}
});
//下载
function downloadFile(url,fileName){

	if(url != null && url != '' && url != "undefined"){
		window.location.href="${webpath}/filedownload.do?url="+url+"&fileName="+fileName;
	}else{
		swal( "操作失败","该材料不存在!", "error");
		}
	}
	
	
$(document).ready(function() {
	$(".topnav").accordion({
		accordion:false,
		speed: 500,
		closedSign: '[+]',
		openedSign: '[-]'
	});
});

</script>
</body>
</html>
