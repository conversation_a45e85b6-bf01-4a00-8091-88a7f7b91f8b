<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
<script type="text/javascript">
var RecommendId;
function deleteRecommend(id){
	 swal({
        title: "您确定执行此操作吗？",
        text: "您确定要删除当前区划吗？",
        type: "warning",
        showCancelButton: true,
        closeOnConfirm: false,
        confirmButtonText: "是的，我要删除",
        confirmButtonColor: "#d9534f"
    }, function(){
        $.ajax({
            url: WEBPATH+"/recom/deleteRecommendCity.do",
            type: "POST",
            data:{"id":id}
        }).done(function(data) {

            if(data.result=="success"){
            	swal("操作成功!", "已成功删除！", "success");
            	macroMgr.onLevelTwoMenuClick(null, 'recom/RecommendCountryDeed.do');
            }else{
                swal("OMG", "删除失败了!", "error");
            }
            
        }).error(function(data) {
            swal("OMG", "所选行政区为空,无法保存", "error");
        });
    });
}
//在县级候选推荐页面点击“增加参选单位”按钮进入推荐模态框时加载本市下的所有市级
function selectCity(){
	$("#zpdf").html("");
	$("#ranking").html("");
	if($("#list").val() < 6){
		$("#myModal").modal("show")
	$.ajax({
		url: WEBPATH+"/recom/selectCityList.do",
		type:'post',
		success:function(data){
			var res = data;
			var option;
			var option1 = "<option value='0'>"+'请选择行政区'+"</option>";
			$.each(res,function(i,n){//循环，i为下标从0开始，n为集合中对应的第i个对象
				
				option += "<option value='"+n.code+"'>"+n.name+"</option>"
			});
			$("#first").html(option1+option);//将循环拼接的字符串插入第二个下拉列表
			$("#first").show();
		},
		error:function(){
			 swal("OMG", "请求错误", "error");
		}
	})
	}else{
		swal("不可增加", "候选县级6个已满", "error");
	}
}
//通过选择的市级code加载本市下已上报的所有县级
function selectCountry(areacodePro){
	$("#zpdf").html("");
	$("#ranking").html("");
	if(areacodePro!=0){
		
	$.ajax({
		url: WEBPATH+"/recom/selectCountrys.do",
		type:'post',
		data:{"areacodePro":areacodePro},
		success:function(data){
			var res = data;
			var option;
			var option1 = "<option value='0'>"+'请选择行政区'+"</option>";
			$.each(res,function(i,n){//循环，i为下标从0开始，n为集合中对应的第i个对象
				
				option += "<option value='"+n.id+"'>"+n.areanamePro+"</option>"
			});
			$("#second").html(option1+option);//将循环拼接的字符串插入第二个下拉列表
			$("#second").show();
		},
		error:function(){
			 swal("OMG", "请求错误", "error");
		}
	})
}
}
//根据所选县级的id查询此县级的信息
function select(id){
	$("#zpdf").html("");
	$("#ranking").html("");
	if(id!=0){
	$.ajax({
		url: WEBPATH+"/recom/selectCityById.do",
		type:'post',
		data:{"id":id},
		success:function(data){
			//var aa='<input type="hidden" class="form-control" name="isrecommend" id="isrecommend" value="+'data.id'+">';
			//$("#recommend").html(aa);
			RecommendId=data.id;
			$("#zpdf").html(data.altogetherscore);
			$("#ranking").html(data.provinceranking);
			
		},
		error:function(){
			
		}
	})
	}	
}
//保存推荐使用
$('#savebtn').click(function(){
// 	saveEvent();
	if(RecommendId != null && RecommendId != "" && RecommendId !="undefined"){
	$.ajax({
		url: WEBPATH+"/recom/updateRecommendCityById.do",
		type:'post',
		data:{"id":RecommendId},
		success:function(data){
			if(data.result=="error"){
				swal("推荐失败!", "", "error");
                return false;
             }else if(data.result=="success"){
             	swal({
				    	title: "推荐成功!",
				        type: "success",
				        closeOnConfirm: true,
				        confirmButtonText: "确定",
				        confirmButtonColor: "#d9534f"
			    	}, function() {
			    		RecommendId=null;
				    	business.addMainContentParserHtml('recom/RecommendCountryDeed.do','');
				});
               return false;
             }	
		},
		error:function(){
			 swal("OMG", "请求错误", "error");
		}
	});
	}else{
		swal("推荐失败", "请选择要推荐的县级再点击确定", "error");
	}
	$('#myModal').modal('hide');
	});
//提交
$('#submitBtn').click(function(){
	var aa=$("#list").val();
	if($("#list").val() <= 6){
	$.ajax({
		url: WEBPATH+"/recom/submitRecommendCounty.do",
		type:'post',
		success:function(data){
			if(data.result=="error"){
				swal("提交失败!", "", "error");
                return false;
             }else if(data.result=="success"){
             	swal({
				    	title: "提交成功!",
				        type: "success",
				        closeOnConfirm: true,
				        confirmButtonText: "确定",
				        confirmButtonColor: "#d9534f"
			    	}, function() {
				    	business.addMainContentParserHtml('recom/RecommendCountryDeed.do','');
				});
               return false;
             }	
		},
		error:function(){
			 swal("OMG", "请求错误", "error");
		}
	})
	
	$('#tijiao').modal('hide');
	}else{
		swal("提交失败", "推荐的县级单位不能超过6个", "error");
		$('#tijiao').modal('hide');
	}
	});
</script>
</head>
<body>
<div class="center_weizhi">当前位置：推荐报送 - 集体和个人推荐 - 候选县级集体推荐</div>
<div class="center">
	<div class="center_list">
    <div class="panel-group" id="accordion">
          
            <!---搜索--->
            <div style="width:100%;" class="btn-group">
            <c:if test="${sysInitConfig.code =='1' && provinceReportUser.electcountyState != 1 && provinceReportUser.reportstate !=1}">
            <c:choose>
   				<c:when test="${provinceReportUser.isDevInternalEva == 1}"> 
   				  	<c:choose>
   				   		<c:when test="${provinceSelectionUnits[0].provinceranking !=null }">
   				   			<button class="btn btn-danger" onclick="selectCity()" type="button" style="width:180px;border-radius: 4px;text-align: center" data-toggle="modal" > 增加参选单位</button>
              				<button type="button" class="btn btn-danger" style="width:180px;border-radius: 4px;margin-left: 20px" data-toggle="modal" data-target="#tijiao">提交</button>
   				   		</c:when>
   				  		<c:otherwise> 

              				<h4 style="color:#F00;" >请确认排完名之后再推荐</h4>

   				  		</c:otherwise>
   				  	</c:choose>
   				</c:when>
   				<c:otherwise> 
   					<button class="btn btn-danger" onclick="selectCity()" type="button" style="text-align: center;border-radius: 4px" data-toggle="modal" > 增加参选单位</button>
              		<button type="button" class="btn btn-danger" style="width:180px; border-radius: 4px;margin-left: 20px" data-toggle="modal" data-target="#tijiao">提交</button>
   				</c:otherwise>
			</c:choose> 
            </c:if>
            <c:if test="${ provinceReportUser.electcountyState ==1 }">
             <h4 style="color:#F00;" >候选县级单位已提交完毕，提交候选县级单位信息如列表所示：</h4>
             </c:if>
            </div>            
    </div>
    <input type="hidden" class="form-control" name="list" id="list" value="${listSize}">
        <table class="table table-bordered table-hover table-condensed">
         <thead>
           <tr>
             <th width="50" height="30" bgcolor="#efefef">序号</th>
             <th bgcolor="#efefef">所属市级</th>
             <th bgcolor="#efefef">县级参选单位</th>
             <th bgcolor="#efefef" style="width:100px;">操作</th>
           </tr>
         </thead>
         <tbody>
            <c:forEach  var="recommendList" items="${recommendList}" varStatus="status">
         		 <tr>
	             <td height="30" align="center">${status.index+1}</td>
	             <td>${recommendList.cityPro}</td>
	             <td>${recommendList.areanamePro}</td>
	             <td style="text-align: center;">
	             	<c:if test="${sysInitConfig.code =='1' && provinceReportUser.electcountyState != 1 && provinceReportUser.reportstate !=1}">
	             		<a href="#"><button class="btn btn-danger btn-xs" onclick="deleteRecommend('${recommendList.id}')">删除</button></a> 
	             	</c:if>
	             </td>
	           </tr>
          	</c:forEach>
                
         </tbody>
       </table>
    </div>
<!--     <div class="page"> -->
<!--         <ul class="pagination"> -->
<!--             <li><a href="#">&laquo;</a></li> -->
<!--             <li class="active"><a href="#">1</a></li> -->
<!--             <li><a href="#">2</a></li> -->
<!--             <li><a href="#">3</a></li> -->
<!--             <li><a href="#">4</a></li> -->
<!--             <li><a href="#">5</a></li> -->
<!--             <li><a href="#">&raquo;</a></li> -->
<!--         </ul> -->
<!--     </div> -->
</div>
<!-- 新增县级参选单位（Modal） -->
<div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title">新增县级参选单位</h4>
            </div>
            <div class="modal-body form-horizontal" style="height:300px; font-size:16px;">
            	<div class="form-group" style="padding:10px;">
                	<label class="col-lg-3 control-label">省份</label>
                    <div class="col-lg-8" style="margin-top:7px;">
                	${areaUser.userName}
                    </div>
                </div>
            	<div class="form-group" style="padding:10px;">
                	<label class="col-lg-3 control-label">市级行政区</label>
                    <div class="col-lg-8">
                		<select id="first" class="form-control" onChange="selectCountry(this.value)" style="width:300px;margin-right:5px;">
                             
                          </select>
                    </div>
                </div>
                <div class="form-group" style="padding:10px;">
                	<label class="col-lg-3 control-label">县级行政区</label>
                    <div class="col-lg-8">
                		<select id="second" class="form-control" onChange="select(this.value)" style="width:300px;margin-right:5px;">
                            
                          </select>
                    </div>
                </div>
                <c:if test="${provinceReportUser.isDevInternalEva == 1}">
                <div class="form-group" style="padding:10px;">
                	<label class="col-lg-3 control-label">自评得分</label>
                    <div id="zpdf" class="col-lg-8" style="margin-top:7px;">
                	
                    </div>
                </div>
                 </c:if>
                <c:if test="${provinceReportUser.isDevInternalEva == 1}">
                <div class="form-group" style="padding:10px;">
                	<label class="col-lg-3 control-label">排名</label>
                    <div id="ranking" class="col-lg-8" style="margin-top:7px;">
                	
                    </div>
                </div>
                 </c:if>
            </div>
            <div class="modal-footer"><button id="savebtn" name="savebtn" type="button" class="btn btn-danger">确定</button>
                <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
<!-- 提交（Modal） -->
<div class="modal fade" id="tijiao" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title">提示</h4>
            </div>
            <div class="modal-body form-horizontal text-center" style="height:200px; font-size:18px; padding:80px 0; color:red;">
            	提交后参选单位将无法更改，是否确认提交？
            </div>
            <div class="modal-footer"><button id="submitBtn" name="submitBtn" type="button" class="btn btn-danger">确定</button>
                <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
<script language="JavaScript">

$(document).ready(function() {
	$(".topnav").accordion({
		accordion:false,
		speed: 500,
		closedSign: '[+]',
		openedSign: '[-]'
	});
});

</script>

</body>
</html>