<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
</head>
<body>
<div class="center_weizhi">当前位置：信息汇总- 未上报案卷单位列表</div>
<div class="center">
<div class="center_list">
    <div class="panel-group" id="accordion">
	    <form id= "searchForm">
	    	<select id="areaType" name="areaType" class="form-control" style="width:150px;margin-right:5px;">
	           <option value="" <c:if test="${areaType==''}">selected</c:if>>请选择查询类型</option>
	           <option value="2" <c:if test="${areaType=='2'}">selected</c:if>>市级</option>
	           <option value="3" <c:if test="${areaType=='3'}">selected</c:if>>县级</option>
	        </select>
	        <span class="input-group-btn">
	           <button id="searchButt"  class="btn btn-danger" type="button">快速搜索</button>
	           <c:if test="${loginArea=='00000000' }">
	           	<button type="button" class="btn btn-warning" id="goback">返回</button>
	           </c:if>
	        </span>
	        
    		<!-- <button type="button" class="btn btn-danger" id ="viewExcel" >导出EXCEL</button> --> 
    		<input type="hidden" value="${subArea}" id="subAreaCode" name="subAreaCode">
    	</form>
    	
    </div>
        <table class="table table-bordered table-hover table-condensed">
         <thead>
         <tr>
             <th width="40" height="30" bgcolor="#efefef">序号</th>
             <th bgcolor="#efefef">省</th>
             <th bgcolor="#efefef">地市</th>
             <th bgcolor="#efefef">区县</th>
           </tr>
         </thead>
         <tbody>
            <c:forEach items="${unitList.list}" var="item" varStatus="status">
	           <tr>
	             <td height="30" align="center">${status.index+1}<input id="id${status.index+1}" type="hidden"  value="${item.code}"></td>
	             <td>${item.province}</td>
	             <td>${item.city}</td>
	             <td>${item.country}</td>
	           </tr>
           </c:forEach>
         </tbody>
       </table>
    </div>
     <!--列表翻页 开始-->
    <div class="page">
    	<span style="padding:12px; float:left; color:#0099cc;">共${unitList.pages}页 &nbsp;&nbsp;&nbsp;&nbsp;${unitList.total}条记录</span>
        <ul class="pagination" id="pageCon">
            
        </ul>
    </div>
    <!--列表翻页 结束-->
</div>
<script type="text/javascript">

$("#searchButt").click(function(){
	business.addMainContentParserHtml("provinceScore/goNoFilesUnitList.do",$("#searchForm").serialize());
});

$("#goback").click(function(){
	business.addMainContentParserHtml("provinceScore/goNoFilesUnitList.do",null);
});


//分页
$(document).ready(function(){
	var curentPage = eval('${unitList.pageNum}');
	var totalPage = eval('${unitList.pages}');
	if(totalPage>0){
		var options = {
			bootstrapMajorVersion: 3,
		    currentPage: curentPage,
		    totalPages: totalPage,
		    numberOfPages: 5,
		    itemTexts: function (type, page, current) {
		            switch (type) {
		            case "first":
		                return "首页";
		            case "prev":
		                return "&laquo;";
		            case "next":
		                return "&raquo;";
		            case "last":
		                return "尾页";
		            case "page":
		                return page;
		            }
		    },
		    onPageClicked: function (event, originalEvent, type, page) {
            	business.addMainContentParserHtml(WEBPATH+'/provinceScore/goNoFilesUnitList.do?pageNum='+page+'&subAreacode=${subArea}',$("#searchForm").serialize());
            }
    	};
    	$('#pageCon').bootstrapPaginator(options);
   	}
});


</script>
</body>
</html>
