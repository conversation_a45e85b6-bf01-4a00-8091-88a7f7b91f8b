<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %> 
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<html>
<head>
</head>
<body>
<div class="center_weizhi">当前位置：推荐报送 - 信息汇总 - 稽查案卷列表</div>
<div class="center">
<div class="center_list">
    <div class="panel-group" id="accordion">
              
           <!---快速查询--->
               <form id= "searchForm" class="bs-example bs-example-form" role="form">
                  <div class="row">                     
                     <div class="col-lg-6">
                                     <c:choose>
         	<c:when test="${sessionScope.sa_session.loginid == 'changneng'||sessionScope.sa_session.loginid == 'sysAdmin' }">
<%--          	<c:if test="${areaUser.areaCode == '45000000' }"> --%>
              <select id="areacodePro" name="areacodePro" class="form-control" style="width:150px;margin-right:5px;">
              	 <option value="" <c:if test="${areatypePro==''}">selected</c:if>>请选择省份</option>
                 <option value="11000000" <c:if test="${areacodePro=='11000000'}">selected</c:if>>北京市</option>
                 <option value="12000000" <c:if test="${areacodePro=='12000000'}">selected</c:if>>天津市</option>
                 <option value="13000000" <c:if test="${areacodePro=='13000000'}">selected</c:if>>河北省</option>
                 <option value="14000000" <c:if test="${areacodePro=='14000000'}">selected</c:if>>山西省</option>
                 <option value="15000000" <c:if test="${areacodePro=='15000000'}">selected</c:if>>内蒙古自治区</option>
                 <option value="16000000" <c:if test="${areacodePro=='16000000'}">selected</c:if>>河南省</option>
                 <option value="21000000" <c:if test="${areacodePro=='21000000'}">selected</c:if>>辽宁省</option>
                 <option value="22000000" <c:if test="${areacodePro=='22000000'}">selected</c:if>>吉林省</option>
                 <option value="23000000" <c:if test="${areacodePro=='23000000'}">selected</c:if>>黑龙江省</option>
                 <option value="31000000" <c:if test="${areacodePro=='31000000'}">selected</c:if>>上海市</option>
                 <option value="32000000" <c:if test="${areacodePro=='32000000'}">selected</c:if>>江苏省</option>
                 <option value="33000000" <c:if test="${areacodePro=='33000000'}">selected</c:if>>浙江省</option>
                 <option value="34000000" <c:if test="${areacodePro=='34000000'}">selected</c:if>>安徽省</option>
                 <option value="35000000" <c:if test="${areacodePro=='35000000'}">selected</c:if>>福建省</option>
                 <option value="36000000" <c:if test="${areacodePro=='36000000'}">selected</c:if>>江西省</option>
                 <option value="37000000" <c:if test="${areacodePro=='37000000'}">selected</c:if>>山东省</option>
                 <option value="42000000" <c:if test="${areacodePro=='42000000'}">selected</c:if>>湖北省</option>
                 <option value="43000000" <c:if test="${areacodePro=='43000000'}">selected</c:if>>湖南省</option>
                 <option value="44000000" <c:if test="${areacodePro=='44000000'}">selected</c:if>>广东省</option>
                 <option value="45000000" <c:if test="${areacodePro=='45000000'}">selected</c:if>>广西壮族自治区</option>
                 <option value="46000000" <c:if test="${areacodePro=='46000000'}">selected</c:if>>海南省</option>
                 <option value="51000000" <c:if test="${areacodePro=='51000000'}">selected</c:if>>四川省</option>
                 <option value="52000000" <c:if test="${areacodePro=='52000000'}">selected</c:if>>贵州省</option>
                 <option value="53000000" <c:if test="${areacodePro=='53000000'}">selected</c:if>>云南省</option>
                 <option value="54000000" <c:if test="${areacodePro=='54000000'}">selected</c:if>>西藏自治区</option>
                 <option value="55000000" <c:if test="${areacodePro=='55000000'}">selected</c:if>>重庆市</option>
                 <option value="61000000" <c:if test="${areacodePro=='61000000'}">selected</c:if>>陕西省</option>
                 <option value="62000000" <c:if test="${areacodePro=='62000000'}">selected</c:if>>甘肃省</option>
                 <option value="63000000" <c:if test="${areacodePro=='63000000'}">selected</c:if>>青海省</option>
                 <option value="64000000" <c:if test="${areacodePro=='64000000'}">selected</c:if>>宁夏回族自治区</option>
                 <option value="65000000" <c:if test="${areacodePro=='65000000'}">selected</c:if>>新疆维吾尔自治区</option>
                 <option value="66000000" <c:if test="${areacodePro=='66000000'}">selected</c:if>>新疆建设兵团</option>
               </select>
<%--                </c:if> --%>
             </c:when>
             </c:choose>
             <div style="width:260px;" class="btn-group">
                        <div class="input-group">
                           <input type="text" class="form-control" style="width:200px;"   name="filecodePro" id="filecodePro" value="${filecodePro}" placeholder="稽查意见文书号关键字">
                           <span class="input-group-btn">
                              <button class="btn btn-danger" id ="search" type="button">快速搜索</button>
                              <button type="button" id ="viewExcel" class="btn btn-danger">导出EXCEL</button>
                           </span>
                        </div><!-- /input-group -->
                     </div><!-- /.col-lg-6 -->
                  </div><!-- /.row -->
               </form>
            </div>    
    </div>
        <table class="table table-bordered table-hover table-condensed" style="table-layout:fixed;">
         <thead>
           <tr>
             <th width="50" height="30" bgcolor="#efefef">序号</th>
             <th bgcolor="#efefef">稽查单位</th>
             <th width="150" bgcolor="#efefef">稽查时间</th>
             <th bgcolor="#efefef">被稽查单位名称</th>
             <th bgcolor="#efefef">稽查意见书文号</th>
             <th bgcolor="#efefef">稽查案卷文本</th>
             <th width="70" bgcolor="#efefef">材料下载</th>
           </tr>
         </thead>
         <tbody>
               <c:forEach items="${xxcjProFilesList.list}" var="item" varStatus="status">
	           <tr>
	             <td height="30" align="center">${status.index+1}</td>
	             <td>${item.checkunitPro}</td>
	             <td><fmt:formatDate value="${item.checkdatePro}" pattern="yyyy-MM-dd"/></td>
	             <td>${item.checkedunitPro}</td>
	             <td>${item.filecodePro}</td>
	              <td>${item.checkfiletextPro}</td>
	              <td>
	             	<c:if test="${item.checkfiletextPro!=null and item.checkfiletextPro!=''}" > 
	             		<button class="btn btn-danger btn-xs" onclick="downloadFile('${item.checkfiletexturlPro}',' ${item.checkfiletextPro}')">下载</button>
	             	</c:if>
	             </td>
	           </tr>
           </c:forEach> 
         </tbody>
       </table>
    </div>
 	<div class="page">
    	<span style="padding:12px; float:left; color:#0099cc;">共${xxcjProFilesList.total}条记录</span>
        <ul class="pagination" id="pageCon">
            
        </ul>
    </div>
</div>
<script >
$("#viewExcel").click(function(){
	var filecodePro = $("#filecodePro").val();
	var areacodePro = $("#areacodePro").val();
	window.location.href= WEBPATH+'/xxcj/JCAJExportExcle.do?filecodePro='+filecodePro+'&areacodePro='+areacodePro;
});
//搜索条件
$(document).ready(function(){
	$("#search").click(function(){
		business.addMainContentParserHtml("xxcj/jcajTotalProList.do",$("#searchForm").serialize());
	});
	$("#areacodePro").change(function(){
		business.addMainContentParserHtml("xxcj/jcajTotalProList.do",$("#searchForm").serialize());
	});
});
//分页
$(document).ready(function(){
	var curentPage = eval('${xxcjProFilesList.pageNum}');
	var totalPage = eval('${xxcjProFilesList.pages}');
	var areatypePro = eval('${areatypePro}');
	if(totalPage>0){
		var options = {
			bootstrapMajorVersion: 3,
		    currentPage: curentPage,
		    totalPages: totalPage,
		    numberOfPages: 5,
		    itemTexts: function (type, page, current) {
		            switch (type) {
		            case "first":
		                return "首页";
		            case "prev":
		                return "&laquo;";
		            case "next":
		                return "&raquo;";
		            case "last":
		                return "尾页";
		            case "page":
		                return page;
		            }
		    },
		    onPageClicked: function (event, originalEvent, type, page) {
            	business.addMainContentParserHtml(WEBPATH+'/xxcj/jcajTotalProList.do?pageNum='+page,$("#searchForm").serialize());
            }
    	};
    	$('#pageCon').bootstrapPaginator(options);
   	}
});

function downloadFile(url,fileName){
	if(url != null && url != '' && url != "undefined"){
		window.location.href="${webpath}/filedownload.do?url="+url+"&fileName="+fileName;
	}else{
		swal( "操作失败","该材料不存在!", "error");
		}
	}
	
$(document).ready(function() {
	$(".topnav").accordion({
		accordion:false,
		speed: 500,
		closedSign: '[+]',
		openedSign: '[-]'
	});
});

</script>
</body>
</html>
