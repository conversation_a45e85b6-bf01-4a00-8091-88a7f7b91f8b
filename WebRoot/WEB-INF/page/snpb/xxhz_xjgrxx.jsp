<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
</head>
<body>
<div class="center_weizhi">当前位置：推荐报送 - 信息汇总 - 候选个人信息列表</div>
<div class="center" style="overflow-x: auto;">
<div class="center_list">
    <div class="panel-group" id="accordion">
              
           <!---快速查询--->
        <form id= "searchForm"  role="form">
                		<input type="hidden" id="areacodePro" name="areacodePro" value="${areacodePro}"/>
        <select class="form-control" style="width:150px;margin-right:5px;"  name="areatypePro" id="areatypePro">
                 <option value="" >请选择查询类型</option>
                 <option value="1" <c:if test="${areatypePro=='1' }">selected</c:if> >省级</option>
                 <option value="2" <c:if test="${areatypePro=='2' }">selected</c:if> >市级</option>
                 <option value="3" <c:if test="${areatypePro=='3' }">selected</c:if> >县级</option>
              </select>
        </form>
            <!---搜索--->
             <div style="width:260px;" class="btn-group">
                 <button id ="search" class="btn btn-danger" type="button">快速搜索</button>
                 <button type="button" id ="viewExcel" class="btn btn-danger">导出EXCEL</button>
            </div> 
    </div>
        <table class="table table-bordered table-hover table-condensed" style="table-layout:fixed;">
         <thead>
           <tr>
             <th height="15" bgcolor="#efefef" style="vertical-align:middle;width:50px;">序号</th>
             <th width="60" bgcolor="#efefef" style="vertical-align:middle;">省</th>
             <th width="60" bgcolor="#efefef" style="vertical-align:middle;">地市</th>
             <th width="60" bgcolor="#efefef" style="vertical-align:middle;">区县</th>
             <th width="80" bgcolor="#efefef" style="vertical-align:middle;">个人姓名</th>
             <th <%--width="120"--%> bgcolor="#efefef" style="vertical-align:middle;">所在单位名称</th>
             <th <%--width="200"--%> bgcolor="#efefef" style="vertical-align:middle;">个人案卷号</th>
             <th<%-- width="100"--%> bgcolor="#efefef" style="vertical-align:middle;">环保工作年限</th>
             <th width="180" bgcolor="#efefef" style="vertical-align:middle;">身份证号码</th>
             <th width="60" bgcolor="#efefef" style="vertical-align:middle;">学历</th>
             <th width="100" bgcolor="#efefef" style="vertical-align:middle;">编制性质</th>
            <%-- <th width="100" bgcolor="#efefef" style="vertical-align:middle;">个人参与调查处理案件数量</th>--%>
<!--              <td width="70" bgcolor="#efefef">参评案卷</td> -->
             <th bgcolor="#efefef" style="vertical-align:middle;">个人事迹材料</th>
<!--              <th width="70" bgcolor="#efefef" style="vertical-align:middle;">材料下载</th> -->
             <th bgcolor="#efefef" style="vertical-align:middle;" >个人照片</th>
<!--              <th width="70" bgcolor="#efefef" style="vertical-align:middle;">材料下载</th> -->
             <%--<th bgcolor="#efefef" style="vertical-align:middle;">个人廉洁执法证明相关材料</th>--%>
<!--              <th width="70" bgcolor="#efefef" style="vertical-align:middle;">材料下载</th> -->

               <th bgcolor="#efefef"  style="vertical-align:middle;">日常执法监督材料</th>
           </tr>
         </thead>
         <tbody>
               <c:forEach items="${proPersonalList.list}" var="item" varStatus="status">
			<tr>
				<td height="30" align="center">${status.index+1}</td>
				<td>${item.provincePro}</td>
				<td>${item.cityPro}</td>
				<td>${item.countryPro}</td>
				<td>${item.namePro}</td>
				<td style="width:200px; word-break:break-all;">${item.unitnamePro}</td>
				<td style="width:200px; word-break:break-all;">${item.fileCodePro}</td>
				<td>${item.workYearPro}</td>
				<td>${item.cardidPro}</td>
				<td>${item.educationPro}</td>
				<td>${item.orgpropPro}</td>
				<%--<td>${item.handlcasenumPro}</td>--%>
				<%--<td>${item.provincePro}</td> --%>
				<td>
					<a href="javascript:void(0)" onclick="downloadFile('${item.personalmaterialurlPro}',' ${item.personalmaterialPro}')"> ${item.personalmaterialPro} </a>
				</td>
								<%-- <td>
									<c:if test="${item.personalmaterialPro!=null and item.personalmaterialPro!=''}" > 
					             		<button class="btn btn-success btn-xs" onclick="downloadFile('${item.personalmaterialurlPro}',' ${item.personalmaterialPro}')">下载</button>
					             	</c:if>
								</td> --%>
				<td>
					<a style="word-wrap:break-word;" href="javascript:void(0)" onclick="downloadFile('${item.personalphotouploadurlPro}',' ${item.personalphotouploadPro}')">${item.personalphotouploadPro}</a>
				</td>
                <td>
                    <a href="javascript:void(0)" onclick="downloadFile('${item.dailySupervisionWorkUrlPro}',' ${item.dailySupervisionWorkPro}')">${item.dailySupervisionWorkPro}</a>
                </td>
<%--                <td>--%>
<%--                    <a href="javascript:void(0)" onclick="downloadFile('${item.dailySupervisionWorkPro}',' ${item.dailySupervisionWorkPro}')">${item.dailySupervisionWorkPro}</a>--%>
<%--                </td>--%>
									<%-- <td>
						             	<c:if test="${item.personalphotouploadPro!=null and item.personalphotouploadPro!=''}" > 
						             		<button class="btn btn-success btn-xs" onclick="downloadFile('${item.personalphotouploadurlPro}',' ${item.personalphotouploadPro}')">下载</button>
						             	</c:if>
									</td> --%>
				<%--<td>
					<a href="javascript:void(0)" onclick="downloadFile('${item.perhonestfileurlPro}',' ${item.perhonestfilenamePro}')">${item.perhonestfilenamePro}</a>
				</td>--%>
							<%-- <td>
								<c:if test="${item.perhonestfilenamePro!=null and item.perhonestfilenamePro!=''}" > 
								<button class="btn btn-success btn-xs" onclick="downloadFile('${item.perhonestfileurlPro}',' ${item.perhonestfilenamePro}')">下载</button>
								</c:if>
							</td> --%>
	           </tr>
           </c:forEach> 
         </tbody>
       </table>
    </div>
 	<div class="page">
    	<span style="padding:12px; float:left; color:#0099cc;">共${proPersonalList.total}条记录</span>
        <ul class="pagination" id="pageCon">
            
        </ul>
    </div>
</div>
<script >
$("#viewExcel").click(function(){
	var areatypePro = $("#areatypePro").val();
	var areacodePro = $("#areacodePro").val();

	window.location.href= WEBPATH+'/xxcj/personalExportExcle.do?areatypePro='+areatypePro+'&areacodePro='+areacodePro;
});
//搜索条件
$(document).ready(function(){
	$("#search").click(function(){
		business.addMainContentParserHtml("xxcj/personalTotalProList.do",$("#searchForm").serialize());
		
	});
});
//分页
$(document).ready(function(){
	var curentPage = eval('${proPersonalList.pageNum}');
	var totalPage = eval('${proPersonalList.pages}');

	var areatypePro = eval('${areatypePro}');
	if(totalPage>0){
		var options = {
			bootstrapMajorVersion: 3,
		    currentPage: curentPage,
		    totalPages: totalPage,
		    numberOfPages: 2,
		    itemTexts: function (type, page, current) {
		            switch (type) {
		            case "first":
		                return "首页";
		            case "prev":
		                return "&laquo;";
		            case "next":
		                return "&raquo;";
		            case "last":
		                return "尾页";
		            case "page":
		                return page;
		            }
		    },
		    onPageClicked: function (event, originalEvent, type, page) {
            	business.addMainContentParserHtml(WEBPATH+'/xxcj/personalTotalProList.do?pageNum='+page,$("#searchForm").serialize());
            }
    	};
    	$('#pageCon').bootstrapPaginator(options);
   	}
});

function downloadFile(url,fileName){

	if(url != null && url != '' && url != "undefined"){
		window.location.href="${webpath}/filedownload.do?url="+url+"&fileName="+fileName;
	}else{
		swal( "操作失败","该材料不存在!", "error");
		}
	}
	
$(document).ready(function() {

	$(".topnav").accordion({
		accordion:false,
		speed: 500,
		closedSign: '[+]',
		openedSign: '[-]'
	});
});

</script>
</body>
</html>
