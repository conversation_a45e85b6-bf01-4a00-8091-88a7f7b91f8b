<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
<style type="text/css">
	.pdfobject-container {
		width: 100%;
		height:580px;
		margin: 2em 0;
	}
	.textarea-success {
		border-top:green 1px solid;
		border-bottom:green 1px solid; 
		border-left:green 1px solid;
     	border-right:green 1px solid;
	}
	.textarea-error {
		border-top:red 1px solid;
		border-bottom:red 1px solid; 
		border-left:red 1px solid;
     	border-right:red 1px solid;
	}
</style>
</head>

<body>
<div id="indexDiv">
<c:if test="${type==0}"><div class="center_weizhi">推荐报送 - 省内评比指标配置 - 市级指标</div></c:if>
<c:if test="${type==1}"><div class="center_weizhi">推荐报送 - 省内评比指标配置 - 县级指标</div></c:if>
<c:if test="${type==2}"><div class="center_weizhi">推荐报送 - 省内评比指标配置 - 个人指标</div></c:if>
<div class="center">
<div class="center_list">
    <div v-for="(index,i) in indexList" class="shangbao_panel col-lg-10">
		<div class="shangbao_titlt" style="padding-left: 3%;font-weight:bold;">指标项</div>
		<table style="width:100%;" class="table_input">				
		   <tr>
			 <td style="width:224px;">指标项名称</td>
			 <td>
			 <div  :id="'indexNameMsg'+i" style="margin:0 0 5px 0">
			 	<input @input="updateName(index.indexName,i)"  type="text" v-model="index.indexName" class="form-control" placeholder="请输入指标项名称">
             </div>
			 <div style="color:red; font-size: 12px;">{{index.indexNameMsg}}</div>
			 </td>
		   </tr>
		   <tr>
			 <td>本指标满分分值</td>
			 <td>
			 <div  :id="'indexScoreMsg'+i" style="margin:0 0 5px 0;">
			 	<input @input="updateScore(index.indexScore,i)" type="text" value="100" disabled v-model="index.indexScore" class="form-control" placeholder="请输入指标项分值">
             </div>
			 <div style="color:red; font-size: 12px;">{{index.indexScoreMsg}}</div>
			 </td>
		   </tr>
		   <tr>
			 <td>本指标占总指标权重（%）</td>
			 <td>
			 <div  :id="'weightMsg'+i" style="margin:0 0 5px 0;">
			 	<input @input="updateWeight(index.weight,i)" type="text" v-model="index.weight" class="form-control" placeholder="请输入指标项权重">
             </div>
			 <div style="color:red; font-size: 12px;">{{index.weightMsg}}</div>
			 </td>
		   </tr>
		   <tr>
			 <td>指标说明</td>
			 <td><textarea @input="updateDesc(index.indexDesc,i)" :id="'indexDescMsg'+i" v-model="index.indexDesc" name="name2" rows="6" class="form-control" placeholder="请输入指标说明"></textarea>
		   	 <div style="color:red; font-size: 12px;">{{index.indexDescMsg}}</div></td>
		   </tr>			   
		</table>
		<c:if test="${sysInitConfig.code=='1' && provinceReportUser.isDevInternalEva==null}">
		<div class="col-lg-4 col-xs-offset-2">
			<button type="button" v-on:click="deleted(i)" class="btn btn-danger removeButton">删除该指标</button>
		</div>
		</c:if>
	</div>
	
	<div id="btn" class="col-lg-5 col-xs-offset-2" style="margin-top: 20px;">		
		<c:if test="${sysInitConfig.code=='1' && provinceReportUser.isDevInternalEva==null}">
			<button type="button" v-on:click="add()" class="btn btn-danger addButton" data-template="textbox">增加指标项</button>
			<button type="submit" v-on:click="save()" class="btn btn-danger" name="signup" value="Sign up">信息保存</button>
		</c:if>
	</div>
</div>
</div>
</body>
<script type="text/javascript">
	var str = '${json}';
	var str2 = '${jsonObj}';//一个空的provinceIndex对象，用于新增时给vue赋值。
	
	var indexList = JSON.parse(str);
	var delIDs = "";
	//var index = JSON.parse(str2);
	
	var number = indexList.length;
	
	var indexVue = new Vue({
		el: '#indexDiv',
		data:{
			indexList:indexList,
			number:number
		},
		methods:{
			add:function(){
				indexVue.indexList.push(JSON.parse(str2));
				
				/* var htmlstr = "<div class='shangbao_panel col-lg-10'>"+
				"<div class='shangbao_titlt' style='padding-left: 3%;'>指标项</div>"+
				"<table style='width:100%;' class='table_input'>"+				
				   "<tr><td style='width:224px;'>指标项名称</td><td><input type='text' v-model='indexList[number].indexName' class='form-control' placeholder='请输入指标项名称'></td></tr>"+
				   "<tr><td>分值</td><td><input type='text' v-model='indexList[number].indexScore' class='form-control' placeholder='请输入指标项分值'></td></tr>"+
				   "<tr><td>权重</td><td><input type='text' v-model='indexList[number].weight' class='form-control' placeholder='请输入指标项权重'></td></tr>"+
				   "<tr><td>指标说明</td><td><textarea v-model='indexList[number].indexDesc' rows='6' class='form-control' placeholder='请输入指标说明'></textarea></td></tr>"+			   
				"</table></div>";
				$("#btn").before(htmlstr); */
				
				number++;
			},
			deleted:function(i){
				var id = indexVue.indexList[i].id;
				indexVue.indexList.splice(i,1);
				
				if(id!=null && id != ''){//原有的，标记删除状态
					delIDs += id+",";
				}
			},
			updateName:function(value,i){
				if(value==null || value==""){
					indexVue.indexList[i].indexNameVail = false;
					indexVue.indexList[i].indexNameMsg = "指标项名称不能为空";
					$("#indexNameMsg"+i).removeClass("has-success");	
					$("#indexNameMsg"+i).addClass("has-error");
				}else{
					if(value.length>50){
						indexVue.indexList[i].indexNameVail = false;
						indexVue.indexList[i].indexNameMsg = "指标项名称不能超过50字符";
						$("#indexNameMsg"+i).removeClass("has-success");	
						$("#indexNameMsg"+i).addClass("has-error");
					}else{
						var reg = /^(?!.*(\<|\>))/;
						//var reg = /^([1-9][0-9]*){1,3}$/;
						if(value.indexOf("<")!=-1 || value.indexOf(">")!=-1){
							indexVue.indexList[i].indexNameVail = false;
							indexVue.indexList[i].indexNameMsg = "不允许输入非法的字符<、>";
							$("#indexNameMsg"+i).removeClass("has-success");	
							$("#indexNameMsg"+i).addClass("has-error");
							return;
						}
						
						indexVue.indexList[i].indexNameVail = true;
						indexVue.indexList[i].indexNameMsg = "";
						$("#indexNameMsg"+i).removeClass("has-error");	
						$("#indexNameMsg"+i).addClass("has-success");
					}
				}
			},
			updateScore:function(value,i){
				if(value==null || value==""){
					indexVue.indexList[i].indexScoreVail = false;
					indexVue.indexList[i].indexScoreMsg = "分值不能为空";
					$("#indexScoreMsg"+i).removeClass("has-success");	
					$("#indexScoreMsg"+i).addClass("has-error");
				}else{
					if(value>100){
						indexVue.indexList[i].indexScoreVail = false;
						indexVue.indexList[i].indexScoreMsg = "分值范围设定1-100整数";
						$("#indexScoreMsg"+i).removeClass("has-success");	
						$("#indexScoreMsg"+i).addClass("has-error");
						return;
					}
					
					var reg = /^([1-9][0-9]*){1,3}$/;
					if(reg.test(value)){
						indexVue.indexList[i].indexScoreVail = true;
						indexVue.indexList[i].indexScoreMsg = "";
						$("#indexScoreMsg"+i).removeClass("has-error");	
						$("#indexScoreMsg"+i).addClass("has-success");
					}else{
						indexVue.indexList[i].indexScoreVail = false;
						indexVue.indexList[i].indexScoreMsg = "分值范围设定1-100整数";
						$("#indexScoreMsg"+i).removeClass("has-success");	
						$("#indexScoreMsg"+i).addClass("has-error");
					}
				}
			},
			updateWeight:function(value,i){
				if(value==null || value==""){
					indexVue.indexList[i].weightVail = false;
					indexVue.indexList[i].weightMsg = "权重不能为空";
					$("#weightMsg"+i).removeClass("has-success");	
					$("#weightMsg"+i).addClass("has-error");
				}else{
					if(value>100 || value<1){
						indexVue.indexList[i].weightVail = false;
						indexVue.indexList[i].weightMsg = "权重范围设定1-100整数";
						$("#weightMsg"+i).removeClass("has-success");	
						$("#weightMsg"+i).addClass("has-error");
						return;
					}
					var reg = /^([1-9][0-9]*){1,3}$/;
					if(reg.test(value)){
						indexVue.indexList[i].weightVail = true;
						indexVue.indexList[i].weightMsg = "";
						$("#weightMsg"+i).removeClass("has-error");	
						$("#weightMsg"+i).addClass("has-success");
					}else{
						indexVue.indexList[i].weightVail = false;
						indexVue.indexList[i].weightMsg = "权重范围设定1-100整数";
						$("#weightMsg"+i).removeClass("has-success");	
						$("#weightMsg"+i).addClass("has-error");
					}
				}
			},
			updateDesc:function(value,i){
				if(value==null || value==""){
					indexVue.indexList[i].indexDescVail = false;
					indexVue.indexList[i].indexDescMsg = "指标项名称不能为空";
					$("#indexDescMsg"+i).removeClass("textarea-success");	
					$("#indexDescMsg"+i).addClass("textarea-error");
					$("#indexDescMsg"+i).blur();
				}else{
					if(value.length>1000){
						indexVue.indexList[i].indexDescVail = false;
						indexVue.indexList[i].indexDescMsg = "指标项名称不能超过1000字符";
						$("#indexDescMsg"+i).removeClass("textarea-success");	
						$("#indexDescMsg"+i).addClass("textarea-error");
						$("#indexDescMsg"+i).blur();
					}else{
						/* if(indexVue.indexList[i].indexDescVail == false){
							$("#indexDescMsg"+i).blur();
						} */
						indexVue.indexList[i].indexDescVail = true;
						indexVue.indexList[i].indexDescMsg = "";
						$("#indexDescMsg"+i).removeClass("textarea-error");	
						$("#indexDescMsg"+i).addClass("textarea-success");
					}
				}
			},
			save:function(){
				
				var list = indexVue.indexList;
				
				var weightSum = 0;
				
				for (var i = 0; i < list.length; i++) {
					var index = list[i];
					
					weightSum += parseFloat(index.weight);
					
					var indexNameVail = index.indexNameVail;
					var indexScoreVail = index.indexScoreVail;
					var weightVail = index.weightVail;
					var indexDescVail = index.indexDescVail;
					
					if(!indexNameVail){
						if(index.indexNameMsg==null || index.indexNameMsg=='' ){
							indexVue.indexList[i].indexNameMsg = "指标项名称不能为空";
							$("#indexNameMsg"+i).removeClass("has-success");	
							$("#indexNameMsg"+i).addClass("has-error");
						}
						swal("提示", "请确保信息填写正确!", "info");
						return;
					}
					if(!indexScoreVail){
						if(index.indexScoreMsg==null || index.indexScoreMsg=='' ){
							indexVue.indexList[i].indexScoreMsg = "指标项名称不能为空";
							$("#indexScoreMsg"+i).removeClass("has-success");	
							$("#indexScoreMsg"+i).addClass("has-error");
						}
						swal("提示", "请确保信息填写正确!", "info");
						return;
					}
					if(!weightVail){
						if(index.weightMsg==null || index.weightMsg=='' ){
							indexVue.indexList[i].weightMsg = "权重不能为空";
							$("#weightMsg"+i).removeClass("has-success");	
							$("#weightMsg"+i).addClass("has-error");
						}
						swal("提示", "请确保信息填写正确!", "info");
						return;
					}
					if(!indexDescVail){
						if(index.indexDescMsg==null || index.indexDescMsg=='' ){
							indexVue.indexList[i].indexDescMsg = "指标说明不能为空";
							$("#indexDescMsg"+i).removeClass("textarea-success");	
							$("#indexDescMsg"+i).addClass("textarea-error");
							$("#indexDescMsg"+i).blur();
						}
						swal("提示", "请确保信息填写正确!", "info");
						return;
					}
				}
				
				/* if(weightSum!=100){
					swal("提示", "指标权重相加必须等于100!", "info");
					return;
				} */
				
				$.ajax({
				    type:"post",
				    url:WEBPATH+'/provinceScore/saveIndex.do',
				    data:{
				    	delIDs:delIDs,
				    	type:'${type}',
				    	areacode:'${areacode}',
				    	indexList:JSON.stringify(indexVue.indexList)
				    },           //注意数据用{}
				    success:function(data){  //成功
				    	if(data.result=="success"){
				    		swal({title: "保存成功",text: "",type:"success"});
				    		business.addMainContentParserHtml(WEBPATH+'/provinceScore/indexConfig.do?type=${type}');
					    }
				    	else if(data.result=="failure"){
				    		swal("错误", "信息保存操作失败了!", "error");
				    	}else{
				    		swal("提示", "请勿操作过于频繁!", "info");
					    }
				    }
				});
			}
		}
	});
	
	if(indexList.length==0){
		indexVue.add();
	}
	
	
</script>
</html>
