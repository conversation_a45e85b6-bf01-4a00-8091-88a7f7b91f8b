<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
<script type="text/javascript">
	    	  
</script>
</head>

<body>
<div class="center_weizhi">当前位置：推荐报送 - 省内评比启动设置 - 省内评比启动</div>
<div class="center">
<form id="staForm">
<div class="center_list">
 	<div class="shangbao_panel col-lg-10">
		<div class="shangbao_titlt" style="padding-left: 3%;font-weight:bold;">省内评比设置</div>
		<table style="width:100%;" class="table_input">				
		   <tr>
			 <td style="width:224px;">是否开展并启动省内评比</td>
			 <td style="text-align: left;">
			 	<div class="form-group">
			 		<lable style="padding-right: 150px;">
						<input type="radio" value="1" name="isDevInternalEva" <c:if test="${currUser.isDevInternalEva==1}">checked</c:if> <c:if test="${currUser.isDevInternalEva!=null}">disabled</c:if>>开展并启动
					</lable>
					<lable>
						<input type="radio" value="0" name="isDevInternalEva" <c:if test="${currUser.isDevInternalEva==0}">checked</c:if> <c:if test="${currUser.isDevInternalEva!=null}">disabled</c:if>>未开展
					</lable>
				</div>
			 </td>
		   </tr>
		   <tr>
			 <td>请选择活动起止时间</td>
			 <td>
			 	<div class="form-group">
				<input type="text" class="form-control" name="ActivityTime" id="ActivityTime" <c:if test="${currUser.isDevInternalEva==1}">value="<fmt:formatDate value="${currUser.startActivityTime}" pattern="yyyy-MM-dd"/> - <fmt:formatDate value="${currUser.endActivityTime}" pattern="yyyy-MM-dd"/>"</c:if> <c:if test="${currUser.isDevInternalEva!=null}">disabled</c:if> placeholder="请点击选择时间段" readonly>             	
				</div>
				 <script type="text/javascript">
				   $(document).ready(function() {
					  $('#ActivityTime').daterangepicker(
						null,
						function(start, end, label) {
							//console.log(start.toISOString(), end.toISOString(), label);
							$('#staForm').data('formValidation').revalidateField("ActivityTime");
					  });
				   });
				</script>
			 </td>
		   </tr>
		   <tr>
			 <td></td>
			 <td style="text-align: left;">
				<p><strong>设置说明</strong>：</p>
				<p style="color: #ff0000;">1、是否开展并启动省内评比：指确定本省是否开启省内自评活动。一旦选择，将不能修改，请慎重操作！！！</p>
				<p>启动省内评比后，影响的后续操作：1）各市县登录，上报信息功能停止且不能再操作数据；2）进入省内评比结果汇总阶段。</p>
				<p>2、请选择活动起止时间：指本省开展省内练兵活动的时间范围。对应参选案件范围也与之相同。<span style="color: #ff0000;">请注意，案件选取时间范围不是指其填报时间，而是决定下达日期/实施期限/移送时间等。</span></p>
				<p>3、“全国环境行政处罚案件办理信息系统”各类型案件选取字段对应表如下：</p>
				<table class="table table-striped table-hover table-bordered no-margin">
				  <tbody>
					<tr>
					  <th scope="col" style="width: 20%;">案件类型</th>
					  <th scope="col" style="width: 40%;">唯一性判断字段名称</th>
					  <th scope="col" style="width: 40%;">时间字段名称</th>
					</tr>
					<tr>
					  <td>一般行政处罚案件</td>
					  <td>决定书文号</td>
					  <td>决定下达日期</td>
					</tr>
					<tr>
					  <td>按日连续处罚案件</td>
					  <td>决定书文号（按日连续处罚决定部分的决定书文号）</td>
					  <td>决定下达日期</td>
					</tr>
					<tr>
					  <td>查封、扣押案件</td>
					  <td>查封、扣押决定文号</td>
					  <td>实施期限（取开始时间）</td>
					</tr>
					<tr>
					  <td>限产、停产案件</td>
					  <td>决定文号（填报页面中，实施措施类型下方的决定文号）</td>
					  <td>限产取实施期限（取开始时间）；停产（取决定下达日期）</td>
					</tr>
					<tr>
					  <td>行政拘留案件</td>
					  <td>移送案卷编号</td>
					  <td>移送时间</td>
					</tr>
					<tr>
					  <td>涉嫌违法犯罪案件</td>
					  <td>移送案卷编号</td>
					  <td>移送时间</td>
					</tr>
				  </tbody>
				</table>
			 </td>
		 	</tr>		   			   
		</table>
	</div>
	
	<div class="col-lg-5 col-xs-offset-2" style="margin: 20px 0 0 285px;">
		<c:if test="${sysInitConfig.code == 1}">	
			<button type="button" id="sta_Butt" class="btn btn-danger" <c:if test="${currUser.isDevInternalEva!=null}">disabled</c:if>>信息保存</button>
		</c:if>
	</div>
	
</div>
</form>
</div>
<script type="text/javascript">
	//表单校验
	$(document).ready(function() {
	$('#staForm').formValidation({
		message: 'This value is not valid',
	    icon: {
	    	valid: 'glyphicon glyphicon-ok',
	        invalid: 'glyphicon glyphicon-remove',
	        validating: 'glyphicon glyphicon-refresh'
	    },
	    autoFocus: true,
	    fields: {
	        "isDevInternalEva": {
	        	validators: {
	            	notEmpty: {
	                	message: '请选择是否开启大练兵.'
	                }
	            }
	        },
	        "ActivityTime": {
	        	enabled:false,
	        	validators: {
	            	notEmpty: {
	                	message: '若开展启动大练兵，请选择起止活动时间.'
	                }
	            }
	        }
	        
	              
	        }
	    });
	});
	
	//关联验证
	$("[name='isDevInternalEva']").on('change', function(){
	    var isTrue = ($(this).val() == '1');
	    $('#staForm').formValidation('enableFieldValidators','ActivityTime',isTrue);
	    if(isTrue){
	    	$('#ActivityTime').prop('disabled', false);
	    }else{
	    	$('#ActivityTime').prop('disabled', true);
	    	$('#ActivityTime').val('');
	    }
	});
	
	//表单提交
	$(document).ready(function(){
		$("#sta_Butt").click(function(){
			var validate = false;
			$("#staForm").data('formValidation').validate();
			validate = $("#staForm").data('formValidation').isValid();
			if(validate){
				swal({
	                 title: "一旦选择，将不能修改，请慎重操作！！！",
	                 type: "warning",
	                 showCancelButton: true,
	                 closeOnConfirm: false,
	                 confirmButtonText: "继续保存",
	                 confirmButtonColor: "#ec6c62"
	             }, function() {
	            	 var options = {
     					url:WEBPATH+'/sys2018/savActivitySta.do',
     					type:'post',
     					success:function(data){
     						if(data.result=="success"){
     							$("#staForm :input").prop('disabled', true);
     							swal({
							    	title: "保存成功!",
							        type: "success",
							        closeOnConfirm: true,
							        confirmButtonText: "确定",
							        confirmButtonColor: "#d9534f"
							    	}, function() {
							    		macroMgr.onLevelOneMenuClick(null, 'goProvinceScore.do','type=1');
								});
     			               return false;
     			             }else{
     			            	 swal(data.message, "", "error");
     				             return false; 
     			             }	
     					},
     					error:function(){
     						swal("服务异常,保存失败!", "", "error");
     					}
     				};
     				$("#staForm").ajaxSubmit(options);
	            	 
	             });
				
			}else if(validate==null){
				//表单未填写
				$("#staForm").data('formValidation').validate();
	       
	        }
		});
	});
</script>

</body>
</html>