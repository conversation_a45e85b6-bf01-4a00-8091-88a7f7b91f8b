<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>

<script type="text/javascript">
var RecommendId;
function deleteRecommend(id){
	 swal({
         title: "您确定执行此操作吗？",
         text: "您确定要删除当前区划吗？",
         type: "warning",
         showCancelButton: true,
         closeOnConfirm: false,
         confirmButtonText: "是的，我要删除",
         confirmButtonColor: "#d9534f"
     }, function(){
         $.ajax({
             url: WEBPATH+"/recom/deleteRecommendCity.do",
             type: "POST",
             data:{"id":id}
         }).done(function(data) {
             if(data.result=="success"){
             	swal("操作成功!", "已成功删除！", "success");
             	macroMgr.onLevelTwoMenuClick(null, 'recom/RecommendCityDeed.do');
             }else{
                 swal("OMG", "删除失败了!", "error");
             }
             
         }).error(function(data) {
             swal("OMG", "请求错误", "error");
         });
     });
}
//在市级推荐页面点击“增加参选单位”进入候选推荐页面时加载本省已上报的所有市级
function selectCity(){

	$("#zpdf").html("");
	$("#ranking").html("");
	//var aa=$("#list").val();
  //  alert($("#areaCode"))
  //  console.log($("#areaCode"))
    if($("#areaCode").val()=="15000000"){

        //alert("-------------")
        if ($("#list").val() < 5){
            $("#myModal").modal("show")
            $.ajax({
                url: WEBPATH+"/recom/selectCitys.do",
                type:'post',
                success:function(data){
                    var res = data;
                    var option;
                    var option1 = "<option value='0'>"+'请选择行政区'+"</option>";
                    $.each(res,function(i,n){//循环，i为下标从0开始，n为集合中对应的第i个对象

                        option += "<option value='"+n.id+"'>"+n.areanamePro+"</option>"
                    });
                    $("#first").html(option1+option);//将循环拼接的字符串插入第二个下拉列表
                    $("#first").show();
                },
                error:function(){
                    swal("OMG", "请求错误", "error");
                }
            })

        }else{
            swal("不可增加", "候选市级4个已满", "error");
        }

    }else if($("#list").val() < 4){
	$("#myModal").modal("show")
	$.ajax({
		url: WEBPATH+"/recom/selectCitys.do",
		type:'post',
		success:function(data){
			var res = data;
			var option;
			var option1 = "<option value='0'>"+'请选择行政区'+"</option>";
			$.each(res,function(i,n){//循环，i为下标从0开始，n为集合中对应的第i个对象
				
				option += "<option value='"+n.id+"'>"+n.areanamePro+"</option>"
			});
			$("#first").html(option1+option);//将循环拼接的字符串插入第二个下拉列表
			$("#first").show();
		},
		error:function(){
			 swal("OMG", "请求错误", "error");
		}
	})
		
	}else{
		swal("不可增加", "候选市级4个已满", "error");
	}
}



//根据已选的市级id查询本市级的信息
function select(id){
	$("#zpdf").html("");
	$("#ranking").html("");
	if(id!=0){
	$.ajax({
		url: WEBPATH+"/recom/selectCityById.do",
		type:'post',
		data:{"id":id},
		success:function(data){
			//var aa='<input type="hidden" class="form-control" name="isrecommend" id="isrecommend" value="+'data.id'+">';
			//$("#recommend").html(aa);
			RecommendId=data.id;
			$("#zpdf").html(data.altogetherscore);
			$("#ranking").html(data.provinceranking);
			
		},
		error:function(){
			
		}
	})
	}	
}
//保存市级推荐使用
$('#savebtn').click(function(){
	if(RecommendId != null && RecommendId != "" && RecommendId !="undefined"){
	$.ajax({
		url: WEBPATH+"/recom/updateRecommendCityById.do",
		type:'post',
		data:{"id":RecommendId},
		success:function(data){
			if(data.result=="error"){
				swal("推荐失败!", "", "error");
                return false;
             }else if(data.result=="success"){
             	swal({
				    	title: "推荐成功!",
				        type: "success",
				        closeOnConfirm: true,
				        confirmButtonText: "确定",
				        confirmButtonColor: "#d9534f"
			    	}, function() {
			    		RecommendId=null;
				    	business.addMainContentParserHtml('recom/RecommendCityDeed.do','');
				});
               return false;
             }	
		},
		error:function(){
			 swal("OMG", "请求错误", "error");
		}
	})
	}else{
		swal("推荐失败", "请选择要推荐的市级再点击确定", "error");
	}
	$('#myModal').modal('hide');
	});
	//提交
$('#submitBtn').click(function(){
	var aa=$("#list").val();
    if($("#areaCode").val()=="15000000"){
        if(aa <= 5){
            $.ajax({
                url: WEBPATH+"/recom/submitRecommendCity.do",
                type:'post',
                //data:{"id":RecommendId},
                success:function(data){
                    if(data.result=="error"){
                        swal("提交失败!", "", "error");
                        return false;
                    }else if(data.result=="success"){
                        swal({
                            title: "提交成功!",
                            type: "success",
                            closeOnConfirm: true,
                            confirmButtonText: "确定",
                            confirmButtonColor: "#d9534f"
                        }, function() {
                            //RecommendId=null;
                            business.addMainContentParserHtml('recom/RecommendCityDeed.do','');
                        });
                        return false;
                    }
                },
                error:function(){
                    swal("OMG", "请求错误", "error");
                }

            })
// 	}else{
// 		swal("推荐失败", "请选择要推荐的市级再点击确定", "error");
// 	}
            $('#tijiao').modal('hide');
        }else{
            swal("提交失败", "推荐的市级单位不能超过4个", "error");
            $('#tijiao').modal('hide');
        }

    } else if(aa <= 4){
	$.ajax({
		url: WEBPATH+"/recom/submitRecommendCity.do",
		type:'post',
		//data:{"id":RecommendId},
		success:function(data){
			if(data.result=="error"){
				swal("提交失败!", "", "error");
                return false;
             }else if(data.result=="success"){
             	swal({
				    	title: "提交成功!",
				        type: "success",
				        closeOnConfirm: true,
				        confirmButtonText: "确定",
				        confirmButtonColor: "#d9534f"
			    	}, function() {
			    		//RecommendId=null;
				    	business.addMainContentParserHtml('recom/RecommendCityDeed.do','');
				});
               return false;
             }	
		},
		error:function(){
			 swal("OMG", "请求错误", "error");
		}
	
	})
// 	}else{
// 		swal("推荐失败", "请选择要推荐的市级再点击确定", "error");
// 	}
	$('#tijiao').modal('hide');
}else{
	 swal("提交失败", "推荐的市级单位不能超过4个", "error");
	 $('#tijiao').modal('hide');
}
	});
	
</script>
</head>
<body>
<div class="center_weizhi">当前位置：推荐报送 - 集体和个人推荐 - 候选市级集体推荐</div>
<div class="center">
	<div class="center_list">
    <div class="panel-group" id="accordion">
         
          
            <!---搜索--->
            <div style="width:100%px;" class="btn-group">
            <!-- && provinceReportUser.reportstate !=1(上报) -->
			<c:if test="${sysInitConfig.code =='1' && provinceReportUser.electcityState !=1 && provinceReportUser.reportstate !=1 }">
            <c:choose>
   				<c:when test="${provinceReportUser.isDevInternalEva == 1}"> 
   				  	<c:choose>
   				   		<c:when test="${provinceSelectionUnits[0].provinceranking !=null }">
   				   			<button class="btn btn-danger" onclick="selectCity()" type="button" style="width:120px;" data-toggle="modal" > 增加参选单位</button>
              				<button type="button" class="btn btn-danger" style="width:120px;" data-toggle="modal" data-target="#tijiao">提交</button>
   				   		</c:when>
   				  		<c:otherwise> 

              			

              				<h4 style="color:#F00;" >请确认排完名之后再推荐</h4>

   				  		</c:otherwise>
   				  	</c:choose>
   				</c:when>
   				<c:otherwise> 
   					<button class="btn btn-danger" onclick="selectCity()" type="button" style="text-align: center;border-radius: 4px" data-toggle="modal" > 增加参选单位</button>
              		<button type="button" class="btn btn-danger" style="width: 180px;margin-left: 20px;border-radius: 4px" data-toggle="modal" data-target="#tijiao">提交</button>
   				</c:otherwise>
			</c:choose> 	
            </c:if>
			<c:if test="${ provinceReportUser.electcityState ==1 }">
            	<h4 style="color:#F00;" >候选市级单位已提交完毕，提交候选市级单位信息如列表所示：</h4>
			</c:if>
            
            </div>            
    </div>
    <input type="hidden" class="form-control" name="list" id="list" value="${listSize}">
        <table class="table table-bordered table-hover table-condensed"  >
         <thead>
           <tr>
             <th width="50" height="30" bgcolor="#efefef">序号</th>
             <th bgcolor="#efefef">市级参选单位</th>
             <th bgcolor="#efefef" style="width:100px;">操作</th>
           </tr>
         </thead>
         <tbody>
           <c:forEach  var="recommendList" items="${recommendList}" varStatus="status">
         		 <tr>
	             <td height="30" align="center">${status.index+1}</td>
	             <td>${recommendList.areanamePro}</td>
	             <td style="text-align: center;">
		             <c:if test="${sysInitConfig.code =='1' && provinceReportUser.electcityState != 1 && provinceReportUser.reportstate !=1}">
		             <a href="#"><button class="btn btn-danger btn-xs" onclick="deleteRecommend('${recommendList.id}')">删除</button></a> 
		             </c:if>
	             </td>
	           </tr>
          	</c:forEach>
          <input type="hidden" id="areaCode" value="${provinceReportUser.areacode}">
         </tbody>
       </table>
    </div>
</div>
<!-- 新增市级参选单位（Modal） -->
<div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title">新增市级参选单位</h4>
            </div>
            <div class="modal-body form-horizontal" style="height:300px; font-size:16px;">
            	<div class="form-group" style="padding:10px;">
                	<label class="col-lg-3 control-label">省份</label>
                    <div  class="col-lg-8" style="margin-top:7px;">
                	${areaUser.userName}
                    </div>
                </div>
            	<div class="form-group" style="padding:10px;">
                	<label class="col-lg-3 control-label">市级行政区</label>
                    <div class="col-lg-8">
                		<select  id="first" class="form-control" onChange="select(this.value)" style="width:300px;margin-right:5px;">
<!--                              <option  value="-1">请选择行政区</option> -->
                            
                          </select>
                    </div>
                </div>
                <c:if test="${provinceReportUser.isDevInternalEva == 1}">
                <div class="form-group" style="padding:10px;">
                	<label class="col-lg-3 control-label">自评得分</label>
                    <div id="zpdf" class="col-lg-8" style="margin-top:7px;">
                	
                    </div>
                </div>
                </c:if>
                <c:if test="${provinceReportUser.isDevInternalEva == 1}">
                <div class="form-group" style="padding:10px;">
                	<label class="col-lg-3 control-label">排名</label>
                    <div  id="ranking" class="col-lg-8" style="margin-top:7px;">
                	
                    </div>
                </div>
                </c:if>
                <div id="recommend"></div>
            </div>
            <div class="modal-footer"><button type="button" id="savebtn" name="savebtn"  class="btn btn-danger">确定</button>
                <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
<!-- 提交（Modal） -->
<div class="modal fade" id="tijiao" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title">提示</h4>
            </div>
            <div class="modal-body form-horizontal text-center" style="height:200px; font-size:18px; padding:80px 0; color:red;">
            	提交后参选单位将无法更改，是否确认提交？
            </div>
            <div class="modal-footer"><button id="submitBtn" name="submitBtn"  type="button" class="btn btn-danger">确定</button>
                <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
<script language="JavaScript">

$(document).ready(function() {
	$(".topnav").accordion({
		accordion:false,
		speed: 500,
		closedSign: '[+]',
		openedSign: '[-]'
	});
});

</script>
</body>
</html>