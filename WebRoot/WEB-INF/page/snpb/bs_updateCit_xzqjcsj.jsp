<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
<script type="text/javascript">
function butChange(index){
	var butVal = $("#reButton"+index).attr("value");
	var butStyle = $("#reButton"+index).attr("style");
	if(butStyle!=""&&typeof(butStyle)!="undefined"){
		$("#reButton"+index).attr("style","");
    	$("#uploadTr"+index).attr("style","display:none");
    	$("#wsc"+index).attr("style","");
	}else{
		if(butVal=="重新上传"){
			$("#reButton"+index).attr("value","返回");
			$("#uploadTr"+index).attr("style","");
	    	$("#wsc"+index).attr("style","display:none");
		}else if(butVal=="返回"){
			$("#reButton"+index).attr("value","重新上传");
			$("#uploadTr"+index).attr("style","display:none");
	    	$("#wsc"+index).attr("style","");
		}
	}
}

$(document).ready(function(){
	 //排污企业证明材料文件上传、
	 /*$("#polluterfile").fileinput({
        uploadUrl: WEBPATH+'/pdffileupload.do', // you must set a valid URL here else you will get an error
        allowedFileExtensions : ['pdf'],
        language:'zh',
        browseClass:'btn btn-danger',
        //overwriteInitial: true,
        minFileCount: 1,
        maxFileCount: 1,
        minFileSize:1,
        maxFileSize:53500,
        enctype: 'multipart/form-data',        
        dropZoneTitle:"可拖拽文件到此处...",
        initialPreviewShowDelete: false,
        msgFilesTooMany:'选择上传的文件数量({n}) 超过允许的最大数值{m}！',
        msgZoomModalHeading:'文件预览',
        msgInvalidFileExtension:'不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
        msgNoFilesSelected:'请选择文件',
        msgValidationError:'文件类型不正确',
        initialPreviewFileType:'pdf',
        browseLabel:"选择文件",
        removeLabel:'删除',
        removeTitle:'删除文件',
        uploadLabel:'上传',
        uploadTitle:'上传文件',
        cancelLabel: '取消',
        cancelTitle: '取消上传',
        showPreview:false,
        autoReplace:true,
        slugCallback: function(filename) {
            return filename.replace('(', '_').replace(']', '_');
        }
	}).on('filepreupload', function(event, data, previewId, index) { //文件开始上传操作
		
	   $("#xxcj_AJSL_Butt").prop('disabled', true);
	
	}).on('fileuploaded', function(event, data, previewId, index) {//文件上传完成执行操作

		$("#polluter_Fileurl").val(data.response.url);
	  	$("#polluter_Filename").val(data.response.fileRealName);
	  	$("#wsc").text(data.response.fileRealName);
	  	butChange('');
	  	$("#xxcj_AJSL_Butt").prop('disabled',false);
	  	
	})/!* .on('fileclear', function(event) {  //文件上传删除文件执行的操作
		
    	$("#province_Fileurl").val("");
	  	$("#province_Filename").val("");
	  	$("#wsc").text("未上传");
    }); *!/
    
    $("#reButton").click(function(){
    	butChange('');
    });
    */
    //集体事迹材料文件上传、
	 $("#evidencefile").fileinput({
        uploadUrl: WEBPATH+'/pdffileupload.do', // you must set a valid URL here else you will get an error
        allowedFileExtensions : ['doc','docx'],
        language:'zh',
        browseClass:'btn btn-danger',
        //overwriteInitial: true,
        minFileCount: 1,
        maxFileCount: 1,
        minFileSize:1,
        maxFileSize:1060,
        enctype: 'multipart/form-data',        
        dropZoneTitle:"可拖拽文件到此处...",
        initialPreviewShowDelete: false,
        msgFilesTooMany:'选择上传的文件数量({n}) 超过允许的最大数值{m}！',
        msgZoomModalHeading:'文件预览',
        msgInvalidFileExtension:'不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
        msgNoFilesSelected:'请选择文件',
        msgValidationError:'文件类型不正确或文件过大',
        initialPreviewFileType:'doc',
        browseLabel:"选择文件",
        removeLabel:'删除',
        removeTitle:'删除文件',
        uploadLabel:'上传',
        uploadTitle:'上传文件',
        cancelLabel: '取消',
        cancelTitle: '取消上传',
        showPreview:false,
        autoReplace:true,
        slugCallback: function(filename) {
            return filename.replace('(', '_').replace(']', '_');
        }
	}).on('filepreupload', function(event, data, previewId, index) { //文件开始上传操作
		
	   $("#xxcj_AJSL_Butt").prop('disabled', true);
	
	}).on('fileuploaded', function(event, data, previewId, index) {//文件上传完成执行操作

		$("#evidence_Fileurl").val(data.response.url);
	  	$("#evidence_Filename").val(data.response.fileRealName);
	  	$("#wsc2").text(data.response.fileRealName);
	  	butChange('2');
	  	$("#xxcj_AJSL_Butt").prop('disabled',false);
	  	
	})/* .on('fileclear', function(event) {  //文件上传删除文件执行的操作
		
    	$("#city_Fileurl").val("");
	  	$("#city_Filename").val("");
	  	$("#wsc2").text("未上传");
    }); */
    $("#reButton2").click(function(){
    	butChange('2');
    });
    
	//集体事迹照片文件上传
	$("#picturefile").fileinput({
        uploadUrl: WEBPATH+'/pdffileupload.do', // you must set a valid URL here else you will get an error
        allowedFileExtensions : ['jpg','png','bmp'],
        language:'zh',
        browseClass:'btn btn-danger',
        //overwriteInitial: true,
        minFileCount: 1,
        maxFileCount: 1,
        minFileSize:1,
        maxFileSize:21500,
        enctype: 'multipart/form-data',        
        dropZoneTitle:"可拖拽文件到此处...",
        initialPreviewShowDelete: false,
        msgFilesTooMany:'选择上传的文件数量({n}) 超过允许的最大数值{m}！',
        msgZoomModalHeading:'文件预览',
        msgInvalidFileExtension:'不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
        msgNoFilesSelected:'请选择文件',
        msgValidationError:'文件类型不正确或文件过大',
        initialPreviewFileType:'jpg',
        browseLabel:"选择文件",
        removeLabel:'删除',
        removeTitle:'删除文件',
        uploadLabel:'上传',
        uploadTitle:'上传文件',
        cancelLabel: '取消',
        cancelTitle: '取消上传',
        showPreview:false,
        autoReplace:true,
        slugCallback: function(filename) {
            return filename.replace('(', '_').replace(']', '_');
        }
	}).on('filepreupload', function(event, data, previewId, index) { //文件开始上传操作
		
	   $("#xxcj_AJSL_Butt").prop('disabled', true);
	
	}).on('fileuploaded', function(event, data, previewId, index) {//文件上传完成执行操作

		$("#picture_Fileurl").val(data.response.url);
	  	$("#picture_Filename").val(data.response.fileRealName);
	  	$("#wsc3").text(data.response.fileRealName);
	  	butChange('3');
	  	$("#xxcj_AJSL_Butt").prop('disabled',false);
	  	
	})
	$("#reButton3").click(function(){
		butChange('3');
    });

	/*//考核奖惩制度
	$("#assessmentfile").fileinput({
        uploadUrl: WEBPATH+'/pdffileupload.do', // you must set a valid URL here else you will get an error
        allowedFileExtensions : ['pdf','rar'],
        language:'zh',
        browseClass:'btn btn-danger',
        //overwriteInitial: true,
        minFileCount: 1,
        maxFileCount: 1,
        minFileSize:1,
        maxFileSize:52000,
        enctype: 'multipart/form-data',        
        dropZoneTitle:"可拖拽文件到此处...",
        initialPreviewShowDelete: false,
        msgFilesTooMany:'选择上传的文件数量({n}) 超过允许的最大数值{m}！',
        msgZoomModalHeading:'文件预览',
        msgInvalidFileExtension:'不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
        msgNoFilesSelected:'请选择文件',
        msgValidationError:'文件类型不正确',
        initialPreviewFileType:'pdf',
        browseLabel:"选择文件",
        removeLabel:'删除',
        removeTitle:'删除文件',
        uploadLabel:'上传',
        uploadTitle:'上传文件',
        cancelLabel: '取消',
        cancelTitle: '取消上传',
        showPreview:false,
        autoReplace:true,
        slugCallback: function(filename) {
            return filename.replace('(', '_').replace(']', '_');
        }
	}).on('filepreupload', function(event, data, previewId, index) { //文件开始上传操作
		
	   $("#xxcj_AJSL_Butt").prop('disabled', true);
	
	}).on('fileuploaded', function(event, data, previewId, index) {//文件上传完成执行操作

		$("#assessment_Fileurl").val(data.response.url);
	  	$("#assessment_Filename").val(data.response.fileRealName);
	  	$("#wsc7").text(data.response.fileRealName);
	  	butChange('7');
	  	$("#xxcj_AJSL_Butt").prop('disabled',false);
	  	
	})
	$("#reButton7").click(function(){
		butChange('7');
    });
	//执法人员人身安全保障制度
	$("#lawenforcfile").fileinput({
        uploadUrl: WEBPATH+'/pdffileupload.do', // you must set a valid URL here else you will get an error
        allowedFileExtensions : ['pdf','rar'],
        language:'zh',
        browseClass:'btn btn-danger',
        //overwriteInitial: true,
        minFileCount: 1,
        maxFileCount: 1,
        minFileSize:1,
        maxFileSize:52000,
        enctype: 'multipart/form-data',        
        dropZoneTitle:"可拖拽文件到此处...",
        initialPreviewShowDelete: false,
        msgFilesTooMany:'选择上传的文件数量({n}) 超过允许的最大数值{m}！',
        msgZoomModalHeading:'文件预览',
        msgInvalidFileExtension:'不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
        msgNoFilesSelected:'请选择文件',
        msgValidationError:'文件类型不正确',
        initialPreviewFileType:'pdf',
        browseLabel:"选择文件",
        removeLabel:'删除',
        removeTitle:'删除文件',
        uploadLabel:'上传',
        uploadTitle:'上传文件',
        cancelLabel: '取消',
        cancelTitle: '取消上传',
        showPreview:false,
        autoReplace:true,
        slugCallback: function(filename) {
            return filename.replace('(', '_').replace(']', '_');
        }
	}).on('filepreupload', function(event, data, previewId, index) { //文件开始上传操作
		
	   $("#xxcj_AJSL_Butt").prop('disabled', true);
	
	}).on('fileuploaded', function(event, data, previewId, index) {//文件上传完成执行操作

		$("#lawenforc_Fileurl").val(data.response.url);
	  	$("#lawenforc_Filename").val(data.response.fileRealName);
	  	$("#wsc0").text(data.response.fileRealName);
	  	butChange('0');
	  	$("#xxcj_AJSL_Butt").prop('disabled',false);
	  	
	})
	$("#reButton0").click(function(){
		butChange('0');
    });
            
	//尽职照单免责和失职照单问责制度文件上传、
	 $("#accountabilityfile").fileinput({
     uploadUrl: WEBPATH+'/pdffileupload.do', // you must set a valid URL here else you will get an error
     allowedFileExtensions : ['pdf','rar'],
     language:'zh',
     browseClass:'btn btn-danger',
     //overwriteInitial: true,
     minFileCount: 1,
     maxFileCount: 1,
     minFileSize:1,
     maxFileSize:52000,
     enctype: 'multipart/form-data',        
     dropZoneTitle:"可拖拽文件到此处...",
     initialPreviewShowDelete: false,
     msgFilesTooMany:'选择上传的文件数量({n}) 超过允许的最大数值{m}！',
     msgZoomModalHeading:'文件预览',
     msgInvalidFileExtension:'不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
     msgNoFilesSelected:'请选择文件',
     msgValidationError:'文件类型不正确',
     initialPreviewFileType:'pdf',
     browseLabel:"选择文件",
     removeLabel:'删除',
     removeTitle:'删除文件',
     uploadLabel:'上传',
     uploadTitle:'上传文件',
     cancelLabel: '取消',
     cancelTitle: '取消上传',
     showPreview:false,
     autoReplace:true,
     slugCallback: function(filename) {
         return filename.replace('(', '_').replace(']', '_');
     }
	}).on('filepreupload', function(event, data, previewId, index) { //文件开始上传操作
		
	   $("#xxcj_AJSL_Butt").prop('disabled', true);
	
	}).on('fileuploaded', function(event, data, previewId, index) {//文件上传完成执行操作

		$("#accountability_Fileurl").val(data.response.url);
	  	$("#accountability_Filename").val(data.response.fileRealName);
	  	$("#wsc9").text(data.response.fileRealName);
	  	$("#xxcj_AJSL_Butt").prop('disabled',false);
	  	
	  	butChange(9);
	})
 
	$("#reButton9").click(function(){
	   butChange(9);
	});*/
    //证明材料上传
    $("#praiseFile").fileinput({
        uploadUrl: WEBPATH+'/pdffileupload.do', // you must set a valid URL here else you will get an error
        allowedFileExtensions : ['rar'],
        language:'zh',
        browseClass:'btn btn-danger',
        //overwriteInitial: true,
        minFileCount: 1,
        maxFileCount: 1,
        minFileSize:1,
        maxFileSize:53500,
        enctype: 'multipart/form-data',
        dropZoneTitle:"可拖拽文件到此处...",
        initialPreviewShowDelete: false,
        msgFilesTooMany:'选择上传的文件数量({n}) 超过允许的最大数值{m}！',
        msgZoomModalHeading:'文件预览',
        msgInvalidFileExtension:'不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
        msgNoFilesSelected:'请选择文件',
        msgValidationError:'文件类型不正确或文件过大',
        initialPreviewFileType:'rar',
        browseLabel:"选择文件",
        removeLabel:'删除',
        removeTitle:'删除文件',
        uploadLabel:'上传',
        uploadTitle:'上传文件',
        cancelLabel: '取消',
        cancelTitle: '取消上传',
        showPreview:false,
        autoReplace:true,
        slugCallback: function(filename) {
            return filename.replace('(', '_').replace(']', '_');
        }
    }).on('filepreupload', function(event, data, previewId, index) { //文件开始上传操作

        $("#xxcj_AJSL_Butt").prop('disabled', true);

    }).on('fileuploaded', function(event, data, previewId, index) {//文件上传完成执行操作

        $("#praise_Fileurl").val(data.response.url);
        $("#praise_Filename").val(data.response.fileRealName);
        $("#wsc4").text(data.response.fileRealName);
        $("#xxcj_AJSL_Butt").prop('disabled',false);
        butChange(4);
    });

    $("#reButton4").click(function(){
        butChange(4);
    });

});
</script>
</head>
<body>
<div class="center_weizhi">当前位置：推荐报送- 审核报送 - 报送国家 </div>
<div class="center">
<form id="ajslForm">
<div class="center_list">
  <table align="center" class="table_input">
    <tbody>

    <input type="hidden" class="form-control" name="areacodePro" id="areacodePro" value="${provinceSelectionUnit.areacodePro}">
	<input type="hidden" class="form-control" name="areatypePro" id="areatypePro" value="${areaUser.arealevel}"/>
    <input type="hidden" class="form-control" name="xxxx" id="xxxx" value="${provinceSelectionUnit.publicityway}"/>
		<tr>
	        <td width="400px">行政区</td>
	        <td style="text-align: left;" width="510px">
	         ${provinceSelectionUnit.areanamePro}
	        </td>
		</tr>
		<tr>
			<td>机构名称（全称）<i class="ace-icon fa fa-asterisk" style="color:red"></i></td>
			<td>
				<div class="form-group">
					<input
						<c:if test="${provinceReportUser.reportstate ==1 || sysInitConfig.code !='1'}">disabled="disabled"</c:if>
						type="text" class="form-control" id="companyname" name="companyname"
						value="${provinceSelectionUnit.companyname}"
						placeholder="请输入机构名称（全称）<c:if test="${areaUser.arealevel == '2'}">例如：河北省衡水市环境监察支队</c:if><c:if test="${areaUser.arealevel == '3'}">例如：河北省石家庄市环境保护局正定县分局</c:if>">
						
						<c:if test="${areaUser.arealevel == '3'}">
						<span style="color: red;">注意：综合行政执法改革之后取消的市辖区执法机构不能参评</span>
						</c:if>
				</div>
			</td>
			<td></td>
		</tr>
	<%--	<tr>
			<td>行政区内重点排污单位数量<i class="ace-icon fa fa-asterisk" style="color:red"></i></td>
			<td>
				<div class="form-group">
					<input type="text" class="form-control" id="polluternumber" name="polluternumber" value="${provinceSelectionUnit.polluternumber}" placeholder="请输入行政区内重点排污单位数量">
				</div>
			</td>
		</tr>--%>
			
		<%--<c:if test="${areaUser.arealevel == '2' && provinceSelectionUnit.areacodePro.substring(0,2)!='11' && provinceSelectionUnit.areacodePro.substring(0,2)!='12' && provinceSelectionUnit.areacodePro.substring(0,2)!='31' && provinceSelectionUnit.areacodePro.substring(0,2)!='55' && provinceSelectionUnit.areacodePro.substring(0,2)!='66'}">
			<tr>
		        <td>行政区内重点排污单位名录（加盖公章）<i class="ace-icon fa fa-asterisk" style="color:red"></i></td>
		        <td style="text-align:left;">
		        <span id="wsc" style="font-size:16px;">
		        <c:choose>
		        	<c:when test="${provinceSelectionUnit.polluterevidencename!='' && provinceSelectionUnit.polluterevidencename!=null }">
		        		<div style="float:left;margin-right:8px;padding-top:4px;">
		        			${provinceSelectionUnit.polluterevidencename}
		        		</div>
		        	</c:when>
		        </c:choose>
		        </span>
		        <span id="uploadTr" <c:if test="${provinceSelectionUnit.polluterevidencename!='' && provinceSelectionUnit.polluterevidencename!=null }">style='display:none'</c:if>>
			        <c:if test="${sysInitConfig.code =='1' && provinceReportUser.electcityState != 1 && provinceReportUser.isDevInternalEva !=1}">
			        <input id="polluterfile" type="file" name="polluterfile" title="提示：附件要求PDF格式，大小不超过50M。" class="file-loading">
			        <input type="hidden" class="form-control" name="polluterevidencename" id="polluter_Filename" value="${provinceSelectionUnit.polluterevidencename}">
					<input type="hidden" class="form-control" name="polluterevidenceurl" id="polluter_Fileurl" value="${provinceSelectionUnit.polluterevidenceurl}">
			        </c:if>
			      </span>
		        </td>
		        <td> 
		        	<c:if test="${sysInitConfig.code =='1' && provinceReportUser.electcityState !=1 && provinceReportUser.isDevInternalEva !=1}">
		        		<input id="reButton" <c:if test="${provinceSelectionUnit.polluterevidencename=='' || provinceSelectionUnit.polluterevidencename==null }">style='display:none'</c:if>
		        		 class="btn btn-danger btn-xs" type="button" value="重新上传"/>
		        	</c:if>
		        </td>
		      </tr>
			
			<tr>
				<td>公开方式<i class="ace-icon fa fa-asterisk" style="color:red"></i></td>
				<td><div class="form-group">
					<div class="col-sm-15">
						<select name="publicityway" id="publicityway"  onchange="listchange(this.value);return false;" class="form-control">
						   <option  value="">请选择</option>
						   <option  <c:if test="${provinceSelectionUnit.publicityway==0}">selected</c:if> value="0">无</option>
						   <option  <c:if test="${provinceSelectionUnit.publicityway==1}">selected</c:if> value="1">网站公开</option>
						   <option  <c:if test="${provinceSelectionUnit.publicityway==2}">selected</c:if> value="2">其他公开方式</option>
						 </select>
					</div>
					</div>
				</td>
			</tr>
		        
		     <tr id="tasklistoriginalno1" style="display:none;">
		        <td>公开网站地址</td>
		        <td>
		        	<div >
			        	<div class="form-group">
						<input type="text" class="form-control" id="website" name="website" value="${provinceSelectionUnit.website}" placeholder="请输入公开网站地址">
			        	</div>
		        	</div>
		        </td>
		     </tr>
		     <tr id="tasklistoriginalno2" style="display:none;">
		        <td>其他公开方式描述</td>
		        <td>
		        	<div class="form-group" >
						<textarea rows="6" class="form-control" id="publicitywaydescribe" name="publicitywaydescribe" placeholder="请输入其他公开方式描述">${provinceSelectionUnit.publicitywaydescribe}</textarea>
					</div>
		        </td>
		     </tr>--%>
				
		     
		      <%-- <tr id="uploadTr" <c:if test="${provinceSelectionUnit.polluterevidencename!='' && provinceSelectionUnit.polluterevidencename!=null }">style='display:none'</c:if>>
		        <c:if test="${sysInitConfig.code =='1' && provinceReportUser.electcityState != 1 && provinceReportUser.isDevInternalEva !=1}">
		        <td>&nbsp;</td>
		        <td style="text-align:left;">
		        <input id="polluterfile" type="file" name="polluterfile" class="file-loading">
		        <span style="color: red;">提示：附件要求PDF格式，大小不超过50M。</span>
		        <input type="hidden" class="form-control" name="polluterevidencename" id="polluter_Filename" value="${provinceSelectionUnit.polluterevidencename}">
				<input type="hidden" class="form-control" name="polluterevidenceurl" id="polluter_Fileurl" value="${provinceSelectionUnit.polluterevidenceurl}">
		        </td>
		        </c:if>
		      </tr> --%>
		<%--</c:if>--%>
		<tr>
	        <td>集体事迹材料<i class="ace-icon fa fa-asterisk" style="color:red"></i></td>
	        <td style="text-align:left;">
		        <span id="wsc2" style="font-size:16px;">
		        <c:choose>
		        	<c:when test="${provinceSelectionUnit.deedevidencename!='' && provinceSelectionUnit.deedevidencename!=null }">
		        		<div style="float:left;margin-right:8px;padding-top:4px;">
		        			${provinceSelectionUnit.deedevidencename}
		        		</div>
		        	</c:when>
		        </c:choose>
		        </span>
		        <span id="uploadTr2" <c:if test="${provinceSelectionUnit.deedevidencename!='' && provinceSelectionUnit.deedevidencename!=null }">style='display:none'</c:if>>
			        <c:if test="${sysInitConfig.code =='1' && provinceReportUser.electcityState !=1 && provinceReportUser.isDevInternalEva !=1}">
			        <input id="evidencefile" type="file" name="evidencefile" title="提示：要求1500字以内，请上传word文件，大小不超过1M。" class="file-loading">
						<span style="color: deepskyblue;">要求1500字以内，请上传word文件，大小不超过1M。</span>
			        <input type="hidden" class="form-control" name="deedevidencename" id="evidence_Filename" value="${provinceSelectionUnit.deedevidencename}">
					<input type="hidden" class="form-control" name="deedevidenceurl" id="evidence_Fileurl" value="${provinceSelectionUnit.deedevidenceurl}">
			        </c:if>
			    </span>
	        </td>
	        <td>
	        	<c:if test="${sysInitConfig.code =='1' && provinceReportUser.electcityState !=1 && provinceReportUser.isDevInternalEva !=1}">
	        		<input id="reButton2" <c:if test="${provinceSelectionUnit.deedevidencename=='' || provinceSelectionUnit.deedevidencename==null }">style='display:none'</c:if>
	        		 class="btn btn-danger btn-xs" type="button" value="重新上传"/>
	        	</c:if>
	        </td>
	      </tr>
			      <%-- <tr id="uploadTr2" <c:if test="${provinceSelectionUnit.deedevidencename!='' && provinceSelectionUnit.deedevidencename!=null }">style='display:none'</c:if>>
			        <td>&nbsp;</td>
			        <td style="text-align:left;">
			        <input id="evidencefile" type="file" name="evidencefile" class="file-loading">
			        <span style="color: red;">提示：附件要求DOC、DOCX，大小不超过1M。</span>
			        <input type="hidden" class="form-control" name="deedevidencename" id="evidence_Filename" value="${provinceSelectionUnit.deedevidencename}">
					<input type="hidden" class="form-control" name="deedevidenceurl" id="evidence_Fileurl" value="${provinceSelectionUnit.deedevidenceurl}">
			        </td>
			      </tr> --%>
				
		<tr>
			<td>集体照片<i class="ace-icon fa fa-asterisk" style="color:red"></i></td>
			<td style="text-align:left;">
		      <span id="wsc3" style="font-size:16px;">
		      <c:choose>
		      	<c:when test="${provinceSelectionUnit.deedpicturename!='' && provinceSelectionUnit.deedpicturename!=null }">
		      		<div style="float:left;margin-right:8px;padding-top:4px;">
		      			${provinceSelectionUnit.deedpicturename}
		      		</div>
		      	</c:when>
		      </c:choose>
		      </span>
		      <span id="uploadTr3" <c:if test="${provinceSelectionUnit.deedpicturename!='' && provinceSelectionUnit.deedpicturename!=null }">style='display:none'</c:if>>
			  	<c:if test="${sysInitConfig.code =='1' && provinceReportUser.electcityState !=1 && provinceReportUser.isDevInternalEva !=1}">
			       <input id="picturefile" type="file" name="picturefile" title="提示：请上传单位大门或集体照片一张。附件要求JPG、PNG、BMP格式，大小不超过20M。" class="file-loading">
			       <input type="hidden" class="form-control" name="deedpicturename" id="picture_Filename" value="${provinceSelectionUnit.deedpicturename}">
					<input type="hidden" class="form-control" name="deedpictureurl" id="picture_Fileurl" value="${provinceSelectionUnit.deedpictureurl}">
			   	</c:if>
				</span>
		 	</td>
	      <td> 
	      <c:if test="${sysInitConfig.code =='1' && provinceReportUser.electcityState !=1 && provinceReportUser.isDevInternalEva !=1}">
	      	<input id="reButton3" <c:if test="${provinceSelectionUnit.deedpicturename=='' || provinceSelectionUnit.deedpicturename==null }">style='display:none'</c:if>
	      		 class="btn btn-danger btn-xs" type="button" value="重新上传"/>
	      </c:if>
	      </td>
	    </tr>
	<tr>
		<td>是否因党建工作突出被表扬<i class="ace-icon fa fa-asterisk" style="color:red"></i></td>
		<td style="text-align: left">
			<div class="form-group">
				<div class="col-sm-15">
					<select <c:if test="${provinceReportUser.isDevInternalEva ==1 || provinceReportUser.electcityState == 1  || sysInitConfig.code !='1'}">disabled="disabled"</c:if>name="isPraise" id="isPraise"  onchange="listchange(this.value);return false;" class="form-control">
						<option  value="">请选择</option>
						<option  <c:if test="${provinceSelectionUnit.isPraise==0}">selected</c:if> value="0">是</option>
						<option  <c:if test="${provinceSelectionUnit.isPraise==1}">selected</c:if> value="1">否</option>
					</select>
				</div>
			</div>
			<span style="color: deepskyblue;">如选择是，请上传证明材料。</span>
		</td>
	</tr>

	<tr>
		<td>证明材料</td>
		<td style="text-align:left;">
									<span id="wsc4" style="font-size:16px;">
									<c:choose>
										<c:when test="${provinceSelectionUnit.praiseName!='' && provinceSelectionUnit.praiseName!=null }">
											<div style="float:left;margin-right:8px;padding-top:4px;">
													${provinceSelectionUnit.praiseName}
											</div>
										</c:when>
										<%-- <c:otherwise>
										<font>未上传</font>
										</c:otherwise> --%>
									</c:choose>
									</span>
			<span id="uploadTr4" <c:if test="${provinceSelectionUnit.praiseName!='' && provinceSelectionUnit.praiseName!=null }">style='display:none'</c:if>>
										<c:if test="${sysInitConfig.code =='1' && provinceReportUser.electcityState !=1 && provinceReportUser.isDevInternalEva !=1}">
											<input id="praiseFile" type="file" name="praiseFile" title="请上传rar格式文件，大小不超过50M。" class="file-loading">
											<input type="hidden" class="form-control" name="praiseName" id="praise_Filename" value="${provinceSelectionUnit.praiseName}">
											<input type="hidden" class="form-control" name="praiseUrl" id="praise_Fileurl" value="${provinceSelectionUnit.praiseUrl}">
										</c:if>
									</span>
		</td>
		<td>
			<c:if test="${sysInitConfig.code =='1' && provinceReportUser.electcityState !=1 && provinceReportUser.isDevInternalEva !=1}">
				<input id="reButton4" <c:if test="${provinceSelectionUnit.praiseName=='' || provinceSelectionUnit.praiseName==null }">style='display:none'</c:if>
					   class="btn btn-danger btn-xs" type="button" value="重新上传"/>
			</c:if>
		</td>
	</tr>
	<tr>
		<td></td>
		<td style="text-align:left;">
			<span style="color: deepskyblue">请上传rar格式文件，大小不超过50M。主要指因党建工作表现突出被各级组织部门表扬的情况，上传文件内容包含两部分：EXCEL文件（<a style="color: blue;" href="#" onclick="down()">模板下载</a>）和证明材料（PDF格式），证明材料名称应与Excel中材料名称保持一致。</span>
		</td>
	</tr>
		    
		<%--<tr>
			<td>考核奖惩制度</td>
			<td style="text-align: left;"><span id="wsc7"
				style="font-size: 16px;"> <c:choose>
						<c:when
							test="${provinceSelectionUnit.assessmentsystemname!='' && provinceSelectionUnit.assessmentsystemname!=null }">
							<div
								style="float: left; margin-right: 8px; padding-top: 4px;">
								${provinceSelectionUnit.assessmentsystemname}</div>
						</c:when>
					</c:choose>
				</span>
				<span id="uploadTr7" <c:if test="${provinceSelectionUnit.assessmentsystemname!='' && provinceSelectionUnit.assessmentsystemname!=null }">style='display:none'</c:if>>
					<c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
						<input id="assessmentfile" type="file" name="assessmentfile" title="提示：附件要求PDF或rar格式，大小不超过50M。" class="file-loading"> 
						<input type="hidden" class="form-control" name="assessmentsystemname" id="assessment_Filename" value="${provinceSelectionUnit.assessmentsystemname}"> 
						<input type="hidden" class="form-control" name="assessmentsystemurl" id="assessment_Fileurl" value="${provinceSelectionUnit.assessmentsystemurl}">
					</c:if>
				</span> 
			</td>
			<td>
				<c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
					<input id="reButton7" <c:if test="${provinceSelectionUnit.assessmentsystemname=='' || provinceSelectionUnit.assessmentsystemname==null }">style='display:none'</c:if> 
					class="btn btn-danger btn-xs" type="button" value="重新上传" />
				</c:if>
			</td>
		</tr>
		<tr>
			<td>执法人员人身安全保障制度</td>
			<td style="text-align: left;"><span id="wsc0"
				style="font-size: 16px;"> <c:choose>
						<c:when test="${provinceSelectionUnit.lawenforcnetworkname!='' && provinceSelectionUnit.lawenforcnetworkname!=null }">
							<div
								style="float: left; margin-right: 8px; padding-top: 4px;">
								${provinceSelectionUnit.lawenforcnetworkname}</div>
						</c:when>
					</c:choose>
				</span>
				<span id="uploadTr0" <c:if test="${provinceSelectionUnit.lawenforcnetworkname!='' && provinceSelectionUnit.lawenforcnetworkname!=null }">style='display:none'</c:if>>
					<c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
						<input id="lawenforcfile" type="file" name="lawenforcfile" title="提示：附件要求PDF或rar格式，大小不超过50M。" class="file-loading"> 
						<input type="hidden" class="form-control" name="lawenforcnetworkname" id="lawenforc_Filename" value="${provinceSelectionUnit.lawenforcnetworkname}">
						<input type="hidden" class="form-control" name="lawenforcnetworkurl" id="lawenforc_Fileurl" value="${provinceSelectionUnit.lawenforcnetworkurl}">
					</c:if>
				</span> 
			</td>
			<td>
				<c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
					<input id="reButton0" <c:if test="${provinceSelectionUnit.lawenforcnetworkname=='' || provinceSelectionUnit.lawenforcnetworkname==null }">style='display:none'</c:if> 
					class="btn btn-danger btn-xs" type="button" value="重新上传" />
				</c:if>
			</td>
		</tr>

		<tr>
			<td>尽职照单免责和失职照单问责制度</td>
			<td style="text-align: left;"><span id="wsc9"
				style="font-size: 16px;"> <c:choose>
						<c:when
							test="${provinceSelectionUnit.accountabilityname!='' && provinceSelectionUnit.accountabilityname!=null }">
							<div style="float: left; margin-right: 8px; padding-top: 4px;">
								${provinceSelectionUnit.accountabilityname}</div>
						</c:when>
					</c:choose>
				</span>
				<span id="uploadTr9"
					<c:if test="${provinceSelectionUnit.accountabilityname!='' && provinceSelectionUnit.accountabilityname!=null }">style='display:none'</c:if>>
					<c:if test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
						<input id="accountabilityfile" type="file" name="accountabilityfile" title="提示：附件要求PDF或rar格式，大小不超过50M。" class="file-loading"> 
						<input type="hidden" class="form-control" name="accountabilityname" id="accountability_Filename" value="${provinceSelectionUnit.accountabilityname}"> 
						<input type="hidden" class="form-control" name="accountabilityurl" id="accountability_Fileurl" value="${provinceSelectionUnit.accountabilityurl}">
					</c:if>
				</span> 
			</td>
			<td>
				<c:if
					test="${sysInitConfig.code =='1' && provinceReportUser.reportstate !=1}">
					<input id="reButton9" <c:if test="${provinceSelectionUnit.accountabilityname=='' || provinceSelectionUnit.accountabilityname==null }">style='display:none'</c:if> 
					class="btn btn-danger btn-xs" type="button" value="重新上传" />
				</c:if>
			</td>--%>
		</tr>
	
		<tr>
			<td align="center">&nbsp;
			<input type="hidden" name="id" id="id" value="${provinceSelectionUnit.id }"/>
			</td>
			<td style="text-align:left;"><a href="#">
		    <button type="button"  id="xxcj_AJSL_Butt" class="btn btn-danger" name="signup" value="Sign up" style="font-size:16px;width:120px;">信息保存</button>
		    </a></td>
		</tr>
    </tbody>
  </table>
    </div>
    </form>
</div>
<script type="text/javascript">


	//表单校验
	$(document).ready(function() {
	$('#ajslForm').formValidation({
		message: 'This value is not valid',
	    icon: {
	    	valid: 'glyphicon glyphicon-ok',
	        invalid: 'glyphicon glyphicon-remove',
	        validating: 'glyphicon glyphicon-refresh'
	    },
	    autoFocus: true,
	    fields: {
	    	"companyname" : {
				validators : {
					notEmpty : {
						message : '请填写机构名称（全称）.'
					}
				}
			},
	        /*"polluternumber": {
	        	verbose: false,
	        	validators: {
	            	notEmpty: {
	                	message: '请填写行政区内重点排污单位数量.'
	                },
	                regexp: {
	                    regexp: /^[0-9]+$/,
	                    message: '该项只能填写整数.'
	                },
	                greaterThan: {
                        value:0,
                        message:' '
                    },
					lessThan: {
                        value:10000,
                        message: '该项为小于等于10000的整数.'
                    }
	            }
	        },
	        "publicityway": {
                validators: {
                    notEmpty: {
                        message: '请选择公开方式'
                    }
                }
            },
            "website": {
            	enabled:false,
            	verbose: false,
            	validators: {
                    notEmpty: {
                        message: '请输入信息公开网址'
                    },
                     regexp: {
 	                    regexp: 
	                          /^([hH][tT]{2}[pP]:\/\/|[hH][tT]{2}[pP][sS]:\/\/)/,
	                    message: '请输入正确网址'
 	                }
                   }
            },
            "publicitywaydescribe": {
            	enabled:false,
            	verbose: false,
            	validators: {
                    notEmpty: {
                        message: '请输入其他公开方式描述'
                    },
	                stringLength: {
	                	max: 300,
	                	message: '该项不能超过300字符.'
	                },
	                  regexp:{
	                         message:'不允许输入非法的字符<、>',
	                         regexp:/^(?!.*(\<|\>))/
	                    }
                }
            }*/
	              
	        }
	    });
	});
/*	//关联验证
	$("#publicityway").on('change', function(){
	    if($(this).val() == '1'){
	    	$('#ajslForm').formValidation('enableFieldValidators','website',true);
	    	$('#ajslForm').formValidation('enableFieldValidators','publicitywaydescribe',false);
	    	$('#publicitywaydescribe').prop('disabled', true);
	    	$('#website').prop('disabled', false);
	    	$('#website').val("")
	    }else if($(this).val() == '2'){
	    	$('#ajslForm').formValidation('enableFieldValidators','website',false);
	    	$('#ajslForm').formValidation('enableFieldValidators','publicitywaydescribe',true);
	    	$('#publicitywaydescribe').prop('disabled', false);
	    	$('#website').prop('disabled', true);
	    	$('#publicitywaydescribe').val("")
	    }else if($(this).val() == '0'){
	    	$('#ajslForm').formValidation('enableFieldValidators','website',false);
	    	$('#ajslForm').formValidation('enableFieldValidators','publicitywaydescribe',false);
	    	$('#tasklistoriginalno1').hide();
	    	$('#tasklistoriginalno2').hide();
	    }
	});*/
	//表单提交
	$(document).ready(function(){
		$("#xxcj_AJSL_Butt").click(function(){
			var validate = false;
			/*if($("#areatypePro").val()==3){
				$('#ajslForm').formValidation('enableFieldValidators','publicityway',false);
			}*/
			$("#ajslForm").data('formValidation').validate();
			validate = $("#ajslForm").data('formValidation').isValid();
			if(validate){
				var fileurl=$("#polluter_Fileurl").val();
       			var filename=$("#polluter_Filename").val();
       			
       			var fileurl1=$("#evidence_Fileurl").val();
       			var filename1=$("#evidence_Filename").val();

                var isPraise=$("#isPraise").val();

                var fileurl3=$("#praise_Fileurl").val();
                var filename3=$("#praise_Filename").val();

       			var fileurl2=$("#picture_Fileurl").val();
       			var filename2=$("#picture_Filename").val();

                if(fileurl1!=""&&filename1!=""){

                    if(fileurl2!=""&&filename2!=""){

                        if(isPraise !=null && isPraise != ""){

                            if ((isPraise=='0' && fileurl3!=""&&filename3!="") || (isPraise=='1' && fileurl3==""&&filename3=="") || (isPraise=='1')) {
								var options = {
									url:WEBPATH+'/snpb/saveProvinceBase.do',
									type:'post',
									success:function(data){

									if(data.result=="error"){
										swal({title:data.message, text:"", type:"error",confirmButtonColor: "#d9534f"});
										return false;
									 }else if(data.result=="success"){
										swal({
												title: "保存成功!",
												type: "success",
												closeOnConfirm: true,
												confirmButtonText: "确定",
												confirmButtonColor: "#d9534f"
											}, function() {
												business.addMainContentParserHtml('snpb/snpb_list.do','');
										});
									   return false;
									 }
									},
						error:function(){
							swal({title:"服务异常,保存失败!", text:"", type:"error",confirmButtonColor: "#d9534f"});
						}
					};
					$("#ajslForm").ajaxSubmit(options);
                            }else {
                                swal({title:"未上传附件",text:"请上传证明材料!",type:"error",confirmButtonColor: "#d9534f"});
                                return  false ;
                            }
                        }else {
                            swal({title:"请选择是否因党建工作突出被表扬!",/*text:"请选择是否因党建工作突出被表扬!",*/type:"error",confirmButtonColor: "#d9534f"});
                            return  false ;
                        }
                    }else{
                        swal({title:"未上传附件",text:"请上传集体照片!",type:"error",confirmButtonColor: "#d9534f"});
                        return  false ;
                    }
                }else{
                    swal({title:"未上传附件",text:"请上传集体事迹材料!",type:"error",confirmButtonColor: "#d9534f"});
                    return  false ;
                }

			}else if(validate==null){
				//表单未填写
				$("#ajslForm").data('formValidation').validate();
	       
	        }
		});
	});
	
$(document).ready(function() {
	$(".topnav").accordion({
		accordion:false,
		speed: 500,
		closedSign: '[+]',
		openedSign: '[-]'
	});
});
/*function listchange(value){
	
	var sel=document.getElementsByName('publicityway');
	for(var i=0;i<publicityway.options.length;i++)
	{
	 if(publicityway.options[i].selected)
	 {
	  //if(tasklist_type.options[i].value!=1){
		//document.getElementById( "tasklistoriginalno1").style.display= "none";
		//document.getElementById("tasklist_originalno1").value = "";
	
	  //}
	  if(publicityway.options[i].value==1){
		document.getElementById("tasklistoriginalno1").style.display= "";
		document.getElementById("tasklistoriginalno2").style.display= "none";
	  }
	  if(publicityway.options[i].value==2){
		document.getElementById("tasklistoriginalno2").style.display= "";
		document.getElementById("tasklistoriginalno1").style.display= "none";
	  }
	 }
	}
 
}
$(document).ready(function() {

	var value = $("#xxxx").val();
	if(value == 1){
		document.getElementById("tasklistoriginalno1").style.display= "";
		document.getElementById("tasklistoriginalno2").style.display= "none";
	}
	if(value == 2){
		document.getElementById("tasklistoriginalno1").style.display= "none";
		document.getElementById("tasklistoriginalno2").style.display= "";
	}
})*/
    function down() {
        var name = "市县级表现突出集体";
        var path = "${webpath}/xxcj/xcclExcel.do";
        window.location.href = path + "?name="+name;
    }
</script>
</body>
</html>
