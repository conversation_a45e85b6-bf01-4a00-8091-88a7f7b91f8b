<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<div class="center_weizhi">当前位置：推荐报送- 审核报送 - 报送国家</div>
<div class="center">
<div class="center_list">
	 <input type="hidden" id="areatypePro" name="areatypePro" value="3">
	 <input type="hidden" id="areacodePro" name="areacodePro"  value="${areacodePro}">
       <h4 style="font-weight:bold;">县级案件</h4>
         <table class="table table-bordered table-hover table-condensed">
         <thead>
           <tr>
             <th width="50" height="30" bgcolor="#efefef">序号</th>
             <th bgcolor="#efefef">参选单位</th>
             <th width="300" bgcolor="#efefef">案卷类型</th>
             <th width="300" bgcolor="#efefef">案卷文号</th>
             <th width="60" bgcolor="#efefef">操作</th>
           </tr>
         </thead>
         <tbody> 
	     	<c:forEach var="provinceFiles" items="${provinceFilesList}" varStatus="status">
         	<c:choose>
	         	<c:when test="${status.index==0}">
		         	<tr>
		             <td height="30" align="center" rowspan="5" style="vertical-align: middle;">1</td>
		             <td rowspan="5" style="vertical-align: middle;">${areanamePro }</td>
		             <td>
		             	 <c:choose>
			             	 <c:when test="${provinceFiles.filetypePro == '0'}">一般行政处罚</c:when>
				             <c:when test="${provinceFiles.filetypePro == '5'}">稽查</c:when>
				             <c:otherwise >配套办法</c:otherwise>
                         </c:choose>
					 </td>
		             <td>${provinceFiles.filecodePro}</td>
		             <td>
		             <c:choose>
		             	<c:when test="${provinceFiles.filetypePro == '0'}">
		             		<c:choose>
		             			<c:when test="${provinceFiles.id==null}">
		             				<button class="btn btn-danger btn-xs" onclick="macroMgr.onLevelTwoMenuClick(null, 'snpb/caseUpdate.do?filetypePro=${provinceFiles.filetypePro}&joinunitPro=${areanamePro}&areacodePro=${areacodePro}&areatypePro=3')">新增</button>
		             			</c:when>
		             			<c:otherwise>
		             				<button class="btn btn-danger btn-xs" onclick="macroMgr.onLevelTwoMenuClick(null, 'snpb/caseUpdate.do?id=${provinceFiles.id}&filetypePro=${provinceFiles.filetypePro}&joinunitPro=${areanamePro}&areacodePro=${areacodePro}&areatypePro=3')">编辑</button>
		             			</c:otherwise>
		             		</c:choose>
		             	</c:when>
		             	<c:otherwise>
		             		<c:choose>
		             			<c:when test="${provinceFiles.id==null}">
		             				<button class="btn btn-danger btn-xs" onclick="macroMgr.onLevelTwoMenuClick(null, 'snpb/caseUpdate.do?filetypePro=${provinceFiles.filetypePro}&joinunitPro=${areanamePro}&areacodePro=${areacodePro}&areatypePro=3')">新增</button>
		             			</c:when>
		             			<c:otherwise>
		             				<button class="btn btn-danger btn-xs" onclick="macroMgr.onLevelTwoMenuClick(null, 'snpb/caseUpdate.do?id=${provinceFiles.id}&filetypePro=${provinceFiles.filetypePro}&joinunitPro=${areanamePro}&areacodePro=${areacodePro}&areatypePro=3')">编辑</button>
		             			</c:otherwise>
		             		</c:choose>
		             	</c:otherwise>
		             </c:choose>
		             
		             </td>
		           </tr>
	           </c:when>
	           <c:otherwise>
		           <tr>
		             <td>
		             	 <c:choose>
			             	 <c:when test="${provinceFiles.filetypePro == '0'}">一般行政处罚</c:when>
				             <c:when test="${provinceFiles.filetypePro == '5'}">稽查</c:when>
				             <c:otherwise >配套办法</c:otherwise>
                         </c:choose>
		             </td>
		             <td>${provinceFiles.filecodePro}</td>
		             <td>
		             	<c:choose>
		             	<c:when test="${provinceFiles.filetypePro == '0'}">
		             		<c:choose>
		             			<c:when test="${provinceFiles.id==null}">
		             				<button class="btn btn-danger btn-xs" onclick="macroMgr.onLevelTwoMenuClick(null, 'snpb/caseUpdate.do?filetypePro=${provinceFiles.filetypePro}&joinunitPro=${areanamePro}&areacodePro=${areacodePro}&areatypePro=3')">新增</button>
		             			</c:when>
		             			<c:otherwise>
		             				<button class="btn btn-danger btn-xs" onclick="macroMgr.onLevelTwoMenuClick(null, 'snpb/caseUpdate.do?id=${provinceFiles.id}&filetypePro=${provinceFiles.filetypePro}&joinunitPro=${areanamePro}&areacodePro=${areacodePro}&areatypePro=3')">编辑</button>
		             			</c:otherwise>
		             		</c:choose>
		             	</c:when>
		             	
		             	<c:otherwise>
		             		<c:choose>
		             			<c:when test="${provinceFiles.id==null}">
		             				<button class="btn btn-danger btn-xs" onclick="macroMgr.onLevelTwoMenuClick(null, 'snpb/caseUpdate.do?filetypePro=${provinceFiles.filetypePro}&joinunitPro=${areanamePro}&areacodePro=${areacodePro}&areatypePro=3')">新增</button>
		             			</c:when>
		             			<c:otherwise>
		             				<button class="btn btn-danger btn-xs" onclick="macroMgr.onLevelTwoMenuClick(null, 'snpb/caseUpdate.do?id=${provinceFiles.id}&filetypePro=${provinceFiles.filetypePro}&joinunitPro=${areanamePro}&areacodePro=${areacodePro}&areatypePro=3')">编辑</button>
		             			</c:otherwise>
		             		</c:choose>
		             	</c:otherwise>
		             </c:choose>
		             </td>
		           </tr>
	           </c:otherwise>
			</c:choose>
         </c:forEach>
         </tbody>
       </table>
</div>
</div>
</body>
</html>