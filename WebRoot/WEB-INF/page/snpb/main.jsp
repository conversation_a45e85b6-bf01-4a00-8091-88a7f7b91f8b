<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>

<%-- <script type="text/javascript" src="${webpath}/static/businessJs/xxcj/xxcjMain.js"></script> --%>
<script type="text/javascript">
$(document).ready(function(){
	//绑定主菜单单击方法，设置样式
	$(".topnav li").bind("click",function(){
		$(".topnav li a").removeClass("active");
		$(this).children(0).addClass("active");
	});
	
	var skipTo = '${Flag}';
	//触发点击事件
	if(skipTo==""){
		$(".topnav li a:first").click();
	}else{
		$(".topnav:eq(1)").find('li a:eq(0)').click();
	}
	
});
</script> 
<body>
<!--框架左侧菜单 开始-->

<div class="frame_left">
	<div class="left_menu">
	<c:if test="${sessionScope.sa_session.userTypeCode == '1' }">
		
        
        <%--<div class="left_menu_bt"><img src="${webpath}/static/images/caiji_1.png" style="margin-right:5px;" />省内评比指标配置</div>
        <div class="tree">
            <ul class="topnav">
            	<!-- 只有省级有此菜单 --> 
            	<c:if test="${sessionScope.sa_session.arealevel=='1' }">
                	<li><a href="javascript:void(0);" onclick="macroMgr.onLevelTwoMenuClick(null, 'provinceScore/indexConfig.do?type=0')" class="active"><img src="${webpath}/static/images/shubiao.png" />市级指标</a></li>
                </c:if>
                <!-- 只有省级有此菜单 --> 
            	<c:if test="${sessionScope.sa_session.arealevel=='1' && userareacode!='11000000'&&userareacode!='12000000'&&userareacode!='55000000'&&userareacode!='31000000'&&userareacode!='66000000'}">
                	<li><a href="javascript:void(0);" onclick="macroMgr.onLevelTwoMenuClick(null, 'provinceScore/indexConfig.do?type=1')" class="active"><img src="${webpath}/static/images/shubiao.png" />县级指标</a></li>
                </c:if>
                <!-- 只有省级有此菜单 --> 
            	<c:if test="${sessionScope.sa_session.arealevel=='1' }">
                	<li><a href="javascript:void(0);" onclick="macroMgr.onLevelTwoMenuClick(null, 'provinceScore/indexConfig.do?type=2')" class="active"><img src="${webpath}/static/images/shubiao.png" />个人指标</a></li>
                </c:if>
            </ul>
        </div>
        
        <div class="left_menu_bt"><img src="${webpath}/static/images/caiji_1.png" style="margin-right:5px;" />省内评比启动设置</div>
        <div class="tree">
            <ul class="topnav">
                <li><a href="#" onclick="macroMgr.onLevelTwoMenuClick(null, 'sys2018/setSnpbSwitch.do')"><img src="${webpath}/static/images/shubiao.png" />省内评比启动</a></li>
            </ul>
        </div>--%>
        
        
		<c:if test="${unitUser.isDevInternalEva!=null }">
			<div class="left_menu_bt"><img src="${webpath}/static/images/caiji_1.png" style="margin-right:5px;" /><%--省内自评结果--%>集体和个人推荐</div>
	        <div class="tree">
	            <ul class="topnav">
	            	<c:if test="${unitUser.isDevInternalEva==1}">
	                	<li><a href="#" onclick="macroMgr.onLevelTwoMenuClick(null, 'provinceScore/goCityScore.do')"  class="active"><img src="${webpath}/static/images/shubiao.png" />候选集体地市综合评分</a></li>
	                	<c:if test="${userareacode!='11000000'&&userareacode!='12000000'&&userareacode!='55000000'&&userareacode!='31000000'&&userareacode!='66000000'}">
	                	<li><a href="#" onclick="macroMgr.onLevelTwoMenuClick(null, 'provinceScore/goCountryScore.do')"  class="active"><img src="${webpath}/static/images/shubiao.png" />候选集体区县综合评分</a></li>
	                	</c:if>
	                	<li><a href="#" onclick="macroMgr.onLevelTwoMenuClick(null, 'provinceScore/goPersonScore.do')"  class="active"><img src="${webpath}/static/images/shubiao.png" />候选个人综合评分</a></li>
	            	</c:if>
	                <li><a href="#" onclick="macroMgr.onLevelTwoMenuClick(null, 'recom/RecommendCityDeed.do')"  class="active"><img src="${webpath}/static/images/shubiao.png" />候选市级集体推荐</a></li>
	                <c:if test="${userareacode!='11000000'&&userareacode!='12000000'&&userareacode!='55000000'&&userareacode!='31000000'&&userareacode!='66000000'}">
	                	<li><a href="javascript:void(0);" onclick="macroMgr.onLevelTwoMenuClick(null, 'recom/RecommendCountryDeed.do')"  class="active"><img src="${webpath}/static/images/shubiao.png" />候选县级集体推荐</a></li>
	               	</c:if>
	                <li><a href="#" onclick="macroMgr.onLevelTwoMenuClick(null, 'recom/RecommendPersonal.do')"  class="active"><img src="${webpath}/static/images/shubiao.png" />候选个人推荐</a></li>                
	            </ul>
	        </div>
	        
	        <div class="left_menu_bt"><img src="${webpath}/static/images/caiji_1.png" style="margin-right:5px;" />审核报送</div>
	        <div class="tree">
	            <ul class="topnav">
	                <li><a href="javascript:void(0);" onclick="macroMgr.onLevelTwoMenuClick(null, 'snpb/snpb_list.do')"><img src="${webpath}/static/images/shubiao.png" />报送国家</a></li>
	            </ul>
	        </div>
        </c:if>

        <div class="left_menu_bt"><img src="${webpath}/static/images/caiji_1.png" style="margin-right:5px;" />信息汇总</div>
        <div class="tree">
            <ul class="topnav">
            	<%--<li><a href="javascript:void(0);" onclick="macroMgr.onLevelTwoMenuClick(null, 'provinceScore/goNoFilesUnitList.do')"><img src="${webpath}/static/images/shubiao.png" />未上报案卷单位列表</a></li>--%>
                <li><a href="javascript:void(0);" onclick="macroMgr.onLevelTwoMenuClick(null, 'recom/basicDataList.do')"><img src="${webpath}/static/images/shubiao.png" />行政区基础数据列表</a></li>
                <li><a href="javascript:void(0);" onclick="macroMgr.onLevelTwoMenuClick(null, 'xxcj/personalTotalProList.do')"><img src="${webpath}/static/images/shubiao.png" />候选个人信息列表</a></li>
                <%--<li><a href="javascript:void(0);" onclick="macroMgr.onLevelTwoMenuClick(null, 'recom/polluterDataList.do')"><img src="${webpath}/static/images/shubiao.png" />重点排污单位数量列表</a></li>--%>
<%--                <li><a href="javascript:void(0);"  onclick="macroMgr.onLevelTwoMenuClick(null, 'xxcj/jcajTotalProList.do')" ><img src="${webpath}/static/images/shubiao.png" />稽查案卷列表</a></li>--%>
                <li><a href="javascript:void(0);" onclick="macroMgr.onLevelTwoMenuClick(null, 'provinceScore/goUnitList.do')"><img src="${webpath}/static/images/shubiao.png" />参选单位列表</a></li>
                <li><a href="javascript:void(0);" onclick="macroMgr.onLevelTwoMenuClick(null, 'provinceScore/goFileList.do')"><img src="${webpath}/static/images/shubiao.png" />案卷列表</a></li>
            </ul>
        </div>
    </c:if>
        
        <c:if test="${sessionScope.sa_session.loginid == 'changneng'||sessionScope.sa_session.loginid == 'sysAdmin' }">
        <div class="left_menu_bt"><img src="${webpath}/static/images/caiji_1.png" style="margin-right:5px;" />信息汇总</div>
        <div class="tree">
            <ul class="topnav">
            	<%--<li><a href="javascript:void(0);" onclick="macroMgr.onLevelTwoMenuClick(null, 'provinceScore/goNoFilesUnitList.do')"><img src="${webpath}/static/images/shubiao.png" />未上报案卷单位列表</a></li>--%>
                <li><a href="javascript:void(0);" onclick="macroMgr.onLevelTwoMenuClick(null, 'recom/basicDataList.do')"><img src="${webpath}/static/images/shubiao.png" />行政区基础数据列表</a></li>
                <li><a href="javascript:void(0);" onclick="macroMgr.onLevelTwoMenuClick(null, 'xxcj/goPersonalListSys.do')"><img src="${webpath}/static/images/shubiao.png" />候选个人信息列表</a></li>
                <%--<li><a href="javascript:void(0);" onclick="macroMgr.onLevelTwoMenuClick(null, 'recom/polluterDataList.do')"><img src="${webpath}/static/images/shubiao.png" />重点排污单位数量列表</a></li>--%>
<%--                <li><a href="javascript:void(0);" onclick="macroMgr.onLevelTwoMenuClick(null, 'xxcj/jcajTotalProList.do')" ><img src="${webpath}/static/images/shubiao.png" />稽查案卷列表</a></li>--%>
                <li><a href="javascript:void(0);" onclick="macroMgr.onLevelTwoMenuClick(null, 'provinceScore/goUnitListSys.do')"><img src="${webpath}/static/images/shubiao.png" />参选单位列表</a></li>
                <li><a href="javascript:void(0);" onclick="macroMgr.onLevelTwoMenuClick(null, 'provinceScore/goFileList.do')"><img src="${webpath}/static/images/shubiao.png" />案卷列表</a></li>
            </ul>
        </div>
        </c:if>
        <%-- <c:if test="${sessionScope.sa_session.userTypeCode == '1' && sessionScope.sa_session.arealevel=='1'}">
        <div class="left_menu_bt"><img src="${webpath}/static/images/caiji_1.png" style="margin-right:5px;" />${areaUser.arealevel}审核报送</div>
        <div class="tree">
            <ul class="topnav">

                <li><a href="javascript:void(0);" onclick="macroMgr.onLevelTwoMenuClick(null, '/recom/basicDataList.do')"><img src="${webpath}/static/images/shubiao.png" />行政区基础数据列表</a></li>
          		<li><a href="javascript:void(0);" onclick="macroMgr.onLevelTwoMenuClick(null, '/recom/polluterDataList.do')"><img src="${webpath}/static/images/shubiao.png" />重点排污单位数量列表</a></li>
	                <li><a href="javascript:void(0);" onclick="macroMgr.onLevelTwoMenuClick(null, '/recom/basicDataList.do')"><img src="${webpath}/static/images/shubiao.png" />行政区基础数据列表</a></li>
           			<li><a href="javascript:void(0);" onclick="macroMgr.onLevelTwoMenuClick(null, '/recom/polluterDataList.do')"><img src="${webpath}/static/images/shubiao.png" />重点排污单位数量列表</a></li>
	                <li><a href="javascript:void(0);" onclick="macroMgr.onLevelTwoMenuClick(null, 'xxcj/examineSubmit.do')"><img src="${webpath}/static/images/shubiao.png" />信息报送</a></li>
           		<li><a href="javascript:void(0);" onclick="macroMgr.onLevelTwoMenuClick(null, '/provinceScore/goUnitList.do')"><img src="${webpath}/static/images/shubiao.png" />参选单位列表</a></li>
            	<li><a href="javascript:void(0);" onclick="macroMgr.onLevelTwoMenuClick(null, '/provinceScore/goFileList.do')"><img src="${webpath}/static/images/shubiao.png" />案卷列表</a></li>
            </ul>
        </div>
        </c:if> --%>
    </div>
</div>
<!--框架左侧菜单 结束-->

<!-- 页面中部~中间 start  -->
<div id="main_content" >
    

</div>
</body>