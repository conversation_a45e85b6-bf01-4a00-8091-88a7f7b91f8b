<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>

</head>

<body>
<div id="scoreVue">
<div class="center_weizhi">当前位置：推荐报送 - 集体和个人推荐 - 候选个人综合评分</div>
<div class="center">
<div class="center_list">
		 <h4 style="color:#F00;">提示：综合得分等于各指标分值*权重占比，相加之和</h4>	
		<c:if test="${sysInitConfig.code =='1'}">
			<button type="submit" v-on:click="rank()" class="btn btn-danger" name="signup" value="Sign up" style="font-size:16px;width:120px;right:0;top:5px;position:absolute;">汇总排名</button>
        </c:if>
        <table class="table table-bordered table-hover table-condensed">
         <thead>
           <tr>
             <th height="30" bgcolor="#efefef">省</th>
             <th height="30" bgcolor="#efefef">市</th>
             <th height="30" bgcolor="#efefef">县</th>
             <th bgcolor="#efefef">个人姓名</th>
             <th bgcolor="#efefef">所在单位名称</th>
             <th bgcolor="#efefef">身份证号码</th>
             <c:forEach items="${indexList}" var="index">
             	<th bgcolor="#efefef">${index.indexName}</th>
             </c:forEach>
             <th bgcolor="#efefef">综合得分</th>
             <th bgcolor="#efefef">省内个人排名</th>
             <th bgcolor="#efefef" style="width:60px;">操作</th>
           </tr>
         </thead>
         <tbody>
          <tr v-for="(obj,i) in list">
         	 <td style="vertical-align: middle;">{{obj.provincePro}}</td>
             <td style="vertical-align: middle;">{{obj.cityPro}}</td>
             <td style="vertical-align: middle;">{{obj.countryPro}}</td>
             <td style="vertical-align: middle;">{{obj.namePro}}</td>
             <td style="vertical-align: middle;">{{obj.unitnamePro}}</td>
             <td style="vertical-align: middle;">{{obj.cardidPro}}</td>
             <td v-for="(indexScore,j) in obj.indexScoreList">
             	{{indexScore.score}}
             </td>
             <td style="vertical-align: middle;">{{obj.altogetherscorePro}}</td>
             <td style="vertical-align: middle;">{{obj.provincerankingPro}}</td>
           	 <td>
           	 	<c:if test="${sysInitConfig.code =='1'}">
           	 		<button v-if="obj.provincerankingPro==null" class="btn btn-danger btn-xs" v-on:click="score(i)">综合评分</button>
           	 	</c:if>
           	 </td>
           </tr>
         </tbody>
       </table>
    </div>
    
    <div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
	    <div class="modal-dialog">
	        <div class="modal-content">
	            <div class="modal-header">
	                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
	                <h4 class="modal-title">候选个人综合评分</h4>
	            </div>
	            <div class="modal-body form-horizontal" style="font-size:16px;">
	            	<div class="form-group" style="padding:10px;">
	                	<label class="col-lg-3 control-label">行政区</label>
	                    <div class="col-lg-8" style="margin-top:7px;">
	                	{{unit.provincePro}}{{unit.cityPro}}{{unit.countryPro}}
	                    </div>
	                </div>
	            	<div v-for="(index,i) in unit.indexScoreList" class="form-group" style="padding:10px;">
	                	<label class="col-lg-3 control-label">{{index.indexName}}</label>
	                    <div class="col-lg-6">
	                    	<div :id="'indexMsg'+i" style="margin:0 0 5px 0;">
				             	<input type="text" @input="updateScore(i)" class="form-control" v-model="index.score" placeholder="请输入分数">
				            </div>
				            <div style="color:red; font-size: 12px;">{{index.indexMsg}}</div>
	                    </div>
	                </div>
	            </div>
	            <div class="modal-footer"><button type="button" class="btn btn-primary" v-on:click="scoreSave()">确定</button>
	                <button type="button" class="btn btn-default" v-on:click="socreClose()">关闭</button>
	            </div>
	        </div>
	    </div>
	</div>
    
    <!--列表翻页 开始-->
    <div class="page">
    	<span style="padding:12px; float:left; color:#0099cc;">共${PageBean.total}条记录</span>
        <ul class="pagination" id="pageCon">
        </ul>
    </div>
    
</div>
</div>
</body>
<script type="text/javascript">
	var str = '${json}';
	var list = JSON.parse(str);
	
	var listStr = '';
	var unitStr = '${unitjson}';
	var unit = JSON.parse(unitStr);
	
	
	var scoreVue = new Vue({
		el: '#scoreVue',
		data:{
			list:list,
			unit:unit
		},
		methods:{
			score:function(i){
				scoreVue.unit = scoreVue.list[i];
				unitStr = JSON.stringify(scoreVue.unit);
				listStr = JSON.stringify(scoreVue.list);
				$('#myModal').modal({backdrop:false});
			},
			socreClose:function(){
				scoreVue.unit = JSON.parse(unitStr);
				scoreVue.list = JSON.parse(listStr);
				var indexList = scoreVue.unit.indexScoreList;
				for (var i = 0; i < indexList.length; i++) {
					$("#indexMsg"+i).removeClass("has-success");
					$("#indexMsg"+i).removeClass("has-error");	
				}
				
				$('#myModal').modal('hide');
			},
			updateScore:function(i){
				var index = scoreVue.unit.indexScoreList[i];
				
				var indexScore = parseFloat(index.indexScore);
				var indexName = index.indexName;
				var score = parseFloat(index.score);
				
				var reg = /^\d+(?=\.{0,1}\d+$|$)/;
				if(reg.test(score)){
					if(score>indexScore){
						$("#indexMsg"+i).removeClass("has-success");	
						$("#indexMsg"+i).addClass("has-error");
						index.indexMsg = "不能超过满分";
						index.indexVail = false;
						return;
					}
				}else{
					$("#indexMsg"+i).removeClass("has-success");	
					$("#indexMsg"+i).addClass("has-error");
					index.indexMsg = "分值不合规范";
					index.indexVail = false;
					return;
				}
				$("#indexMsg"+i).removeClass("has-error");	
				$("#indexMsg"+i).addClass("has-success");
				index.indexVail = true;
				index.indexMsg = "";
			},
			scoreSave:function(){
				//验证
				var totalScore = 0;
				
				var indexScoreList = scoreVue.unit.indexScoreList;
				for (var i = 0; i < indexScoreList.length; i++) {
					if(indexScoreList[i].indexVail==false){
						$("#indexMsg"+i).removeClass("has-success");	
						$("#indexMsg"+i).addClass("has-error");
						indexScoreList[i].indexMsg = "分值不合规范";
						swal({title:"提示!", text:"请确保信息填写正确", type:"info",confirmButtonColor: "#d9534f"});
						return;
					}
					var score = parseFloat(indexScoreList[i].score)*parseFloat(indexScoreList[i].weight)/100//加权重
					//var score = parseFloat(indexScoreList[i].score)//不加权重
					totalScore = totalScore + score;
				}
				totalScore = parseFloat(totalScore).toFixed(2);
				scoreVue.unit.altogetherscorePro = totalScore;
				
				$('#myModal').modal('hide');
				$.ajax({
					type:"post",
					url:WEBPATH+"/provinceScore/scoreSavePerson.do",
					data:JSON.stringify(scoreVue.unit),
					dataType:"json",
					contentType : 'application/json',
					success:function(data){
						if(data.code=='200'){
							swal({title:"保存成功!", text:"", type:"success",confirmButtonColor: "#d9534f"});
							business.addMainContentParserHtml(WEBPATH+"/provinceScore/goPersonScore.do?pageNum="+"${pageNum}");
						}else if(data.result=="failure"){
				    		swal({title:"提示!", text:"信息保存操作失败了!", type:"error",confirmButtonColor: "#d9534f"});
					        return false; 
					    }else{
					    	swal({title:"提示!", text:"请勿操作过于频繁", type:"info",confirmButtonColor: "#d9534f"});
					    	scoreVue.unit = JSON.parse(unitStr);
							scoreVue.list = JSON.parse(listStr);
					    }
						
					},
					error:function(){
						swal({title:"保存失败!", text:"", type:"error",confirmButtonColor: "#d9534f"});
					}
				});
			},
			rank:function(){
				$.ajax({
					type:"post",
					url:WEBPATH+"/provinceScore/unitRankPerson.do",
					data:{},
					dataType:"json",
					success:function(data){
						if(data.code=='200'){
							swal({title:"汇总成功", text:"", type:"success",confirmButtonColor: "#d9534f"});
							business.addMainContentParserHtml(WEBPATH+'/provinceScore/goPersonScore.do');
						}else if(data.code=='300'){
							swal({title:"提示", text:"有未评分的数据，不能进行排名汇总!", type:"info",confirmButtonColor: "#d9534f"});
						}else{
							swal({title:"错误", text:"系统错误!", type:"error",confirmButtonColor: "#d9534f"});
						}
					},
					error:function(){
						swal({title:"保存失败!", text:"", type:"error",confirmButtonColor: "#d9534f"});
					}
				});
			}
		}
	});
	
	//分页
	$(document).ready(function(){
		var curentPage = eval('${PageBean.pageNum}');
		var totalPage = eval('${PageBean.pages}');
		if(totalPage>0){
			var options = {
				bootstrapMajorVersion: 3,
			    currentPage: curentPage,
			    totalPages: totalPage,
			    numberOfPages: 5,
			    itemTexts: function (type, page, current) {
			            switch (type) {
			            case "first":
			                return "首页";
			            case "prev":
			                return "&laquo;";
			            case "next":
			                return "&raquo;";
			            case "last":
			                return "尾页";
			            case "page":
			                return page;
			            }
			    },
			    onPageClicked: function (event, originalEvent, type, page) {
	            	business.addMainContentParserHtml(WEBPATH+'/provinceScore/goPersonScore.do?pageNum='+page);
	            }
	    	};
	    	$('#pageCon').bootstrapPaginator(options);
	   	}

	});
</script>
</html>
