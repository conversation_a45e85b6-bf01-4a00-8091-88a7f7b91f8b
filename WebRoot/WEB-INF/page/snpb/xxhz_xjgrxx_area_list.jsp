<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
</head>
<body>
<div class="center_weizhi">当前位置：信息汇总- 候选个人列表</div>
<div class="center">
<div class="center_list">
    <div class="panel-group" id="accordion">
    	<button type="button" class="btn btn-danger" id ="viewExcel" >导出EXCEL</button> 
    </div>
        <table class="table table-bordered table-hover table-condensed">
         <thead>
         <tr>
             <td width="50" height="30" bgcolor="#efefef">序号</td>
             <td bgcolor="#efefef">行政区名称</td>
             <td bgcolor="#efefef">省级个人参选数量</td>
             <td bgcolor="#efefef">市级个人参选数量</td>
             <td bgcolor="#efefef">县级个人参选数量</td>
             <td bgcolor="#efefef">个人参选总数</td>
            
             <td bgcolor="#efefef">查看</td>
           </tr>
         </thead>
         <tbody>
            <c:forEach items="${areaList.list}" var="item" varStatus="status">
	           <tr>
	             <td height="30" align="center">${status.index+1}<input id="id${status.index+1}" type="hidden"  value="${item.id}"></td>
	             <td>${item.name}</td>
	             <td>${item.provinceNum}</td>
	             <td>${item.cityNum}</td>
	             <td>${item.countryNum}</td>
	             <td>${item.sum}</td>
	             <td>
					<button class="btn btn-danger btn-xs" onclick="look(${item.code})">查看</button>
				 </td>
	           </tr>
           </c:forEach>
         </tbody>
       </table>
    </div>
     <!--列表翻页 开始-->
    <div class="page">
    	<span style="padding:12px; float:left; color:#0099cc;">共${areaList.total}条记录</span>
        <ul class="pagination" id="pageCon">
            
        </ul>
    </div>
    <!--列表翻页 结束-->
</div>
<script type="text/javascript">
$("#viewExcel").click(function(){
	window.location.href= WEBPATH+'/xxcj/personalNumExcle.do?';
});
//分页
$(document).ready(function(){
	var curentPage = eval('${areaList.pageNum}');
	var totalPage = eval('${areaList.pages}');
	if(totalPage>0){
		var options = {
			bootstrapMajorVersion: 3,
		    currentPage: curentPage,
		    totalPages: totalPage,
		    numberOfPages: 5,
		    itemTexts: function (type, page, current) {
		            switch (type) {
		            case "first":
		                return "首页";
		            case "prev":
		                return "&laquo;";
		            case "next":
		                return "&raquo;";
		            case "last":
		                return "尾页";
		            case "page":
		                return page;
		            }
		    },
		    onPageClicked: function (event, originalEvent, type, page) {
            	business.addMainContentParserHtml(WEBPATH+'/xxcj/goPersonalListSys.do?pageNum='+page);
            }
    	};
    	$('#pageCon').bootstrapPaginator(options);
   	}
});

function look(areacode){
	business.addMainContentParserHtml(WEBPATH+'/xxcj/personalTotalProList.do?areacodePro='+areacode);
}
</script>
</body>
</html>
