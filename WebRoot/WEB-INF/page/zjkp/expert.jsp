<%@ page language="java" import="java.util.*,java.lang.*" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>

<script type="text/javascript">
   $(document).ready(function(){
	 //监听enter
		business.listenEnter("searchButt");
	 //导出execl表单
		$("#viewExcel").click(function(){

			window.location.href= WEBPATH+'/sys/downExpertZjpsExecl.do';
		});
   });
   function resetChildrenPass(id){
       swal({
                       title: "您确定执行此操作吗？",
                       text: "您确定要重置当前登录密码吗？",
                       type: "warning",
                       showCancelButton: true,
                       closeOnConfirm: false,
                       confirmButtonText: "是的，我要重置",
                       confirmButtonColor: "#ec6c62"
                   }, function() {
                       $.ajax({
                           url: WEBPATH+"/sys/resetExpPass.do",
                           type: "POST",
                           data:{id:id}
                       }).done(function(data) {

                           if(data.type="success"){
                           	swal("操作成功!", "已成功重置密码！", "success");
                           	business.addMainContentParserHtml('sys/expertUser.do','pageNum=1');
                          // 	macroMgr.onLevelTwoMenuClick(null, 'sys/expertUser.do');
                           }else{
                               swal("OMG", "重置密码失败了!", "error");
                           }

                       }).error(function(data) {
                           swal("OMG", "删除操作失败了!", "error");
                       });
                   });
   }


</script>
<div class="center_weizhi">当前位置：系统管理 - 专家用户管理</div>
<!--框架内容 开始-->


<div class="center">
<div class="center_list">
    <div class="panel-group" id="accordion">
          <!---快速查询--->

              <select id="type" class="form-control" style="width:160px;margin-right:5px;">
                 <option value="">请选择专家类型</option>
                 <option value="0">行政处罚</option>
                 <option value="1">按日计罚</option>
                 <option  value="6">查封扣押</option>
                 <option value="7">停产限产</option>
                 <option value="2">移送行政拘留</option>
                 <option value="3">涉嫌犯罪移送</option>
                 <option value="9">不予处罚</option>
              </select>

            <!---搜索--->
            <div style="width:360px;" class="btn-group">

                  <div class="row">
                     <div class="col-lg-6">
                        <div class="input-group">
                           <input type="text" id="name" placeholder="真实姓名关键字"   class="form-control" style="width:200px;">
                           <span class="input-group-btn">
                              <button onclick="search()" id="searchButt" class="btn btn-danger" type="button">
                                 快速搜索
                              </button>
                           </span>
                        </div><!-- /input-group -->
                     </div><!-- /.col-lg-6 -->
                  </div><!-- /.row -->
            </div>

             <div style="width:360px;" class="btn-group">
                  &nbsp;&nbsp;&nbsp;&nbsp;  <a href ="#"><button type="button" id ="viewExcel" class="btn btn-danger">导出EXCEL</button></a>
            </div>


    </div>
        <table class="table table-bordered table-hover table-condensed">
         <thead>
           <tr>
             <td width="50" height="30" bgcolor="#efefef">序号</td>
<%--             <td bgcolor="#efefef">用户账号</td>--%>
             <td bgcolor="#efefef">用户类型</td>
             <td bgcolor="#efefef">真实姓名</td>
               <td bgcolor="#efefef">专家类型</td>
             <td bgcolor="#efefef">分配案卷数</td>
             <td bgcolor="#efefef">已评案件数</td>
               <td bgcolor="#efefef">未评案卷数</td>
               <td bgcolor="#efefef">争议案卷数</td>
             <td width="200" bgcolor="#efefef">查看</td>
           </tr>
         </thead>
         <tbody>
         <c:forEach items="${expertResult.list }" var="expert" varStatus="status">
	           <tr>
	             <td height="30" align="center">${status.index+1}</td>
<%--	             <td>${expert.loginname }</td>--%>
	             <td>
	             <c:choose>
	                 <c:when test="${expert.type=='0' }">行政处罚</c:when>
	                 <c:when test="${expert.type=='1' }">按日计罚</c:when>
	                 <c:when test="${expert.type=='6' }">查封扣押</c:when>
	                 <c:when test="${expert.type=='7' }">停产限产</c:when>
	                 <c:when test="${expert.type=='2' }">移送行政拘留</c:when>
	                 <c:when test="${expert.type=='3' }">涉嫌犯罪移送</c:when>
	                 <c:when test="${expert.type=='9' }">不予处罚</c:when>
	                 <c:otherwise></c:otherwise>
	             </c:choose>
	             </td>
	             <td>${expert.name }</td>
                   <td>${expert.remark }</td>
	             <td>${expert.quanbu }</td>
                   <td>${expert.yiping }</td>
                   <td>${expert.weiping }</td>
                   <td>${expert.yichang }</td>
	             <td>
<%--             		     <c:when test="${expert.loginpass=='9216d0928653889efc936b40e4449029' }">--%>
<%--             		     		  <button type="button" class="btn btn-primary btn-xs" disabled="disabled"  style="width:100px; margin:1px;background-color: gray;">查看</button>--%>
<%--             		     </c:when>--%>

<%--             		        	 <a href="#" onclick="business.addMainContentParserHtml(null, 'zjpf/zjpfLists?UserId=4')"><button type="button" class="btn btn-primary btn-xs" style="width:100px; margin:1px;">查看</button></a>--%>
<%--    <button type="button" onclick="startScore('${expert.id }'}" class="btn btn-primary btn-xs"   style="width:100px; margin:1px;background-color: gray;">查看</button>--%>

    <button class="btn btn-danger btn-xs" onclick="look(${expert.id})">查看</button>
<%--    <button type="button" onclick="onclick="startScore('${experList.id}'business.addMainContentParserHtml(null, 'zjpf/zjpfLists?UserId=4')" class="btn btn-primary btn-xs"   style="width:100px; margin:1px;background-color: gray;">查看</button>--%>

<%--    zjpf/entityAScore.do?id="+id+"&pageNum=${pageNum}"--%>
			     </td>
	           </tr>
           </c:forEach>
         </tbody>
       </table>
    </div>
     <!--列表翻页 开始-->
    <div class="page">
    	<span style="padding:12px; float:left; color:#0099cc;">共${expertResult.total}条记录</span>
        <ul class="pagination" id="pageCon">

        </ul>
    </div>
    <!--列表翻页 结束-->

</div>
<script type="text/javascript">
//分页
$(document).ready(function(){


	var curentPage = eval('${expertResult.pageNum}');
	var totalPage = eval('${expertResult.pages}');
	//var type = eval('${areaType}');
	if(totalPage>0){
		var options = {
			bootstrapMajorVersion: 3,
		    currentPage: curentPage,
		    totalPages: totalPage,
		    numberOfPages: 5,
		    itemTexts: function (type, page, current) {
		            switch (type) {
		            case "first":
		                return "首页";
		            case "prev":
		                return "&laquo;";
		            case "next":
		                return "&raquo;";
		            case "last":
		                return "尾页";
		            case "page":
		                return page;
		            }
		    },
		    onPageClicked: function (event, originalEvent, type, page) {
            	business.addMainContentParserHtml(WEBPATH+'/zjkpGerenList.do?type='+$('#type').val()+'&name='+$('#name').val()+'&pageNum='+page,null);
            }
    	};
    	$('#pageCon').bootstrapPaginator(options);

   	}
	var type='${type}';
	var name='${name}';
	if(!business.isNull(type)){
		$("#type").val(type);
	}
	if(!business.isNull(name)){
		$("#name").val(name);
	}
});

function search(){
	business.addMainContentParserHtml(WEBPATH+'/zjkpGerenList.do?type='+$('#type').val()+'&name='+$('#name').val()+'&pageNum=1',null);
}

function look(id){
    // alert(id)
    business.addMainContentParserHtml(WEBPATH+'/zjpf/zjpfLists.do?UserId='+id);
}

</script>




