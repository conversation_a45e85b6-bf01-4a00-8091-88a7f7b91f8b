<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
 
<html>
<head>
</head>

<body>
<div class="center_weizhi">当前位置：案卷总分 - 案卷评分结果 - 交叉案卷合议列表  </div>
<div class="center">
<div class="center_list">
    <div class="panel-group" id="accordion">
          <h4 class="panel-title">
          <!---快速查询--->
          <form  id= "searchForm" role="form"  >
          	  <input id ="fileCodeCurrent" name="fileCodeCurrent" type="hidden">
          	  <select class="form-control" style="width:150px;margin-right:5px;" name="areaCodeLeave" id="areaCodeLeave">
                 <option value="">请选择行政区</option>
                 <option value="1"  <c:if test="${areaCodeLeave=='1' }">selected</c:if> >省级</option>
                 <option value="2" <c:if test="${areaCodeLeave=='2' }">selected</c:if>  >地市级</option>
                 <option value="3" <c:if test="${areaCodeLeave=='3' }">selected</c:if>  >区县级</option>
              </select> 
              <select class="form-control" style="width:150px;margin-right:5px;" name="isConsiderCross" id="isConsiderCrossSelect">
                  <!-- 本地放开请选择和无需合议   不需要升级线上 -->
                  <option value="">请选择评审状态</option> 
                 <option value="1" <c:if test="${isConsiderCross=='1' }">selected</c:if>  >需要合议</option>
                 <option value="0"  <c:if test="${isConsiderCross=='0' }">selected</c:if> >无需合议</option> 
              </select>           
            <!---搜索--->
            <div style="width:260px;" class="btn-group">
                  <div class="row">                     
                     <div class="col-lg-6">
                        <div class="input-group">
                           <input id="fileCode" name="fileCode" type="text" class="form-control" style="width:200px;" value="${fileCode}" placeholder="决定书文号关键字">
                           <span class="input-group-btn">
                              <button id="searchButt" class="btn btn-success" type="button">
                           		      快速搜索
                              </button>
                             <button type="button" class="btn btn-primary" id ="viewExcel" >导出EXCEL</button> 
                           </span>
                       
                        </div><!-- /input-group -->
                     </div><!-- /.col-lg-6 -->
                  </div><!-- /.row -->
              </div> 
             
            </form>
                      
          </h4>
    </div>
        <table class="table table-bordered table-hover table-condensed">
         <thead>
           <tr>
             <td width="50" height="30" bgcolor="#efefef">序号</td>
             <td bgcolor="#efefef">决定书文号</td>
             <td bgcolor="#efefef">来源</td>
             <td width="110" bgcolor="#efefef">交叉骨干A姓名</td>
             <td width="70" bgcolor="#efefef">初评得分</td>
             <!-- <td width="100" bgcolor="#efefef">初评得分（含无需评查）</td> -->
             <td width="110" bgcolor="#efefef">交叉骨干B姓名</td>
             <td width="70" bgcolor="#efefef">初评得分</td>
             <!-- <td width="100" bgcolor="#efefef">初评得分（含无需评查）</td> -->
             <!-- <td width="120" bgcolor="#efefef">合议评审得分（原始）</td> -->
             <td width="120" bgcolor="#efefef">最终合议评审得分</td>
             <td width="90" bgcolor="#efefef">操作</td>
           </tr>
         </thead>
         <tbody>
          <c:forEach items="${ajpfBeanList.list}" var="item" varStatus="status">
		      <tr>
			     <td <c:if test="${item.isConsiderCross=='1'}"> bgcolor='#D6F1F1' </c:if> height="30" align="center">${status.index+1 }
				     <input id="id${status.index+1}" type="hidden"  value="${item.id}">
				     
				     <input id="filetype${status.index+1}" type="hidden"  value="${item.filetype}">
				     <input id="fileCode${status.index+1}" type="hidden"  value="${item.fileCode}">
				     <input id="crossReviewAId${status.index+1}" type="hidden"  value="${item.crossreviewaid}">
				     <input id="crossReviewBId${status.index+1}" type="hidden"  value="${item.crossreviewbid}">
			     </td>
			     <td <c:if test="${item.isConsiderCross=='1'}"> bgcolor='#D6F1F1' </c:if>>${item.fileCode}</td>
			     <!-- 来源 -->
	             <td <c:if test="${item.isConsiderCross=='1'}"> bgcolor='#D6F1F1' </c:if>>
	             <c:choose>
	             	<c:when test="${item.reportType==0 }">
	             		推选
	             	</c:when>
	             	<c:otherwise>
	             		随机
	             	</c:otherwise>
	             </c:choose>
	             </td>
	             <!-- 交叉人员计算 -->
                <td <c:if test="${item.isConsiderCross=='1'}"> bgcolor='#D6F1F1' </c:if>>${item.crossReviewNameA}</td>
	             <td <c:if test="${item.isConsiderCross=='1'}"> bgcolor='#D6F1F1' </c:if>>
	             	<a href="javascript:void(0);" onclick="selectExpertScore('${status.index+1 }',1,2)">${item.crossReviewAScore}</a>
	             </td>
	             
	             <%-- <td  bgcolor='#D6F1F1'>
	             	<a href="javascript:void(0);" onclick="selectExpertScore('${status.index+1 }',1,2)">${item.inCheckCrossAScore}</a>
	             </td> --%>
	             
	             <td <c:if test="${item.isConsiderCross=='1'}"> bgcolor='#D6F1F1' </c:if>>${item.crossReviewNameB}</td>
	             <td <c:if test="${item.isConsiderCross=='1'}"> bgcolor='#D6F1F1' </c:if>>
	             	<a href="javascript:void(0);" onclick="selectExpertScore('${status.index+1 }',2,2)">${item.crossReviewBScore}</a>
	             </td>
	             <%-- <td  bgcolor='#D6F1F1'>
	             	<a href="javascript:void(0);" onclick="selectExpertScore('${status.index+1 }',2,2)">${item.inCheckCrossBScore}</a>
	             </td>
	             <td width="92" <c:if test="${item.isConsiderCross=='1'}"> bgcolor='#D6F1F1' </c:if>>
	             	${item.originalCrossScore}
	             </td> --%>
	             <td width="92" <c:if test="${item.isConsiderCross=='1'}"> bgcolor='#D6F1F1' </c:if>>
	             	<!-- 1:标识需要合议  ， 0：无需合议 -->
	             	<c:choose>
					       <c:when test="${(item.isConsiderCross=='1' and item.crossConsiderScore=='') or (item.isConsiderCross=='1' and item.crossConsiderScore== null) }">
					       		<input id="crossConsiderScore${status.index+1}" readonly type="text" class="form-control input-sm"  placeholder="请输入得分" value="${item.crossConsiderScore}">
					       </c:when>
					       <c:otherwise>
					      	${item.crossConsiderScore}
					      	<input id="crossHiddenScore${status.index+1}" type="hidden" value="${item.crossConsiderScore}">
					       </c:otherwise>
					</c:choose>
	             </td>
	             <!-- 交叉人员计算  结束-->
	             <td <c:if test="${item.isConsiderCross=='1'}"> bgcolor='#D6F1F1' </c:if> >
	             	<c:choose> 
				       <c:when test="${item.isConsiderCross=='1' && sessionScope.sa_session.sysStatus =='2' }">
				        <button id="heyi${status.index+1}" class="btn btn-danger btn-xs" onclick="heYi('${status.index+1 }')"  data-toggle="modal" data-target="#myModal">合议</button> 
             		 	<button style="display:none"   onclick="submitHeYi('${status.index+1 }')"   id="baocun${status.index+1}" class="btn btn-info btn-xs" data-toggle="modal" data-target="#myModal">保存</button> 
             		 	<button style="display:none"   onclick="cancelHeYi('${status.index+1 }')" id="quxiao${status.index+1}" class="btn btn-primary btn-xs" data-toggle="modal" data-target="#myModal">返回</button>
				       </c:when>
				       <c:otherwise>
				     	 &nbsp;
				       </c:otherwise>
					</c:choose>
	             </td>
		      </tr>
	      </c:forEach>
         </tbody>
       </table>
    </div>
    <!--列表翻页 开始-->
    <div class="page">
    	<span style="padding:12px;  float:left; color:#0099cc;">共${ajpfBeanList.total}条记录</span>
        <ul class="pagination" id="pageCon">
            
        </ul>
    </div>
    <!--列表翻页 结束-->
</div>
 
<script language="JavaScript">
// 搜索条件
$(document).ready(function(){
	//监听enter
	business.listenEnter("searchButt");
	$("#searchButt").click(function(){
		business.addMainContentParserHtml("ajpfCross.do",$("#searchForm").serialize());
	});
	$("#areaTypeSelect").change(function(){
		business.addMainContentParserHtml("ajpfCross.do",$("#searchForm").serialize());
	});
	$("#isConsiderCrossSelect").change(function(){
		business.addMainContentParserHtml("ajpfCross.do",$("#searchForm").serialize());
	});
	$("#areaCodeLeave").change(function(){
		business.addMainContentParserHtml("ajpfCross.do",$("#searchForm").serialize());
	})
	$("#viewExcel").click(function(){
		var areaCodeLeave = $("#areaCodeLeave").val();
		var isConsiderCross = $("#isConsiderCrossSelect").val();
		var fileCode = $("#fileCode").val();
	  var path = WEBPATH+"/crossListExportExcel.do?target=1&viewType=2&fileCode="+fileCode+"&isConsiderCross="+isConsiderCross+"&areaCodeLeave="+areaCodeLeave; 
      window.location.href=path;
	});
});
//分页
$(document).ready(function(){
	var curentPage = eval('${ajpfBeanList.pageNum}');
	var totalPage = eval('${ajpfBeanList.pages}');
	if(totalPage>0){
		var options = {
			bootstrapMajorVersion: 3,
		    currentPage: curentPage,
		    totalPages: totalPage,
		    numberOfPages: 5,
		    itemTexts: function (type, page, current) {
		            switch (type) {
		            case "first":
		                return "首页";
		            case "prev":
		                return "&laquo;";
		            case "next":
		                return "&raquo;";
		            case "last":
		                return "尾页";
		            case "page":
		                return page;
		            }
		    },
		    onPageClicked: function (event, originalEvent, type, page) {
            	business.addMainContentParserHtml(WEBPATH+'/ajpfCross.do?pageNum='+page,$("#searchForm").serialize());
            }
    	};
    	$('#pageCon').bootstrapPaginator(options);
   	}
});

// 点击合议按钮  coordinate：坐标
function heYi(coordinate){
	$("#heyi"+coordinate).hide();
	$("#baocun"+coordinate).show();
	$("#quxiao"+coordinate).show();
 
	var crossScore = $("#crossConsiderScore"+coordinate);
 
	if(crossScore.length >0)
	{
		crossScore.removeAttr("readonly");
	} 
};
// 合议后保存
function submitHeYi (coordinate){
	// 回显页面条件信息
	var pageNum = getPageNum();
	var areaTypeVal = getAreaTypeVal();  
	var isConsiderCrossVal= getisConsiderCrossVal();  
	var fileCodeVal =getFileCodeVal();  
	// 校验  和 数值区间				
	var regexpCh = /(?!^0\.0?0$)^[0-9][0-9]?(\.[0-9]{1,2})?$|^(100|0\.0|0\.00|100\.0|100\.00)$/;
	// 
	var fileIdVal="", crossScoreVal="";
	var fileId = $("#id"+coordinate).val();
 
	var crossScore = $("#crossConsiderScore"+coordinate);
	var fileType = $("#filetype"+coordinate).val();
	if(crossScore.length>0)
	{
		crossScoreVal = crossScore.val();
		// 这里需要校验
		if($.trim(crossScoreVal)==""){
			swal("合议分不能为空", "信息保存操作失败了!", "error");
			return false;
		}
		if(!regexpCh.test(crossScoreVal))
		{
			swal("分值范围在0-100，可输入整数和小数，小数部分最多保留2位", "信息保存操作失败了!", "error");
			return false;
		}
	}else
	{
		crossScoreVal = $("#crossHiddenScore"+coordinate).val(); 
	}
    $.ajax({
             type: "POST",
             url: WEBPATH+"/saveFilesBeanCross.do",
             data:{fileId:fileId,crossScoreVal:crossScoreVal,fileType:fileType},
             async:false,
             success: function(data){
	             if(data.result=="error"){
	               	swal({title: data.message ,text: "",type:"error"});
	                return false;
	             }else if(data.result=="success"){
	              	swal({title: data.message ,text: "",type:"success"});
	              	business.addMainContentParserHtml(WEBPATH+'/ajpfCross.do?pageNum='+pageNum+'&areaType='+areaTypeVal+'&isConsiderCross='+isConsiderCrossVal+'&fileCode='+fileCodeVal,null);
	             }
           }
     });
	
	 
};
// 取消合议
function cancelHeYi(coordinate){
	$("#heyi"+coordinate).show();
	$("#baocun"+coordinate).hide();
	$("#quxiao"+coordinate).hide();
 
	var crossScore = $("#crossConsiderScore"+coordinate);
 
	if(crossScore.length >0)
	{
		crossScore.val("");
		crossScore.attr("readonly","readonly");
	} 
};
 
// 查询专家和交叉打分情况  coordinate 下标   type 第几位专家 expertOrCross 专家还是交叉
function selectExpertScore(coordinate,type,expertOrCross){
 	var pageNum = getPageNum();
 	
	var fileId = $("#id"+coordinate).val();				      
	var fileCode  = $("#fileCode"+coordinate).val();
	var fileType = $("#filetype"+coordinate).val();
	var expertId;
	// 
	if(expertOrCross=='1'){
		if(type=='1'){
			expertId =  $("#expertAId"+coordinate).val();
		}
		if(type =='2'){
			expertId =  $("#expertBId"+coordinate).val();
		}
	}
	if(expertOrCross=='2'){
		if(type=='1'){
			expertId =  $("#crossReviewAId"+coordinate).val();
		}
		if(type =='2'){
			expertId =  $("#crossReviewBId"+coordinate).val();
		}
	}
	// 非空判断专家或者交叉的id为空
	if(expertId === undefined || $.trim(expertId)=="")
	{
		swal("信息操作错误！", "信息保存操作失败了!", "error");
		return false;
	}		 
	if(fileCode === undefined || $.trim(fileCode)=="")
	{
		swal("案卷Code为空", "信息保存操作失败了!", "error");
		return false;
	}	 
	if(fileType === undefined || $.trim(fileType)=="")
	{
		swal("案卷类型为空", "信息保存操作失败了!", "error");
		return false;
	}	 
	$("#fileCodeCurrent").val(fileCode);
	var expertOrCrossType = "2";
	//fileCodeCurrent='+fileCode+'
    business.addMainContentParserHtml(WEBPATH+'/showSorePageByType.do?pageIndex=2&fileType='+fileType+'&fileId='+fileId+'&expertId='+expertId+'&pageNum='+pageNum+'&expertOrCross='+expertOrCross+'&expertOrCrossType='+expertOrCrossType,$("#searchForm").serialize());
}

// 获取回显当前页
function getPageNum(){
	var pageNum = eval('${ajpfBeanList.pageNum}');
	if(pageNum === undefined ){
			pageNum = '';
	}
	return pageNum;
}
// 获得回显案卷类型
function getAreaTypeVal(){
	var areaTypeVal = eval('${areaType}');  
	if(areaTypeVal === undefined ){
		areaTypeVal = '';
	}
	return areaTypeVal;
}
// 获得回显是否合议状态
function getisConsiderCrossVal(){
	var isConsiderCrossVal= eval('${isConsiderCross}');  
		if(isConsiderCrossVal === undefined ){
		isConsiderCrossVal = '';
	}
	return isConsiderCrossVal;
}
// 获得回显决定文书号
function getFileCodeVal(){
	var fileCodeVal = '${fileCode}';
		if(fileCodeVal === undefined ){
		fileCodeVal = '';
	}
	return  fileCodeVal;
}

$(document).ready(function() {
	$(".topnav").accordion({
		accordion:false,
		speed: 500,
		closedSign: '[+]',
		openedSign: '[-]'
	});
});

</script>
</body>
</html>
