<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>

<html>
<head>
</head>

<body>
<div class="center_weizhi">当前位置：案卷总分 - 实体否决结果 - 实体否决结果</div>
<div class="center">
<div class="center_list">
    <div class="panel-group" id="accordion">
          <h4 class="panel-title">
          <!---快速查询--->
	          <form  id= "searchForm" role="form"  >
          		  <select class="form-control" style="width:150px;margin-right:5px;" id="areaType" name="areaType">
	                 <option value="" >请选择行政区</option>
	                 <option value="1" <c:if test="${areaType=='1' }">selected </c:if>>省级</option>
	                 <option value="2" <c:if test="${areaType=='2' }">selected </c:if>>地市级</option>
	                 <option value="3" <c:if test="${areaType=='3' }">selected </c:if>>区县级</option>
              	  </select>

	              <select class="form-control" style="width:150px;margin-right:5px;" name="indexId" id="indexId">
	                 <option value="">请选择实体否决内容</option>
	                 <option value="104" <c:if test="${indexId=='104' }">selected</c:if>  >执法主体</option>
	                 <option value="105" <c:if test="${indexId=='105' }">selected</c:if>  >违法主体</option>
	                 <option value="106" <c:if test="${indexId=='106' }">selected</c:if>  >违法事实证据</option>
	                 <option value="107" <c:if test="${indexId=='107' }">selected</c:if>  >法律适用</option>
	                 <option value="108" <c:if test="${indexId=='108' }">selected</c:if>  >执法程序</option>
	                 <option value="109" <c:if test="${indexId=='109' }">selected</c:if>  >案卷完整性</option>
	              </select>
		          <!---搜索--->
		          <div style="width:260px;" class="btn-group">
	                  <div class="row">
	                     <div class="col-lg-6">
	                        <div class="input-group">
	                           <input id="fileCode" name="fileCode" type="text" class="form-control" style="width:200px;" value="${fileCode}" placeholder="案卷文号关键字">
	                           <span class="input-group-btn">
	                              <button id="searchButt" class="btn btn-success" type="button">
	                           		      快速搜索
	                              </button>
	                             <button type="button" class="btn btn-primary" id ="viewExcel" >导出EXCEL</button>
	                           </span>

	                        </div><!-- /input-group -->
	                     </div><!-- /.col-lg-6 -->
	                  </div><!-- /.row -->
	              </div>
            </form>
          </h4>
    </div>
        <table class="table table-bordered table-hover table-condensed">
         <thead>
           <tr>
             <td width="50" height="30" bgcolor="#efefef">序号</td>
             <td width="100" bgcolor="#efefef">案卷文书号</td>
             <td width="100" bgcolor="#efefef">案卷类型</td>
             <td width="100" bgcolor="#efefef">省</td>
             <td width="100" bgcolor="#efefef">市</td>
             <td width="100" bgcolor="#efefef">县</td>
             <td width="100" bgcolor="#efefef">实体否决项</td>
			 <td width="100" bgcolor="#efefef">专家否决次数</td>
             <td width="100" bgcolor="#efefef">操作</td>

           </tr>
         </thead>
         <tbody>
          <c:forEach items="${ajpfBeanList.list}" var="item" varStatus="status">
		      <tr>
				  <%--序号--%>
			     <td <c:if test="${item.isConsider=='1'}"> bgcolor='#D6F1F1' </c:if> height="30" align="center">${status.index+1 }
				     <input id="id${status.index+1}" type="hidden"  value="${item.id}">

				     <input id="filetype${status.index+1}" type="hidden"  value="${item.filetype}">
				     <input id="fileCode${status.index+1}" type="hidden"  value="${item.fileCode}">
				     <input id="expertAId${status.index+1}" type="hidden"  value="${item.expertaid}">
				     <input id="expertBId${status.index+1}" type="hidden"  value="${item.expertbid}">
			     </td>
				  <%--案卷文书号--%>
			     <td <c:if test="${item.isConsider=='1'}"> bgcolor='#D6F1F1' </c:if>>${item.fileCode}</td>
			      <%--案卷类型--%>
			     <td <c:if test="${item.isConsider=='1'}"> bgcolor='#D6F1F1' </c:if>>
			         <c:choose>
	             	 	 <c:when test="${item.filetype =='0' }">
				       		行政处罚案卷
					     </c:when>
					  	 <c:when test="${item.filetype =='1' }">
				       		按日计罚案卷
					     </c:when>
					     <c:when test="${item.filetype =='2' }">
				       		移送行政拘留案卷
					     </c:when>
					     <c:when test="${item.filetype =='3' }">
				       		环境污染犯罪移送公安机关案卷
					     </c:when>
					     <c:when test="${item.filetype =='4' }">
				       		申请法院强制执行案卷
					     </c:when>
					     <c:when test="${item.filetype =='5' }">
				       		发现问题的污染源现场监督检查稽查案卷
					     </c:when>
					     <c:when test="${item.filetype == '6' }">
					     	查封扣押案卷
					     </c:when>
					     <c:when test="${item.filetype == '7' }">
					     	限产停产案卷
					     </c:when>
						 <c:when test="${item.filetype == '9' }">
							 不予处罚案卷
						 </c:when>
				       <c:otherwise>

				       </c:otherwise>
					</c:choose>
			      </td>
			     <%--省--%>
				<td <c:if test="${item.isConsider=='1'}"> bgcolor='#D6F1F1' </c:if>>${item.province}</td>
			     <%--市--%>
				<td <c:if test="${item.isConsider=='1'}"> bgcolor='#D6F1F1' </c:if>>${item.city}</td>
			     <%--县--%>
				<td <c:if test="${item.isConsider=='1'}"> bgcolor='#D6F1F1' </c:if>>${item.country}</td>
			     <%--实体否决项--%>
				<td <c:if test="${item.isConsider=='1'}"> bgcolor='#D6F1F1' </c:if>>${item.scoringIndexNames}</td>
			     <%--专家否决次数--%>
				<td <c:if test="${item.isConsider=='1'}"> bgcolor='#D6F1F1' </c:if>>${item.expertVetoNumber}</td>
			     <%--操作--%>
			  <td <c:if test="${item.isConsider=='1'}"> bgcolor='#D6F1F1' </c:if> >
				  <button style="margin: 0 10px" class="btn btn-success btn-xs"
						  id="viewInfo"  data-toggle="modal"
						  data-target="#myModal"
						  OnClick="viewInfo(${item.id},'${item.scoringIndexNames}','${areaType}','${indexId}','${fileCode}')">
					  查看
				  </button>
			  </td>
		      </tr>
	      </c:forEach>
         </tbody>
       </table>
    </div>
    <!--列表翻页 开始-->
    <div class="page">
    	<span style="padding:12px;  float:left; color:#0099cc;">共${ajpfBeanList.total}条记录</span>
        <ul class="pagination" id="pageCon">

        </ul>
    </div>
    <!--列表翻页 结束-->
</div>

<script language="JavaScript">
//跳转页面
function goCommitScore(){

}


// 搜索条件
$(document).ready(function(){
	//监听enter
	business.listenEnter("searchButt");
	$("#searchButt").click(function(){
		business.addMainContentParserHtml("anjuanList.do?pageIndex=3",$("#searchForm").serialize());
	});
	$("#fileTypeSelect").change(function(){
		business.addMainContentParserHtml("anjuanList.do?pageIndex=3",$("#searchForm").serialize());
	});
	$("#isConsiderSelect").change(function(){
		business.addMainContentParserHtml("anjuanList.do?pageIndex=3",$("#searchForm").serialize());
	});
	//导出Excel
	$("#viewExcel").click(function(){
	  var areaType = $("#areaType").val();
	  var indexId = $("#indexId").val();
	  var fileCode = $("#fileCode").val();
	  var path = WEBPATH+"/entityVetoExportExcel.do?target=1&viewType=3&areaType="+areaType+"&indexId="+indexId+"&fileCode="+fileCode;
      window.location.href=path;

	});
});
//分页
$(document).ready(function(){
	var curentPage = eval('${ajpfBeanList.pageNum}');
	var totalPage = eval('${ajpfBeanList.pages}');
	if(totalPage>0){
		var options = {
			bootstrapMajorVersion: 3,
		    currentPage: curentPage,
		    totalPages: totalPage,
		    numberOfPages: 5,
		    itemTexts: function (type, page, current) {
		            switch (type) {
		            case "first":
		                return "首页";
		            case "prev":
		                return "&laquo;";
		            case "next":
		                return "&raquo;";
		            case "last":
		                return "尾页";
		            case "page":
		                return page;
		            }
		    },
		    onPageClicked: function (event, originalEvent, type, page) {
            	business.addMainContentParserHtml(WEBPATH+'/anjuanList.do?pageIndex=3&pageNum='+page,$("#searchForm").serialize());
            }
    	};
    	$('#pageCon').bootstrapPaginator(options);
   	}
});

// 点击合议按钮  coordinate：坐标
function heYi(coordinate){
	$("#heyi"+coordinate).hide();
	$("#baocun"+coordinate).show();
	$("#quxiao"+coordinate).show();
	var expertScore = $("#expertConsiderScore"+coordinate);

	if(expertScore.length >0)
	{
		expertScore.removeAttr("readonly");
	}

};

$(document).ready(function() {
	$(".topnav").accordion({
		accordion:false,
		speed: 500,
		closedSign: '[+]',
		openedSign: '[-]'
	});
});


//查看详情
function viewInfo(filesId,scoringIndexNames,areaType,indexId,fileCode){
	console.log("查看详情:",filesId,scoringIndexNames)
	//跳转页面
	business.addMainContentParserHtml(WEBPATH+'/goEntityVetoInfo.do?filesId='+filesId+'&scoringIndexNames='+scoringIndexNames+'&areaType='+areaType+'&indexId='+indexId+'&fileCode='+fileCode);


}
</script>
</body>
</html>
