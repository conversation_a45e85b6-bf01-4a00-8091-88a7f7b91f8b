<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>

<html>
<head>
</head>

<body>
<div class="center_weizhi">当前位置：案卷总分 - 案卷评分结果 - 专家组长案卷合议列表</div>
<div class="center">
<div class="center_list">
    <div class="panel-group" id="accordion">
          <h4 class="panel-title">
          <!---快速查询--->
	          <form  id= "searchForm" role="form"  >
          		  <select class="form-control" style="width:150px;margin-right:5px;" id="areaType" name="areaType">
	                 <option value="" >请选择行政区</option>
	                 <option value="1" <c:if test="${areaType=='1' }">selected </c:if>>省级</option>
	                 <option value="2" <c:if test="${areaType=='2' }">selected </c:if>>地市级</option>
	                 <option value="3" <c:if test="${areaType=='3' }">selected </c:if>>区县级</option>
              	  </select>

		           <%-- <select id ="fileTypeSelect" class="form-control" style="width:300px;margin-right:5px;" name ="fileType">
		              <!-- 0代表 行政处罚案卷 ；1代表按日计罚案卷； 2代表移送行政拘留案卷；3代表涉嫌犯罪移送案卷；4代表申请法院强制执行案卷；5代表发现问题的污染源现场监督检查稽查案卷   -->
		                 <option value = ""  >请选择案卷类型</option>
		                 <option value="0" <c:if test="${fileType=='0' }">selected</c:if>>行政处罚案卷</option>
		                 <option value="1" <c:if test="${fileType=='1' }">selected</c:if>>按日计罚案卷</option>
		                 <option value="2" <c:if test="${fileType=='2' }">selected</c:if>>移送行政拘留案卷</option>
		                 <option value="3" <c:if test="${fileType=='3' }">selected</c:if>>涉嫌环境污染犯罪</option>
		                 <option value="4" <c:if test="${fileType=='4' }">selected</c:if>>申请法院强制执行案卷</option>
		           </select> --%>

	              <select class="form-control" style="width:150px;margin-right:5px;" name="isConsider" id="isConsiderSelect">
	                 <option value="">请选择评审状态</option>
	                 <option value="1" <c:if test="${isConsider=='1' }">selected</c:if>  >需要合议</option>
	                 <option value="0"  <c:if test="${isConsider=='0' }">selected</c:if> >无需合议</option>
	              </select>
		          <!---搜索--->
		          <div style="width:260px;" class="btn-group">
	                  <div class="row">
	                     <div class="col-lg-6">
	                        <div class="input-group">
	                           <input id="fileCode" name="fileCode" type="text" class="form-control" style="width:200px;" value="${fileCode}" placeholder="决定书文号关键字">
	                           <span class="input-group-btn">
	                              <button id="searchButt" class="btn btn-success" type="button">
	                           		      快速搜索
	                              </button>
	                             <button type="button" class="btn btn-primary" id ="viewExcel" >导出EXCEL</button>
	                           </span>

	                        </div><!-- /input-group -->
	                     </div><!-- /.col-lg-6 -->
	                  </div><!-- /.row -->
	              </div>
            </form>
          </h4>
    </div>
        <table class="table table-bordered table-hover table-condensed">
         <thead>
           <tr>
             <td width="50" height="30" bgcolor="#efefef">序号</td>
             <td width="80" bgcolor="#efefef">省</td>
             <td width="80" bgcolor="#efefef">市</td>
             <td width="50" bgcolor="#efefef">县</td>
             <td width="100" bgcolor="#efefef">决定书文号</td>
             <td width="100" bgcolor="#efefef">案卷类型</td>
             <td width="80" bgcolor="#efefef">专家A姓名</td>
             <td width="80" bgcolor="#efefef">初评得分</td>
             <td width="80" bgcolor="#efefef">专家B姓名</td>
             <td width="80" bgcolor="#efefef">初评得分</td>

              <td width="80" bgcolor="#efefef">委员A姓名</td>
             <td width="80" bgcolor="#efefef">复评得分</td>
             <td width="80" bgcolor="#efefef">委员B姓名</td>
             <td width="80" bgcolor="#efefef">复评得分</td>
             <td width="80" bgcolor="#efefef">最终得分</td>
             <td width="100" bgcolor="#efefef">操作</td>

           </tr>
         </thead>
         <tbody>
          <c:forEach items="${ajpfBeanList.list}" var="item" varStatus="status">
		      <tr>
			     <td <c:if test="${item.isConsider=='1'}"> bgcolor='#D6F1F1' </c:if> height="30" align="center">${status.index+1 }
				     <input id="id${status.index+1}" type="hidden"  value="${item.id}">

				     <input id="filetype${status.index+1}" type="hidden"  value="${item.filetype}">
				     <input id="fileCode${status.index+1}" type="hidden"  value="${item.fileCode}">
				     <input id="expertAId${status.index+1}" type="hidden"  value="${item.expertaid}">
				     <input id="expertBId${status.index+1}" type="hidden"  value="${item.expertbid}">
			     </td>
				  <td <c:if test="${item.isConsider=='1'}"> bgcolor='#D6F1F1' </c:if>>${item.province}</td>
				  <td <c:if test="${item.isConsider=='1'}"> bgcolor='#D6F1F1' </c:if>>${item.city}</td>
				  <td <c:if test="${item.isConsider=='1'}"> bgcolor='#D6F1F1' </c:if>>${item.country}</td>
			     <td <c:if test="${item.isConsider=='1'}"> bgcolor='#D6F1F1' </c:if>>${item.fileCode}</td>

			     <td <c:if test="${item.isConsider=='1'}"> bgcolor='#D6F1F1' </c:if>>
			         <c:choose>
	             	 	 <c:when test="${item.filetype =='0' }">
				       		行政处罚案卷
					     </c:when>
					  	 <c:when test="${item.filetype =='1' }">
				       		按日计罚案卷
					     </c:when>
					     <c:when test="${item.filetype =='2' }">
				       		移送行政拘留案卷
					     </c:when>
					     <c:when test="${item.filetype =='3' }">
				       		涉嫌环境污染犯罪
					     </c:when>
					     <c:when test="${item.filetype =='4' }">
				       		申请法院强制执行案卷
					     </c:when>
					     <c:when test="${item.filetype =='5' }">
				       		发现问题的污染源现场监督检查稽查案卷
					     </c:when>
					     <c:when test="${item.filetype == '6' }">
					     	查封扣押案卷
					     </c:when>
					     <c:when test="${item.filetype == '7' }">
					     	限产停产案卷
					     </c:when>
						 <c:when test="${item.filetype == '9' }">
					     	不予处罚案卷
					     </c:when>
				       <c:otherwise>

				       </c:otherwise>
					</c:choose>
			      </td>

	             <!-- 专家人员计算 expertAId, expertBId, crossReviewAId, crossReviewBId,fileType  -->
	             <td <c:if test="${item.isConsider=='1'}"> bgcolor='#D6F1F1' </c:if>>${item.expertAName}</td>
	             <td <c:if test="${item.isConsider=='1'}"> bgcolor='#D6F1F1' </c:if>>
	             	<a href="javascript:void(0);" onclick="checkExpertFile('${item.expertaid}','${item.id}','')">${item.expertAScore}</a>
	             </td>
	             <td <c:if test="${item.isConsider=='1'}"> bgcolor='#D6F1F1' </c:if>>${item.expertBName}</td>
	             <!-- 专家B 得分 -->
	             <td <c:if test="${item.isConsider=='1'}"> bgcolor='#D6F1F1' </c:if>>
	             	<a href="javascript:void(0);" onclick="checkExpertFile('${item.expertbid}','${item.id}','')">${item.expertBScore}</a>
	             </td>
	             <!-- 委员评分 -->
	             <td <c:if test="${item.isConsider=='1'}"> bgcolor='#D6F1F1' </c:if>>${item.expertCommitAName}</td>
	             <td <c:if test="${item.isConsider=='1'}"> bgcolor='#D6F1F1' </c:if>>
	             <a href="javascript:void(0);" onclick="checkExpertFile('${item.expertCommitAId}','${item.id}',1)">${item.expertCommitAScore}</a>
	             </td>
	             <td <c:if test="${item.isConsider=='1'}"> bgcolor='#D6F1F1' </c:if>>${item.expertCommitBName}</td>
	             <td <c:if test="${item.isConsider=='1'}"> bgcolor='#D6F1F1' </c:if>>
	             <a href="javascript:void(0);" onclick="checkExpertFile('${item.expertCommitBId}','${item.id}',1)" >${item.expertCommitBScore}</a>
	             </td>

	             <td width="92" <c:if test="${item.isConsider=='1'}"> bgcolor='#D6F1F1' </c:if>>
	            	<!-- 1:标识需要合议  ， 0：无需合议 -->

<%--	             	<c:choose>--%>
<%--	             	 	<c:when test="${(item.isConsider=='1' and item.expertConsiderScore=='') or (item.isConsider=='1' and item.expertConsiderScore== null)}">--%>
				       		<%-- <input id="expertConsiderScore${status.index+1}" readonly type="text" class="form-control input-sm"  placeholder="请输入得分" value="${item.expertConsiderScore}"> --%>
<%--					     </c:when>--%>
<%--				       <c:otherwise>--%>
				      	${item.expertConsiderScore}
<%--				      	${item.expertconsiderscore2}--%>
<%--				      	<input id="expertHiddenScore${status.index+1}" type="hidden" value="${item.expertConsiderScore}">--%>
				      	<input id="expertHiddenScore${status.index+1}" type="hidden" value="${item.expertConsiderScore}">
<%--				       </c:otherwise>--%>
<%--					</c:choose>--%>

	             </td>
	             <!-- 专家人员计算  结束-->
	             <td <c:if test="${item.isConsider=='1'}"> bgcolor='#D6F1F1' </c:if> >
	             	<c:choose>
				       <c:when test="${item.isConsider=='1' && sessionScope.sa_session.sysStatus =='5' }">

						   <%-- <button id="heyi${status.index+1}" class="btn btn-danger btn-xs" onclick="heYi('${status.index+1 }')"  data-toggle="modal" data-target="#myModal">合议</button>
                             <button style="display:none"   onclick="submitHeYi('${status.index+1 }')"   id="baocun${status.index+1}" class="btn btn-info btn-xs" data-toggle="modal" data-target="#myModal">保存</button>
                             <button style="display:none"   onclick="cancelHeYi('${status.index+1 }')" id="quxiao${status.index+1}" class="btn btn-primary btn-xs" data-toggle="modal" data-target="#myModal">返回</button> --%>
				       </c:when>
				       <c:otherwise>
				     	 &nbsp;
				       </c:otherwise>
					</c:choose>
					<%-- <button   onclick="downloadFile('${item.id }')"   id="baocun${status.index+1}" class="btn btn-info btn-xs" data-toggle="modal" data-target="#myModal">下载</button>--%>
					 <a v-if="item.downUrl != null && item.downUrl != ''" href="${item.downUrl}" target="_Blank">
						 <button id="download" class="btn btn-success btn-xs" data-toggle="modal" data-target="#myModal"  style="margin: 0 10px">下载</button>
					 </a>
	             </td>
		      </tr>
	      </c:forEach>
         </tbody>
       </table>
    </div>
    <!--列表翻页 开始-->
    <div class="page">
    	<span style="padding:12px;  float:left; color:#0099cc;">共${ajpfBeanList.total}条记录</span>
        <ul class="pagination" id="pageCon">

        </ul>
    </div>
    <!--列表翻页 结束-->
</div>

<script language="JavaScript">
//跳转页面
function goCommitScore(){

}


// 搜索条件
$(document).ready(function(){
	//监听enter
	business.listenEnter("searchButt");
	$("#searchButt").click(function(){
		business.addMainContentParserHtml("anjuanList.do?pageIndex=1",$("#searchForm").serialize());
	});
	$("#fileTypeSelect").change(function(){
		business.addMainContentParserHtml("anjuanList.do?pageIndex=1",$("#searchForm").serialize());
	});
	$("#isConsiderSelect").change(function(){
		business.addMainContentParserHtml("anjuanList.do?pageIndex=1",$("#searchForm").serialize());
	});
	$("#viewExcel").click(function(){
	  var areaType = $("#areaType").val();
	  var isConsider = $("#isConsiderSelect").val();
	  var fileCode = $("#fileCode").val();
	  var path = WEBPATH+"/ajpfViewExcel.do?target=1&viewType=3&areaType="+areaType+"&isConsider="+isConsider+"&fileCode="+fileCode;
      window.location.href=path;
	});
});
//分页
$(document).ready(function(){
	var curentPage = eval('${ajpfBeanList.pageNum}');
	var totalPage = eval('${ajpfBeanList.pages}');
	if(totalPage>0){
		var options = {
			bootstrapMajorVersion: 3,
		    currentPage: curentPage,
		    totalPages: totalPage,
		    numberOfPages: 5,
		    itemTexts: function (type, page, current) {
		            switch (type) {
		            case "first":
		                return "首页";
		            case "prev":
		                return "&laquo;";
		            case "next":
		                return "&raquo;";
		            case "last":
		                return "尾页";
		            case "page":
		                return page;
		            }
		    },
		    onPageClicked: function (event, originalEvent, type, page) {
            	business.addMainContentParserHtml(WEBPATH+'/anjuanList.do?pageIndex=1&pageNum='+page,$("#searchForm").serialize());
            }
    	};
    	$('#pageCon').bootstrapPaginator(options);
   	}
});

// 点击合议按钮  coordinate：坐标
function heYi(coordinate){
	$("#heyi"+coordinate).hide();
	$("#baocun"+coordinate).show();
	$("#quxiao"+coordinate).show();
	var expertScore = $("#expertConsiderScore"+coordinate);

	if(expertScore.length >0)
	{
		expertScore.removeAttr("readonly");
	}

};
// 合议后保存
function submitHeYi (coordinate){
	// 回显页面条件信息
	var pageNum = getPageNum();
	var areaTypeVal = getAreaTypeVal();
	var isConsiderVal= getIsConsiderVal();
	var fileCodeVal =getFileCodeVal();
	var fileType = getfileType();
	// 校验  和 数值区间
	//var  regexpCh = /(?!^0\.0?0$)^([0-9]|[0-9][0-9]|[1][0-1][0-9])?(\.[0-9]{1,2})?$|^(120|120\.0|120\.00|0\.0|0.00)/;
	var regexpCh =  /(?!^0\.0?0$)^([0-9]|[0-9][0-9]|[1][0-1][0-9])?(\.[0-9]{1,2})?$|^(120|120\.0|120\.00|0\.0|0.00)$/;

	// var regexpCh = /(?!^0\.0?0$)^[0-9][0-9]?(\.[0-9]{1,2})?$|^(100|0\.0|0\.00|100\.0|100\.00)$/;
	var fileIdVal="",expertScoreVal="";
	var fileId = $("#id"+coordinate).val();
	var expertScore = $("#expertConsiderScore"+coordinate);

	//var crossScore = $("#crossConsiderScore"+coordinate);
	// 校验数据合法性
	if(expertScore.length >0) // 是否找到这个对象
	{
		expertScoreVal = expertScore.val();
		// 这里需要校验
		if($.trim(expertScoreVal)==""){
			swal("合议分不能为空", "信息保存操作失败了!", "error");
			return false;
		}
		if(!regexpCh.test(expertScoreVal))
		{
			swal("分值范围在0-120，可输入整数和小数，小数部分最多保留2位", "信息保存操作失败了!", "error");
			return false;
		}

	}else
	{
		expertScoreVal = $("#expertHiddenScore"+coordinate).val();
	}
/* 	if(crossScore.length>0)
	{
		crossScoreVal = crossScore.val();
		// 这里需要校验
		if($.trim(crossScoreVal)==""){
			swal("交叉合议分不能为空", "信息保存操作失败了!", "error");
			return false;
		}
		if(!regexpCh.test(crossScoreVal))
		{
			swal("交叉合议分输入不合法分区间0~100", "信息保存操作失败了!", "error");
			return false;
		}
	}else
	{
		crossScoreVal = $("#crossHiddenScore"+coordinate).val();
	} */
    $.ajax({
             type: "POST",
             url: WEBPATH+"/saveFilesBean.do",
             data:{fileId:fileId,expertScoreVal:expertScoreVal},
             async:false,
             success: function(data){
	             if(data.result=="error"){
	               	swal({title: data.message ,text: "",type:"error"});
	                return false;
	             }else if(data.result=="success"){
	              	swal({title: data.message ,text: "",type:"success"});
	              	business.addMainContentParserHtml(WEBPATH+'/anjuanList.do?pageIndex=1&pageNum='+pageNum+'&areaType='+areaTypeVal+'&isConsider='+isConsiderVal+'&fileType='+fileType+'&fileCode='+fileCodeVal,null);
	             }
           }
     });


};
// 取消合议
function cancelHeYi(coordinate){
	$("#heyi"+coordinate).show();
	$("#baocun"+coordinate).hide();
	$("#quxiao"+coordinate).hide();
	var expertScore = $("#expertConsiderScore"+coordinate);
	//var crossScore = $("#crossConsiderScore"+coordinate);
	if(expertScore.length >0)
	{
		expertScore.val("");
		expertScore.attr("readonly","readonly");
	}
	/* if(crossScore.length >0)
	{
		crossScore.val("");
		crossScore.attr("readonly","readonly");
	}  */
};

// 查询专家和交叉打分情况  coordinate 下标   type 第几位专家 expertOrCross 专家还是交叉
function selectExpertScore(coordinate,type,expertOrCross){
 	var pageNum = getPageNum();
	var fileId = $("#id"+coordinate).val();
	var fileCode  = $("#fileCode"+coordinate).val();
	var fileTypeTwo = $("#filetype"+coordinate).val();
	var expertId;
	//
	if(expertOrCross=='1'){
		if(type=='1'){
			expertId =  $("#expertAId"+coordinate).val();
		}
		if(type =='2'){
			expertId =  $("#expertBId"+coordinate).val();
		}
	}
	if(expertOrCross=='2'){
		if(type=='1'){
			expertId =  $("#crossReviewAId"+coordinate).val();
		}
		if(type =='2'){
			expertId =  $("#crossReviewBId"+coordinate).val();
		}
	}
	// 非空判断专家或者交叉的id为空
	if(expertId === undefined || $.trim(expertId)=="")
	{
		swal("信息操作错误！", "信息保存操作失败了!", "error");
		return false;
	}
	if(fileCode === undefined || $.trim(fileCode)=="")
	{
		swal("案卷Code为空", "信息保存操作失败了!", "error");
		return false;
	}
	if(fileTypeTwo === undefined || $.trim(fileTypeTwo)=="")
	{
		swal("案卷类型为空", "信息保存操作失败了!", "error");
		return false;
	}
	var expertOrCrossType = "1";
    business.addMainContentParserHtml(WEBPATH+'/getExpertUserByType.do?pageIndex=1&fileTypeTwo='+fileTypeTwo+'&fileId='+fileId+'&expertId='+expertId+'&fileCodeCurrent='+fileCode+'&pageNum='+pageNum+'&expertOrCross='+expertOrCross+'&expertOrCrossType='+expertOrCrossType,$("#searchForm").serialize());
}

// 获取回显当前页
function getPageNum(){
	var pageNum = eval('${ajpfBeanList.pageNum}');
	if(pageNum === undefined ){
			pageNum = '';
	}
	return pageNum;
}
// 获得回显案卷类型
function getAreaTypeVal(){
	var areaTypeVal = eval('${areaType}');
	if(areaTypeVal === undefined ){
		areaTypeVal = '';
	}
	return areaTypeVal;
}
// 获得回显是否合议状态
function getIsConsiderVal(){
	var isConsiderVal= eval('${isConsider}');
		if(isConsiderVal === undefined ){
		isConsiderVal = '';
	}
	return isConsiderVal;
}
// 获得回显决定文书号
function getFileCodeVal(){
	var fileCodeVal = '${fileCode}';
		if(fileCodeVal === undefined ){
		fileCodeVal = '';
	}
	return  fileCodeVal;
}
function getfileType(){
	var fileType = '${fileType}';
	if(fileType === undefined ){
		fileType = '';
	}
	return  fileType;

}
$(document).ready(function() {
	$(".topnav").accordion({
		accordion:false,
		speed: 500,
		closedSign: '[+]',
		openedSign: '[-]'
	});
});
function checkExpertFile(expertID,fileID,type){

	if(expertID==null||expertID == ''){
		return ;
	}
	$.ajax({
		data:{expertID:expertID,fileID:fileID},
		dataType:"JSON",
		Type:"post",
		url:WEBPATH+'/zjpf/getExpertHanderID.do',
		success:function(data){

			if(data.id !=null && data.id!=''){
				business.addMainContentParserHtml(WEBPATH+'/zjpf/zjpfScoreView.do?consider='+type+'&status=1&id='+data.id,$("#searchForm").serialize());
			}else{
				swal("操作失败!", data.text, "error");
			}
		}
	});
}

//下载文件
function xiaZaiAnJuan(){
	var fileid1  = $("#fileId").val();
	$.ajax({
		type:"post",
		url:WEBPATH+'/jcpf/jcpfExistFileUrl.do',
		data:{fileId:fileid1 },           //注意数据用{}
		success:function(data){  //成功
			if("yes" == data){
				window.location.href="${pageContext.request.contextPath}/jcpf/jcpfAnJuandown.do?fileid="+fileid1;
				return false;
			}else if("no" == data){
				swal( "操作失败","该案卷不存在!", "error");
				return false;
			}else if("suffixerror" ==data){
				swal( "操作失败","该案卷上传数据格式有问题!", "error");
				return false;
			}
		}
	});
}
</script>
</body>
</html>
