<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>


<c:set var="webpath">${pageContext.request.contextPath}</c:set>

<script type="text/javascript" src="${webpath}/static/businessJs/zlpf/zlpfMain.js"></script>

<body>
<!--框架左侧菜单 开始-->
<div class="frame_left">
	<div class="left_menu">
        <div class="left_menu_bt"><img src="${webpath}/static/images/caiji_1.png" style="margin-right:5px;" />案卷评分结果</div>
        <div class="tree">
            <input id="pageIndex" type="hidden" value="${pageIndex}">
            <ul class="topnav">
               <c:if test="${fn:startsWith(sessionScope.sa_session.loginid, 'crossAdmin')}">
               			<%-- <li><a href="javascript:void(0);" onclick="macroMgr.onLevelTwoMenuClick(null, 'firstAjpfCross.do')"  class="active"> <img src="${webpath}/static/images/shubiao.png" />交叉案卷合议列表</a></li> --%>
               			<li><a href="javascript:void(0);"  onclick="macroMgr.onLevelTwoMenuClick(null, 'xxhz/fileList.do')" class="active"><img src="${webpath}/static/images/shubiao.png" />案卷列表</a></li>
               </c:if>
               <c:if test="${sessionScope.sa_session.loginid == 'expAdmin'}">
               			<li><a href="javascript:void(0);" onclick="macroMgr.onLevelTwoMenuClick(null, 'anjuanList.do?pageIndex=1')" > <img src="${webpath}/static/images/shubiao.png" />专家组长案卷合议列表</a></li>
               			<li><a href="javascript:void(0);"  onclick="macroMgr.onLevelTwoMenuClick(null, 'xxhz/fileList.do')" class="active"><img src="${webpath}/static/images/shubiao.png" />案卷列表</a></li>
               </c:if>
                <c:if test="${!(fn:startsWith(sessionScope.sa_session.loginid, 'crossAdmin'))&& sessionScope.sa_session.loginid != 'expAdmin'}">
            	<%-- <li><a href="javascript:void(0);" onclick="macroMgr.onLevelTwoMenuClick(null, 'firstAjpfCross.do')"  class="active"> <img src="${webpath}/static/images/shubiao.png" />交叉案卷合议列表</a></li> --%>
            	<li><a href="javascript:void(0);" onclick="macroMgr.onLevelTwoMenuClick(null, 'anjuanList.do?pageIndex=1')" > <img src="${webpath}/static/images/shubiao.png" />专家组长案卷合议列表</a></li>
                <li><a href="javascript:void(0);" onclick="macroMgr.onLevelTwoMenuClick(null, 'anjuanList.do?pageIndex=2&userTypeCode=${userTypeCode}&areaType=${areaType}&isInCheck=${isInCheck}&fileCode=${fileCode}&pageNum=${pageNum}')" ><img src="${webpath}/static/images/shubiao.png" />案卷评分结果</a></li>
                <li><a href="javascript:void(0);" onclick="macroMgr.onLevelTwoMenuClick(null, 'anjuanList.do?pageIndex=3')" ><img src="${webpath}/static/images/shubiao.png" />实体否决结果</a></li>
                </c:if>
            </ul>
        </div>
         <c:if test="${!(fn:startsWith(sessionScope.sa_session.loginid, 'crossAdmin'))&& sessionScope.sa_session.loginid != 'expAdmin'}">
        <%-- <div class="left_menu_bt"><img src="${webpath}/static/images/caiji_1.png" style="margin-right:5px;" />个人事迹评分结果</div> --%>
        <div class="tree">
            <%-- <ul class="topnav">
                <li><a href="javascript:void(0);" onclick="macroMgr.onLevelTwoMenuClick(null, 'gerenList.do')"><img src="${webpath}/static/images/shubiao.png" />个人事迹评分列表</a></li>
            </ul> --%>
        </div>
        </c:if>
    </div>
</div>
<!--框架左侧菜单 结束-->
<!-- 页面中部~中间 start  -->
<div id="main_content" >
</div>
</body>
