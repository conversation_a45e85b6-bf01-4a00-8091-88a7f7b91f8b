<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%--fmt 标签库--%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
<style type="text/css">
	.pdfobject-container {
		width: 100%;
		height:580px;
		margin: 2em 0;
	}
	.textarea-success {
		border-top:green 1px solid;
		border-bottom:green 1px solid;
		border-left:green 1px solid;
     	border-right:green 1px solid;
	}
	.textarea-error {
		border-top:red 1px solid;
		border-bottom:red 1px solid;
		border-left:red 1px solid;
     	border-right:red 1px solid;
	}
	.ceshi{
		color: #DF1912;
		font-weight: 600;
		font-size: 14px;
	}
	.center_list{background-color: #ffffff;padding-left:19px;margin: 0}
	#indexName{
	height: 40px;
	width: 270px;
	background: #FFFFFF;
	border: 1px solid #DCDFE6;
	border-radius: 3px;
	margin-left: 15px;}
	.table>thead>tr>td{line-height:3.1}
	.center{	position: absolute;left: 210px;top: 180px;bottom: 35px;right: 5px;
	}
	.table-bordered>thead>tr>td{
		background-color: #F5F7FA;
		border: 1px solid #EBEEF5;
		text-align: center;
	}

</style>
</head>
<body>
	<div id="ExpertVue">
		<div class="center_weizhi">当前位置：专家评分 - 实体否决结果 - ${filetypeName}</div>
		<div style="position: absolute;top: 127px;left: 210px;right: 5px;padding: 5px 0 0 19px;background: #ffffff;">
			<c:if test="${userTypeCode == '4'}">
				<button class="btn btn-primary"  style="font-size:16px;width:98px;height: 40px; padding: 6px 34px; margin-right: 10px;float: right;" onclick="macroMgr.onLevelTwoMenuClick(null, 'anjuanList.do?pageIndex=3&areaType=${areaType}&indexId=${indexId}&fileCode=${fileCode}')">返回</button>
			</c:if>
			<c:if test="${userTypeCode != '4'}">
				<button class="btn btn-primary"  style="font-size:16px;width:98px;height: 40px; padding: 6px 34px; margin-right: 10px;float: right;" onclick="macroMgr.onLevelTwoMenuClick(null, 'anjuanList.do?pageIndex=3')">返回</button>
			</c:if>
		</div>

		<div class="center" id="scrollBarCenter">
			<div class="center_list">
		 		<input id="filesId" type="hidden" value="${entityVetoInfo.filesId}" >

		       	<form action="#" id ="zjpfForm" method="post">
		        	<table class="table table-bordered table-hover table-condensed" >
						<thead>
						<tr>
							<td height="30" colspan="2" bgcolor="#efefef"><h4>案卷评分情况</h4></td>
						</tr>

						</thead>
						<tbody  class="form-group">
						<tr >
							<td bgcolor="#efefef">
								<h4>案卷号: <text style="color: #1a7bc9;font-size: 18px">${entityVetoInfo.fileCode}</text></h4>
							</td>
							<td bgcolor="#efefef" width="60%">
								<h4>否决类型: <text style="color: #1a7bc9;font-size: 18px">${scoringIndexNames}</text></h4>
							</td>
						</tr>
						<c:forEach items="${entityVetoInfo.filesEntityExpertList}" var="FilesEntityExpertVo"  varStatus="FilesEntityExpertStatus">

							<tr>
								<td colspan="2"><h4>第${FilesEntityExpertVo.handlType+1}轮评审情况</h4></td>
							</tr>
							<c:forEach items="${FilesEntityExpertVo.filesEntityItemList}" var="FilesEntityItemVo" varStatus="FilesEntityItemStatus">
								<tr>
									<td style="color: #1a7bc9"><h5>${FilesEntityItemVo.indexName}</h5></td>
									<td style="color: #1a7bc9">${FilesEntityItemVo.itemName}</td>
								</tr>
								<c:forEach items="${FilesEntityItemVo.filesEntityHandlTypeList}" var="FilesEntityHandlTypeVo" varStatus="FilesEntityHandlTypeStatus">
									<tr>
										<td style="color: #1a7bc9">${FilesEntityHandlTypeVo.expertName}</td>
										<td style="color: #1a7bc9">${FilesEntityHandlTypeVo.comment}</td>
									</tr>
								</c:forEach>
							</c:forEach>
						</c:forEach>
						<tr style="text-align: center; ">
							<td colspan="2">
								该案卷存在的问题:
								<textarea id="filesIssues" rows="4" wrap="hard" maxlength="400"
										  style="width: 90%" placeholder="请输入..."
									>${entityVetoInfo.filesIssues}</textarea>
							</td>
						</tr>
						</tbody>
					</table>

				</form>
				<button class="btn btn-primary" style="font-size:16px;width:98px;height: 40px;padding: 6px 34px;margin-right: 10px;float: right;" id="saveEntityVeto"
						onclick="saveEntityVetoInfo(${entityVetoInfo.filesId})">
					保存
				</button>
			</div>
		</div>
	</div>
</body>
<script type="text/javascript">
	function saveEntityVetoInfo(filesId){
		//案卷存在的问题
		var filesIssues = document.getElementById("filesIssues").value;
		$.ajax({
			type:"post",
			url:WEBPATH+'/saveEntityVetoInfo.do',
			data:{
				filesId:filesId,
				filesIssues:filesIssues
			},
			success:function (data){
				console.log("测试",data)
				if (data.result == 'success'){
					swal({title: data.message ,text: "保存成功",type:"success"});
					business.addMainContentParserHtml(WEBPATH+'/anjuanList.do?pageIndex=3');
				}else {
					swal({title: data.message ,text: "保存失败",type:"error"});
					return false;
				}
			}
		})

	}

	<%--$(".left_menu").hide();--%>

	<%--business.listenEnter();--%>
	<%--var pageNum = $("#pageNum").val();--%>
	<%--var scoringIndexList = null;--%>
	<%--var expertFileId = $("#expertFileId").val();--%>


	<%--$.ajax({--%>
	<%--	cache : true,--%>
	<%--	type : "GET",--%>
	<%--	async : false,--%>
	<%--	//api/test/detail/behaviour/74--%>
	<%--	url: WEBPATH+"/zjpf/getIndexList.do",--%>
	<%--	data:{--%>
	<%--		id:expertFileId--%>
	<%--	},--%>
	<%--	error : function(request) {--%>
	<%--		swal("错误!","请求异常！", "error");--%>
	<%--	},--%>
	<%--	success : function(data) {--%>
	<%--		if(data.result =='success'){--%>
	<%--			scoringIndexList = data.data;--%>
	<%--			for(var i=0;i<data.data.expertHandlIndexScoreList.length;i++){--%>
	<%--				data.data.expertHandlIndexScoreList[i].className = 'class'+ data.data.expertHandlIndexScoreList[i].id;--%>
	<%--				var index = data.data.expertHandlIndexScoreList[i];--%>
	<%--				//先创建好select里面的option元素--%>
	<%--				var option=document.createElement("option");--%>
	<%--				//转换DOM对象为JQ对象,好用JQ里面提供的方法 给option的value赋值--%>
	<%--				$(option).val('class' + index.id);--%>
	<%--				$(option).text(index.indexname);--%>
	<%--				$("#indexName").append(option);--%>
	<%--			}--%>
	<%--			//	console.log(scoringIndexList);--%>
	<%--		}--%>
	<%--	}--%>
	<%--});--%>

	<%--var xzcfVue = new Vue({--%>
	<%--	el: '#ExpertVue',--%>
	<%--	data: {--%>
	<%--		scoringIndexList:scoringIndexList,--%>
	<%--	},--%>
	<%--	computed:{--%>
	<%--		totalScore:function(){--%>
	<%--			var sum = 0;--%>
	<%--			this.scoringIndexList.expertHandlIndexScoreList.forEach(function(item){--%>
	<%--				if(item.inCheckValue != 1) {--%>
	<%--					if((item.indexscore=="null"||item.indexscore==="")||item.temIndexId==110||item.indexid==110){--%>
	<%--						sum += 0;--%>
	<%--					}else{--%>
	<%--						sum += item.indexscore;--%>
	<%--					}--%>
	<%--				}--%>
	<%--			});--%>
	<%--			return sum;--%>
	<%--		}--%>
	<%--	},--%>
	<%--	methods: {}--%>
	<%--});--%>


	<%--function change(value) {--%>
	<%--	debugger--%>
	<%--	//alert(selectOption.val());--%>
	<%--	var mao;--%>
	<%--	var pos;--%>
	<%--	var poshigh;--%>
	<%--	mao = $("." + value);--%>
	<%--	pos = mao.offset().top;--%>
	<%--	poshigh = mao.height();--%>
	<%--	debugger--%>
	<%--	$("#scrollBarCenter").animate({scrollTop: pos - 229})--%>
	<%--}--%>



	<%--//处理浮点型数据相减--%>
	<%--function handFloat(biaozhunfen,subScore){--%>
	<%--	var r1,r2,m,n;--%>
	<%--	try{r1=biaozhunfen.toString().split(".")[1].length}catch(e){r1=0};--%>
	<%--	try{r2=subScore.toString().split(".")[1].length}catch(e){r2=0};--%>
	<%--	m=Math.pow(10,Math.max(r1,r2));--%>
	<%--	n = (r1 >= r2) ? r1 : r2;--%>
	<%--	return ((biaozhunfen * m - subScore * m) / m).toFixed(n);--%>
	<%--}--%>

	<%--/**--%>
	<%-- * 回显案卷类型--%>
	<%-- */--%>
	<%--var a = xzcfVue.scoringIndexList.yxdxanlituijian;--%>
	<%--$(function () {--%>
	<%--	if(xzcfVue.scoringIndexList.yxdxanlituijian=="1"){--%>
	<%--		//$('#yxdxanlituijian').attr('checked', true)--%>

	<%--		$("[name = yxdxanlituijian]:checkbox").attr("checked", true);--%>
	<%--		$("#yxdxAnLiTuiJianReviews1").css("display","block");--%>

	<%--		//优秀案卷选项回显--%>
	<%--		var checkArray = $("input[name='yxaj']");--%>
	<%--		var codes = xzcfVue.scoringIndexList.ajpjYxdxanlituijianCode;--%>
	<%--		if(codes != null && codes != "" ){--%>
	<%--			var myArray=codes.split(",");--%>
	<%--			for (var i = 0; i < myArray.length; i++) {--%>
	<%--				//获取所有复选框对象的value属性，然后，用checkArray[i]和他们匹配，如果有，则说明他应被选中--%>
	<%--				$.each(checkArray, function (j, checkbox) {--%>
	<%--					//获取复选框的value属性--%>
	<%--					var checkValue=$(checkbox).val();--%>
	<%--					//console.log(j+"----"+checkValue)--%>
	<%--					if (myArray[i] == checkValue) {--%>
	<%--						$(checkbox).attr("checked", true);--%>
	<%--					}--%>
	<%--				})--%>
	<%--			}--%>
	<%--		}--%>
	<%--		//禁止较差推荐--%>
	<%--		$("#ajpjYxdxanlituijian").attr("disabled","disabled");--%>
	<%--		$("#ajpjYxdxAnLiTuiJianReviews1").css("display", "none");--%>
	<%--		//禁止不推荐为典型案例--%>
	<%--		$("#noTuiJian").attr("disabled","disabled");--%>
	<%--	}--%>


	<%--	var ajpja = xzcfVue.scoringIndexList.ajpjYxdxanlituijian;--%>
	<%--	if(xzcfVue.scoringIndexList.ajpjYxdxanlituijian=="1"){--%>
	<%--		//$('#ajpjYxdxanlituijian').attr('checked', true)--%>
	<%--		$("[name = ajpjYxdxanlituijian]:checkbox").attr("checked", true);--%>
	<%--		$("#ajpjYxdxAnLiTuiJianReviews1").css("display","block");--%>
	<%--		//较差案卷选项回显--%>
	<%--		var checkArray = $("input[name='jcaj']");--%>
	<%--		var codes = xzcfVue.scoringIndexList.jcajtuijianCode;--%>
	<%--		if(codes != null && codes != "" ){--%>
	<%--			var myArray=codes.split(",");--%>
	<%--			for (var i = 0; i < myArray.length; i++) {--%>
	<%--				//获取所有复选框对象的value属性，然后，用checkArray[i]和他们匹配，如果有，则说明他应被选中--%>
	<%--				$.each(checkArray, function (j, checkbox) {--%>
	<%--					//获取复选框的value属性--%>
	<%--					var checkValue=$(checkbox).val();--%>
	<%--					//console.log(j+"----"+checkValue)--%>
	<%--					if (myArray[i] == checkValue) {--%>
	<%--						$(checkbox).attr("checked", true);--%>
	<%--					}--%>
	<%--				})--%>
	<%--			}--%>
	<%--		}--%>
	<%--		//禁止优秀推荐--%>
	<%--		$("#yxdxanlituijian").attr("disabled","disabled");--%>
	<%--		$("#yxdxAnLiTuiJianReviews1").css("display", "none");--%>
	<%--		//禁止不推荐为典型案例--%>
	<%--		$("#noTuiJian").attr("disabled","disabled");--%>
	<%--	}--%>

	<%--	// 不推荐为典型案例 回显--%>
	<%--	if(xzcfVue.scoringIndexList.noTuiJian=="1"){--%>
	<%--		$("#noTuiJian").attr("checked", true);--%>
	<%--		//禁止优秀推荐--%>
	<%--		$("#yxdxanlituijian").attr("disabled","disabled");--%>
	<%--		$("#yxdxAnLiTuiJianReviews1").css("display", "none");--%>
	<%--		//禁止较差推荐--%>
	<%--		$("#ajpjYxdxanlituijian").attr("disabled","disabled");--%>
	<%--		$("#ajpjYxdxAnLiTuiJianReviews1").css("display", "none");--%>
	<%--	}--%>

	<%--})--%>


	<%--//下载文件--%>
	<%--function xiaZaiAnJuan(){--%>
	<%--	var fileId = $("#fileId").val();--%>
	<%--	$.ajax({--%>
	<%--		type:"post",--%>
	<%--		url:WEBPATH+'/zjpf/xzcfExistFileUrl.do',--%>
	<%--		data:{fileId:fileId},           //注意数据用{}--%>
	<%--		success:function(data){  //成功--%>
	<%--			if("yes" == data){--%>
	<%--				window.location.href="${pageContext.request.contextPath}/zjpf/zjpfAnJuandown.do?fileId="+fileId;--%>
	<%--				return false;--%>
	<%--			}else if("no" == data){--%>
	<%--				swal( "操作失败","该案卷不存在!", "error");--%>
	<%--				return false;--%>
	<%--			}else if("suffixerror" ==data){--%>
	<%--				swal( "操作失败","该案卷上传数据格式有问题!", "error");--%>
	<%--				return false;--%>
	<%--			}--%>
	<%--		}--%>
	<%--	});--%>
	<%--}--%>

	<%--$(document).ready(function(){--%>
	<%--	$(":input[type='checkbox']").attr("disabled","disabled");--%>
	<%--	$(":input[type='text'], textarea").attr("disabled","disabled");--%>

	<%--	//保存数据方法--%>
	<%--	var status = '${status}';--%>
	<%--	if(status==1){//status为1时是查看，否则为编辑--%>
	<%--		$("#chickAnjuan").hide();--%>
	<%--		$("#fileCodeInput").attr("disabled","disabled");--%>
	<%--		var scoredstate = ${expertHandlFileList.scoredstate};--%>
	<%--		// if(scoredstate==1 || scoredstate ==5){--%>
	<%--		$("#fileCodeInput").val('${expertHandlFileList.filecode}');--%>
	<%--		// }else{--%>
	<%--		// 	/* $("#download").attr("disabled","disalbed"); */--%>
	<%--		// 	$("#look").attr("disabled","disalbed");//预览按钮在评分后才能点击--%>
	<%--		// }--%>
	<%--	}else{--%>
	<%--		var scoredstate = ${expertHandlFileList.scoredstate};--%>
	<%--		if(scoredstate==1){--%>
	<%--			$("input[name='scoreInput']").removeAttr("disabled");--%>

	<%--			$("#zjpy").removeAttr("disabled");--%>
	<%--			$("#ajpj").removeAttr("disabled");--%>
	<%--			$("#submitBtn").removeAttr("style display");--%>
	<%--			$("#chickAnjuan").hide();--%>
	<%--			$("#fileCodeInput").attr("disabled","disabled");--%>
	<%--			$("textarea").removeAttr("disabled");--%>
	<%--			$("#fileCodeInput").val('${ expertHandlFileList.filecode}');--%>
	<%--			$("#yxdxanlituijian").attr("disabled",false);--%>
	<%--			$("#ajpjYxdxanlituijian").attr("disabled",false);--%>

	<%--			/* 页面初始化加载设置隐藏 input输入框，当用户选择一票否决和无须评查  */--%>
	<%--			if(xzcfVue.scoringIndexList != null && xzcfVue.scoringIndexList.expertHandlIndexScoreList != null ){--%>
	<%--				for( var i=0;i< xzcfVue.scoringIndexList.expertHandlIndexScoreList.length;i++){--%>
	<%--					if(xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].inCheckValue ==1 ||xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].voteDownValue ==1){--%>
	<%--						var checkitem =".checkitem"+i;--%>
	<%--						$(checkitem).attr("disabled","disabled");--%>
	<%--						if(xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].inCheckValue ==1){--%>
	<%--							//如果选了无须评查--%>
	<%--							xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].resultScore=xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].indexscore;--%>
	<%--							var isVoteDownid = '#isVoteDown'+i;--%>
	<%--							$(isVoteDownid).attr("disabled","disabled");--%>
	<%--						}--%>
	<%--						if(xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].voteDownValue ==1){--%>
	<%--							//若果选了一票否决--%>
	<%--							xzcfVue.scoringIndexList.expertHandlIndexScoreList[i].resultScore=0;--%>
	<%--							var isInCheckid = '#isInCheck'+i;--%>
	<%--							$(isInCheckid).attr("disabled","disabled");--%>
	<%--						}--%>
	<%--					}--%>
	<%--				}--%>
	<%--			}--%>
	<%--		}else{--%>
	<%--			/* $("#download").attr("disabled","disalbed"); */--%>
	<%--			$("#look").attr("disabled","disalbed");--%>
	<%--		}--%>
	<%--	}--%>
	<%--});--%>





	<%--function loding(btn,itext){--%>
	<%--	document.getElementById(btn).innerHTML = "加载.."--%>
	<%--	document.getElementById(btn).disabled = "disabled"--%>
	<%--	setTimeout(function () {--%>
	<%--		document.getElementById(btn).innerHTML = itext;--%>
	<%--		document.getElementById(btn).removeAttribute("disabled");--%>
	<%--	},3000);--%>
	<%--}--%>



	<%--$('#searcher').focus();--%>
</script>

</html>
