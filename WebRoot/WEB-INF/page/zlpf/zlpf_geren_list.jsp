<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<!doctype html>
<html>
<head>
</head>

<body>
<div class="center_weizhi">当前位置：案卷总分 - 个人事迹评分结果 - 个人事迹评分列表</div>
<div class="center">
<div class="center_list">
    <div class="panel-group" id="accordion">
          <h4 class="panel-title">
            <!---信息填报
            <div class="btn-group" style="margin-right:20px;float:left;">
               <a href="CementEnterprises_input.html"><button type="button" class="btn btn-success" style="font-size:16px;">企业信息录入</button></a>
            </div>--->          
            
          <!---快速查询--->
          <form id= "searchForm"  role="form">
            	<!-- <select class="form-control" style="width:150px;margin-right:5px;" name="areaType" id="areaTypeSelect">
                 <option value="" >请选择行政区</option>
                 <option value="1" <c:if test="${areaType=='1' }">selected</c:if>  >省级</option>
                 <option value="2" <c:if test="${areaType=='2' }">selected</c:if> >地市级</option>
                 <option value="3" <c:if test="${areaType=='3' }">selected</c:if> >区县级</option>
              </select>  -->
<%--               <select class="form-control" style="width:150px;margin-right:5px;" name="isConsider" id="isConsiderSelect">
                 <option value="">请选择评审状态</option>
                 <option value="1" <c:if test="${isConsider=='1' }">selected</c:if>  >需要合议</option>
                 <option value="0"  <c:if test="${isConsider=='0' }">selected</c:if> >无需合议</option>
              </select>   --%>       
            <!---搜索--->
            <div style="width:260px;" class="btn-group">
                  <div class="row">                     
                     <div class="col-lg-6">
                        <div class="input-group">
                           <input id="personalMaterialName" name="personalMaterialName" type="text" class="form-control" style="width:200px;"  value="${personalMaterialName}" placeholder="个人事迹材料名称关键字">
                           <span class="input-group-btn">
                              <button id="searchButt"  class="btn btn-success" type="button">
                              		   快速搜索
                              </button>
                              <button type="button" class="btn btn-primary" id ="viewExcel">导出EXCEL</button>
                           </span>
                       
                        </div><!-- /input-group -->
                     </div><!-- /.col-lg-6 -->
                        
                  </div><!-- /.row -->
                  </div>   
              
               </form>
          </h4>
    </div>
    <table class="table table-bordered table-hover table-condensed">
      <thead>
        <tr>
          <td width="50" height="30" bgcolor="#efefef">序号</td>
       <!--  <td bgcolor="#efefef">省</td>
          <td bgcolor="#efefef">地市</td>
          <td bgcolor="#efefef">区县</td>  -->
          <td bgcolor="#efefef">个人事迹材料名称</td>
          <td width="100" bgcolor="#efefef">交叉骨干姓名</td>
          <!-- <td width="100" bgcolor="#efefef">初评得分</td> -->
<!--           <td width="100" bgcolor="#efefef">交叉骨干姓名</td>
          <td width="100" bgcolor="#efefef">初评得分</td>
          <td width="100" bgcolor="#efefef">合议评审得分</td> -->
          <td width="100" bgcolor="#efefef">评审得分</td>
          <!-- <td width="90" bgcolor="#efefef">操作</td> -->
        </tr>
      </thead>
      <tbody>
        <c:forEach items="${ajpfPersonalList.list}" var="item" varStatus="status">
		      <tr>
		      <!-- <c:if test="${item.isConsider=='1'}"> bgcolor='#D6F1F1' </c:if> -->
			     <td  height="30" align="center">${status.index+1 }  
			     	 <input id="id${status.index+1}" type="hidden"  value="${item.id}">
			     	 
			     	 <input id="personalMaterialName${status.index+1}" type="hidden"  value="${item.personalMaterialName}">
				     <input id="crossreviewaid${status.index+1}" type="hidden"  value="${item.crossreviewaid}">
				     <input id="crossreviewbid${status.index+1}" type="hidden"  value="${item.crossreviewbid}">
				   
			     </td>
			     <!-- 
	             <td <c:if test="${item.isConsider=='1'}"> bgcolor='#D6F1F1' </c:if>>${item.province}</td>
	             <td <c:if test="${item.isConsider=='1'}"> bgcolor='#D6F1F1' </c:if>>${item.city}</td>
	             <td <c:if test="${item.isConsider=='1'}"> bgcolor='#D6F1F1' </c:if>>${item.country}</td>
	              -->
	             <td  >${item.personalMaterialName}</td>
	             <td  >${item.perCrossReviewNameA}</td>
	             <%-- <td  >${item.perCrossReviewAScore} --%>
	             		<%-- <a  href="javascript:void(0);" onclick="selectExpertScore('${status.index+1 }',1)" ></a> --%>
	             <%-- </td> --%>
<%-- 	             <td <c:if test="${item.isConsider=='1'}"> bgcolor='#D6F1F1' </c:if>>${item.perCrossReviewNameB}</td>
	             <td <c:if test="${item.isConsider=='1'}"> bgcolor='#D6F1F1' </c:if>>
	             		<a  href="javascript:void(0);" onclick="selectExpertScore('${status.index+1 }',2)">${item.perCrossReviewBScore}</a>
	             </td>
	             <td <c:if test="${item.isConsider=='1'}"> bgcolor='#D6F1F1' </c:if>>
	            	<!-- 1:标识需要合议  ， 0：无需合议 -->
	           
	             	<c:choose>
					       <c:when test="${(item.isConsider=='1' and item.perConsiderScore=='') or (item.isConsider=='1' and item.perConsiderScore== null) }">
					       		<input id="perConsiderScore${status.index+1}" readonly type="text" class="form-control input-sm"  placeholder="请输入得分" value="${item.perConsiderScore}">
					       </c:when>
					       <c:otherwise>
					      	${item.perConsiderScore}
					       </c:otherwise>
					</c:choose>
	             </td> --%>
	             <td  >${item.perFinalScore}</td>
<%-- 	             <td <c:if test="${item.isConsider=='1'}"> bgcolor='#D6F1F1' </c:if> >
	             	<c:choose>
				       <c:when test="${item.isConsider=='1'  && sessionScope.sa_session.sysStatus =='2' }">
				        <button id="heyi${status.index+1}" class="btn btn-danger btn-xs" onclick="heYiGeren('${status.index+1 }')"  data-toggle="modal" data-target="#myModal">合议</button> 
             		 	<button style="display:none"   onclick="submitHeYiGeren('${status.index+1 }')"   id="baocun${status.index+1}" class="btn btn-info btn-xs" data-toggle="modal" data-target="#myModal">保存</button> 
             		 	<button style="display:none"   onclick="cancelHeYiGeren('${status.index+1 }')" id="quxiao${status.index+1}" class="btn btn-primary btn-xs" data-toggle="modal" data-target="#myModal">返回</button>
				       </c:when>
				       <c:otherwise>
				     	 &nbsp;
				       </c:otherwise>
					</c:choose>
	             </td> --%>
		      </tr>
	    </c:forEach>
      </tbody>
    </table>
</div>
     <!--列表翻页 开始-->
    <div class="page">
    	<span style="padding:12px;float:left; color:#0099cc;">共${ajpfPersonalList.total}条记录</span>
        <ul class="pagination" id="pageCon">
            
        </ul>
    </div>
</div>



<script language="JavaScript">

// 搜索条件
$(document).ready(function(){
	//监听enter
	business.listenEnter("searchButt");
	$("#searchButt").click(function(){
		business.addMainContentParserHtml("gerenList.do",$("#searchForm").serialize());
	});
	$("#areaTypeSelect").change(function(){
		business.addMainContentParserHtml("gerenList.do",$("#searchForm").serialize());
	});
	$("#isConsiderSelect").change(function(){
		business.addMainContentParserHtml("gerenList.do",$("#searchForm").serialize());
	});
	$("#viewExcel").click(function(){
	  var path = WEBPATH+"/ajpfViewExcel.do?target=2"; 
      window.location.href=path;
	});
});
//分页
$(document).ready(function(){
	var curentPage = eval('${ajpfPersonalList.pageNum}');
	var totalPage = eval('${ajpfPersonalList.pages}');
	if(totalPage>0){
		var options = {
			bootstrapMajorVersion: 3,
		    currentPage: curentPage,
		    totalPages: totalPage,
		    numberOfPages: 5,
		    itemTexts: function (type, page, current) {
		            switch (type) {
		            case "first":
		                return "首页";
		            case "prev":
		                return "&laquo;";
		            case "next":
		                return "&raquo;";
		            case "last":
		                return "尾页";
		            case "page":
		                return page;
		            }
		    },
		    onPageClicked: function (event, originalEvent, type, page) {
            	business.addMainContentParserHtml(WEBPATH+'/gerenList.do?pageNum='+page,$("#searchForm").serialize());
            }
    	};
    	$('#pageCon').bootstrapPaginator(options);
   	}
});



// 点击合议按钮  coordinate：坐标
function heYiGeren(coordinate){
	$("#heyi"+coordinate).hide();
	$("#baocun"+coordinate).show();
	$("#quxiao"+coordinate).show();
	var perConsiderScore = $("#perConsiderScore"+coordinate);
	if(perConsiderScore.length >0)
	{
		perConsiderScore.removeAttr("readonly");
	}
};
// 合议后保存
function submitHeYiGeren (coordinate){
	var pageNum = eval('${ajpfPersonalList.pageNum}');
	var areaTypeVal = eval('${areaType}');  
	var isConsiderVal= eval('${isConsider}');  
	var  test =eval('${personalMaterialName}');
	var personalMaterialName = $("#personalMaterialName").val();
	//	regexp: /^[0-9]+([.]{1}[0-9]+){0,1}$/,
	// 校验  和 数值区间				
	var regexpCh =  /(?!^0\.0?0$)^([0-9]|[1][0-9])?(\.[0-9]{1,2})?$|^(20|20\.0|20\.00|0\.0)$/;

	var perScoreVal="";
	var fileId = $("#id"+coordinate).val();
	if(personalMaterialName === undefined ){
		personalMaterialName = '';
	}
	if(isConsiderVal === undefined ){
		isConsiderVal = '';
	}
	if(areaTypeVal === undefined ){
		areaTypeVal = '';
	}
	if(pageNum === undefined ){
		pageNum = '';
	}
	var perScore = $("#perConsiderScore"+coordinate);
	if(perScore.length>0)
	{
		perScoreVal = perScore.val();
		// 这里需要校验
		if($.trim(perScoreVal)==""){
			swal("合议分不能为空", "信息保存操作失败了!", "error");
			return false;
		}
		if(!regexpCh.test(perScoreVal))
		{
			swal("分值范围在0-20，可输入整数和小数，小数部分最多保留2位", "信息保存操作失败了!", "error");
			return false;
		}
		
	}
    $.ajax({
             type: "POST",
             url: WEBPATH+"/saveAjpfPersonalBean.do",
             data:{fileId:fileId,perConsiderScore:perScoreVal},
             async:false,
             success: function(data){
	             if(data.result=="error"){
	               	swal({title: data.message ,text: "",type:"error"});
	                return false;
	             }else if(data.result=="success"){
	              	swal({title: data.message ,text: "",type:"success"});
	              	business.addMainContentParserHtml(WEBPATH+'/gerenList.do?pageNum='+pageNum+'&areaType='+areaTypeVal+'&isConsider='+isConsiderVal+'&personalMaterialName='+personalMaterialName,null);
	             }
           }
     });
	
	 
};
// 取消合议
function cancelHeYiGeren(coordinate){
	$("#heyi"+coordinate).show();
	$("#baocun"+coordinate).hide();
	$("#quxiao"+coordinate).hide();
	var perConsiderScore = $("#perConsiderScore"+coordinate);
	if(perConsiderScore.length >0)
	{
		perConsiderScore.val("");
		perConsiderScore.attr("readonly","readonly");
	}
};

// 查询专家和交叉打分情况  coordinate 下标   type 第几位专家 expertOrCross 专家还是交叉
function selectExpertScore(coordinate,type){
 	var pageNum = getPageNum();
 	
	var fileId = $("#id"+coordinate).val();				      
	var fileCode  = $("#personalMaterialName"+coordinate).val();
	var expertId;
 
	if(type=='1'){
		expertId =  $("#crossreviewaid"+coordinate).val();
	}
	if(type =='2'){
		expertId =  $("#crossreviewbid"+coordinate).val();
	}
 
	// 非空判断    交叉的id为空
	if(expertId === undefined || $.trim(expertId)=="")
	{
		swal("信息操作错误！", "信息保存操作失败了!", "error");
		return false;
	}		 
	if(fileCode === undefined || $.trim(fileCode)=="")
	{
		swal("案卷Code为空", "信息保存操作失败了!", "error");
		return false;
	}	 
 	   
    business.addMainContentParserHtml(WEBPATH+'/getCrossPersonalFileBean.do?fileId='+fileId+'&expertId='+expertId+'&fileCodeCurrent='+fileCode+'&pageNum='+pageNum,$("#searchForm").serialize());
}
// 获取回显当前页
function getPageNum(){
	var pageNum = eval('${ajpfPersonalList.pageNum}');
	if(pageNum === undefined ){
			pageNum = '';
	}
	return pageNum;
}

$(document).ready(function() {
	$(".topnav").accordion({
		accordion:false,
		speed: 500,
		closedSign: '[+]',
		openedSign: '[-]'
	});
});

</script>
</body>
</html>
