<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>

<html>
<head>
</head>

<body>
<div class="center_weizhi">当前位置：案卷总分 - 案卷评分结果 - 案卷评分结果</div>
<div class="center">
<div class="center_list">
    <div class="panel-group" id="accordion">
          <!---快速查询--->
          <form  id= "searchForm" role="form"  >
          	<input type="hidden" id ="fileCodeCurrent" name="fileCodeCurrent">
            <!---搜索--->
         <%--    <select class="form-control" style="width:150px;margin-right:5px;" name="isConsider" id="isConsiderSelect">
                 <option value="">请选择评审状态</option>
                 <option value="0"  <c:if test="${isConsider=='0' }">selected</c:if> >合议</option>
                 <option value="1" <c:if test="${isConsider=='1' }">selected</c:if>  >不合议</option>
              </select>  --%>
              <select class="form-control" style="width:150px;margin-right:5px;" name="areaCodeLeave" id="areaCodeLeave">
                 <option value="">请选择行政区</option>
                 <option value="2"  <c:if test="${areaCodeLeave=='2' }">selected</c:if> >省级</option>
                 <option value="3" <c:if test="${areaCodeLeave=='3' }">selected</c:if>  >地市级</option>
                 <option value="4" <c:if test="${areaCodeLeave=='4' }">selected</c:if>  >区县级</option>
              </select>
<%--              <select class="form-control" style="width:150px;margin-right:5px;" id="isInCheck" name ="isInCheck">--%>
<%--              	<option value ="">请选择评查状态</option>--%>
<%--                 <option value ="1" <c:if test="${isInCheck=='1' }">selected</c:if> >无需评查</option>--%>
<%--              </select>--%>

             <div style="width:260px;" class="btn-group">
                  <div class="row">
                     <div class="col-lg-6">
                        <div class="input-group">
                           <input id="fileCode" name="fileCode" type="text" class="form-control" style="width:200px;" value="${fileCode}" placeholder="决定书文号关键字">
                           <span class="input-group-btn">
                              <button id="searchButt" class="btn btn-success" type="button">
                           		      快速搜索
                              </button>
                             <button type="button" class="btn btn-primary" id ="viewExcel" >导出EXCEL</button>
                           </span>

                        </div><!-- /input-group -->
                     </div><!-- /.col-lg-6 -->
                  </div><!-- /.row -->
              </div>
            </form>
    </div>
        <table class="table table-bordered table-hover table-condensed">
         <thead>
           <tr>
             <td width="50" height="30" bgcolor="#efefef">序号</td>
               <td width="80" bgcolor="#efefef">省</td>
               <td width="80" bgcolor="#efefef">市</td>
               <td width="50" bgcolor="#efefef">县</td>
             <td width="120" bgcolor="#efefef">决定书文号</td>
             <td width="120" bgcolor="#efefef">案卷类型</td>
             <td width="70" bgcolor="#efefef">专家A姓名</td>
             <td width="70" bgcolor="#efefef">初评得分</td>
             <td width="70" bgcolor="#efefef">专家B姓名</td>
             <td width="70" bgcolor="#efefef">初评得分</td>
               <td width="100" bgcolor="#efefef">严重不全情况</td>
               <td width="100" bgcolor="#efefef">严重不全理由</td>
               <td width="100" bgcolor="#efefef">二轮评查内容</td>
             <td width="70" bgcolor="#efefef">委员A姓名</td>
             <td width="70" bgcolor="#efefef">复评得分</td>
             <td width="70" bgcolor="#efefef">委员B姓名</td>
             <td width="70" bgcolor="#efefef">复评得分</td>


             <td width="70" bgcolor="#efefef">最终得分</td>

             <!-- <td width="100" bgcolor="#efefef">交叉骨干A姓名</td>
             <td width="70" bgcolor="#efefef">初评得分</td>
             <td width="100" bgcolor="#efefef">交叉骨干B姓名</td>
             <td width="70" bgcolor="#efefef">初评得分</td>
             <td width="110" bgcolor="#efefef">交叉合议评审得分</td> -->
<%--             <td width="80" bgcolor="#efefef">评审得分</td>--%>
            <!--  <td width="90" bgcolor="#efefef">操作</td> -->
           </tr>
         </thead>
         <tbody>

          <c:forEach items="${ajpfBeanList.list}" var="item" varStatus="status">
		      <tr>
			     <td <c:if test="${item.isConsider=='1'}"> bgcolor='#D6F1F1' </c:if> height="30" align="center">${status.index+1 }
				     <input id="id${status.index+1}" type="hidden"  value="${item.id}">

				     <input id="filetype${status.index+1}" type="hidden"  value="${item.filetype}">
				     <input id="fileCode${status.index+1}" type="hidden"  value="${item.fileCode}">
				     <input id="expertAId${status.index+1}" type="hidden"  value="${item.expertaid}">
				     <input id="expertBId${status.index+1}" type="hidden"  value="${item.expertbid}">
				     <input id="commitAId${status.index+1}" type="hidden"  value="${item.expertCommitAId}">
				     <input id="commitBId${status.index+1}" type="hidden"  value="${item.expertCommitBId}">
				     <input id="crossReviewAId${status.index+1}" type="hidden"  value="${item.crossreviewaid}">
				     <input id="crossReviewBId${status.index+1}" type="hidden"  value="${item.crossreviewbid}">
			     </td>
                  <td <c:if test="${item.isConsider=='1'}"> bgcolor='#D6F1F1' </c:if>>${item.areaProvince}</td>
                  <td <c:if test="${item.isConsider=='1'}"> bgcolor='#D6F1F1' </c:if>>${item.areaCity}</td>
                  <td <c:if test="${item.isConsider=='1'}"> bgcolor='#D6F1F1' </c:if>>${item.areaCounty}</td>
		 	    <td <c:if test="${item.isConsider=='1'}"> bgcolor='#D6F1F1' </c:if>>${item.fileCode}</td>
			     <c:if test ="${item.filetype==0 }">
			       <td id ='filetype' <c:if test="${item.isConsider=='1'}"> bgcolor='#D6F1F1' </c:if>> 行政处罚案卷 </td>
			     </c:if>
			      <c:if test ="${item.filetype==1 }">
			       <td id ='filetype' <c:if test="${item.isConsider=='1'}"> bgcolor='#D6F1F1' </c:if>> 按日计罚案卷 </td>
			     </c:if>
			      <c:if test ="${item.filetype==2 }">
			       <td id ='filetype' <c:if test="${item.isConsider=='1'}"> bgcolor='#D6F1F1' </c:if>> 移送行政拘留案卷 </td>
			     </c:if>
			     <c:if test ="${item.filetype==3 }">
			       <td id ='filetype' <c:if test="${item.isConsider=='1'}"> bgcolor='#D6F1F1' </c:if>> 涉嫌环境污染犯罪 </td>
			     </c:if>
			     <c:if test ="${item.filetype==5 }">
			       <td id ='filetype' <c:if test="${item.isConsider=='1'}"> bgcolor='#D6F1F1' </c:if>> 监督检查稽查案卷 </td>
			     </c:if>
			      <c:if test ="${item.filetype==6 }">
			       <td id ='filetype' <c:if test="${item.isConsider=='1'}"> bgcolor='#D6F1F1' </c:if>> 查封扣押案卷 </td>
			     </c:if>
			      <c:if test ="${item.filetype==7 }">
			       <td id ='filetype' <c:if test="${item.isConsider=='1'}"> bgcolor='#D6F1F1' </c:if>> 限产停产案卷 </td>
			     </c:if>
                  <c:if test ="${item.filetype==9 }">
                      <td id ='filetype' <c:if test="${item.isConsider=='1'}"> bgcolor='#D6F1F1' </c:if>> 不予处罚案卷 </td>
                  </c:if>
	             <!-- 专家人员a计算 expertAId, expertBId, crossReviewAId, crossReviewBId,fileType  -->
	             <td <c:if test="${item.isConsider=='1'}"> bgcolor='#D6F1F1' </c:if>>${item.expertAName}</td>
	             <td <c:if test="${item.isConsider=='1'}"> bgcolor='#D6F1F1' </c:if>>
                     <a href="javascript:void(0);" onclick="checkExpertFile('${item.expertaid}','${item.id}','')">${item.expertAScore}</a>
	             </td>
	             <!-- 专家b -->
	             <td <c:if test="${item.isConsider=='1'}"> bgcolor='#D6F1F1' </c:if>>${item.expertBName}</td>
	             <td <c:if test="${item.isConsider=='1'}"> bgcolor='#D6F1F1' </c:if>>
                     <a href="javascript:void(0);" onclick="checkExpertFile('${item.expertbid}','${item.id}','')">${item.expertBScore}</a>
	             </td>
<%--                  <c:if test="${type==2 || type ==3}">--%>
                  <td>
                      <c:if test="${item.areaName=='1' && item.areaCode=='1'}"> 严重不全 </c:if>
                      <c:if test="${item.areaName=='0' && item.areaCode=='0'}"> 正常案卷 </c:if>
                      <c:if test="${item.areaName + item.areaCode == 1 }"> 疑似严重不全</c:if>
<%--                          ${item.areaName},${item.areaCode}--%>
                  </td>
                  <td >
<%--                      <c:if test="${item.areaName=='1' && item.areaCode=='1' }">  </c:if>--%>
                      <c:if test="${item.areaName=='1' && item.areaCode=='1'}"> 专家A：${item.province},专家B：${item.city}  </c:if>
                      <c:if test="${item.areaName=='0' && item.areaCode=='1'}"> 专家A：${item.province} </c:if>
                      <c:if test="${item.areaName=='1' && item.areaCode=='0'}"> 专家B：${item.city}  </c:if>
<%--                      <c:if test="${item.areaName=='0' && item.areaCode=='1' }">  ${item.province},专家B：${item.city} </c:if>--%>
<%--                      <c:if test="${item.areaName=='1' && item.areaCode=='1' }">  专家A：${item.province},${item.city} </c:if>--%>
<%--    专家A：${item.province},专家B：${item.city}--%>
                  </td>
                  <td>
                      <c:choose>
                          <c:when test="${ item.caseCheckState == '1' }">
                              只评卷面
                          </c:when>
                          <c:when test="${item.caseCheckState == '2' }">
                              只评实体
                          </c:when>
                          <c:when test="${item.caseCheckState == '3' }">
                              卷面实体都评
                          </c:when>

                          <c:otherwise>

                          </c:otherwise>
                      </c:choose>
                  </td>

	             <!-- 委员a -->
	             <td <c:if test="${item.isConsider=='1'}"> bgcolor='#D6F1F1' </c:if>>${item.expertCommitAName}</td>
	             <td <c:if test="${item.isConsider=='1'}"> bgcolor='#D6F1F1' </c:if>>
                     <a href="javascript:void(0);" onclick="checkExpertFile('${item.expertCommitAId}','${item.id}','')">${item.expertCommitAScore}</a>
	             </td>
	             <!-- 委员b -->
	             <td <c:if test="${item.isConsider=='1'}"> bgcolor='#D6F1F1' </c:if>>${item.expertCommitBName}</td>
	             <td <c:if test="${item.isConsider=='1'}"> bgcolor='#D6F1F1' </c:if>>
                     <a href="javascript:void(0);" onclick="checkExpertFile('${item.expertCommitBId}','${item.id}','')">${item.expertCommitBScore}</a>
	             </td>



<%--	             <td width="92" bgcolor='#D6F1F1'> ${item.expertConsiderScore} </td>--%>
<%--	             <td width="92" bgcolor='#D6F1F1'> ${item.expertconsiderscore2} </td>--%>
	             <!-- 专家人员计算  结束-->
	             <!-- 交叉人员计算 -->
                <%-- <td <c:if test="${item.isConsider=='1'}"> bgcolor='#D6F1F1' </c:if>>${item.crossReviewNameA}</td>
	             <td <c:if test="${item.isConsider=='1'}"> bgcolor='#D6F1F1' </c:if>>
	             	<a href="javascript:void(0);" onclick="selectExpertScore('${status.index+1 }',1,2)">${item.crossReviewAScore}</a>
	             </td>
	             <td <c:if test="${item.isConsider=='1'}"> bgcolor='#D6F1F1' </c:if>>${item.crossReviewNameB}</td>
	             <td <c:if test="${item.isConsider=='1'}"> bgcolor='#D6F1F1' </c:if>>
	             	<a href="javascript:void(0);" onclick="selectExpertScore('${status.index+1 }',2,2)">${item.crossReviewBScore}</a>
	             </td>
	             <td width="92" bgcolor='#D6F1F1'>${item.crossConsiderScore}</td> --%>
	             <!-- 交叉人员计算  结束-->
                <td <c:if test="${item.isConsider=='1'}"> bgcolor='#D6F1F1' </c:if>>${item.expertConsiderScore}</td>
		      </tr>
    </c:forEach>
         </tbody>
       </table>
    </div>
    <!--列表翻页 开始-->
    <div class="page">
    	<span style="padding:12px;  float:left; color:#0099cc;">共${ajpfBeanList.total}条记录</span>
        <ul class="pagination" id="pageCon">

        </ul>
    </div>
    <!--列表翻页 结束-->
</div>

<script language="JavaScript">
// 搜索条件
$(document).ready(function(){
	//监听enter
	business.listenEnter("searchButt");

	$("#searchButt").click(function(){
		business.addMainContentParserHtml("anjuanList.do?pageIndex=2",$("#searchForm").serialize());
	});
	$("#isInCheck").change(function(){
		business.addMainContentParserHtml("anjuanList.do?pageIndex=2",$("#searchForm").serialize());
	})
	$("#viewExcel").click(function(){
		var fileCode = $("#fileCode").val();
	//	var isConsider = $("#isConsiderSelect").val();
		var areaCodeLeave = $("#areaCodeLeave").val();
		var isInCheck =$("#isInCheck").val();
	  // var path = WEBPATH+"/caseListExportExcel.do?target=1&viewType=1&fileCode="+fileCode+"&isConsider=&areaCodeLeave="+areaCodeLeave+"&isInCheck="+isInCheck;
	  var path = WEBPATH+"/caseListExportExcel.do?target=1&viewType=1&fileCode="+fileCode+"&isConsider=&areaCodeLeave="+areaCodeLeave;
      window.location.href=path;
	});
});
//分页
$(document).ready(function(){
	var curentPage = eval('${ajpfBeanList.pageNum}');
	var totalPage = eval('${ajpfBeanList.pages}');
	if(totalPage>0){
		var options = {
			bootstrapMajorVersion: 3,
		    currentPage: curentPage,
		    totalPages: totalPage,
		    numberOfPages: 5,
		    itemTexts: function (type, page, current) {
		            switch (type) {
		            case "first":
		                return "首页";
		            case "prev":
		                return "&laquo;";
		            case "next":
		                return "&raquo;";
		            case "last":
		                return "尾页";
		            case "page":
		                return page;
		            }
		    },
		    onPageClicked: function (event, originalEvent, type, page) {
            	business.addMainContentParserHtml(WEBPATH+'/anjuanList.do?pageIndex=2&pageNum='+page,$("#searchForm").serialize());
            }
    	};
    	$('#pageCon').bootstrapPaginator(options);
   	}
});


function checkExpertFile(expertID,fileID,type){

    if(expertID==null||expertID == ''){
        return ;
    }
    $.ajax({
        data:{expertID:expertID,fileID:fileID},
        dataType:"JSON",
        Type:"post",
        url:WEBPATH+'/zjpf/getExpertHanderID.do',
        success:function(data){

            if(data.id !=null && data.id!=''){
                business.addMainContentParserHtml(WEBPATH+'/zjpf/zjpfScoreView.do?consider='+type+'&status=1&id='+data.id+'&pageIndex=${pageIndex}&pageNum=${pageNum}&gopath=anjuanList',$("#searchForm").serialize());
            }else{
                swal("操作失败!", data.text, "error");
            }
        }
    });
}

</script>
</body>
</html>
