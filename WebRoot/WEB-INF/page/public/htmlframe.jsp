<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<title>生态环境保护执法大练兵评审系统</title>
<meta name="renderer" content="webkit">
<meta http-equiv="pragma" content="no-cache">
<meta http-equiv="cache-control" content="no-cache">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0" />
</head>

<!-- basic js -->
<script type="text/javascript">
		      var WEBPATH='${webpath}';
		</script>
<link rel="shortcut icon" href="${webpath }/static/images/favicon.ico" />
<!--  模型样式 -->
<link rel="stylesheet" type="text/css"
	href="${webpath }/static/easyui/easyui.css" />
<link rel="stylesheet" type="text/css"
	href="${webpath }/static/easyui/icon.css" />

<link href="${webpath }/static/css/bootstrap.min.css" rel="stylesheet"
	type="text/css" />
<link href="${webpath }/static/css/style.css" rel="stylesheet"
	type="text/css" />
<!-- 遮罩层 -->
<link href="${webpath }/static/css/loding.css" rel="stylesheet">

<link href="${webpath }/static/css/sweetalert.css" rel="stylesheet"
	type="text/css" />
<link href="${webpath }/static/css/frame.css" rel="stylesheet"
	type="text/css">
<link href="${webpath }/static/css/menu.css" rel="stylesheet"
	type="text/css">
<link href="${webpath }/static/css/jquery.pwstabs.min.css"
	rel="stylesheet" type="text/css" />
<%--<link--%>
	<%--href="http://netdna.bootstrapcdn.com/font-awesome/4.1.0/css/font-awesome.min.css"--%>
	<%--rel="stylesheet">--%>
<link href="${webpath }/static/css/jquery-ui.min.css" rel="stylesheet"
	type="text/css" />
<link href="${webpath }/static/css/fileinput.min.css" rel="stylesheet"
	type="text/css" />
<link href="${webpath }/static/css/bootstrap-multiselect.css"
	rel="stylesheet" type="text/css" />
<%-- <link href="${webpath }/static/css/example.css" rel="stylesheet"  type="text/css"/> --%>
<link href="${webpath }/static/css/select2.css" rel="stylesheet"
	type="text/css" />
<link href="${webpath }/static/css/select2-bootstrap.css"
	rel="stylesheet" type="text/css" />
<link href="${webpath }/static/css/datetimepicker.css" rel="stylesheet"
	type="text/css">
<link rel="stylesheet" type="text/css" media="all"
	href="${webpath }/static/css/daterangepicker.css" />
<%-- <link rel="stylesheet" type="text/css" href="${webpath }/static/css/bootstrapSwitch.css" /> --%>

<script type="text/javascript"
	src="${webpath }/static/js/jquery.min.js"></script>

<script type="text/javascript"
	src="${webpath }/static/js/bootstrap.min.js"></script>
<script type="text/javascript"
	src="${webpath }/static/js/formValidation.js"></script>
<script type="text/javascript"
	src="${webpath }/static/js/framework/bootstrap.js"></script>
<script type="text/javascript"
	src="${webpath }/static/js/jquery.form.js"></script>
<script type="text/javascript"
	src="${webpath }/static/js/language/zh_CN.js"></script>
<script type="text/javascript"
	src="${webpath }/static/js/jquery-ui.min.js"></script>
<script type="text/javascript"
	src="${webpath }/static/js/fileinput.min.js"></script>
<script type="text/javascript"
	src="${webpath }/static/js/bootstrap-multiselect.js"></script>
<script type="text/javascript"
	src="${webpath }/static/js/typeahead.bundle.js"></script>
<script type="text/javascript" src="${webpath }/static/js/handlebars.js"></script>
<script type="text/javascript"
	src="${webpath }/static/js/select2.full.js"></script>
<script type="text/javascript"
	src="${webpath }/static/js/locales/select2/zh-CN.js"></script>
<script type="text/javascript"
	src="${webpath }/static/js/locales/bootstrap-fileinput/zh.js"></script>

<script type="text/javascript"
	src="${webpath }/static/js/scriptbreaker-multiple-accordion-1.js"></script>
<script type="text/javascript" src="${webpath }/static/js/data.js"></script>

<script type="text/javascript"
	src="${webpath }/static/js/framework/bootstrap-paginator.js"></script>
<script type="text/javascript"
	src="${webpath }/static/js/sweetalert.min.js"></script>

<script type="text/javascript"
	src="${webpath}/static/easyui/jquery.easyui.min.js"></script>
<script type="text/javascript"
	src="${webpath}/static/easyui/easyui-lang-zh_CN.js"></script>
<script type="text/javascript"
	src="${webpath }/static/js/jquery.pwstabs-1.1.3.min.js"></script>

<script type="text/javascript"
	src="${webpath }/static/js/bootstrap-datetimepicker.js"></script>
	<!-- highcharts引用文件 -->
<%-- <script type="text/javascript" src="${webpath}/static/js/highstock.js"></script> --%>
<!-- 业务逻辑相关js -->
<script type="text/javascript"
	src="${webpath}/static/common/js/macro.js"></script>
<script type="text/javascript"
	src="${webpath}/static/common/js/business.js"></script>
<script type="text/javascript"
	src="${webpath}/static/common/js/message.js"></script>
<script type="text/javascript"
	src="${webpath}/static/common/js/phone.js"></script>

<!-- 引入vue -->
<script type="text/javascript" src="${webpath}/static/vue/vue.min.js"></script>
<%-- <script type="text/javascript" src="${webpath}/static/js/vue-validator.common.js"></script> --%>
<script type="text/javascript"
	src="${webpath}/static/js/vue-validator.min.js"></script>

<!-- 引入pdfObject -->
<script type="text/javascript"
	src="${webpath}/static/PDFObject/pdfobject.min.js"></script>
<script type="text/javascript" src="${webpath}/static/js/moment.js"></script>

<script type="text/javascript" src="${webpath}/static/js/daterangepicker.js"></script>

<%--引入pdfjs--%>
<%--<script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>--%>
<script type="text/javascript" src="${webpath}/static/pdfjs/build/pdf.js"></script>

<%--引入markdown-it.js库--%>
<%--<script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>--%>
<script src="${webpath}/static/marked/marked.min.js"></script>

<%--引入layUI--%>
<link rel="stylesheet" href="${webpath }/static/layui/css/layui.css" >
<script type="text/javascript" src="${webpath}/static/layui/layui.js"></script>

<!--引入结束-->
<script type="text/javascript">
	var isIEWhether=false;
	$(document).ready(function(){
		isIEWhether=isIE();
	})

	function isIE() { //ie?
		 if (!!window.ActiveXObject || "ActiveXObject" in window){
			 return true;
		 }else{
			 return false;
		 }
	}

</script>
<script type="text/javascript">
 /*=======================初始化=================================================*/
   $(function(){
	   //disabledF5();

   });
   $(document).ready(function() {
	   history.pushState(null, null, document.URL);
	   window.addEventListener('popstate', function () {
	       history.pushState(null, null, document.URL);
	   });
	});
   function disabledF5(){
	    document.onkeydown = function (e) {
		    var ev = window.event || e;
		    var code = ev.keyCode || ev.which;
		   	if (code == 116) {
			   ev.keyCode ? ev.keyCode = 0 : ev.which = 0;
			   cancelBubble = true;
			   return false;
			}
		}; //禁止f5刷新
   }
   //修改密码
   function resetPass(){
     $("#sysManage").click();
   }

	//使用帮助
	function useHelp(){
		//var path = "${webpath}/downUseHelp.do";
		//window.location.href = path;
		macroMgr.onLevelOneMenuClick(null, 'help.do');
	}
</script>
</head>

<body>

	<!--框架头 开始-->
	<div class="frame_top">
		<div class="top_logo"></div>
		<div class="top_nav">
			<ul id="headerNav">
<%--				<li>--%>
<%--					<a href="javascript:void(0);" onclick="macroMgr.onLevelOneMenuClick(null, 'Aipf.do')">--%>
<%--						<img src="${webpath}/static/image2024/zjpf.png" /><br />AI评分--%>
<%--					</a>--%>
<%--				</li>--%>
<%--				<li><a href="#"--%>
<%--					onclick="macroMgr.onLevelOneMenuClick(null, 'newhome.do')"><img--%>
<%--						src="${webpath}/static/images/p1.png" /><br />系统首页</a></li>--%>
				<%--<li>
					<a href="#" onclick="macroMgr.onLevelOneMenuClick(null, 'filesDrawActivity/extractMethod.do')">
						<img src="${webpath}/static/images/p6.png" /><br />第二批次抽取
					</a>
				</li>--%>
					<c:if test="${sessionScope.sa_session.userTypeCode == '1'}">
						<li>
							<a href="#" onclick="macroMgr.onLevelOneMenuClick(null, 'filesRecheck/declareRecheckList.do')">
								<img src="${webpath}/static/image2024/ajfh.png" /><br /><%--省内评比--%>案卷复核
							</a>
						</li>
					</c:if>
				<!-- 省级初始化阶段隐藏信息采集菜单 -->
				<c:if test="${sessionScope.sa_session.sysStatus != '6'}">

					<%--<c:if test="${sessionScope.sa_session.userTypeCode == '1'}">--%>
						<%--<li>--%>
							<%--<a href="#" onclick="macroMgr.onLevelOneMenuClick(null, 'xxcj.do')">--%>
								<%--<img src="${webpath}/static/images/p2.png" /><br />信息采集--%>
							<%--</a>--%>
						<%--</li>--%>
					<%--</c:if>--%>
					<%--<c:if test="${sessionScope.sa_session.userTypeCode == '5'}">--%>
						<%--<li>--%>
							<%--<a href="#" onclick="macroMgr.onLevelOneMenuClick(null, 'xxcj.do')">--%>
								<%--<img src="${webpath}/static/images/caiji_d.png" /><br />信息采集--%>
							<%--</a>--%>
						<%--</li>--%>
					<%--</c:if>--%>


					<c:if test="${sessionScope.sa_session.loginid == 'changneng'||sessionScope.sa_session.loginid == 'sysAdmin'}">
					<li>
							<%--							<a href="#" onclick="macroMgr.onLevelOneMenuClick(null, 'goAjcq.do')">--%>
						<a href="#" onclick="macroMgr.onLevelOneMenuClick(null, 'filesDrawActivity/goActivityList.do')">
							<img src="${webpath}/static/image2024/ajcq2.png" /><br /><%--省内评比--%>案卷抽取
						</a>
					</li>
					</c:if>
					<c:if test="${sessionScope.sa_session.arealevel == '1' || sessionScope.sa_session.loginid == 'changneng'||sessionScope.sa_session.loginid == 'sysAdmin'
					 ||sessionScope.sa_session.loginid == 'wanli' ||sessionScope.sa_session.loginid == 'maruicong' ||sessionScope.sa_session.loginid == 'huozhijian'
					||sessionScope.sa_session.loginid == 'expAdmin'||sessionScope.sa_session.loginid == 'testadmin'||sessionScope.sa_session.loginid == 'guhaiyun' ||sessionScope.sa_session.loginid == 'sunchanghao'
					||sessionScope.sa_session.loginid == 'yemao'||sessionScope.sa_session.loginid == 'lijiaxiang'||sessionScope.sa_session.loginid == 'caoxiaofan'||sessionScope.sa_session.loginid == 'wurongliang' ||sessionScope.sa_session.loginid == 'wenlizhao'
					||sessionScope.sa_session.loginid == 'zhoujun'||sessionScope.sa_session.loginid == 'xuzhili'||sessionScope.sa_session.loginid == 'humin'||sessionScope.sa_session.loginid == 'jiaorubin' ||sessionScope.sa_session.loginid == 'zhaochiming'
					||sessionScope.sa_session.loginid == 'zoukun'||sessionScope.sa_session.loginid == 'wangtonglin'||sessionScope.sa_session.loginid == 'tangruili'||sessionScope.sa_session.loginid == 'dingyanlin'||sessionScope.sa_session.loginid == 'songhaiou'
					||sessionScope.sa_session.loginid == 'admin1'||sessionScope.sa_session.loginid == 'admin2'||sessionScope.sa_session.loginid == 'admin3'}">



<%--						<li>--%>
<%--								&lt;%&ndash;							<a href="#" onclick="macroMgr.onLevelOneMenuClick(null, 'goAjcq.do')">&ndash;%&gt;--%>
<%--							<a href="#" onclick="macroMgr.onLevelOneMenuClick(null, 'filesRecheck/declareRecheckList.do')">--%>
<%--								<img src="${webpath}/static/image2024/ajfh.png" /><br />&lt;%&ndash;省内评比&ndash;%&gt;案卷复核--%>
<%--							</a>--%>
<%--						</li>--%>

						<%--<li>
							<a href="#" onclick="macroMgr.onLevelOneMenuClick(null, 'goProvinceScore.do')">
								<img src="${webpath}/static/images/p3.png" /><br />&lt;%&ndash;省内评比&ndash;%&gt;推荐报送
							</a>
						</li>--%>
					</c:if>
				</c:if>

				<c:if test="${sessionScope.sa_session.userTypeCode == '2'  && sessionScope.sa_session.level == '0' }">
					<li>
						<a href="javascript:void(0);" onclick="macroMgr.onLevelOneMenuClick(null, 'zjpf.do')">
							<img src="${webpath}/static/image2024/zjpf.png" /><br />专家评分
						</a>
					</li>

				</c:if>
				<c:if test="${sessionScope.sa_session.userTypeCode == '2'  && sessionScope.sa_session.level == '1' }">
					<li>
						<a href="javascript:void(0);" onclick="macroMgr.onLevelOneMenuClick(null, 'zjpf.do')">
							<img src="${webpath}/static/image2024/zjpf.png" /><br />专家委员评分
						</a>
					</li>
				</c:if>
				<%--<c:if test="${sessionScope.sa_session.userTypeCode == '2'  && sessionScope.sa_session.level == '2' }">
					<li>
						<a href="javascript:void(0);" onclick="macroMgr.onLevelOneMenuClick(null, 'zjpf.do')">
							<img src="${webpath}/static/image2024/zjpf.png" /><br />首席专家评分
						</a>
					</li>


				</c:if>--%>
<%--				<li><a id="sysManage" href="#" onclick="macroMgr.onLevelOneMenuClick(null, 'zjk/goMain.do')"><img src="${webpath}/static/image2024/zjpf.png"><br>专家库</a></li>--%>
				<c:if test="${sessionScope.sa_session.userTypeCode == '2'  && sessionScope.sa_session.level == '6' }">
					<li>
						<a href="javascript:void(0);" onclick="macroMgr.onLevelOneMenuClick(null, 'zhpg/main.do')">
							<img src="${webpath}/static/img/zhpg.png" /><br />综合评估
						</a>
					</li>
				</c:if>

				<%-- <c:if test="${sessionScope.sa_session.userTypeCode == '3'}">
					<c:choose>
						<c:when test="${sessionScope.sa_session.loginid == 'syndic1' || sessionScope.sa_session.loginid == 'syndic2' || sessionScope.sa_session.loginid == 'syndic3' || sessionScope.sa_session.loginid == 'syndic4'}">
							<li>
								<a href="javascript:void(0);" onclick="macroMgr.onLevelOneMenuClick(null, 'zhpg/main.do')">
									<img src="${webpath}/static/img/zhpg.png" /><br />综合评估
								</a>
							</li>
						</c:when>
						<c:otherwise>
							<li>
								<a href="#" onclick="macroMgr.onLevelOneMenuClick(null, 'jcpf.do')">
									<img src="${webpath}/static/img/jiti_d.png" /><br />交叉评分
								</a>
							</li>
						</c:otherwise>
					</c:choose>
				</c:if> --%>

				<c:if test="${sessionScope.sa_session.userTypeCode == '4'}">

					<c:if test="${fn:startsWith(sessionScope.sa_session.loginid, 'crossAdmin')|| sessionScope.sa_session.loginid == 'expAdmin' }">
						<li>
							<a href="javascript:void(0);" onclick="macroMgr.onLevelOneMenuClick(null, 'zlpf.do')">
								<img src="${webpath}/static/img/anjuan_d.png" /><br />案卷总分
							</a>
						</li>
					</c:if>
<%--					<c:if test="${!(fn:startsWith(sessionScope.sa_session.loginid, 'crossAdmin')) || sessionScope.sa_session.loginid =='expAdmin'}">--%>
						<c:if test="${(sessionScope.sa_session.loginid == 'changneng'||sessionScope.sa_session.loginid == 'sysAdmin') && (sessionScope.sa_session.sysStatus != '1')}">
							<c:if test="${sessionScope.sa_session.loginid == 'changneng'||sessionScope.sa_session.loginid == 'sysAdmin'}">
								<li>
									<a href="javascript:void(0);" onclick="macroMgr.onLevelOneMenuClick(null, 'zlpf.do')">
										<img src="${webpath}/static/image2024/ajpf.png" /><br />案卷评分
									</a>
								</li>

								<li>
									<a href="javascript:void(0);" onclick="macroMgr.onLevelOneMenuClick(null, 'zjkp.do')">
										<img src="${webpath}/static/image2024/zjkp.png" /><br />专家考评
									</a>
								</li>

								<li>
									<a href="javascript:void(0);" onclick="macroMgr.onLevelOneMenuClick(null, 'ajpf.do')">
										<img src="${webpath}/static/image2024/zldf.png" /><br />质量得分
									</a>
								</li>
								<%-- <li>
									<a href="javascript:void(0);" onclick="macroMgr.onLevelOneMenuClick(null, 'slpf.do')">
										<img src="${webpath}/static/img/shuliang_d.png" /><br />日常监督执法
									</a>
								</li>
								<li>
									<a href="javascript:void(0);" onclick="macroMgr.onLevelOneMenuClick(null, 'gztp.do')">
										<img src="${webpath}/static/img/toupiao_d.png" /><br />公众投票
									</a>
								</li>--%>
								<li>
									<a href="javascript:void(0);" onclick="macroMgr.onLevelOneMenuClick(null, 'pgfs/main.do')">
										<img src="${webpath}/static/image2024/tczzpf.png" /><br />突出组织评分
									</a>
								</li>
								<%--
								<li>
									<a href="javascript:void(0);" onclick="macroMgr.onLevelOneMenuClick(null, 'pxjg.do')">
										<img src="${webpath}/static/img/jieguo_d.png" /><br />评选结果
									</a>
								</li> --%>
							</c:if>
							<li>
								<a href="javascript:void(0);" onclick="macroMgr.onLevelOneMenuClick(null, 'xxhz.do')">
									<img src="${webpath}/static/image2024/qghz.png" /><br />全国汇总
								</a>
							</li>
						</c:if>
					</c:if>
					<c:if test="${fn:startsWith(sessionScope.sa_session.loginid, 'chufachu') || sessionScope.sa_session.loginid == 'sysAdmin1' || sessionScope.sa_session.loginid == 'sysAdmin2'
										|| sessionScope.sa_session.loginid == 'sysAdmin3'  || sessionScope.sa_session.loginid == 'sysAdmin4' || sessionScope.sa_session.loginid == 'sysAdmin5'}">
						<li>
							<a href="#" onclick="macroMgr.onLevelOneMenuClick(null, 'filesDrawActivity/goActivityList.do')">
								<img src="${webpath}/static/image2024/ajcq2.png" /><br />案卷抽取
							</a>
						</li>
						<li>
							<a href="#" onclick="macroMgr.onLevelOneMenuClick(null, 'filesRecheck/declareRecheckList.do')">
								<img src="${webpath}/static/image2024/ajfh.png" /><br /><%--省内评比--%>案卷复核
							</a>
						</li>
						<li>
							<a href="javascript:void(0);" onclick="macroMgr.onLevelOneMenuClick(null, 'zlpf.do')">
								<img src="${webpath}/static/img/anjuan_d.png" /><br />案卷总分
							</a>
						</li>
						<li>
							<a href="javascript:void(0);" onclick="macroMgr.onLevelOneMenuClick(null, 'zjkp.do')">
								<img src="${webpath}/static/image2024/zjkp.png" /><br />专家考评
							</a>
						</li>
					</c:if>
<%--				</c:if>--%>
				<li><a id="sysManage2" href="#"
					onclick="macroMgr.onLevelOneMenuClick(null, 'sysManage.do')"><img
						src="${webpath}/static/image2024/system.png" /><br />系统管理</a></li>
	            <li>
					<a href="loginOut.do">
						<img
						 src="${webpath}/static/image2024/exit.png" /><br />
						退出系统
				   </a>
				</li>
			</ul>
		</div>
		<div class="top_user">
<%--			<script language="JavaScript" type="text/javascript">--%>
<%--														//获取日期 2007-04-29 16:41--%>
<%--														var now = new Date(); //获取系统日期，即Sun Apr 29 16:41:48 UTC+0800 2007--%>
<%--														var yy = now.getFullYear(); //获取年，即2007--%>
<%--														var mm = now.getMonth() + 1; //获取月，即04--%>
<%--														var dd = now.getDate(); //获取天--%>
<%--														var day = now.getDay();//星期--%>
<%--														//document.write("今天是: ");--%>
<%--														document.write(yy+"年"+mm+"月"+dd+"日    ");--%>
<%--														document.write("星期"+weekString.charAt(day));--%>
<%--														document.write("农历"+GetLunarDay(yy,mm,dd-5));--%>
<%--														</script>--%>
			&nbsp;&nbsp;您好！<span style="color: red">${sessionScope.sa_session.userType}:${sessionScope.sa_session.userName}</span>&nbsp;&nbsp;欢迎登录系统。当前系统状态：
			<span id="sysBiaoZhi1" style="color: red"> <c:choose>

					<c:when test="${sessionScope.sa_session.sysStatus == '1'}">
						<span style="color: red">信息采集阶段</span>
					</c:when>
					<c:when test="${sessionScope.sa_session.sysStatus == '2'}">
						<span style="color: red">交叉评审阶段</span>
					</c:when>
					<c:when test="${sessionScope.sa_session.sysStatus == '3'}">
						<span style="color: red">系统汇总阶段</span>
					</c:when>
					<c:when test="${sessionScope.sa_session.sysStatus == '4'}">
						<span style="color: red">结果公示阶段</span>
					</c:when>
					<c:when test="${sessionScope.sa_session.sysStatus == '5'}">
						<span style="color: red">专家评审阶段</span>
					</c:when>
					<c:when test="${sessionScope.sa_session.sysStatus == '7'}">
						<span style="color: red">案卷复核阶段</span>
					</c:when>
					<c:otherwise>
						<span style="color: red">省级初始化参选信息阶段</span>
					</c:otherwise>
				</c:choose>
			</span>
<%--			<a href="#" onclick="resetPass()">[ 修改密码 ]</a>--%>
<%--			&nbsp <a href="#"--%>
<%--				onclick="useHelp()">[ 使用说明 ]</a> --%>
<%--			&nbsp <a href="loginOut.do">[--%>
<%--				退出系统 ]</a>&nbsp;--%>
		</div>
	</div>
	<!--框架头 结束-->

	<!--当前位置 开始-->
	<!--<div class="center_home_weizhi">当前位置：系统首页</div>-->
	<!--当前位置 结束-->



	<!-- 页面中部~中间 start  -->
	<div id="main_container" style="background: #f6fafe"></div>

	<!--框架底部版权 开始-->
	<div class="frame_bottom">&copy;技术支持：生态环境部环境工程评估中心&nbsp;&nbsp;&nbsp;&nbsp;</div>
	<!--框架底部版权 结束-->


	<!--加载提示框-->
	<div id="mask">
		<div class="spinner_load">
			<div class="spinner_load-container container1">
				<div class="circle1"></div>
				<div class="circle2"></div>
				<div class="circle3"></div>
				<div class="circle4"></div>
			</div>
			<div class="spinner_load-container container2">
				<div class="circle1"></div>
				<div class="circle2"></div>
				<div class="circle3"></div>
				<div class="circle4"></div>
			</div>
			<div class="spinner_load-container container3">
				<div class="circle1"></div>
				<div class="circle2"></div>
				<div class="circle3"></div>
				<div class="circle4"></div>
			</div>
		</div>
	</div>
	<!--加载提示框-->

</body>
</html>
