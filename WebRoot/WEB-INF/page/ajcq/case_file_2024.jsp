<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
    <title>案卷抽取</title>
</head>
<link rel="stylesheet" href="${webpath }/static/css/caiji.css">
<body>
<div class="center_home_weizhi">当前位置：案卷抽取  </div>
<div class="center-body">
    <div class="layui-panel layui-row" id="accordion" style="margin-top: 40px; padding: 15px">
        <form id= "searchForm"  role="form" >
            <input id="activityId" name="activityId"  type="hidden"  value="${activityId}" />
            <div class="layui-col-xs8 ">
                <label class="layui-form-label" style="width: 100px">行政区：</label>
                <input class="layui-input layui-input-inline" type="text" id="provinceName" name="provinceName" value="${provinceName}" placeholder="所属省" style="width: 30%">
<%--                <input class="layui-input layui-input-inline" type="text" id="cityName" name="cityName" value="${cityName}" placeholder="所属市" style="width: 30%">--%>
            </div>
        </form>
        <div class="layui-col-xs4">
            <button class="layui-btn layui-bg-blue" id="filterButton" onclick="filterButton()">筛选</button>
            <button class="layui-btn layui-btn-primary layui-border" id="resetButton" onclick="resetButton()">重置</button>
        </div>
    </div>
    <div class="layui-panel " style="margin-top: 10px; padding: 15px">
        <div class="layui-row">
            <div class="layui-col-xs6">
                <button onclick="backActivityInfo(${activityId})" class="layui-btn layui-btn-primary layui-border-blue">返回</button>
                <button class="layui-btn layui-bg-red" id ="viewExcel" >导出</button>
            </div>
            <div class="layui-col-xs6" style="text-align: right">
                <button onclick="goActivityList(${activityId})" class="layui-btn ">抽取明细</button>
            </div>
        </div>
        <div class="scroll-container">

            <table class="layui-table" id="fileStatistics">
                <thead>
                <tr >
                    <th style="text-align: center" colspan="2">行政区</th>
                    <th style="text-align: center" colspan="2">行政处罚</th>
                    <th style="text-align: center" colspan="2">配套</th>
                    <th style="text-align: center" colspan="2">不予处罚</th>
                    <th style="text-align: center" colspan="2">整体情况</th>
                </tr>
                <tr>
                    <th >省份</th>
                    <th >市级数量</th>
                    <th >实际抽取</th>
                    <th >抽取情况</th>
                    <th >实际抽取</th>
                    <th >抽取情况</th>
                    <th >实际抽取</th>
                    <th >抽取情况</th>
                    <th >实际抽取</th>
                    <th >抽取情况</th>
                </tr>
                </thead>
                <tbody>
                <c:forEach items="${dataList}" var="item" varStatus="status">
                    <tr>
                        <td>${item.provinceName}</td>
                        <td>${item.cityCount}</td>
                        <td>${item.cfCountAll}</td>
                        <td>
                            <c:choose>
                                <c:when test="${item.cfCountAll < item.cityCount*2-5-5}">
                                    <span style="color: orange;">不合格</span>
                                </c:when>
                                <c:otherwise>
                                    <span style="color: green;">合格</span>
                                </c:otherwise>
                            </c:choose>
                        </td>
                        <td>${item.ptCountAll}</td>
                        <td>
                            <c:choose>
                                <c:when test="${item.ptCountAll < 5}">
                                    <span style="color: orange;">不合格</span>
                                </c:when>
                                <c:otherwise>
                                    <span style="color: green;">合格</span>
                                </c:otherwise>
                            </c:choose></td>
                        <td>${item.notPunishableCountAll}</td>
                        <td>
                            <c:choose>
                                <c:when test="${item.notPunishableCountAll < 5}">
                                    <span style="color: orange;">不合格</span>
                                </c:when>
                                <c:otherwise>
                                    <span style="color: green;">合格</span>
                                </c:otherwise>
                            </c:choose></td>
                        <td>${item.allCount}</td>
                        <td>
                            <c:choose>
                                <c:when test="${item.allCount != item.cityCount*2}">
                                    <span style="color: orange;">不合格</span>
                                </c:when>
                                <c:otherwise>
                                    <span style="color: green;">合格</span>
                                </c:otherwise>
                            </c:choose>
                        </td>
                    </tr>
                </c:forEach>

                </tbody>
            </table>
        </div>

    </div>






</div>


</body>
<script>
    //返回按钮
    function backActivityInfo(id){
        macroMgr.onLevelOneMenuClick(null, 'filesDrawActivity/goAllFiles.do?activityId='+id)
    }
    //筛选按钮
    function filterButton() {
        business.addMainContaierHtml(WEBPATH+'/filesDrawActivity/goCaseFile2024.do',$("#searchForm").serialize());
    }
    //重置按钮
    function resetButton(){
        //所属省
        document.getElementById('provinceName').value = "";
        //所属市
        document.getElementById('cityName').value = "";
        business.addMainContaierHtml(WEBPATH+'/filesDrawActivity/goCaseFile2024.do',$("#searchForm").serialize());

    }

    // 明细页面
    function goActivityList(){
        var activityId = $("#activityId").val();
        console.log("跳转案卷,活动id:",activityId)
        macroMgr.onLevelOneMenuClick(null, 'filesDrawActivity/goCqList.do?activityId='+activityId)
    }


    $(document).ready(function(){
        //绑定主菜单单击方法，设置样式
        $(".ul-clearfix li").bind("click",function(){
            $(".ul-clearfix li a").removeClass("active");
            $(this).children(0).addClass("active");
        });
        //触发点击事件
        $(".ul-clearfix li a:first").click();

    });

    //跳转案卷汇总页面
    function goAllFiles(){
        var activityId = $("#activityId").val();
        macroMgr.onLevelOneMenuClick(null, 'filesDrawActivity/goAllFiles.do?activityId='+activityId)
    }

    //导出按钮
    $("#viewExcel").click(function(){
        var provinceName = $("#provinceName").val();
        var cityName = $("#cityName").val();
        var activityId = $("#activityId").val();

        var path = WEBPATH+"/filesDrawActivity/caseFileExport.do?provinceName="+provinceName+"&cityName="+cityName+"&activityId="+activityId;
        window.location.href=path;
    })

</script>

<style>
    .center-body {
        width: 75%;
        margin: 0 auto;
        overflow: hidden;
    }
    .head-item {
        width: 1200px;
        height: 45px;
        margin-top: 15px;
    }

    .head-item>ul>li {
        float: left;
    }

    .head-item>ul>li>a {
        display: block;
        margin-right: 15px;
        width: 158px;
        height: 45px;
        background: #3f91c2;
        text-align: center;
        line-height: 45px;
        box-shadow: 0 0 5px #999;
        overflow: hidden;
        border-radius: 8px;
        color: #ffffff;
        font-size: 16px;
    }
    .ul-clearfix {
        *zoom: 1;
        margin-left: 10px;
    }
    .scroll-container{
        height: 70%;
        width: 95%;/* 设置容器的高度 */
        overflow: auto; /* 自动显示滚动条 */
        margin-left: auto;
        margin-right: auto;
        margin-top: 5px;
    }
    /* 表头固定 */
    .scroll-container thead tr > th {
        position: sticky;
        top: 0;
        z-index: 2;
    }

    /* 首列固定 */
    /*.scroll-container thead tr > th:first-child,*/
    /*.scroll-container tbody tr > td:first-child {*/
    /*    position: sticky;*/
    /*    left: 0;*/
    /*    z-index: 1;*/
    /*}*/
    .table {
        margin-top: auto;
    }



</style>
</html>
