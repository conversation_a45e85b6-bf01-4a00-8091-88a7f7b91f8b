<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
    <title>案卷抽取</title>
</head>
<link rel="stylesheet" href="${webpath }/static/css/caiji.css">
<body>
<div class="center_home_weizhi">当前位置：案卷抽取  </div>
<div class="center-body">


    <div class="item" style="margin-top: 50px;">
        <ul class="clearfix">
            <li>
                <a href="javascript:void(0);" onclick="saveActivity('insert')">创建活动</a>
            </li>
        </ul>
    </div>
    <div class="scroll-container">
    <table class="table table-bordered table-hover table-condensed" id="activityTable">
        <thead>
            <tr>
                <td width="20" height="30" bgcolor="#efefef">序号</td>
                <td width="150" bgcolor="#efefef">活动名称</td>
                <td width="50" bgcolor="#efefef">案卷时间范围</td>
                <td width="50" bgcolor="#efefef">创建时间</td>
                <td width="50" bgcolor="#efefef">活动状态</td>
                <td width="240" bgcolor="#efefef">操作</td>
            </tr>
        </thead>
        <tbody>
        <c:forEach items="${dataList.list}" var="item" varStatus="status">
            <tr>
                <td align="center">${status.index+1 }</td>
                <td>${item.activityName}</td>
                <td align="center"><fmt:formatDate value="${item.startTime}" pattern="yyyy/MM/dd"/>~<fmt:formatDate value="${item.endTime}" pattern="yyyy/MM/dd"/></td>
                <td align="center"><fmt:formatDate value="${item.createTime}" pattern="yyyy/MM/dd"/></td>
                <td style="color: ${item.activityState == 0 ? '#ff9c00' : (item.activityState == 1 ? '#00b400' : 'inherit')}">
                        ${item.activityState == 0 ? "未抽取" : ""}${item.activityState == 1 ? "已抽取" : ""}
                </td>
                <td  >
                    <button onclick="extractCaseFile(${item.id})" class="btn btn-success btn-xs" style="margin: 0 10px">活动查看</button>
                    <button onclick="manageActivity(${item.id})" style="margin: 0 10px" class="btn btn-info btn-xs">案卷清单</button>
                    <c:if test="${item.isUseExpert == 0}">
                        <button onclick="subExpert(${item.id})" class="btn btn-danger btn-xs" style="margin: 0 10px">专家分配</button>
                    </c:if>
                    <c:if test="${item.isUseExpert == 1}">
                        <button onclick="macroMgr.onLevelOneMenuClick(null, 'zjkp.do')" class="btn btn-success btn-xs" style="margin: 0 10px">专家查看</button>
                    </c:if>
                    <button style="margin: 0 10px" class="btn btn-info btn-xs"
                            id="viewInfo"  data-toggle="modal"
                            data-target="#myModal"
                            OnClick="saveActivity('update','${item.id}','${item.activityName}','<fmt:formatDate value="${item.startTime}" pattern="yyyy-MM-dd"/>','<fmt:formatDate value="${item.endTime}" pattern="yyyy-MM-dd"/>')">

                        编辑
                    </button>
                    <button onclick="deleteActivity(${item.id})" class="btn btn-danger btn-xs" style="margin: 0 10px">删除</button>
                </td>
            </tr>
        </c:forEach>
        </tbody>


    </table>

    <!--列表翻页 开始-->
    <div class="page">
        <span style="padding:12px;  float:left; color:#0099cc;">共${dataList.total}条记录</span>
        <ul class="pagination" id="pageCon">

        </ul>
    </div>
    </div>
    <!--列表翻页 结束-->
    <!--新增/编辑 弹窗 开始-->
    <div id="my_dialog" style="height: 250px!important;">
        <div class="layui-row">
            <div class="layui-form-label " style="width: 100px">活动名称:</div>
            <input class="layui-input" id="activityName" type="text"  lay-verify="required" placeholder="请输入" value="" style="width: 60%"/>
            <input id="activityId"  type="text" class="form-control" style="display: none ;width: 80%" value="" />
            <input id="addOrEdit"  style="display: none ;" value="" />
        </div>
        <div class="layui-row" id="startAndEndTime" style="margin-top: 20px">
            <label class="layui-form-label" style="width: 100px; margin: 0px 0px 0px 0px;">日期范围:</label>
            <div class="layui-input-inline lay" style="width: 30%">
                <input type="text" autocomplete="off" id="startTime" class="layui-input" placeholder="开始日期">
            </div>
            <div class="layui-input-inline" style="width: 30%">
                <input type="text" autocomplete="off" id="endTime" class="layui-input" placeholder="结束日期">
            </div>
        </div>



        <div style="text-align: center;margin-top: 20px">
            <button id="topBnt" class="layui-btn layui-btn-primary layui-border-blue"  OnClick="Cancel()" >取消</button>
            <button class="layui-btn layui-bg-blue" OnClick="createActivity()" >确定</button>
        </div>
    </div>
    <%--edit编辑弹窗--%>
<%--    <div id="my_dialog_edit" style="height: 250px!important;">--%>
<%--        <div style="display: flex; align-items: center;">--%>
<%--            <p style="color: red; margin-right: 5px; margin-left: 15px">*</p>--%>
<%--            <div style="margin-right: 5px;">活动名称</div>--%>
<%--            <input id="activityName_edit" type="text" class="form-control" style="width: 80%" value="" />--%>
<%--            <input id="activityId_edit"  type="text" class="form-control" style="display: none ;width: 80%" value="" />--%>
<%--        </div>--%>
<%--        <div style="display: flex; align-items: center;">--%>
<%--            <p style="color: red; margin-right: 5px; margin-left: 15px">*</p>--%>
<%--            <div style="margin-right: 5px;">时间范围</div>--%>
<%--            <input id="startDate_edit" type="date" style="width: 80%">--%>
<%--            <input id="endDate_edit" type="date" style="width: 80%">--%>

<%--        </div>--%>

<%--        <div style="text-align: center;margin-top: 20px">--%>
<%--            <button id="topBnt_edit" class="my-btn-gray"  OnClick="Cancel_edit()" style="background: #ECF5FF; margin-right: 50px;margin-top: 10px; outline: none; border: 1px solid #C6E2FF;border-radius: 3px;font-size: 18px;color:#409EFF; width: 80px; height: 40px">取消</button>--%>
<%--            <button class="my-btn-blue" OnClick="editActivity()" style="background: #009CCD; margin-top: 10px;outline: none; border: none; width: 80px; height: 40px;border: 1px solid #C6E2FF;border-radius: 3px;font-size: 18px;color:#ffffff;">确定</button>--%>
<%--        </div>--%>
<%--    </div>--%>
    <!--新增/编辑 弹窗 结束-->

<%--    <div id="myModal" class="modal">--%>
<%--        <div class="modal-content">--%>
<%--            <div class="modal-title">创建活动</div>--%>
<%--            <span class="modal-close" onclick="cancelCreateActivity()">&times;</span>--%>
<%--            <div class="modal-line"></div>--%>

<%--            <span--%>
<%--                    style="color: red;">*活动名称:</span><input id="activityName" class="modal-input" type="text" placeholder="请输入内容">--%>

<%--            <div class="modal-buttons">--%>
<%--                <button onclick="cancelCreateActivity()" style="color: #000000">取消</button>--%>
<%--                <button onclick="createActivity()" style="background-color:deepskyblue ;">确定</button>--%>
<%--            </div>--%>
<%--        </div>--%>
<%--    </div>--%>

    <div class="mask-layer">
        <div class="loading">专家分配中...</div>
    </div>
</div>

</body>
<script>

    layui.use(function(){
        var laydate = layui.laydate;
        // 日期范围 - 左右面板联动选择模式
        laydate.render({
            elem: '#startAndEndTime',
            range: ['#startTime', '#endTime'],
            rangeLinked: true
        });
    });

    $(function (){
        $('#my_dialog').dialog({
            width : '600',
            height : "450",
            autoOpen : false,
            resizable : false,
            modal : true,
            closed: true,
            saveType: 'insert'
        });
        $('#my_dialog_edit').dialog({
            width : "500",
            height : "450",
            autoOpen : false,
            resizable : false,
            modal : true,
            closed: true,
            saveType: 'insert'
        });
    });
    $(document).ready(function(){
        //绑定主菜单单击方法，设置样式
        $(".ul-clearfix li").bind("click",function(){
            $(".ul-clearfix li a").removeClass("active");
            $(this).children(0).addClass("active");
        });
        //触发点击事件
        $(".ul-clearfix li a:first").click();

    });
    //分页-页面初始化时执行
    $(document).ready(function(){
        var curentPage = eval('${dataList.pageNum}');
        var totalPage = eval('${dataList.pages}');
        if(totalPage>0){
            var options = {
                bootstrapMajorVersion: 3,
                currentPage: curentPage,
                totalPages: totalPage,
                numberOfPages: 5,
                itemTexts: function (type, page, current) {
                    switch (type) {
                        case "first":
                            return "首页";
                        case "prev":
                            return "&laquo;";
                        case "next":
                            return "&raquo;";
                        case "last":
                            return "尾页";
                        case "page":
                            return page;
                    }
                },
                onPageClicked: function (event, originalEvent, type, page) {
                     business.addMainContaierHtml(WEBPATH+'/filesDrawActivity/goActivityList.do?pageNum='+page);
                }
            };
            $('#pageCon').bootstrapPaginator(options);
        }
    });

    //新增编辑弹窗
    function saveActivity(saveType,id,activityName,startTime,endTime){
        console.log(saveType,activityName);
        let dialogTitle = '';
        if (saveType=='insert'){
            dialogTitle = '创建活动'
            document.getElementById("activityName").value = '';
            document.getElementById("activityId").value = '';
            document.getElementById("addOrEdit").value = 'add';
            $('#my_dialog').dialog({ title :dialogTitle,closed:false,saveType:saveType})
        }else if (saveType == 'update'){
            dialogTitle = '编辑活动'
            document.getElementById("activityName").value = activityName;
            document.getElementById("activityId").value = id;
            document.getElementById("startTime").value = startTime;
            document.getElementById("endTime").value = endTime;
            document.getElementById("addOrEdit").value = 'edit';
            $('#my_dialog').dialog({ title :dialogTitle,closed:false,saveType:saveType})
        }


    }
    //关闭弹窗
    function Cancel(){
        console.log("点击关闭")
        $('#my_dialog').dialog({closed:true});
        document.getElementById("activityName").value = "";
        document.getElementById("startTime").value = "";
        document.getElementById("endTime").value = "";
    }
    function Cancel_edit(){
        console.log("点击关闭")
        $('#my_dialog_edit').dialog({closed:true});
        document.getElementById("activityName_edit").value = "";
        document.getElementById("activityName_id").value = "";
    }
    //创建活动
    function createActivity() {
        var activityNameInput = document.getElementById("activityName");
        var startTime = document.getElementById("startTime").value;
        var endTime = document.getElementById("endTime").value;
        var activityName = activityNameInput.value;
        var activityId = document.getElementById("activityId").value;
        console.log("创建活动，活动名称：" + activityName);


        var addOrEdit = document.getElementById("addOrEdit").value;
        if (addOrEdit == 'add'){
            $.ajax({
                type: "post",
                url: WEBPATH+"/filesDrawActivity/addActivity.do",
                data:{activityName:activityName,startTime:startTime,endTime:endTime},
                async:false,
                success: function(data){
                    console.log(data)
                    if(data.result=="error"){
                        swal({title: data.message ,text: "",type:"error"});
                        return false;
                    }else if(data.result=="success"){
                        $('#my_dialog').dialog({closed:true});
                        swal({title: data.message ,text: "",type:"success"});
                        //刷新页面
                        business.addMainContaierHtml(WEBPATH+'/filesDrawActivity/goActivityList.do');
                        // modal.style.display = "none";
                        document.getElementById("activityName").value = "";
                    }
                }
            });
        }else if (addOrEdit == 'edit'){
            $.ajax({
                type: "post",
                url: WEBPATH+"/filesDrawActivity/editActivity.do",
                data:{id:activityId,activityName:activityName,startTime:startTime,endTime:endTime},
                async:false,
                success: function(data){
                    console.log(data)
                    if(data.result=="error"){
                        swal({title: data.message ,text: "",type:"error"});
                        return false;
                    }else if(data.result=="success"){
                        $('#my_dialog').dialog({closed:true});
                        swal({title: data.message ,text: "",type:"success"});
                        //刷新页面
                        business.addMainContaierHtml(WEBPATH+'/filesDrawActivity/goActivityList.do');
                        document.getElementById("activityName").value = "";
                        document.getElementById("activityName").value = "";
                    }
                }
            });
        }



    }
    //编辑
    function editActivity() {
        var activityName_edit = document.getElementById("activityName_edit").value;
        var activityId_edit = document.getElementById("activityId_edit").value;
        console.log("编辑，活动名称：" + activityName_edit);
        $.ajax({
            type: "post",
            url: WEBPATH+"/filesDrawActivity/editActivity.do",
            data:{
                    id:activityId_edit,
                    activityName:activityName_edit

                },
            async:false,
            success: function(data){
                console.log(data)
                if(data.result=="error"){
                    swal({title: data.message ,text: "",type:"error"});
                    return false;
                }else if(data.result=="success"){
                    $('#my_dialog_edit').dialog({closed:true});
                    swal({title: data.message ,text: "",type:"success"});
                    //刷新页面
                    business.addMainContaierHtml(WEBPATH+'/filesDrawActivity/goActivityList.do');
                    document.getElementById("activityName_edit").value = "";
                    document.getElementById("activityName_id").value = "";
                }
            }
        });
    }
    //查看专家
    function viewExpert(){
        business.addMainContentParserHtml(WEBPATH+'/zjkpGerenList.do?&pageNum=1',null);
    }
    //分专家临时按钮,测接口用
    function subExpert(id){
        var This = this;
        $.ajax({
            type: "GET",
            url: WEBPATH+"/filesDrawActivity/subExpert.do",
            data:{activityId:id},
            async:true,
            beforeSend:function(){
                console.log("请求开始1")
                This.showMaskLayer();
            },
            success: function(data){
                if(data.result=="error"){
                    swal({title: data.message ,text: "",type:"error"});
                    return false;
                }else if(data.result=="success"){
                    swal({title: data.message ,text: "",type:"success"});
                    //刷新页面
                    business.addMainContaierHtml(WEBPATH+'/filesDrawActivity/goActivityList.do');
                }
            },
            complete: function() {  // 请求结束后关闭遮罩层
                this.hideMaskLayer();  // 这里的 this 仍然代表的是 jqXHR 对象
                business.addMainContaierHtml(WEBPATH+'/filesDrawActivity/goActivityList.do');
                console.log("请求已结束")
            }.bind(this)  // 使用 bind 将 this 绑定到 cqCaseBaseInfo 对象

        });
    }
    //删除活动
    function deleteActivity(id) {
        console.log("删除活动，ID：" + id);
        swal({
                title: "删除后不可恢复，已抽取案卷也将清空！",
                text: "确认删除？",
                type: "warning",
                showCancelButton: true,
                cancelButtonText: "取消",
                showConfirmButton: true,
                confirmButtonText: "确定",
                closeOnConfirm:false
            },function (){
            $.ajax({
                type: "GET",
                url: WEBPATH+"/filesDrawActivity/removeActivity.do",
                data:{activityId:id},
                async:false,
                success: function(data){
                    if(data.result=="error"){
                        swal({title: data.message ,text: "",type:"error"});
                        return false;
                    }else if(data.result=="success"){
                        swal({title: data.message ,text: "",type:"success"});
                        //刷新页面
                        business.addMainContaierHtml(WEBPATH+'/filesDrawActivity/goActivityList.do');
                    }
                }
            });
        })
    }
    //跳转抽取案卷页面   并根据活动id查询列表
    function extractCaseFile(id) {
        console.log("抽取案卷，ID：" + id);
        macroMgr.onLevelOneMenuClick(null, 'filesDrawActivity/goAllFiles.do?activityId='+id)
    }
    //跳转管理案卷页面   并根据活动ID查询列表
    function manageActivity(id) {
        console.log("管理活动，ID：" + id);
        macroMgr.onLevelOneMenuClick(null, 'filesDrawActivity/goAjManage.do?activityId='+id)
    }

    //显示遮罩层
    function showMaskLayer() {
        var mask = document.querySelector('.mask-layer');
        var loading = mask.querySelector('.loading');
        mask.style.display = 'block';
        loading.style.display = 'block';

        console.log("遮罩开启")
    }
    //关闭遮罩层
    function hideMaskLayer() {
        console.log("遮罩关闭")
        var mask = document.querySelector('.mask-layer');
        var loading = mask.querySelector('.loading');
        mask.style.display = 'none';
        loading.style.display = 'none';
    }


    //  --- ↓-↓-↓-↓-↓-↓-↓--↓-↓-↓-↓-↓-↓-↓-↓-↓-↓-↓-↓-↓-↓-↓-↓-↓-↓-  ---

    // var modal = document.getElementById("myModal");

    // function openModal() {
    //     modal.style.display = "block";
    // }

    // function cancelCreateActivity() {
    //     modal.style.display = "none";
    //     var activityNameInput = document.getElementById("activityName");
    //     activityNameInput.value = "";
    //
    // }











</script>

<style>

    .center-body {
        width: 1200px;
        margin: 0 auto;
        overflow: hidden;
    }
    .head-item {
        width: 1200px;
        height: 45px;
        margin-top: 15px;
    }

    .head-item>ul>li {
        float: left;
    }

    .head-item>ul>li>a {
        display: block;
        margin-right: 15px;
        width: 158px;
        height: 45px;
        background: #3f91c2;
        text-align: center;
        line-height: 45px;
        box-shadow: 0 0 5px #999;
        overflow: hidden;
        border-radius: 8px;
        color: #ffffff;
        font-size: 16px;
    }
    .ul-clearfix {
        *zoom: 1;
        margin-left: 10px;
    }
    .table {
        margin-top: 5px;
    }
    .scroll-container{
        height: 70%;
        width: 99%;/* 设置容器的高度 */
        overflow: auto; /* 自动显示滚动条 */
        margin-left: auto;
        margin-right: auto;
        margin-top: 10px;
    }
    /* 遮罩层样式 */
    .mask-layer {
        display: none;  /* 初始不显示 */
        position: fixed; /* 固定定位 */
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.6); /* 半透明黑色背景 */
        z-index: 99999; /* 最高层级 */
    }

    /* 加载状态样式 */
    .loading {
        display: none; /* 初始不显示 */
        position: absolute; /* 相对定位 */
        top: 50%; /* 垂直居中 */
        left: 50%; /* 水平居中 */
        transform: translate(-50%, -50%);
        padding: 12px 24px;
        color: #fff;
        font-size: 18px;
        background-color: #333;
        border-radius: 4px;
        z-index: 100000; /* 高于遮罩层层级 */
    }

</style>
</html>
