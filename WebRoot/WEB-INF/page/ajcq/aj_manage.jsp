<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
    <title>案卷抽取</title>
</head>
<link rel="stylesheet" href="${webpath }/static/css/caiji.css">
<body>
<div class="center_home_weizhi">当前位置：案卷抽取  </div>
<div class="center-body">


    <%--页面上方搜索栏--%>
    <div class="layui-panel layui-form" id="accordion" style="margin-top: 40px; padding: 15px">
        <form id= "searchForm"  role="form" >
            <input id="activityId" name="activityId"  type="hidden" value="${activityId}" />
            <div class="layui-row">
                <div class="layui-col-xs4">
                    <label class="layui-form-label" style="width: 100px">案卷名称：</label>
                    <input class="layui-input layui-input-inline" type="text" id="fileName" name="fileName" value="${fileName}" style="width: 70%">
                </div>
                <div class="layui-col-xs4">
                    <label class="layui-form-label" style="width: 100px">文书号：</label>
                    <input class="layui-input layui-input-inline" type="text" id="fileCode" name="fileCode" value="${fileCode}" style="width: 70%">
                </div>
                <div class="layui-col-xs4">
                    <label class="layui-form-label" style="width: 100px">案卷状态：</label>
                    <div class="layui-input-block">
                        <select  name="caseState" id="caseState">
                            <option value="">请选择</option>
                            <option value="1" <c:if test="${caseState=='1' }">selected </c:if>>已结案</option>
                            <option value="2" <c:if test="${caseState=='2' }">selected </c:if>>未结案</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs4">
                    <label class="layui-form-label" style="width: 100px">案卷类型：</label>
                    <div class="layui-input-block">
                        <select id ="fileType"  name ="fileType">
                            <!-- 0代表 行政处罚案卷 ；1代表按日计罚案卷； 2代表移送行政拘留案卷；3代表涉嫌犯罪移送案卷；4代表申请法院强制执行案卷；5代表发现问题的污染源现场监督检查稽查案卷   -->
                            <option value = ""  >请选择案卷类型</option>
                            <option value="0" <c:if test="${fileType=='0' }">selected</c:if>>行政处罚案卷</option>
                            <option value="1" <c:if test="${fileType=='1' }">selected</c:if>>按日计罚案卷</option>
                            <option value="2" <c:if test="${fileType=='2' }">selected</c:if>>移送行政拘留案卷</option>
                            <option value="3" <c:if test="${fileType=='3' }">selected</c:if>>涉嫌犯罪案卷</option>
                            <option value="6" <c:if test="${fileType=='6' }">selected</c:if>>查封扣押案卷</option>
                            <option value="7" <c:if test="${fileType=='7' }">selected</c:if>>限产停产案卷</option>
                            <option value="7" <c:if test="${fileType=='9' }">selected</c:if>>不予处罚</option>
                        </select>
                    </div>
                </div>
                <div class="layui-col-xs4" >
                    <label class="layui-form-label" style="width: 100px">行政区：</label>
                    <input class="layui-input layui-input-inline" type="text" id="provinceName" name="provinceName" value="${provinceName}" placeholder="所属省" style="width: 34%">
                    <input class="layui-input layui-input-inline" type="text" id="cityName" name="cityName" value="${cityName}" placeholder="所属市" style="width: 35%">

                </div>

            </div>


        </form>
        <div  style="text-align: right; margin-top: -38px;">
            <button class="layui-btn layui-bg-blue" id="filterButton" onclick="filterButton()">筛选</button>
            <button class="layui-btn layui-btn-primary layui-border" id="resetButton" onclick="resetButton()">重置</button>
        </div>

    </div>
    <div class="layui-panel" style="margin-top: 10px; padding: 15px">
        <div class="layui-row">
                <button onclick="backActivity()" class="layui-btn layui-btn-primary layui-border-blue">返回</button>
                <button class="layui-btn layui-bg-red" id ="viewExcel" >导出</button>
        </div>
        <div class="table-container">
        <table class="layui-table" >
            <colgroup>
                <col >
                <col >
                <col >
                <col >
                <col >
                <col width="300">
                <col >
                <col width="80">
            </colgroup>
            <thead>
            <tr>
                <th >省级</th>
                <th >市级</th>
                <th >县级</th>
                <th >案卷类型</th>
                <th >文书号</th>
                <th >案卷状态</th>
                <th >案卷名称</th>
                <th >备注</th>
                <th >操作</th>
            </tr>
            </thead>
            <tbody>
            <c:forEach items="${dataList.list}" var="item" varStatus="status">
                <tr>
                    <div><input id ="id"  name = "id" type="hidden" value="${item.id}"></div>
                    <td>${item.province}</td>
                    <td>${item.city}</td>
                    <td>${item.county}</td>
                    <td>${item.fileTypeName}</td>
                    <td>${item.fileCode}</td>

                    <td>${item.closedName}</td>
                    <td>${item.fileName}</td>
                    <td>${item.remark}</td>
                    <td><button class="btn btn-success btn-xs" onclick="xiaZaiAnJuan(${item.id})">下载</button>
                    </td>
                </tr>
            </c:forEach>
            </tbody>
        </table>
        </div>
        <!--列表翻页 开始-->
        <div class="page">
            <span style="padding:12px; float:left; color:#0099cc;">共${dataList.total}条记录</span>
            <ul class="pagination" id="pageCon">

            </ul>
        </div>
        <!--列表翻页 结束-->
    </div>



<%--
    <div class="scroll-container">
        <table class="table table-bordered  ">
            <thead>
            <tr>
                <td rowspan="2" height="30" bgcolor="#efefef">序号</td>
                <td colspan="2" bgcolor="#efefef">行政区</td>
                <td rowspan="2" bgcolor="#efefef">案卷名称</td>
                <td rowspan="2" bgcolor="#efefef">文书号</td>
                <td rowspan="2" bgcolor="#efefef">案卷类型</td>
                <td rowspan="2" bgcolor="#efefef">案卷状态</td>
                <td colspan="3" bgcolor="#efefef">案卷归属</td>
                <td rowspan="2" bgcolor="#efefef">操作</td>
            </tr>
            <tr>
                <td height="30" bgcolor="#efefef">省</td>
                <td bgcolor="#efefef">市</td>
&lt;%&ndash;                <td bgcolor="#efefef">县</td>&ndash;%&gt;
                <td bgcolor="#efefef">省</td>
                <td bgcolor="#efefef">市</td>
                <td bgcolor="#efefef">县</td>
            </tr>
            </thead>
            <tbody>
            <c:forEach items="${dataList.list}" var="item" varStatus="status">
                <tr>
                    <div><input id ="id"  name = "id" type="hidden" value="${item.id}"></div>
                    <td align="center">${status.index+1 }</td>
                    <td>${item.province}</td>
                    <td>${item.city}</td>
&lt;%&ndash;                    <td>${item.county}</td>&ndash;%&gt;
                    <td>${item.fileName}</td>
                    <td>${item.fileCode}</td>
                    <td>${item.fileTypeName}</td>
                    <td>${item.closedName}</td>
                    <td>${item.belongProvince}</td>
                    <td>${item.belongCity}</td>
                    <td>${item.belongCounty}</td>
                    <td><button class="btn btn-success btn-xs" onclick="xiaZaiAnJuan(${item.id})">下载</button>
                    </td>
                </tr>
            </c:forEach>
            </tbody>
        </table>


    </div>--%>



</div>

</body>
<script>
    layui.use('form', function(){
        var form = layui.form;
        form.render();

        var util = layui.util;
        // 自定义固定条
        util.fixbar({
            bars: [{ // 定义可显示的 bar 列表信息 -- v2.8.0 新增
                type: 'share',
                icon: 'layui-icon-share'
            }, ],
            // bar1: true,
            // bar2: true,
            // default: false, // 是否显示默认的 bar 列表 --  v2.8.0 新增
            // bgcolor: '#393D52', // bar 的默认背景色
            // css: {right: 100, bottom: 100},
            // target: '#target-test', // 插入 fixbar 节点的目标元素选择器
            // duration: 300, // top bar 等动画时长（毫秒）
            on: { // 任意事件 --  v2.8.0 新增
                mouseenter: function(type){
                    layer.tips(type, this, {
                        tips: 4,
                        fixed: true
                    });
                },
                mouseleave: function(type){
                    layer.closeAll('tips');
                }
            },
            // 点击事件
            click: function(type){
                console.log(this, type);
                // layer.msg(type);
            }
        });
    });
    //分页-页面初始化时执行
    $(document).ready(function(){
        // var activityId = document.getElementById("activityId").value;
        var curentPage = eval('${dataList.pageNum}');
        var totalPage = eval('${dataList.pages}');
        if(totalPage>0){
            var options = {
                bootstrapMajorVersion: 3,
                currentPage: curentPage,
                totalPages: totalPage,
                numberOfPages: 5,
                itemTexts: function (type, page, current) {
                    switch (type) {
                        case "first":
                            return "首页";
                        case "prev":
                            return "&laquo;";
                        case "next":
                            return "&raquo;";
                        case "last":
                            return "尾页";
                        case "page":
                            return page;
                    }
                },
                onPageClicked: function (event, originalEvent, type, page) {
                    business.addMainContaierHtml(WEBPATH+'/filesDrawActivity/goAjManage.do?pageNum='+page,$("#searchForm").serialize());
                }
            };
            $('#pageCon').bootstrapPaginator(options);
        }
    });


    // 获取DOM元素
    // var resetButton = document.getElementById('resetButton');
    // var dataTable = document.getElementById('dataTable');
    // var pagination = document.getElementById('pagination');

    // 添加按钮点击事件监听器
    // resetButton.addEventListener('click', handleReset);

    // 处理筛选按钮点击事件
    function filterButton() {

        business.addMainContaierHtml(WEBPATH+'/filesDrawActivity/goAjManage.do',$("#searchForm").serialize());
        // 获取用户输入的筛选条件
        // //案卷名称
        // const fileName = document.getElementById('fileName').value;
        // //案卷文书号
        // const fileCode = document.getElementById('fileCode').value;
        // //案卷类型
        // const fileType = document.getElementById('fileType').value;
        // //案卷状态
        // const caseState = document.getElementById('caseState').value;

        // 根据筛选条件发送请求到服务器获取数据，并动态生成表格内容

/*        // 清空表格
        dataTable.innerHTML = '';

        // 生成分页器
        pagination.innerHTML = '分页器的HTML代码';*/
    }
    //重置按钮,重置筛选条件
    function resetButton(){
        //案卷名称
        document.getElementById('fileName').value = "";
        //案卷文书号
        document.getElementById('fileCode').value = "";
        //案卷类型
        document.getElementById('fileType').value = "";
        //案卷状态
        document.getElementById('caseState').value = "";
        business.addMainContaierHtml(WEBPATH+'/filesDrawActivity/goAjManage.do',$("#searchForm").serialize());

    }
    //导出Excel
    $("#viewExcel").click(function(){
        var activityId = $("#activityId").val();
        var fileName = $("#fileName").val();
        var fileCode = $("#fileCode").val();
        var caseState = $("#caseState").val();
        var fileType = $("#fileType").val();

        var path = WEBPATH+"/filesDrawActivity/filesExportExcel.do?activityId="+activityId+"&fileName="+fileName+"&fileCode="+fileCode+"&caseState="+caseState+"&fileType="+fileType;
        window.location.href=path;

    });
    // 案卷下载
    function xiaZaiAnJuan(id){
        //var fileCode = $("#fileCode").html();
        var fileid  = id;
        $.ajax({
            type:"GET",
            url:WEBPATH+'/filesDrawActivity/checkFileUrl.do',
            data:{fileId:id },
            async:false,
            success:function(data){
                debugger
                //成功
                if("success" == data.result){
                    window.location.href="${pageContext.request.contextPath}/filesDrawActivity/fileDownload.do?fileId="+id;
                    return false;
                }else if("error" == data.result){
                    swal({title:data.message, type:"error"});
                    return false;
                }
            }
        });
    }

    //返回活动列表页面
    function backActivity(){
        macroMgr.onLevelOneMenuClick(null, 'filesDrawActivity/goActivityList.do')
    }



</script>

<style>
    .center-body {
        width: 75%;
        margin: 0 auto;
        overflow: auto;
        position: relative
    }
    .head-item {
        width: 1200px;
        height: 45px;
        margin-top: 15px;
    }

    .head-item>ul>li {
        float: left;
    }

    .head-item>ul>li>a {
        display: block;
        margin-right: 15px;
        width: 158px;
        height: 45px;
        background: #3f91c2;
        text-align: center;
        line-height: 45px;
        box-shadow: 0 0 5px #999;
        overflow: hidden;
        border-radius: 8px;
        color: #ffffff;
        font-size: 16px;
    }
    .ul-clearfix {
        *zoom: 1;
        margin-left: 10px;
    }
    .scroll-container{
        height: 600px;
        width: 99%;/* 设置容器的高度 */
        overflow: auto; /* 自动显示滚动条 */
    }
    .table-container {
        margin-bottom: 10px;
        margin-top: 10px;
        width: 100%; /* 根据需要设置宽度 */
        max-height: 800px; /* 设置最大高度 */
        height: auto; /* 自动调整高度 */
        overflow: auto; /* 内容超出时显示滚动条 */
    }
    /*table {*/
    /*    border-collapse: collapse;*/
    /*    width: 100%;*/
    /*}*/
    /*.table>thead>tr>td{*/
    /*    font-size: 18px;*/
    /*    font-weight: bold;*/
    /*}*/
    /*th, td {*/
    /*    border: 1px solid black;*/
    /*    padding: 8px;*/
    /*    text-align: center;*/
    /*    font-size: 18px;*/
    /*}*/

</style>
</html>
