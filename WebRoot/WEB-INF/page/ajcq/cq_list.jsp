<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
    <title>案卷抽取</title>
</head>
<link rel="stylesheet" href="${webpath }/static/css/caiji.css">
<body>
<div class="center_home_weizhi">当前位置：案卷抽取明细  </div>
<div class="center-body">
    <div class="layui-panel layui-row" id="accordion" style="margin-top: 40px; padding: 15px">

    </div>
    <div class="layui-panel " style="margin-top: 10px; padding: 15px">
        <div class="layui-row">
            <div class="layui-col-xs6">
                <input id="activityId"  type="hidden" value="${activityId}" />
                <button onclick="backCountList(${activityId})" class="layui-btn layui-btn-primary layui-border-blue">返回</button>
                <button class="layui-btn layui-bg-red" id ="viewExcel" >导出</button>
            </div>
            <div class="layui-col-xs6" style="text-align: right">
            </div>
        </div>
        <div class="scroll-container">
            <table class="layui-table">
                <thead>
                <tr>
                    <th bgcolor="#efefef">序号</th>
                    <th bgcolor="#efefef">抽取类型</th>
                    <th bgcolor="#efefef">姓名</th>
                    <th bgcolor="#efefef">省级</th>
                    <th bgcolor="#efefef">市级</th>
                    <th bgcolor="#efefef">县级</th>
                    <th bgcolor="#efefef">行政处罚</th>
                    <th bgcolor="#efefef">按日连续处罚</th>
                    <th bgcolor="#efefef">行政拘留</th>
                    <th bgcolor="#efefef">涉嫌污染犯罪</th>
                    <th bgcolor="#efefef">查封扣押</th>
                    <th bgcolor="#efefef">限产停产</th>
                    <th bgcolor="#efefef">不予处罚</th>
                    <th bgcolor="#efefef">合计</th>
                </tr>
                </thead>
                <tbody>
                <c:forEach items="${dataList}" var="item" varStatus="status">
                    <tr>
                        <td align="center">${status.index+1 }</td>
                        <td>${item.cqType}</td>
                        <td>${item.name}</td>
                        <td>${item.province}</td>
                        <td>${item.city}</td>
                        <td>${item.county}</td>
                        <td>${item.penalty}</td>
                        <td>${item.dailyPenalty}</td>
                        <td>${item.detention}</td>
                        <td>${item.crimes}</td>
                        <td>${item.seizureDetain}</td>
                        <td>${item.stopProduction}</td>
                        <td>${item.notPunishable}</td>
                        <td>${item.total}</td>
                    </tr>
                </c:forEach>
                <tr>
                    <td colspan="6" align="center">总计</td>
                    <td>${total.penalty}</td>
                    <td>${total.dailyPenalty}</td>
                    <td>${total.detention}</td>
                    <td>${total.crimes}</td>
                    <td>${total.seizureDetain}</td>
                    <td>${total.stopProduction}</td>
                    <td>${total.notPunishable}</td>
                    <td>${total.total}</td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>






</div>


</body>
<script>
    //返回统计页面
    function backCountList(id){
        console.log("跳转案卷,活动id:",id)
        macroMgr.onLevelOneMenuClick(null, 'filesDrawActivity/goCaseFile2024.do?activityId='+id)
    }


    $(document).ready(function(){
        //绑定主菜单单击方法，设置样式
        $(".ul-clearfix li").bind("click",function(){
            $(".ul-clearfix li a").removeClass("active");
            $(this).children(0).addClass("active");
        });
        //触发点击事件
        $(".ul-clearfix li a:first").click();

    });

    //导出按钮
    $("#viewExcel").click(function(){
        var provinceName = $("#provinceName").val();
        var cityName = $("#cityName").val();
        var activityId = $("#activityId").val();

        var path = WEBPATH+"/filesDrawActivity/extractDetailsExport.do?provinceName="+provinceName+"&cityName="+cityName+"&activityId="+activityId;
        window.location.href=path;
    })


</script>

<style>
    .center-body {
        width: 88%;
        margin: 0 auto;
        overflow: hidden;
    }
    .head-item {
        width: 1200px;
        height: 45px;
        margin-top: 15px;
    }

    .head-item>ul>li {
        float: left;
    }

    .head-item>ul>li>a {
        display: block;
        margin-right: 15px;
        width: 158px;
        height: 45px;
        background: #3f91c2;
        text-align: center;
        line-height: 45px;
        box-shadow: 0 0 5px #999;
        overflow: hidden;
        border-radius: 8px;
        color: #ffffff;
        font-size: 16px;
    }
    .ul-clearfix {
        *zoom: 1;
        margin-left: 10px;
    }
    .scroll-container{
        height: 70%;
        width: 95%;/* 设置容器的高度 */
        overflow: auto; /* 自动显示滚动条 */
        margin-left: auto;
        margin-right: auto;
        margin-top: 5px;
    }

    /* 首列固定 */
    /*.scroll-container thead tr > th:first-child,*/
    /*.scroll-container tbody tr > td:first-child {*/
    /*    position: sticky;*/
    /*    left: 0;*/
    /*    z-index: 1;*/
    /*}*/
    .table {
        margin-top: auto;
    }
    /* 表头固定 */
    .scroll-container thead tr > th {
        position: sticky;
        top: 0;
        z-index: 2;
    }

    /* 表头首列强制最顶层 */
    /*.scroll-container thead tr > td:first-child {*/
    /*    z-index: 3;*/
    /*}*/
    /* 遮罩层样式 */
    .mask-layer {
        display: none;  /* 初始不显示 */
        position: fixed; /* 固定定位 */
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.6); /* 半透明黑色背景 */
        z-index: 99999; /* 最高层级 */
    }

    /* 加载状态样式 */
    .loading {
        display: none; /* 初始不显示 */
        position: absolute; /* 相对定位 */
        top: 50%; /* 垂直居中 */
        left: 50%; /* 水平居中 */
        transform: translate(-50%, -50%);
        padding: 12px 24px;
        color: #fff;
        font-size: 18px;
        background-color: #333;
        border-radius: 4px;
        z-index: 100000; /* 高于遮罩层层级 */
    }
</style>
</html>
