<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
    <title>案卷抽取</title>
</head>
<link rel="stylesheet" href="${webpath }/static/css/caiji.css">
<body>
<div class="center_home_weizhi">当前位置：案卷抽取  </div>
<div class="center-body">
    <div class="item" style="margin-top: 40px;">
        <ul class="clearfix">
            <li>
                <a href="javascript:void(0);" onclick="goAllFiles()">案卷汇总</a>
            </li><li>
            <a href="javascript:void(0);" onclick="" style="color: #333; background-color: #fff; border-color: #ccc;">案卷抽取</a>
        </li>
        </ul>
    </div>
    <div class="item" style="margin-top: 25px;">
        <input id="activityId"  type="hidden" value="${activityId}" />
        <ul class="clearfix">
            <li>
                <a href="javascript:void(0);" onclick="cqCaseBaseInfo(1)" style="padding: 8px 5px;
                                                                                font-size: 15px;
                                                                                line-height: 2;
                                                                                border-radius: 3px;
                                                                                width: 120px;
                                                                                height: 45px;
                                                                                background: #80C269;
                                                                                margin-left: 30px;" >抽取案卷</a>
            </li>
<%--            <li>--%>
<%--                <a href="javascript:void(0);" onclick="cqCaseBaseInfo(2)" style="padding: 8px 5px;--%>
<%--                                                                                font-size: 10px;--%>
<%--                                                                                line-height: 1.5;--%>
<%--                                                                                border-radius: 3px;--%>
<%--                                                                                width: 120px;--%>
<%--                                                                                height: 45px;--%>
<%--                                                                                background: #80C269;--%>
<%--                                                                                margin-left: 30px;" >抽取案卷<br>(可抽本市未结案)</a>--%>
<%--            </li>--%>
<%--            <li>--%>
<%--                <a href="javascript:void(0);" onclick="cqCaseBaseInfo(3)" style="padding: 8px 5px;--%>
<%--                                                                                font-size: 10px;--%>
<%--                                                                                line-height: 1.5;--%>
<%--                                                                                border-radius: 3px;--%>
<%--                                                                                width: 120px;--%>
<%--                                                                                height: 45px;--%>
<%--                                                                                background: #80C269;--%>
<%--                                                                                margin-left: 30px;" >抽取案卷<br>(可抽省内未结案)</a>--%>
<%--            </li>--%>
        </ul>
    </div>
    <div class="scroll-container">
        <table class="table table-bordered  ">
        <thead>
            <tr>
                <th bgcolor="#efefef">序号</th>
                <th bgcolor="#efefef">省</th>
                <th bgcolor="#efefef">市</th>
                <th bgcolor="#efefef">行政处罚</th>
                <th bgcolor="#efefef">按日记罚</th>
                <th bgcolor="#efefef">行政拘留</th>
                <th bgcolor="#efefef">环境污染犯罪</th>
                <th bgcolor="#efefef">查封扣押</th>
                <th bgcolor="#efefef">限产停产</th>
                <th bgcolor="#efefef">合计</th>
            </tr>
        </thead>
        <tbody>
        <c:forEach items="${dataList}" var="item" varStatus="status">
            <tr>
                <td align="center">${status.index+1 }</td>
                <td>${item.province}</td>
                <td>${item.city}</td>
                <td>${item.penalty}</td>
                <td>${item.dailyPenalty}</td>
                <td>${item.detention}</td>
                <td>${item.crimes}</td>
                <td>${item.seizureDetain}</td>
                <td>${item.stopProduction}</td>
                <td>${item.total}</td>
            </tr>
        </c:forEach>
        <tr>
            <td colspan="3" align="center">总计</td>
            <td>${total.penalty}</td>
            <td>${total.dailyPenalty}</td>
            <td>${total.detention}</td>
            <td>${total.crimes}</td>
            <td>${total.seizureDetain}</td>
            <td>${total.stopProduction}</td>
            <td>${total.total}</td>
        </tr>
        </tbody>
    </table>
    </div>


    <div class="mask-layer">
        <div class="loading">抽取中...</div>
    </div>

</div>


</body>
<script>
    //抽取案卷
    function cqCaseBaseInfo(type) {
        var This = this;

        var activityId = document.getElementById("activityId").value;
        $.ajax({
            type: "GET",
            url: WEBPATH+"/filesDrawActivity/getRandomFiles.do",
            // data:{activityId:activityId,type:type},
            data:{activityId:activityId},
            async:true,
            beforeSend:function(){
                console.log("请求开始1")
                This.showMaskLayer();
            },
            success: function(data){

                if(data.result=="error"){
                    swal({title: data.message ,text: "",type:"error"});
                    return false;
                }else if(data.result=="success"){
                    swal({title: data.message ,text: "",type:"success"});
                    business.addMainContaierHtml(WEBPATH+'/filesDrawActivity/goCqList.do?activityId='+activityId);
                }
            },
            complete: function() {  // 请求结束后关闭遮罩层
                this.hideMaskLayer();  // 这里的 this 仍然代表的是 jqXHR 对象
                console.log("请求已结束")
            }.bind(this)  // 使用 bind 将 this 绑定到 cqCaseBaseInfo 对象
        });
    }

    $(document).ready(function(){
        //绑定主菜单单击方法，设置样式
        $(".ul-clearfix li").bind("click",function(){
            $(".ul-clearfix li a").removeClass("active");
            $(this).children(0).addClass("active");
        });
        //触发点击事件
        $(".ul-clearfix li a:first").click();

    });

    //跳转案卷汇总页面
    function goAllFiles(){
        var activityId = $("#activityId").val();
        macroMgr.onLevelOneMenuClick(null, 'filesDrawActivity/goAllFiles.do?activityId='+activityId)
    }



    //显示遮罩层
    function showMaskLayer() {
        var mask = document.querySelector('.mask-layer');
        var loading = mask.querySelector('.loading');
        mask.style.display = 'block';
        loading.style.display = 'block';

        console.log("遮罩开启")
    }
    //关闭遮罩层
    function hideMaskLayer() {
        console.log("遮罩关闭")
        var mask = document.querySelector('.mask-layer');
        var loading = mask.querySelector('.loading');
        mask.style.display = 'none';
        loading.style.display = 'none';
    }
</script>

<style>
    .center-body {
        width: 100%;
        margin: 10px;
        overflow: hidden;
    }
    .head-item {
        width: 1200px;
        height: 45px;
        margin-top: 15px;
    }

    .head-item>ul>li {
        float: left;
    }

    .head-item>ul>li>a {
        display: block;
        margin-right: 15px;
        width: 158px;
        height: 45px;
        background: #3f91c2;
        text-align: center;
        line-height: 45px;
        box-shadow: 0 0 5px #999;
        overflow: hidden;
        border-radius: 8px;
        color: #ffffff;
        font-size: 16px;
    }
    .ul-clearfix {
        *zoom: 1;
        margin-left: 10px;
    }
    .scroll-container{
        height: 70%;
        width: 95%;/* 设置容器的高度 */
        overflow: auto; /* 自动显示滚动条 */
        margin-left: auto;
        margin-right: auto;
        margin-top: 5px;
    }

    /* 首列固定 */
    /*.scroll-container thead tr > th:first-child,*/
    /*.scroll-container tbody tr > td:first-child {*/
    /*    position: sticky;*/
    /*    left: 0;*/
    /*    z-index: 1;*/
    /*}*/
    .table {
        margin-top: auto;
    }
    /* 表头固定 */
    .scroll-container thead tr > th {
        position: sticky;
        top: 0;
        z-index: 2;
    }

    /* 表头首列强制最顶层 */
    /*.scroll-container thead tr > td:first-child {*/
    /*    z-index: 3;*/
    /*}*/
    /* 遮罩层样式 */
    .mask-layer {
        display: none;  /* 初始不显示 */
        position: fixed; /* 固定定位 */
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.6); /* 半透明黑色背景 */
        z-index: 99999; /* 最高层级 */
    }

    /* 加载状态样式 */
    .loading {
        display: none; /* 初始不显示 */
        position: absolute; /* 相对定位 */
        top: 50%; /* 垂直居中 */
        left: 50%; /* 水平居中 */
        transform: translate(-50%, -50%);
        padding: 12px 24px;
        color: #fff;
        font-size: 18px;
        background-color: #333;
        border-radius: 4px;
        z-index: 100000; /* 高于遮罩层层级 */
    }
</style>
</html>
