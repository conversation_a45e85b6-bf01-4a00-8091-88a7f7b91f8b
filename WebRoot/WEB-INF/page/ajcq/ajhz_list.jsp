<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
    <title>案卷抽取</title>
</head>
<link rel="stylesheet" href="${webpath }/static/css/caiji.css">
<body>
<div class="center_home_weizhi">当前位置：案卷汇总  </div>
<div class="center-body">
    <div class="layui-panel layui-row" id="accordion" style="margin-top: 40px; padding: 15px">
        <form id= "searchForm"  role="form" >
            <input id="activityId" name="activityId"  type="hidden"  value="${activityId}" />
            <input id="startTime" name="startTime"  type="hidden"  value="<fmt:formatDate value="${startTime}" pattern="yyyy-MM-dd"/>" />
            <input id="endTime" name="endTime"  type="hidden"  value="<fmt:formatDate value="${endTime}" pattern="yyyy-MM-dd"/>" />
            <div class="layui-col-xs8 ">
                <label class="layui-form-label" style="width: 100px">行政区：</label>
                <input class="layui-input layui-input-inline" type="text" id="provinceName" name="provinceName" value="${provinceName}" placeholder="所属省" style="width: 30%">
                <input class="layui-input layui-input-inline" type="text" id="cityName" name="cityName" value="${cityName}" placeholder="所属市" style="width: 30%">
            </div>
        </form>
        <div class="layui-col-xs4">
            <button class="layui-btn layui-bg-blue" id="filterButton" onclick="filterButton()">筛选</button>
            <button class="layui-btn layui-btn-primary layui-border" id="resetButton" onclick="resetButton()">重置</button>
        </div>
    </div>

    <div class="layui-panel" style="margin-top: 10px; padding: 15px">
        <div class="layui-row">
            <div class="layui-col-xs6">
                <button onclick="backActivity()" class="layui-btn layui-btn-primary layui-border-blue">返回</button>
                <button class="layui-btn layui-bg-red" id ="viewExcel" >导出</button>
            </div>
            <div class="layui-col-xs6" style="text-align: right">
<%--                <button class="layui-btn layui-bg-blue"  onclick="goActivityList()">案卷结果(旧)</button>--%>
                <button class="layui-btn ${activitySate == 1 ? "" : "hidden"}" onclick="goCaseFile2024()">抽取结果</button>
                <button class="layui-btn layui-bg-red ${activitySate == 0 ? "" : "hidden"}" onclick="getCaseFile2024(${activityId})">案卷抽取</button>
            </div>
        </div>
        <table class="layui-table" lay-skin="line">
            <colgroup>
                <col width="100">
                <col width="150">
                <col width="100">
                <col width="100">
                <col>
            </colgroup>
            <thead>
            <tr>
                <th>截止时间:</th>
                <th><fmt:formatDate value="${startTime}" pattern="yyyy/MM/dd"/>~<fmt:formatDate value="${endTime}" pattern="yyyy/MM/dd"/></th>
                <th>总计:</th>
                <th>${dataList.total}    </th>
                <th>(注:只统计附件不为空的案卷)</th>
            </tr>
            </thead>
        </table>

        <table class="layui-table"  id="ID-table-demo-init">
            <thead>
            <tr>
                <th >省级</th>
                <th >市级</th>
                <th >县级</th>
                <th >总案卷数</th>
                <th >行政处罚</th>
                <th >按日连续处罚</th>
                <th >查封扣押</th>
                <th >限产停产</th>
                <th >涉嫌污染环境犯罪</th>
                <th >行政拘留</th>
                <th >不予处罚</th>
            </tr>
            </thead>
            <tbody>
            <c:forEach items="${dataList.list}" var="item" varStatus="status">
                <tr>
                    <td>${item.provinceName}</td>
                    <td>${item.cityName}</td>
                    <td>${item.countyName}</td>
                    <td>${item.allCount}</td>
                    <td>${item.ybxzcfCount}</td>
                    <td>${item.arjfCount}</td>
                    <td>${item.cfkyCount}</td>
                    <td>${item.xctcCount}</td>
                    <td>${item.sxfzCount}</td>
                    <td>${item.ysxzjlCount}</td>
                    <td>${item.notPunishable}</td>

                    </td>
                </tr>
            </c:forEach>
            </tbody>
        </table>
        <!--列表翻页 开始-->
        <div class="page">
            <span style="padding:12px;  float:left; color:#0099cc;">共${dataList.total}条记录</span>
            <ul class="pagination" id="pageCon">

            </ul>
        </div>
        <!--列表翻页 结束-->
    </div>


<%--    <div class="panel-group" id="accordion" style="margin-top: 20px;">--%>
<%--        <form id= "searchForm"  role="form">--%>
<%--            <input id="activityId" name="activityId"  type="hidden"  value="${activityId}" />--%>
<%--            <div class="col-lg-6">--%>
<%--                <label >行政区：</label>--%>
<%--                <input type="text" id="provinceName" name="provinceName" value="${provinceName}" placeholder="所属省">--%>
<%--                <input type="text" id="cityName" name="cityName" value="${cityName}" placeholder="所属市">--%>
<%--            </div>--%>
<%--        </form>--%>
<%--        <div>--%>
<%--            <button class="btn btn-primary" id="filterButton" onclick="filterButton()">筛选</button>--%>
<%--            <button class="btn btn-default" id="resetButton" onclick="resetButton()">重置</button>--%>
<%--            <button type="button" class="btn btn-primary" id ="viewExcel" >导出</button>--%>
<%--        </div>--%>
<%--    </div>--%>


    <div class="mask-layer">
        <div class="loading">抽取中...</div>
    </div>
</div>

</body>
<script>
    //案卷抽取按钮
    function getCaseFile2024(activityId){
        var This = this;
        $.ajax({
            type: "GET",
            // url: WEBPATH+"/filesDrawActivity/getCaseFile2024.do",
            url: WEBPATH+"/filesDrawActivity/getCaseFile2024Down.do",
            data:{activityId:activityId},
            async:true,
            beforeSend:function(){
                console.log("请求开始1")
                This.showMaskLayer();
            },
            success: function(data){
                console.log("案卷抽取按钮",data)
            },
            complete: function() {  // 请求结束后关闭遮罩层
                this.hideMaskLayer();  // 这里的 this 仍然代表的是 jqXHR 对象
                this.goCaseFile2024()
                console.log("请求已结束")
            }.bind(this)  // 使用 bind 将 this 绑定到 cqCaseBaseInfo 对象
        });
    }


    $(function (){
        $('#my_dialog').dialog({
            width : "500",
            height : "450",
            autoOpen : false,
            resizable : false,
            modal : true,
            closed: true,
            saveType: 'insert'
        });
        $('#my_dialog_edit').dialog({
            width : "500",
            height : "450",
            autoOpen : false,
            resizable : false,
            modal : true,
            closed: true,
            saveType: 'insert'
        });
    });
    $(document).ready(function(){
        //绑定主菜单单击方法，设置样式
        $(".ul-clearfix li").bind("click",function(){
            $(".ul-clearfix li a").removeClass("active");
            $(this).children(0).addClass("active");
        });
        //触发点击事件
        $(".ul-clearfix li a:first").click();

    });
    //分页-页面初始化时执行
    $(document).ready(function(){
        var curentPage = eval('${dataList.pageNum}');
        var totalPage = eval('${dataList.pages}');
        if(totalPage>0){
            var options = {
                bootstrapMajorVersion: 3,
                currentPage: curentPage,
                totalPages: totalPage,
                numberOfPages: 5,
                itemTexts: function (type, page, current) {
                    switch (type) {
                        case "first":
                            return "首页";
                        case "prev":
                            return "&laquo;";
                        case "next":
                            return "&raquo;";
                        case "last":
                            return "尾页";
                        case "page":
                            return page;
                    }
                },
                onPageClicked: function (event, originalEvent, type, page) {
                     business.addMainContaierHtml(WEBPATH+'/filesDrawActivity/goAllFiles.do?pageNum='+page,$("#searchForm").serialize());
                }
            };
            $('#pageCon').bootstrapPaginator(options);
        }
    });
    //筛选按钮
    function filterButton() {
        business.addMainContaierHtml(WEBPATH+'/filesDrawActivity/goAllFiles.do',$("#searchForm").serialize());
    }
    //重置按钮
    function resetButton(){
        //所属省
        document.getElementById('provinceName').value = "";
        //所属市
        document.getElementById('cityName').value = "";
        business.addMainContaierHtml(WEBPATH+'/filesDrawActivity/goAllFiles.do',$("#searchForm").serialize());

    }

    //导出按钮
    $("#viewExcel").click(function(){
        var provinceName = $("#provinceName").val();
        var cityName = $("#cityName").val();
        var activityId = $("#activityId").val();

        var path = WEBPATH+"/filesDrawActivity/allFilesExport.do?provinceName="+provinceName+"&cityName="+cityName+"&activityId="+activityId;
        window.location.href=path;
    })

    // 跳转案卷抽取页面(旧)
    function goActivityList(){
        var activityId = $("#activityId").val();
        console.log("跳转案卷,活动id:",activityId)
        macroMgr.onLevelOneMenuClick(null, 'filesDrawActivity/goCqList.do?activityId='+activityId)
    }
    // 跳转抽取结果页面
    function goCaseFile2024(){
        var activityId = $("#activityId").val();
        console.log("跳转案卷,活动id:",activityId)
        macroMgr.onLevelOneMenuClick(null, 'filesDrawActivity/goCaseFile2024.do?activityId='+activityId)
    }
    //返回活动列表页面
    function backActivity(){
        macroMgr.onLevelOneMenuClick(null, 'filesDrawActivity/goActivityList.do')
    }


    //显示遮罩层
    function showMaskLayer() {
        var mask = document.querySelector('.mask-layer');
        var loading = mask.querySelector('.loading');
        mask.style.display = 'block';
        loading.style.display = 'block';

        console.log("遮罩开启")
    }
    //关闭遮罩层
    function hideMaskLayer() {
        console.log("遮罩关闭")
        var mask = document.querySelector('.mask-layer');
        var loading = mask.querySelector('.loading');
        mask.style.display = 'none';
        loading.style.display = 'none';
    }

</script>

<style>
    .center-body {
        width: 75%;
        margin: 0 auto;
        overflow: hidden;
    }
    .head-item {
        width: 1200px;
        height: 45px;
        margin-top: 15px;
    }

    .head-item>ul>li {
        float: left;
    }

    .head-item>ul>li>a {
        display: block;
        margin-right: 15px;
        width: 158px;
        height: 45px;
        background: #3f91c2;
        text-align: center;
        line-height: 45px;
        box-shadow: 0 0 5px #999;
        overflow: hidden;
        border-radius: 8px;
        color: #ffffff;
        font-size: 16px;
    }
    .ul-clearfix {
        *zoom: 1;
        margin-left: 10px;
    }
    .table {
        margin-top: 1px;
    }
    .scroll-container{
        height: 70%;
        width: 95%;/* 设置容器的高度 */
        overflow: auto; /* 自动显示滚动条 */
        margin-left: auto;
        margin-right: auto;
        margin-top: 10px;
    }

    /* 表头首列强制最顶层 */
    /*.scroll-container thead tr > td:first-child {*/
    /*    z-index: 3;*/
    /*}*/
    /* 遮罩层样式 */
    .mask-layer {
        display: none;  /* 初始不显示 */
        position: fixed; /* 固定定位 */
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.6); /* 半透明黑色背景 */
        z-index: 99999; /* 最高层级 */
    }

    /* 加载状态样式 */
    .loading {
        display: none; /* 初始不显示 */
        position: absolute; /* 相对定位 */
        top: 50%; /* 垂直居中 */
        left: 50%; /* 水平居中 */
        transform: translate(-50%, -50%);
        padding: 12px 24px;
        color: #fff;
        font-size: 18px;
        background-color: #333;
        border-radius: 4px;
        z-index: 100000; /* 高于遮罩层层级 */
    }

</style>
</html>
