<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
<script type="text/javascript">
	//分页
	$(document).ready(function(){
		business.listenEnter("searchButt");
		var curentPage = eval('${Unit_PXJGList.pageNum}');
		var totalPage = eval('${Unit_PXJGList.pages}');
		if(totalPage>0){
			var options = {
				bootstrapMajorVersion: 3,
			    currentPage: curentPage,
			    totalPages: totalPage,
			    numberOfPages: 5,
			    itemTexts: function (type, page, current) {
			            switch (type) {
			            case "first":
			                return "首页";
			            case "prev":
			                return "&laquo;";
			            case "next":
			                return "&raquo;";
			            case "last":
			                return "尾页";
			            case "page":
			                return page;
			            }
			    },
			    onPageClicked: function (event, originalEvent, type, page) {
	            	business.addMainContentParserHtml(WEBPATH+'/pxjg/provinceGroup.do?pageNum='+page,$("#searchForm").serialize());
	            }
	    	};
	    	$('#pageCon').bootstrapPaginator(options);
    	}
	});
	
	$(document).ready(function(){
		$("#province").change(function(){
			business.addMainContentParserHtml("pxjg/provinceGroup.do",$("#searchForm").serialize());
		});
		//导出Excel表格
		
		$("#viewExcel").click(function(){

			window.location.href= WEBPATH+'/pxjg/downPxjgGroupExcel.do?areaType=1';
		});
		
	});
</script>
</head>

<body>
<!-- 页面显示内容-开始 -->
 <div class="center_weizhi">当前位置：评选结果 - 先进集体评选 - 省级评选结果</div>
<div class="center">
<div class="center_list">
    <div class="panel-group" id="accordion">
          <!---快速查询--->
          <form id="searchForm" name="searchForm" role="form">
              <select id="province" name="province"  class="form-control" style="width:120px;margin-right:5px;">
                  <option value="" <c:if test="${areaCode=='' }">selected</c:if>>省（直辖市）</option>
	              <c:forEach items="${electionUnits }" var="electionUnit">
	              <option value="${electionUnit.areacode }" <c:if test="${areaCode==electionUnit.areacode }">selected</c:if> >${electionUnit.areaname }</option>
	              </c:forEach>
              </select>
        
          
            <!---导出--->
            <c:if test="${fn:length(Unit_PXJGList.list)>0 }">
            <div style="width:260px;" class="btn-group">
            	<a href ="#"><button type="button" id ="viewExcel" class="btn btn-primary">导出EXCEL</button></a>  
            </div>
            </c:if>          
          </form>
    </div>
        <table class="table table-bordered table-hover table-condensed">
         <thead>
           <tr>
             <td width="50" height="30" bgcolor="#efefef">序号</td>
             <td bgcolor="#efefef">省</td>
             <td width="100" bgcolor="#efefef">质量得分</td>
             <td width="100" bgcolor="#efefef">数量得分</td>
             <td width="120" bgcolor="#efefef">公众投票得分</td>
             <td width="100" bgcolor="#efefef">分数</td>
             <td width="100" bgcolor="#efefef">排名</td>
             <td width="100" bgcolor="#efefef">备注</td>
           </tr>
         </thead>
         <tbody>
           <c:forEach items="${Unit_PXJGList.list}" var="ElectionUnit_PXJG" varStatus="sta">
           <tr>
             <td height="30" align="center">${sta.index+1}</td>
             <td>${ElectionUnit_PXJG.areaname }</td>
             <td>${ElectionUnit_PXJG.qualityfiletotalscore }</td>
             <td>${ElectionUnit_PXJG.numberfinalscore }</td>
             <td>${ElectionUnit_PXJG.publicvotenumscore }</td>
             <td>${ElectionUnit_PXJG.totalscore }</td>
             <td>
             <c:choose>
             	<c:when test="${ElectionUnit_PXJG.ranking !='' && ElectionUnit_PXJG.ranking !=null }">
             		第${ElectionUnit_PXJG.ranking }名
             	</c:when>
             	<c:otherwise>
             	</c:otherwise>
             </c:choose>
             </td>
             <td style="color:red"><c:choose><c:when test="${ElectionUnit_PXJG.isyipiaofoujue =='1' }">一票否决</c:when><c:otherwise></c:otherwise> </c:choose></td>
           </tr>
           </c:forEach>
           
           
         </tbody>
       </table>
    </div>
    <div class="page">
        <span style="padding:12px; float:left; color:#0099cc;">共${Unit_PXJGList.total}条记录</span>
        <ul class="pagination" id="pageCon">
            
        </ul>
    </div>
</div>
<!-- 页面显示内容-结束 -->
</body>
</html>