<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
</head>

<body>
<div class="center_weizhi">当前位置：专家排名 - 专家先进集体评选 - 市级专家案卷评分</div>
<div class="center">
<div class="center_list">
    <div class="panel-group" id="accordion">
          <h4 class="panel-title">
            <div style="width:260px;" class="btn-group">
                    <a href ="#"><button type="button" id ="viewExcel" class="btn btn-primary">导出EXCEL</button></a>  
            </div>  
          </h4>
          <br/>
    </div>
        <table class="table table-bordered table-hover table-condensed">
         <thead>
           <tr>
             <td width="50" bgcolor="#efefef">序号</td>
             <td width="200" bgcolor="#efefef">省</td>
             <td width="200" bgcolor="#efefef">地市</td>
             <!-- <td width="200" bgcolor="#efefef">区县</td> -->
             <td width="150" height="30" bgcolor="#efefef">应参评案卷数量</td>
             <td width="150" height="30" bgcolor="#efefef">实际参评案卷数量</td>
             <td width="150" bgcolor="#efefef">专家案卷最终得分</td>
             <!-- <td width="150" bgcolor="#efefef">专家平均分</td> -->
             <td width="150" bgcolor="#efefef">专家排名</td>
           </tr>
         </thead>
         <tbody>
            <c:forEach items="${zlpfJitiBeanList.list}" var="item" varStatus="status">
	           <tr>
	             <td height="30" align="center">${status.index+1}<input id="id${status.index+1}" type="hidden"  value="${item.id}"></td>
	             <td>${item.province}</td>
	             <td>${item.city}</td>
	             <%-- <td>${item.country}</td> --%>
	             <td>${item.standardfilenumber}</td>
	             <td>${item.qualityfilenumber}</td>
				 <td>${item.expertTotalScore}</td>
	             <%-- <td>${item.crossaveragescore}</td> --%>
	             <td>
		             <c:choose>
		             	<c:when test="${item.expertRanking !='' && item.expertRanking !=null }">
		             		第${item.expertRanking }名
		             	</c:when>
		             	<c:otherwise>
		             	</c:otherwise>
		             </c:choose>
		         </td>
	           </tr>
           </c:forEach>
         </tbody>
       </table>
    </div>
     <!--列表翻页 开始-->
    <div class="page">
    	<span style="padding:12px; float:left; color:#0099cc;">共${zlpfJitiBeanList.total}条记录</span>
        <ul class="pagination" id="pageCon">
            
        </ul>
    </div>
    <!--列表翻页 结束-->
</div>
<script type="text/javascript">
// 导出表格
$(document).ready(function(){
	$("#viewExcel").click(function(){
	  var path = WEBPATH+"/ajpfViewExcel.do?target=10&viewType=2"; 
      window.location.href=path;
	});
});
//分页
$(document).ready(function(){
	var curentPage = eval('${zlpfJitiBeanList.pageNum}');
	var totalPage = eval('${zlpfJitiBeanList.pages}');
	var areaType = eval('${areaType}');
	if(totalPage>0){
		var options = {
			bootstrapMajorVersion: 3,
		    currentPage: curentPage,
		    totalPages: totalPage,
		    numberOfPages: 5,
		    itemTexts: function (type, page, current) {
		            switch (type) {
		            case "first":
		                return "首页";
		            case "prev":
		                return "&laquo;";
		            case "next":
		                return "&raquo;";
		            case "last":
		                return "尾页";
		            case "page":
		                return page;
		            }
		    },
		    onPageClicked: function (event, originalEvent, type, page) {
            	business.addMainContentParserHtml(WEBPATH+'/jcpm/goZjpmJt.do?areaType='+areaType+'&pageNum='+page,null);
            }
    	};
    	$('#pageCon').bootstrapPaginator(options);
   	}
});
</script>
</body>
</html>