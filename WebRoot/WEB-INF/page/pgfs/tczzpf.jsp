<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
 
<html>
<head>
<script language="JavaScript">
// 初始化
$(document).ready(function(){
	/* $("#viewExcel").click(function(){
		var areaCodeLeave = $("#areaCodeLeave").val();
		var isConsiderCross = $("#isConsiderCrossSelect").val();
		var fileCode = $("#fileCode").val();
	  var path = WEBPATH+"/crossListExportExcel.do?target=1&viewType=2&fileCode="+fileCode+"&isConsiderCross="+isConsiderCross+"&areaCodeLeave="+areaCodeLeave; 
      window.location.href=path;
	}); */
	
	$("#viewExcel").click(function(){
		window.location.href= WEBPATH+'/pgfs/zhpgExcle.do';
	});
	
	$("#tczzForm").formValidation({
		   framework: 'bootstrap',
	     message: 'This value is not valid',
	     icon:{
		            valid: 'glyphicon glyphicon-ok',
		            invalid: 'glyphicon glyphicon-remove',
		            validating: 'glyphicon glyphicon-refresh'
	     },
	     fields: {
	     	"prefourscore": {
	             validators: {
	                 callback: {
	                     callback: function (value, validator, $field) {
	                  		   if($("#isHeYiSave").val()=="1"){
	                  			   return {
	                      			   notEmpty: {
		           		                        message: '卷内目录得分不能为空!'
		           		                    },
		           		                    regexp: {
		           		                        regexp:  /(?!^0\.0?0$)^[0-70](\.[0-9]{1,2})?$|^(2|2\.0|2\.00|0\.0|0\.00)$/,
		           		                        message: '请输入0-70之间的整数或者包含2位小数!'
		           		                    }
	                      			   }
	                      		   
	                      	   }else{
	                      		   return true  
	                      		   }
	                      	   }   
	                     }
	             }
	         },
	      	 "selectedpersonScore": {
	             validators: {
	                 notEmpty: {
	                     message: '候选集体得分不能为空!'
	                 },
	                 regexp: {
	                     regexp: /(?!^0\.0?0$)^[0-5](\.[0-9]{1,2})?$|^(8|8\.0|8\.00|0\.0|0\.00)$/,
	                     message: '请输入0-5之间的整数或者包含2位小数!'
	                 }
	             }
	         },
	      	"selectedunitScore": {
	             validators: {
	                 notEmpty: {
	                     message: '候选个人得分不能为空！'
	                 },
	                 regexp: {
	                     regexp:  /(?!^0\.0?0$)^([0-5])(\.[0-9]{1,2})?$|^(9|9\.0|9\.00|0\.0|0\.00)$/,
	                     message: '请输入0-5之间的整数或者包含2位小数!'
	                 }
	             }
	         },
	      	"candidatepersonScore": {
	             validators: {
	                 notEmpty: {
	                     message: '入选集体得分不能为空！'
	                 },
	                 regexp: {
	                     regexp: /(?!^0\.0?0$)^([0-10])(\.[0-9]{1,2})?$|^(8|8\.0|8\.00|0\.0|0\.00)$/,
	                     message: '请输入0-10之间的整数或者包含2位小数!'
	                 }
	             }
	         },
	         "candidateunitScore": {
	             validators: {
	                 notEmpty: {
	                     message: '入选个人得分不能为空！'
	                 },
	                 regexp: {
	                     regexp: /(?!^0\.0?0$)^([0-10])(\.[0-9]{1,2})?$|^(8|8\.0|8\.00|0\.0|0\.00)$/,
	                     message: '请输入0-10之间的整数或者包含2位小数!'
	                 }
	             }
	         }
	     }
	});
	
});
var pageNum=$("#pageNum").val();
//分页
$(document).ready(function(){
	var curentPage = eval('${zhpgExtrudeScoreList.pageNum}');
	var totalPage = eval('${zhpgExtrudeScoreList.pages}');
	if(totalPage>0){
		var options = {
			bootstrapMajorVersion: 3,
		    currentPage: curentPage,
		    totalPages: totalPage,
		    numberOfPages: 5,
		    itemTexts: function (type, page, current) {
		            switch (type) {
		            case "first":
		                return "首页";
		            case "prev":
		                return "&laquo;";
		            case "next":
		                return "&raquo;";
		            case "last":
		                return "尾页";
		            case "page":
		                return page;
		            }
		    },
		    onPageClicked: function (event, originalEvent, type, page) {
		    	$("#pageNum").val(page);
            	business.addMainContentParserHtml(WEBPATH+'/pgfs/pgfs.do?pageNum='+page,$("#tczzForm").serialize());
            }
    	};
    	$('#pageCon').bootstrapPaginator(options);
   	}
});


$(document).ready(function() {
	$(".topnav").accordion({
		accordion:false,
		speed: 500,
		closedSign: '[+]',
		openedSign: '[-]'
	});
});


//保存按钮
function save(coordinate){
	var id=$("#id"+coordinate).val();
	var prefourscore=$("#prefourscore"+coordinate).val();
	if(prefourscore==null || prefourscore==undefined){
		prefourscore="";
	}
	var candidateunitScore=$("#candidateunitScore"+coordinate).val();
	var candidatepersonScore=$("#candidatepersonScore"+coordinate).val();
	var selectedunitScore=$("#selectedunitScore"+coordinate).val();
	var selectedpersonScore=$("#selectedpersonScore"+coordinate).val();
	
	var regexpChFive = /(?!^0\.0?0$)^([0-4])(\.[0-9]{1,2})?$|^(5|5\.0|5\.00|0\.0|0\.00)$/;
	var regexpChTen =  /(?!^0\.0?0$)^([0-9])(\.[0-9]{1,2})?$|^(10|10\.0|10\.00|0\.0|0\.00)$/;
	var regexpCh =     /(?!^0\.0?0$)^[0-6][0-9]?(\.[0-9]{1,2})?$|^(70|70\.0|70\.00|0\.0|0\.00)$/;
	var flag=0;
		if(prefourscore!=null && prefourscore!=undefined && prefourscore!=""){
			if(!regexpCh.test(prefourscore))
			{
				swal("提示", "前四项指标得分分值范围在0-70，可输入整数和小数，小数部分最多保留2位!", "info");
				flag=1;
				return false;
			}
		}
		if(!regexpChFive.test(candidateunitScore))
		{
			swal("提示", "候选集体分值范围在0-5，可输入整数和小数，小数部分最多保留2位!", "info");
			flag=1;
			return false;
		}
		if(!regexpChFive.test(candidatepersonScore))
		{
			swal("提示", "候选个人分值范围在0-5，可输入整数和小数，小数部分最多保留2位!", "info");
			flag=1;
			return false;
		}
		if(!regexpChTen.test(selectedunitScore))
		{
			swal("提示", "入选集体分值范围在0-10，可输入整数和小数，小数部分最多保留2位!", "info");
			flag=1;
			return false;
		}
		if(!regexpChTen.test(selectedpersonScore))
		{
			swal("提示", "入选个人分值范围在0-10，可输入整数和小数，小数部分最多保留2位!", "info");
			flag=1;
			return false;
		}
		//计算总分
		if(prefourscore!=""){
			var finalscore=parseFloat(prefourscore)+parseFloat(candidateunitScore)+
			parseFloat(candidatepersonScore)+parseFloat(selectedunitScore)+parseFloat(selectedpersonScore);
		}else{
			var finalscore=parseFloat(candidateunitScore)+
			parseFloat(candidatepersonScore)+parseFloat(selectedunitScore)+parseFloat(selectedpersonScore);
		}
		var options = {
			type: "POST",
	        url: WEBPATH+"/pgfs/savePgfs.do",
	        data:{
	        		id:id,
	        		prefourscore:prefourscore,
	        		candidateunitScore:candidateunitScore,
	        		candidatepersonScore:candidatepersonScore,
	        		selectedunitScore:selectedunitScore,
	        		selectedpersonScore:selectedpersonScore,
	        		finalscore:finalscore
	        	},
	        async:false,
	        success: function(data){
	              if(data.result=="error"){
	               	swal({title: data.message ,text: "",type:"error"});
	                return false;
	             }else if(data.result=="success"){
	              	swal({title: data.message ,text: "",type:"success"});
	              	business.addMainContentParserHtml(WEBPATH+'/pgfs/pgfs.do?pageNum='+pageNum,null);
	              	//business.addMainContentParserHtml(WEBPATH+'/ajpfCross.do?pageNum='+pageNum+'&areaType='+areaTypeVal+'&isConsiderCross='+isConsiderCrossVal+'&fileCode='+fileCodeVal,null);
	             } 
	      }
	       	 };
	/* $("#tczzForm").data('formValidation').validate();
   	var validate = $("#tczzForm").data('formValidation').isValid(); */
   	if(flag==0){
   	 	$('#tczzForm').ajaxSubmit(options);
   	}else{
   		return false;
   	}
};

//合议按钮
function heyiBtn(coordinate){
	$("#cancel"+coordinate).css('display','block'); 
	$("#heyiSave"+coordinate).css('display','block'); 
	$("#heyi"+coordinate).hide();
	$("#prefourscore"+coordinate).css('display','block'); 
	$("#prefourscoreMSG"+coordinate).css('display','block'); 
	
}
//取消按钮,显示合议按钮
function heyiCancelBtn(coordinate){
	$("#cancel"+coordinate).hide();
	$("#heyiSave"+coordinate).hide();
	$("#heyi"+coordinate).show();
	$("#prefourscore"+coordinate).hide();
	$("#prefourscoreMSG"+coordinate).hide();
}
//合议保存
function heyiSaveBtn(coordinate){
	$("#isHeYiSave"+coordinate).val("1");//将是否合议状态改为1
	var prefourscore=$("#prefourscore"+coordinate).val();
	//前四项不输入的情况下不允许保存
	if(prefourscore!="" && prefourscore!=null && prefourscore!=undefined){
				var id=$("#id"+coordinate).val();
				var prefourscore=$("#prefourscore"+coordinate).val();
				var candidateunitScore=$("#candidateunitScore"+coordinate).val();
				var candidatepersonScore=$("#candidatepersonScore"+coordinate).val();
				var selectedunitScore=$("#selectedunitScore"+coordinate).val();
				var selectedpersonScore=$("#selectedpersonScore"+coordinate).val();
				 //验证公式
				var regexpChFive = /(?!^0\.0?0$)^([0-4])(\.[0-9]{1,2})?$|^(5|5\.0|5\.00|0\.0|0\.00)$/;
				var regexpChTen =  /(?!^0\.0?0$)^([0-9])(\.[0-9]{1,2})?$|^(10|10\.0|10\.00|0\.0|0\.00)$/;
				var regexpCh =     /(?!^0\.0?0$)^[0-6][0-9]?(\.[0-9]{1,2})?$|^(70|70\.0|70\.00|0\.0|0\.00)$/;
				var flag=0;
						if(!regexpCh.test(prefourscore))
						{
							swal("提示", "前四项指标得分分值范围在0-70，可输入整数和小数，小数部分最多保留2位!", "info");
							flag=1;
							return false;
						}
					//计算总分
						var finalscore=parseFloat(prefourscore);//前四项得分
						if(candidateunitScore!="" && candidateunitScore!=null && candidateunitScore!=undefined){
							if(!regexpChFive.test(candidateunitScore))
							{
								swal("提示", "候选集体分值范围在0-5，可输入整数和小数，小数部分最多保留2位!", "info");
								flag=1;
								return false;
							}
							finalscore=finalscore+parseFloat(candidateunitScore);
						}
						if(candidatepersonScore!="" && candidatepersonScore!=null && candidatepersonScore!=undefined){
							if(!regexpChFive.test(candidatepersonScore))
							{
								swal("提示", "候选个人分值范围在0-5，可输入整数和小数，小数部分最多保留2位!", "info");
								flag=1;
								return false;
							}
							finalscore=finalscore+	parseFloat(candidatepersonScore);				
												}
						if(selectedunitScore!="" && selectedunitScore!=null && selectedunitScore!=undefined){
							if(!regexpChTen.test(selectedunitScore))
							{
								swal("提示", "入选集体分值范围在0-10，可输入整数和小数，小数部分最多保留2位!", "info");
								flag=1;
								return false;
							}
							finalscore=finalscore+	parseFloat(selectedunitScore);	
						}
						if(selectedpersonScore!="" && selectedpersonScore!=null && selectedpersonScore!=undefined){
							if(!regexpChTen.test(selectedpersonScore))
							{
								swal("提示", "入选个人分值范围在0-10，可输入整数和小数，小数部分最多保留2位!", "info");
								flag=1;
								return false;
							}
							finalscore=finalscore+	parseFloat(selectedpersonScore);	
						}
				var isheyi=2;
				$("#cancel"+coordinate).hide();//隐藏取消按钮
				var options = {
						type: "POST",
				        url: WEBPATH+"/pgfs/savePgfs.do",
				        data:{
				        		id:id,
				        		prefourscore:prefourscore,
				        		candidateunitScore:candidateunitScore,
				        		candidatepersonScore:candidatepersonScore,
				        		selectedunitScore:selectedunitScore,
				        		selectedpersonScore:selectedpersonScore,
				        		finalscore:finalscore,
				        		isheyi:isheyi
				        	},
				        async:false,
				        success: function(data){
				              if(data.result=="error"){
				               	swal({title: data.message ,text: "",type:"error"});
				                return false;
				             }else if(data.result=="success"){
				              	swal({title: data.message ,text: "",type:"success"});
				              	business.addMainContentParserHtml(WEBPATH+'/pgfs/pgfs.do?pageNum='+pageNum,$("#tczzForm").serialize());
				              	//business.addMainContentParserHtml(WEBPATH+'/ajpfCross.do?pageNum='+pageNum+'&areaType='+areaTypeVal+'&isConsiderCross='+isConsiderCrossVal+'&fileCode='+fileCodeVal,null);
				             } 
				      }
				       	 };
				/* $("#tczzForm").data('formValidation').validate();
			   	var validate = $("#tczzForm").data('formValidation').isValid(); */
			   	if(flag==0){
			   	 	$('#tczzForm').ajaxSubmit(options);
			   	}else{
			   		return false;
			   	}
	}else{
		swal({title: "保存失败" ,text: "前四项指标得分不能为空!",type:"error"});
	}
}

function selectAllScore(coordinate,type){
	var unitid=$("#unitid"+coordinate).val();
	var crossId="";
	if(type==1){
		crossId=$("#crossuserid"+coordinate).val();
	}else if(type==2){
		crossId=$("#crossuserBid"+coordinate).val();
	}
	var view=1;
	business.addMainContentParserHtml(WEBPATH+'/pgfs/toUnitScore.do?tczzpageNum='+pageNum+'&view='+view
						+'&unitid='+unitid+'&crossId='+crossId,$("#tczzForm").serialize());
}

</script>
</head>

<body>
<div class="center_weizhi">当前位置：评估分数 - 突出组织评分结果 - 突出组织最终评分列表  </div>
<div class="center">
<div class="center_list">
   		<div class="panel-group" id="accordion">
			<button type="button" class="btn btn-primary" id="viewExcel">导出EXCEL</button>
    	</div>
    	<input type="hidden" id="pageNum" value="${zhpgExtrudeScoreList.pageNum}"/>
        <table class="table table-bordered table-hover table-condensed">
         <thead>
           <tr>
             <td width="50" rowspan="3" bgcolor="#efefef" style="text-align: center;vertical-align: middle;">序号</td>
             <td rowspan="3" bgcolor="#efefef" style="text-align: center;vertical-align: middle;">省</td>
             <td width="120" rowspan="3" bgcolor="#efefef" style="text-align: center;vertical-align: middle;">评分人员A姓名</td>
             <td width="120" rowspan="3" bgcolor="#efefef" style="text-align: center;vertical-align: middle;">初评得分</td>
             <td width="120" rowspan="3" bgcolor="#efefef" style="text-align: center;vertical-align: middle;">评分人员B姓名</td>
             <td width="120" rowspan="3" bgcolor="#efefef" style="text-align: center;vertical-align: middle;">初评得分</td>
             <td width="120" rowspan="3" bgcolor="#efefef" style="text-align: center;vertical-align: middle;">前四项指标得分</td>
             <td height="30" colspan="4" bgcolor="#efefef" style="text-align: center;vertical-align: middle;">练兵成绩</td>
             <td width="120" height="30" rowspan="3" bgcolor="#efefef" style="text-align: center;vertical-align: middle;">最终评审得分</td>
             <td width="120" rowspan="3" bgcolor="#efefef" style="text-align: center;vertical-align: middle;">操作</td>
           </tr>
           <tr>
             <td height="30" colspan="2" bgcolor="#efefef" style="text-align: center;vertical-align: middle;">推荐数量</td>
             <td colspan="2" bgcolor="#efefef" style="text-align: center;vertical-align: middle;">表现突出数量比例</td>
           </tr>
           <tr>
             <td width="120" height="30" bgcolor="#efefef" style="text-align: center;vertical-align: middle;">候选集体<br/><span style="color:red; font-size: 12px;">分值范围在0-5</span></td>
             <td width="120" bgcolor="#efefef" style="text-align: center;vertical-align: middle;">候选个人<br/><span style="color:red; font-size: 12px;">分值范围在0-5</span></td>
             <td width="120" bgcolor="#efefef" style="text-align: center;vertical-align: middle;">入选集体<br/><span style="color:red; font-size: 12px;">分值范围在0-10</span></td>
             <td width="120" bgcolor="#efefef" style="text-align: center;vertical-align: middle;">入选个人<br/><span style="color:red; font-size: 12px;">分值范围在0-10</span></td>
           </tr>
         </thead>
         <tbody>
          <c:forEach items="${zhpgExtrudeScoreList.list}" var="item" varStatus="status">
          	<form id ="tczzForm">
		      <tr>
		      	 <td height="30" align="center" style="vertical-align: middle;">${status.index+1 }
		      	 	<input id="isheyi${status.index+1}" type="hidden"  value="${item.isheyi}">
		      	 	<input id="id${status.index+1}" type="hidden"  value="${item.id}">
		      	 	<input id="isHeYiSave${status.index+1}" type="hidden">
		      	 	<input id="crossuserid${status.index+1}" type="hidden"  value="${item.crossuserid}">
		      	 	<input id="crossuserBid${status.index+1}" type="hidden"  value="${item.crossuserBid}">
		      	 	<input id="unitid${status.index+1}" type="hidden"  value="${item.unitid}">
		      	 </td>
		      	 <td style="vertical-align: middle;">${item.unitname}</td>
		      	 <td style="vertical-align: middle;width=120;">${item.namea}</td>
		      	 <td style="vertical-align: middle;width=120;">
					<a href="javascript:void(0);" onclick="selectAllScore('${status.index+1 }',1)">  
						${item.nameaScore}
					</a>
				 </td>
		      	 <td style="vertical-align: middle;width=120;">${item.nameb}</td>
		      	 <td style="vertical-align: middle;width=120;">
		      	 	<a href="javascript:void(0);" onclick="selectAllScore('${status.index+1 }',2)">
		      	 		${item.namebScore}
		      	 	</a>
		      	 </td>
			     <td width="120" height="30" style="vertical-align:middle;" align="center" >
				    <c:choose>
				       <c:when test="${item.isheyi!='1'}">
				       		${item.prefourscore}
				       		<input id="prefourscore${status.index+1}" type="hidden" value="${item.prefourscore}">
				       </c:when>
				       <c:otherwise>
				      		<input id="prefourscore${status.index+1}" style="display:none;" class="form-control" placeholder="分数" name="prefourscore">
				      		<span id="prefourscoreMSG${status.index+1}" style="display:none;color:red; font-size: 12px;">分值范围在0-70</span>
				       </c:otherwise>
					</c:choose>
			     </td>
			     <td width="120" height="30" align="center">
				     <input id="candidateunitScore${status.index+1}" type="text" class="form-control" placeholder="分数" name="candidateunitScore" value="${item.candidateunitScore}">
			     </td>
			    <td width="120" height="30" align="center">
				     <input id="candidatepersonScore${status.index+1}" type="text" class="form-control" placeholder="分数" name="candidatepersonScore" value="${item.candidatepersonScore}">
			     </td>
			    <td width="120" width="80" height="30" align="center">
				     <input id="selectedunitScore${status.index+1}" type="text" class="form-control" placeholder="分数" name="selectedunitScore" value="${item.selectedunitScore}">
			     </td>
			    <td width="120" height="30" align="center">
				     <input id="selectedpersonScore${status.index+1}" type="text" class="form-control" placeholder="分数" name="selectedpersonScore" value="${item.selectedpersonScore}">
			     </td>
			     <td width="120" align="center" style="vertical-align:middle;">${item.finalscore}</td>
			     <td width="120" align="center" style="vertical-align:middle;">	
			     	<c:choose>
				       <c:when test="${item.isheyi!='1'}">
				       		<c:if test="${item.prefourscore!=null }">
				       			<button type="button" class="btn btn-primary btn-sm" onclick="save('${status.index+1 }')">保存</button>
				       		</c:if>
				       </c:when>
				       <c:otherwise>
				      		<button type="button" class="btn btn-danger btn-sm"  onclick="heyiBtn('${status.index+1 }')" id="heyi${status.index+1}">合议</button>
				      			<button type="button" style="display:none;float:left;" onclick="heyiSaveBtn('${status.index+1 }')" id="heyiSave${status.index+1}" class="btn btn-primary btn-sm">保存</button>
				       			<button type="button"  style="display:none;float:right;" onclick="heyiCancelBtn('${status.index+1 }')" id="cancel${status.index+1}" class="btn btn-info btn-sm">取消</button>
				       </c:otherwise>
					</c:choose>						 
			     </td>
			  </tr>
          	</form>
	      </c:forEach>
         </tbody>
       </table>
    </div>
    <!--列表翻页 开始-->
    <div class="page">
    	<span style="padding:12px;  float:left; color:#0099cc;">共${zhpgExtrudeScoreList.total}条记录</span>
        <ul class="pagination" id="pageCon">
            
        </ul>
    </div>
    <!--列表翻页 结束-->
</div>

</body>
</html>
