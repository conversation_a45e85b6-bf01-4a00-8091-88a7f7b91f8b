<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
</head>
<body>
<div class="center_weizhi">当前位置：专家评分 - 专家评审案卷 - 稽查案卷</div>
<div class="center">
<div class="center_list">
		<div class="dingwei">
			决定书文号：<span id="filename" style="color:#06C;font-size:16px;">${fileCode}</span>
		</div>
        <table class="table table-bordered table-hover table-condensed">
         <thead>
           <tr>
             <td width="50" height="30" bgcolor="#efefef">序号</td>
             <td bgcolor="#efefef">分指标</td>
             <td width="150" bgcolor="#efefef">分指标分值</td>
             <td width="150" bgcolor="#efefef">初评得分</td>
           </tr>
         </thead>
         <tbody>
           <tr>
             <td height="30" align="center">1</td>
             <td>法律适用 </td>
             <td>44</td>
             <td>
             <div class="form-group">
                 <div class="col-sm-12">
                 <label>${expertHandlFileList.falushiyongscore }</label>
           		</div>
           		</div>
           		</td>
           </tr>
           <tr>
             <td height="30" align="center">2</td>
             <td>稽查程序</td>
             <td>56</td>
             <td>
             <div class="form-group">
                 <div class="col-sm-12">
                 <label>${expertHandlFileList.jichachengxuscore }</label>
          		</div>
          		</div>
          		</td>
           </tr>
              <tr>
             <td height="30" colspan="2" align="center">小计</td>
             <td>100</td>
             <td><span id ="expertfinalscore1">&nbsp;&nbsp;&nbsp;&nbsp;${expertHandlFileList.expertfinalscore}</span></td>
           </tr>
           <tr>
             <td height="30" colspan="4">专家评语
             <div class="form-group">
                 <div class="col-sm-12">
             <textarea  rows="5" class="form-control" readonly="readonly"  id="expertreviews" name ="expertreviews"  placeholder="请输入专家评语">${expertHandlFileList.expertreviews }</textarea>
         		</div>
         		</div>
         		</td>
           </tr>
           
         </tbody>
       </table>
 	 <div class="submit"><a href="#"><button type="button" class="btn btn-primary" id="submitBaoChong" name="signup" value="Sign up" style="font-size:16px;width:150px; margin-top:5px;">返回</button></a></div>
</div>
</div> 
	<form action="" id="searchForm">
		<input name="areaType" type="hidden" value="${areaType}"/>
		<input name="isConsider" type="hidden" value="${isConsider}"/>
		<input name="isConsiderCross" type="hidden" value="${isConsiderCross}"/>
		<input name="fileCode" type="hidden" value="${fileCodeBck}"/>
		<input name="pageNum" type="hidden" value="${pageNum}"/>
		<input name="fileType" type="hidden" value="${fileType}"/>
	</form>
	<script type="text/javascript">
		$(document).ready(function(){
			$("#submitBaoChong").click(function(){
				if('${expertOrCrossType}' == 1){
					business.addMainContentParserHtml("anjuanList.do?pageIndex="+'${pageIndex}',$("#searchForm").serialize());
				}else{
					business.addMainContentParserHtml("ajpfCross.do",$("#searchForm").serialize());
				}
			});
		});
	</script>
</body>
</html>