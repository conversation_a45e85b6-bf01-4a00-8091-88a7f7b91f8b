<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
</head>
<body>
<div class="center_weizhi">当前位置：交叉评分 - 交叉评分结果 - 交叉案卷评分 - 发现问题的污染源现场监督检查稽查案卷</div>
<div class="center">
<div class="center_list">
	<div class="dingwei">
		决定书文号：<span id="filename" style="color:#06C;font-size:16px;">${fileCode}</span>
	</div>
    <table class="table table-bordered table-hover table-condensed">
      <thead>
           <tr>
             <td width="50" height="30" bgcolor="#efefef">序号</td>
             <td bgcolor="#efefef">分指标</td>
             <td width="150" bgcolor="#efefef">分指标分值</td>
             <td width="150" bgcolor="#efefef">初评得分</td>
           </tr>
         </thead>
          <tbody>
           <tr>
             <td height="30" align="center">1</td>
             <td>卷内目录</td>
             <td>5</td>
             <td>
              <div class="form-group">
                 <div class="col-sm-12">
            	<label> ${crossHandlFileList.juanneimuluscore }</label>
          		</div>
          		</div>
             </td>
           </tr>
           <tr>
             <td height="30" align="center">2</td>
             <td>立案审批表或相关公文</td>
             <td>8</td>
             <td>
              <div class="form-group">
                 <div class="col-sm-12">
            <label>${crossHandlFileList.lianshenpibiaoscore }</label>
           		</div>
           		</div>
             </td>
           </tr>
           <tr>
              <tr>
             <td height="30" align="center">3</td>
             <td>稽查通知书</td>
             <td>10</td>
             <td>
              <div class="form-group">
                 <div class="col-sm-12">
             <label>${crossHandlFileList.jichatongzhishuscore }</label>
           		</div>
           		</div>
             </td>
           </tr>
           <tr>
             <td height="30" align="center">4</td>
             <td>询问笔录</td>
             <td>12</td>
             <td>
              <div class="form-group">
                 <div class="col-sm-12">
             <label>${crossHandlFileList.xunwenbiluscore }</label>
           		</div>
           		</div>
             </td>
           </tr>
           <tr>
             <td height="30" align="center">5</td>
             <td>稽查记录</td>
             <td>12</td>
             <td>
             	 <div class="form-group">
                 <div class="col-sm-12">
             <label>${crossHandlFileList.jichajiluscore }</label>
				</div></div>
             </td>
           </tr>
           <tr>
             <td height="30" align="center">6</td>
             <td>被稽查单位污染源现场监察记录</td>
             <td>15</td>
             <td>
              <div class="form-group">
                 <div class="col-sm-12">
             <label>${crossHandlFileList.jichadqzlqingdanscore }</label>
           		</div></div>
             </td>
           </tr>
           <tr>
             <td height="30" align="center">7</td>
             <td>稽查报告书</td>
             <td>15</td>
             <td>
              <div class="form-group">
                 <div class="col-sm-12">
             <label>${crossHandlFileList.jichabgshuscore }</label>
           		</div></div>
             </td>
           </tr>
           <tr>
             <td height="30" align="center">8</td>
             <td>稽查意见书</td>
             <td>15</td>
             <td>
              <div class="form-group">
                 <div class="col-sm-12">
            <label>${crossHandlFileList.jichayijianshuscore }</label>
             	</div></div>
             </td>
          	
           </tr>
           <tr>
             <td height="30" align="center">9</td>
             <td>整改报告</td>
             <td>8</td>
             <td>
              <div class="form-group">
                 <div class="col-sm-12">
            <label>${crossHandlFileList.zhenggaibgscore }</label>
             </div></div>
             </td>
           </tr>
           <tr>
             <td height="30" colspan="2" align="center">小计</td>
             <td>100</td>
             <td><label>${crossHandlFileList.expertfinalscore}</label></td>
           </tr>
          </tbody>
       </table>
    <div class="submit"><a href="#"><button type="submit" id="submitBaoChong" class="btn btn-primary" name="signup" value="Sign up" style="font-size:16px;width:150px; margin-top:5px;">返回</button></a></div>
</div>
</div>
	<form action="" id="searchForm">
		<input name="areaType" type="hidden" value="${areaType}"/>
		<input name="isConsider" type="hidden" value="${isConsiderCross}"/>
		<input name="isConsiderCross" type="hidden" value="${isConsider}"/>
		<input name="fileCode" type="hidden" value="${fileCodeBck}"/>
		<input name="pageNum" type="hidden" value="${pageNum}"/>
	</form>
	<script type="text/javascript">
		$(document).ready(function(){
			$("#submitBaoChong").click(function(){
				if('${expertOrCrossType}' == 1){
					business.addMainContentParserHtml("anjuanList.do?pageIndex="+'${pageIndex}',$("#searchForm").serialize());
				}else{
					business.addMainContentParserHtml("ajpfCross.do",$("#searchForm").serialize());
				}
			});
		});
	</script>
</body>
</html>
