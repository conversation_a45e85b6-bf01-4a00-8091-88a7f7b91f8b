<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
</head>
 
<body>
<div class="center_weizhi">当前位置：专家评分 - 专家评审案卷 - 申请法院强制执行案卷</div>
<div class="center">
<div class="center_list">
			<div class="dingwei">
				决定书文号：<span id="filename" style="color:#06C;font-size:16px;">${fileCode}</span>
			</div>
        <table class="table table-bordered table-hover table-condensed">
         <thead>
           <tr>
             <td width="50" height="30" bgcolor="#efefef">序号</td>
             <td bgcolor="#efefef">分指标</td>
             <td width="150" bgcolor="#efefef">分指标分值</td>
             <td width="150" bgcolor="#efefef">初评得分</td>
              <td bgcolor="#efefef">评审依据</td>
           </tr>
         </thead>
         <tbody>
            <tr>
             <td height="30" align="center">1</td>
             <td>申请强制执行依据 </td>
             <td width="100">30</td>
             <td width="150">
             <div class="form-group">
                 <label>${expertHandlFileList.sqqzzhixingyijuscore }</label>
          		</div>
             </td>
             <td><div class="form-group">
               <textarea  disabled="disabled" style ="word-wrap: break-word; word-break: break-all;"  rows="3"  wrap ="hard" class="form-control" id="sqqzzhixingyijureviews" name ="sqqzzhixingyijureviews"  >${expertHandlFileList.sqqzzhixingyijureviews }</textarea>
             </div></td>
           </tr>
           <tr>
             <td height="30" align="center">2</td>
             <td>申请执行手续完备 </td>
             <td width="100">20</td>
             <td width="150">
             <div class="form-group">
                  <label>${expertHandlFileList.sqzxshouxuwanbeiscore }</label>
           		</div>
             </td>
             <td><div class="form-group">
               <textarea  disabled="disabled" style ="word-wrap: break-word; word-break: break-all;"  rows="3"  wrap ="hard" class="form-control" id="sqzxshouxuwanbeireviews" name ="sqzxshouxuwanbeireviews"  >${expertHandlFileList.sqzxshouxuwanbeireviews }</textarea>
             </div></td>
           </tr>
           <tr>
             <td height="30" align="center">3</td>
             <td>申请程序合法</td>
             <td width="100">30</td>
             <td width="150">
             <div class="form-group">
                 <label>${expertHandlFileList.sqchengxuhefascore }</label>
           		</div>
             </td>
             <td><div class="form-group">
               <textarea  disabled="disabled" style ="word-wrap: break-word; word-break: break-all;"  rows="3"  wrap ="hard" class="form-control" id="sqchengxuhefareviews" name ="sqchengxuhefareviews"  >${expertHandlFileList.sqchengxuhefareviews }</textarea>
             </div></td>
           </tr>
           <tr>
             <td height="30" align="center">4</td>
             <td>申请强制执行材料</td>
             <td width="100">20</td>
             <td width="150">
             <div class="form-group">
                  <label>${expertHandlFileList.sqqzzxcailiaoscore }</label>
           		</div>
             </td>
             <td><div class="form-group">
               <textarea  disabled="disabled" style ="word-wrap: break-word; word-break: break-all;"  rows="3"  wrap ="hard" class="form-control" id="sqqzzxcailiaoreviews" name ="sqqzzxcailiaoreviews"  >${expertHandlFileList.sqqzzxcailiaoreviews }</textarea>
             </div></td>
           </tr>
           <tr>
             <td height="30" align="center">5</td>
             <td>加分情况 </td>
             <td width="100">20</td>
             <td width="150">
             <div class="form-group">
                        <label>${expertHandlFileList.jiafenqingkuang }</label>
                </div>
             </td>
             <td><div class="form-group">
               <textarea  disabled="disabled" style ="word-wrap: break-word; word-break: break-all;"  rows="3"  wrap ="hard" class="form-control" id="jiafenqingkuangreviews" name ="jiafenqingkuangreviews"  >${expertHandlFileList.jiafenqingkuangreviews }</textarea>
             </div></td>
           </tr>
           <tr>
             <td height="30" colspan="2" align="center">小计</td>
             <td width="100">120</td>
             <td width="150"><label>${expertHandlFileList.expertfinalscore}</label></td>
             <td>&nbsp;</td>
           </tr>
           <tr>
            <!--  <td height="30" colspan="4">专家评语
             <textarea  rows="5" class="form-control" id="expertreviews" name ="expertreviews" placeholder="请输入专家评语"></textarea></td>
           -->
            <td height="30" colspan="5">
             <input id = "yxdxanlituijian" disabled="disabled"   value="0" name = "yxdxanlituijian" type="checkbox"  onchange="checkBoxClick()" >是否可以作为优秀典型案例推荐
             <div class="form-group">
             <div class="col-sm-12" style="display: none;" id ="yxdxAnLiTuiJianReviews1"  >
             <textarea  style ="word-wrap: break-word; word-break: break-all;"  rows="5"  wrap ="hard" class="form-control" id="yxdxanlituijianreviews" name ="yxdxanlituijianreviews"  >${expertHandlFileList.yxdxanlituijianreviews }</textarea>
             </div>
             </div>
           </tr>
         </tbody>
       </table>
	   <div class="submit"><a href="#"><button type="button" class="btn btn-primary" id="submitBaoChong" name="signup" value="Sign up" style="font-size:16px;width:150px; margin-top:5px;">返回</button></a></div>
</div>
</div> 
	<form action="" id="searchForm">
		<input name="areaType" type="hidden" value="${areaType}"/>
		<input name="isConsider" type="hidden" value="${isConsider}"/>
		<input name="isConsiderCross" type="hidden" value="${isConsiderCross}"/>
		<input name="fileCode" type="hidden" value="${fileCodeBck}"/>
		<input name="pageNum" type="hidden" value="${pageNum}"/>
		<input name="fileType" type="hidden" value="${fileType}"/>
	</form>
	<script type="text/javascript">
		$(document).ready(function(){
	 var yxdxanlituijian = eval("${expertHandlFileList.yxdxanlituijian }");
		if(yxdxanlituijian == "1"){
			$("#yxdxanlituijian").attr("checked","checked");
			$("#yxdxAnLiTuiJianReviews1").css("display","block");
			$("#yxdxanlituijianreviews").attr("disabled","disabled");
		} 
			$("#submitBaoChong").click(function(){
				if('${expertOrCrossType}' == 1){
					business.addMainContentParserHtml("anjuanList.do?pageIndex="+'${pageIndex}',$("#searchForm").serialize());
				}else{
					business.addMainContentParserHtml("ajpfCross.do",$("#searchForm").serialize());
				}
			});
		});
	</script>
</body>
</html>