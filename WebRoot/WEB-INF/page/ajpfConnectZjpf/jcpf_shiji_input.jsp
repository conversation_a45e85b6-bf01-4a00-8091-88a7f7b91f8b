<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
</head>
<body>

<div class="center_weizhi">当前位置：交叉评分 - 交叉评分结果 - 个人事迹评分</div>
<div class="center">
<div class="center_list">
		<div class="dingwei">
			决定书文号：<span id="filename" style="color:#06C;font-size:16px;">${fileCode}</span>
		</div>
        <table class="table table-bordered table-hover table-condensed">
         <thead>
           <tr>
             <td width="50" height="30" bgcolor="#efefef">序号</td>
             <td bgcolor="#efefef">分指标</td>
             <td width="150" bgcolor="#efefef">分指标分值</td>
             <td width="150" bgcolor="#efefef">初评得分</td>
           </tr>
         </thead>
         <tbody>
           <tr>
             <td height="30" align="center">1</td>
             <td>综合素质</td>
             <td>8分（上限）</td>
             <td>	<label>${crossPersonalFileList.zonghesuzhiscore}</label>
             </td>
           </tr>
           <tr>
             <td height="30" align="center">2</td>
             <td>工作业绩</td>
             <td>12分（上限） </p></td>
             <td>	<label>${crossPersonalFileList.gongzuoyejiscore}</label>
             </td>
           </tr>
           <tr>
             <td height="30" colspan="2" align="center">小计</td>
             <td>20</td>
             <td><span id ="expertfinalscore1" class = "expertfinalscore">&nbsp;&nbsp;&nbsp;&nbsp;${crossPersonalFileList.expertfinalscore}</span></td>
           </tr>
          </tbody>
       </table>
    <div class="submit"><a href="#"><button type="submit" id="submitBaoChong"  class="btn btn-primary" name="signup" value="Sign up" style="font-size:16px;width:150px; margin-top:5px;">返回</button></a></div>
</div>
</div>
	<form action="" id="searchForm">
		<input name="areaType" type="hidden" value="${areaType}"/>
		<input name="isConsider" type="hidden" value="${isConsider}"/>
		<input name="personalMaterialName" type="hidden" value="${personalMaterialName}"/>
		<input name="pageNum" type="hidden" value="${pageNum}"/>
	</form>
	<script type="text/javascript">
		$(document).ready(function(){
			$("#submitBaoChong").click(function(){
				business.addMainContentParserHtml("gerenList.do",$("#searchForm").serialize());
			});
		});
	</script>
</body>
</html>