<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <title>案卷识别</title>
<%--  <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">--%>
<%--  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">--%>
<%--  <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>--%>
<%--  <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>--%>
</head>
<body>
<div class="container mt-5">
  <button class="btn btn-primary" data-toggle="modal" data-target="#caseModal">案卷识别</button>

  <!-- 模态框 -->
  <div class="modal fade" id="caseModal" tabindex="-1" role="dialog" aria-labelledby="caseModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="caseModalLabel">案卷信息</h5>
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <form id="caseForm">

            <div class="form-group">
              <label for="areacode">行政区</label>
              <input type="text" style="display: none" class="form-control" id="areacode" name="areacode">
            </div>
            <div class="form-group">
              <label for="province">行政区划</label>
              <div class="row">
                <div class="col-md-4">
                  <select class="form-control" id="province" name="province" required>
                    <option value="">选择省</option>
                    <!-- 省份选项会在页面加载时填充 -->
                  </select>
                </div>
                <div class="col-md-4">
                  <select class="form-control" id="city" name="city" disabled>
                    <option value="">选择市</option>
                  </select>
                </div>
                <div class="col-md-4">
                  <select class="form-control" id="county" name="county" disabled>
                    <option value="">选择县</option>
                  </select>
                </div>
              </div>
            </div>
            <div class="form-group">
              <label for="filecode">案卷号</label>
              <input type="text" class="form-control" id="filecode" name="filecode" required>
            </div>
            <div class="form-group">
              <label for="fileType">案卷类型</label>
              <select class="form-control" id="filetype" name="filetype" required>
                <option value = ""  >请选择案卷类型</option>
                <option value="0" >行政处罚案卷</option>
                <option value="1" >按日计罚案卷</option>
                <option value="2" >移送行政拘留案卷</option>
                <option value="3" >涉嫌犯罪案卷</option>
                <option value="6" >查封扣押案卷</option>
                <option value="7" >限产停产案卷</option>
                <option value="9" >不予处罚</option>
              </select>
            </div>
            <div class="form-group">
              <label for="closed">是否结案</label>
              <select class="form-control" id="closed" name="closed" required>
                <option value = ""  >请选择</option>
                <option value="1">已结案</option>
                <option value="2">未结案</option>
              </select>
            </div>
            <div class="form-group">
              <label for="expert1">分配专家</label>
              <div class="row">
                <div class="col-md-6">
                  <input type="text" class="form-control" disabled="true"id="expert1" name="expert1" placeholder="专家A" required>
                </div>
                <div class="col-md-6">
                  <input type="text" class="form-control" disabled="true" id="expert2" name="expert2" placeholder="专家B">
                </div>
              </div>
            </div>
            <div  style="display: flex; align-items: flex-end;">
              <p style="  height: 30px; line-height: 30px; margin: 0;  padding: 0 10px 0 0; box-sizing: border-box;"> </p>
                <span id="uploadTr" >
                        <input type="file" id="provinceFile" value="文件上传">
                        <input type="hidden"  name="filename" id="filename" value="${filename}" required>
                        <input type="hidden"  name="fileurl" id="fileurl" value="${fileurl}">
                </span>
               <span id="fileSpan"></span>


            </div>
          </form>
        </div>

        <div class="modal-footer" style="margin-top: 5px;">
          <button type="button" class="btn btn-secondary" data-dismiss="modal">返回</button>
          <button type="button" class="btn btn-primary" id="saveButton">保存</button>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  $(document).ready(function () {
    var filename = document.getElementById("filename").value;
    var fileurl = document.getElementById("fileurl").value;

    $("#provinceFile").fileinput({
      uploadUrl: WEBPATH + '/pdffileupload.do', // you must set a valid URL here else you will get an error
      language: 'zh',
      browseClass: 'btn btn-danger',//按钮样式
      //overwriteInitial: true,
      minFileCount: 1,
      allowedFileExtensions : [ 'rar', 'zip', 'pdf','RAR', 'ZIP', 'PDF'],
      maxFileCount: 1,
      minFileSize: 1,
      maxFileSize: 209920, //政策文件附件大小限制205M
      enctype: 'multipart/form-data',
      dropZoneTitle: "可拖拽文件到此处...",
      initialPreviewShowDelete: false,
      msgFilesTooMany: '选择上传的文件数量({n}) 超过允许的最大数值{m}！',
      msgZoomModalHeading: '文件预览',
      msgInvalidFileExtension: '不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
      msgNoFilesSelected: '请选择文件',
      msgValidationError: '文件类型不正确或文件过大',
      initialPreviewFileType: 'els',
      browseLabel: "选择文件",
      removeLabel: '删除',
      removeTitle: '删除文件',
      uploadLabel: '上传',
      uploadTitle: '上传文件',
      cancelLabel: '取消',
      cancelTitle: '取消上传',
      showPreview: false,
      autoReplace: true,
      slugCallback: function (filename) {
        return filename.replace('(', '_').replace(']', '_');
      }
    }).on('filepreupload', function (event, data, previewId, index) {
      //文件开始上传操作


    }).on('fileuploaded', function (event, data, previewId, index) {//文件上传完成执行操作

      console.log("---文件上传成功----", data.response.url, data.response.fileRealName)
      // saveProvinceFile(data.response.fileRealName,data.response.url);

      $("#fileurl").val(data.response.url);
      $("#filename").val(data.response.fileRealName);
      $("#fileSpan").html("<a href='javascript:void(0)' onclick='downloadFile(\"" + data.response.url + "\",\"" + data.response.fileRealName + "\")'>" + data.response.fileRealName + "</a>");

      $("#uploadTr").css("display", "none");
    })

    $.ajax({
      url: WEBPATH+"/zjpf/getProvinces.do", // 调用获取省的接口
      method: "GET",
      success: function (data) {
        data.forEach(function(data) {
          $('#province').append('<option value="' + data.province + '">' + data.provinceName + '</option>');
        });
      },
      error: function (error) {
        console.error("获取省失败", error);
      }
    });

    // 2. 省份选择改变时，加载市下拉框
    $('#province').change(function () {
      var provinceCode = $(this).val();
      $('#city').empty().append('<option value="">选择市</option>').prop('disabled', true);
      $('#county').empty().append('<option value="">选择县</option>').prop('disabled', true);

      if (provinceCode) {
        $.ajax({
          url: WEBPATH+'/zjpf/getCities.do', // 调用获取市的接口
          method: 'GET',
          data: { province: provinceCode },
          success: function (data) {
            data.forEach(function(city) {
              $('#city').append('<option value="' + city.code + '">' + city.name + '</option>');
            });
            $('#city').prop('disabled', false);
          },
          error: function (error) {
            console.error("获取市失败", error);
          }
        });
      }
    });

    // 3. 市选择改变时，加载县下拉框
    $('#city').change(function () {
      var cityCode = $(this).val();
      $('#county').empty().append('<option value="">选择县</option>').prop('disabled', true);

      if (cityCode) {
        $.ajax({
          url: WEBPATH+'/zjpf/getCounties.do', // 调用获取县的接口
          method: 'GET',
          data: { city: cityCode },
          success: function (data) {
            data.forEach(function(county) {
              $('#county').append('<option value="' + county.code + '">' + county.name + '</option>');
            });
            $('#county').prop('disabled', false);
          },
          error: function (error) {
            console.error("获取县失败", error);
          }
        });
      }
    });
 function getAreaCode(){
      var areaCode='';

      var pro = $("#province").val();
       var city = $("#city").val();
      var country = $("#country").val();

      if(country!=""){
          areaCode = country;
      }else if(city!=""){
          areaCode = city;
      }else if(pro!=""){
         areaCode = pro;
      }
      document.getElementById('areacode').value = areaCode;
    }

    $(document).ready(function () {
    $('#saveButton').click(function () {

      var areaCode = "";
      var pro = $("#province").val();
      var city = $("#city").val();
      var county = $("#county").val();

      if(county!="" && county!=undefined){
        areaCode = county;
      }else if(city!="" && city!=undefined){
        areaCode = city;
      }else if(pro!="" && pro!=undefined){
        areaCode = pro;
      }
      document.getElementById('areacode').value = areaCode;
      // 通过 AJAX 提交表单数据
      $.ajax({
        type: "POST",
        url: WEBPATH+'/zjpf/saveAiFiles.do', // 替换为实际的服务器端处理地址
        data: $('#caseForm').serialize(),
        success: function (response) {
          // alert("数据保存成功！");
          $('#caseModal').modal('hide');
          // 这里可以添加处理成功后的其他逻辑
          swal({title: '' ,text: "案卷识别成功",type:"success"});
        },
        error: function (error) {
          alert("保存失败，请重试！");
          console.error(error);
        }
      });


    });
    });

  });
</script>
<!-- CSS部分（可选，自定义样式） -->
<style>
  .modal-body {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
</style>
</body>
</html>
