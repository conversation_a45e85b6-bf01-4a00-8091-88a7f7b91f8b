<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<html>
<head>
</head>
<body>
<div class="center_weizhi">当前位置：信息汇总 - 参选县级断面列表</div>
<div class="center">
<div class="center_list">
    <div class="panel-group" id="accordion">
          <h4 class="panel-title">
            <!---信息填报
            <div class="btn-group" style="margin-right:20px;float:left;">
               <a href="CementEnterprises_input.html"><button type="button" class="btn btn-success" style="font-size:16px;">企业信息录入</button></a>
            </div>--->          
            <!---搜索--->
            <div style="width:260px;" class="btn-group">
                    <a href ="#"><button type="button" id ="viewExcel" class="btn btn-primary">导出EXCEL</button></a>  
            </div>  
          </h4>
          <br/>
    </div>
        <table class="table table-bordered table-hover table-condensed">
         <thead>
           <tr>
             <td width="50" height="30" bgcolor="#efefef">序号</td>
             <td bgcolor="#efefef">县级参选单位</td>
             <td bgcolor="#efefef">国控监测断面名称</td>
           </tr>
         </thead>
         <tbody>
               <c:forEach items="${electionUnitList.list}" var="item" varStatus="status">
		         <tr>
	             <td height="30" align="center">${status.index+1}<input id="id" type="hidden"  value="${item.id}"></td>
	             <td>${item.danWeiName}</td>
	             <td>${item.guokongduanmian}</td>
	          </tr>
	           </c:forEach>
         </tbody>
       </table>
    </div>
       <!--列表翻页 开始-->
    <div class="page">
    	<span style="padding:12px; float:left; color:#0099cc;">共${electionUnitList.total}条记录</span>
        <ul class="pagination" id="pageCon">
        </ul>
    </div>
</div>
<div class="bottom">&copy;2016 北京长能环境大数据科技有限公司 研发并提供技术支持</div>

<script type="text/javascript">

$(document).ready(function() {
	
	//导出Excel表格
	
	$("#viewExcel").click(function(){

		window.location.href= WEBPATH+'/xxhz/downXianJiDuanMaExcel.do';
	});
	
	//分页
	$(document).ready(function(){
		var curentPage = eval('${electionUnitList.pageNum}');
		var totalPage = eval('${electionUnitList.pages}');
		var areaType = eval('${areaType}');
		if(totalPage>0){
			var options = {
				bootstrapMajorVersion: 3,
			    currentPage: curentPage,
			    totalPages: totalPage,
			    numberOfPages: 5,
			    itemTexts: function (type, page, current) {
			            switch (type) {
			            case "first":
			                return "首页";
			            case "prev":
			                return "&laquo;";
			            case "next":
			                return "&raquo;";
			            case "last":
			                return "尾页";
			            case "page":
			                return page;
			            }
			    },
			    onPageClicked: function (event, originalEvent, type, page) {
	            	business.addMainContentParserHtml(WEBPATH+'/xxhz/xjdmList.do?pageNum='+page,$("#searchForm").serialize());
	            }
	    	};
	    	$('#pageCon').bootstrapPaginator(options);
	   	}

	});
	
	
	
	$(".topnav").accordion({
		accordion:false,
		speed: 500,
		closedSign: '[+]',
		openedSign: '[-]'
	});
});

</script>
</body>
</html>
