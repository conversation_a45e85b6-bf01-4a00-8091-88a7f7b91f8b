<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<html>
<head>
</head>
<body>
	<div class="center_weizhi">当前位置：信息汇总 - 专家推荐案卷列表</div>
	<div class="center">
		<div class="center_list">
			<div class="panel-group" id="accordion">
				<h4 class="panel-title">
					<!---信息填报
            <div class="btn-group" style="margin-right:20px;float:left;">
               <a href="CementEnterprises_input.html"><button type="button" class="btn btn-success" style="font-size:16px;">企业信息录入</button></a>
            </div>--->
					<!---快速查询--->
					<form id="searchForm" role="form">
						<select class="form-control"
							style="width: 150px; margin-right: 5px;" name="areaType"
							id="areaTypeSelect">
							<option value="">请选择行政区</option>
							<option value="1" <c:if test="${areaType=='1' }">selected</c:if>>省级</option>
							<option value="2" <c:if test="${areaType=='2' }">selected</c:if>>市级</option>
							<option value="3" <c:if test="${areaType=='3' }">selected</c:if>>县级</option>
						</select> <select id="fileTypeSelect" class="form-control"
							style="width: 300px; margin-right: 5px;" name="fileType">
							<!-- 0代表 行政处罚案卷 ；1代表按日计罚案卷； 2代表移送行政拘留案卷；3代表涉嫌犯罪移送案卷；4代表申请法院强制执行案卷；5代表发现问题的污染源现场监督检查稽查案卷   -->
							<option value="">请选择案卷类型</option>
							 <option value="0" <c:if test="${fileType=='0' }">selected</c:if>>行政处罚案卷</option>
	                 		 <option value="1" <c:if test="${fileType=='1' }">selected</c:if>>按日计罚案卷</option>
	                 		 <option value="2" <c:if test="${fileType=='6' }">selected</c:if>>查封扣押案卷</option>
	                 		 <option value="3" <c:if test="${fileType=='7' }">selected</c:if>>限产停产案卷</option>
	                 		 <option value="4" <c:if test="${fileType=='2' }">selected</c:if>>移送行政拘留案卷</option>
	                 		 <option value="5" <c:if test="${fileType=='3' }">selected</c:if>>涉嫌犯罪移送案卷</option>
						</select> <select class="form-control"
							style="width: 150px; margin-right: 5px;" name="type"
							id="typeSelect">
							<option value="">请选择案卷类型</option>
							<option value="0" <c:if test="${type =='0' }">selected</c:if>>集体案卷类型</option>
							<option value="1"
								<c:if test="${type =='1' or  type =='2'}">selected</c:if>>个人案卷类型</option>
						</select>
						<div style="width: 260px;" class="btn-group">
							<div class="row">
								<div class="col-lg-6">
									<div class="input-group">
										<input type="text" class="form-control" name="word"
											value="${word }" style="width: 200px;" placeholder="案卷号关键字">
										<span class="input-group-btn">
											<button id="searchButt" class="btn btn-success" type="button">
												快速搜索</button>
										</span>
									</div>
									<!-- /input-group -->
								</div>
								<!-- /.col-lg-6 -->
							</div>
						</div>
					</form>
					<!---搜索--->
				</h4>
			</div>
			<table class="table table-bordered table-hover table-condensed">
				<thead>
					<tr>
						<td width="20" height="30" bgcolor="#efefef">序号</td>
						<td width="50" bgcolor="#efefef">省</td>
						<td width="50" bgcolor="#efefef">地市</td>
						<td width="50" bgcolor="#efefef">区县</td>
						<td width="100" bgcolor="#efefef">案卷名称</td>
						<td width="100" bgcolor="#efefef">案卷号</td>
						
						 <td width="50" bgcolor="#efefef">专家姓名</td>
			             <td width="50" bgcolor="#efefef">评审得分</td>
			             <td width="50" bgcolor="#efefef">专家姓名</td>
			             <td width="50" bgcolor="#efefef">评审得分</td>
			             
			             <td width="200" bgcolor="#efefef">推荐评语</td>
			             
						 <td width="50" bgcolor="#efefef">材料下载</td>
					</tr>
				</thead>
				<tbody>
					<c:forEach items="${filesList.list}" var="item" varStatus="status">
						<tr>
							<td width="20" height="30" align="center">${status.index+1}
							<td width="50" >${item.province}</td>
							<td width="50" >${item.city}</td>
							<td width="50" >${item.country}</td>
							<td width="100" >${item.fileName}</td>
							<td width="100" >${item.fileCode }</td>
							
							<td width="50" >${item.expertAName }</td>
							<td width="50" >
								<a style="color:#6495ED" href="javascript:void(0);" onclick="macroMgr.onLevelTwoMenuClick(null, 'zjpf/zjpfScoreLook.do?expertId=${item.expertAId}&fileId=${item.id}&status=1' )">${item.expertAScore }</a>
							</td>
							<td width="50" >${item.expertBName }</td>
							<td width="50" >
								<a style="color:#6495ED" href="javascript:void(0);" onclick="macroMgr.onLevelTwoMenuClick(null, 'zjpf/zjpfScoreLook.do?expertId=${item.expertBId}&fileId=${item.id}&status=1' )">${item.expertBScore}</a>
							</td>
							<td width="200" >
								 <textarea   rows="5" class="form-control" readonly="readonly" >${item.recommendRemarks }</textarea>
								
							</td>
							<td width="50" >
								<c:if test="${ not empty item.fileUrl}">
									<a href="#"><button class="btn btn-success btn-xs" data-toggle="modal" onclick="down('${item.id}')" data-target="#myModal">下载</button></a>
								</c:if>
							</td>
						</tr>
					</c:forEach>
				</tbody>
			</table>
		</div>
		<!--列表翻页 开始-->
		<div class="page">
			<span style="padding: 12px; float: left; color: #0099cc;">共${filesList.total}条记录</span>
			<ul class="pagination" id="pageCon">
			</ul>
		</div>
	</div>
	<div class="bottom">&copy;2016 北京长能环境大数据科技有限公司 研发并提供技术支持</div>
	<script type="text/javascript">
		// 搜索条件
		$(document).ready(
				function() {
					//导出Excel表格

					$("#viewExcel").click(function() {

						window.location.href = WEBPATH + '/xxhz/downExcel.do';
					});

					//搜索
					$("#areaTypeSelect").change(
							function() {
								business.addMainContentParserHtml(
										"xxhz/recommend-files.do", $("#searchForm")
												.serialize());
							});
					$("#fileTypeSelect").change(
							function() {
								business.addMainContentParserHtml(
										"xxhz/recommend-files.do", $("#searchForm")
												.serialize());
							});
					$("#typeSelect").change(
							function() {
								business.addMainContentParserHtml(
										"xxhz/recommend-files.do", $("#searchForm")
												.serialize());
							});
					$("#searchButt").click(
							function() {
								business.addMainContentParserHtml(
										"xxhz/recommend-files.do", $("#searchForm")
												.serialize());
							});
				});
		//分页
		$(document).ready(
				function() {
					var curentPage = eval('${filesList.pageNum}');
					var totalPage = eval('${filesList.pages}');
					var areaType = eval('${areaType}');
					if (totalPage > 0) {
						var options = {
							bootstrapMajorVersion : 3,
							currentPage : curentPage,
							totalPages : totalPage,
							numberOfPages : 5,
							itemTexts : function(type, page, current) {
								switch (type) {
								case "first":
									return "首页";
								case "prev":
									return "&laquo;";
								case "next":
									return "&raquo;";
								case "last":
									return "尾页";
								case "page":
									return page;
								}
							},
							onPageClicked : function(event, originalEvent,
									type, page) {
								business.addMainContentParserHtml(WEBPATH
										+ '/xxhz/recommend-files.do?pageNum=' + page,
										$("#searchForm").serialize());
							}
						};
						$('#pageCon').bootstrapPaginator(options);
					}

				});
		//下载

		function down(index) {

			$.ajax({
				type : "post",
				url : WEBPATH + '/xxhz/xxhzFileUrl.do',
				data : {
					id : index
				}, //注意数据用{}
				success : function(data) { //成功
					if ("yes" == data) {
						window.location.href = WEBPATH
								+ '/xxhz/xxhzAnJuandown1.do?id=' + index;
						return false;
					} else if ("no" == data) {
						swal("操作失败", "该案卷不存在!", "error");
						return false;
					} else if ("suffixerror" == data) {
						swal("操作失败", "该案卷上传数据格式有问题!", "error");
						return false;
					}
				}
			});
		}
	</script>
	<script>
		$(document).ready(function() {
			$(".topnav").accordion({
				accordion : false,
				speed : 500,
				closedSign : '[+]',
				openedSign : '[-]'
			});
		});
	</script>
</body>
</html>