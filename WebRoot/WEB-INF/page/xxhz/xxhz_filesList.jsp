<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<html>
<head>
</head>
<body>
<div class="center_weizhi">当前位置：信息汇总 - 案卷列表</div>
<div class="center">
<div class="center_list">
    <div class="panel-group" id="accordion">
          <h4 class="panel-title">
            <!---信息填报
            <div class="btn-group" style="margin-right:20px;float:left;">
               <a href="CementEnterprises_input.html"><button type="button" class="btn btn-success" style="font-size:16px;">企业信息录入</button></a>
            </div>--->          
            <!---快速查询--->
          <form id= "searchForm"  role="form">
              <select class="form-control" style="width:150px;margin-right:5px;" name="areaType" id="areaTypeSelect">
                 <option value="" >请选择行政区</option>
                 <option value="1" <c:if test="${areaType=='1' }">selected</c:if> >省级</option>
                 <option value="2" <c:if test="${areaType=='2' }">selected</c:if> >市级</option>
                 <option value="3" <c:if test="${areaType=='3' }">selected</c:if> >县级</option>
              </select>
              
               <select id ="fileTypeSelect" class="form-control" style="width:300px;margin-right:5px;" name ="fileType">
              <!-- 0代表 行政处罚案卷 ；1代表按日计罚案卷； 2代表移送行政拘留案卷；3代表涉嫌犯罪移送案卷；4代表申请法院强制执行案卷；5代表发现问题的污染源现场监督检查稽查案卷   -->
                 <option value = ""  >请选择案卷类型</option>
                 <option value="0" <c:if test="${fileType=='0' }">selected</c:if>>行政处罚案卷</option>
                 <option value="1" <c:if test="${fileType=='1' }">selected</c:if>>按日计罚案卷</option>
                 <option value="6" <c:if test="${fileType=='6' }">selected</c:if>>查封扣押案卷</option>
                 <option value="7" <c:if test="${fileType=='7' }">selected</c:if>>限产停产案卷</option>
                 <option value="2" <c:if test="${fileType=='2' }">selected</c:if>>移送行政拘留案卷</option>
                 <option value="3" <c:if test="${fileType=='3' }">selected</c:if>>涉嫌犯罪移送案卷</option>
                 <option value="5" <c:if test="${fileType=='5' }">selected</c:if>>污染源现场监督检查稽查案卷</option>
              </select>
              
              <select class="form-control" style="width:150px;margin-right:5px;" name="type" id="typeSelect">
                 <option value="" >请选择案卷类别</option>
                 <option value="0" <c:if test="${type =='0' }">selected</c:if>  >集体案卷类型</option>
                 <option value="1" <c:if test="${type =='1' }">selected</c:if> >个人案卷类型</option>
                 <option value="2" <c:if test="${type =='2' }">selected</c:if> >集体与个人共同案卷类型</option>
              </select> 
              
              <select class="form-control" style="width:150px;margin-right:5px;" name="reportType" id="reportTypeSelect">
                 <option value="">请选择来源</option>
                 <option value="0" <c:if test="${reportType=='0' }">selected</c:if>>推选</option>
                 <option value="1" <c:if test="${reportType=='1' }">selected</c:if>>随机</option>
              </select>
              
            <div style="width:260px;" class="btn-group">
          <div class="row">                     
                     <div class="col-lg-6">
                        <div class="input-group">
                           <input type="text" class="form-control" name ="word" value="${word }" style="width:200px;"  placeholder="案卷号关键字">
                           <span class="input-group-btn">
                              <button id="searchButt"  class="btn btn-success" type="button">
                               	  快速搜索
                             
                           </span>
                              <span class="input-group-btn">
                            <button id="outExcel"  class="btn btn-success" type="button">
                               	 导出excel
                              </button>
                               </span>
                        </div><!-- /input-group -->
                     </div><!-- /.col-lg-6 -->
                  </div>
            </div>            
         </form>
            <!---搜索--->
          </h4>
    </div>
        <table class="table table-bordered table-hover table-condensed">
         <thead>
           <tr>
             <td width="50" height="30" bgcolor="#efefef">序号</td>
              <td width="100" bgcolor="#efefef">案卷号</td>
             <td width="100" bgcolor="#efefef">案卷名称</td>
             <td width="70" bgcolor="#efefef">来源</td>
             <td bgcolor="#efefef">案卷所属省</td>
             <td bgcolor="#efefef">案件所属市</td>
             <td bgcolor="#efefef">案件所属县</td>
             <td width="70" bgcolor="#efefef">材料下载</td>
           </tr>
         </thead>
         <tbody>
	          <c:forEach items="${filesList.list}" var="item" varStatus="status">
		         <tr>
	             <td height="30" align="center">${status.index+1}
	                <td>${item.fileCode }</td>
	             <td>${item.fileName}</td>
	             <td>
	             	<c:if test="${item.reporttype==0}">
	             		推选
	             	</c:if>
	             	<c:if test="${item.reporttype==1}">
	             		随机
	             	</c:if>
	             </td>
	              <td>${item.province}</td>
	             <td>${item.city}</td>
	             <td>${item.country}</td>
	             <td>
	             <c:if test="${ not empty item.fileUrl}">
	             <!-- <a href ="${item.downUrl}" target="_Blank"><button class="btn btn-success btn-xs" data-toggle="modal" data-target="#myModal">下载</button></a> -->
	             <button class="btn btn-success btn-xs" onclick="javascript:window.location.href='${item.downUrl}'">下载</button>
	             </c:if>
	             </td>
	        <%--      <td>${item.cardid}</td>
	             <td> ${item.personalmaterialname }</td>
	             <td>
	               <c:if test="${not empty item.personalmaterialname}" > <a  href="javascript:void(0);"   onclick="down1('${item.id}')" ><button class="btn btn-success btn-xs" data-toggle="modal" data-target="#myModal">下载</button></a></c:if> 
	       		 </td>
	       		 <td>${item.perhonestfilename }</td>
	               <td><c:if test="${not empty item.perhonestfilename}" >  <a  href="javascript:void(0);"   onclick="down('${item.id}')" ><button class="btn btn-success btn-xs" data-toggle="modal" data-target="#myModal">下载</button></a></c:if> 
	             </td> --%>
	          </tr>
	           </c:forEach>
         </tbody>
       </table>
    </div>
   <!--列表翻页 开始-->
    <div class="page">
    	<span style="padding:12px; float:left; color:#0099cc;">共${filesList.total}条记录</span>
        <ul class="pagination" id="pageCon">
        </ul>
    </div>
</div>
<div class="bottom">&copy;2016 北京长能环境大数据科技有限公司 研发并提供技术支持</div>
<script type="text/javascript">

// 搜索条件
$(document).ready(function(){
	//导出Excel表格
	
	$("#viewExcel").click(function(){

		window.location.href= WEBPATH+'/xxhz/downExcel.do';
	});
	
	//搜索
	$("#areaTypeSelect").change(function(){
		business.addMainContentParserHtml("xxhz/fileList.do",$("#searchForm").serialize());
	});
	$("#fileTypeSelect").change(function(){
		business.addMainContentParserHtml("xxhz/fileList.do",$("#searchForm").serialize());
	});
	$("#typeSelect").change(function(){
		business.addMainContentParserHtml("xxhz/fileList.do",$("#searchForm").serialize());
	});
	$("#searchButt").click(function(){
		business.addMainContentParserHtml("xxhz/fileList.do",$("#searchForm").serialize());
	});
	
	$("#reportTypeSelect").change(function(){
		business.addMainContentParserHtml("xxhz/fileList.do",$("#searchForm").serialize());
	});
});
//分页
$(document).ready(function(){
	var curentPage = eval('${filesList.pageNum}');
	var totalPage = eval('${filesList.pages}');
	var areaType = eval('${areaType}');
	if(totalPage>0){
		var options = {
			bootstrapMajorVersion: 3,
		    currentPage: curentPage,
		    totalPages: totalPage,
		    numberOfPages: 5,
		    itemTexts: function (type, page, current) {
		            switch (type) {
		            case "first":
		                return "首页";
		            case "prev":
		                return "&laquo;";
		            case "next":
		                return "&raquo;";
		            case "last":
		                return "尾页";
		            case "page":
		                return page;
		            }
		    },
		    onPageClicked: function (event, originalEvent, type, page) {
            	business.addMainContentParserHtml(WEBPATH+'/xxhz/fileList.do?pageNum='+page,$("#searchForm").serialize());
            }
    	};
    	$('#pageCon').bootstrapPaginator(options);
   	}
	
	//导出excel
	$("#outExcel").click(function(){
		//window.location.href= WEBPATH+'/sys2017/outputCityExcel.do';
		window.location.href=WEBPATH+'/xxhz/outPutExcelfileList.do?'+$("#searchForm").serialize();
	})
	
	$("#outExcel").click(function(){
		//window.location.href= WEBPATH+'/sys2017/outputCityExcel.do';
		window.location.href=WEBPATH+'/xxhz/outPutExcelfileList.do?'+$("#searchForm").serialize();
	})

});
//下载

	function down(index){
			 //var fileCode = $("#fileCode").html();
		
			 $.ajax({
				    type:"post",
				    url:WEBPATH+'/xxhz/xxhzFileUrl.do',
				    data:{id:index },           //注意数据用{}
				    success:function(data){  //成功
					 if("yes" == data){
							window.location.href= WEBPATH+'/xxhz/xxhzAnJuandown1.do?id='+index;
						    return false;
				         }else if("no" == data){
				            	  swal( "操作失败","该案卷不存在!", "error");
				            	  return false;
						}else if("suffixerror" ==data){
							  swal( "操作失败","该案卷上传数据格式有问题!", "error");
			            	  return false;
						}
				         }
				});
			}

/* function down1(index){
		//下载个人事迹材料  status标记下载的类型，0 代表要下载的个人事迹材料
		window.location.href= WEBPATH+'/xxhz/xxhzAnJuandown.do?id='+index+'&status=0';
}
function down(index){
		//个人廉洁执法证明相关材料    status标记下载的类型，1代表要个人廉洁执法证明相关材料
		window.location.href= WEBPATH+'/xxhz/xxhzAnJuandown.do?id='+index+'&status=1';
} */
</script>
<script >

$(document).ready(function() {
	$(".topnav").accordion({
		accordion:false,
		speed: 500,
		closedSign: '[+]',
		openedSign: '[-]'
	});
});

</script>
</body>
</html>