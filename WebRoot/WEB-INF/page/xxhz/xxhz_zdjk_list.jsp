<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<html>
<head>
</head>
<body>
<div class="center_weizhi">当前位置：信息汇总 - 重点排污单位数量列表</div>
<div class="center">
<div class="center_list">
    <div class="panel-group" id="accordion">
          <h4 class="panel-title">
            <!---信息填报
            <div class="btn-group" style="margin-right:20px;float:left;">
               <a href="CementEnterprises_input.html"><button type="button" class="btn btn-success" style="font-size:16px;">企业信息录入</button></a>
            </div>--->        
              
           <!---快速查询--->
        <form id= "searchForm"  role="form">
              <select class="form-control" style="width:150px;margin-right:5px;" name="areaType" id="areaTypeSelect">
                 <option value="" >请选择行政区</option>
                 <option value="1" <c:if test="${areaType=='1' }">selected</c:if> >省级</option>
                 <option value="2" <c:if test="${areaType=='2' }">selected</c:if> >市级</option>
                 <option value="3" <c:if test="${areaType=='3' }">selected</c:if> >县级</option>
              </select>
        </form>
            <!---搜索--->
            <div style="width:260px;" class="btn-group">
                      <a href ="#"><button type="button" id ="viewExcel" class="btn btn-primary">导出EXCEL</button></a>  
            </div>            
          </h4>
    </div>
        <table class="table table-bordered table-hover table-condensed" style="table-layout:fixed;">
         <thead>
           <tr>
             <td height="30" bgcolor="#efefef" style="vertical-align:middle;width:50px;">序号</td>
             <td bgcolor="#efefef" style="vertical-align:middle;">省</td>
             <td bgcolor="#efefef" style="vertical-align:middle;">地市</td>
             <td bgcolor="#efefef" style="vertical-align:middle;">区县</td>
             <td bgcolor="#efefef" style="vertical-align:middle;width:110px;">行政区域内重点排污企业数量</td>
             <td bgcolor="#efefef" style="vertical-align:middle;width:70px;">公开方式</td>
             <td bgcolor="#efefef" style="word-wrap:break-all; width:200px;vertical-align:middle;">公开网址</td>
             <td bgcolor="#efefef" style="vertical-align:middle;width:100px;">其他公开描述</td>
             <td bgcolor="#efefef" style="vertical-align:middle;width:70px;">材料下载</td>
           </tr>
         </thead>
         <tbody>
               <c:forEach items="${electionUnitList.list}" var="item" varStatus="status">
	           <tr>
	             <td height="30" align="center">${status.index+1}</td>
	             <td>${item.province}</td>
	             <td>${item.city}</td>
	             <td>${item.country}</td>
	             <td>${item.areaguokongqynum}</td><!-- 行政区域内国控企业数量 -->
	             <td>
	             	<c:if test="${item.openType==1}">
	             		网站公开
	             	</c:if>
	             	<c:if test="${item.openType==2}">
	             		其他公开方式
	             	</c:if>
	             </td><!-- 公开方式 -->
	             <td style="width:200px; word-break:break-all;">${item.openUrl}</td><!-- 公开网址 -->
	             <td>${item.openDesc}</td><!-- 其他公开描述 -->
	             <td>
	             	<%-- ${item.areatype==1} --%>
	             	<!-- <a href ="${item.provinceofficialdocumentDownUrl}" target="_Blank"><button class="btn btn-success btn-xs" data-toggle="modal" data-target="#myModal">下载</button></a> -->
	             	<c:if test="${item.areatype!=3 and item.provinceofficialdocument!=null and item.provinceofficialdocument!=''}" > 
	             		<button class="btn btn-success btn-xs" onclick="javascript:window.location.href='${item.provinceofficialdocumentDownUrl}'">下载</button>
	             	</c:if>
	             </td>
	           </tr>
           </c:forEach> 
         </tbody>
       </table>
    </div>
 	<div class="page">
    	<span style="padding:12px; float:left; color:#0099cc;">共${electionUnitList.total}条记录</span>
        <ul class="pagination" id="pageCon">
            
        </ul>
    </div>
</div>
<div class="bottom">&copy;2016 北京长能环境大数据科技有限公司 研发并提供技术支持</div>
<script >

function xxhzDownZdjkqy(index){
	 $.ajax({
		    type:"post",
		    url:WEBPATH+'/xxhz/xxhzZdjkqyExistFileUrl.do',
		    data:{id:index,status:"0" },           //注意数据用{}
		    success:function(data){  //成功
			 if("yes" == data){
				 window.location.href= WEBPATH+'/xxhz/downZdjkqy.do?id='+index+'&status=0';
				    return false;
		         }else if("no" == data){
		            	  swal( "操作失败","该案卷不存在!", "error");
		            	  return false;
				}else if("suffixerror" ==data){
					  swal( "操作失败","该案卷上传数据格式有问题!", "error");
	            	  return false;
				}
		         }
		});
	}
	
	
function xxhzDownZdjkqy1(index){
	$.ajax({
	    type:"post",
	    url:WEBPATH+'/xxhz/xxhzZdjkqyExistFileUrl.do',
	    data:{id:index,status:"1" },           //注意数据用{}
	    success:function(data){  //成功
		 if("yes" == data){
			 window.location.href= WEBPATH+'/xxhz/downZdjkqy.do?id='+index+'&status=1';
			    return false;
	         }else if("no" == data){
	            	  swal( "操作失败","该案卷不存在!", "error");
	            	  return false;
			}else if("suffixerror" ==data){
				  swal( "操作失败","该案卷上传数据格式有问题!", "error");
           	  return false;
			}
	         }
	});
}

//下载
/* function xxhzDownZdjkqy(index){
	 //行政区域内省级重点监控企业数量
	 window.location.href= WEBPATH+'/xxhz/downZdjkqy.do?id='+index+'&status=0';
}
function xxhzDownZdjkqy1(index){
		 //行政区域内市级重点监控企业数量
	 window.location.href= WEBPATH+'/xxhz/downZdjkqy.do?id='+index+'&status=1';
} */

//导出Excel表格

$("#viewExcel").click(function(){
	var areaType = $("#areaTypeSelect").val();
	window.location.href= WEBPATH+'/xxhz/downZdjkqyslExcel.do?areaType='+areaType;
});
//搜索条件
$(document).ready(function(){
	$("#areaTypeSelect").change(function(){
		business.addMainContentParserHtml("xxhz/zdjkList.do",$("#searchForm").serialize());
	});
});
//分页
$(document).ready(function(){
	var curentPage = eval('${electionUnitList.pageNum}');
	var totalPage = eval('${electionUnitList.pages}');
	var areaType = eval('${areaType}');
	if(totalPage>0){
		var options = {
			bootstrapMajorVersion: 3,
		    currentPage: curentPage,
		    totalPages: totalPage,
		    numberOfPages: 5,
		    itemTexts: function (type, page, current) {
		            switch (type) {
		            case "first":
		                return "首页";
		            case "prev":
		                return "&laquo;";
		            case "next":
		                return "&raquo;";
		            case "last":
		                return "尾页";
		            case "page":
		                return page;
		            }
		    },
		    onPageClicked: function (event, originalEvent, type, page) {
            	business.addMainContentParserHtml(WEBPATH+'/xxhz/zdjkList.do?pageNum='+page,$("#searchForm").serialize());
            }
    	};
    	$('#pageCon').bootstrapPaginator(options);
   	}
});

$(document).ready(function() {
	$(".topnav").accordion({
		accordion:false,
		speed: 500,
		closedSign: '[+]',
		openedSign: '[-]'
	});
});

</script>
</body>
</html>
