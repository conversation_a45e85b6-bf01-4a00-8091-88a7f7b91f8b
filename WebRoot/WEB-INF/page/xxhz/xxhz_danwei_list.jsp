<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
</head>
<body>
<div class="center_weizhi">当前位置：信息汇总- 参选单位列表</div>
<div class="center">
<div class="center_list">
    <div class="panel-group" id="accordion">
          <h4 class="panel-title">
               <button type="button" class="btn btn-primary" id ="viewExcel" >导出EXCEL</button> 
          </h4>
    </div>
        <table class="table table-bordered table-hover table-condensed">
         <thead>
         <tr>
             <td width="50" height="30" bgcolor="#efefef">序号</td>
             <td bgcolor="#efefef">省</td>
             <td bgcolor="#efefef">地市</td>
             <td bgcolor="#efefef">区县</td>
             <td bgcolor="#efefef">实际上报案件数量</td>
             <td bgcolor="#efefef">应上报案件数量</td>
             <td bgcolor="#efefef" style="width:100px;">全省重点排污单位名录（加盖公章）</td>
             <td bgcolor="#efefef" style="width:100px;">集体事迹材料下载</td>
             <td bgcolor="#efefef" style="width:100px;">动员部署情况</td>
             <td bgcolor="#efefef" style="width:100px;">集体照片</td>
             <td bgcolor="#efefef" style="width:100px;">宣传资料</td>
             <td bgcolor="#efefef" style="width:100px;">活动总结报告</td>
             <td bgcolor="#efefef" style="width:100px;">自评报告</td>
             <td bgcolor="#efefef" style="width:100px;">表彰激励情况（含省级表彰）</td>
           </tr>
         </thead>
         <tbody>
            <c:forEach items="${electionUnitList.list}" var="item" varStatus="status">
	           <tr>
	             <td height="30" align="center">${status.index+1}<input id="id${status.index+1}" type="hidden"  value="${item.id}"></td>
	             <td>${item.provincePro}</td>
	             <td>${item.cityPro}</td>
	             <td>${item.countryPro}</td>
	             <td>
	             	${item.shijishangbao}
	             </td>
	             <td>
	             	${item.yingshangbao}
	             </td>
	           	 <td>
	             	<c:if test="${ not empty item.polluterevidenceurl}">
	             		<button class="btn btn-success btn-xs" onclick="javascript:window.location.href='${item.polluterevidenceDownUrl}'">              下载</button>
	             	</c:if>
				 </td>
	             <td>
	             	<c:if test="${ not empty item.deedevidenceurl}">
	             		<button class="btn btn-success btn-xs" onclick="javascript:window.location.href='${item.deedevidenceDownUrl}'">下载</button>
	             	</c:if>
				 </td>
				 <td>
	             	<c:if test="${ not empty item.dongyuanurl}">
	             		<button class="btn btn-success btn-xs" onclick="javascript:window.location.href='${item.dongyuanDownUrl}'">              下载</button>
	             	</c:if>
				 </td>
				 <td>
				 	<c:if test="${ not empty item.deedpictureurl}">
	             		<button class="btn btn-success btn-xs" onclick="javascript:window.location.href='${item.deedpictureDownUrl}'">下载</button>
	             	</c:if>
	             </td>
	             <td>
	             	<c:if test="${ not empty item.publicityinformationurl}">
	             		<button class="btn btn-success btn-xs" onclick="javascript:window.location.href='${item.publicityinformationDownUrl}'">下载</button>
	             	</c:if>
	             </td>
	             <td>
	             	<c:if test="${ not empty item.publicityinformationurl}">
	             		<button class="btn btn-success btn-xs" onclick="javascript:window.location.href='${item.activityreportDownUrl}'">下载</button>
	             	</c:if>
	             </td>
	             <td>
	             	<c:if test="${ not empty item.publicityinformationurl}">
	             		<button class="btn btn-success btn-xs" onclick="javascript:window.location.href='${item.selfevaluationreportDownUrl}'">下载</button>
	             	</c:if>
	             </td>
	             <td>
	             	<c:if test="${ not empty item.jiliurl}">
	             		<button class="btn btn-success btn-xs" onclick="javascript:window.location.href='${item.jiliDownUrl}'">             下载</button>
	             	</c:if>
				 </td>
	           </tr>
           </c:forEach>
         </tbody>
       </table>
       <input type="hidden" value="${areaType}" id ="areaType">
    </div>
     <!--列表翻页 开始-->
    <div class="page">
    	<span style="padding:12px; float:left; color:#0099cc;">共${electionUnitList.total}条记录</span>
        <ul class="pagination" id="pageCon">
            
        </ul>
    </div>
    <!--列表翻页 结束-->
</div>
<script type="text/javascript">
function download(url,name){
	if(url==null || url=='' || name==null || name==''){
		swal({title: "操作失败",text: "下载的文件不存在",type:"error"});
	}else{
		window.location.href="${pageContext.request.contextPath}/xxhz/teamMaterialDown.do?url="+url+"&name="+name;
	}
}

$("#viewExcel").click(function(){
	window.location.href= WEBPATH+'/xxhz/downDanWeiExcel.do';
});

//分页
$(document).ready(function(){
	var curentPage = eval('${electionUnitList.pageNum}');
	var totalPage = eval('${electionUnitList.pages}');
	if(totalPage>0){
		var options = {
			bootstrapMajorVersion: 3,
		    currentPage: curentPage,
		    totalPages: totalPage,
		    numberOfPages: 5,
		    itemTexts: function (type, page, current) {
		            switch (type) {
		            case "first":
		                return "首页";
		            case "prev":
		                return "&laquo;";
		            case "next":
		                return "&raquo;";
		            case "last":
		                return "尾页";
		            case "page":
		                return page;
		            }
		    },
		    onPageClicked: function (event, originalEvent, type, page) {
            	business.addMainContentParserHtml(WEBPATH+'/xxhz/cxdwList.do?pageNum='+page,$("#searchForm").serialize());
            }
    	};
    	$('#pageCon').bootstrapPaginator(options);
   	}
});


</script>
</body>
</html>
