<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<html>
<head>
</head>
<body>
<div class="center_weizhi">当前位置：信息汇总 - 先进个人信息列表</div>
<div class="center">
<div class="center_list">
    <div class="panel-group" id="accordion">
          <h4 class="panel-title">
            <!---信息填报
            <div class="btn-group" style="margin-right:20px;float:left;">
               <a href="CementEnterprises_input.html"><button type="button" class="btn btn-success" style="font-size:16px;">企业信息录入</button></a>
            </div>--->          
            <!---快速查询--->
          <form id= "searchForm"  role="form">
              <select class="form-control" style="width:150px;margin-right:5px;" name="areaType" id="areaTypeSelect">
                 <option value="" >请选择行政区</option>
                 <option value="1" <c:if test="${areaType=='1' }">selected</c:if> >省级</option>
                 <option value="2" <c:if test="${areaType=='2' }">selected</c:if> >市级</option>
                 <option value="3" <c:if test="${areaType=='3' }">selected</c:if> >县级</option>
              </select>
         </form>
            <!---搜索--->
            <div style="width:260px;" class="btn-group">
           <!--                    <button class="btn btn-success" type="button">
                                 快速搜索
                              </button> -->
                  <a href="#"><button type="button" class="btn btn-primary" id ="viewExcel">导出EXCEL</button></a>   
            </div>            
          </h4>
    </div>
        <table class="table table-bordered table-hover table-condensed">
         <thead>
           <tr>
             <td width="50" height="30" bgcolor="#efefef">序号</td>
             <td bgcolor="#efefef">省</td>
             <td bgcolor="#efefef">地市</td>
             <td bgcolor="#efefef">区县</td>
             <td width="100" bgcolor="#efefef">个人姓名</td>
             <td width="120" bgcolor="#efefef">所在单位名称</td>
             <td width="100" bgcolor="#efefef">环保工作年限</td>
             <td width="150" bgcolor="#efefef">身份证号码</td>
             <td width="100" bgcolor="#efefef">学历</td>
             <td width="100" bgcolor="#efefef">编制性质</td>
             <td width="200" bgcolor="#efefef">个人事迹材料</td>
             <td width="70" bgcolor="#efefef">材料下载</td>
             <td width="200" bgcolor="#efefef">个人廉洁执法证明相关材料</td>
             <td width="70" bgcolor="#efefef">材料下载</td>
             <td width="200" bgcolor="#efefef">个人照片</td>
             <td width="70" bgcolor="#efefef">材料下载</td>
           </tr>
         </thead>
         <tbody>
	          <c:forEach items="${electionPersonalList.list}" var="item" varStatus="status">
		         <tr>
	             <td height="30" align="center">${status.index+1}
	             <td>${item.province}</td>
	             <td>${item.city}</td>
	             <td>${item.country}</td>
	             <td>${item.name}</td>
	             <td>${item.unitname}</td>
	             <td>${item.workyearnum}</td>
	             <td>${item.cardid}</td>
	             <td>${item.educationname}</td>
	             <td>${item.orgpropname}</td>
	             <td> ${item.personalmaterialname }</td>
	             <!-- 
	             <td>
	               <c:if test="${not empty item.personalmaterialname}" > <a  href="${item.materialDownUrl }" target="_Blank"><button class="btn btn-success btn-xs" data-toggle="modal" data-target="#myModal">下载</button></a></c:if> 
	       		 </td>
	       		 <td>${item.perhonestfilename }</td>
	               <td><c:if test="${not empty item.perhonestfilename}" >  <a  href="${item.honestfileDownUrl }" target="_Blank"><button class="btn btn-success btn-xs" data-toggle="modal" data-target="#myModal">下载</button></a></c:if> 
	             </td>
	              -->
	             <td>
	               <c:if test="${not empty item.personalmaterialname}" ><button class="btn btn-success btn-xs" onclick="javascript:window.location.href='${item.materialDownUrl}'">下载</button></c:if> 
	       		 </td>
	       		 <td>${item.perhonestfilename }</td>
	               <td><c:if test="${not empty item.perhonestfilename}" ><button class="btn btn-success btn-xs" onclick="javascript:window.location.href='${item.honestfileDownUrl}'">下载</button></c:if> 
	             </td>
	             <td>${item.personalPhotoUpload_pro }</td>
	               <td><c:if test="${not empty item.personalPhotoUpload_pro}" ><button class="btn btn-success btn-xs" onclick="javascript:window.location.href='${item.personalPhotoUploadDownUrl_pro}'">下载</button></c:if> 
	             </td>
	          </tr>
	           </c:forEach>
         </tbody>
       </table>
    </div>
   <!--列表翻页 开始-->
    <div class="page">
    	<span style="padding:12px; float:left; color:#0099cc;">共${electionPersonalList.total}条记录</span>
        <ul class="pagination" id="pageCon">
        </ul>
    </div>
</div>
<div class="bottom">&copy;2016 北京长能环境大数据科技有限公司 研发并提供技术支持</div>
<script type="text/javascript">

// 搜索条件
$(document).ready(function(){
	//导出Excel表格
	
	$("#viewExcel").click(function(){
		var areaType = $("#areaTypeSelect").val();
		window.location.href= WEBPATH+'/xxhz/downExcel.do?areaType='+areaType;
	});
	
	//搜索
	$("#areaTypeSelect").change(function(){
		business.addMainContentParserHtml("xxhz/grcxList.do",$("#searchForm").serialize());
	});
});
//分页
$(document).ready(function(){
	var curentPage = eval('${electionPersonalList.pageNum}');
	var totalPage = eval('${electionPersonalList.pages}');
	var areaType = eval('${areaType}');
	if(totalPage>0){
		var options = {
			bootstrapMajorVersion: 3,
		    currentPage: curentPage,
		    totalPages: totalPage,
		    numberOfPages: 5,
		    itemTexts: function (type, page, current) {
		            switch (type) {
		            case "first":
		                return "首页";
		            case "prev":
		                return "&laquo;";
		            case "next":
		                return "&raquo;";
		            case "last":
		                return "尾页";
		            case "page":
		                return page;
		            }
		    },
		    onPageClicked: function (event, originalEvent, type, page) {
            	business.addMainContentParserHtml(WEBPATH+'/xxhz/grcxList.do?pageNum='+page,$("#searchForm").serialize());
            }
    	};
    	$('#pageCon').bootstrapPaginator(options);
   	}

});
//下载

	function down1(index){
			 //var fileCode = $("#fileCode").html();
			 var fileid  = $("#fileId").val();
			 $.ajax({
				    type:"post",
				    url:WEBPATH+'/xxhz/xxhzExistFileUrl.do',
				    data:{id:index,status:"0" },           //注意数据用{}
				    success:function(data){  //成功
					 if("yes" == data){
							window.location.href= WEBPATH+'/xxhz/xxhzAnJuandown.do?id='+index+'&status=0';
						    return false;
				         }else if("no" == data){
				            	  swal( "操作失败","该案卷不存在!", "error");
				            	  return false;
						}else if("suffixerror" ==data){
							  swal( "操作失败","该案卷上传数据格式有问题!", "error");
			            	  return false;
						}
				         }
				});
			}
			
			
	function down(index){
		 //var fileCode = $("#fileCode").html();
		 var fileid  = $("#fileId").val();
		 $.ajax({
			    type:"post",
			    url:WEBPATH+'/xxhz/xxhzExistFileUrl.do',
			    data:{id:index,status:"1" },           //注意数据用{}
			    success:function(data){  //成功
				 if("yes" == data){
						window.location.href= WEBPATH+'/xxhz/xxhzAnJuandown.do?id='+index+'&status=1';
					    return false;
			         }else if("no" == data){
			            	  swal( "操作失败","该案卷不存在!", "error");
			            	  return false;
					}else if("suffixerror" ==data){
						  swal( "操作失败","该案卷上传数据格式有问题!", "error");
		            	  return false;
					}
			         }
			});
		}

/* function down1(index){
		//下载个人事迹材料  status标记下载的类型，0 代表要下载的个人事迹材料
		window.location.href= WEBPATH+'/xxhz/xxhzAnJuandown.do?id='+index+'&status=0';
}
function down(index){
		//个人廉洁执法证明相关材料    status标记下载的类型，1代表要个人廉洁执法证明相关材料
		window.location.href= WEBPATH+'/xxhz/xxhzAnJuandown.do?id='+index+'&status=1';
} */
</script>
<script >

$(document).ready(function() {
	$(".topnav").accordion({
		accordion:false,
		speed: 500,
		closedSign: '[+]',
		openedSign: '[-]'
	});
});

</script>
</body>
</html>