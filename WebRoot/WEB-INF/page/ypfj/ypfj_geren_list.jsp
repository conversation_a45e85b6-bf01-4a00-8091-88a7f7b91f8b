<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
<script type="text/javascript">
	//分页
	$(document).ready(function(){
		business.listenEnter("searchButt");
		var curentPage = eval('${ypfjPerson_List.pageNum}');
		var totalPage = eval('${ypfjPerson_List.pages}');
		if(totalPage>0){
			var options = {
				bootstrapMajorVersion: 3,
			    currentPage: curentPage,
			    totalPages: totalPage,
			    numberOfPages: 5,
			    itemTexts: function (type, page, current) {
			            switch (type) {
			            case "first":
			                return "首页";
			            case "prev":
			                return "&laquo;";
			            case "next":
			                return "&raquo;";
			            case "last":
			                return "尾页";
			            case "page":
			                return page;
			            }
			    },
			    onPageClicked: function (event, originalEvent, type, page) {
	            	business.addMainContentParserHtml(WEBPATH+'/ypfj/personList.do?pageNum='+page,$("#searchForm").serialize());
	            }
	    	};
	    	$('#pageCon').bootstrapPaginator(options);
    	}
	});
	
	$(document).ready(function(){
		$("#searchButt").click(function(){
			business.addMainContentParserHtml("ypfj/personList.do",$("#searchForm").serialize());
		});
		
		$("#areaType").change(function(){
			$("#searchButt").click();
		});
		
		
		//导出execl表单
		$("#viewExcel").click(function(){

			window.location.href= WEBPATH+'/ypfj/downPersonListExcel.do';
		});
	});
</script>
</head>

<body>
<div class="center_weizhi">当前位置：一票否决 - 一票否决个人 - 廉洁执法</div>
<div class="center">
<div class="center_list">
    <div class="panel-group" id="accordion">
          <form id="searchForm" name="searchForm" class="bs-example bs-example-form" role="form">
          <c:if test="${user.sysStatus=='3' }"> 
            <div class="btn-group" style="margin:0 5px 5px 0;float:left;">
               <a href="#" onclick="macroMgr.onLevelTwoMenuClick(null, 'ypfj/ypfjGerenInput.do')"><button type="button" class="btn btn-primary" style="font-size:16px;">廉洁执法信息录入</button></a>
            </div> 
          </c:if>  
          <!---快速查询--->
              <select id="areaType" name="areaType" class="form-control" style="width:150px;margin-right:5px;">
                 <option value="" <c:if test="${areaType==''}">selected</c:if>>请选择查询类型</option>
                 <option value="1" <c:if test="${areaType=='1'}">selected</c:if>>省级</option>
                 <option value="2" <c:if test="${areaType=='2'}">selected</c:if>>市级</option>
                 <option value="3" <c:if test="${areaType=='3'}">selected</c:if>>县级</option>
              </select>
            <!---搜索--->
            <div style="width:260px;" class="btn-group">
               
                  <div class="row">                     
                     <div class="col-lg-6">
                        <div class="input-group">
                           <input name="name" type="text" value="${name }" class="form-control"  placeholder="输入候选人姓名" style="width:200px;">
                           <span class="input-group-btn">
                              <button id="searchButt" class="btn btn-success" type="button">
                                 快速搜索
                              </button>
                           </span>
                        </div><!-- /input-group -->
                     </div><!-- /.col-lg-6 -->
                  </div><!-- /.row -->
               
            </div>
               
             <div style="width:260px;" class="btn-group">
                &nbsp;&nbsp;&nbsp;&nbsp;    <a href ="#"><button type="button" id ="viewExcel" class="btn btn-primary">导出EXCEL</button></a>  
            </div>
                        
          </form>
    </div>
        <table class="table table-bordered table-hover table-condensed">
         <thead>
           <tr>
             <td width="50" height="30" bgcolor="#efefef">序号</td>
             <td bgcolor="#efefef">省</td>
             <td bgcolor="#efefef">地市</td>
             <td bgcolor="#efefef">区县</td>
             <td width="150" bgcolor="#efefef">候选人姓名</td>
             <td width="150" bgcolor="#efefef">候选人身份证号码</td>
             <td width="150" bgcolor="#efefef">是否廉洁执法</td>
             <td width="150" bgcolor="#efefef">操作</td>
           </tr>
         </thead>
         <tbody>
         <c:forEach items="${ypfjPerson_List.list}" var="NoDescPerson" varStatus="sta">
           <tr>
             <td height="30" align="center">${sta.index+1 }</td>
             <td>${NoDescPerson.province }</td>
             <td>${NoDescPerson.city }</td>
             <td>${NoDescPerson.country }</td>
             <td>${NoDescPerson.name }</td>
             <td>${NoDescPerson.cardid }</td>
             <td>${NoDescPerson.ishonest }</td>
             <td>
             <c:if test="${user.sysStatus=='3' }"> 
             	<a href="#"  onclick="business.addMainContentParserHtml('ypfj/ypfjGerenInput.do','noDescID=${NoDescPerson.id}&listPageNum=${ypfjPerson_List.pageNum}');"><button class="btn btn-success btn-xs" data-toggle="modal" data-target="#myModal">编辑</button></a>
             	<a href="#" onclick="delDataPer('${NoDescPerson.id}','${NoDescPerson.personalid}');"><button class="btn btn-success btn-xs" data-toggle="modal" data-target="#myModal">删除</button></a>
             </c:if>
             </td>
           </tr>
           </c:forEach>
         </tbody>
       </table>
    </div>
        <div class="page">
        <span style="padding:12px; float:left; color:#0099cc;">共${ypfjPerson_List.total}条记录</span>
        <ul class="pagination" id="pageCon"></ul>
    </div>

</div>
<script language="JavaScript">
//导出Excel表格



$(document).ready(function() {
	$(".topnav").accordion({
		accordion:false,
		speed: 500,
		closedSign: '[+]',
		openedSign: '[-]'
	});
});
//删除数据
function delDataPer(ID,personID){
	swal({
    	title: "您确定要执行删除操作吗？",
        type: "warning",
        showCancelButton: true,
        closeOnConfirm: false,
        confirmButtonText: "是的，我要删除",
        confirmButtonColor: "#ec6c62"
    }, function() {
    	$.ajax({
        	url: WEBPATH+"/ypfj/delPerson_YPFJ.do",
        	data:{ID : ID,personID : personID },
            type: "POST"
        }).done(function(data) {
        	if(data.type=="success" ){ 
        		swal("操作成功!", "已成功删除！", "success");
        		business.addMainContentParserHtml('ypfj/personList.do','pageNum=${ypfjPerson_List.pageNum}');
        	}else{
        		swal("操作失败!", "删除数据失败了!", "error");
        	}
        }).error(function(data) {
            swal("服务异常!", "删除操作失败了!", "error");
        });
    });
}
</script>
</body>
</html>
