<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<c:set var="webpath">${pageContext.request.contextPath}</c:set>

<script type="text/javascript" src="${webpath}/static/businessJs/xxcj/xxcjMain.js"></script>
 
<body>
<!--框架左侧菜单 开始-->

<div class="frame_left">
	<div class="left_menu">
	<%-- 
        <div class="left_menu_bt"><img src="${webpath}/static/images/caiji_1.png" style="margin-right:5px;" />一票否决集体</div>
        <div class="tree">
            <ul class="topnav">
                <li><a href="#"  onclick="macroMgr.onLevelTwoMenuClick(null, 'ypfj/provinceGroup.do')" class="active"><img src="${webpath}/static/images/shubiao.png" />省级环境质量变化</a></li>
                <li><a href="#"  onclick="macroMgr.onLevelTwoMenuClick(null, 'ypfj/cityGroup.do')" class="active"><img src="${webpath}/static/images/shubiao.png" />市级环境质量变化</a></li>
                <li><a href="#"  onclick="macroMgr.onLevelTwoMenuClick(null, 'ypfj/countryGroup.do')" class="active"><img src="${webpath}/static/images/shubiao.png" />县级环境质量变化</a></li>
            </ul>
        </div> --%> 
        <div class="left_menu_bt"><img src="${webpath}/static/images/caiji_1.png" style="margin-right:5px;" />一票否决个人</div>
        <div class="tree">
            <ul class="topnav">
                <li><a href="javascript:void(0);"  onclick="macroMgr.onLevelTwoMenuClick(null, 'ypfj/personList.do')"><img src="${webpath}/static/images/shubiao.png" />廉洁执法</a></li>
            </ul>
        </div>       
    </div>
</div>

<!--框架左侧菜单 结束-->

<!-- 页面中部~中间 start  -->
<div id="main_content" >
    

</div>
<script type="text/javascript">
//删除数据
function delData_ypfj(ID,areaCode,flag,pagenum){
	swal({
    	title: "您确定要执行删除操作吗？",
        type: "warning",
        showCancelButton: true,
        closeOnConfirm: false,
        confirmButtonText: "是的，我要删除",
        confirmButtonColor: "#ec6c62"
    }, function() {
    	$.ajax({
        	url: WEBPATH+"/ypfj/delGroup_YPFJ.do",
        	data:{ID : ID,areaCode : areaCode },
            type: "POST"
        }).done(function(data) {
        	if(data.type=="success" ){ 
        		swal("操作成功!", "已成功删除！", "success");
        		if(flag=="1"){
        			business.addMainContentParserHtml('ypfj/provinceGroup.do','pageNum='+pagenum);
        		}else if(flag=="2"){
        			business.addMainContentParserHtml('ypfj/cityGroup.do','pageNum='+pagenum);
        		}else if(flag=="3"){
        			business.addMainContentParserHtml('ypfj/countryGroup.do','pageNum='+pagenum);
        		}
        		
        	}else{
        		swal("操作失败!", "删除数据失败了!", "error");
        	}
        }).error(function(data) {
            swal("服务异常!", "删除操作失败了!", "error");
        });
    });
}
</script>
</body>