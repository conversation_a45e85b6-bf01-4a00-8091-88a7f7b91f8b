<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
</head>
<body>
<div class="center_weizhi">当前位置：一票否决 - 一票否决个人 - 廉洁执法</div>
<div class="center">
<form id="noDescPersonForm">
<div class="center_list">
    <table align="center" class="table_input">
      <tbody>
        <tr>
          <td>所属行政区</td>
          <td><div style="float:left; margin-right:5px;">
          <div class="form-group">
            <select id="province" name="province" class="form-control">
              <option value="" <c:if test="${noDescPerson.areacode=='' }">selected</c:if>>省（直辖市）</option>
              <c:forEach items="${electionPersonAreas }" var="electionPersonArea">
              <option value="${fn:substring(electionPersonArea.areacode,0,2) }000000" <c:if test="${fn:substring(noDescPerson.areacode,0,2)==fn:substring(electionPersonArea.areacode,0,2) }">selected</c:if> >${electionPersonArea.province }</option>
              </c:forEach>
            </select>
            </div>
          </div>
            </td>
        </tr>
        <tr>
          <td width="200">候选人姓名</td>
          <td>
          <div class="form-group">
          <select id="personName" name="personName" class="form-control" >
          	<option value="">请选择候选人姓名</option>
          </select>
          </div>
        </tr>
        <tr>
          <td>候选人身份证号码</td>
          <td>
          <div class="form-group">
          <input id="personCardID" name="personCardID" type="text" class="form-control input-sm" value="${noDescPerson.cardid }"  placeholder="请输入候选人身份证号码" readonly="readonly">
          </div>
          </td>
        </tr>
        <tr>
          <td>是否廉洁执法</td>
          <td>
          <div class="form-group">
          <select id="ishonest" name="ishonest" class="form-control">
            <option value="" <c:if test="${noDescPerson.ishonest=='' }">selected</c:if>>请选择</option>
            <option <c:if test="${noDescPerson.ishonest=='否' }">selected</c:if>>否</option>
          </select>
          </div>
          </td>
        </tr>
        <tr>
          <td align="center">&nbsp;
          <input name="id" type="hidden" value="${noDescPerson.id}">
          <input name="personalid" id="personalid" type="hidden" value="${noDescPerson.personalid}">
          <input name="oldPersonalID" type="hidden" value="${noDescPerson.personalid}">
          </td>
          <td style="text-align:left;"><a href="#">
            <button id="noDesc_Butt" type="button" class="btn btn-primary" name="signup" value="Sign up" style="font-size:16px;width:150px; margin-top:5px;">信息保存</button>
          </a></td>
        </tr>
      </tbody>
    </table>
</div>
</form>
</div>
<script language="JavaScript">

	//表单校验
	$(document).ready(function() {
	 
	$('#noDescPersonForm').formValidation({
		message: 'This value is not valid',
	    icon: {
	    	valid: 'glyphicon glyphicon-ok',
	        invalid: 'glyphicon glyphicon-remove',
	        validating: 'glyphicon glyphicon-refresh'
	    },
	    autoFocus: true,
	    fields: {
	        province: {
	        	validators: {
	            	notEmpty: {
	                	message: '请选择省份.'
	                }
	            }
	        },
	        personName: {
	        	validators: {
	        		notEmpty: {
	                	message: '请选择候选人.'
	                }
	            }
	        },
	        personCardID: {
	        	validators: {
	        		notEmpty: {
	                	message: '请输入候选人身份证号码.'
	                }
	            }
	        },
	        ishonest: {
	        	validators: {
	            	notEmpty: {
	                	message: '请选择是否廉洁执法.'
	                }
	            }
	        }
	        
	        }
	    });
	});

	//表单提交
	$(document).ready(function(){
		$("#noDesc_Butt").click(function(){
			var validate = false;
			$("#noDescPersonForm").data('formValidation').validate();
			validate = $("#noDescPersonForm").data('formValidation').isValid();
			if(validate){
				var options = {
					url:WEBPATH+'/ypfj/saveNoDescPerson.do',
					type:'post',
					success:function(data){
					if(data.type=="error"){
						swal("保存失败!", "", "error");
		                return false;
		             }else if(data.type=="success"){
		             	swal("保存成功!", "", "success");
				        business.addMainContentParserHtml(data.backUrl,'');
		               return false;
		             }	
					},
					error:function(){
						swal("服务异常,保存失败!", "", "error");
					}
				};
				$("#noDescPersonForm").ajaxSubmit(options);
			}else if(validate==null){
				//表单未填写
				$("#noDescPersonForm").data('formValidation').validate();
	       
	        }
		});
	});
	
	//根据行政区查询人员
	function getPersonsByareaCode(areaCode,defaultVal){
		if(areaCode!=""&&typeof(areaCode)!=undefined&&areaCode!=null){
			$.ajax({
			   type: "POST",
			   url: WEBPATH+'/ypfj/getPersonByArea.do',
			   data:{areaCode:areaCode,defaultVal:defaultVal},
			   async:false,
			   success: function(data){
			   			if(data.result=="error"){
							swal({title: "查询失败",text: "",type:"error"});
			               	return false;
				       	}else if(data.result=="success"){
						   if(data.data.length>0){
				               $("#personName").empty();
				               $("#personName").append("<option value=''>请选择候选人姓名</option>");
					       }
					       var option="";
					       for (var int = 0; int < data.data.length; int++) {
							   if(data.data[int].cardid==defaultVal&&defaultVal!=""){
								   option +="<option value="+data.data[int].cardid+" pid="+data.data[int].id+" selected >"+data.data[int].name+"</option>";
							   }else{
								   option +="<option value="+data.data[int].cardid+" pid="+data.data[int].id+" >"+data.data[int].name+"</option>";
							   }
						   }
						   $("#personName").append(option);
			       		}
			   }
			});
		}
	};
	
	$("#province").change(function(){
		var areaCode = $("#province").val();
		var defaultVal = '';
		getPersonsByareaCode(areaCode,defaultVal);
	});
	
	$(document).ready(function(){
		var areaCode = '${fn:substring(noDescPerson.areacode,0,2)}';
		var defaultVal = '${noDescPerson.cardid}';
		getPersonsByareaCode(areaCode,defaultVal);
	});
	
	$("#personName").change(function(){
		var personCarId = $("#personName").val();
		var pid = $("#personName").find("option:selected").attr("pid");
		if(personCarId!=""&&personCarId!=null&&typeof(personCarId)!=undefined){
			$("#personCardID").attr("value",personCarId);
		}else{
			$("#personCardID").attr("value","请输入候选人身份证号码");
		}
		if(pid!=""&&pid!=null&&typeof(pid)!=undefined){
			$("#personalid").attr("value",pid);
		}else{
			$("#personalid").attr("value","0");
		}
	});


$(document).ready(function() {
	$(".topnav").accordion({
		accordion:false,
		speed: 500,
		closedSign: '[+]',
		openedSign: '[-]'
	});
});

</script>
</body>
</html>
