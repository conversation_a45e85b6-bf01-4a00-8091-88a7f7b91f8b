<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
<script type="text/javascript">
	//分页
	$(document).ready(function(){
		business.listenEnter("searchButt");
		var curentPage = eval('${ypfjUnit_List.pageNum}');
		var totalPage = eval('${ypfjUnit_List.pages}');
		if(totalPage>0){
			var options = {
				bootstrapMajorVersion: 3,
			    currentPage: curentPage,
			    totalPages: totalPage,
			    numberOfPages: 5,
			    itemTexts: function (type, page, current) {
			            switch (type) {
			            case "first":
			                return "首页";
			            case "prev":
			                return "&laquo;";
			            case "next":
			                return "&raquo;";
			            case "last":
			                return "尾页";
			            case "page":
			                return page;
			            }
			    },
			    onPageClicked: function (event, originalEvent, type, page) {
	            	business.addMainContentParserHtml(WEBPATH+'/ypfj/provinceGroup.do?pageNum='+page,$("#searchForm").serialize());
	            }
	    	};
	    	$('#pageCon').bootstrapPaginator(options);
    	}
	});
	
	$(document).ready(function(){
		$("#searchButt").click(function(){
			business.addMainContentParserHtml("ypfj/provinceGroup.do",$("#searchForm").serialize());
		});
		
		//导出execl表单
		$("#viewExcel").click(function(){
			window.location.href= WEBPATH+'/ypfj/downGroupExcel.do?areaType=1';
		});
		
	});
</script>
</head>

<body>
<div class="center_weizhi">当前位置：一票否决 - 一票否决集体 - 省级环境质量变化</div>
<div class="center">
<div class="center_list">
    <div class="panel-group" id="accordion">
      <h4 class="panel-title">
      		<c:if test="${user.sysStatus=='3' }"> 
            <div class="btn-group">
               <a href="#" onclick="macroMgr.onLevelTwoMenuClick(null, 'ypfj/provinceGroupInput.do')"><button type="button" class="btn btn-success">环境质量变化信息录入</button></a>
            </div>
            </c:if>
           <a href ="#"> <button type="button" id ="viewExcel"  class="btn btn-primary">导出EXCEL</button>  </a>
      </h4>
    <br/>
    </div>
        <table class="table table-bordered table-hover table-condensed">
         <thead>
           <tr>
             <td width="50" height="30" bgcolor="#efefef">序号</td>
             <td bgcolor="#efefef">省</td>
             <td width="150" bgcolor="#efefef">PM2.5上升</td>
             <td width="150" bgcolor="#efefef">PM10上升</td>
             <td width="150" bgcolor="#efefef">断面水质类别下降</td>
             <td width="150" bgcolor="#efefef">操作</td>
           </tr>
         </thead>
         <tbody>
           <c:forEach items="${ypfjUnit_List.list}" var="NoDescUnit" varStatus="sta">
           	<tr>
             <td height="30" align="center">${sta.index+1}</td>
             <td>${NoDescUnit.province }</td>
             <td>${NoDescUnit.pm25rise }</td>
             <td>${NoDescUnit.pm10rise }</td>
             <td>${NoDescUnit.shuizhixiajiang }</td>
             <td>
             <c:if test="${user.sysStatus=='3' }"> 
             	<a href="#" onclick="business.addMainContentParserHtml('ypfj/provinceGroupInput.do','noDescID=${NoDescUnit.id}&listPageNum=${ypfjUnit_List.pageNum}');"><button class="btn btn-success btn-xs" data-toggle="modal" data-target="#myModal">编辑</button></a>
             	<a href="#" onclick="delData_ypfj('${NoDescUnit.id}','${NoDescUnit.unitareacode}','1','${ypfjUnit_List.pageNum}');"><button class="btn btn-success btn-xs" data-toggle="modal" data-target="#myModal">删除</button></a>
             </c:if>
             </td>
           </tr>
           </c:forEach>
         </tbody>
       </table>
    </div>
        <div class="page">
        <span style="padding:12px; float:left; color:#0099cc;">共${ypfjUnit_List.total}条记录</span>
        <ul class="pagination" id="pageCon"></ul>
    </div>

</div>
<script language="JavaScript">

$(document).ready(function() {
	$(".topnav").accordion({
		accordion:false,
		speed: 500,
		closedSign: '[+]',
		openedSign: '[-]'
	});
});

</script>
</body>
</html>
