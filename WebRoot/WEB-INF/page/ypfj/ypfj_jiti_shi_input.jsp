<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
<script type="text/javascript">
$(document).ready(function() {
	var areacode="${noDescUnit.unitareacode }";
	if(typeof(areacode)!="undefined"&&areacode!=""&&areacode!=null){
		business.cascadedDLB(areacode,'city','2');
	}
	
});

</script>
</head>
<body>
<div class="center_weizhi">当前位置：一票否决 - 一票否决集体 - 市级环境质量变化</div>
<div class="center">
<form id="noDescUnitForm">
<div class="center_list">
    <table align="center" class="table_input">
      <tbody>
        <tr>
          <td>所属行政区</td>
          <td><div style="float:left; margin-right:5px;">
          <div class="form-group">
            <select id="province" name="province" class="form-control" <c:if test="${noDescUnit.unitareacode!=''&&noDescUnit.unitareacode!= null }">disabled</c:if> onchange="business.cascadedDLB($('#province').val(),'city','2');" >
              <option value="" <c:if test="${noDescUnit.unitareacode=='' }">selected</c:if>>省（直辖市）</option>
              <c:forEach items="${electionUnits }" var="electionUnit">
              <option value="${electionUnit.areacode }" <c:if test="${fn:substring(noDescUnit.unitareacode,0,2)==fn:substring(electionUnit.areacode,0,2) }">selected</c:if> >${electionUnit.areaname }</option>
              </c:forEach>
            </select>
          </div>
          </div>
            <div style="float:left; margin-right:5px;">
              <div class="form-group">
              <select id="city" name="city" class="form-control" <c:if test="${noDescUnit.unitareacode!=''&&noDescUnit.unitareacode!= null }">disabled</c:if>>
                <option value="">地市</option>
              </select>
              </div>
            </div>
            </td>
        </tr>
        <tr>
        <td>&nbsp;</td>
        <td style="color:red;">
        	提示：若该单位被一票否决，则它的上级单位将被一票否决，请到相应的【省级环境质量变化】菜单中录入一票否决的信息
        </td>
        </tr>
        <tr>
          <td width="200">PM2.5上升</td>
          <td>
          <div class="form-group">
          <select id="pm25rise" name="pm25rise" class="form-control">
            <option value="" <c:if test="${noDescUnit.pm25rise=='' }">selected</c:if> >请选择</option>
            <option <c:if test="${noDescUnit.pm25rise=='是' }">selected</c:if> >是</option>
            <option <c:if test="${noDescUnit.pm25rise=='否' }">selected</c:if> >否</option>
          </select>
          </div>
          </td>
        </tr>
        <tr>
          <td>PM10上升</td>
          <td>
          <div class="form-group">
          <select id="pm10rise" name="pm10rise" class="form-control">
            <option value="" <c:if test="${noDescUnit.pm10rise=='' }">selected</c:if> >请选择</option>
            <option <c:if test="${noDescUnit.pm10rise=='是' }">selected</c:if> >是</option>
            <option <c:if test="${noDescUnit.pm10rise=='否' }">selected</c:if> >否</option>
          </select>
          </div>
          </td>
        </tr>
        <tr>
          <td>断面水质类别下降</td>
          <td>
          <div class="form-group">
          <select id="shuizhixiajiang" name="shuizhixiajiang" class="form-control">
            <option value="" <c:if test="${noDescUnit.shuizhixiajiang=='' }">selected</c:if> >请选择</option>
            <option <c:if test="${noDescUnit.shuizhixiajiang=='是' }">selected</c:if> >是</option>
            <option <c:if test="${noDescUnit.shuizhixiajiang=='否' }">selected</c:if> >否</option>
          </select>
          </div>
          </td>
        </tr>
        <tr>
          <td align="center">&nbsp;
          <input type="hidden" name="id" value="${noDescUnit.id }">
          <input type="hidden" name="oldUnitID" value="${noDescUnit.unitid }">
          <input type="hidden" name="areaCode" value="${noDescUnit.unitareacode }">
          <input type="hidden" name="areaType" value="2">
          </td>
          <td style="text-align:left;"><a href="#" id="noDesc_Butt">
            <button type="button" class="btn btn-primary" name="signup" value="Sign up" style="font-size:16px;width:150px; margin-top:5px;">信息保存</button>
          </a></td>
        </tr>
      </tbody>
    </table>
</div>
</form>
</div>
<script language="JavaScript">

	//表单校验
	$(document).ready(function() {
	 
	$('#noDescUnitForm').formValidation({
		message: 'This value is not valid',
	    icon: {
	    	valid: 'glyphicon glyphicon-ok',
	        invalid: 'glyphicon glyphicon-remove',
	        validating: 'glyphicon glyphicon-refresh'
	    },
	    autoFocus: true,
	    fields: {
	        province: {
	        	validators: {
	            	notEmpty: {
	                	message: '请选择省份.'
	                },
	                callback: {
	                    message: "该省份地市级已添加,请重新选择.",
	                    callback: function(value, validator, $field) {
	                    	var areaCode = $("#city").val();
							if(areaCode!=""){
								return checkRepeat(areaCode,'2');
							}else{
								return true;
							}
	                    }
	                }
	            }
	        },
	        city: {
	        	validators: {
	        		notEmpty: {
	                	message: '请选择地市.'
	                }
	            }
	        },
	        pm25rise: {
	        	validators: {
	        		notEmpty: {
	                	message: '请选择PM2.5是否上升.'
	                }
	            }
	        },
	        pm10rise: {
	        	validators: {
	        		notEmpty: {
	                	message: '请选择PM10是否上升.'
	                }
	            }
	        },
	        shuizhixiajiang: {
	        	validators: {
	            	notEmpty: {
	                	message: '请选择断面水质类别是否下降.'
	                }
	            }
	        }
	        
	        }
	    });
	});
	
	//行政区关联验证
	$('#province').on('change', function(){
		$('#noDescUnitForm').data('formValidation').revalidateField("city");
	});

	//表单提交
	$(document).ready(function(){
		$("#noDesc_Butt").click(function(){
			var validate = false;
			$("#noDescUnitForm").data('formValidation').validate();
			validate = $("#noDescUnitForm").data('formValidation').isValid();
			if(validate){
				var pm25riseVal = $("#pm25rise").val();
				var pm10riseVal = $("#pm10rise").val();
				var shuizhixiajiangVal = $("#shuizhixiajiang").val();
				if(pm25riseVal=="否"&&pm10riseVal=="否"&&shuizhixiajiangVal=="否"){
					swal("至少应有一项环境质量变化选择‘是’!", "", "error");
				}else{
					var options = {
						url:WEBPATH+'/ypfj/saveNoDescUnit.do',
						type:'post',
						success:function(data){
						if(data.type=="error"){
							swal("保存失败!", "", "error");
			                return false;
			             }else if(data.type=="success"){
			             	swal("保存成功!", "", "success");
				            business.addMainContentParserHtml(data.backUrl,'');
			               return false;
			             }	
						},
						error:function(){
							swal("服务异常,保存失败!", "", "error");
						}
					};
					$("#noDescUnitForm").ajaxSubmit(options);
				}
			}else if(validate==null){
				//表单未填写
				$("#noDescUnitForm").data('formValidation').validate();
	       
	        }
		});
	});
	
	//根据行政区检查重复
	function checkRepeat(areaCode,areaType){
		var flag = false;
		$.ajax({
		   type: "POST",
		   url: WEBPATH+'/ypfj/checkRepeaNoDescUnit.do',
		   data:{areaCode:areaCode,areaType:areaType},
		   async:false,
		   success: function(data){
			   if(data.data.length>0){
	               flag = false;
		       }else if(data.data.length==0){
		           flag = true;
		       }else{
		           flag = false;
		       }
		   }
		});
		
		return flag;
	}


$(document).ready(function() {
	$(".topnav").accordion({
		accordion:false,
		speed: 500,
		closedSign: '[+]',
		openedSign: '[-]'
	});
});

</script>
</body>
</html>
