<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<html>
<head>
<script type="text/javascript">

	$(document).ready(function(){
		business.listenEnter("searchButt");
		$("#searchButt").click(function(){
			business.addMainContentParserHtml("jcpf/jcpfList.do",$("#searchForm").serialize());
		});
		$("#scoredStateSelect").change(function(){
			business.addMainContentParserHtml("jcpf/jcpfList.do",$("#searchForm").serialize());
		});
		$("#fileTypeSelect").change(function(){
			business.addMainContentParserHtml("jcpf/jcpfList.do",$("#searchForm").serialize());
		});
	});
	//分页
	$(document).ready(function(){
		var curentPage = eval('${pageBean.pageNum}');
		var totalPage = eval('${pageBean.pages}');
		if(totalPage>0){
			var options = {
				bootstrapMajorVersion: 3,
			    currentPage: curentPage,
			    totalPages: totalPage,
			    numberOfPages: 5,
			    itemTexts: function (type, page, current) {
			            switch (type) {
			            case "first":
			                return "首页";
			            case "prev":
			                return "&laquo;";
			            case "next":
			                return "&raquo;";
			            case "last":
			                return "尾页";
			            case "page":
			                return page;
			            }
			    },
			    onPageClicked: function (event, originalEvent, type, page) {
	            	business.addMainContentParserHtml(WEBPATH+'/jcpf/jcpfList.do?pageNum='+page,$("#searchForm").serialize());
	            }
	    	};
	    	$('#pageCon').bootstrapPaginator(options);
    	}
	});

</script>
</head>
<body>
<div class="center_weizhi">当前位置：交叉评分 - 交叉评分结果 - 交叉案卷评分</div>
<div class="center">
<div class="center_list">
    <div class="panel-group" id="accordion">
          <h4 class="panel-title">
            <!---信息填报
            <div class="btn-group" style="margin-right:20px;float:left;">
               <a href="CementEnterprises_input.html"><button type="button" class="btn btn-success" style="font-size:16px;">企业信息录入</button></a>
            </div>--->          
            
         <form role="form" method="get" id ="searchForm" name ="form">
          <select id ="scoredStateSelect" class="form-control" style="width:150px;margin-right:5px;" name="scoredState" >
                 <option value ="">请选择评分状态</option>
                  <!-- 1代表已评状态，0代表未评状态 -->
                 <option value="1" <c:if test="${scoredState=='1' }">selected</c:if> >已评</option>
                 <option value="0" <c:if test="${scoredState=='0' }">selected</c:if>>未评</option>
              </select>
              
              <select id ="fileTypeSelect" class="form-control" style="width:300px;margin-right:5px;" name ="fileType">
              <!-- 0代表 行政处罚案卷 ；1代表按日计罚案卷； 2代表移送行政拘留案卷；3代表涉嫌犯罪移送案卷；6查封扣押案卷；7涉嫌犯罪移送案卷   -->
                 <option value = ""  >请选择案卷类型</option>
                 <option value="0" <c:if test="${fileType=='0' }">selected</c:if>>行政处罚案卷</option>
                 <option value="1" <c:if test="${fileType=='1' }">selected</c:if>>按日计罚案卷</option>
                 <option value="6" <c:if test="${fileType=='6' }">selected</c:if>>查封扣押案卷</option>
                 <option value="7" <c:if test="${fileType=='7' }">selected</c:if>>限产停产案卷</option>
                 <option value="2" <c:if test="${fileType=='2' }">selected</c:if>>移送行政拘留案卷</option>
                 <option value="3" <c:if test="${fileType=='3' }">selected</c:if>>涉嫌犯罪移送案卷</option>
                 <option value="5" <c:if test="${fileType=='5' }">selected</c:if>>发现问题的污染源现场监督检查稽查案卷</option>
              </select>
              
        
          
            <!---搜索--->
            <div style="width:260px;" class="btn-group">
                  <div class="row">                     
                     <div class="col-lg-6">
                        <div class="input-group">
                           <input type="text" class="form-control" name ="fileCode" value="${fileCode }" style="width:200px;" placeholder="案卷文号关键字">
                           <span class="input-group-btn">
                              <button class="btn btn-success" id ="searchButt" type="button">
                                 快速搜索
                              </button>
                           </span>
                        </div><!-- /input-group -->
                     </div><!-- /.col-lg-6 -->
                  </div><!-- /.row -->
              </div>
           </form>
          </h4>
            </div>            
    </div>
    
        <table class="table table-bordered table-hover table-condensed">
         <thead>
           <tr>
             <td width="50" height="30" bgcolor="#efefef">序号</td>
             <td bgcolor="#efefef">案卷文号</td>
             <td width="270" bgcolor="#efefef">案卷类型</td>
             <td width="100" bgcolor="#efefef">交叉骨干姓名</td>
             <td width="70" bgcolor="#efefef">初评得分</td>
             <td width="60" bgcolor="#efefef">状态</td>
             <td width="100" bgcolor="#efefef">操作</td>
           </tr>
         </thead>
         <tbody>
         <c:if test="${not empty pageBean.list }">
         <c:forEach  varStatus="id"  items="${pageBean.list}" var="crossHandList">
         	<tr>
         		<td height="30" align="center" >${ id.index + 1}</td>
         		<td>${crossHandList.fileCode}</td>
         		<td>${crossHandList.fileType}</td>
         		<td>${crossHandList.name}</td>
         		<td>${crossHandList.expertFinalScore }</td>
         		<c:choose>
					<c:when test="${crossHandList.scoredState == '1' }">
						<td><button class="btn btn-success btn-xs" data-toggle="modal" data-target="#myModal">已评</button></td>
					</c:when>
					<c:otherwise>
						<td><button class="btn btn-warning btn-xs" data-toggle="modal" data-target="#myModal">未评</button></td>
					</c:otherwise>
				</c:choose>
		        <td><a  href="javascript:void(0);" onclick="macroMgr.onLevelTwoMenuClick(null, 'jcpf/jcpf_input.do?code=${crossHandList.code}&id=${crossHandList.id }&status=1' )"><button class="btn btn-info btn-xs" data-toggle="modal" data-target="#myModal">查看</button></a>
				
				<c:if test ="${sessionScope.sa_session.sysStatus == '2' and crossHandList.considerState != '1' }">
		         <a href="javascript:void(0);"   onclick="macroMgr.onLevelTwoMenuClick(null, 'jcpf/jcpf_input.do?code=${crossHandList.code}&id=${crossHandList.id }&pageNum=${pageNum }' )" ><button class="btn btn-primary btn-xs" data-toggle="modal" data-target="#myModal">评分</button>	</a>	
		    	 <!-- <a  href="javascript:void(0);" onclick="macroMgr.onLevelTwoMenuClick(null, 'jcpf/jcpf_input.do?code=${crossHandList.code}&id=${crossHandList.id } )"><button class="btn btn-primary btn-xs" data-toggle="modal" data-target="#myModal">评分</button></a>-->
		    	</c:if>
		    	
				</td>			        
         	</tr>
         </c:forEach>
         </c:if>
         </tbody>
       </table>
    <div class="page">
    <span style="padding:12px; float:left; color:#0099cc;">共${pageBean.total}条记录</span>
        <ul class="pagination" id="pageCon">
        </ul>
    </div>
    </div>
<script type="text/javascript">

$(document).ready(function() {
	$(".topnav").accordion({
		accordion:false,
		speed: 500,
		closedSign: '[+]',
		openedSign: '[-]'
	});
});

</script>
</body>
</html>
