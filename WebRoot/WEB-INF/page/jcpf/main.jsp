<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<c:set var="webpath">${pageContext.request.contextPath}</c:set>

<script type="text/javascript" src="${webpath}/static/businessJs/xxcj/xxcjMain.js"></script>
 
<body>
<!--框架左侧菜单 开始-->

<div class="frame_left">
	<div class="left_menu">
        <div class="left_menu_bt"><img src="${webpath}/static/images/caiji_1.png" style="margin-right:5px;" />交叉评分结果</div>
        <div class="tree">
            <ul class="topnav">
            	<c:if test="${sessionScope.sa_session.crossType != 'crossB'}"> 
                <li><a href="#" onclick="macroMgr.onLevelTwoMenuClick(null, 'jcpf/jcpfList.do')" ><img src="${webpath}/static/images/shubiao.png" />交叉案卷评分</a></li>
               	</c:if>
               	<%-- <c:if test="${sessionScope.sa_session.crossType == 'crossB'}"> 
             	  	<li><a href="#" onclick="macroMgr.onLevelTwoMenuClick(null, 'jcpf/perSonShiJiList.do')" ><img src="${webpath}/static/images/shubiao.png" />个人事迹评分</a></li> 
               	</c:if> --%>
              <%--   --%>
            </ul>
        </div> 
    </div>
</div>
<!--框架左侧菜单 结束-->

<!-- 页面中部~中间 start  -->
<div id="main_content" >
    

</div>
</body>