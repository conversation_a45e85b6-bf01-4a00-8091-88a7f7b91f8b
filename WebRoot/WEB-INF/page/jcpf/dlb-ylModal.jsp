<%@ page language="java" contentType="text/html; charset=utf-8"
    pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<div class="modal-header">
			<div style="float:right; margin-top:-5px;">
              <!-- <button type="button" class="btn btn-info" data-dismiss="modal">打印</button>-->
			   <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
            </div>
			<h4 class="modal-title" >预览</h4>
</div>
<div class="modal-body">
		
	<div class="smart-widget-body form-horizontal">
			<div class="pricing-value">
				<div id="B1"></div>
			</div>
	</div>
</div>
<div class="modal-footer"> 
	
</div>

<script type="text/javascript">
$(document).ready(function(){
    debugger
		$('#view').on('hide.bs.modal', function () {
			   $(this).removeData("bs.modal");  
		})
		var select="#B1";
			if(isIEWhether){
				if(PDFObject.supportsPDFs){
					PDFObject.embed(${fastdfs_addr},select);
				} else {
					 swal({ 
					        title: "提示",  
					        text: "您的浏览器不支持pdf预览功能，请安装pdf阅读器后重试！",  
					        type: "warning", 
					        showCancelButton: true, 
					        closeOnConfirm: true, 
					        confirmButtonText: "下载并安装", 
					        confirmButtonColor: "#ec6c62" 
					    }, function() { 
					        window.location.href=WEBPATH+"/sysUser/downloadPdfReader"
					    }); 
				}
			}else{
				var options = {
						pdfOpenParams: {
							navpanes: 0,
							toolbar: 0,
							statusbar: 0,
							page: 1
						},
						forcePDFJS: true,
						PDFJS_URL: WEBPATH+"/static/pdfjs/web/viewer.html"
				};
				PDFObject.embed(${fastdfs_addr},select, options);
			}

})

</script>