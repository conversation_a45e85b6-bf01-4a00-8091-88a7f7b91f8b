<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
	<meta http-equiv="pragma" content="no-cache">
	<meta http-equiv="cache-control" content="no-cache">
	<meta http-equiv="expires" content="0"> 
<head>
<style>
/*
PDFObject appends the classname "pdfobject-container" to the target element.
This enables you to style the element differently depending on whether the embed was successful.
In this example, a successful embed will result in a large box.
A failed embed will not have dimensions specified, so you don't see an oddly large empty box.
*/
.pdfobject-container {
	width: 100%;
	height:580px;
	margin: 2em 0;
}
.textarea-success {
	border-top:green 1px solid;
	border-bottom:green 1px solid; 
	border-left:green 1px solid;
    	border-right:green 1px solid;
}
.textarea-error {
	border-top:red 1px solid;
	border-bottom:red 1px solid; 
	border-left:red 1px solid;
    	border-right:red 1px solid;
}
</style>
<script type="text/javascript">
/* 	判断评分的状态scoredState为1 */
		 $(document).ready(function(){
			 var scoredState= eval('${crossHandlFileList.scoredstate}');
			 var status = eval('${status}');
			 //查看
			 if(status =='1'){
				 $("#chickAnjuan").hide();
				 $("#submitBtn").hide();
				 $("#fileCodeInput").hide();
				 $("#queren").hide();
				 $(".inputDisabled").attr("readonly","readonly");
				 $("#xiaZaiFuJian").removeAttr("disabled");
				 $("#xiaZai").removeAttr("disabled");
				 $("input:checkbox").attr("disabled",true);
			 }
			 if(scoredState == '1'){
				 if(status !='1'){
					$("#submitBtn").show();
					$("#jcpj").removeAttr("disabled");
					$("#jcpjYxdxanlituijian").attr("disabled",false);
					$("#chickAnjuan").hide();
					/*$("#fileCodeInput").attr("disabled","disabled");
					$("#fileCodeInput").val('${ crossHandlFileList.filecode}');*/
					$("#fileCodeInput").hide();
					 $("#queren").hide();
				 	$(".inputDisabled").removeAttr("readonly");
					//扣分理由
				 	$("textarea").removeAttr("disabled");
			 	}
			}else{
				 $(".inputDisabled").attr("readonly","readonly");
				 //$("input:checkbox").attr("disabled",true);
			 }
			});
		 /* 	评分状态的scoredState为0 */
			function chickAnjuan(){//开始评分
				var fileCodeInput = trim($("#fileCodeInput").val());
				var fileCode = trim($("#fileCode").text());
				if( fileCodeInput == fileCode){
				swal("开始打分", "开始打分!", "success");
				//隐藏开始打分按钮
				$("#chickAnjuan").hide();
				//移除打分div只读状态
				$(".inputDisabled").removeAttr("readonly");
				
				$("#xiaZaiFuJian").removeAttr("disabled");
				$("#xiaZai").removeAttr("disabled");
				//案卷处罚书 设为不可用
				$("#fileCodeInput").attr("disabled","disabled");	
				//案卷评价设为可用
				$("#jcpjYxdxanlituijian").attr("disabled",false)
				//显示保存按钮
				$("#submitBtn").show();
				//扣分理由
				$("textarea").removeAttr("disabled");
				$("input:checkbox").removeAttr("disabled");
				}else{
					swal("开始打分", "请输入正确的案卷号！", "error");
				}
			}
			function trim(str) {
				  return str.replace(/(^\s+)|(\s+$)/g, "");
				}
			//下载文件
			function xiaZaiAnJuan(){
			 //var fileCode = $("#fileCode").html();
			 var fileid  = $("#fileId").val();
		//	 var fileid  = $("#fileId").val();
			 $.ajax({
				    type:"post",
				    url:WEBPATH+'/jcpf/jcpfExistFileUrl.do',
				    data:{fileId:fileid },           //注意数据用{}
				    success:function(data){  //成功
					 if("yes" == data){
							window.location.href="${pageContext.request.contextPath}/jcpf/jcpfAnJuandown.do?fileid="+fileid;
						    return false;
				         }else if("no" == data){
				            	  swal( "操作失败","该案卷不存在!", "error");
				            	  return false;
						}else if("suffixerror" ==data){
							  swal( "操作失败","该案卷上传数据格式有问题!", "error");
			            	  return false;
						}
				         }
				});
			 }
			function loding(btn,itext){
				document.getElementById(btn).innerHTML = "加载.."
				document.getElementById(btn).disabled = "disabled"
			    setTimeout(function () {
			      document.getElementById(btn).innerHTML = itext;
			      document.getElementById(btn).removeAttribute("disabled");
			    },3000);
	}	
	 function loding(btn,itext){
		document.getElementById(btn).innerHTML = "加载.."
			document.getElementById(btn).disabled = "disabled"
		    setTimeout(function () {
		      document.getElementById(btn).innerHTML = itext;
		      document.getElementById(btn).removeAttribute("disabled");
		    },4000);
		}
</script>
</head>
<script type="text/javascript">

$(document).ready(function(){
		//保存数据方法
		var pageNum = $("#pageNum").val();
});
</script>
<div id='crossXzcfVue'>
<div   class="center_weizhi">当前位置：交叉评分 - 交叉评分结果 - 交叉案卷评分 - ${anJuanType }</div>
<div class="center">
<div class="center_list" id="anjuanxinxi">
	<input id="crossFileId"  type="hidden" value="${crossFileId }" >
	<input id="fileId" type="hidden" :value="scoringIndexList.fileid">
	<div class="dingwei">案卷或材料：<span style="color:#23b7e5;font-size:16px; font-weight:bold;" id ="fileCode">{{scoringIndexList.filecode }} </span> 
	<c:if test ="${sessionScope.sa_session.sysStatus == '2' }">	
		<a v-bind:href="scoringIndexList.downUrl" target="_Blank">
			<button class="btn btn-success btn-xs" disabled="true" id="xiaZai" data-toggle="modal" data-target="#myModal">下载</button>
		</a>	
		<span id="viewBtn" v-html='datas' ></span>

	</c:if>
	</div>
    <div class="dingwei" id ="queren">案卷处罚决定书文号确认：<input type="text" class="form-control" style="width:200px; float:right;"  id="fileCodeInput" placeholder="请输入案卷处罚决定书文号" ></div>
    <div class="dingwei"><button id ="chickAnjuan" class="btn btn-primary" data-toggle="modal"  onclick="chickAnjuan()" data-target="#myModal">开始评分</button></div>
 	   <table class="table table-striped table-bordered table-condensed" id="tableOuter" >
      <thead>
           <tr>
             <td width="50" height="30" bgcolor="#efefef">序号</td>
             <td width="100" bgcolor="#efefef">分指标</td>
             <td width="90" bgcolor="#efefef">分指标分值</td>
             <td width="200" bgcolor="#efefef">指标说明</td>
             <td bgcolor="#efefef"> <span style="float: left;">判断标准</span><span style="float: right;margin-right: 120px;">分值</span></td>
             <td bgcolor="#efefef" width="70">一票否决</td>
             <td bgcolor="#efefef" width="70">无需评查</td>
             <td bgcolor="#efefef" width="80">得分</td>
             <td width="15%" bgcolor="#efefef">扣分理由</td>
           </tr>
         </thead>
         	<tbody  class="form-group">
          		 <tr v-for="(scoringIndex, index) in scoringIndexList.crossHandlIndexScoreList">
		             <td height="30" align="center" style="vertical-align:middle;">{{index+1}}</td>
		             <td style="vertical-align:middle;">{{scoringIndex.indexname}}</td>
		             <td style="vertical-align:middle;">{{scoringIndex.indexscore}}</td>
		             <td style="vertical-align:middle; width:200px;">{{scoringIndex.indexdesc}}</td>
		             <td v-if="scoringIndex.crossHandlItemScoreList != null && scoringIndex.crossHandlItemScoreList.length !=0 "  style="padding:0;">
                        <table width="100%" border="0" cellspacing="0" cellpadding="0" style="padding:0;">
                          <tr  v-for="(scoringItem, index1) in scoringIndex.crossHandlItemScoreList">   
                          	<td style="border-bottom:solid 1px #ddd; border-right:solid 1px #ddd;vertical-align:middle;">
                                 <div style="vertical-align:middle; margin:0 5px 5px 0;">
		            			{{scoringItem.itemname}}（{{scoringItem.itemscore}}分）
		            			</div>
		            		<span style="color:red" v-if="scoringItem.isInCheck==1 && scoringIndex.inCheckValue!=1 && scoringIndex.voteDownValue!=1">
		            			&nbsp;&nbsp;是否无需评查：
                                			<input type="checkbox" id="" v-model="scoringItem.inCheckValue"
					              		  @change="wuxupingcha2(index,index1)" 
					              		  style="width:16px; height:16px;  display: table-cell;vertical-align: middle; text-align: center; ">
                                		</span>	
                            </td>                      
                            <td style="border-bottom:solid 1px #ddd; width:155px;vertical-align:middle;">
                                 <div  :id="index+'crossItem'+index1" style="margin:0 0 5px 0; float:left;">
	                                 <div v-if="scoringIndex.isAdd==1" style="float:left; padding:5px 2px 7px 3px; color:red; font-size:20px;">＋</div>
	                                 <div v-if="scoringIndex.isAdd!=1" style="float:left; padding:0 0 3px 0; margin:0; color:red; font-size:26px;">－</div>
	                                 <div style="float:right; padding-top:4px;">
	                                 	<input  @input="updateItem(index,index1,scoringItem.itemscore,scoringItem.temItemId)"  
	                                       v-model="scoringItem.score" type="text" :class="'checkitem'+index" :name = "index+'item'+index1"
	                                      class="form-control inputDisabled"   placeholder="请输入初评得分" style="width:125px;">
	                                 </div>
                                 	<div style="color:red; font-size: 12px;">{{scoringItem.validatorMessage}}
                                 	</div>
                                 </div>
                            </td>                          
                          </tr>
                        </table>
		             </td>
		            <td v-else >
		             <table width="100%" border="0" cellspacing="0" cellpadding="0">
		             	<tr>
		             		<td style="border-bottom:solid 1px #f7f7f7;">
                                 <div style="vertical-align:middle; margin:0 5px 5px 0;">
		            				{{scoringIndex.indexdesc}}
		            			</div>
                            </td>    
                            <td style="border-bottom:solid 1px #f7f7f7;">
                            	<div :id="'crossIndex'+index"  class="form-group" style="width: 150px;float: right;">
	                            	<div v-if="scoringIndex.isAdd==1" style="float:left; padding:5px 2px 7px 3px; color:red; font-size:20px;">+</div>
		                            <div v-if="scoringIndex.isAdd!=1" style="float:left; padding:0 0 3px 0; margin:0; color:red; font-size:26px;">-</div>
				                		<input   type="text"    v-model="scoringIndex.score" 
				                		 @input="updateIndex(index,scoringIndex.indexscore)" :class="'checkitem'+index"
				               	  		 class="form-control inputDisabled"  placeholder="请输入初评得分">
			         			  	<div style="color:red;font-size: 5px;">{{scoringIndex.validatorMessage}}</div>
			         			 </div>
                            </td>
		             	</tr>
		             </table>
		           </td>
		           <!-- 一票否决 -->
		            <td style="vertical-align:middle; text-align:center;">
		              <span v-if="scoringIndex.isVoteDown ==1" >
		              		 <input type="checkbox" :id="'checkbox'+index" 
		              		  @change="changeCheckBoxCli(index,'1')" v-model="scoringIndex.voteDownValue"
		              		  style="width:16px; height:16px; display: table-cell;vertical-align: middle; text-align: center;">
               				 <label :for="'checkbox'+index" class="checkbox-blue" checked></label>
		              </span>
		           </td>
		           <!-- 无需评查 -->
		            <td style="vertical-align:middle;text-align:center;">
		            <span v-if="scoringIndex.isInCheck ==1">
		              		 <input type="checkbox" id="checkbox1" v-model="scoringIndex.inCheckValue"
		              		  @change="wuxupingcha(index)" 
		              		  style="width:16px; height:16px;  display: table-cell;vertical-align: middle; text-align: center; ">
               				 <label for="checkbox1" class="checkbox-blue" checked></label>
		              </span>
		           </td>
		           <td style="vertical-align:middle;text-align:center;">
		           	  <input type="text" id="111" v-if="scoringIndex.calculScore==null || scoringIndex.calculScore==''" disabled="disabled" v-model="scoringIndex.resultScore" style="background-color:transparent; width:80px; border:0px;vertical-align:middle;text-align:center;">
		           	  <input type="text" id="222" v-if="scoringIndex.calculScore!=null && scoringIndex.calculScore!=''" disabled="disabled" v-model="scoringIndex.calculScore" style="background-color:transparent; width:80px; border:0px;vertical-align:middle;text-align:center;">
		           </td>
		           
		           <!-- 扣分理由 -->
	             <td >																   
	           	   <textarea @input="updateCommon(index)" :id="'comment'+index" :rows="scoringIndex.crossHandlItemScoreList.length" disabled class="form-control" v-model="scoringIndex.comment" placeholder="请输入扣分理由" style="width:100%;">
				   
				   </textarea>
				   <div style="color:red; font-size: 12px;">{{scoringIndex.validatorMessage}}</div>
	             </td>
		           
		           </tr>
		           <tr>
		           		<td></td>
		           		<td>打分合计</td>
		           		<td>100</td>
		           		<td></td>
		           		<td>打分合计：输入的分数总和</td>
		           		<td></td>
		           		<td></td>
		           		<td style="vertical-align:middle;text-align:center;">{{scoringIndexList.inputScore}}</td>
		           		<td></td>
		           </tr>
		           <tr>
		           		<td></td>
		           		<td>最终得分</td>
		           		<td>100</td>
		           		<td></td>
		           		<td>最终得分：打分合计 / [（满分-无需评查分数总和）/满分]，保存后计算</td>
		           		<td></td>
		           		<td></td>
		           		<td style="vertical-align:middle;text-align:center;">{{scoringIndexList.expertfinalscore}}</td>
		           		<td></td>
		           </tr>
		           <tr>
	           		<td height="30" colspan="9">
	           		<input disabled id="jcpjYxdxanlituijian" 
	           		  name = "jcpjYxdxanlituijian" type="checkbox"  v-on:click="jcpjCheckBoxClick()" >是否进行案卷评价
             		 <div class="col-sm-12" style="display: none;" id ="jcpjYxdxAnLiTuiJianReviews1"  >
             			<textarea disabled v-model="scoringIndexList.jcpjYxdxanlituijianreviews" rows="5" class="form-control" id="jcpj" placeholder="请输入案卷评价"></textarea>
             		 </div>
             		</td>
	           </tr>
          	</tbody>
       </table>
       	<a href="#"><button class="btn btn-primary" id="submitBtn" v-on:click="saveSubmit(scoringIndexList.id)" type="button" style="font-size:16px;width:150px;float:right;margin-right:30px;margin-bottom:30px; margin-top:5px;display:none">信息保存</button></a>
      	 <!-- <button type="button" onclick="saveSubmit()" id ="saveSubmit">信息保存</button> -->
<!--         <a href="#"><button type="button" class="btn btn-primary" id="saveSubmit" name="signup" value="Sign up" style="font-size:16px;width:150px; margin-top:5px; display:none">信息保存</button></a>
		 -->
         <div><input id ="pageNum" type="hidden" value="${pageNum }"></div>
 		<div class="submit">
 			<c:choose> 
 			 <c:when test = "${sessionScope.sa_session.sysStatus != 2}"> 
			 </c:when> 
			 <c:otherwise> 
			<!-- 	<a href="#"><button type="button" class="btn btn-primary" id="saveSubmit" name="signup" value="Sign up" style="font-size:16px;width:150px; margin-top:5px; display:none">信息保存</button></a>
			 --></c:otherwise> 
			</c:choose></div>
 	</div>
</div>
</div>
<div class="bottom">&copy;2016 北京长能环境大数据科技有限公司 研发并提供技术支持</div>
<script >
		var scoringIndexList =null;
		var crossFileId = $("#crossFileId").val();
		if(crossFileId != null){
		$.ajax({
		cache : true,
		type : "GET",
		async : false,
		//api/test/detail/behaviour/74
		url: WEBPATH+"/jcpf/getIndexList.do",
		data:{
			id:crossFileId,
		},
		error : function(request) {
			swal("错误!","请求异常！", "error");
		},
		success : function(data) {

		  	if(data.result =='success'){
		  		////console.log(data.data)
		  		scoringIndexList=data.data;
		  		//console.log(scoringIndexList);
		  	}
		} 
	});
			
		}
	var veiwBtnVue = new Vue({
		el:'#viewBtn',
		data:{
			datas:'<button class="btn btn-info btn-xs" id="xiaZaiFuJian" disabled="true" v-on:click="showPdfClick()">预览</button>',
		},
		
	});
	var xzcfVue = new Vue({
		  el: '#crossXzcfVue',
		  data: {
			  scoringIndexList:scoringIndexList,
		  },
		  
		  methods: {
			  jcpjCheckBoxClick:function(){
					if($("#jcpjYxdxAnLiTuiJianReviews1").css("display")=='none' ){//如果show是隐藏的
						$("#jcpjYxdxAnLiTuiJianReviews1").css("display","block"); //  yxdxanlituijianreviews show的display属性设置为block（显示）
						//$("#jcpjYxdxanlituijian").val("1");
						xzcfVue.scoringIndexList.jcpjYxdxanlituijian="1";
						}else{//如果show是显示的
						xzcfVue.scoringIndexList.jcpjYxdxanlituijianreviews="";
						xzcfVue.scoringIndexList.jcpjYxdxanlituijian="0";
						$("#jcpjYxdxAnLiTuiJianReviews1").css("display","none");//show的display属性设置为none（隐藏）
				}
			  },
			  changeCheckBoxCli:function(index,status){

				  //获取选中未选中的状态 stauts =1一票否决 status=2无需评查
				  if(status=='1'){
					  //一票否决
					var flag=  xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].voteDownValue;
					
					if(xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].inCheckValue == 1){
						xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].inCheckValue = 0;
						var data = xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].crossHandlItemScoreList;
				  		if(data!=null &&  data.length>0){
				  			for(var i=0;i<data.length;i++){
								xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].crossHandlItemScoreList[i].validatorFlag= false;
				  			}
				  		}else{
				  			xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].validatorFlag= false;
				  		}
				  		xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].score='';
				  		xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].resultScore='';
				  		$(checkitem).removeAttr("disabled");
				  		
				  		xzcfVue.scoringIndexList.wxpjScore -= xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].indexscore;
					}
					 
				  	var checkitem =".checkitem"+index;
				  	if(flag){//选择一票否决
				  		var data = xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].crossHandlItemScoreList;
				  		if(data!=null &&  data.length>0){
				  			for(var i=0;i<data.length;i++){
				  				data[i].score='';
				  				$("#"+index+"crossItem"+i).removeClass("has-error");	
								$("#"+index+"crossItem"+i).removeClass("has-success");
								xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].crossHandlItemScoreList[i].validatorMessage="";
				  			}
				  		}else{
				  			xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].score='';
				  			xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].resultScore='';
				  			$("#crossIndex"+index).removeClass("has-error");	
							$("#crossIndex"+index).removeClass("has-success");	
							xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].validatorMessage="";
				  		}
			  				xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].resultScore=0;
			  				xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].calculScore='';
				  		/* 	if(xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].isAdd ==1){
			  				//是否为加分项
			  			}else{
			  				//减分项
			  				//xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].resultScore=xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].indexscore;
			  			} */
				  		$(checkitem).attr("disabled","disabled");
			  			
			  			/**/
				  		/* var text = $("#comment"+index).val();
				  		if(text==null || text==''){
				  			$("#comment"+index).removeClass("textarea-success");	
					  		$("#comment"+index).addClass("textarea-error");	  
							xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].validatorFlag= false;
							xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].validatorMessage="选择一票否决，此项扣分理由必填";
				  		} */
				  		
				  		//恢复小指标无需评查
				  		var wxpcScoreLittle = 0;
				  		var data = xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].crossHandlItemScoreList;
				  		if(data!=null &&  data.length>0){
				  			for(var i=0;i<data.length;i++){
				  				data[i].score='';
						  		$("#"+index+"crossItem"+i).removeClass("has-error");	
								$("#"+index+"crossItem"+i).removeClass("has-success");
								xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].crossHandlItemScoreList[i].validatorMessage="";
								
								if(data[i].inCheckValue==1){//如果小指标勾选了无需评查，给它还原
									data[i].inCheckValue = 0;
									wxpcScoreLittle += data[i].itemscore;
								}
				  			}
				  		}
			  			xzcfVue.scoringIndexList.wxpjScore -= wxpcScoreLittle;
				  		
				  	}else{//取消一票否决
				  		xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].score='';
				  		xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].resultScore='';
				  		var data = xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].crossHandlItemScoreList;
				  		if(data!=null &&  data.length>0){
				  			for(var i=0;i<data.length;i++){
								xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].crossHandlItemScoreList[i].validatorFlag= false;
				  			}
				  		}else{
				  			xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].validatorFlag= false;
				  		}
				  		$(checkitem).removeAttr("disabled");
				  		
				  		/**/
				  		var text = $("#comment"+index).val();
				  		if(text==null || text==''){
				  			$("#comment"+index).removeClass("textarea-error");	
					  		$("#comment"+index).addClass("textarea-success");	  
							xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].validatorFlag= false;
							xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].validatorMessage="";
				  		}
				  		
				  	}
				  	 //总分技算
				  	 var num = 0;
					 var childNum =0;
			  		 var score1 = xzcfVue.scoringIndexList.crossHandlIndexScoreList;
					 if(score1 != null){
			  			for(var i=0;i<score1.length;i++){
		  					  if(!isNaN(parseFloat(score1[i].resultScore))){
		  						num = (Math.round((num + parseFloat(score1[i].resultScore))*100))/100; 
		  					  }
		  		 		}
				  	 }
					xzcfVue.scoringIndexList.inputScore =num;
				  }
			  },
			  wuxupingcha:function(index){//大指标无需评查
				  var flag=  xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].inCheckValue;//是否勾选
				  	var checkitem =".checkitem"+index;
				    var wxpcScoreLittle = 0;
				  	if(flag){//勾选
				  		
				  		if(xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].voteDownValue == 1){
				  			xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].voteDownValue = 0;
				  			xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].score='';
					  		xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].resultScore='';
					  		var data = xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].crossHandlItemScoreList;
					  		if(data!=null &&  data.length>0){
					  			for(var i=0;i<data.length;i++){
									xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].crossHandlItemScoreList[i].validatorFlag= false;
					  			}
					  		}else{
					  			xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].validatorFlag= false;
					  		}
						}
				  		
				  		var data = xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].crossHandlItemScoreList;
				  		if(data!=null &&  data.length>0){
				  			for(var i=0;i<data.length;i++){
				  				data[i].score='';
						  		$("#"+index+"crossItem"+i).removeClass("has-error");	
								$("#"+index+"crossItem"+i).removeClass("has-success");
								xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].crossHandlItemScoreList[i].validatorMessage="";
								
								if(data[i].inCheckValue){//如果小指标勾选了无需评查，给它还原
									data[i].inCheckValue = '';
									wxpcScoreLittle += data[i].itemscore;
								}
				  			}
				  		}
			  			xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].resultScore = '';
			  			xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].calculScore = '';
			  			xzcfVue.scoringIndexList.wxpjScore += xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].indexscore - wxpcScoreLittle;
			  			
				  		$(checkitem).attr("disabled","disabled");
				  	}else{//取消
				  		var data = xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].crossHandlItemScoreList;
				  		if(data!=null &&  data.length>0){
				  			for(var i=0;i<data.length;i++){
								xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].crossHandlItemScoreList[i].validatorFlag= false;
				  			}
				  		}else{
				  			xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].validatorFlag= false;
				  		}
				  		xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].score='';
				  		xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].resultScore='';
				  		$(checkitem).removeAttr("disabled");
				  		
				  		xzcfVue.scoringIndexList.wxpjScore -= xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].indexscore;
				  	}
				  	
				 	//计算最终总得分
		  			var num = 0;
					var childNum =0;
			  		var score1 = xzcfVue.scoringIndexList.crossHandlIndexScoreList;
					if(score1 != null){
				  		for(var i=0;i<score1.length;i++){
				  			if(!isNaN(parseFloat(score1[i].resultScore))){
				  				num = (Math.round((num + parseFloat(score1[i].resultScore))*100))/100;
				  		 	}
				  		}
				  	}
					xzcfVue.scoringIndexList.inputScore = num.toFixed(2); 
			  },
			  wuxupingcha2:function(index,index1){//小指标无需评查
					//无需评查
					//console.log(xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].crossHandlItemScoreList[index1]);
					  var flag=  xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].crossHandlItemScoreList[index1].inCheckValue;//是否勾选
					  
					  	var checkitem ="input[name='"+index+"item"+index1+"']";
					  	if(flag){//勾选
					  		xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].crossHandlItemScoreList[index1].inCheckValue = 1;
					  		var data = xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].crossHandlItemScoreList[index1];
					  		$("#"+index+"crossItem"+index1).removeClass("has-error");	
							$("#"+index+"crossItem"+index1).removeClass("has-success");
							xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].crossHandlItemScoreList[index1].validatorFlag=1;	
							xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].crossHandlItemScoreList[index1].validatorMessage="";	
				  			$(checkitem).attr("disabled","disabled");
					  		
				  			xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].crossHandlItemScoreList[index1].score = '';
				  			xzcfVue.scoringIndexList.wxpjScore += xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].crossHandlItemScoreList[index1].itemscore;
				  			
				  			var indexFinalScore;
				  			var a = xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].crossHandlItemScoreList;
				  			var indexSum;//输入的分数和
				  			var wxpcSum=0;//勾选的无需评查分值和
				  			for(var i=0;i<a.length;i++){//计算大指标下所有小指标输入的分数和，以及勾选的无需评查分值和
						  		if(!isNaN(parseFloat(a[i].score))){
						  			if(isNaN(parseFloat(indexSum))){
						  				indexSum = (Math.round((parseFloat(a[i].score))*100))/100;
						  			}else{
						  				indexSum = (Math.round((indexSum + parseFloat(a[i].score))*100))/100;
						  			}
						  		}
				  				if(a[i].inCheckValue){
				  					wxpcSum += a[i].itemscore;
				  				}
						  	}
				  			var indexScore = xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].indexscore;
				  			if (indexSum!=null){
				  				//计算指标最终得分
					  			indexFinalScore = indexSum*(indexScore/(indexScore-wxpcSum));
					  			xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].resultScore = indexSum;
					  			xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].calculScore = indexFinalScore.toFixed(2);
					  			
					  			var num = 0;
								var childNum =0;
						  		var score1 = xzcfVue.scoringIndexList.crossHandlIndexScoreList;
								if(score1 != null){
							  		for(var i=0;i<score1.length;i++){
							  			if(!isNaN(parseFloat(score1[i].resultScore))){
							  				num = (Math.round((num + parseFloat(score1[i].resultScore))*100))/100; 
							  		 	}
							  		}
							  	}
								xzcfVue.scoringIndexList.inputScore = num.toFixed(2); 
				  			}else{
				  				xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].resultScore = '';
					  			xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].calculScore = '';
				  			}
					  	}else{//取消
					  		xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].crossHandlItemScoreList[index1].inCheckValue = 0;
					  		var data = xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].crossHandlItemScoreList;
					  		if(data!=null &&  data.length>0){
					  			for(var i=0;i<data.length;i++){
									xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].crossHandlItemScoreList[index1].validatorFlag= false;
					  			}
					  		}else{
					  			xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].validatorFlag= false;
					  		}
					  		xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].score='';
					  		xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].resultScore='';
					  		$(checkitem).removeAttr("disabled");
					  		
					  		xzcfVue.scoringIndexList.wxpjScore -= xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].crossHandlItemScoreList[index1].itemscore;
					  		
					  		var indexFinalScore;
				  			var a = xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].crossHandlItemScoreList;
				  			var indexSum;//输入的分数和
				  			var wxpcSum=0;//勾选的无需评查分值和
				  			for(var i=0;i<a.length;i++){
						  		if(!isNaN(parseFloat(a[i].score))){
					  				if(isNaN(parseFloat(indexSum))){
					  					indexSum = (Math.round((parseFloat(a[i].score))*100))/100;
					  				}else{
					  					indexSum = (Math.round((indexSum + parseFloat(a[i].score))*100))/100;
					  				}
						  		}
						  		if(a[i].inCheckValue){
				  					wxpcSum += a[i].itemscore;
				  				}
						  	}
				  			var indexScore = xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].indexscore;
				  			
				  			if (indexSum!=null){//別的小指标打分了
				  				//计算大指标最终得分
				  				//计算该大指标下无需评查的小指标总分
				  				
			  					indexFinalScore = indexSum*(indexScore/(indexScore-wxpcSum))
					  			
					  			xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].resultScore = indexSum;
				  				if(wxpcSum==0){
				  					xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].calculScore = '';
				  				}else{
				  					xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].calculScore = indexFinalScore;
				  				}
					  			
					  			
					  			//计算最终总得分
					  			var num = 0;
								var childNum =0;
						  		var score1 = xzcfVue.scoringIndexList.crossHandlIndexScoreList;
								if(score1 != null){
							  		for(var i=0;i<score1.length;i++){
							  			if(!isNaN(parseFloat(score1[i].resultScore))){
							  				num = (Math.round((num + parseFloat(score1[i].resultScore))*100))/100;
							  		 	}
							  		}
							  	}
								xzcfVue.scoringIndexList.inputScore = num.toFixed(2); 
				  				
				  			}else{//别的小指标没打分
				  				
				  			}
					  	}
				  },
			  showPdfClick:function(){
				//pdf cha kan
				//alert(xzcfVue.scoringIndexList.fileurl)
				  var options = {
							remote:WEBPATH+'/jcpf/showFileModal.do?fastDFSUrl="'+xzcfVue.scoringIndexList.fileurl+'"'
						  };
						$('#view').modal(options);
			  },
			  
			  getView:function(){
				  //判断url是不是pdf
				  if(xzcfVue.scoringIndexList.fileurl != null ){
					  var url = (xzcfVue.scoringIndexList.fileurl).split(".");
					  if(url[url.length-1]=='pdf' || url[url.length-1]=='PDF'){
						  $("#viewBtn").show();
					  }else{
						  $("#viewBtn").hide();
					  }
				  }
			  },
			  updateItem:function (index,itemIndex,itemScore,id){
			    	var num = 0;
				  	var childNum =0;
				  	var value = xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].crossHandlItemScoreList[itemIndex].score;
				  	 if(value != '' && value != null){
			  		 	if(parseFloat(value) <= parseFloat(itemScore) && parseFloat(value)>=0){
			  		 		if(value.substring(value.length-1,value.length) =="."){
			  		 			$("#"+index+"crossItem"+itemIndex).removeClass("has-success");	
								$("#"+index+"crossItem"+itemIndex).addClass("has-error");	  
								xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].crossHandlItemScoreList[itemIndex].validatorFlag= false;
								xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].crossHandlItemScoreList[itemIndex].validatorMessage="分值项不能为空，且在0-"+itemScore+"分之间，最多两位小数！";
						   		return false;
			  		 		}
				  		 	var re = /^-?\d+\.?\d{0,2}$/;
				    		if( re.test(value) ){   // 返回true
				    			$("#"+index+"crossItem"+itemIndex).removeClass("has-error");	
								$("#"+index+"crossItem"+itemIndex).addClass("has-success");	 
								xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].crossHandlItemScoreList[itemIndex].validatorFlag= true;
								xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].crossHandlItemScoreList[itemIndex].validatorMessage="";
				    		}else{
				    			$("#"+index+"crossItem"+itemIndex).removeClass("has-success");	
								$("#"+index+"crossItem"+itemIndex).addClass("has-error");	  
								xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].crossHandlItemScoreList[itemIndex].validatorFlag= false;
								xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].crossHandlItemScoreList[itemIndex].validatorMessage="分值项不能为空，且在0-"+itemScore+"分之间，最多两位小数！";
						   		return false;
					    	}
				    		
				    		var indexSum;//输入的分数和
				  			var wxpcSum=0;//勾选的无需评查分值和
				  			var a = xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].crossHandlItemScoreList;
				  			for(var i=0;i<a.length;i++){//计算大指标下所有小指标输入的分数和，以及勾选的无需评查分值和
						  		if(!isNaN(parseFloat(a[i].score))){
						  			if(isNaN(parseFloat(indexSum))){
						  				indexSum = (Math.round((parseFloat(a[i].score))*100))/100;
						  			}else{
						  				indexSum = (Math.round((indexSum + parseFloat(a[i].score))*100))/100;
						  			}
						  		}
				  				if(a[i].inCheckValue){
				  					wxpcSum += a[i].itemscore;
				  				}
						  	}
				  			
							var indexScore = xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].indexscore;
				  			
				  			if (indexSum!=null){
				  				//计算指标最终得分
				  				var isAdd = xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].isAdd ==1;
				  				if(wxpcSum==0){
						  			if(isAdd){
										//加分项
						  				xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].resultScore = indexSum;
							  			xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].calculScore = '';
								  	}else{
										//减分项
									   	xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].resultScore = (parseFloat(indexScore) - parseFloat(indexSum))< 0 ? 0 : parseFloat(indexScore) - parseFloat(indexSum);
							  			xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].calculScore = '';
								  	}
				  				}else{
				  					if(isAdd){
										//加分项
					  					indexFinalScore = indexSum*(indexScore/(indexScore-wxpcSum));
							  			xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].resultScore = indexSum;
							  			xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].calculScore = indexFinalScore.toFixed(2);
								  	}else{
										//减分项
								  		indexSum = indexScore - wxpcSum - indexSum;
					  					indexFinalScore = indexSum*(indexScore/(indexScore-wxpcSum));
							  			xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].resultScore = indexSum;
							  			xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].calculScore = indexFinalScore.toFixed(2);
								  	}
				  				}
				  				
					  			
					  			
					  			var num = 0;
								var childNum =0;
						  		var score1 = xzcfVue.scoringIndexList.crossHandlIndexScoreList;
								if(score1 != null){
							  		for(var i=0;i<score1.length;i++){
							  			if(!isNaN(parseFloat(score1[i].resultScore))){
							  				num = (Math.round((num + parseFloat(score1[i].resultScore))*100))/100; 
							  		 	}
							  		}
							  	}
								xzcfVue.scoringIndexList.inputScore = num.toFixed(2); 
				  			}
				  		}else{
				  			$("#"+index+"crossItem"+itemIndex).removeClass("has-success");	
							$("#"+index+"crossItem"+itemIndex).addClass("has-error");	  
							xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].crossHandlItemScoreList[itemIndex].validatorFlag= false;
							xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].crossHandlItemScoreList[itemIndex].validatorMessage="分值项不能为空，且在0-"+itemScore+"分之间，最多两位小数！";
					   		return false;
				  		}
				  	}else{
				  		$("#"+index+"crossItem"+itemIndex).removeClass("has-success");	
						$("#"+index+"crossItem"+itemIndex).addClass("has-error");	  
						xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].crossHandlItemScoreList[itemIndex].validatorFlag= false;
						xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].crossHandlItemScoreList[itemIndex].validatorMessage="分值项不能为空，且在0-"+itemScore+"分之间，最多两位小数！";
				   		return false;
				  	}
					  		 	
			  },
			  updateCommon:function (index){
				    //var flag=  xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].voteDownValue;
				    var text = $("#comment"+index).val();
				    var length = text.length;
				    //console.log(length);
				    if(length>1000){
			  			$("#comment"+index).removeClass("textarea-success");	
				  		$("#comment"+index).addClass("textarea-error");	  
						xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].validatorFlag= 3;
						xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].validatorMessage="长度不能大于1000";
		  			}else{
		  				$("#comment"+index).removeClass("textarea-error");	
				  		$("#comment"+index).addClass("textarea-success");	  
						xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].validatorFlag= true;
						xzcfVue.scoringIndexList.crossHandlIndexScoreList[index].validatorMessage="";
		  			}
				   
			  },
			  //保存
			  saveSubmit:function(id){
				  $("#submitBtn").attr("disabled",true);
				  $("#submitBtn").text("保存中...");
					if(xzcfVue.scoringIndexList!=null){
						var data = xzcfVue.scoringIndexList.crossHandlIndexScoreList;
						for(var i=0;i<data.length;i++){
							if((data[i].voteDownValue !=0 || data[i].voteDownValue != 1) && data[i].voteDownValue){//把true和false置为1,0
								data[i].voteDownValue =1;
							}else{
								data[i].voteDownValue=0;
							}
							if((data[i].inCheckValue!= 0 || data[i].inCheckValue!=1) && data[i].inCheckValue){//把true和false置为1,0
								data[i].inCheckValue =1;
							}else{
								data[i].inCheckValue=0;
							}
							
							if(data[i].validatorFlag== 3){//验证长度
								$("#submitBtn").attr("disabled",false);
								$("#submitBtn").text("信息保存");
							  	return false;
							}
							
							var jcpjYxdxanlituijianreviews = xzcfVue.scoringIndexList.jcpjYxdxanlituijianreviews;
							
							if(xzcfVue.scoringIndexList.jcpjYxdxanlituijian=="1"){
								if(jcpjYxdxanlituijianreviews==null || jcpjYxdxanlituijianreviews == ''){
									swal({title: "提示",text: "请输入案卷评价",type:"info"});
									$("#submitBtn").attr("disabled",false);
									$("#submitBtn").text("信息保存");
									return false;
								}else{
									if(jcpjYxdxanlituijianreviews.length>1000){
										swal({title: "提示",text: "案卷评价长度不能超过1000",type:"info"});
										$("#submitBtn").attr("disabled",false);
										$("#submitBtn").text("信息保存");
										return false;
									}
								}
							}
							
							if(data[i].inCheckValue==1 || data[i].voteDownValue == 1){//如果勾选了则不需要验证
								continue;
							}
							
							if(data[i].crossHandlItemScoreList ==null&&(data[i].validatorFlag == null || !data[i].validatorFlag) ){
								xzcfVue.scoringIndexList.crossHandlIndexScoreList[i].validatorFlag= false;
								xzcfVue.scoringIndexList.crossHandlIndexScoreList[i].validatorMessage="分值项不能为空，且在0-"+xzcfVue.scoringIndexList.crossHandlIndexScoreList[i].indexscore+"分之间，最多两位小数！";
								$("#crossIndex"+i).removeClass("has-success");	
								$("#crossIndex"+i).addClass("has-error");
								$("#submitBtn").attr("disabled",false);
								$("#submitBtn").text("信息保存");
								return false;
							}else{
								if(data[i].crossHandlItemScoreList != null){
									for(var j=0;j<data[i].crossHandlItemScoreList.length;j++){
										if(data[i].crossHandlItemScoreList[j].validatorFlag == null || !data[i].crossHandlItemScoreList[j].validatorFlag){
											xzcfVue.scoringIndexList.crossHandlIndexScoreList[i].crossHandlItemScoreList[j].validatorFlag= false;
											xzcfVue.scoringIndexList.crossHandlIndexScoreList[i].crossHandlItemScoreList[j].validatorMessage="分值项不能为空，且在0-"+xzcfVue.scoringIndexList.crossHandlIndexScoreList[i].crossHandlItemScoreList[j].itemscore+"分之间，最多两位小数！";
											$("#crossItem"+xzcfVue.scoringIndexList.crossHandlIndexScoreList[i].crossHandlItemScoreList[j].temItemId+""+i).removeClass("has-success");	
											$("#crossItem"+xzcfVue.scoringIndexList.crossHandlIndexScoreList[i].crossHandlItemScoreList[j].temItemId+""+i).addClass("has-error");
											$("#submitBtn").attr("disabled",false);
											$("#submitBtn").text("信息保存");
											return false;
										}
									}
								}
							}
						}
						//loding('submitBtn', '信息保存');
						
						//无需评审公式换算：最终得分 = 打分合计 *（满分/（满分-无需评审总分））
						if(xzcfVue.scoringIndexList.wxpjScore!=0){
							var expertfinalscore = xzcfVue.scoringIndexList.inputScore*(100/(100-xzcfVue.scoringIndexList.wxpjScore));
							xzcfVue.scoringIndexList.expertfinalscore = expertfinalscore.toFixed(2);
						}else{
							xzcfVue.scoringIndexList.expertfinalscore = xzcfVue.scoringIndexList.inputScore;
						}
						
						$.ajax({
						    type:"post",
						    url:WEBPATH+'/jcpf/saveCrossScore.do',
						    data:{id:id,
						    	scoringIndex:JSON.stringify(xzcfVue.scoringIndexList)},           //注意数据用{}
						    success:function(data){  //成功
						    	if(data.result=="success"){
						    	  swal({title: "保存成功",text: "",type:"success"});
						          business.addMainContentParserHtml('jcpf/jcpfList.do','pageNum=${pageNum}');
						          return false;
						    	}else{
						    		swal("提示", "信息保存操作失败了!", "error");
						    		$("#submitBtn").attr("disabled",false);
									$("#submitBtn").text("信息保存");
									$("#submitBtn").attr("disabled",false);
									$("#submitBtn").text("信息保存");  
							    	return false; 
						    	}
						      }
						});
					}
			  }
		 }
	});
	
	 var ajpja = xzcfVue.scoringIndexList.jcpjYxdxanlituijian;
		if(xzcfVue.scoringIndexList.jcpjYxdxanlituijian=="1"){
			//$('#jcpjYxdxanlituijian').attr('checked', true)
			
		     $("[name = jcpjYxdxanlituijian]:checkbox").attr("checked", true);
			$("#jcpjYxdxAnLiTuiJianReviews1").css("display","block"); 
		}
	
	/* 页面初始化加载设置隐藏 input输入框，当用户选择一票否决和无须评查  */
	if(xzcfVue.scoringIndexList != null && xzcfVue.scoringIndexList.crossHandlIndexScoreList != null ){
			for( var i=0;i< xzcfVue.scoringIndexList.crossHandlIndexScoreList.length;i++){
				if(xzcfVue.scoringIndexList.crossHandlIndexScoreList[i].inCheckValue ==1 ||xzcfVue.scoringIndexList.crossHandlIndexScoreList[i].voteDownValue ==1){
					var checkitem =".checkitem"+i;
					$(checkitem).attr("disabled","disabled");
					/* if(xzcfVue.scoringIndexList.crossHandlIndexScoreList[i].inCheckValue ==1){
						//无须评查
						xzcfVue.scoringIndexList.crossHandlIndexScoreList[i].resultScore=xzcfVue.scoringIndexList.crossHandlIndexScoreList[i].indexscore;
					} */
					if(xzcfVue.scoringIndexList.crossHandlIndexScoreList[i].voteDownValue ==1){
						//无须评查
						xzcfVue.scoringIndexList.crossHandlIndexScoreList[i].resultScore=0;
					}
				}
				var items = xzcfVue.scoringIndexList.crossHandlIndexScoreList[i].crossHandlItemScoreList;
				for (var j = 0; j < items.length; j++) {
					//console.log(items[j].inCheckValue);
					if(items[j].inCheckValue){
						var checkitem ="input[name='"+i+"item"+j+"']";
				  		$(checkitem).attr("disabled","disabled");	
					}
				}
			}
			
	}
	xzcfVue.getView();
	var statusTemp = eval('${status}');
	 if(statusTemp =='1'){
		 $("#xiaZaiFuJian").removeAttr("disabled");
		 $("#xiaZai").removeAttr("disabled");
	 }
	 var scoredStateTemp= eval('${crossHandlFileList.scoredstate}');
	 
	 if(scoredStateTemp == '1'){
		 if(statusTemp !='1'){
			 $("#xiaZaiFuJian").removeAttr("disabled");
			 $("#xiaZai").removeAttr("disabled");
	 	}
	}
	 
	 function keepTwoDecimal(num) {
		 var result = parseFloat(num);
		 if (isNaN(result)) {
		 alert('传递参数错误，请检查！');
		 return false;
		 }
		 result = Math.round(num * 100) / 100;
		 return result;
		}
		//四舍五入保留2位小数（不够位数，则用0替补）
		function keepTwoDecimalFull(num) {
		 var result = parseFloat(num);
		 if (isNaN(result)) {
		 alert('传递参数错误，请检查！');
		 return false;
		 }
		 result = Math.round(num * 100) / 100;
		 var s_x = result.toString();
		 var pos_decimal = s_x.indexOf('.');
		 if (pos_decimal < 0) {
		 pos_decimal = s_x.length;
		 s_x += '.';
		 }
		 while (s_x.length <= pos_decimal + 2) {
		 s_x += '0';
		 }
		 return s_x;
		}

</script>
	<!-- 附件预览  -->
	<div class="modal fade" id="view" tabindex="-1" role="dialog"
		aria-labelledby="myModalLabel" aria-hidden="true">
		<div class="modal-dialog modal-lg">
			<div class="modal-content">
			</div>
		</div>
	</div>
</body>
</html>
