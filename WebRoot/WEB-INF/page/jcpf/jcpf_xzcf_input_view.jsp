<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
	<meta http-equiv="pragma" content="no-cache">
	<meta http-equiv="cache-control" content="no-cache">
	<meta http-equiv="expires" content="0"> 
<head>
<style>
.pdfobject-container {
	width: 100%;
	height:580px;
	margin: 2em 0;
}
</style>
<script type="text/javascript">
			function trim(str) {
				  return str.replace(/(^\s+)|(\s+$)/g, "");
				}
			//下载文件
			function xiaZaiAnJuan(){
			 var fileid  = $("#fileId").val();
			 $.ajax({
				    type:"post",
				    url:WEBPATH+'/jcpf/jcpfExistFileUrl.do',
				    data:{fileId:fileid },           //注意数据用{}
				    success:function(data){  //成功
					 if("yes" == data){
							window.location.href="${pageContext.request.contextPath}/jcpf/jcpfAnJuandown.do?fileid="+fileid;
						    return false;
				         }else if("no" == data){
				            	  swal( "操作失败","该案卷不存在!", "error");
				            	  return false;
						}else if("suffixerror" ==data){
							  swal( "操作失败","该案卷上传数据格式有问题!", "error");
			            	  return false;
						}
				         }
				});
			 }
</script>
</head>
<script type="text/javascript">
</script>
<div id='crossXzcfVue'>
<div   class="center_weizhi">当前位置：交叉评分 - 交叉评分结果 - 交叉案卷评分 - <span id="fileTypeSpan" style="font-size: 16px;"></span></div>
<div class="center">
<div class="center_list">
	<input id="crossFileId"  type="hidden" value="${crossFileId }" >
	<div class="dingwei">案卷或材料：（<span style="color:#06C;font-size:16px;" id ="fileCode">{{scoringIndexList.filecode }} </span>） 
	<c:if test ="${sessionScope.sa_session.sysStatus == '2' }">	
	 <button class="btn btn-success btn-xs" data-toggle="modal" data-target="#myModal" onclick="xiaZaiAnJuan()">下载</button>
	 <span id="viewBtn" v-html='datas' >{{datas}}</span>
	</c:if>
	</div>
 	   <table class="table table-striped table-bordered table-condensed">
      <thead>
	      <tr>
	      	  <td width="50" height="30" bgcolor="#efefef">序号</td>
             <td width="100" bgcolor="#efefef">分指标</td>
             <td width="90" bgcolor="#efefef">分指标分值</td>
              <td width="200" bgcolor="#efefef">指标说明</td>
             <td bgcolor="#efefef"> <span style="float: left;">判断标准</span><span style="float: right;margin-right: 120px;">分值</span></td>
             <td bgcolor="#efefef" width="70">一票否决</td>
             <td bgcolor="#efefef" width="70">无需评查</td>
             <td bgcolor="#efefef" width="80">分指标得分</td>
	      </tr>
         </thead>
         	<tbody  class="form-group">
         		 <tr v-for="(scoringIndex, index) in scoringIndexList.crossHandlIndexScoreList">
		             <td height="30" align="center" style="vertical-align:middle;">{{index+1}}</td>
		             <td style="vertical-align:middle;">{{scoringIndex.indexname}}</td>
		             <td style="vertical-align:middle;">{{scoringIndex.indexscore}}</td>
		             <td style="vertical-align:middle; width:200px;">{{scoringIndex.indexdesc}}</td>
		             <td v-if="scoringIndex.crossHandlItemScoreList != null && scoringIndex.crossHandlItemScoreList.length !=0 "  style="padding:0;">
                        <table width="100%" border="0" cellspacing="0" cellpadding="0" style="padding:0;">
                          <tr  v-for="(scoringItem, index1) in scoringIndex.crossHandlItemScoreList">   
                          	<td style="border-bottom:solid 1px #ddd; border-right:solid 1px #ddd;vertical-align:middle;">
                                 <div style="vertical-align:middle; margin:0 5px 5px 0;">
		            			{{scoringItem.itemname}}（{{scoringItem.itemscore}}分）
		            			</div>
                            </td>                      
                            <td style="border-bottom:solid 1px #ddd; width:155px;vertical-align:middle;">
                                 <div  :id="'crossItem'+index1+index" style="margin:0 0 5px 0; float:left;">
	                                 <div v-if="scoringIndex.isAdd==1" style="float:left; padding:5px 2px 7px 3px; color:red; font-size:20px;">＋</div>
	                                 <div v-if="scoringIndex.isAdd!=1" style="float:left; padding:0 0 3px 0; margin:0; color:red; font-size:26px;">－</div>
	                                 <div style="float:right; padding-top:4px;">
	                                 	<input  disabled="disabled"  @input="updateItem(index,index1,scoringItem.itemscore,scoringItem.temItemId)"  
	                                       v-model="scoringItem.score" type="text" :class="'checkitem'+index"
	                                      class="form-control inputDisabled"   placeholder="请输入初评得分" style="width:125px;">
	                                 </div>
                                 	<div style="color:red; font-size: 12px;">{{scoringItem.validatorMessage}}</div>
                                 </div>
                            </td>                          
                          </tr>
                        </table>
		             </td>
		            <td v-else >
		             <table width="100%" border="0" cellspacing="0" cellpadding="0">
		             	<tr>
		             		<td style="border-bottom:solid 1px #f7f7f7;">
                                 <div style="vertical-align:middle; margin:0 5px 5px 0;">
		            				{{scoringIndex.indexdesc}}
		            			</div>
                            </td>    
                            <td style="border-bottom:solid 1px #f7f7f7;">
                            	<div :id="'crossIndex'+index"  class="form-group" style="width: 150px;float: right;">
	                            	<div v-if="scoringIndex.isAdd==1" style="float:left; padding:5px 2px 7px 3px; color:red; font-size:20px;">+</div>
		                            <div v-if="scoringIndex.isAdd!=1" style="float:left; padding:0 0 3px 0; margin:0; color:red; font-size:26px;">-</div>
				                		<input   type="text"  disabled="disabled"  v-model="scoringIndex.score" 
				                		 @input="updateIndex(index,scoringIndex.indexscore)" :class="'checkitem'+index"
				               	  		 class="form-control inputDisabled"  placeholder="请输入初评得分">
			         			  	<div style="color:red;font-size: 5px;">{{scoringIndex.validatorMessage}}</div>
			         			 </div>
                            </td>
		             	</tr>
		             </table>
		           </td>
		            <td style="vertical-align:middle; text-align:center;">
		              <span v-if="scoringIndex.voteDownValue ==1" >是
		              </span>
		           </td>
		            <td style="vertical-align:middle;text-align:center;">
		            <span v-if="scoringIndex.inCheckValue ==1">是
		              </span>
		           </td>
		           <td style="vertical-align:middle;text-align:center;">
		           <span v-if="scoringIndex.inCheckValue ==1">0
		              </span>
		              <span v-if="scoringIndex.inCheckValue !=1">
		           	  <input type="text"  disabled="disabled" v-model="scoringIndex.resultScore" style="background-color:transparent; width:80px; border:0px;vertical-align:middle;text-align:center;">
		           </span>
		           </td>
		           </tr>
		           <tr>
		           		<td></td>
		           		<td>打分合计</td>
		           		<td>100</td>
		           		<td></td>
		           		<td>打分合计：输入的分数总和</td>
		           		<td></td>
		           		<td></td>
		           		<td style="vertical-align:middle;text-align:center;">{{scoringIndexList.inputScore}}</td>
		           		<td></td>
		           </tr>
		           <tr>
		           		<td></td>
		           		<td>合计</td>
		           		<td>100</td>
		           		<td></td>
		           		<td></td>
		           		<td></td>
		           		<td></td>
		           		<td style="vertical-align:middle;text-align:center;">{{scoringIndexList.expertfinalscore}}</td>
		           </tr>
          	</tbody>
       </table>
       <form action="" id="searchForm">
       	<input name="areaType" type="hidden" value="${areaType}"/>
		<input name="isConsider" type="hidden" value="${isConsider}"/>
		<input name="isConsiderCross" type="hidden" value="${isConsiderCross}"/>
		<input name="fileCode" type="hidden" value="${fileCodeBck}"/>
		<input name="pageNum" type="hidden" value="${pageNum}"/>
		<input name="fileType" type="hidden" value="${fileType}"/>
		<input name="areaCodeLeave" type="hidden" value="${areaCodeLeave}"/>
		<input id ="fileId" type="hidden" :value="scoringIndexList.fileid">
		
		</form>
       	<a style="float: right;margin-right: 30px;" href="#"><button class="btn btn-primary" v-on:click="backUpPageBtn()" type="button" style="font-size:16px;width:150px; margin-top:5px;">返回</button></a>
 	</div>
</div>
</div>
<div class="bottom">&copy;2016 北京长能环境大数据科技有限公司 研发并提供技术支持</div>
<script >
		var scoringIndexList =null;
		var crossFileId = $("#crossFileId").val();
		if(crossFileId != null){
		$.ajax({
		cache : true,
		type : "GET",
		async : false,
		//api/test/detail/behaviour/74
		url: WEBPATH+"/jcpf/getIndexList.do",
		data:{
			id:crossFileId,
		},
		error : function(request) {
			swal("错误!","请求异常！", "error");
		},
		success : function(data) {

		  	if(data.result =='success'){
		  		console.log(data.data)
		  		scoringIndexList=data.data;
		  	}
		} 
	});
			
		}
		
		var veiwBtnVue = new Vue({
			el:'#viewBtn',
			data:{
				datas:'<button class="btn btn-info btn-xs" id="xiaZaiFuJian"  v-on:click="showPdfClick()">预览</button>',
			},
			
		});	
	var xzcfVue = new Vue({
		  el: '#crossXzcfVue' ,
		  data: {
			  scoringIndexList:scoringIndexList,
		  },
		  methods: {
			  backUpPageBtn:function(){
				  if('${expertOrCrossType}' == 1){
						business.addMainContentParserHtml("anjuanList.do?pageIndex="+'${pageIndex}',$("#searchForm").serialize());
					}else{
						business.addMainContentParserHtml("ajpfCross.do",$("#searchForm").serialize());
					}
			  },
			  showPdfClick:function(){
					//pdf cha kan
					//alert(xzcfVue.scoringIndexList.fileurl)
					  var options = {
								remote:WEBPATH+'/jcpf/showFileModal.do?fastDFSUrl="'+xzcfVue.scoringIndexList.fileurl+'"'
							  };
							$('#view').modal(options);
				  },
			  getFileTypeName:function(){
				  var type = scoringIndexList.filetype;
				  if(type=="0"){//行政处罚案卷 0
						$("#fileTypeSpan").html("行政处罚案卷");
					}else if(type=="1"){
						$("#fileTypeSpan").html("按日计罚案卷");
					}else if(type=="2"){// 移送行政拘留案卷2
						$("#fileTypeSpan").html("移送行政拘留案卷");
					}else if(type=="3"){// 涉嫌犯罪移送案卷3
						$("#fileTypeSpan").html("涉嫌犯罪移送案卷");
					}else if(type=="5"){// 发现问题的污染源现场监督检查稽查案卷5
						$("#fileTypeSpan").html("发现问题的污染源现场监督检查稽查案卷");
					}else if(type=="6"){// 查封扣押案卷6
						$("#fileTypeSpan").html("查封扣押案卷");
					}else if(type=="7"){// 停产限产案卷7
						$("#fileTypeSpan").html("限产停产案卷");
					}else{
						$("#fileTypeSpan").html("错误案卷");    						
					}
			  },
			  getView:function(){
				  //判断url是不是pdf
				  if(xzcfVue.scoringIndexList.fileurl != null ){
					  var url = (xzcfVue.scoringIndexList.fileurl).split(".");
					  if(url[url.length-1]=='pdf' || url[url.length-1]=='PDF'){
						  $("#viewBtn").show();
					  }else{
						  $("#viewBtn").hide();
					  }
				  }
			  },
		  }
	});
	/* 页面初始化加载设置隐藏 input输入框，当用户选择一票否决和无须评查  */
	if(xzcfVue.scoringIndexList != null && xzcfVue.scoringIndexList.crossHandlIndexScoreList != null ){
			for( var i=0;i< xzcfVue.scoringIndexList.crossHandlIndexScoreList.length;i++){
				if(xzcfVue.scoringIndexList.crossHandlIndexScoreList[i].inCheckValue ==1 ||xzcfVue.scoringIndexList.crossHandlIndexScoreList[i].voteDownValue ==1){
					var checkitem =".checkitem"+i;
					$(checkitem).attr("disabled","disabled");
					if(xzcfVue.scoringIndexList.crossHandlIndexScoreList[i].inCheckValue ==1){
						//无须评查
						xzcfVue.scoringIndexList.crossHandlIndexScoreList[i].resultScore=xzcfVue.scoringIndexList.crossHandlIndexScoreList[i].indexscore;
					}
					if(xzcfVue.scoringIndexList.crossHandlIndexScoreList[i].voteDownValue ==1){
						//无须评查
						xzcfVue.scoringIndexList.crossHandlIndexScoreList[i].resultScore=0;
					}
				}
			}
	}
	//调用获取案件类型的方法
	xzcfVue.getFileTypeName();

</script>
	<!-- 附件预览  -->
	<div class="modal fade" id="view" tabindex="-1" role="dialog"
		aria-labelledby="myModalLabel" aria-hidden="true">
		<div class="modal-dialog modal-lg">
			<div class="modal-content">
			</div>
		</div>
	</div>
</body>
</html>
