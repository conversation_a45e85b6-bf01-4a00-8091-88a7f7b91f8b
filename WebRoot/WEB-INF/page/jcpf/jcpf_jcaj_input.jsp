<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
<script type="text/javascript">
/* 	判断评分的状态scoredState为1 */
		 $(document).ready(function(){
			 var scoredState= eval('${crossHandlFileList.scoredstate}');
			 var status = eval('${status}');
			 //查看
			 if(status =='1'){
				 $("#chickAnjuan").hide();
				 $("#submitBaoChong").hide();
				 $("#fileCodeInput").hide();
				 $("#queren").hide();
			 }
			 if(scoredState == '1'){
				 if(status !='1'){
				 	$("input").removeAttr("readonly");
					$("#submitBaoChong").removeAttr("style display");
					$("#chickAnjuan").hide();
					$("#fileCodeInput").attr("disabled","disabled");
					$("#fileCodeInput").val('${ crossHandlFileList.filecode}');
			 }}else{
				 $("#fileCodeInput").val("");
			 }
			});
		/* 	评分状态的scoredState为0 */
			function chickAnjuan(){
				var fileCodeInput = trim($("#fileCodeInput").val());
				fileCodeInput = fileCodeInput.replace(/\s+/g,"");
				var fileCode = trim($("#fileCode").text());
				fileCode = fileCode.replace(/\s+/g,"");
				//var fileCodeInput ="黄环监稽通〔 2016 〕  07 号";
				if( fileCodeInput == fileCode){
				swal("开始打分", "开始打分!", "success");
				$("input").removeAttr("readonly");
				$("#fileCodeInput").attr("disabled","disabled");
				$("#submitBaoChong").removeAttr("style display");
				$("#chickAnjuan").hide();
				}else{
					swal("开始打分", "请输入正确的案卷号！", "error");
				}
			}
			function trim(str) {
				  return str.replace(/(^\s+)|(\s+$)/g, "");
				}
			
			//下载文件
			function xiaZaiAnJuan(){
			 //var fileCode = $("#fileCode").html();
			 var fileid  = $("#fileId").val();
			 $.ajax({
				    type:"post",
				    url:WEBPATH+'/jcpf/jcpfExistFileUrl.do',
				    data:{fileId:fileid },           //注意数据用{}
				    success:function(data){  //成功
					 if("yes" == data){
							window.location.href="${pageContext.request.contextPath}/jcpf/jcpfAnJuandown.do?fileid="+fileid;
						    return false;
				         }else if("no" == data){
				            	  swal( "操作失败","该案卷不存在!", "error");
				            	  return false;
						}else if("suffixerror" ==data){
							  swal( "操作失败","该案卷上传数据格式有问题!", "error");
			            	  return false;
						}
				         }
				});
			 }
			
	 $(document).ready(function(){
			//保存数据方法
			var pageNum = $("#pageNum").val();
			$('#submitBaoChong').click(function() {
		       	 var options = {
		           url: WEBPATH+'/jcpf/savejcpf.do',
		           type: 'post',
		           success:function(data){
			           if(data.result=="error"){
			        	   if(data.data="1"){
				        	   swal("操作失败", "您输入数据格式不准确!", "error");
					             return false; 
				        	   }else{
				        		   swal("操作失败", "信息保存操作失败了!", "error");
						           return false; 
				        	   }
			           }else if(data.result=="success"){
			        	  swal({title: "保存成功",text: "",type:"success"});
			        	business.addMainContentParserHtml('jcpf/jcpfList.do','pageNum='+pageNum);
			        	return false;
			           }
		         	}
		       	 };
		       	$("#jcpfForm").data('formValidation').validate();
		       	var validate = $("#jcpfForm").data('formValidation').isValid();
		       	if(validate){
		       	 	$('#jcpfForm').ajaxSubmit(options);
		       	}
		   	});
			
			$("#jcpfForm").formValidation({
				   framework: 'bootstrap',
			        message: 'This value is not valid',
			        icon:{
				            valid: 'glyphicon glyphicon-ok',
				            invalid: 'glyphicon glyphicon-remove',
				            validating: 'glyphicon glyphicon-refresh'
			        },
			        fields: {
			        	"juanneimuluscore": {
			                validators: {
			                    notEmpty: {
			                        message: '卷内目录得分不能为空!'
			                    },
			                    regexp: {
			                        regexp:  /(?!^0\.0?0$)^[0-4](\.[0-9]{1,2})?$|^(5|5\.0|5\.00|0\.0|0\.00)$/,
			                        message: '请输入0-5之间的整数或者包含2位小数!'
			                    }
			                }
			            },
			         	 "lianshenpibiaoscore": {
			                validators: {
			                    notEmpty: {
			                        message: '立案审批表或相关公文得分不能为空！'
			                    },
			                    regexp: {
			                        regexp: /(?!^0\.0?0$)^[0-7](\.[0-9]{1,2})?$|^(8|8\.0|8\.00|0\.0|0\.00)$/,
			                        message: '请输入0-8之间的整数或者包含2位小数!'
			                    }
			                }
			            },
			         	"jichatongzhishuscore": {
			                validators: {
			                    notEmpty: {
			                        message: '稽查通知书得分不能为空！'
			                    },
			                    regexp: {
			                        regexp:  /(?!^0\.0?0$)^([0-9])(\.[0-9]{1,2})?$|^(10|10\.0|10\.00|0\.0|0\.00)$/,
			                        message: '请输入0-10之间的整数或者包含2位小数!'
			                    }
			                }
			            },
			         	"xunwenbiluscore": {
			                validators: {
			                    notEmpty: {
			                        message: '询问笔录得分不能为空！'
			                    },
			                    regexp: {
			                        regexp: /(?!^0\.0?0$)^([0-9]|[1][0-1])(\.[0-9]{1,2})?$|^(12|12\.0|12\.00|0\.0|0\.00)$/,
			                        message: '请输入0-12之间的整数或者包含2位小数!'
			                    }
			                }
			            },
			         	"jichajiluscore": {
			                validators: {
			                    notEmpty: {
			                        message: '稽查记录得分不能为空！'
			                    },
			                    regexp: {
			                        regexp: /(?!^0\.0?0$)^([0-9]|[1][0-1])(\.[0-9]{1,2})?$|^(12|12\.0|12\.00|0\.0|0\.00)$/,
			                        message: '请输入0-12之间的整数或者包含2位小数!'
			                    }
			                }
			            },
			            "jichadqzlqingdanscore": {
			                validators: {
			                    notEmpty: {
			                        message: '被稽查单位污染源现场监察记录得分不能为空！'
			                    },
			                    regexp: {
			                        regexp: /(?!^0\.0?0$)^([0-9]|[1][0-4])(\.[0-9]{1,2})?$|^(15|15\.0|15\.00|0\.0|0\.00)$/,
			                        message: '请输入0-15之间的整数或者包含2位小数!'
			                    }
			                }
			            },
			            "jichabgshuscore": {
			                validators: {
			                    notEmpty: {
			                        message: '稽查报告书得分不能为空！'
			                    },
			                    regexp: {
			                        regexp: /(?!^0\.0?0$)^([0-9]|[1][0-4])(\.[0-9]{1,2})?$|^(15|15\.0|15\.00|0\.0|0\.00)$/,
			                        message: '请输入0-15之间的整数或者包含2位小数!'
			                    }
			                }
			            },
			            "jichayijianshuscore": {
			                validators: {
			                    notEmpty: {
			                        message: '稽查意见书得分不能为空！'
			                    },
			                    regexp: {
			                        regexp: /(?!^0\.0?0$)^([0-9]|[1][0-4])(\.[0-9]{1,2})?$|^(15|15\.0|15\.00|0\.0|0\.00)$/,
			                        message: '请输入0-15之间的整数或者包含2位小数!'
			                    }
			                }
			            },
			             "zhenggaibgscore": {
			                validators: {
			                    notEmpty: {
			                        message: '整改报告得分不能为空！'
			                    },
			                    regexp: {
			                        regexp: /(?!^0\.0?0$)^([0-7])(\.[0-9]{1,2})?$|^(8|8\.0|8\.00|0\.0|0\.00)$/,
			                        message: '请输入0-8之间的整数或者包含2位小数!'
			                    }
			                }
			            } 
			        }
			});
	 });
	 
	 
	 
	 //动态加载计算总分
	 $(document).ready(function(){
		 $("input[type='text']").change(function() {
			 var inputid = new Array();  
			  var inputArray=$("input[class='form-control']");
			 inputArray.each(//使用数组的循环函数 循环这个input数组  
			 function (){  
		             var input =$(this);//循环中的每一个input元素  
					inputid.push(input.val());      
			  })
			  var num = 0;

			  for(var i=0;i<inputid.length;i++){
				  var a = inputid[i];
				  if(!(a)==""){
					  if(isNaN(parseFloat(inputid[i].value))){
						  inputid[i].value = 0;
						  num = (Math.round((num + parseFloat(inputid[i]))*100))/100; 
					  }
				  }else{
				  }
		 		}
			  $("#expertfinalscore1").html("&nbsp;&nbsp;&nbsp;&nbsp;"+num);
			  $("#expertfinalscore").val(num);	
		 });
	 });
</script>
</head>
<div class="center_weizhi">当前位置：交叉评分 - 交叉评分结果 - 交叉案卷评分 - 发现问题的污染源现场监督检查稽查案卷</div>
<div class="center">
<div class="center_list">
	<div class="dingwei">案卷或材料：（<span style="color:#06C;font-size:16px;" id ="fileCode">${crossHandlFileList.filecode } </span>） 
	<c:if test ="${sessionScope.sa_session.sysStatus == '2' }">	
	 <button class="btn btn-success btn-xs" data-toggle="modal" data-target="#myModal" onclick="xiaZaiAnJuan()">下载</button>
	</c:if>
	</div>
    <div class="dingwei" id ="queren">稽查意见书文号确认：<input type="text" style="width:200px; float:right;" id ="fileCodeInput" placeholder="请输入稽查意见书文号"></div>
    <div class="dingwei"><button id ="chickAnjuan" class="btn btn-primary" data-toggle="modal"  onclick="chickAnjuan()" data-target="#myModal">开始评分</button></div>
    <form action="#" id ="jcpfForm" method="post">
    <table class="table table-bordered table-hover table-condensed">
      <thead>
           <tr>
             <td width="50" height="30" bgcolor="#efefef">序号</td>
             <td bgcolor="#efefef">分指标</td>
             <td width="150" bgcolor="#efefef">分指标分值</td>
             <td width="150" bgcolor="#efefef">初评得分</td>
           </tr>
         </thead>
         <tbody>
           <tr>
             <td height="30" align="center">1</td>
             <td>卷内目录</td>
             <td>5</td>
             <td>
              <div class="form-group">
                 <div class="col-sm-12">
             <input type="text" readonly="readonly" value ="${crossHandlFileList.juanneimuluscore }" id ="juanneimuluscore" class="form-control" name="juanneimuluscore" placeholder="请输入初评得分">
          		</div>
          		</div>
             </td>
           </tr>
           <tr>
             <td height="30" align="center">2</td>
             <td>立案审批表或相关公文</td>
             <td>8</td>
             <td>
              <div class="form-group">
                 <div class="col-sm-12">
             <input type="text" id ="lianshenpibiaoscore" value ="${crossHandlFileList.lianshenpibiaoscore }" readonly="readonly"  class="form-control" name="lianshenpibiaoscore" placeholder="请输入初评得分">
           		</div>
           		</div>
             </td>
           </tr>
           <tr>
              <tr>
             <td height="30" align="center">3</td>
             <td>稽查通知书</td>
             <td>10</td>
             <td>
              <div class="form-group">
                 <div class="col-sm-12">
             <input type="text" id ="jichatongzhishuscore" value ="${crossHandlFileList.jichatongzhishuscore }" readonly="readonly"  class="form-control" name="jichatongzhishuscore" placeholder="请输入初评得分">
           		</div>
           		</div>
             </td>
           </tr>
           <tr>
             <td height="30" align="center">4</td>
             <td>询问笔录</td>
             <td>12</td>
             <td>
              <div class="form-group">
                 <div class="col-sm-12">
             <input type="text" id ="xunwenbiluscore" value ="${crossHandlFileList.xunwenbiluscore }" readonly="readonly"  class="form-control" name="xunwenbiluscore" placeholder="请输入初评得分">
           		</div>
           		</div>
             </td>
           </tr>
           <tr>
             <td height="30" align="center">5</td>
             <td>稽查记录</td>
             <td>12</td>
             <td>
             	 <div class="form-group">
                 <div class="col-sm-12">
             <input type="text" id ="jichajiluscore" value ="${crossHandlFileList.jichajiluscore }" readonly="readonly"  class="form-control" name="jichajiluscore" placeholder="请输入初评得分">
				</div></div>
             </td>
           </tr>
           <tr>
             <td height="30" align="center">6</td>
             <td>被稽查单位污染源现场监察记录</td>
             <td>15</td>
             <td>
              <div class="form-group">
                 <div class="col-sm-12">
             <input type="text" id ="jichadqzlqingdanscore" value ="${crossHandlFileList.jichadqzlqingdanscore }" readonly="readonly"  class="form-control" name="jichadqzlqingdanscore" placeholder="请输入初评得分">
           		</div></div>
             </td>
           </tr>
           <tr>
             <td height="30" align="center">7</td>
             <td>稽查报告书</td>
             <td>15</td>
             <td>
              <div class="form-group">
                 <div class="col-sm-12">
             <input type="text" id ="jichabgshuscore" value ="${crossHandlFileList.jichabgshuscore }" readonly="readonly"  class="form-control" name="jichabgshuscore" placeholder="请输入初评得分">
           		</div></div>
             </td>
           </tr>
           <tr>
             <td height="30" align="center">8</td>
             <td>稽查意见书</td>
             <td>15</td>
             <td>
              <div class="form-group">
                 <div class="col-sm-12">
             <input type="text" id ="jichayijianshuscore" value ="${crossHandlFileList.jichayijianshuscore }" readonly="readonly"  class="form-control" name="jichayijianshuscore" placeholder="请输入初评得分">
             	</div></div>
             </td>
          	
           </tr>
           <tr>
             <td height="30" align="center">9</td>
             <td>整改报告</td>
             <td>8</td>
             <td>
              <div class="form-group">
                 <div class="col-sm-12">
             <input type="text" id ="zhenggaibgscore" value ="${crossHandlFileList.zhenggaibgscore }" readonly="readonly"  class="form-control" name="zhenggaibgscore" placeholder="请输入初评得分">
             </div></div>
             </td>
           </tr>
           <tr>
             <td height="30" colspan="2" align="center">小计</td>
             <td>100</td>
             <td><span id ="expertfinalscore1"  class = "expertfinalscore">&nbsp;&nbsp;&nbsp;&nbsp;${crossHandlFileList.expertfinalscore}</span></td>
           </tr>
          </tbody>
       </table>
        <div><input id ="pageNum" type="hidden" value="${pageNum }"></div>
       <div><input id ="jxpfId"  name = "id" type="hidden" value="${crossHandlFileList.id}"></div>
       <div><input id ="fileId"  name = "fileid" type="hidden" value="${crossHandlFileList.fileid}"></div>
       <div><input id="expertfinalscore" type="hidden" name ="expertfinalscore" value="${crossHandlFileList.expertfinalscore}" ></div>
       </form>
		<div class="submit"><c:choose> 
			 <c:when test = "${sessionScope.sa_session.sysStatus != 2}"> 
			 </c:when> 
			 <c:otherwise> 
			 	<a href="#"><button type="button" class="btn btn-primary" id="submitBaoChong" name="signup" value="Sign up" style="font-size:16px;width:150px; margin-top:5px; display:none">信息保存</button></a>
			 </c:otherwise> 
			</c:choose></div>
 	 		</div>
</div>
<script >

$(document).ready(function() {
	$(".topnav").accordion({
		accordion:false,
		speed: 500,
		closedSign: '[+]',
		openedSign: '[-]'
	});
});

</script>
</body>
</html>
