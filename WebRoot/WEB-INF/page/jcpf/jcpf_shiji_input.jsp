<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head></head>
<style>
/*
PDFObject appends the classname "pdfobject-container" to the target element.
This enables you to style the element differently depending on whether the embed was successful.
In this example, a successful embed will result in a large box.
A failed embed will not have dimensions specified, so you don't see an oddly large empty box.
*/
.pdfobject-container {
	width: 100%;
	height:580px;
	margin: 2em 0;
}
</style>
<script type="text/javascript">
/* 	判断评分的状态scoredState为1 */
		 $(document).ready(function(){
			 var scoredState= eval('${crossPersonalFileList.scoredstate}');
			 var status = eval('${status}');
			 /*if(status =='1'){
				 $("#chickAnjuan").hide();
				 $("#submitBaoChong").hide();
				 $("#fileCodeInput").hide();
				 $("#queren").hide();
			 }*/
			 if(scoredState == '1'){
				 if(status !='1'){
				 	$("input").removeAttr("readonly");
					$("#submitBaoChong").removeAttr("style display");
					$("#chickAnjuan").hide();
					$("#fileCodeInput").attr("disabled","disabled");
					$("#personalmaterialname1").val('${ crossPersonalFileList.personalmaterialname}');
			 }}else{
				 $("#fileCodeInput").val("");
			 }
			});
		/* 	评分状态的scoredState为0 */
			function chickAnjuan(){
				var fileCodeInput = trim($("#fileCodeInput").val());
				fileCodeInput = fileCodeInput.replace(/\s+/g,"");
				var fileCode = trim($("#fileCode").text());
				fileCode = fileCode.replace(/\s+/g,"");
				if( fileCodeInput == fileCode){
				swal("开始打分", "开始打分!", "success");
				$("input").removeAttr("readonly");
				$("#fileCodeInput").attr("disabled","disabled");
				$("#submitBaoChong").removeAttr("style display");
				$("#chickAnjuan").hide();
				}else{
					swal("开始打分", "请输入正确的案卷号！", "error");
				}
			}
			function trim(str) {
				  return str.replace(/(^\s+)|(\s+$)/g, "");
				}
			
			//下载文件
			function xiaZaiAnJuan(){
			 //var fileCode = $("#fileCode").html();
			 var personalId  = $("#personalId").val();
			// var personalId = $("#jxpfPersonShiJiId").val();
			 $.ajax({
				    type:"post",
				    url:WEBPATH+'/jcpf/jcpfSjExistFileUrl.do',
				    data:{personalId:personalId },         
				    success:function(data){  //成功
					 if("yes" == data){
							window.location.href="${pageContext.request.contextPath}/jcpf/jcpfGeRenShiJiAnJuan.do?personalId="+personalId;
						    return false;
				         }else if("no" == data){
				            	  swal( "操作失败","该个人事迹附件不存在!", "error");
				            	  return false;
						}else if("suffixerror" ==data){
							  swal( "操作失败","该个人事迹上传数据格式有问题!", "error");
			            	  return false;
						}
				         }
				});
			}
			
	 $(document).ready(function(){
			//保存数据方法
			var pageNum =$("#pageNum").val();
			$('#submitBaoChong').click(function() {
		       	 var options = {
		           url: WEBPATH+'/jcpf/savePersonShiJiSave.do',
		           type: 'post',
		           success:function(data){
			           if(data.result=="error"){
			        	   if(data.data="1"){
				        	   swal("操作失败", "您输入数据格式不准确!", "error");
					             return false; 
				        	   }else{
				        		   swal("操作失败", "信息保存操作失败了!", "error");
						           return false; 
				        	   }
			           }else if(data.result=="success"){
			        	  swal({title: "保存成功",text: "",type:"success"});
			        	business.addMainContentParserHtml('jcpf/perSonShiJiList.do','pageNum='+pageNum);
			        	return false;
			           }
		         	}
		       	 };
		       	$("#jcpfForm").data('formValidation').validate();
		       	var validate = $("#jcpfForm").data('formValidation').isValid();
		       	if(validate){
		       	 	$('#jcpfForm').ajaxSubmit(options);
		       	}
		   	});
			
			$("#jcpfForm").formValidation({
				   framework: 'bootstrap',
			        message: 'This value is not valid',
			        icon:{
				            valid: 'glyphicon glyphicon-ok',
				            invalid: 'glyphicon glyphicon-remove',
				            validating: 'glyphicon glyphicon-refresh'
			        },
			        fields: {
			        	"zonghesuzhiscore": {
			                validators: {
			                    notEmpty: {
			                        message: '综合素质得分不能为空!'
			                    },
			                    regexp: {
			                    	/*/(?!^0\.0?0$)^([0-9]|[1-3][0-9])(\.[0-9]{1,2})?$|^(40|40\.0|40\.00|0\.0|0\.00)$/,*/
			                        regexp:  /(?!^0\.0?0$)^([0-6]|[0-6])(\.[0-9]{1,2})?$|^(6|6\.0|6\.00|0\.0|0\.00)$/,
			                        message: '请输入0-6之间的整数或者小数!'
			                    }
			                }
			            },
			         	 "gongzuoyejiscore": {
			                validators: {
			                    notEmpty: {
			                        message: '工作业绩得分不能为空！'
			                    },
			                    regexp: {
			                    	/*/(?!^0\.0?0$)^([0-9]|[1-5][0-9])(\.[0-9]{1,2})?$|^(60|60\.0|60\.00|0\.0|0\.00)$/,*/
			                        regexp: /(?!^0\.0?0$)^([0-9]|[0-9])(\.[0-9]{1,2})?$|^(9|9\.0|9\.00|0\.0|0\.00)$/,
			                        message: '请输入0-9之间的整数或者小数!'
			                    }
			                }
			            } 
			        }
			});
	 });
	 
	 //动态加载计算总分
	 $(document).ready(function(){
		 $("input[type='text']").change(function() {
			  var zonghesuzhiscore = parseFloat($("input[name='zonghesuzhiscore']").val());  
			  var gongzuoyejiscore = parseFloat($("input[name='gongzuoyejiscore']").val());  
			  if(isNaN(zonghesuzhiscore)){
				  zonghesuzhiscore = 0;
			 }
			  if(isNaN(gongzuoyejiscore)){
				  gongzuoyejiscore = 0;
			 }

			  var r1,r2,m,n;
			     try{r1=zonghesuzhiscore.toString().split(".")[1].length}catch(e){r1=0}
			     try{r2=gongzuoyejiscore.toString().split(".")[1].length}catch(e){r2=0}
			     m=Math.pow(10,Math.max(r1,r2));
			     //last modify by deeka
			     //动态控制精度长度
			     n=(r1>=r2)?r1:r2;
			     var expertfinalscore = ((zonghesuzhiscore*m+gongzuoyejiscore*m)/m).toFixed(n);
			  $("#expertfinalscore1").html("&nbsp;&nbsp;&nbsp;&nbsp;"+expertfinalscore);	
			  $("#expertfinalscore").val(expertfinalscore);	
			  
		 });
		 function accAdd(arg1,arg2){
			 var r1,r2,m;
			 try{r1=arg1.toString().split(".")[1].length}catch(e){r1=0}
			 try{r2=arg2.toString().split(".")[1].length}catch(e){r2=0}
			 m=Math.pow(10,Math.max(r1,r2))
			 return (arg1*m+arg2*m)/m
			 }
	 });
</script>
<body>
<div class="center_weizhi">当前位置：交叉评分 - 交叉评分结果 - 个人事迹评分</div>
<div class="center">
<div class="center_list">
	<div class="dingwei">个人事迹材料：（<span style="color:#06C;font-size:16px; " id ="fileCode">${crossPersonalFileList.personalmaterialname }

		</span>）
		<c:if test ="${sessionScope.sa_session.sysStatus == '2' }">	
	 		<button class="btn btn-success btn-xs" data-toggle="modal" data-target="#myModal" onclick="xiaZaiAnJuan()">下载</button>
			<c:if test="${attaState =='1'}">
				<button class="btn btn-info btn-xs" data-toggle="modal" data-remote="${webpath}/jcpf/showFileModal.do?fastDFSUrl=+'${fastDFS}'+" data-target="#view">预览</button>
			</c:if>
		</c:if>
	
	 </div>
        <div class="dingwei" id ="queren">个人事迹材料名称确认：<input type="text"   style="width:200px; float:right;" class="form-control" id="fileCodeInput"  value = "${crossPersonalFileList.personalmaterialname }" placeholder="请输入个人事迹材料名称"></div>
        
		<div class="dingwei"><button class="btn btn-primary"  data-toggle="modal" data-target="#myModal" id ="chickAnjuan" onclick="chickAnjuan()">开始评分</button></div>
        
        <form action="#" id ="jcpfForm" method="post">
        <table class="table table-bordered table-hover table-condensed">
         <thead>
           <tr>
             <td width="50" height="30" bgcolor="#efefef">序号</td>
             <td bgcolor="#efefef">分指标</td>
             <td width="150" bgcolor="#efefef">分指标分值</td>
             <td width="150" bgcolor="#efefef">初评得分</td>
           </tr>
         </thead>
         <tbody>
           <tr>
             <td height="30" align="center">1</td>
             <td>综合素质</td>
             <td>6</td>
             <td>
             <div class="form-group">
                 <div class="col-sm-12">
             <input type="text" class="form-control" id="zonghesuzhiscore" value ="${crossPersonalFileList.zonghesuzhiscore }"  name ="zonghesuzhiscore"  readonly="readonly"  placeholder="请输入初评得分">
             	</div></div>
             </td>
           </tr>
           <tr>
             <td height="30" align="center">2</td>
             <td>工作业绩</td>
             <td>9</td>
             <td>
             <div class="form-group">
                 <div class="col-sm-12">
             <input type="text" class="form-control" id="gongzuoyejiscore" value ="${crossPersonalFileList.gongzuoyejiscore }" name ="gongzuoyejiscore"  readonly="readonly"  placeholder="请输入初评得分">
             </div></div>
             </td>
           </tr>
           <tr>
             <td height="30" colspan="2" align="center">小计</td>
             <td>15</td>
             <td><span id ="expertfinalscore1"  class = "expertfinalscore"> &nbsp;&nbsp;&nbsp;&nbsp;${crossPersonalFileList.expertfinalscore}</span></td>
     		 </tr>
          </tbody>
       </table>
		<div class="submit"><c:choose> 
			 <c:when test = "${sessionScope.sa_session.sysStatus != 2}"> 
			 </c:when> 
			 <c:otherwise> 
			 </c:otherwise> 
			</c:choose>
			 	<a href="#"><button type="button" class="btn btn-primary" id="submitBaoChong" name="signup" value="Sign up" style="font-size:16px;width:150px; margin-top:5px; display:none">信息保存</button></a>
			</div>
			 <div><input id ="pageNum" type="hidden" value="${pageNum }"></div>
  <div><input id ="jxpfPersonShiJiId"  name = "id" type="hidden" value="${crossPersonalFileList.id}"></div>
  <div><input id ="personalId"  name = "personalId" type="hidden" value="${crossPersonalFileList.personalid}"></div>
  <div><input id="expertfinalscore" type="hidden" name ="expertfinalscore" value="${crossPersonalFileList.expertfinalscore}" ></div>
 </form>
</div>
</div>
<script type="text/javascript">
$(document).ready(function() {
	$(".topnav").accordion({
		accordion:false,
		speed: 500,
		closedSign: '[+]',
		openedSign: '[-]'
	});
});

</script>
	<!-- 附件预览  -->
	<div class="modal fade" id="view" tabindex="-1" role="dialog"
		aria-labelledby="myModalLabel" aria-hidden="true">
		<div class="modal-dialog modal-lg">
			<div class="modal-content">
			</div>
		</div>
	</div>
</body>
</html>
