<!DOCTYPE html>
<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@page import="com.changneng.sa.util.PropertiesUtil"%>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>

<html>
<head>
<style>
/*
PDFObject appends the classname "pdfobject-container" to the target element.
This enables you to style the element differently depending on whether the embed was successful.
In this example, a successful embed will result in a large box.
A failed embed will not have dimensions specified, so you don't see an oddly large empty box.
*/
.pdfobject-container {
	width: 100%;
	height:580px;
	margin: 2em 0;
}
</style>
<script type="text/javascript">
/* 	判断评分的状态scoredState为1 */
		 $(document).ready(function(){
			 var scoredState= eval('${crossHandlFileList.scoredstate}');
			 var status = eval('${status}');
			 //查看
			 if(status =='1'){
				 $("#chickAnjuan").hide();
				 $("#submitBaoChong").hide();
				 $("#fileCodeInput").hide();
				 $("#queren").hide();
			 }
			 if(scoredState == '1'){
				 if(status !='1'){
				 	$("input").removeAttr("readonly");
					$("#submitBaoChong").removeAttr("style display");
					$("#chickAnjuan").hide();
					$("#fileCodeInput").attr("disabled","disabled");
					$("#fileCodeInput").val('${ crossHandlFileList.filecode}');
			 }}else{
				 $("#fileCodeInput").val("");
			 }
			});
		/* 	评分状态的scoredState为0 */
			function chickAnjuan(){
				var fileCodeInput = trim($("#fileCodeInput").val());
				fileCodeInput = fileCodeInput.replace(/\s+/g,"");
				var fileCode = trim($("#fileCode").text());
				fileCode = fileCode.replace(/\s+/g,"");

				if( fileCodeInput == fileCode){
				swal("开始打分", "开始打分!", "success");
				$("input").removeAttr("readonly");
				$("#fileCodeInput").attr("disabled","disabled");
				$("#submitBaoChong").removeAttr("style display");
				$("#chickAnjuan").hide();
				}else{
					swal("开始打分", "请输入正确的案卷号！", "error");
				}
			}
			function trim(str) {
				  return str.replace(/(^\s+)|(\s+$)/g, "");
				}
			
			//下载文件
			function xiaZaiAnJuan(){
			 //var fileCode = $("#fileCode").html();
			 var fileid  = $("#fileId").val();
			 $.ajax({
				    type:"post",
				    url:WEBPATH+'/jcpf/jcpfExistFileUrl.do',
				    data:{fileId:fileid },           //注意数据用{}
				    success:function(data){  //成功
					 if("yes" == data){
							window.location.href="${pageContext.request.contextPath}/jcpf/jcpfAnJuandown.do?fileid="+fileid;
						    return false;
				         }else if("no" == data){
				            	  swal( "操作失败","该案卷不存在!", "error");
				            	  return false;
						}else if("suffixerror" ==data){
							  swal( "操作失败","该案卷上传数据格式有问题!", "error");
			            	  return false;
						}
				         }
				});
			}
			
	 $(document).ready(function(){
		 var pageNum =$("#pageNum").val();
			//保存数据方法
			$('#submitBaoChong').click(function() {
		       	 var options = {
		           url: WEBPATH+'/jcpf/savejcpf.do',
		           type: 'post',
		           success:function(data){
			           if(data.result=="error"){
			        	   if(data.data="1"){
				        	   swal("操作失败", "您输入数据格式不准确!", "error");
					             return false; 
				        	   }else{
				        		   swal("操作失败", "信息保存操作失败了!", "error");
						           return false; 
				        	   }
			           }else if(data.result=="success"){
			        	  swal({title: "保存成功",text: "",type:"success"});
			        	business.addMainContentParserHtml('jcpf/jcpfList.do','pageNum='+pageNum);
			        	return false;
			           }
		         	}
		       	 };
		       	$("#jcpfForm").data('formValidation').validate();
		       	var validate = $("#jcpfForm").data('formValidation').isValid();
		       	if(validate){
		       	 	$('#jcpfForm').ajaxSubmit(options);
		       	}
		   	});
			
			$("#jcpfForm").formValidation({
				   framework: 'bootstrap',
			        message: 'This value is not valid',
			        icon:{
				            valid: 'glyphicon glyphicon-ok',
				            invalid: 'glyphicon glyphicon-remove',
				            validating: 'glyphicon glyphicon-refresh'	
			        },
			        fields: {
			        	"juanneimuluscore": {
			                validators: {
			                    notEmpty: {
			                        message: '卷内目录得分不能为空!'
			                    },
			                    regexp: {
			                        regexp: /(?!^0\.0?0$)^([0-1])(\.[0-9]{1,2})?$|^(2|2\.0|2\.00|0\.0|0\.00)$/,
			                        message: '请输入0-2之间的整数或者包含2位小数!'
			                    }
			                }
			            },
			         	 "lianshenpibiaoscore": {
			                validators: {
			                    notEmpty: {
			                        message: '立案审批表或复核计划得分不能为空！'
			                    },
			                    regexp: {
			                        regexp: /(?!^0\.0?0$)^[0-7](\.[0-9]{1,2})?$|^(8|8\.0|8\.00|0\.0|0\.00)$/,
			                        message: '请输入0-8之间的整数或者包含2位小数!'
			                    }
			                }
			            },
			         	"xianchangjianchascore": {
			                validators: {
			                    notEmpty: {
			                        message: '现场检查（勘察）笔录得分不能为空！'
			                    },
			                    regexp: {
			                        regexp:  /(?!^0\.0?0$)^([0-8])(\.[0-9]{1,2})?$|^(9|9\.0|9\.00|0\.0|0\.00)$/,
			                        message: '请输入0-9之间的整数或者包含2位小数!'
			                    }
			                }
			            },
			         	"xunwenbiluscore": {
			                validators: {
			                    notEmpty: {
			                        message: '调查询问笔录得分不能为空！'
			                    },
			                    regexp: {
			                        regexp: /(?!^0\.0?0$)^[0-7](\.[0-9]{1,2})?$|^(8|8\.0|8\.00|0\.0|0\.00)$/,
			                        message: '请输入0-8之间的整数或者包含2位小数!'
			                    }
			                }
			            },
			         	"diaoquzhenjuscore": {
			                validators: {
			                    notEmpty: {
			                        message: '调取证据得分不能为空！'
			                    },
			                    regexp: {
			                        regexp: /(?!^0\.0?0$)^([0-9]|[1][0-2])(\.[0-9]{1,2})?$|^(13|13\.0|13\.00|0\.0|0\.00)$/,
			                        message: '请输入0-13之间的整数或者包含2位小数!'
			                    }
			                }
			            },
			            "anjianneibuspcx": {
			                validators: {
			                    notEmpty: {
			                        message: '案件内部审批程序得分不能为空！'
			                    },
			                    regexp: {
			                        regexp: /(?!^0\.0?0$)^([0-4])(\.[0-9]{1,2})?$|^(5|5\.0|5\.00|0\.0|0\.00)$/,
			                        message: '请输入0-5之间的整数或者包含2位小数!'
			                    }
			                }
			            },
			            "diaochabaogaoscore": {
			                validators: {
			                    notEmpty: {
			                        message: '案件调查报告得分不能为空！'
			                    },
			                    regexp: {
			                        regexp: /(?!^0\.0?0$)^([0-5])(\.[0-9]{1,2})?$|^(6|6\.0|6\.00|0\.0|0\.00)$/,
			                        message: '请输入0-6之间的整数或者包含2位小数!'
			                    }
			                }
			            },
			            "zlgzwfxwjuedingshuscore": {
			                validators: {
			                    notEmpty: {
			                        message: '责令改正违法行为决定书得分不能为空！'
			                    },
			                    regexp: {
			                        regexp: /(?!^0\.0?0$)^([0-5])(\.[0-9]{1,2})?$|^(6|6\.0|6\.00|0\.0|0\.00)$/,
			                        message: '请输入0-6之间的整数或者包含2位小数!'
			                    }
			                }
			            },
			            "gaozhishuscore": {
			                validators: {
			                    notEmpty: {
			                        message: '行政处罚事先（听证）告知书得分不能为空！'
			                    },
			                    regexp: {
			                        regexp: /(?!^0\.0?0$)^([0-5])(\.[0-9]{1,2})?$|^(6|6\.0|6\.00|0\.0|0\.00)$/,
			                        message: '请输入0-6之间的整数或者包含2位小数!'
			                    }
			                }
			            },
			            "tinzhenbiluscore": {
			                validators: {
			                    notEmpty: {
			                        message: '听证笔录得分不能为空！'
			                    },
			                    regexp: {
			                        regexp: /(?!^0\.0?0$)^([0-4])(\.[0-9]{1,2})?$|^(5|5\.0|5\.00|0\.0|0\.00)$/,
			                        message: '请输入0-5之间的整数或者包含2位小数!'
			                    }
			                }
			            },
			             "tongzhishuscore": {
			                validators: {
			                    notEmpty: {
			                        message: '行政处罚听证通知书得分不能为空！'
			                    },
			                    regexp: {
			                        regexp: /(?!^0\.0?0$)^([0-7])(\.[0-9]{1,2})?$|^(8|8\.0|8\.00|0\.0|0\.00)$/,
			                        message: '请输入0-8之间的整数或者包含2位小数!'
			                    }
			                }
			            }, 
			            "juedingshuscore": {
			                validators: {
			                    notEmpty: {
			                        message: '原罚款处罚决定书得分不能为空！'
			                    },
			                    regexp: {
			                        regexp: /(?!^0\.0?0$)^([0-7])(\.[0-9]{1,2})?$|^(8|8\.0|8\.00|0\.0|0\.00)$/,
			                        message: '请输入0-8之间的整数或者包含2位小数!'
			                    }
			                }
			            },
			            "fchlianxujuedingscore": {
			                validators: {
			                    notEmpty: {
			                        message: '（复查后）按日连续处罚决定书得分不能为空！'
			                    },
			                    regexp: {
			                        regexp: /(?!^0\.0?0$)^([0-9])(\.[0-9]{1,2})?$|^(10|10\.0|10\.00|0\.0|0\.00)$/,
			                        message: '请输入0-10之间的整数或者包含2位小数!'
			                    }
			                }
			            },
			            "zczlgzweifaxingweiscore": {
			                validators: {
			                    notEmpty: {
			                        message: '（复查后）责令改正违法行为决定书得分不能为空！'
			                    },
			                    regexp: {
			                        regexp: /(?!^0\.0?0$)^([0-5])(\.[0-9]{1,2})?$|^(6|6\.0|6\.00|0\.0|0\.00)$/,
			                        message: '请输入0-6之间的整数或者包含2位小数!'
			                    }
			                }
			            } 
			        }
			});
	 });
	 
	 //动态加载计算总分
	 $(document).ready(function(){
		 $("input[type='text']").change(function() {
			 var inputid = new Array();  
			  var inputArray=$("input[class='form-control']");
			 inputArray.each(//使用数组的循环函数 循环这个input数组  
			 function (){  
		             var input =$(this);//循环中的每一个input元素  
					inputid.push(input.val());      
			  })
			  var num = 0;

			  for(var i=0;i<inputid.length;i++){
				  var a = inputid[i];
				  if(!(a)==""){
					  if(isNaN(parseFloat(inputid[i].value))){
						  inputid[i].value = 0;
						  inputid[i].toString().split(".")
						  //精度问题
				      	  num = (Math.round((num + parseFloat(inputid[i]))*100))/100; 
					  }
				  }else{
				  }
		 		}
			  $("#expertfinalscore1").html("&nbsp;&nbsp;&nbsp;&nbsp;"+num);
			  $("#expertfinalscore").val(num);	
		 });
	 });
</script>
</head>
<div class="center_weizhi">当前位置：交叉评分 - 交叉评分结果 - 交叉案卷评分 - 按日计罚案卷</div>
<div class="center">
<div class="center_list">
	<div class="dingwei">案卷或材料：（<span style="color:#06C;font-size:16px;" id ="fileCode">${crossHandlFileList.filecode }
		<c:if test="${attaState =='1'}">.pdf</c:if>
	</span>）
		<c:if test ="${sessionScope.sa_session.sysStatus == '2' }">	
	 	<button class="btn btn-success btn-xs" data-toggle="modal" data-target="#myModal" onclick="xiaZaiAnJuan()">下载</button>
	 	<c:if test="${attaState =='1'}">
			<button class="btn btn-info btn-xs" data-toggle="modal" data-remote="${webpath}/jcpf/showFileModal.do?fastDFSUrl=+'${fastDFS}'+" data-target="#view">预览</button>
	 	</c:if>
		</c:if>
	</div>	 
	
</div>
    <div class="dingwei" id ="queren">案卷处罚决定书文号确认：<input type="text" style="width:200px; float:right;" id ="fileCodeInput" placeholder="请输入案卷处罚决定书文号"></div>
    <div class="dingwei"><button id ="chickAnjuan" class="btn btn-primary" data-toggle="modal"  onclick="chickAnjuan()" data-target="#myModal">开始评分</button></div>
    <form action="#" id ="jcpfForm" method="post">
    <table class="table table-bordered table-hover table-condensed">
      <thead>
           <tr>
             <td width="50" height="30" bgcolor="#efefef">序号</td>
             <td bgcolor="#efefef">分指标</td>
             <td width="150" bgcolor="#efefef">分指标分值</td>
             <td width="150" bgcolor="#efefef">初评得分</td>
           </tr>
         </thead>
         <tbody>
           <tr>
             <td height="30" align="center">1</td>
             <td>卷内目录</td>
             <td>2</td>
             <td>
              <div class="form-group">
                 <div class="col-sm-12">
             <input type="text" readonly="readonly" value ="${crossHandlFileList.juanneimuluscore }" id ="juanneimuluscore" class="form-control" name="juanneimuluscore" placeholder="请输入初评得分">
          		</div>
          		</div>
             </td>
           </tr>
           <tr>
             <td height="30" align="center">2</td>
             <td>立案审批表或复核计划</td>
             <td>8</td>
             <td>
              <div class="form-group">
                 <div class="col-sm-12">
             <input type="text" id ="lianshenpibiaoscore" value ="${crossHandlFileList.lianshenpibiaoscore }" readonly="readonly"  class="form-control" name="lianshenpibiaoscore" placeholder="请输入初评得分">
           		</div>
           		</div>
             </td>
           </tr>
           <tr>
             <td height="30" align="center">3</td>
             <td>现场检查（勘察）笔录</td>
             <td>9</td>
             <td>
              <div class="form-group">
                 <div class="col-sm-12">
             <input type="text" id ="xianchangjianchascore" value ="${crossHandlFileList.xianchangjianchascore }" readonly="readonly"  class="form-control"  name ="xianchangjianchascore" placeholder="请输入初评得分">
           		</div>
           		</div>
             </td>
           </tr>
           <tr>
             <td height="30" align="center">4</td>
             <td>调查询问笔录</td>
             <td>8</td>
             <td>
              <div class="form-group">
                 <div class="col-sm-12">
             <input type="text" id ="xunwenbiluscore" value ="${crossHandlFileList.xunwenbiluscore }" readonly="readonly"  class="form-control" name="xunwenbiluscore" placeholder="请输入初评得分">
           		</div>
           		</div>
             </td>
           </tr>
           <tr>
             <td height="30" align="center">5</td>
             <td>调取证据</td>
             <td>13</td>
             <td>
             	 <div class="form-group">
                 <div class="col-sm-12">
             <input type="text" id ="diaoquzhenjuscore" value ="${crossHandlFileList.diaoquzhenjuscore }" readonly="readonly"  class="form-control" name="diaoquzhenjuscore" placeholder="请输入初评得分">
				</div></div>
             </td>
           </tr>
           <tr>
             <td height="30" align="center">6</td>
             <td>案件调查报告</td>
             <td>6</td>
             <td>
              <div class="form-group">
                 <div class="col-sm-12">
             <input type="text" id ="diaochabaogaoscore" value ="${crossHandlFileList.diaochabaogaoscore }" readonly="readonly"  class="form-control" name="diaochabaogaoscore" placeholder="请输入初评得分">
           		</div></div>
             </td>
           </tr>
           
           <tr>
            <td height="30" align="center">7</td>
            <td>案件内部审批程序</td>
            <td>5</td>
            <td>
             <div class="form-group">
                <div class="col-sm-12">
            <input type="text" id ="anjianneibuspcx" value ="${crossHandlFileList.anjianneibuspcx }" readonly="readonly"  class="form-control" name="anjianneibuspcx" placeholder="请输入初评得分">
          		</div></div>
            </td>
          </tr>
          
            <tr>
             <td height="30" align="center">8</td>
             <td>行政处罚事先（听证）告知书</td>
             <td>6</td>
             <td>
              <div class="form-group">
                 <div class="col-sm-12">
             <input type="text" id ="gaozhishuscore" value ="${crossHandlFileList.gaozhishuscore }" readonly="readonly"  class="form-control" name="gaozhishuscore" placeholder="请输入初评得分">
             	</div></div>
             </td>
          	
           </tr>
           
              <tr>
             <td height="30" align="center">9</td>
             <td>行政处罚听证通知书</td>
             <td>8</td>
             <td>
              <div class="form-group">
                 <div class="col-sm-12">
             <input type="text" id ="tongzhishuscore" value ="${crossHandlFileList.tongzhishuscore }" readonly="readonly"  class="form-control" name="tongzhishuscore" placeholder="请输入初评得分">
             </div></div>
             </td>
           </tr>
              <tr>
             <td height="30" align="center">10</td>
             <td>听证笔录</td>
             <td>5</td>
             <td>
              <div class="form-group">
                 <div class="col-sm-12">
             <input type="text" id="tinzhenbiluscore" value ="${crossHandlFileList.tinzhenbiluscore }" readonly="readonly"   class="form-control" name="tinzhenbiluscore" placeholder="请输入初评得分">
             </div></div>
             </td>
           </tr>
           
           
           <tr>
             <td height="30" align="center">11</td>
             <td>责令改正违法行为决定书</td>
             <td>6</td>
             <td>
              <div class="form-group">
                 <div class="col-sm-12">
             <input type="text" id ="zlgzwfxwjuedingshuscore" value ="${crossHandlFileList.zlgzwfxwjuedingshuscore }" readonly="readonly"  class="form-control" name="zlgzwfxwjuedingshuscore" placeholder="请输入初评得分">
           		</div></div>
             </td>
           </tr>
           
           <tr>
             <td height="30" align="center">12</td>
             <td>原罚款处罚决定书</td>
             <td>8</td>
             <td>
              <div class="form-group">
                 <div class="col-sm-12">
             <input type="text" id ="juedingshuscore" value ="${crossHandlFileList.juedingshuscore }" readonly="readonly"  class="form-control" name="juedingshuscore" placeholder="请输入初评得分">
             </div></div>
             </td>
           </tr>
         
           <tr>
             <td height="30" align="center">13</td>
             <td>（复查后）责令改正违法行为决定书 </td>
             <td>6</td>
             <td>
              <div class="form-group">
                 <div class="col-sm-12">
             <input type="text" id ="zczlgzweifaxingweiscore" value ="${crossHandlFileList.zczlgzweifaxingweiscore }" readonly="readonly"  class="form-control" name="zczlgzweifaxingweiscore" placeholder="请输入初评得分">
             </div></div>
             </td>
           </tr>
           
             <tr>
             <td height="30" align="center">14</td>
             <td>（复查后）按日连续处罚决定书</td>
             <td>10</td>
             <td>
              <div class="form-group">
                 <div class="col-sm-12">
             <input type="text" id ="fchlianxujuedingscore" value ="${crossHandlFileList.fchlianxujuedingscore }" readonly="readonly"  class="form-control" name="fchlianxujuedingscore" placeholder="请输入初评得分">
             </div></div>
             </td>
           </tr>
           <tr>
             <td height="30" colspan="2" align="center">小计</td>
             <td>100</td>
             <td><span id ="expertfinalscore1"  class = "expertfinalscore">&nbsp;&nbsp;&nbsp;&nbsp;${crossHandlFileList.expertfinalscore}</span></td>
           </tr>
          </tbody>
       </table>
        <div><input id ="pageNum" type="hidden" value="${pageNum }"></div>
       <div><input id ="jxpfId"  name = "id" type="hidden" value="${crossHandlFileList.id}"></div>
       <div><input id ="fileId"  name = "fileid" type="hidden" value="${crossHandlFileList.fileid}"></div>
       <div><input id="expertfinalscore" type="hidden" name ="expertfinalscore" value="${crossHandlFileList.expertfinalscore}" ></div>
       </form>
 	<div class="submit"><c:choose> 
			 <c:when test = "${sessionScope.sa_session.sysStatus != 2}"> 
			 </c:when> 
			 <c:otherwise> 
			 	<a href="#"><button type="button" class="btn btn-primary" id="submitBaoChong" name="signup" value="Sign up" style="font-size:16px;width:150px; margin-top:5px; display:none">信息保存</button></a>
			 </c:otherwise> 
			</c:choose></div>
</div>
</div>

	<!--  附件查看 -->
	<!-- 附件预览  -->
	<div class="modal fade" id="view" tabindex="-1" role="dialog"
		aria-labelledby="myModalLabel" aria-hidden="true">
		<div class="modal-dialog modal-lg">
			<div class="modal-content">
			</div>
		</div>
	</div>
	
<script >

$(document).ready(function() {
	$(".topnav").accordion({
		accordion:false,
		speed: 500,
		closedSign: '[+]',
		openedSign: '[-]'
	});
});



</script>
</body>
</html>
