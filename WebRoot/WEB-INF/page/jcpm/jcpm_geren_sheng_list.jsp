<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
</head>

<body>
<div class="center_weizhi">当前位置：交叉排名 - 交叉先进个人评选 - 省级交叉案卷评分</div>
<div class="center">
<div class="center_list">
    <div class="panel-group" id="accordion">
          <h4 class="panel-title">
            <div style="width:260px;" class="btn-group">
                    <a href ="#"><button type="button" id ="viewExcel" class="btn btn-primary">导出EXCEL</button></a>  
            </div>  
          </h4>
          <br/>
    </div>

        <table class="table table-bordered table-hover table-condensed">
         <thead>
           <tr>
             <td width="50" bgcolor="#efefef">序号</td>
             <td width="200" bgcolor="#efefef">省</td>
       		 <td width="200" bgcolor="#efefef">姓名</td>
             <td width="200" bgcolor="#efefef">身份证号</td>  
             <td width="150" height="30" bgcolor="#efefef">案卷文书号</td>
             <td width="150" bgcolor="#efefef">交叉总得分</td>
             <td width="150" bgcolor="#efefef">交叉排名</td>
           </tr>
         </thead>
         <tbody>
           <c:forEach items="${zlpfGerenBeanList.list}" var="item" varStatus="status">
	           <tr>
	             <td height="30" align="center">${status.index+1}<input id="id${status.index+1}" type="hidden"  value="${item.id}"></td>
	             <td>${item.province}</td>
	   			 <td>${item.name}</td>
	             <td>${item.cardid}</td>
	             <td>${item.filecode}</td>
	             <td>${item.crossscore}</td>
	             <td>
		             <c:choose>
		             	<c:when test="${item.crossranking !='' && item.crossranking !=null }">
		             		第${item.crossranking }名
		             	</c:when>
		             	<c:otherwise>
		             	</c:otherwise>
		             </c:choose>
		         </td>
	           </tr>
           </c:forEach>
         </tbody>
       </table>
    </div>
 	 <!--列表翻页 开始-->
    <div class="page">
    	<span style="padding:12px; float:left; color:#0099cc;">共${zlpfGerenBeanList.total}条记录</span>
        <ul class="pagination" id="pageCon">
            
        </ul>
    </div>
    <!--列表翻页 结束-->
</div>
<script type="text/javascript">
// 导出表格
$(document).ready(function(){
	$("#viewExcel").click(function(){
	  var path = WEBPATH+"/ajpfViewExcel.do?target=9&viewType=1"; 
      window.location.href=path;
	});
});
//分页
$(document).ready(function(){
	var curentPage = eval('${zlpfGerenBeanList.pageNum}');
	var totalPage = eval('${zlpfGerenBeanList.pages}');
	var areaType = eval('${areaType}');
	if(totalPage>0){
		var options = {
			bootstrapMajorVersion: 3,
		    currentPage: curentPage,
		    totalPages: totalPage,
		    numberOfPages: 5,
		    itemTexts: function (type, page, current) {
		            switch (type) {
		            case "first":
		                return "首页";
		            case "prev":
		                return "&laquo;";
		            case "next":
		                return "&raquo;";
		            case "last":
		                return "尾页";
		            case "page":
		                return page;
		            }
		    },
		    onPageClicked: function (event, originalEvent, type, page) {
            	business.addMainContentParserHtml(WEBPATH+'/jcpm/goJcpmGr.do?areaType='+areaType+'&pageNum='+page,null);
            }
    	};
    	$('#pageCon').bootstrapPaginator(options);
   	}
});
</script>
</body>
</html>