<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
    <title>案卷复核</title>
</head>
<link rel="stylesheet" href="${webpath }/static/css/caiji.css">
<div style="width: 100%;padding: 0 2.5%;  box-sizing: border-box;" class="auto-apply">
<div class="center_home_weizhi">当前位置：案卷复核列表  </div>
<div class="center-body">
    <div class="panel-group" id="accordion">
        <div class="row">
            <div class="col-lg-12">
                <div class="smart-widget">
                    <div class="smart-widget-inner">
                        <div class="smart-widget-body form-horizontal">

                                 <div class="form-group">
                                     <div class="col-lg-12" style="margin-top: 50px;padding-left: 0px;padding-right: 0px">
                                         <form id= "searchForm"  role="form">
                                         <input id="areaCode" name="areaCode" value="${areaCode}" type="hidden">
                                         <%--<div class="col-lg-2" style="padding-left: 0px;padding-right: 0px">
                                             <label class="control-label col-md-4" style="padding-left: 0px;padding-right: 0px">案卷名称：</label>
                                             <div class="col-md-7" style="padding-left: 0px;padding-right: 0px">
                                                 <input class="form-control" type="text" id="fileName" name="fileName" value="${fileName}">
                                             </div>
                                         </div>--%>
                                         <div class="col-lg-2" style="padding-left: 0px;padding-right: 0px">
                                             <label class="control-label col-md-3"  style="padding-left: 0px;padding-right: 0px">文书号：</label>
                                             <div class="col-md-8" style="padding-left: 0px;padding-right: 0px">
                                                 <input class="form-control" type="text" id="fileCode" name="fileCode" value="${fileCode}">
                                             </div>
                                         </div>
                                         <div class="col-lg-2" style="padding-left: 0px;padding-right: 0px">
                                             <label class="control-label col-md-3" style="padding-left: 0px;padding-right: 0px">复核状态:</label>
                                             <div class="col-md-8" style="padding-left: 0px;padding-right: 0px">
                                                 <select class="form-control"  name="recheckType" id="recheckType">
                                                     <option value="">请选择</option>
                                                     <option value="0" <c:if test="${recheckType=='0' }">selected </c:if>>实体否决</option>
                                                     <option value="1" <c:if test="${recheckType=='1' }">selected </c:if>>严重不全</option>

                                                 </select>
                                             </div>
                                         </div>
                                         <div class="col-lg-2" style="padding-left: 0px;padding-right: 0px">
                                             <label class="control-label col-md-3" style="padding-left: 0px;padding-right: 0px">案卷类型:</label>
                                             <div class="col-md-8" style="padding-left: 0px;padding-right: 0px">
                                                 <select id ="fileType" class="form-control" name ="fileType">
                                                     <!-- 0代表 行政处罚案卷 ；1代表按日计罚案卷； 2代表移送行政拘留案卷；3代表涉嫌犯罪移送案卷；4代表申请法院强制执行案卷；5代表发现问题的污染源现场监督检查稽查案卷   -->
                                                     <option value = ""  >请选择案卷类型</option>
                                                     <option value="0" <c:if test="${fileType=='0' }">selected</c:if>>行政处罚案卷</option>
                                                     <option value="1" <c:if test="${fileType=='1' }">selected</c:if>>按日计罚案卷</option>
                                                     <option value="2" <c:if test="${fileType=='2' }">selected</c:if>>移送行政拘留案卷</option>
                                                     <option value="3" <c:if test="${fileType=='3' }">selected</c:if>>涉嫌犯罪案卷</option>
                                                     <option value="6" <c:if test="${fileType=='6' }">selected</c:if>>查封扣押案卷</option>
                                                     <option value="7" <c:if test="${fileType=='7' }">selected</c:if>>限产停产案卷</option>
                                                     <option value="9" <c:if test="${fileType=='9' }">selected</c:if>>不予处罚</option>
                                                 </select>
                                             </div>
                                         </div>
                                     </form>
                                         <div class="col-lg-4" style="padding-left: 0px;padding-right: 0px">
                                             <button class="btn btn-primary" id="filterButton" onclick="filterButton()">筛选</button>
                                             <button class="btn btn-default" id="resetButton" onclick="resetButton()">重置</button>
<%--                                             <button type="button" class="btn btn-primary" id ="viewExcel" >导出</button>--%>
                                         </div>

                                     </div>
                                 </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div>

        </div>

    </div>

    <div class="scroll-container">
        <input value="${areaCode}" type="hidden">
        <table class="table table-bordered  " style="margin-top: 20px">
            <thead>
            <tr>
                <td rowspan="2" height="30" bgcolor="#efefef">序号</td>
                <%--<td colspan="3" bgcolor="#efefef">行政区</td>--%>
                <td  bgcolor="#efefef">省</td>
                <td bgcolor="#efefef">市</td>
                <td bgcolor="#efefef">县</td>
<%--                <td rowspan="2" bgcolor="#efefef">案卷名称</td>--%>
                <td rowspan="2" bgcolor="#efefef">文书号</td>
                <td rowspan="2" bgcolor="#efefef">案卷类型</td>
                <td rowspan="2" bgcolor="#efefef">分数</td>
                <td rowspan="2" bgcolor="#efefef">扣分类型</td>
                <td rowspan="2" bgcolor="#efefef">案卷使用类型</td>
                <td rowspan="2" bgcolor="#efefef">操作</td>
<%--                <c:if test="${isTeamLeader == 1 or isTeamLeader == '1'}">--%>
<%--                    <td rowspan="2" bgcolor="#efefef">专家信息</td>--%>
<%--                </c:if>--%>


<%--                <td rowspan="2" bgcolor="#efefef">申请复核时间</td>--%>
<%--                <td rowspan="2" bgcolor="#efefef">是否否决</td>--%>

            </tr>
            <%--<tr>

            </tr>--%>
            </thead>
            <tbody>
            <c:forEach items="${dataList}" var="item" varStatus="status">
                <tr>
                    <input id="lawFileName${status.index+1}" value="${item.lawFileName}" type="hidden">
                    <input id="lawFileUrl${status.index+1}" value="${item.lawFileUrl}" type="hidden">
                    <input id="fileUrl${status.index+1}" value="${item.fileUrl}" type="hidden">
                    <input id="fileName${status.index+1}" value="${item.fileName}" type="hidden">

                    <td align="center">${status.index+1 }</td>
                    <td>${item.province}</td>
                    <td>${item.city}</td>
                    <td>${item.county}</td>
<%--                    <td>${item.fileName}</td>--%>
                    <td>${item.fileCode}</td>
                    <td>
                        <c:if test="${item.fileType=='0' }">行政处罚</c:if>
                        <c:if test="${item.fileType=='1' }">按日计罚</c:if>
                        <c:if test="${item.fileType=='6' }">查封扣押</c:if>
                        <c:if test="${item.fileType=='7' }">限产停产</c:if>
                        <c:if test="${item.fileType=='2' }">行政拘留</c:if>
                        <c:if test="${item.fileType=='3' }">涉嫌犯罪</c:if>
                        <c:if test="${item.fileType=='9' }">不予处罚</c:if>
                    </td>
                    <td>${item.expertConsiderScore}</td>

<%--                    <td>${item.indexName}</td>--%>
<%--                    <c:if test="${isTeamLeader == 1 or isTeamLeader == '1'}">--%>
<%--                        <td>${item.grepUserName}</td>--%>
<%--                    </c:if>--%>

<%--                    <td>--%>
<%--                        <c:if test="${item.recheckType == 0}">未申请</c:if>--%>
<%--                        <c:if test="${item.recheckType == 1}">待合议</c:if>--%>
<%--                        <c:if test="${item.recheckType == 2}">已完成</c:if>--%>
<%--                        <c:if test="${item.recheckType == 3}">进行中</c:if>--%>
<%--                    </td>--%>
                    <td>
                        <c:if test="${item.recheckType == 0}">实体否决</c:if>
                        <c:if test="${item.recheckType == 1}">严重不全</c:if>
                    </td>
                    <td>${item.indexName} </td>

                    <td style="display: flex;justify-content: space-around;align-items: center">
                        <c:if test="${item.recheckType !=''}">
                        <button class="btn btn-success btn-xs" onclick="goApplyPage(${item.id},3)">查看</button>
                        </c:if>
<%--                        <button class="btn btn-default btn-xs" onclick="downloadFile('${item.fileUrl}','${item.fileName}')">下载案卷</button>--%>
<%--                        <button class="btn btn-info btn-xs" onclick="downloadFile('${item.lawFileUrl}','${item.fileCode}_${item.lawFileName}')">下载政策文件</button>--%>
                        <%--<c:if test="${item.recheckType == 1 or item.recheckType == 2}">
                            <button class="btn btn-info btn-xs" onclick="downloadFile('${item.lawFileUrl}','${item.fileCode}_${item.lawFileName}')">下载政策文件</button>
                        </c:if>--%>
<%--                        <c:if test="${userTypeCode==2 or userTypeCode == '2'}">--%>
<%--                            &lt;%&ndash;判断是不是组长&ndash;%&gt;--%>
<%--                            <c:if test="${isTeamLeader == 1 or isTeamLeader == '1'}">--%>
<%--                                <c:if test="${item.recheckType == 1 or item.recheckType == 3 }">--%>
<%--                                    <button class="btn btn-info btn-xs" onclick="goApplyPage(${item.id},2)">复核</button>--%>
<%--                                </c:if>--%>
<%--                                <c:if test="${item.recheckType == 2}">--%>
<%--                                    <button class="btn btn-success btn-xs" onclick="goApplyPage(${item.id},3)">查看</button>--%>
<%--                                </c:if>--%>
<%--                            </c:if>--%>
<%--                            <c:if test="${isTeamLeader == 2 or isTeamLeader == '2'}">--%>
<%--                                <c:if test="${(item.recheckType == 1 or item.recheckType == 3) and item.isEdit ==1}">--%>
<%--                                    <button class="btn btn-info btn-xs" onclick="goApplyPage(${item.id},2)">复核</button>--%>
<%--                                </c:if>--%>
<%--                                <c:if test="${(item.recheckType != 1 and item.recheckType != 3) or item.isEdit !=1}">--%>
<%--                                    <button class="btn btn-success btn-xs" onclick="goApplyPage(${item.id},3)">查看</button>--%>
<%--                                </c:if>--%>
<%--                            </c:if>--%>

<%--                        </c:if>--%>
<%--                        <c:if test="${userTypeCode!=2 and userTypeCode != '2'}">--%>
<%--                            <c:if test="${item.recheckType == 1 or item.recheckType == 3}">--%>
<%--                                <button class="btn btn-info btn-xs" onclick="goApplyPage(${item.id},2)">复核</button>--%>
<%--                            </c:if>--%>
<%--                            <c:if test="${item.recheckType == 2}">--%>
<%--                                <button class="btn btn-success btn-xs" onclick="goApplyPage(${item.id},3)">查看</button>--%>
<%--                            </c:if>--%>
<%--                        </c:if>--%>


                    </td>
                </tr>
            </c:forEach>


            </tbody>
        </table>


    </div>

    <!--列表翻页 开始-->
    <div class="page">
<%--        <span style="padding:12px; float:left; color:#0099cc;">共${dataList.total}条记录</span>--%>
        <ul class="pagination" id="pageCon">

        </ul>
    </div>
    <!--列表翻页 结束-->

    <!-- 强制修改密码模态框 -->
    <div class="modal fade" id="toResetPwd2"tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog">
            <div class="modal-content">

            </div>
        </div>
    </div>

</div>


</div>
<script>
    $(function() {

        //如果密码没有重置
        $.ajax({
            type: "post",
            url: WEBPATH+"/sys/checkpwd.do",
            async:false,
            success: function (data) {
                if(data.result=="success"){
                    var IfUpdatePwd = data.data;
                    if(IfUpdatePwd==0){
                        var options2 = {remote:encodeURI(WEBPATH+'/sys/changePWD.do')};
                        $('#toResetPwd2').modal(options2);
                        $('#toResetPwd2').modal({backdrop: 'static', keyboard: false});
                    }
                }else{
                    swal("服务异常，数据请求失败!", "", "warning");
                }
            },
            error: function(){
                swal("网络异常，请求数据失败!", "", "error");
            }
        });
    });
    //跳转 申请复核页面. 1申请,2修改,3查看
    function goApplyPage(fileId,type) {
        console.log("案卷id",fileId,"操作类型",type)
        macroMgr.onLevelOneMenuClick(null, 'filesRecheck/approvalApplyPage.do?fileId='+fileId+'&type='+type)
        // macroMgr.onLevelOneMenuClick(null, 'filesRecheck/approvalApplyPage.do?fileid='+1)

    }
    function xiaZaiAnJuan(id){
        //var fileCode = $("#fileCode").html();
        var fileid  = id;
        $.ajax({
            type:"post",
            url:WEBPATH+'/jcpf/jcpfExistFileUrl.do',
            data:{fileId:fileid },           //注意数据用{}
            success:function(data){  //成功
                if("yes" == data){
                    window.location.href="${pageContext.request.contextPath}/jcpf/jcpfAnJuandown.do?fileid="+fileid;
                    return false;
                }else if("no" == data){
                    swal( "操作失败","该案卷不存在!", "error");
                    return false;
                }else if("suffixerror" ==data){
                    swal( "操作失败","该案卷上传数据格式有问题!", "error");
                    return false;
                }
            }
        });
    };




    // 处理筛选按钮点击事件
    function filterButton() {
        // business.addMainContaierHtml(WEBPATH + '/filesRecheck/declareRecheckList.do', $("#searchForm").serialize());
        macroMgr.onLevelOneMenuClick(null, 'filesRecheck/provinceRecheckList.do',$("#searchForm").serialize())
    }

    // 处理重置按钮点击事件
    function resetButton() {
        // 清空输入框和下拉框内容
        //案卷名称
        document.getElementById('fileName').value = "";
        //案卷文书号
        document.getElementById('fileCode').value = "";
        //案卷类型
        document.getElementById('fileType').value = "";
        //复核状态
        document.getElementById('recheckType').value = "";

        // business.addMainContaierHtml(WEBPATH + '/filesRecheck/declareRecheckList.do', $("#searchForm").serialize());
        macroMgr.onLevelOneMenuClick(null, 'filesRecheck/provinceRecheckList.do',$("#searchForm").serialize())
    }


    //导出Excel
    $("#viewExcel").click(function(){
        var fileName = $("#fileName").val();
        var fileCode = $("#fileCode").val();
        var recheckType = $("#recheckType").val();
        var fileType = $("#fileType").val();
        var areaCode = $("#areaCode").val();

        var path = WEBPATH+"/filesRecheck/filesExportExcel.do?fileName="+fileName+"&fileCode="+fileCode+"&recheckType="+recheckType+"&fileType="+fileType+"&areaCode="+areaCode;
        window.location.href=path;

    });


    function downloadFile(url, fileName) {
        if(fileName){
            fileName=fileName.replace("[","【");
            fileName=fileName.replace("]","】");
            fileName=fileName.replace("{","【");
            fileName=fileName.replace("}","】");
        }
        if (url != null && url != "") {
            console.log(url)
            location.href = "${webpath}/filedownload.do?url=" + url + "&fileName=" + fileName;
        } else {
            swal({title: "提示", text: "您下载的附件不存在！", type: "info"});
        }
    };






</script>

<style>
    .center-body {
        width: 100%;
        margin: 10px;
        overflow: hidden;
    }
    .head-item {
        width: 1200px;
        height: 45px;
        margin-top: 15px;
    }

    .head-item>ul>li {
        float: left;
    }

    .head-item>ul>li>a {
        display: block;
        margin-right: 15px;
        width: 158px;
        height: 45px;
        background: #3f91c2;
        text-align: center;
        line-height: 45px;
        box-shadow: 0 0 5px #999;
        overflow: hidden;
        border-radius: 8px;
        color: #ffffff;
        font-size: 16px;
    }
    .ul-clearfix {
        *zoom: 1;
        margin-left: 10px;
    }
    .scroll-container{
        height: 600px;
        width: 100%;/* 设置容器的高度 */
        overflow: auto; /* 自动显示滚动条 */
    }
    /*table {*/
    /*    border-collapse: collapse;*/
    /*    width: 100%;*/
    /*}*/
    /*.table>thead>tr>td{*/
    /*    font-size: 18px;*/
    /*    font-weight: bold;*/
    /*}*/
    /*th, td {*/
    /*    border: 1px solid black;*/
    /*    padding: 8px;*/
    /*    text-align: center;*/
    /*    font-size: 18px;*/
    /*}*/

</style>
</html>
