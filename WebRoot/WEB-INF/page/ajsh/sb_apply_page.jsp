
<%@ page import="com.changneng.sa.bean.FilesRecheckItem" %>
<%@ page import="java.util.List" %>
<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
    <title>案卷复核</title>
</head>
<link rel="stylesheet" href="${webpath }/static/css/caiji.css">
<div style="width: 100%;padding: 0 2.5%;  box-sizing: border-box;" class="auto-apply">
<div class="center_home_weizhi">当前位置：案卷复核</div>
<div class="main-center-body">
    <div class="center-body">
        <legend  style="font-weight:bold;color:#23b7e5;font-size: 18px; ">| 案卷基本信息</legend>
        <input id="fileCode" name="fileCode"  type="text" style="display:none;" value="${dataInfo.fileCode}" />
        <input id="filesId" name="filesId"  type="text" style="display:none;" value="${dataInfo.filesId}" />
        <div class="form-group">
            <div class="col-lg-4 col-sm-3 col-xs-12" style="margin-top:10px;">
                <strong>所属行政区:</strong>
                <span>${dataInfo.province}${dataInfo.city}${dataInfo.county}</span>
            </div>
            <div class="col-lg-4 col-sm-3 col-xs-12" style="margin-top:10px;">
                <strong> 案卷文书号: </strong>   <span>${dataInfo.fileCode}</span>
            </div>
            <div class="col-lg-4 col-sm-3 col-xs-12" style="margin-top:10px;">
                <strong>违法案件类型:</strong>    <span>${dataInfo.fileTypeName}</span>
            </div>
        </div>

        <div class="form-group">
            <div class="col-lg-4 col-sm-3 col-xs-12" style="margin-top:20px;">
                <strong> 罚款金额:</strong>
                <span>${dataInfo.faKuanShuE}</span>
            </div>
            <div class="col-lg-4 col-sm-3 col-xs-12" style="margin-top:20px;">
                <strong>案件来源: </strong>   <span>${dataInfo.fileSource}</span>
            </div>
        </div>

        <div class="form-group">
            <div class="col-lg-12 col-sm-3 col-xs-12" style="margin-top:20px;">
                <strong> 案情简介:</strong>
                <span>${dataInfo.fileSimpleInfo}</span>
            </div>
        </div>



</div>

<div class="center-body">
    <legend   style="font-weight:bold;color:#23b7e5; margin-top:30px;font-size: 18px;">| 评查信息 <span style="font-weight:bold;color:#f30418; margin-top:30px;font-size: 16px;">（不显示评查信息内容案卷为严重不全案卷）</span></legend>

    <table class="table table-bordered" id="myTable">
        <thead>
        <tr>
            <td width="50px"  height="30" bgcolor="#efefef">序号</td>
            <td width="150px" bgcolor="#efefef">评查项目</td>
            <td width="350px" bgcolor="#efefef">表现形式</td>
            <td width="500px" bgcolor="#efefef">第一轮评查</td>
            <td bgcolor="#efefef">合议评查</td>
        </tr>
        </thead>
        <tbody>
        <c:forEach items="${itemList}" var="item" varStatus="status">
            <tr>
                <td rowspan="2" align="center">${status.index+1 }</td>
                <td rowspan="2">${item.indexScoreName}</td>
                <td rowspan="2">${item.itemScoreName}</td>
                <td>【专家A】<br>${item.commeStrA}</td>
                <td>【委员A】<br>${item.commeStrCommitA}</td>
            </tr>
            <tr>
                <td>【专家B】<br>${item.commeStrB}</td>
                <td>【委员B】<br>${item.commeStrCommitB}</td>
            </tr>
        </c:forEach>

        </tbody>
    </table>

</div>

<div class="center-body">
    <legend   style="font-weight:bold;color:#23b7e5; margin-top:50px;font-size: 18px;">| 地方复核申请</legend>


        <div class="fileParent">
            <div  style="display: flex; align-items: flex-end;">
                <p style="  height: 30px; line-height: 30px; margin: 0;  padding: 0 10px 0 0; box-sizing: border-box;"> 政策依据:</p>
                <c:if test="${type != 3}">
                     <span id="uploadTr" <c:if test="${ lawFileName != '' && lawFileName!=null}">style='display:none'</c:if> >
                        <input type="file" id="provinceFile" value="文件上传">
                        <input type="hidden" class="form-control" name="lawFileName" id="lawFileName" value="${lawFileName}">
                        <input type="hidden" class="form-control" name="lawFileUrl" id="lawFileUrl" value="${lawFileUrl}">

                    </span>
                </c:if>


                <span id="wsc" <c:if test="${ lawFileName == '' or lawFileName==null}">style='display:none'</c:if>>
                                <span id="fileSpan">
                                        <a href="javascript:void(0)"
                                           onclick="downloadFile('${lawFileUrl}','${lawFileName}')"> ${lawFileName} </a>
                                    </span>
                    <c:if test="${type != 3}">
                        <input id="reButton" class="btn btn-danger btn-xs" type="button" value="重新上传"/>
                    </c:if>

                </span>

            </div>

            <div>
                <c:if test="${type != 3}">
                    <button onclick="addApplyItem(${item.id})" style="margin: 0 10px" class="btn btn-info">添加</button>
                </c:if>

            </div>
        </div>


    <table id="myTable2" class="table table-bordered">
        <thead>
        <tr>
            <th id="itemID" bgcolor="#efefef" width="50px">序号</th>
            <th id="indexScoreId" bgcolor="#efefef" width="140px">评查</th>
            <th style="width: 500px;" id="itemScoreId" bgcolor="#efefef" width="25%" >评查细项</th>
            <th id="reviewOpinion" bgcolor="#efefef">复核意见</th>
            <c:if test="${type==3}"><th id="replyOpinion" bgcolor="#efefef" width="30%">回复意见</th></c:if>
            <c:if test="${type!=3}"><th bgcolor="#efefef" width="80px">操作</th></c:if>
        </tr>
        </thead>
        <tbody>
            <c:if test="${type==2 || type ==3}">
                <c:forEach items="${recheckItemList}" var="item" varStatus="status">
                    <tr>
                        <td align="center" >${status.index+1 }</td>
                        <td>
                            <select style="width: 100%;" id="indexScoreId${status.index+1}" class="select1" onchange="selectChange(this.value,${status.index+1 })" <c:if test="${type==3}">disabled="true"</c:if>>
                                <option value = "">请选择</option>
                                <c:forEach items="${selectList}" var="item2" varStatus="status">
                                    <c:if test="${item.indexScoreId == item2.indexScoreId}">
                                        <option value = "${item2.indexScoreId}"  <c:if test="${item.indexScoreId == item2.indexScoreId}">selected="selected"</c:if>>
                                                ${item2.indexScoreName}
                                        </option>
                                    </c:if>
                                    <c:if test="${item2.isConsider == 2}">
                                        <option value = "153"  <c:if test="${item2.isConsider == 2}">selected="selected"</c:if>>
                                                严重不全
                                        </option>
                                    </c:if>
                                </c:forEach>
                            </select>
                        </td>
                        <td>
                            <select style="width: 500px;" id="itemScoreId${status.index+1}" class="select2" <c:if test="${type==3}">disabled="true"</c:if>>
                                <option value = "">请选择</option>
                                <c:forEach items="${selectList}" var="item3" varStatus="status">
                                    <c:if test="${item3.indexScoreId == item.indexScoreId}">
                                        <c:forEach items="${item3.itemList}" var="item4" varStatus="status2">
                                            <option value = "${item4.itemScoreId}" <c:if test="${item4.itemScoreId == item.itemScoreId}">selected="selected"</c:if>>
                                                    ${item4.itemScoreName}
                                            </option>
                                        </c:forEach>
                                    </c:if>
                                    <c:if test="${item3.isConsider == 2}">
                                        <c:forEach items="${item3.itemList}" var="item4" varStatus="status2">
                                            <option value = "1358" <c:if test="${item3.isConsider == 2}">selected="selected"</c:if>>
                                                    严重不全
                                            </option>
                                        </c:forEach>
                                    </c:if>
                                </c:forEach>
                            </select>
                        </td>
                        <%--<td><input class="input1" style="width: 100%;" type="text" value="${item.reviewOpinion}" <c:if test="${type==3}">disabled="true"</c:if>></td>--%>
                        <td><textarea class="input1" rows="5"  style="width: 100%;" type="text"   <c:if test="${type==3}">disabled="true"</c:if>>${item.reviewOpinion}</textarea></td>
                        <c:if test="${type==3}"><td><span>${item.replyOpinion}</span></td></c:if>
                        <c:if test="${type!=3}"><td style="text-align:center"><button class="btn btn-danger btn-xs"  onclick="deleteRow(this)">删除</button></td></c:if>
                    </tr>
                </c:forEach>
            </c:if>
        </tbody>
    </table>
<c:if test="${type==3}">
    <form>
<%--        <div>--%>
<%--            <span>是否否决：</span>--%>
<%--            <input style="margin-left: 12px" type="radio" name="isMinusScore" value="1"--%>
<%--                   <c:if test="${dataInfo.isMinusScore==1}">checked="checked"</c:if>--%>
<%--                   <c:if test="${type==3}">disabled="true"</c:if>> 否决--%>
<%--            <input type="radio" name="isMinusScore" value="0"--%>
<%--                   <c:if test="${dataInfo.isMinusScore==0}">checked="checked"</c:if>--%>
<%--                   <c:if test="${type==3}">disabled="true"</c:if>> 不否决--%>
<%--        </div>--%>

<%--        <div style="margin-top: 20px;<c:if test="${type==3 && dataInfo.isMinusScore==0}"> display:none </c:if>"> <span style="text-align: right ">倒扣分情形：</span>--%>
<%--            <input style="margin-left: 40px;" type="checkbox" name="minusScene" value="1"  <c:if test="${type==3}">disabled="true"</c:if>--%>
<%--            <c:forEach items="${dataInfo.minusScene.split(',')}" var="item">--%>
<%--                   <c:if test="${item==1}">checked="checked"</c:if>--%>
<%--            </c:forEach>--%>
<%--            > 办案人员不具有行政执法资格，或少于两人的--%>
<%--            <input style="margin-left: 60px;" type="checkbox" name="minusScene" value="2" <c:if test="${type==3}">disabled="true"</c:if>--%>
<%--            <c:forEach items="${dataInfo.minusScene.split(',')}" var="item">--%>
<%--                   <c:if test="${item==2}">checked="checked"</c:if>--%>
<%--            </c:forEach>--%>
<%--            > 违法主体不清或认定错误的--%>
<%--            <br>--%>
<%--            <input style="margin-left: 92px;margin-top: 20px;" type="checkbox" name="minusScene" value="3"  <c:if test="${type==3}">disabled="true"</c:if>--%>
<%--            <c:forEach items="${dataInfo.minusScene.split(',')}" var="item">--%>
<%--                   <c:if test="${item==3}">checked="checked"</c:if>--%>
<%--            </c:forEach>--%>
<%--            > 证据取得的方式、手段、途径不符合法定要求的--%>
<%--            <input style="margin-left: 48px;margin-top: 20px;" type="checkbox" name="minusScene" value="4" <c:if test="${type==3}">disabled="true"</c:if>--%>
<%--            <c:forEach items="${dataInfo.minusScene.split(',')}" var="item">--%>
<%--                   <c:if test="${item==4}">checked="checked"</c:if>--%>
<%--            </c:forEach>--%>
<%--            > 作出的决定无法律、法规、规章依据的--%>
<%--            <input style="margin-left: 48px;margin-top: 20px;" type="checkbox" name="minusScene" value="5" <c:if test="${type==3}">disabled="true"</c:if>--%>
<%--            <c:forEach items="${dataInfo.minusScene.split(',')}" var="item">--%>
<%--                   <c:if test="${item==5}">checked="checked"</c:if>--%>
<%--            </c:forEach>--%>
<%--            > 违反法定程序的--%>
<%--        </div>--%>

        <div style="margin-top: 20px;"><div>否决理由：</div>
            <textarea id="finalOpinion" rows="4" cols="50" style="margin-left: 100px;width: calc(100% - 100px);"
                      <c:if test="${type==3}">disabled="true"</c:if>>${dataInfo.finalOpinion}</textarea>
        </div>
<%--        <div style="margin-top: 20px; <c:if test="${type==3 && dataInfo.isMinusScore==1}"> display:none </c:if>"><div>改进意见：</div>--%>
<%--            <textarea id="modifyOpinion" rows="4" cols="50" style="margin-left: 100px;width: calc(100% - 100px);"--%>
<%--                      <c:if test="${type==3}">disabled="true"</c:if>>${dataInfo.modifyOpinion}</textarea>--%>
<%--        </div>--%>
<%--        <div style="margin-top: 20px;"> <div><span> 争议点：</span></div>--%>
<%--            <textarea id="dispute" rows="4" cols="50" style="margin-left: 100px;width: calc(100% - 100px);"--%>
<%--                      <c:if test="${type==3}">disabled="true"</c:if>>${dataInfo.dispute}</textarea>--%>
<%--        </div>--%>

    </form>
</c:if>


</div>
    <div class="center-body" style="float: right">
        <button onclick="returnList()" style="margin: 0 10px" class="btn btn-default">返回</button>
        <c:if test="${type!=3}"><button onclick="submitSave(${type})" style="margin: 0 10px" class="btn btn-info">提交</button></c:if>
    </div>

<div id="myModal" style="height: 250px!important; display: none">
<%--    <div style="display: flex; align-items: center;">--%>
<%--        <p style="color: red; margin-right: 5px; margin-left: 15px">*</p>--%>
<%--        <div style="margin-right: 5px;">评查项</div>--%>
<%--        <select class="col-lg-2"  name="taskStateCode">--%>
<%--            <option value="">请选择</option>--%>
<%--            <option value="1">已结案</option>--%>
<%--            <option value="2">未结案</option>--%>
<%--        </select>--%>

<%--        <p style="color: red; margin-right: 5px; margin-left: 15px">*</p>--%>
<%--        <div style="margin-right: 5px;">评查细项</div>--%>
<%--        <select class="col-lg-2"  name="taskStateCode">--%>
<%--            <option value="">请选择</option>--%>
<%--            <option value="1">已结案</option>--%>
<%--            <option value="2">未结案</option>--%>
<%--        </select>--%>

<%--        <p style="color: red; margin-right: 5px; margin-left: 15px">*</p>--%>
<%--        <div style="margin-right: 5px;">复核意见</div>--%>
<%--        <input id="mySerious" type="text" class="col-lg-2" style="width: 80%" value="" />--%>

<%--        <div class="col-lg-2">--%>
<%--            <button onclick="delApplyItem(${item.id})" style="margin: 0 10px" class="btn btn-info btn-xs">删除</button>--%>
<%--        </div>--%>
<%--    </div>--%>

</div>


<%--构建隐藏的下拉框--%>
<c:forEach items="${selectList}" var="item" varStatus="status">
    <select id="noneItemScoreId${item.indexScoreId}" style="display:none;">
        <option value = "">请选择</option>
        <c:forEach items="${item.itemList}" var="item2" varStatus="status2">
<%--            <option value = "${item2.itemScoreId}">${item2.itemScoreName}</option>--%>
            <c:if test="${item.isConsider==2}"> <option value = "153">严重不全</option></c:if>
            <c:if test="${item.isConsider!=2}"><option value = "${item2.itemScoreId}">${item2.itemScoreName}</option></c:if>
        </c:forEach>
    </select>
</c:forEach>
</div>
</div>
<script>
    $(function (){

    })
    var table = document.getElementById("myTable");

    // 获取第一行的所有单元格
    var cells = table.rows[0].cells;
    // 合并第一列的单元格
    cells[0].rowSpan = 2;
    cells[1].rowSpan = 2;
    cells[2].rowSpan = 2;

    var rowId= 1;
    function addApplyItem() {
        // var modal = document.getElementById("myModal");
        // modal.style.display = "block";
        var tableBody = document.getElementById("myTable2").getElementsByTagName('tbody')[0];
        var newRow = tableBody.insertRow();
        rowId = newRow.rowIndex
        console.log(rowId,newRow.rowIndex);

        var cell1 = newRow.insertCell(0);
        cell1.innerHTML = rowId;

        var cell2 = newRow.insertCell(1);
        var indexScoreId = "indexScoreId"+rowId;
        cell2.innerHTML = '<select style=\'width:100%;\' id="'+indexScoreId+'" class="select1">' +
                            '<option value = ""  >请选择</option>' +
                            '<c:forEach items="${selectList}" var="item" varStatus="status">'+
                            <%--'<option value = "${item.indexScoreId}"  >${item.indexScoreName}</option>' +--%>
            '<c:if test="${item.isConsider==2}"> <option value = "153">严重不全</option></c:if>' +
            '<c:if test="${item.isConsider!=2}"> <option value = "${item.indexScoreId}"  >${item.indexScoreName}</option></c:if>' +
                            '</c:forEach>'+
                           '</select>';
        var selectElement = document.getElementById(indexScoreId);
        var selectRowId = rowId;
        selectElement.addEventListener("change", function(event) { // 添加 onchange 事件处理函数
            var selectedValue = event.target.value; // 获取当前选中的选项的值
            selectChange(selectedValue,selectRowId); // 将选项的值传递给其他函数进行处理
        });
        var cell3 = newRow.insertCell(2);
        var itemScoreId = "itemScoreId"+rowId;
        cell3.innerHTML = '<select class="select2" id="'+itemScoreId+'" style="width: 500px;"><option value = "">请选择</option></select>';

        var cell4 = newRow.insertCell(3);
        cell4.innerHTML = '<textarea class="input1" style="width: 100%;"  rows="5"></textarea>';

        var cell5 = newRow.insertCell(4);
        cell5.innerHTML = '<button class="btn btn-danger btn-xs"   onclick="deleteRow(this)">删除</button>';
        cell5.style="text-align:center";

        rowId++;

    }
    //评查大项下拉框点击事件
    function selectChange(selectedValue,rawId){
        debugger
            console.log(selectedValue,rawId)
        var itemScoreId = "itemScoreId"+rawId;
        //获取二级下拉框
        var selectElement = document.getElementById(itemScoreId);
        selectElement.options.remove(0);
        selectElement.options.length = 0;

        //获取隐藏下拉框
        var noneItemScoreId = "noneItemScoreId"+selectedValue;
        var nonoSelect = null;
        if(selectedValue==153){
             nonoSelect = 153;
        }else {
             nonoSelect = document.getElementById(noneItemScoreId);
        }

        debugger;
        if (nonoSelect == null){
            var option = document.createElement("option");
            option.text = "请选择";
            option.value = "";
            selectElement.add(option)
            return;
        }
        if (nonoSelect == 153){
            var option = document.createElement("option");
            option.text = "严重不全";
            option.value = "1358";
            selectElement.add(option)
            return;
        }
        // 获取源 <select> 中所有的选项
        var options = nonoSelect.options;
        // var nonoSelect = $('#noneItemScoreId');
        // 复制源 <select> 元素，并将其所有选项添加到目标 <select> 中
        // $(nonoSelect).clone().appendTo('#itemScoreId');
        // selectElement.add(nonoSelect.getElementsByClassName("option"))
        // 遍历所有选项，并将其复制到目标 <select> 中
        for (var i = 0; i < options.length; i++) {
            var option = options[i];
            var newOption = new Option(option.text, option.value);
            selectElement.add(newOption);
        }



        // //TODO  二级联动待实现
        // var option = document.createElement("option");  // 创建一个新的 option 元素
        // option.text = "二级联动待实现";  // 设置 option 的文本内容
        // option.value = "1";  // 设置 option 的值
        // selectElement.add(option);
        //
        //
        // var secondSelect = document.getElementById(itemScoreId);
        //
        //
        // // 根据一级选项的值进行筛选
        // if (selectedValue === "108") {
        //     var option1Values = ["选项1-1", "选项1-2", "选项1-3"];
        //     option1Values.forEach(function(value) {
        //         var option = document.createElement("option");
        //         option.text = value;
        //         option.value = value;
        //         secondSelect.add(option);
        //     });
        // } else if (selectedValue === "option2") {
        //     var option2Values = ["选项2-1", "选项2-2", "选项2-3"];
        //     option2Values.forEach(function(value) {
        //         var option = document.createElement("option");
        //         option.text = value;
        //         option.value = value;
        //         secondSelect.add(option);
        //     });
        // } else if (selectedValue === "option3") {
        //     var option3Values = ["选项3-1", "选项3-2", "选项3-3"];
        //     option3Values.forEach(function (value) {
        //         var option = document.createElement("option");
        //         option.text = value;
        //         option.value = value;
        //         secondSelect.add(option);
        //     });

        // }

        <%--var optionHtml =  '<option value = ""  >请选择</option>' +--%>
        <%--    '<c:forEach items="${selectList[0]}" var="item" varStatus="status">'+--%>
        <%--    '<option value = "${item.itemList.indexScoreId}"  >${item.itemList.indexScoreName}</option>' +--%>
        <%--    '</c:forEach>';--%>
        <%--selectElement.innerHTML = optionHtml;--%>

    }

    function deleteRow(btn) {
        var row = btn.parentNode.parentNode;
        row.parentNode.removeChild(row);
        // 重新计算序号
        var tableBody = document.getElementById("myTable2").getElementsByTagName('tbody')[0];
        var rows = tableBody.getElementsByTagName('tr');
        for (var i = 0; i < rows.length; i++) {
            rows[i].cells[0].innerHTML = i + 1;
        }
        rowId =  rows.length+1;

    }
    //提交保存
    function submitSave(type){
        console.log("提交保存类型:",type)
        var filesId = document.getElementById("filesId").value;
        var fileCode = document.getElementById("fileCode").value;
        //传给后台的json数据
        var tableValues = [];
        var table = document.getElementById("myTable2"); // 获取表格元素
        var rows = table.rows; // 获取所有行
        // 遍历行（从索引为1的行开始，跳过表头）
        for (var i = 1; i < rows.length; i++) {
            var cells = rows[i].cells; // 获取当前行的所有单元格
            var rowValues = {};
            rowValues["filesId"] = filesId;
            rowValues["fileCode"] = fileCode;
            // 遍历单元格
            for (var j = 0; j < cells.length; j++) {
                if (j === 0){
                    //id
                    //     rowValues["id"] = cells[j].innerText;
                }else if (j === 1){
                    // 获取当前行中的下拉框元素
                    var select = cells[j].querySelector(".select1");
                    // 获取选中的项
                    var selectedOption = select.options[select.selectedIndex];
                    // 获取选中项的值和文本内容
                    var selectedValue = selectedOption.value;
                    var selectedText = selectedOption.innerText;
                    if(!selectedValue){
                        swal({title: "验证失败" ,text: "评查内容不能为空!",type:"error"});
                        return false;
                    }
                    //一级评查项
                    rowValues["indexScoreId"] = selectedValue;
                    rowValues["indexScoreName"] = selectedText;
                }else if (j === 2){
                    //二级评查细项
                    // 获取当前行中的下拉框元素
                    var select = cells[j].querySelector(".select2");
                    // 获取选中的项
                    var selectedOption = select.options[select.selectedIndex];
                    // 获取选中项的值和文本内容
                    var selectedValue = selectedOption.value;
                    var selectedText = selectedOption.innerText;
                    if(!selectedValue){
                        swal({title: "验证失败" ,text: "评查细项不能为空!",type:"error"});
                        return false;
                    }
                    //二级评查项
                    rowValues["itemScoreId"] = selectedValue;
                    rowValues["itemScoreName"] = selectedText;
                }else if (j === 3){
                    // 复核意见
                    var v=cells[j].querySelector("textarea").value;
                    if(!v){
                        swal({title: "验证失败" ,text: "复核意见不能为空!",type:"error"});
                        return false;
                    }
                    rowValues["reviewOpinion"] = v;
                }
            }
            tableValues.push(rowValues); // 将行值列表添加到表格值列表
        }
        //传给后台的json数据
        var data = {};
        data["filesId"] = filesId;
        data["fileCode"] = fileCode;
        data["itemList"] = tableValues;
        var lawFileName = document.getElementById("lawFileName").value;
        var lawFileUrl = document.getElementById("lawFileUrl").value;
        data["lawFileName"] = lawFileName;
        data["lawFileUrl"] = lawFileUrl;

        console.log(data)
        if (type === 1){
            //新增
            insert(data);
        }else if (type === 2){
            //修改
            update(data)
        }


    }
    //修改接口
    function update(dataValue){
        console.log(dataValue); // 打印表格值列表
        //调取后台新增接口
        $.ajax({
            type:"post",
            url:WEBPATH+'/filesRecheck/updateFilesRecheck.do',
            dataType: 'json',
            data: JSON.stringify(dataValue), // 将数据对象转换成 JSON 字符串作为请求参数
            contentType: 'application/json; charset=utf-8',
            success:function (data){
                if(data.result=="error"){
                    swal({title: data.message ,text: data.data,type:"error"});
                    return false;
                }else if(data.result=="success"){
                    swal({title: data.message ,text: "",type:"success"});
                    business.addMainContaierHtml(WEBPATH+'/filesRecheck/declareRecheckList.do?pageNum=1');
                }
            }
        })
    }
    //新增接口
    function insert(dataValue){
        console.log(dataValue); // 打印表格值列表
        //调取后台新增接口
        $.ajax({
            type:"post",
            url:WEBPATH+'/filesRecheck/submitFilesRecheck.do',
            dataType: 'json',
            data: JSON.stringify(dataValue), // 将数据对象转换成 JSON 字符串作为请求参数
            contentType: 'application/json; charset=utf-8',
            success:function (data){
                if(data.result=="error"){
                    swal({title: data.message ,text: data.data,type:"error"});
                    return false;
                }else if(data.result=="success"){
                    swal({title: data.message ,text: "",type:"success"});
                    business.addMainContaierHtml(WEBPATH+'/filesRecheck/declareRecheckList.do?pageNum=1');
                }
            }
        })
    }
    //返回接口
    function returnList(){
        business.addMainContaierHtml(WEBPATH+'/filesRecheck/declareRecheckList.do?pageNum=1');
    }


    function cancelApplyItem() {
        var modal = document.getElementById("myModal");
        modal.style.display = "none";

    }
    function delApplyItem( id) {

    }
    function xiaZaiAnJuan(id){
        //var fileCode = $("#fileCode").html();
        var fileid  = id;
        $.ajax({
            type:"post",
            url:WEBPATH+'/jcpf/jcpfExistFileUrl.do',
            data:{fileId:fileid },           //注意数据用{}
            success:function(data){  //成功
                if("yes" == data){
                    window.location.href="${pageContext.request.contextPath}/jcpf/jcpfAnJuandown.do?fileid="+fileid;
                    return false;
                }else if("no" == data){
                    swal( "操作失败","该案卷不存在!", "error");
                    return false;
                }else if("suffixerror" ==data){
                    swal( "操作失败","该案卷上传数据格式有问题!", "error");
                    return false;
                }
            }
        });
    };

    //分页
    $(document).ready(function(){
        var curentPage = eval('${ajMamageList.pageNum}');
        var totalPage = eval('${ajMamageList.pages}');

        if(totalPage>0){
            var options = {
                bootstrapMajorVersion: 3,
                currentPage: curentPage,
                totalPages: totalPage,
                numberOfPages: 5,
                itemTexts: function (type, page, current) {
                    switch (type) {
                        case "first":
                            return "首页";
                        case "prev":
                            return "&laquo;";
                        case "next":
                            return "&raquo;";
                        case "last":
                            return "尾页";
                        case "page":
                            return page;
                    }
                },
                onPageClicked: function (event, originalEvent, type, page) {
                    business.addMainContentParserHtml(WEBPATH+'/ajpf/ajpf_jiti_shi_list.do?areaType='+areaType+'&pageNum='+page,null);
                }
            };
            $('#pageCon').bootstrapPaginator(options);
        }
    });


    // 处理导出按钮点击事件
    function handleExport() {
        // 导出表格数据的逻辑
        $.ajax({
            type: "GET",
            url: WEBPATH+"/saveFilesBean.do",
            // data:{activityName:activityName},
            async:false,
            success: function(data){
                if(data.result=="error"){
                    swal({title: data.message ,text: "",type:"error"});
                    return false;
                }else if(data.result=="success"){
                    swal({title: data.message ,text: "",type:"success"});
                    // 在这里执行抽取案卷的相关操作

                }
            }
        });

    }

    //下载附件按钮
    function downloadFile(url, fileName) {
        if (url != null && url != "") {
            console.log(url)
            location.href = "${webpath}/filedownload.do?url=" + url + "&fileName=" + fileName;
        } else {
            swal({title: "提示", text: "您下载的附件不存在！", type: "info"});
        }
    }
    //重新上传按钮
    $("#reButton").click(function () {
        $("#wsc").css("display", "none");
        $("#uploadTr").css("display", "");
    });
    $(document).ready(function () {
        $("#provinceFile").fileinput({
            uploadUrl: WEBPATH + '/pdffileupload.do', // you must set a valid URL here else you will get an error
            language: 'zh',
            browseClass: 'btn btn-danger',//按钮样式
            //overwriteInitial: true,
            minFileCount: 1,
            allowedFileExtensions : [ 'rar', 'zip', 'pdf','RAR', 'ZIP', 'PDF'],
            maxFileCount: 1,
            minFileSize: 1,
            maxFileSize: 209920, //政策文件附件大小限制205M
            enctype: 'multipart/form-data',
            dropZoneTitle: "可拖拽文件到此处...",
            initialPreviewShowDelete: false,
            msgFilesTooMany: '选择上传的文件数量({n}) 超过允许的最大数值{m}！',
            msgZoomModalHeading: '文件预览',
            msgInvalidFileExtension: '不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
            msgNoFilesSelected: '请选择文件',
            msgValidationError: '文件类型不正确或文件过大',
            initialPreviewFileType: 'els',
            browseLabel: "选择政策文件",
            removeLabel: '删除',
            removeTitle: '删除文件',
            uploadLabel: '上传',
            uploadTitle: '上传文件',
            cancelLabel: '取消',
            cancelTitle: '取消上传',
            showPreview: false,
            autoReplace: true,
            slugCallback: function (filename) {
                return filename.replace('(', '_').replace(']', '_');
            }
        }).on('filepreupload', function (event, data, previewId, index) {
            //文件开始上传操作


        }).on('fileuploaded', function (event, data, previewId, index) {//文件上传完成执行操作

            console.log("---文件上传成功----", data.response.url, data.response.fileRealName)
            // saveProvinceFile(data.response.fileRealName,data.response.url);

            $("#lawFileUrl").val(data.response.url);
            $("#lawFileName").val(data.response.fileRealName);
            $("#fileSpan").html("<a href='javascript:void(0)' onclick='downloadFile(\"" + data.response.url + "\",\"" + data.response.fileRealName + "\")'>" + data.response.fileRealName + "</a>");

            $("#wsc").css("display", "");
            $("#uploadTr").css("display", "none");
        })
    })



</script>

<style>
    .center-body {
        /*width: 99%;*/
        margin: 10px;
        overflow: hidden;
    }
    .head-item {
        width: 1200px;
        height: 45px;
        margin-top: 15px;
    }
    .main-center-body {
        height: 75%;
        overflow: auto; /* 自动显示滚动条 */
        margin-top: 70px;
    }

    .head-item>ul>li {
        float: left;
    }

    .head-item>ul>li>a {
        display: block;
        margin-right: 15px;
        width: 158px;
        height: 45px;
        background: #3f91c2;
        text-align: center;
        line-height: 45px;
        box-shadow: 0 0 5px #999;
        overflow: hidden;
        border-radius: 8px;
        color: #ffffff;
        font-size: 16px;
    }
    .ul-clearfix {
        *zoom: 1;
        margin-left: 10px;
    }

    #myTable {
        width: 100%;
        padding: 0 10px;
        box-sizing: border-box;
    }
    /*.table>thead>tr>td{*/
    /*    font-size: 18px;*/
    /*    font-weight: bold;*/
    /*}*/
    /*th, td {*/
    /*    border: 1px solid black;*/
    /*    padding: 8px;*/
    /*    text-align: center;*/
    /*    font-size: 18px;*/
    /*}*/
    .wscbj{float: right; margin-right: 20px;border-radius: 3px;margin-top: 14px;padding-left:10px}



    .file-caption {
        max-width: 200px !important;
        flex: 1;
        box-sizing: border-box;

    }
    .file-caption-main{
        display: flex;
    }
    .file-caption-name {
        text-overflow: ellipsis;
        overflow: hidden;
        word-break: break-all;
        white-space: nowrap;
        width: 100%;
        box-sizing: border-box;

    }
    .file-input{
        width: 705px;
        box-sizing: border-box;

    }
    .btn-danger{
        color: #fff!important;
    }
    #wsc{
        /*display: flex;*/
        height: 30px;
        line-height: 30px;
        float: left;
    }
    #wsc>p{
        display: inline-block;
        margin: 0;
        float: left;
    }
    #fileSpan{
        text-overflow: ellipsis;
        overflow: hidden;
        word-break: break-all;
        white-space: nowrap;
        width: 200px;
        display: inline-block;
        padding: 0 5px;
        box-sizing: border-box;
        height: 30px;
        line-height: 30px;
        float: left;
    }
    #reButton{
        margin-top: 3px;
        float: left;
    }
    .fileParent{
        display: flex;
        justify-content: end;
        justify-content: space-between;
    }
    select,input,textarea{
        border:1px solid rgba(0,0,0,0.2);
        border-radius: 5px;
        color: rgba(0,0,0,0.7)!important;
    }
    textarea{
        padding: 6px;
        box-sizing: border-box;
    }

</style>
</html>
