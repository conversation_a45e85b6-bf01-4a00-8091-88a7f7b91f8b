<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
    <title>案卷复核</title>
</head>
<link rel="stylesheet" href="${webpath }/static/css/caiji.css">
<body>
<div class="center_home_weizhi">当前位置：案卷复核  </div>
<div class="center-body">
    <div class="center_list" style="margin-top: 30px">
    <div class="panel-group" id="accordion">
        <form id= "searchForm"  role="form">
            <select class="form-control" style="width:150px;margin-right:5px;" id="selectAreaCode" name="selectAreaCode">
                <option value="" >所属省</option>
                <c:forEach items="${provinceList}" var="item" varStatus="status">
                    <option value="${item.areaCode}" <c:if test="${item.areaCode == selectAreaCode}">selected </c:if>>
                            ${item.province}
                    </option>
                </c:forEach>
            </select>
            <select class="form-control" style="width:150px;margin-right:5px;" id="isUploadFile" name="isUploadFile">
                <option value="" >省厅盖章文件</option>
                <option value="1" <c:if test="${isUploadFile=='1' }">selected </c:if>>已上传</option>
                <option value="2" <c:if test="${isUploadFile=='2' }">selected </c:if>>未上传</option>
            </select>
            <!---搜索--->
            <div style="width:260px;" class="btn-group">
                <div class="row">
                    <div class="col-lg-6">
                        <div class="input-group">
                            <span class="input-group-btn">
	                              <button id="searchButt" class="btn btn-success" type="button"> 快速搜索 </button>
	                             <button type="button" class="btn btn-primary" id ="viewExcel" >导出EXCEL</button>
	                           </span>

                        </div><!-- /input-group -->
                    </div><!-- /.col-lg-6 -->
                </div><!-- /.row -->
            </div>

        </form>



    </div>

    <div class="scroll-container">
        <button class="btn btn-primary" id="downloadFile">下载盖章文件</button>
        <table class="table table-bordered  " style="margin-top: 20px">
            <thead>
            <tr>
                <td width="60px"><input id="checkAll" type="checkbox"></td>
                <td height="30" bgcolor="#efefef">序号</td>
                <td  bgcolor="#efefef">省</td>
                <td  bgcolor="#efefef">否决案卷数</td>
                <td  bgcolor="#efefef">申请复核案卷数</td>
                <td  bgcolor="#efefef">省厅盖章文件</td>
                <td  bgcolor="#efefef">操作</td>
            </tr>
            </thead>
            <tbody>
            <c:forEach items="${dataList}" var="item" varStatus="status">
                <tr>
                    <input id="lawFileName${status.index+1}" value="${item.lawFileName}" type="hidden">
                    <input id="lawFileUrl${status.index+1}" value="${item.lawFileUrl}" type="hidden">
                    <td align="center"><input id="js-checkbox" type="checkbox" class="checkSingle"
                                              <c:if test="${item.lawFileName == null}">disabled</c:if>></td>
                    <td align="center">${status.index+1 }</td>
                    <td id="province${status.index+1}">${item.province}</td>
                    <td>${item.entityFileCount}</td>
                    <td>${item.fileRecheckCount}</td>
                    <td>
                        <c:if test="${item.lawFileName != null}">已上传</c:if>
                        <c:if test="${item.lawFileName == null}">未上传</c:if>
                    </td>


                    <td>
                        <button class="btn btn-success btn-xs" onclick="goApplyListPage(${item.areaCode})">查看</button>
                    </td>
                </tr>
            </c:forEach>


            </tbody>
        </table>


    </div>


</div>
</div>

</body>
<script>
    //跳转 申请复核页面. 1申请,2修改,3查看
    function goApplyListPage(areaCode) {
        console.log("地区code",areaCode)
        macroMgr.onLevelOneMenuClick(null, 'filesRecheck/provinceRecheckList.do?areaCode='+areaCode)

    }
    function xiaZaiAnJuan(id){
        //var fileCode = $("#fileCode").html();
        var fileid  = id;
        $.ajax({
            type:"post",
            url:WEBPATH+'/jcpf/jcpfExistFileUrl.do',
            data:{fileId:fileid },           //注意数据用{}
            success:function(data){  //成功
                if("yes" == data){
                    window.location.href="${pageContext.request.contextPath}/jcpf/jcpfAnJuandown.do?fileid="+fileid;
                    return false;
                }else if("no" == data){
                    swal( "操作失败","该案卷不存在!", "error");
                    return false;
                }else if("suffixerror" ==data){
                    swal( "操作失败","该案卷上传数据格式有问题!", "error");
                    return false;
                }
            }
        });
    };

    // 搜索条件
    $(document).ready(function(){
        //监听enter
        business.listenEnter("searchButt");
        $("#searchButt").click(function(){
            // business.addMainContentParserHtml("anjuanList.do?pageIndex=3",$("#searchForm").serialize());
            business.addMainContaierHtml(WEBPATH + '/filesRecheck/declareRecheckList.do', $("#searchForm").serialize());
        });

        $("#isConsiderSelect").change(function(){
            business.addMainContentParserHtml("anjuanList.do?pageIndex=3",$("#searchForm").serialize());
        });
        // //导出Excel
        // $("#viewExcel").click(function(){
        //     var areaType = $("#areaType").val();
        //     var indexId = $("#indexId").val();
        //     var fileCode = $("#fileCode").val();
        //     var path = WEBPATH+"/entityVetoExportExcel.do?target=1&viewType=3&areaType="+areaType+"&indexId="+indexId+"&fileCode="+fileCode;
        //     window.location.href=path;
        //
        // });
    });


    // 处理筛选按钮点击事件
    function filterButton() {
        business.addMainContaierHtml(WEBPATH + '/filesRecheck/declareRecheckList.do', $("#searchForm").serialize());
    }

    // 处理重置按钮点击事件
    function resetButton() {
        // 清空输入框和下拉框内容
        //案卷名称
        document.getElementById('fileName').value = "";
        //案卷文书号
        document.getElementById('fileCode').value = "";
        //案卷类型
        document.getElementById('fileType').value = "";
        //复核状态
        document.getElementById('recheckType').value = "";

        business.addMainContaierHtml(WEBPATH + '/filesRecheck/declareRecheckList.do', $("#searchForm").serialize());
    }


    //导出Excel
    $("#viewExcel").click(function(){
        //导出所有
        var path = WEBPATH+"/filesRecheck/filesExportExcel.do";
        window.location.href=path;

    });
    $(document).ready(function(){
        var checkAll = document.getElementById('checkAll');
        var checkSingle = document.getElementsByClassName('checkSingle');
        var downloadFile = document.getElementById('downloadFile');
        //全选
        checkAll.onchange = function () {
            for (var i = 0; i < checkSingle.length; i++) {
                if(!checkSingle[i].disabled){
                    checkSingle[i].checked = this.checked;
                }

            }
        }
        //单选
        for (var i = 0; i < checkSingle.length; i++) {
            checkSingle[i].onchange = function () {
                var checkedCount = 0;
                var selectedFiles = []; // 存储选中的文件
                for (var j = 0; j < checkSingle.length; j++) {
                    if (checkSingle[j].checked) {
                        checkedCount++;
                        var id = j+1
                        var lawFileName = document.getElementById('lawFileName'+id).value;
                        var lawFileUrl = document.getElementById('lawFileUrl'+id).value;
                        var selectedFileRow = checkSingle[j].parentNode.parentNode;

                        var selectedFile = {
                            fileName: lawFileName,
                            fileUrl: lawFileUrl,
                            province: selectedFileRow.cells[2].innerText
                        };
                        selectedFiles.push(selectedFile);
                    }
                }
                // checkAll.checked = checkedCount === checkSingle.length;
                console.log(selectedFiles); // 打印选中的文件信息
            }
        }
        //下载盖章文件
        downloadFile.onclick = function () {
            var selectedFiles = []; // 存储选中的文件
            for (var i = 0; i < checkSingle.length; i++) {
                if (checkSingle[i].checked) {
                    // 获取选中的文件信息
                    var id = i+1
                    var lawFileName = document.getElementById('lawFileName'+id).value;
                    var lawFileUrl = document.getElementById('lawFileUrl'+id).value;
                    var selectedFileRow = checkSingle[i].parentNode.parentNode;
                    var selectedFile = {
                        fileName: lawFileName,
                        fileUrl: lawFileUrl,
                        province: selectedFileRow.cells[2].innerText
                    };
                    selectedFiles.push(selectedFile);
                }
            }
            console.log(selectedFiles)
            fileDownload(selectedFiles)
        }

    })

    function fileDownload(fileList){
        console.log("---调用下载接口---",fileList)
        if(fileList.length<1){
            swal( "请勾选需要下载的省!","", "error");
        }
        for (var i = 0; i < fileList.length; i++){
            var fileListElement = fileList[i];
            var fileName = fileListElement.fileName;
            var fileUrl = fileListElement.fileUrl;
            var province = fileListElement.province;
            var newName = province + "_" + fileName;
            var url = "${webpath}/filedownload.do?url=" + fileUrl + "&fileName=" + newName
            downloadFile(url,newName)
        }

    }

    function downloadFile(fileUrl, fileName) {
        return new Promise(function(resolve, reject) {
            // 创建 XMLHttpRequest 对象
            var xhr = new XMLHttpRequest();
            // 设置响应类型为二进制文件
            xhr.responseType = 'blob';
            // 监听 load 事件，将响应类型为 blob 的响应体转换为 Blob URL
            xhr.addEventListener('load', function(event) {
                var blobUrl = URL.createObjectURL(xhr.response);
                // 创建 a 标签，模拟下载链接的点击事件
                var link = document.createElement("a");
                link.href = blobUrl;
                link.download = fileName;
                link.click();
                resolve();
            });
            // 捕获 error 事件，显示下载失败
            xhr.addEventListener('error', function(event) {
                console.error('Error:', xhr.statusText);
                reject();
            });
            // 打开连接并发送请求
            xhr.open('GET', fileUrl, true);
            xhr.send();
        });
    }




</script>

<style>
    .center-body {
        width: 100%;
        margin: 10px;
        overflow: hidden;
    }
    .head-item {
        width: 1200px;
        height: 45px;
        margin-top: 15px;
    }

    .head-item>ul>li {
        float: left;
    }

    .head-item>ul>li>a {
        display: block;
        margin-right: 15px;
        width: 158px;
        height: 45px;
        background: #3f91c2;
        text-align: center;
        line-height: 45px;
        box-shadow: 0 0 5px #999;
        overflow: hidden;
        border-radius: 8px;
        color: #ffffff;
        font-size: 16px;
    }
    .ul-clearfix {
        *zoom: 1;
        margin-left: 10px;
    }
    .scroll-container{
        height: 600px;
        width: 100%;/* 设置容器的高度 */
        overflow: auto; /* 自动显示滚动条 */
    }
    /*table {*/
    /*    border-collapse: collapse;*/
    /*    width: 100%;*/
    /*}*/
    /*.table>thead>tr>td{*/
    /*    font-size: 18px;*/
    /*    font-weight: bold;*/
    /*}*/
    /*th, td {*/
    /*    border: 1px solid black;*/
    /*    padding: 8px;*/
    /*    text-align: center;*/
    /*    font-size: 18px;*/
    /*}*/

</style>
</html>
