<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
    <title>案卷复核</title>
</head>
<link rel="stylesheet" href="${webpath }/static/css/caiji.css">
<body>

<div class="center_home_weizhi">当前位置：案卷复核</div>
<div class="center-body">

    <legend class="font-16" style="font-weight:bold;color:#23b7e5; margin-top:50px;">|案卷基本信息</legend>

    <div class="form-group">
        <div class="col-lg-4 col-sm-3 col-xs-12" style="margin-top:7px;">
            所属行政区:
            <span>1111</span>
        </div>
        <div class="col-lg-4 col-sm-3 col-xs-12" style="margin-top:7px;">
            案卷文书号: <span>1111</span>
        </div>
        <div class="col-lg-4 col-sm-3 col-xs-12" style="margin-top:7px;">
            违法案件类型: <span>1111</span>
        </div>
    </div>


    <div class="form-group">
        <div class="col-lg-4 col-sm-3 col-xs-12" style="margin-top:7px;">
            罚款金额:
            <span>1111</span>
        </div>
        <div class="col-lg-4 col-sm-3 col-xs-12" style="margin-top:7px;">
            案件来源: <span>1111</span>
        </div>
    </div>

    <div class="form-group">
        <div class="col-lg-12 col-sm-3 col-xs-12" style="margin-top:7px;">
            案情简介:
            <span>1111</span>
        </div>
    </div>


    <legend class="font-16" style="font-weight:bold;color:#23b7e5; margin-top:50px;">|评查信息</legend>

    <table class="table table-bordered" id="myTable">
        <thead>
        <tr>
            <td height="30" bgcolor="#efefef">序号</td>
            <td bgcolor="#efefef">评查项目</td>
            <td bgcolor="#efefef">表现形式</td>
            <td colspan="2" bgcolor="#efefef">第一轮评查</td>
            <td bgcolor="#efefef">合议评查</td>
        </tr>
        </thead>
        <tbody>
        <%--<c:forEach items="${dataList.list}" var="item" varStatus="status">--%>
        <%--<tr>--%>
        <%--<div><input id ="id"  name = "id" type="hidden" value="${item.id}"></div>--%>
        <%--<td align="center">${status.index+1 }</td>--%>
        <%--<td>${item.province}</td>--%>
        <%--<td>${item.city}</td>--%>
        <%--<td>${item.province}</td>--%>
        <%--<td>${item.city}</td>--%>
        <%--</tr>--%>
        <%--</c:forEach>--%>
        <tr>
            <td rowspan="2">序号</td>
            <td rowspan="2">Row 1</td>
            <td rowspan="2">Row 2</td>
            <td rowspan="2">Row 3</td>
            <td>Row 4</td>
            <td>Row 5</td>
        </tr>
        <tr>
            <td>Row 6</td>
            <td>Row 7</td>
        </tr>
        </tbody>
    </table>

    <!--列表翻页 开始-->
    <div class="page">
        <span style="padding:12px; float:left; color:#0099cc;">共${ajMamageList.total}条记录</span>
        <ul class="pagination" id="pageCon">

        </ul>
    </div>
    <!--列表翻页 结束-->

    <legend class="font-16" style="font-weight:bold;color:#23b7e5; margin-top:50px;">|地方复核申请</legend>

    <table class="table table-bordered" id="myTable2">
        <thead>
        <tr>
            <td height="30" bgcolor="#efefef">序号</td>
            <td bgcolor="#efefef">评查项</td>
            <td bgcolor="#efefef">评查细项</td>
            <td bgcolor="#efefef">复核意见</td>
            <td bgcolor="#efefef">回复意见</td>
        </tr>
        </thead>
        <tbody>
        <%--<c:forEach items="${dataList.list}" var="item" varStatus="status">--%>
        <%--<tr>--%>
        <%--<div><input id ="id"  name = "id" type="hidden" value="${item.id}"></div>--%>
        <%--<td align="center">${status.index+1 }</td>--%>
        <%--<td>${item.province}</td>--%>
        <%--<td>${item.city}</td>--%>
        <%--<td>${item.province}</td>--%>
        <%--<td>${item.city}</td>--%>
        <%--</tr>--%>
        <%--</c:forEach>--%>
        <tr>
            <td>序号</td>
            <td>Row 1</td>
            <td>Row 2</td>
            <td>Row 3</td>
            <td><input type="text" class="remark-input"></td>
        </tr>

        </tbody>
    </table>


    <legend class="font-16" style="font-weight:bold;color:#23b7e5; margin-top:50px;">|最终复核结果</legend>
    <form>
        <div>
            <span>是否扣分：</span>
            <input type="radio" name="deduct" value="是"> 是
            <input type="radio" name="deduct" value="否"> 否
        </div>

        <div> <span>情形：</span>
            <input type="checkbox" name="situation" value="a"> a
            <input type="checkbox" name="situation" value="b"> b
            <input type="checkbox" name="situation" value="c"> c
            <input type="checkbox" name="situation" value="d"> d
        </div>

        <div><span>意见：</span>
            <textarea id="finalOpinion" rows="4" cols="50" style="margin-left: 20px;"></textarea>
        </div>
        <div> <span> 争议点：</span>
            <textarea id="issues" rows="4" cols="50" style="margin-left: 20px;"></textarea>
        </div>

        <button type="button" onclick="submitForm()">提交</button>
    </form>

</div>

<div id="myModal" style="height: 250px!important; display: none">
    <div style="display: flex; align-items: center;">
        <p style="color: red; margin-right: 5px; margin-left: 15px">*</p>
        <div style="margin-right: 5px;">评查项</div>
        <select class="col-lg-2" name="taskStateCode">
            <option value="">请选择</option>
            <option value="1">已结案</option>
            <option value="2">未结案</option>
        </select>

        <p style="color: red; margin-right: 5px; margin-left: 15px">*</p>
        <div style="margin-right: 5px;">评查细项</div>
        <select class="col-lg-2" name="taskStateCode">
            <option value="">请选择</option>
            <option value="1">已结案</option>
            <option value="2">未结案</option>
        </select>

        <p style="color: red; margin-right: 5px; margin-left: 15px">*</p>
        <div style="margin-right: 5px;">复核意见</div>
        <input id="mySerious" type="text" class="col-lg-2" style="width: 80%" value=""/>

        <div class="col-lg-2">
            <button onclick="delApplyItem(${item.id})" style="margin: 0 10px" class="btn btn-info btn-xs">删除</button>
        </div>
    </div>

</div>


</body>
<script>
    function submitForm() {
        var table = document.getElementById("myTable2"); // 获取表格元素
        var rows = table.rows; // 获取所有行

        var tableValues = [];

        // 遍历行（从索引为1的行开始，跳过表头）
        for (var i = 1; i < rows.length; i++) {
            var cells = rows[i].cells; // 获取当前行的所有单元格

            var rowValues = [];

            // 遍历单元格
            for (var j = 0; j < cells.length; j++) {
                var cellValue;

                if (j === cells.length - 1) {
                    // 如果是最后一列（备注列），获取输入框的值
                    cellValue = cells[j].querySelector("input").value;
                } else {
                    // 否则，获取单元格的文本值
                    cellValue = cells[j].innerText;
                }

                rowValues.push(cellValue); // 将值添加到行值列表
            }

            tableValues.push(rowValues); // 将行值列表添加到表格值列表
        }
        console.log(tableValues); // 打印表格值列表


            // 获取是否扣分选项的值
        var isDeducted = document.querySelector('input[name="deduct"]:checked').value;

        // 获取情形选项的值
        var situation = [];
        var checkboxes = document.querySelectorAll('input[name="situation"]:checked');
        for (var i = 0; i < checkboxes.length; i++) {
            situation.push(checkboxes[i].value);
        }

        // 获取意见输入框的值
        var opinion = document.getElementById('finalOpinion').value;

        // 在这里可以将获取到的值进行处理或提交到后端

        // 示例：将获取到的值输出到控制台
        console.log("是否扣分: " + isDeducted);
        console.log("情形: " + situation.join(", "));
        console.log("意见: " + opinion);
    }


    var table = document.getElementById("myTable");

    // 获取第一行的所有单元格
    var cells = table.rows[0].cells;
    // 合并第一列的单元格
    cells[0].rowSpan = 2;
    cells[1].rowSpan = 2;
    cells[2].rowSpan = 2;

    //获取地方复核申请记录。
    $.ajax({
        type: "post",
        url: WEBPATH + '/jcpf/jcpfExistFileUrl.do',
        data: {fileId: 1},           //注意数据用{}
        success: function (data) {  //成功


        }
    });

    var tableBody = document.getElementById("myTable2").getElementsByTagName('tbody')[0];


    function cancelApplyItem() {
        var modal = document.getElementById("myModal");
        modal.style.display = "none";

    }

    function delApplyItem(id) {

    }

    function xiaZaiAnJuan(id) {
        //var fileCode = $("#fileCode").html();
        var fileid = id;
        $.ajax({
            type: "post",
            url: WEBPATH + '/jcpf/jcpfExistFileUrl.do',
            data: {fileId: fileid},           //注意数据用{}
            success: function (data) {  //成功
                if ("yes" == data) {
                    window.location.href = "${pageContext.request.contextPath}/jcpf/jcpfAnJuandown.do?fileid=" + fileid;
                    return false;
                } else if ("no" == data) {
                    swal("操作失败", "该案卷不存在!", "error");
                    return false;
                } else if ("suffixerror" == data) {
                    swal("操作失败", "该案卷上传数据格式有问题!", "error");
                    return false;
                }
            }
        });
    };

    //分页
    $(document).ready(function () {
        var curentPage = eval('${ajMamageList.pageNum}');
        var totalPage = eval('${ajMamageList.pages}');

        if (totalPage > 0) {
            var options = {
                bootstrapMajorVersion: 3,
                currentPage: curentPage,
                totalPages: totalPage,
                numberOfPages: 5,
                itemTexts: function (type, page, current) {
                    switch (type) {
                        case "first":
                            return "首页";
                        case "prev":
                            return "&laquo;";
                        case "next":
                            return "&raquo;";
                        case "last":
                            return "尾页";
                        case "page":
                            return page;
                    }
                },
                onPageClicked: function (event, originalEvent, type, page) {
                    business.addMainContentParserHtml(WEBPATH + '/ajpf/ajpf_jiti_shi_list.do?areaType=' + areaType + '&pageNum=' + page, null);
                }
            };
            $('#pageCon').bootstrapPaginator(options);
        }
    });


    // 处理导出按钮点击事件
    function handleExport() {
        // 导出表格数据的逻辑
        $.ajax({
            type: "GET",
            url: WEBPATH + "/saveFilesBean.do",
            // data:{activityName:activityName},
            async: false,
            success: function (data) {
                if (data.result == "error") {
                    swal({title: data.message, text: "", type: "error"});
                    return false;
                } else if (data.result == "success") {
                    swal({title: data.message, text: "", type: "success"});
                    // 在这里执行抽取案卷的相关操作

                }
            }
        });

    }


</script>

<style>
    .center-body {
        width: 100%;
        height: 100%;
        padding: 10px;
        overflow: auto;
    }

    .head-item {
        width: 1200px;
        height: 45px;
        margin-top: 15px;
    }

    .head-item > ul > li {
        float: left;
    }

    .head-item > ul > li > a {
        display: block;
        margin-right: 15px;
        width: 158px;
        height: 45px;
        background: #3f91c2;
        text-align: center;
        line-height: 45px;
        box-shadow: 0 0 5px #999;
        overflow: hidden;
        border-radius: 8px;
        color: #ffffff;
        font-size: 16px;
    }

    .ul-clearfix {
        *zoom: 1;
        margin-left: 10px;
    }

    .scroll-container {
        height: 600px;
        width: 100%; /* 设置容器的高度 */
        overflow: auto; /* 自动显示滚动条 */
    }

    /*table {*/
    /*    border-collapse: collapse;*/
    /*    width: 100%;*/
    /*}*/
    /*.table>thead>tr>td{*/
    /*    font-size: 18px;*/
    /*    font-weight: bold;*/
    /*}*/
    /*th, td {*/
    /*    border: 1px solid black;*/
    /*    padding: 8px;*/
    /*    text-align: center;*/
    /*    font-size: 18px;*/
    /*}*/

</style>
</html>
