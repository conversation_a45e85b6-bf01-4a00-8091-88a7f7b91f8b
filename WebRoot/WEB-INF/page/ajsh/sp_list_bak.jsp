<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
    <title>案卷复核</title>
</head>
<link rel="stylesheet" href="${webpath }/static/css/caiji.css">
<body>
<div class="center_home_weizhi">当前位置：案卷复核列表  </div>
<div class="center-body">
    <div class="panel-group" id="accordion">
        <form id= "searchForm"  role="form">

                <div style="margin-top: 50px">
                    <label for="province">行政区：</label>
                    <input type="text" id="province">
                    <label for="caseName">案卷名称：</label>
                    <input type="text" id="caseName">
                    <label for="caseNumber">案卷编号：</label>
                    <input type="text" id="caseNumber">
                    <label for="fileCode">文书号：</label>
                    <input type="text" id="fileCode">
                </div>

            <label class="control-label col-lg-1">案卷状态</label>
            <div class="col-lg-2">
                <select class="form-control"  name="taskStateCode">
                    <option value="">请选择</option>
                    <option value="1">已结案</option>
                    <option value="2">未结案</option>
                </select>
            </div>

            <label class="control-label col-lg-1">案卷类型</label>
            <div class="col-lg-2">
                <select id ="fileTypeSelect"class="form-control" name ="fileType">
                    <!-- 0代表 行政处罚案卷 ；1代表按日计罚案卷； 2代表移送行政拘留案卷；3代表涉嫌犯罪移送案卷；4代表申请法院强制执行案卷；5代表发现问题的污染源现场监督检查稽查案卷   -->
                    <option value = ""  >请选择案卷类型</option>
                    <option value="0" <c:if test="${fileType=='0' }">selected</c:if>>行政处罚案卷</option>
                    <option value="1" <c:if test="${fileType=='1' }">selected</c:if>>按日计罚案卷</option>
                    <option value="6" <c:if test="${fileType=='6' }">selected</c:if>>查封扣押案卷</option>
                    <option value="7" <c:if test="${fileType=='7' }">selected</c:if>>限产停产案卷</option>
                    <option value="2" <c:if test="${fileType=='2' }">selected</c:if>>移送行政拘留案卷</option>
                    <option value="3" <c:if test="${fileType=='3' }">selected</c:if>>涉嫌犯罪移送案卷</option>
                    <option value="5" <c:if test="${fileType=='5' }">selected</c:if>>污染源现场监督检查稽查案卷</option>
                </select>
            </div>

            <label class="control-label col-lg-1">案卷归属</label>
            <div class="col-lg-2">
                <select class="form-control"  name="taskStateCode">
                    <option value="">请选择</option>
                    <option value="1">已结案</option>
                    <option value="2">未结案</option>
                </select>
            </div>
        </form>
        <div>
            <button id="filterButton">筛选</button>
            <button id="resetButton">重置</button>
            <button id="exportButton">导出</button>
        </div>

    </div>

    <div class="scroll-container">
        <table class="table table-bordered">
            <thead>
            <tr>
                <td rowspan="2" height="30" bgcolor="#efefef">序号</td>
                <td colspan="3" bgcolor="#efefef">行政区</td>
                <td rowspan="2" bgcolor="#efefef">案卷名称</td>
                <td rowspan="2" bgcolor="#efefef">文书号</td>
                <td rowspan="2" bgcolor="#efefef">案卷类型</td>
                <td rowspan="2" bgcolor="#efefef">分数</td>
                <td rowspan="2" bgcolor="#efefef">复核状态</td>
                <td rowspan="2" bgcolor="#efefef">申请复核时间</td>
                <td rowspan="2" bgcolor="#efefef">是否扣分</td>
                <td rowspan="2" bgcolor="#efefef">操作</td>
            </tr>
            <tr>
                <td height="30" bgcolor="#efefef">省</td>
                <td bgcolor="#efefef">市</td>
                <td bgcolor="#efefef">县</td>
                <
            </tr>
            </thead>
            <tbody>
            <%--<c:forEach items="${dataList.list}" var="item" varStatus="status">--%>
                <%--<tr>--%>
                    <%--<div><input id ="id"  name = "id" type="hidden" value="${item.id}"></div>--%>
                    <%--<td align="center">${status.index+1 }</td>--%>
                    <%--<td>${item.province}</td>--%>
                    <%--<td>${item.city}</td>--%>
                    <%--<td>${item.county}</td>--%>
                    <%--<td>${item.fileName}</td>--%>
                    <%--<td>${item.fileCode}</td>--%>
                    <%--<td>${item.fileTypeName}</td>--%>

                    <%--<td>${item.fileTypeName}</td>--%>
                    <%--<td>--%>
                        <%--<c:if test="${item.filetypePro == 0}">未申请</c:if>--%>
                        <%--<c:if test="${item.filetypePro == 1}">待合议</c:if>--%>
                        <%--<c:if test="${item.filetypePro == 2}">已完成</c:if>--%>
                    <%--</td>--%>
                    <%--<td>${item.fileTypeName}</td>--%>
                    <%--<td>${item.fileTypeName}</td>--%>
                    <%--<td>--%>
                        <%--<c:if test="${ item.fileTypeName ==0 }">--%>
                            <%--<button class="btn btn-success btn-xs" onclick="xiaZaiAnJuan(${item.id})">修改复核</button>--%>
                        <%--</c:if>--%>

                        <%--<c:if test="${ item.fileTypeName ==1 }">--%>
                            <%--<button class="btn btn-success btn-xs" onclick="xiaZaiAnJuan(${item.id})">查看</button>--%>
                        <%--</c:if>--%>

                    <%--</td>--%>
                <%--</tr>--%>
            <%--</c:forEach>--%>
                <tr>
                    <div><input id ="id"  name = "id" type="hidden" value="${item.id}"></div>
                    <td align="center">1</td>
                    <td>1</td>
                    <td>2</td>
                    <td>3</td>
                    <td>4</td>
                    <td>5</td>
                    <td>6</td>

                    <td>7</td>
                    <td>
                     8
                    </td>
                    <td>9</td>
                    <td>10</td>

                    <td>
                            <button class="btn btn-success btn-xs" onclick="goApplyPage(${item.id})">修改复核</button>

                    </td>
                </tr>

            </tbody>
        </table>
        <!--列表翻页 开始-->
        <div class="page">
            <span style="padding:12px; float:left; color:#0099cc;">共${ajMamageList.total}条记录</span>
            <ul class="pagination" id="pageCon">

            </ul>
        </div>
        <!--列表翻页 结束-->
    </div>


</div>


</body>
<script>
function goApplyPage() {
    macroMgr.onLevelOneMenuClick(null, 'filesRecheck/approvalApplyPage.do?fileid='+1)

}
    function xiaZaiAnJuan(id){
        //var fileCode = $("#fileCode").html();
        var fileid  = id;
        $.ajax({
            type:"post",
            url:WEBPATH+'/jcpf/jcpfExistFileUrl.do',
            data:{fileId:fileid },           //注意数据用{}
            success:function(data){  //成功
                if("yes" == data){
                    window.location.href="${pageContext.request.contextPath}/jcpf/jcpfAnJuandown.do?fileid="+fileid;
                    return false;
                }else if("no" == data){
                    swal( "操作失败","该案卷不存在!", "error");
                    return false;
                }else if("suffixerror" ==data){
                    swal( "操作失败","该案卷上传数据格式有问题!", "error");
                    return false;
                }
            }
        });
    };

    //分页
    $(document).ready(function(){
        var curentPage = eval('${ajMamageList.pageNum}');
        var totalPage = eval('${ajMamageList.pages}');

        if(totalPage>0){
            var options = {
                bootstrapMajorVersion: 3,
                currentPage: curentPage,
                totalPages: totalPage,
                numberOfPages: 5,
                itemTexts: function (type, page, current) {
                    switch (type) {
                        case "first":
                            return "首页";
                        case "prev":
                            return "&laquo;";
                        case "next":
                            return "&raquo;";
                        case "last":
                            return "尾页";
                        case "page":
                            return page;
                    }
                },
                onPageClicked: function (event, originalEvent, type, page) {
                    business.addMainContentParserHtml(WEBPATH+'/ajpf/ajpf_jiti_shi_list.do?areaType='+areaType+'&pageNum='+page,null);
                }
            };
            $('#pageCon').bootstrapPaginator(options);
        }
    });



    // 获取DOM元素
    var filterButton = document.getElementById('filterButton');
    var resetButton = document.getElementById('resetButton');
    var exportButton = document.getElementById('exportButton');
    var dataTable = document.getElementById('dataTable');
    var pagination = document.getElementById('pagination');

    // 添加按钮点击事件监听器
    filterButton.addEventListener('click', handleFilter);
    resetButton.addEventListener('click', handleReset);
    exportButton.addEventListener('click', handleExport);

    // 处理筛选按钮点击事件
    function handleFilter() {
        // 获取用户输入的筛选条件
        var province = document.getElementById('province').value;
        var caseName = document.getElementById('caseName').value;
        var caseNumber = document.getElementById('caseNumber').value;
        var documentNumber = document.getElementById('documentNumber').value;
        var caseType = document.getElementById('caseType').value;
        var caseStatus = document.getElementById('caseStatus').value;

        // 根据筛选条件发送请求到服务器获取数据，并动态生成表格内容



        // 清空表格
        dataTable.innerHTML = '';



        // 生成分页器
        pagination.innerHTML = '分页器的HTML代码';
    }

    // 处理重置按钮点击事件
    function handleReset() {
        // 清空输入框和下拉框内容
        document.getElementById('province').value = '';
        document.getElementById('caseName').value = '';
        document.getElementById('caseNumber').value = '';
        document.getElementById('documentNumber').value = '';
        document.getElementById('caseType').value = 'option1';
        document.getElementById('caseStatus').value = 'option1';

        // 清空表格和分页器
        dataTable.innerHTML = '';
        pagination.innerHTML = '';
    }

    // 处理导出按钮点击事件
    function handleExport() {
        // 导出表格数据的逻辑
        $.ajax({
            type: "GET",
            url: WEBPATH+"/saveFilesBean.do",
            // data:{activityName:activityName},
            async:false,
            success: function(data){
                if(data.result=="error"){
                    swal({title: data.message ,text: "",type:"error"});
                    return false;
                }else if(data.result=="success"){
                    swal({title: data.message ,text: "",type:"success"});
                    // 在这里执行抽取案卷的相关操作

                }
            }
        });

    }


</script>

<style>
    .center-body {
        width: 100%;
        margin: 10px;
        overflow: hidden;
    }
    .head-item {
        width: 1200px;
        height: 45px;
        margin-top: 15px;
    }

    .head-item>ul>li {
        float: left;
    }

    .head-item>ul>li>a {
        display: block;
        margin-right: 15px;
        width: 158px;
        height: 45px;
        background: #3f91c2;
        text-align: center;
        line-height: 45px;
        box-shadow: 0 0 5px #999;
        overflow: hidden;
        border-radius: 8px;
        color: #ffffff;
        font-size: 16px;
    }
    .ul-clearfix {
        *zoom: 1;
        margin-left: 10px;
    }
    .scroll-container{
        height: 600px;
        width: 100%;/* 设置容器的高度 */
        overflow: auto; /* 自动显示滚动条 */
    }
    /*table {*/
    /*    border-collapse: collapse;*/
    /*    width: 100%;*/
    /*}*/
    /*.table>thead>tr>td{*/
    /*    font-size: 18px;*/
    /*    font-weight: bold;*/
    /*}*/
    /*th, td {*/
    /*    border: 1px solid black;*/
    /*    padding: 8px;*/
    /*    text-align: center;*/
    /*    font-size: 18px;*/
    /*}*/

</style>
</html>
