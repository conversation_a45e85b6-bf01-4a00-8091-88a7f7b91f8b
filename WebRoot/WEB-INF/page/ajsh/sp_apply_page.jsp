<%@ page import="com.changneng.sa.bean.FilesRecheckItem" %>
<%@ page import="java.util.List" %>
<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
    <title>案卷复核</title>
</head>
<link rel="stylesheet" href="${webpath }/static/css/caiji.css">
<div style="width: 100%;padding: 0 2.5%; box-sizing: border-box;" class="auto-apply">
<div class="center_home_weizhi">当前位置：案卷复核  </div>
<div class="main-center-body">
    <div class="center-body">
        <div class="col text-right">
            <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#reviewHelperModal">
                评查助手
            </button>
        </div>

        <legend   style="font-weight:bold;color:#23b7e5; font-size: 18px;">| 案卷基本信息</legend>
        <input id="fileCode" name="fileCode"  type="text" style="display:none;" value="${dataInfo.fileCode}" />
        <input id="filesId" name="filesId"  type="text" style="display:none;" value="${dataInfo.filesId}" />
        <div class="form-group">
            <div class="col-lg-4 col-sm-3 col-xs-12" style="margin-top:7px;">
                <strong>所属行政区:</strong>
                <span>${dataInfo.province}${dataInfo.city}${dataInfo.county}</span>
            </div>
            <div class="col-lg-4 col-sm-3 col-xs-12" style="margin-top:7px;">
                <strong> 案卷文书号: </strong>   <span>${dataInfo.fileCode}</span>
            </div>
            <div class="col-lg-4 col-sm-3 col-xs-12" style="margin-top:7px;">
                <strong>案件类型:</strong>    <span>${dataInfo.fileTypeName}</span>
            </div>
        </div>

        <div class="form-group">
            <div class="col-lg-4 col-sm-3 col-xs-12" style="margin-top:20px;">
                <strong> 罚款金额:</strong>
                <span>${dataInfo.faKuanShuE}</span>
            </div>
            <div class="col-lg-4 col-sm-3 col-xs-12" style="margin-top:20px;">
                <strong> 案件来源: </strong>   <span>${dataInfo.fileSource}</span>
            </div>
        </div>

        <div class="form-group">
            <div class="col-lg-12 col-sm-3 col-xs-12" style="margin-top:20px;">
                <strong> 案情简介:</strong>
                <span>${dataInfo.fileSimpleInfo}</span>
            </div>
        </div>



</div>

<div class="center-body">
    <legend   style="font-weight:bold;color:#23b7e5; margin-top:20px;font-size: 18px;">| 评查信息</legend>

    <table class="table table-bordered" id="myTable">
        <thead>
        <tr>
            <td width="50px"  height="30" bgcolor="#efefef">序号</td>
            <td width="150px" bgcolor="#efefef">评查项目</td>
            <td width="300px" bgcolor="#efefef">表现形式</td>
            <td width="400px" bgcolor="#efefef">第一轮评查</td>
            <td width="400px" bgcolor="#efefef">合议评查</td>
            <td bgcolor="#efefef">智能识别</td>
        </tr>
        </thead>
        <tbody>
        <c:forEach items="${itemList}" var="item" varStatus="status">
            <tr>
                <td rowspan="2" align="center">${status.index+1 }</td>
                <td rowspan="2">${item.indexScoreName}</td>
                <td rowspan="2">${item.itemScoreName}</td>
                <td>【专家A】<br>${item.commeStrA}</td>
                <td>【委员A】<br>${item.commeStrCommitA}</td>
                <td rowspan="2">
                    <c:choose>
                        <c:when test="${not empty item.htmlAiInfo}">
                            ${item.htmlAiInfo}
                        </c:when>
                        <c:otherwise>
                            该项人工评查
                        </c:otherwise>
                    </c:choose>
                </td>
            </tr>
            <tr>
                <td>【专家B】<br>${item.commeStrB}</td>
                <td>【委员B】<br>${item.commeStrCommitB}</td>
            </tr>
        </c:forEach>

        </tbody>
    </table>

</div>

<div class="center-body">
    <legend   style="font-weight:bold;color:#23b7e5; margin-top:50px;font-size: 18px;">| 地方复核申请</legend>

    <dev style="float: left; margin-left: 20px">
        <span style="font-size: 18px">政策依据文件:</span>
        <a href='javascript:void(0)' onclick='downloadFile( "${lawFileUrl}", "${lawFileName}")'> ${lawFileName}</a>
    </dev>
    <table id="myTable2" class="table table-bordered">
        <thead>
        <tr>
            <th id="itemID" bgcolor="#efefef" width="50px">序号</th>
            <th id="indexScoreId" bgcolor="#efefef" width="140px">评查</th>
            <th id="itemScoreId" bgcolor="#efefef" width="25%">评查细项</th>
            <th id="reviewOpinion" bgcolor="#efefef" width="30%">复核意见</th>
            <th id="replyOpinion" bgcolor="#efefef">回复意见</th>
        </tr>
        </thead>
        <tbody>
            <c:forEach items="${recheckItemList}" var="item" varStatus="status">
                <tr>
                    <td align="center" >${status.index+1 }<input id="id" name="id"  style="display:none;" value="${item.id}" /></td>
                    <td>
                        <span>${item.indexScoreName}</span>
                    </td>
                    <td>
                        <span>${item.itemScoreName}</span>
                    </td>
                    <td><span>${item.reviewOpinion}</span></td>
                    <%--type 2审批,3查看只读--%>
                    <td>
                        <c:if test="${type==2}">
                            <input id="isAgree${status.index}" value="${item.isAgree}" type="hidden">
                            是否同意地方申诉意见：
                            <select id="select-isAgree${status.index}" onchange="changeIsAgree(${status.index})" style="width: 100px">
                                <option value="0" ${item.isAgree != 1 ? 'selected' : ''}>同意</option>
                                <option value="1" ${item.isAgree == 1 ? 'selected' : ''}>不同意</option>
                            </select>
                            <br/>

                            <input id="six${status.index}" value="${item.textSix}" type="hidden">
                            <span  id="select-six-label${status.index}" style="<c:if test="${item.isAgree != 1 || item.isAgree ==null }">display: none;</c:if>">该项是否存在问题：</span>

                            <select id="textSix${status.index}" style="width: 100px;<c:if test="${item.isAgree != 1 || item.isAgree ==null }">display: none;</c:if>">
                                <option value="">请选择</option>
                                <option value="存在问题" ${item.textSix == '存在问题' ? 'selected' : ''}>存在问题</option>
                                <option value="不存在问题" ${item.textSix == '不存在问题' ? 'selected' : ''}>不存在问题</option>
                            </select>

                            <div id="disagreeDiv${status.index}" <c:if test="${item.isAgree !=null && item.isAgree != 0}">style="display: none;"</c:if>><%--空值,或者选中0,显示这个div--%>
                                详情说明:
                                <textarea id="replyOpinion${status.index}" class="input1" required style="width: 100%;" rows="4" type="text" placeholder="如同意地方观点，请简述前期专家评查观点及案件具体情形。">${item.replyOpinion}</textarea>
                            </div>

                            <div id="agreeDiv${status.index}" <c:if test="${item.isAgree != 1 || item.isAgree ==null }">style="display: none;"</c:if>><%--没有选中不同意或者空值都不展示这个div--%>
                                该项<input id="textOne${status.index}" value="${item.textOne}" placeholder="认同 xx 专家/委员" type="hidden">
                                <select id="select-textOne${status.index}" style="width: 120px;z-index: 1000;" multiple="multiple" >
                                    <option value="均不认同" ${item.textOne.contains('均不认同') ? 'selected' : ''}>均不认同</option>
                                    <option value="认同专家A" ${item.textOne.contains('认同专家A') ? 'selected' : ''}>认同专家A</option>
                                    <option value="认同专家B" ${item.textOne.contains('认同专家B') ? 'selected' : ''}>认同专家B</option>
                                    <option value="认同委员A" ${item.textOne.contains('认同委员A') ? 'selected' : ''}>认同委员A</option>
                                    <option value="认同委员B" ${item.textOne.contains('认同委员B') ? 'selected' : ''}>认同委员B</option>
                                </select>
                                的观点,
<%--                                该案卷存在<input id="textTwo${status.index}" value="${item.textTwo}" placeholder="具体问题(仅限200字)" class="one-seven-input">问题,--%>
<%--                                详见--%>
<%--                                <input id="textThree${status.index}" value="${item.textThree}" type="hidden">--%>
<%--                                <select id="select-textThree${status.index}" style="width: 120px" multiple="multiple" >--%>
<%--                                    <c:forEach items="${indexOneList}" var="item2">--%>
<%--                                        <option value=${item2} ${item.textThree.contains(item2) ? 'selected' : ''}>${item2}</option>--%>
<%--                                    </c:forEach>--%>

<%--                                </select>--%>
<%--                                文书,--%>
<%--                                <input id="textFour${status.index}" value="${item.textFour}" placeholder="第x页(仅限200字)" class="one-seven-input">页,--%>
<%--                                <input id="textFive${status.index}" value="${item.textFive}" placeholder="(仅限200字)" class="one-seven-input">错误,--%>
<%--                                &lt;%&ndash;应<input id="textSix${status.index}" value="${item.textSix}" placeholder="(仅限200字)" class="one-seven-input">,&ndash;%&gt;因此认定该案卷错误.--%>
                                <br>详情说明:
                                <textarea id="textSeven${status.index}" class="input1" style="width: 100%;" rows="4" placeholder="需对存在的问题进行详细描述,仅限2000字"><c:out value="${item.textSeven}"/></textarea>
                            </div>

                        </c:if>
                        <%--type==3 只读查看--%>
                        <c:if test="${type==3}">
                            <textarea class="input1" style="width: 100%;" rows="5" type="text"  disabled="true"><c:if test="${item.isAgree==1}">是否同意地方申诉意见：不同意。${item.replyOpinion}</c:if><c:if test="${item.isAgree==0}">是否同意地方申诉意见：同意。详情说明:${item.replyOpinion}</c:if></textarea>
                        </c:if>
                    </td>
                </tr>
            </c:forEach>

        </tbody>
    </table>
    <form>
        <legend   style="font-weight:bold;color:#23b7e5; margin-top:50px;font-size: 18px;">| 最终复核结果</legend>
        <div>
            <span ><span style="color: red">*</span>是否否决：</span>
            <input style="margin-left: 12px" type="radio" name="isMinusScore" value="1" onclick="displayCaseInfo(this.value)"
                   <c:if test="${dataInfo.isMinusScore==1}">checked="checked"</c:if>
                   <c:if test="${type==3}">disabled="true"</c:if>> 否决
            <input type="radio" name="isMinusScore" value="0" onclick="displayCaseInfo(this.value)"
                   <c:if test="${dataInfo.isMinusScore==0}">checked="checked"</c:if>
                   <c:if test="${type==3}">disabled="true"</c:if>> 不否决
        </div>

<%--        <div id="caseInfo" style="margin-top: 20px; <c:if test="${type==3 && dataInfo.isMinusScore==0}"> display:none </c:if>"> <span style="text-align: right">情形：</span>--%>
        <div id="caseInfo" style="margin-top: 20px; <c:if test="${ dataInfo.isMinusScore==0 or dataInfo.isMinusScore=='0'}"> display:none </c:if>"> <span style="text-align: right">倒扣分情形：</span>
            <input style="margin-left: 40px;" type="checkbox" name="minusScene" value="1"  <c:if test="${type==3}">disabled="true"</c:if>
            <c:forEach items="${dataInfo.minusScene.split(',')}" var="item">
                   <c:if test="${item==1}">checked="checked"</c:if>
            </c:forEach>
            > 办案人员不具有行政执法资格，或少于两人的
            <input style="margin-left: 60px;" type="checkbox" name="minusScene" value="2" <c:if test="${type==3}">disabled="true"</c:if>
            <c:forEach items="${dataInfo.minusScene.split(',')}" var="item">
                   <c:if test="${item==2}">checked="checked"</c:if>
            </c:forEach>
            > 违法主体不清或认定错误的
            <br>
            <input style="margin-left: 92px;margin-top: 20px;" type="checkbox" name="minusScene" value="3"  <c:if test="${type==3}">disabled="true"</c:if>
            <c:forEach items="${dataInfo.minusScene.split(',')}" var="item">
                   <c:if test="${item==3}">checked="checked"</c:if>
            </c:forEach>
            > 证据取得的方式、手段、途径不符合法定要求的
            <input style="margin-left: 48px;margin-top: 20px;" type="checkbox" name="minusScene" value="4" <c:if test="${type==3}">disabled="true"</c:if>
            <c:forEach items="${dataInfo.minusScene.split(',')}" var="item">
                   <c:if test="${item==4}">checked="checked"</c:if>
            </c:forEach>
            > 作出的决定无法律、法规、规章依据的
            <input style="margin-left: 48px;margin-top: 20px;" type="checkbox" name="minusScene" value="5" <c:if test="${type==3}">disabled="true"</c:if>
            <c:forEach items="${dataInfo.minusScene.split(',')}" var="item">
                   <c:if test="${item==5}">checked="checked"</c:if>
            </c:forEach>
            > 违反法定程序的
        </div>

        <div id="redDiv" style="margin-top: 20px; <c:if test="${dataInfo.isMinusScore==0 or dataInfo.isMinusScore=='0'}">display:none</c:if>">
            <div><span   style="color: red;  ">*</span> 否决理由：</div>
            <textarea id="finalOpinion" rows="4"  style="margin-left: 100px;width: calc(100% - 100px);"
                      <c:if test="${type==3}">disabled="true"</c:if>>${dataInfo.finalOpinion}</textarea>
        </div>
        <div id="modifyOpinionDiv" style="margin-top: 20px; <c:if test="${dataInfo.isMinusScore==1 || dataInfo.isMinusScore==null}"> display:none </c:if>">
            <div><span style="color: red">*</span>改进意见：</div>
            <textarea id="modifyOpinion" rows="4"  style="margin-left: 100px;width: calc(100% - 100px);"
                      <c:if test="${type==3}">disabled="true"</c:if>>${dataInfo.modifyOpinion}</textarea>
        </div>
        <legend   style="font-weight:bold;color:#23b7e5; margin-top:50px; font-size: 18px;">| 争议点</legend>
        <div style="margin-top: 20px;">
            <textarea id="dispute" rows="4" cols="50" style="margin-left: 100px;width: calc(100% - 100px);"
                      <c:if test="${type==3}">disabled="true"</c:if>>${dataInfo.dispute}</textarea>
        </div>

    </form>
</div>
    <div class="center-body" style="">
        <button onclick="returnList()" style="float: left;margin: 0 10px" class="btn btn-default">返回</button>
        <c:if test="${type==2}"><button onclick="submitSave(${type},2)" style="float: right;margin: 0 10px" class="btn btn-info">提交</button></c:if>
        <c:if test="${type==2}"><button onclick="submitSave(${type},3)" style="float: right;margin: 0 10px" class="btn btn-info">暂存</button></c:if>
    </div>
    <!-- 模态框 -->
    <div class="modal fade" id="reviewHelperModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document" style="width: 70%">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">评查助手</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="col-lg-12" style="padding-left: 0px;padding-right: 0px">
                        <div class="form-group">
<%--                            <label class="control-label col-md-3"  style="padding-left: 0px;padding-right: 0px">行政区划：</label>--%>
                            <div class="col-md-4" style="padding-left: 0px;padding-right: 0px">
                                <input class="form-control" type="text" id="cityInput" name="cityInput" placeholder="行政区划模糊查询">
                            </div>

                        </div>
                        <div class="col-lg-4" style="padding-left: 0px;padding-right: 0px">
                            <button id="queryButton" class="btn btn-info">查询</button>
                        </div>
                    </div>

                    <div class="section" id="legalReview" style="margin-top: 60px;">
                        <legend   style="font-weight:bold;color:#23b7e5; font-size: 16px; margin-top: 10px"> 一、法制审核：</legend>
                        <!-- 个人和法人或组织金额将被填充 -->
                        <div class="item" id="personalArea"></div>
                        <div class="item" id="personalAmount"></div>
                        <div class="item" id="enterpriseAmount"></div>
                        <div style="width: 90%;margin-left: 50px;margin-top: 5px;">
                            <span id="legalBasis"></span>
                        </div>
                    </div>

                    <div class="section" id="groupDiscussion" style="margin-top: 30px;margin-bottom: 30px;">
                        <legend   style="font-weight:bold;color:#23b7e5; font-size: 16px; margin-top: 10px"> 二、集体讨论：</legend>
                        <!-- 个人和法人或组织金额将被填充 -->
                        <div class="item" id="groupPersonalArea"></div>
                        <div class="item" id="groupPersonalAmount"></div>
                        <div class="item" id="groupEnterpriseAmount"></div>
                        <div style="width: 90%;margin-left: 50px;margin-top: 5px;">
                            <span id="groupLegalBasis"></span>
                        </div>
                    </div>


                </div>
            </div>
        </div>
                    <div id="result" class="mt-3"></div>
                </div>
            </div>
        </div>
    </div>

</div>

<script>
    $(function (){
        if (typeof $().modal !== 'undefined') {
            console.log("Bootstrap 已引入");
        }
        if (typeof $.fn.easyui !== 'undefined') {
            console.log("jQuery EasyUI 已引入");
        }


        //初始化的多选下拉框
        for (let i = 0; i < ${recheckCount}; i++) {
            $('#select-textOne'+i).multiselect({
                nonSelectedText: '请选择认同 xx 专家/委员',
                numberDisplayed: 4,
                onChange: function(option, checked) {

                    if (this.$select.val() !=null && this.$select.val().length > 0) {
                        console.log("回显的值",this.$select.val().join(','))
                        $("#textOne"+i).val(this.$select.val().join(','));
                    }else {
                        $("#textOne"+i).val(null);
                    }
                    console.log("textOne",$("#textOne"+i).val())

                    // 新增的禁用逻辑:选中均不认同时禁用其他选项
                    if (this.$select.val() != null && this.$select.val().includes('均不认同')) {
                        this.$select.find('option').not('[value="均不认同"]').prop('disabled', true);
                        this.$select.multiselect('refresh'); //
                    } else {
                        this.$select.find('option[value="均不认同"]').prop('disabled', true);
                        this.$select.multiselect('refresh');
                    }
                    if (this.$select.val() == null || this.$select.val().length == 0) {
                        this.$select.find('option').prop('disabled', false);
                        this.$select.multiselect('refresh');
                    }
                }
            });
            $('#select-textThree'+i).multiselect({
                nonSelectedText: '请选择文书',
                numberDisplayed: 5,
                maxHeight: 200,
                onChange: function(option, checked) {
                    if (this.$select.val() !=null && this.$select.val().length > 0) {
                        $("#textThree"+i).val(this.$select.val().join(','));
                    }else {
                        $("#textThree"+i).val(null);
                    }
                    console.log("textThree"+i,$("#textThree"+i).val())

                    if(this.$select.val() !=null && this.$select.val().length > 4){
                        var vals = this.$select.val();
                        this.$select.multiselect('setOptions', {
                            nSelectedText: "个已选:"+vals[0]+"等"
                        });
                        this.$select.multiselect('rebuild');
                    }
                }
            });
        }


    })
    var table = document.getElementById("myTable");

    // 获取第一行的所有单元格
    var cells = table.rows[0].cells;
    // 合并第一列的单元格
    cells[0].rowSpan = 2;
    cells[1].rowSpan = 2;
    cells[2].rowSpan = 2;

    var rowId= 1;

    $('#queryButton').click(function() {
        var cityInput = document.getElementById("cityInput").value;
        if (cityInput==null || cityInput==''){
            document.getElementById('personalArea').innerText = "" ;
            document.getElementById('personalAmount').innerText = "";
            document.getElementById('enterpriseAmount').innerText = "";
            document.getElementById('legalBasis').innerText = "";
            // 集体讨论部分使用第二个对象的数据
            document.getElementById('groupPersonalArea').innerText = "" ;
            document.getElementById('groupPersonalAmount').innerText = "";
            document.getElementById('groupEnterpriseAmount').innerText = "";
            document.getElementById('groupLegalBasis').innerText = "";
            return;
        }

        $.ajax({
            url: WEBPATH+'/zjpf/getCaseAssistant2024.do', // 替换为你的请求URL
            type: 'POST',
            data:  {Reqcity: cityInput }, // 将输入内容作为JSON发送
            success: function(data) {
                // 假设我们使用第一个对象的数据填充法制审核部分

                document.getElementById('personalArea').innerText = '行政区：'+data.data[0].province +"-"+ data.data[0].city ;
                document.getElementById('personalAmount').innerText = '个人：'+data.data[0].personal;
                document.getElementById('enterpriseAmount').innerText = '法人或组织：'+data.data[0].enterprise;
                document.getElementById('legalBasis').innerText = '政策文件：'+data.data[0].coment;

                // 集体讨论部分使用第二个对象的数据

                document.getElementById('groupPersonalArea').innerText = '行政区：'+data.data[1].province +"-"+ data.data[1].city ;
                document.getElementById('groupPersonalAmount').innerText = '个人：'+data.data[1].personal;
                document.getElementById('groupEnterpriseAmount').innerText = '法人或组织：'+data.data[1].enterprise;
                document.getElementById('groupLegalBasis').innerText ='政策文件：'+data.data[1].coment;

            },
            error: function(xhr, status, error) {
                $('#result').html('<strong>错误:</strong> ' + error);
            }
        });
    });

    function changeIsAgree(index){
        var val = $("#select-isAgree"+index).val();
        console.log(index,"是否同意选中值:",val)
        if (val == '1') {
            $('#agreeDiv' + index).show();
            $('#textSix' + index).show();
            $('#select-six-label' + index).show();
            $('#disagreeDiv' + index).hide();
        } else {
            $('#agreeDiv' + index).hide();
            $('#textSix' + index).hide();
            $('#select-six-label' + index).hide();
            $('#disagreeDiv' + index).show();
        }
        $("#isAgree"+index).val(val);
        console.log(index,"选中值:",val,"input赋值后:",$("#isAgree"+index).val())
    }

    //提交保存,saveType:3暂存,2保存
    function submitSave(type,saveType){
        console.log("提交类型(2保存,3暂存):",saveType,saveType===3,saveType===2)
        var filesId = document.getElementById("filesId").value;
        var fileCode = document.getElementById("fileCode").value;
        //传给后台的json数据
        var data = {};
        var tableValues = [];
        var table = document.getElementById("myTable2"); // 获取表格元素
        var rows = table.rows; // 获取所有行
        // 遍历行（从索引为1的行开始，跳过表头）
        var index = 0;

        for (var i = 1; i < rows.length; i++) {
            // debugger
            var cells = rows[i].cells; // 获取当前行的所有单元格
            var rowValues = {};
            rowValues["filesId"] = filesId;
            rowValues["fileCode"] = fileCode;
/*            // 遍历单元格
            for (var j = 0; j < cells.length; j++) {
                if (j === 0){
                    rowValues["id"] = cells[j].querySelector("input").value;
                }else if (j === 4){
                    // 复核意见
                    rowValues["replyOpinion"] = cells[j].querySelector("textarea").value;
                    if(!cells[j].querySelector("textarea").value){
                        swal({title: "验证不通过" ,text: "回复意见不能为空!",type:"error"});
                        return false;
                    }
                }
            }*/
            rowValues["id"] = cells[0].querySelector("input").value;
            var isAgree = $("#isAgree"+index).val();
            if (isAgree!=null && (isAgree === 1 || isAgree === '1')){
                isAgree = 1;
                rowValues["textIsAgree"] = "同意";
                var textOne = $("#textOne"+index).val();
                // var textTwo = $("#textTwo"+index).val();
                // var textThree = $("#textThree"+index).val();
                // var textFour = $("#textFour"+index).val();
                var textFive = $("#textFive"+index).val();

                //存在问题/不存在问题
                var textSix = $("#textSix"+index).val();
                var textSeven = $("#textSeven"+index).val();
                if ((textOne==null /*|| textTwo ==null || textThree==null || textFour==null || textFive==null*/ /*|| textSix==null*/ || textSeven==null || textSix == null
                    || textOne=="" /*|| textTwo =="" || textThree=="" || textFour=="" || textFive==""*/ || textSeven=="" || textSix =="")
                    /*&& saveType===2*/){
                    swal({title: "验证不通过" ,text: "请完整填写'不同意'的回复意见!",type:"error"});
                    return false;
                }

                rowValues["textOne"] = textOne;
                // rowValues["textTwo"] = textTwo;
                // rowValues["textThree"] = textThree;
                // rowValues["textFour"] = textFour;
                // rowValues["textFive"] = textFive;
                rowValues["textSix"] = textSix;
                rowValues["textSeven"] = textSeven;
            }else {
                isAgree = 0;
                rowValues["textIsAgree"] = "不同意";
                var replyOpinion = $("#replyOpinion"+index).val();
                if ((replyOpinion ==null || replyOpinion =="" ) && saveType===2){
                    swal({title: "验证不通过" ,text: "请填写'同意申诉意见'的具体情形说明!",type:"error"});
                    return false;
                }
                rowValues["replyOpinion"] = $("#replyOpinion"+index).val();
            }
            rowValues["isAgree"] = isAgree;
            tableValues.push(rowValues); // 将行值列表添加到表格值列表
            index++;
        }
        console.log("拼接的要保存的所有数据",tableValues)
        debugger
        //是否扣分
        var isMinusScore = $('input[name="isMinusScore"]:checked').val();
        if(!isMinusScore  /*&& saveType===2*/){
            swal({title: "验证不通过" ,text: "是否决不能为空!",type:"error"});
            return false;
        }
        var finalOpinion = $("#finalOpinion").val();
        if(!finalOpinion && (isMinusScore ==1 || isMinusScore =='1') /*&& saveType===2*/){
            swal({title: "验证不通过" ,text: "否决理由不能为空!",type:"error"});
            return false;
        }
        var modifyOpinion = $("#modifyOpinion").val();
        if(!modifyOpinion && (isMinusScore ==0 || isMinusScore =='0') /*&& saveType===2*/){
            swal({title: "验证不通过" ,text: "改进意见不能为空!",type:"error"});
            return false;
        }
        var dispute = $("#dispute").val();
        var minusScene =[];
        $('input[name="minusScene"]:checked').each(function(){
            minusScene.push($(this).val());
        })
        // if(!minusScene||minusScene.length==0){
        //     swal({title: "验证不通过" ,text: "情形不能为空!",type:"error"});
        //     return false;
        // }
        data["filesId"] = filesId;
        data["fileCode"] = fileCode;
        data["isMinusScore"] = isMinusScore;
        data["finalOpinion"] = finalOpinion;
        data["modifyOpinion"] = modifyOpinion;
        data["dispute"] = dispute;
        data["minusScene"] = minusScene.toString();
        data["itemList"] = tableValues;
        data["recheckType"] = saveType;
        console.log(data)

        submit(data);

    }
    //复核回复申请
    function submit(data){
        console.log(data); // 打印表格值列表
        //调取后台新增接口
        $.ajax({
            type:"post",
            url:WEBPATH+'/filesRecheck/replyFilesRecheck.do',
            dataType: 'json',
            data: JSON.stringify(data), // 将数据对象转换成 JSON 字符串作为请求参数
            contentType: 'application/json; charset=utf-8',
            success:function (data){
                debugger;
                if(data.result=="error"){
                    swal({title: data.message ,text: data.data,type:"error"});
                    return false;
                }else if(data.result=="success"){
                    swal({title: data.message ,text: "",type:"success"});
                    business.addMainContaierHtml(WEBPATH+'/filesRecheck/declareRecheckList.do?pageNum=1');
                }
            }
        })
    }
    //新增接口
    function insert(tableValues){
        console.log(tableValues); // 打印表格值列表
        //调取后台新增接口
        $.ajax({
            type:"post",
            url:WEBPATH+'/filesRecheck/submitFilesRecheck.do',
            dataType: 'json',
            data: JSON.stringify(tableValues), // 将数据对象转换成 JSON 字符串作为请求参数
            contentType: 'application/json; charset=utf-8',
            success:function (data){
                if(data.result=="error"){
                    swal({title: data.message ,text: data.data,type:"error"});
                    return false;
                }else if(data.result=="success"){
                    swal({title: data.message ,text: "",type:"success"});
                    business.addMainContaierHtml(WEBPATH+'/filesRecheck/declareRecheckList.do?pageNum=1');
                }
            }
        })
    }
    //返回接口
    function returnList(){
        var areaCode =  ${dataInfo.areaCode}

        business.addMainContaierHtml(WEBPATH+'/filesRecheck/provinceRecheckList.do?areaCode='+areaCode+'&pageNum=1');
    }


    function cancelApplyItem() {
        var modal = document.getElementById("myModal");
        modal.style.display = "none";

    }
    function delApplyItem( id) {

    }
    function xiaZaiAnJuan(id){
        //var fileCode = $("#fileCode").html();
        var fileid  = id;
        $.ajax({
            type:"post",
            url:WEBPATH+'/jcpf/jcpfExistFileUrl.do',
            data:{fileId:fileid },           //注意数据用{}
            success:function(data){  //成功
                if("yes" == data){
                    window.location.href="${pageContext.request.contextPath}/jcpf/jcpfAnJuandown.do?fileid="+fileid;
                    return false;
                }else if("no" == data){
                    swal( "操作失败","该案卷不存在!", "error");
                    return false;
                }else if("suffixerror" ==data){
                    swal( "操作失败","该案卷上传数据格式有问题!", "error");
                    return false;
                }
            }
        });
    };

    //分页
    $(document).ready(function(){
        var curentPage = eval('${ajMamageList.pageNum}');
        var totalPage = eval('${ajMamageList.pages}');

        if(totalPage>0){
            var options = {
                bootstrapMajorVersion: 3,
                currentPage: curentPage,
                totalPages: totalPage,
                numberOfPages: 5,
                itemTexts: function (type, page, current) {
                    switch (type) {
                        case "first":
                            return "首页";
                        case "prev":
                            return "&laquo;";
                        case "next":
                            return "&raquo;";
                        case "last":
                            return "尾页";
                        case "page":
                            return page;
                    }
                },
                onPageClicked: function (event, originalEvent, type, page) {
                    business.addMainContentParserHtml(WEBPATH+'/ajpf/ajpf_jiti_shi_list.do?areaType='+areaType+'&pageNum='+page,null);
                }
            };
            $('#pageCon').bootstrapPaginator(options);
        }
    });


    // 处理导出按钮点击事件
    function handleExport() {
        // 导出表格数据的逻辑
        $.ajax({
            type: "GET",
            url: WEBPATH+"/saveFilesBean.do",
            // data:{activityName:activityName},
            async:false,
            success: function(data){
                if(data.result=="error"){
                    swal({title: data.message ,text: "",type:"error"});
                    return false;
                }else if(data.result=="success"){
                    swal({title: data.message ,text: "",type:"success"});
                    // 在这里执行抽取案卷的相关操作

                }
            }
        });

    }
    //下载附件按钮
    function downloadFile(url, fileName) {
        if (url != null && url != "") {
            var fileCode = document.getElementById("fileCode").value;
            var s = fileCode.substring(0,20);
            var s1 = fileName.substring(0,15);
            var suffix = fileName.substring(fileName.length - 4);
            var newName = s+"_"+s1+suffix

            console.log(url)
            console.log(newName)
            location.href = "${webpath}/filedownload.do?url=" + url + "&fileName=" + newName;
        } else {
            swal({title: "提示", text: "您下载的附件不存在！", type: "info"});
        }
    }

    function displayCaseInfo(value) {
        var div = document.getElementById("caseInfo")
        var redDiv = document.getElementById("redDiv")
        var modifyOpinionDiv = document.getElementById("modifyOpinionDiv")
        if(value == '0') {
            div.style.display = 'none';
            redDiv.style.display = 'none';
            modifyOpinionDiv.style.display = 'block';
        } else {
            div.style.display = 'block';
            redDiv.style.display = 'block';
            modifyOpinionDiv.style.display = 'none';
        }
    }


</script>

<style>
    .modal-body {
        max-height: calc(100vh - 210px);  /* 调整 '210px' 到适合你的模版的大小 */
        overflow-y: auto;
    }

    .item {
        width: 90%;
    /*    内容左右居中*/
        margin-left: 50px;
        margin-top: 5px;
    }
    .center-body {
        /*width: 99%;*/
        margin: 10px;
        overflow: hidden;
    }
    .head-item {
        width: 1200px;
        height: 45px;
        margin-top: 15px;
    }
    .main-center-body {
        height: 75%;
        overflow: auto; /* 自动显示滚动条 */
        margin-top: 70px;
    }

    .head-item>ul>li {
        float: left;
    }

    .head-item>ul>li>a {
        display: block;
        margin-right: 15px;
        width: 158px;
        height: 45px;
        background: #3f91c2;
        text-align: center;
        line-height: 45px;
        box-shadow: 0 0 5px #999;
        overflow: hidden;
        border-radius: 8px;
        color: #ffffff;
        font-size: 16px;
    }
    .ul-clearfix {
        *zoom: 1;
        margin-left: 10px;
    }

    #myTable {
        width: 100%;
    }
    /*.table>thead>tr>td{*/
    /*    font-size: 18px;*/
    /*    font-weight: bold;*/
    /*}*/
    /*th, td {*/
    /*    border: 1px solid black;*/
    /*    padding: 8px;*/
    /*    text-align: center;*/
    /*    font-size: 18px;*/
    /*}*/
    .wscbj{width: 40%;margin:0 auto;border-radius: 3px;margin-top: 14px;padding-left:10px}

    select,input,textarea{
        border:1px solid rgba(0,0,0,0.2);
        border-radius: 5px;
        color: rgba(0,0,0,0.7)!important;
    }
    textarea{
        padding: 6px;
        box-sizing: border-box;
    }
    .one-seven-input{

        margin: 5px;
        border: none;
        border-bottom: 1px solid black;
        border-radius: 0px!important;
    }

</style>
</html>
