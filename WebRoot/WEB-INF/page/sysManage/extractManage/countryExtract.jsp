<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>


<div class="center_weizhi">当前位置：系统管理 -随机管理-县级抽选</div>
<!--框架内容 开始-->
<div class="center">
<div class="center_list">
 <div class="panel-group" id="accordion">
          <!---快速查询--->
          <form role="form">
              
              <select id="fileType" class="form-control" style="width:300px;margin-right:5px;">
                 <option value=''>请选择案卷类型</option>
                  <option value='0'>行政处罚案卷</option>
                  <option value='1'>按日计罚案卷</option>
                  <option value='6'>查封扣押案卷</option>
                  <option value='7'>限产停产案卷</option>
                  <option value='2'>移送行政拘留案卷</option>
                  <option value='3'>涉嫌犯罪移送案卷</option>
              </select>
        
        </form>
            <!---搜索--->
            <div style="width:260px;" class="btn-group">
               <form class="bs-example bs-example-form" role="form">
                  <div class="row">                     
                     <div class="col-lg-6">
                        <div class="input-group">
                           <input type="text" class="form-control" id="fileCode" style="width:300px;" placeholder="请输入案卷文号">
                           <span class="input-group-btn">
                              <button class="btn btn-danger" type="button" onclick="search()">
                                 快速搜索
                              </button>
                              <button id="viewExcel" type="button" class="btn btn-danger">导出EXCEL</button>
                           </span>
                        </div><!-- /input-group -->
                     </div><!-- /.col-lg-6 -->
                  </div><!-- /.row -->
               </form>
            </div>            
    </div>
        <table class="table table-bordered table-hover table-condensed">
         <thead>
           <tr>
             <td width="50" height="30" bgcolor="#efefef">序号</td>
             <td bgcolor="#efefef">省</td>
             <td bgcolor="#efefef">地市</td>
             <td bgcolor="#efefef">区县</td>
             <td bgcolor="#efefef">案卷类型</td>
             <td bgcolor="#efefef">案卷文号</td>
           
             <td bgcolor="#efefef">操作</td>
           </tr>
         </thead>
         <tbody>
           
          <c:forEach var="list" items="${pageBean.list}" varStatus="index">
          <tr>
           <td height="30" align="center">${index.index+1}</td>
            <td>${list.province}</td>
            <td>${list.city}</td>
            <td>${list.country}</td>
           <%--  <c:if test="${list.fileType == }"></c:if> --%>
           
            <c:if test="${list.fileType == 0 }">
             <td>行政处罚案卷</td>
            </c:if>
            <c:if test="${list.fileType == 1 }">
             <td>按日计罚案卷</td>
            </c:if>
            <c:if test="${list.fileType == 2 }">
             <td>移送行政拘留案卷</td>
            </c:if>
            <c:if test="${list.fileType == 3 }">
             <td>涉嫌环境污染犯罪</td>
            </c:if>
            <c:if test="${list.fileType == 4 }">
             <td>申请法院强制执行案卷</td>
            </c:if>
            <c:if test="${list.fileType == 5 }">
             <td>发现问题的污染源现场监督检查稽查案卷</td>
            </c:if>
            <c:if test="${list.fileType == 6 }">
             <td>查封扣押案卷</td>
            </c:if>
            <c:if test="${list.fileType == 7 }">
             <td>限产停产案卷</td>
            </c:if>
            <td>${list.fileCode}</td>
          <c:choose>
            	<c:when test="${sessionScope.sa_session.sysStatus == '1' }">
              		 <td><button class="btn btn-success btn-xs" data-toggle="modal" data-target="#myModal" onclick="tianBao(${list.belongAreacode},${list.fileType},${list.id})">编辑</button></td>
          		</c:when>
          		<c:otherwise>
          			<td>&nbsp;</td>
          		</c:otherwise>
          </c:choose>
          </tr>
          </c:forEach>
           
         </tbody>
       </table>
  
</div>
  <div class="page">
    <span style="padding:12px; float:left; color:#0099cc;">共${pageBean.total}条记录</span>
        <ul class="pagination" id="pageCon">
        </ul>
    </div>

</div>
<!-- 编辑（Modal） -->
<div class="modal fade" id="myModal"  role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title" id="myModalLabel">编辑</h4>
            </div>
            <div class="modal-body form-horizontal" style="height:300px; font-size:16px;">
            	<div class="form-group" style="padding:10px;">
                	<label class="col-lg-2 control-label">案卷类型</label>
                    <div class="col-lg-10" style="margin-top:7px;">
                	行政处罚 
                    </div>
                </div>
            	<div class="form-group" style="padding:10px;">
                	<label class="col-lg-2 control-label">案卷文号</label>
                 
              
                 <div class="col-sm-12"> 
                 <select id="typeaheadxxcf1" >
				 </select>
                 <input type="hidden"  id="fileCode" />
                 <input type="hidden"  id="oldID"/>
                  <input type="hidden"  id="id"/>
                </div>
             
            
                    </div>
                </div>
            </div>
            <div class="modal-footer"><button type="button" class="btn btn-primary" onclick="save()" data-dismiss="modal">确定</button>
                <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal -->
</div>

<!--框架内容 结束-->


 
<script type="text/javascript">
var webPath = '${webpath}';
function search(){
	var fileType = $("#fileType").val();
	var fileCode = $("#fileCode").val();
	business.addMainContentParserHtml(WEBPATH+'/sys2017/extractConutry.do?fileType='+fileType+'&fileCode='+fileCode);
}
  

$(document).ready(function(){
	
		
	$("#viewExcel").click(function(){
		var fileType = $("#fileType").val();
		var fileCode = $("#fileCode").val();
		window.location.href= WEBPATH+'/sys2017/outputCountryExcel.do?fileType='+fileType+"&fileCode="+fileCode;
	});
	
	var curentPage = eval('${pageBean.pageNum}');
	var totalPage = eval('${pageBean.pages}');
	var fileType = '${fileType}';
	var fileCode = '${fileCode}';
	if(fileType != ''){
		$("#fileType").find("option[value='"+fileType+"']").attr("selected","selected");
	}
	if(fileCode != ''){
		$("#fileCode").val(fileCode);
	}
	var fileCode = $("#fileCode").val();
	if(totalPage>0){
		var options = {
			bootstrapMajorVersion: 3,
		    currentPage: curentPage,
		    totalPages: totalPage,
		    numberOfPages: 5,
		    itemTexts: function (type, page, current) {
		            switch (type) {
		            case "first":
		                return "首页";
		            case "prev":
		                return "&laquo;";
		            case "next":
		                return "&raquo;";
		            case "last":
		                return "尾页";
		            case "page":
		                return page;
		            }
		    },
		    onPageClicked: function (event, originalEvent, type, page) {
		    	var fileType = $("#fileType").val();
		    	var fileCode = $("#fileCode").val();

		    	console.log(fileType);
            	business.addMainContentParserHtml(WEBPATH+'/sys2017/extractConutry.do?pageNum='+page+'&fileType='+fileType+'&fileCode='+fileCode);
            }
    	};
    	$('#pageCon').bootstrapPaginator(options);
	}
	
	
});
	

var areaCode1 = null;
var fileType1 = null;
function save(){
	var fileCode = $("#fileCode").val();
	var id = $("#id").val();
	var oldID = $("#oldID").val();
	if(fileCode ==''){
		swal("操作失败!", "没有选择案卷，无法修改", "error");
		return ;
	}
	
	swal({
        title: "您确定执行此操作吗？",
        text: "您确定要修改此案卷号吗？",
        type: "warning",
        showCancelButton: true,
        closeOnConfirm: false,
        confirmButtonText: "是的，我要修改",
        confirmButtonColor: "#ec6c62"
    }, function() {
        $.ajax({
            url: webPath+"/sys2017/editAnJuanWenHao.do",
            type: "POST",
            data:{"fileCode":fileCode,"id":id,"oldID":oldID}
        }).done(function(data) {
        
            if(data="success"){
            	swal("操作成功!", "已成功修改！", "success");
            	macroMgr.onLevelTwoMenuClick(null, 'sys2017/extractConutry.do?pageNum='+curentPage);
            }else{
                swal("OMG", "修改失败了!", "error");
            }
            
        }).error(function(data) {
            swal("OMG", "发生错误", "error");
        });
    });
}


$(document).ready(function(){
	 
	
		//初始化加载案件一select2
		$("#typeaheadxxcf1").select2({
			  language:'zh-CN',
			  ajax: {
			    url: "${webpath}/sys2017/GetAnJuanWenHao.do",
			    dataType: 'json',
			    delay: 500,
			    type:'POST',
			    data: function (params) {
			      return {
			        query: params.term,
			        qtype:0,
			        areaCode:areaCode1,
			        fileType:fileType1,
			        areaType:'3'
			      };
			    },
			    processResults: function (data, params) {
			      return {
			        results: data
			      };
			    },
			    cache: true
			  },
			  escapeMarkup: function (markup) 
			  { 
				  return markup; 
			  },
			  templateResult:function (result) {
			        return result.text;
		      },
			  templateSelection:function (result) {
			        return result.text;
		      },
			  minimumInputLength:3,
			  theme:'bootstrap',
			  placeholder:'请选择决定书文号',
			  allowClear:true
			});
	    	//绑定选择select事件
			$('#typeaheadxxcf1').on('select2:select', function (evt) {
				var res = $("#typeaheadxxcf1").select2("data");
				$("#fileCode").val(res[0].text);
				$("#id").val(res[0].id);
			});
			//绑定取消选择select事件
			$('#typeaheadxxcf1').on('select2:unselecting', function (evt) {
				$("#fileCode").val("");
				$("#id").val("");
			});
			
 
 
});
function tianBao(areaCode,fileType,id){
	console.log(areaCode);
	areaCode1= areaCode;
	fileType1 = fileType;
	console.log("id...."+id);
	$("#oldID").val(id);
	console.log(fileType);
	var typeText = null;
	if(fileType == 0){
		typeText = '行政处罚案卷';
	}else if(fileType == 1){
		typeText = '按日继罚案卷';
	}else if(fileType == 2 ){
		typeText = '移送行政拘留案卷';
	}else if (fileType == 3){
		typeText = '涉嫌环境污染犯罪';
	}else if(fileType == 4){
		typeText = '申请法院强制执行案卷';
	}else if(fileType ==5){
		typeText='发现问题的污染源现场监督检查稽查案卷';
	}else if(fileType ==6){
		typeText='查封扣押案卷';
	}else if (fileType ==7){
		typeText = '限产停产案卷';
	}
	console.log("..........."+areaCode1);
	//$("#fileType").text(typeText);
	//$("#fileType").innerHTML = typeText;
	$("#fileTP").html(typeText);
	
	
}	
	
</script>