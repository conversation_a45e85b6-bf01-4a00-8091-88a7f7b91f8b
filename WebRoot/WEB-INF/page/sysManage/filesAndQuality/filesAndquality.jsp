<%@ page language="java" import="java.util.*,java.lang.*" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
 
<style>
    table{width: 900px;}
    tr{width:100%;}
    td{text-align: left; }
</style>

<div class="center_weizhi">当前位置：系统管理 - 案卷&质量、得分模块</div>
<!--框架内容 开始-->
  
<div class="center">
      <div class="center_list">
     <span id ="sysBiaoZhi" hidden="hidden"></span>
        <table class="table_input" style="margin-left: 100px">
         <tbody>
           <c:forEach items="${initList }" var="init" varStatus="status">
           		<tr>
		             <td>
		             	 <div style="width:150px;">
		           		  ${status.index+1 }、${init.actionname }
		            	 </div>
		             </td>
		             <td >
		             	<div style="width:250px;">
		                                                             执行结果：<label >${init.result }</label>
		                </div>
		             </td>
		             <td >执行条件:
		             	    <c:choose>
							       <c:when test="${init.code=='7' or init.code=='17' or init.code=='35'}">
							       		<br/>
							       		<form  id= "searchForm${init.code}" method="post">
					            		 <div style="width:550px;">
					            		   <input name="code"  type="hidden"  value="${init.code}">
					             		<span>  普通县:&nbsp; 
					             							  行政:<input class="weightBean${init.code}" name="countryXz" style="width: 35px;" />&nbsp; 
					             		 					   按日:<input class="weightBean${init.code}" name="countryAr" style="width: 35px;" />&nbsp;
					             		 					   移送:<input class="weightBean${init.code}" name="countryYs" style="width: 35px;" />&nbsp;
					             		 					   涉嫌:<input class="weightBean${init.code}" name="countrySx" style="width: 35px;" />&nbsp;
					             		 					   法院:<input class="weightBean${init.code}" name="countryFy" style="width: 35px;" />&nbsp;
					            		 </span>
					            		 <br/>
					            		 <br/>
					             		 <span >普通市:&nbsp;  行政:<input class="weightBean${init.code}" name="cityPtXz" style="width: 35px;"  />&nbsp; 
					             		 					   按日:<input class="weightBean${init.code}" name="cityPtAr" style="width: 35px;"  />&nbsp;
					             		 					   移送:<input class="weightBean${init.code}" name="cityPtYs" style="width: 35px;"  />&nbsp;
					             		 					   涉嫌:<input class="weightBean${init.code}" name="cityPtSx" style="width: 35px;"  />&nbsp;
					             		 					   法院:<input class="weightBean${init.code}" name="cityPtFy" style="width: 35px;"  />&nbsp;
					             		 					   稽查:<input class="weightBean${init.code}" name="cityPtJc" style="width: 35px;"  />&nbsp;
					             		 </span>
					             		 <br/>
					             		 <br/>
					             		 <span >直辖市（区县）:&nbsp; 行政:<input class="weightBean${init.code}" name="cityZxXz" style="width: 35px;"  />&nbsp; 
					             		 					   按日:<input class="weightBean${init.code}" name="cityZxAr" style="width: 35px;"  />&nbsp;
					             		 					   移送:<input class="weightBean${init.code}" name="cityZxYs" style="width: 35px;"  />&nbsp;
					             		 					   涉嫌:<input class="weightBean${init.code}" name="cityZxSx" style="width: 35px;"  />&nbsp;
					             		 					   法院:<input class="weightBean${init.code}" name="cityZxFy" style="width: 35px;"  />&nbsp;
					             		 </span>
					             		 <br/>
					             		 <br/>
					             		 <span >普通省:&nbsp; 行政:<input class="weightBean${init.code}" name="provincePtXz" style="width: 35px;"  />&nbsp; 
					             		 					   稽查:<input class="weightBean${init.code}" name="provincePtJc" style="width: 35px;"  />&nbsp;
					             		 					   最高市:<input class="weightBean${init.code}" name="provincePtCityScore" style="width: 35px;"  />&nbsp;
					             		 					   最高县:<input class="weightBean${init.code}" name="provincePtCountryScore" style="width: 35px;"  />&nbsp;
					             		 </span>
					             		 <br/>
					             		 <br/>
					             		 <span >直辖市:&nbsp; 行政:<input class="weightBean${init.code}" name="provinceZxXz" style="width: 35px;"  />&nbsp; 
					             		 					稽查:<input class="weightBean${init.code}" name="provinceZxJc" style="width: 35px;"  />&nbsp;
					             		 					最高（区县）:<input class="weightBean${init.code}" name="provinceZxCityScore" style="width: 35px;"  />&nbsp;
					             		 </span>
					            		
					            	  </div>
					            	  </form>
							       </c:when>
							       <c:otherwise>
							       </c:otherwise>
							</c:choose>
		             </td>
		             <td>
		                 <c:if test="${init.flag=='1' }">
             				<button  class="btn btn-primary" name="signup" value="Sign up" style="font-size:16px;width:250px; margin-top:5px;background-color: gray;">${status.index+1 }、&nbsp;&nbsp;执行操作...</button>
                         </c:if>
                         <c:if test="${init.flag=='0' }">
	                          <c:choose>
							       <c:when test="${init.code=='17' or init.code=='7' or init.code=='35'}">
							       		<button onclick="submitConfigType('${init.code }')" type="button" class="btn btn-primary" name="signup" value="Sign up" style="font-size:16px;width:250px; margin-top:5px;">${status.index+1 }、&nbsp;&nbsp;执行操作...</button>
							       </c:when>
							       <c:otherwise>
							      		<button onclick="submitConfig('${init.code }')" type="button" class="btn btn-primary" name="signup" value="Sign up" style="font-size:16px;width:250px; margin-top:5px;">${status.index+1 }、&nbsp;&nbsp;执行操作...</button>
							       </c:otherwise>
							</c:choose>
                           	 
                         </c:if>
		             </td>
				</tr>
				<tr>
		           		<td colspan="4">
		           			<hr/>
		           		</td>
               </tr>
           </c:forEach>
         </tbody>
       </table>
  </div>
</div>
<script type="text/javascript">
function submitConfig(code){
	    $.ajax({
             type: "POST",
             url: WEBPATH+"/sys/sysInitSubmit.do",
             data:{code:code},
             async:false,
             success: function(data){
	             if(data.type=="error"){
	               	swal({title: data.result ,text: "",type:"error"});
	                return false;
	             }else if(data.type=="success"){
	              	swal({title: data.result ,text: "",type:"success"});
	             }
           }
     });
     business.addMainContentParserHtml(WEBPATH+'/sys/sysFilesAndQuality.do',null);
}

function submitConfigType(code){
	var weightBeanList =  $(".weightBean"+code);

	var regexpCh = /(?!^0\.0?0$)^[0]?(\.[0-9]{1,2})?$|^(1|0\.0|0\.00|1\.0|1\.00)$/;
	
/* 	for (var i = 0; i < weightBeanList.size(); i++) {
		var inputVal = weightBeanList.eq(i).val();
		// 这里需要校验
		if($.trim(inputVal)==""){
			swal("权重比例不能为空", "信息操作失败了!", "error");
			return false;
		}
		if(!regexpCh.test(inputVal))
		{
			swal("权重区间0~1,小数点保留两位", "信息操作失败了!", "error");
			return false;
		}
	}	 */
	    $.ajax({
	        type: "POST",
	        url: WEBPATH+"/sys/sysInitSubmit.do",
	        data:$("#searchForm"+code).serialize(),
	        async:false,
	        success: function(data){
	            if(data.type=="error"){
	              	swal({title: data.result ,text: "",type:"error"});
	               return false;
	            }else if(data.type=="success"){
	             	swal({title: data.result ,text: "",type:"success"});
	            }
	      }
	});
	business.addMainContentParserHtml(WEBPATH+'/sys/sysFilesAndQuality.do',null);
}
</script>

 
 
