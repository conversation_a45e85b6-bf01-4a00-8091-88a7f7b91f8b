<%@ page language="java" import="java.util.*,java.lang.*" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
 
<style>
    table{width: 900px;}
    tr{width:100%;}
    td{text-align: left; }
</style>

<div class="center_weizhi">当前位置：系统管理 - 案卷&质量、得分模块</div>
<!--框架内容 开始-->
  
<div class="center">
      <div class="center_list">
     <span id ="sysBiaoZhi" hidden="hidden"></span>
        <table class="table_input" style="margin-left: 100px">
         <tbody>
           <c:forEach items="${initList }" var="init" varStatus="status">
           		<tr>
		             <td>
		             	 <div style="width:150px;">
		           		  ${status.index+1 }、${init.actionname }
		            	 </div>
		             </td>
		             <td >
		             	<div style="width:250px;">
		                                                             执行结果：<label >${init.result }</label>
		                </div>
		             </td>
		             <td >计算公式:
		             	    <c:choose>
							       <c:when test="${init.code=='10'}">
							       		<br/>
							       		<form  id= "searchForm${init.code}" method="post">
					            		 <div style="width:550px;">
					            		   <input name="code"  type="hidden"  value="${init.code}">
					             		<span><b>县(直辖市下辖区县):</b>&nbsp; 
					             			县级【交叉】案卷质量得分=推选案卷（2份行政处罚案卷得分/2×0.6+移送公安案卷×0.4）×0.5+抽选案卷（2份一般行政处罚案卷合计得分/2×0.6+适用四个配套办法案卷×0.4）×0.5
					            		 </span>
					            		 <br/>
					            		 <br/>
					             		 <span><b>市:</b>&nbsp;  
					             			市级 【交叉】案卷质量得分=推选案卷（2份行政处罚案卷得分/2×0.5+移送公安案卷×0.3+2份稽查案卷/2×0.2）×0.5+抽选案卷（2份一般行政处罚案卷合计得分/2×0.6+适用四个配套办法案卷×0.4）×0.5
					             		 </span>
					             		 <br/>
					             		 <br/>
					             		 <span><b>省(直辖市):</b>&nbsp; 
					             		 	省级【交叉】案卷质量得分=推选案卷（行政处罚案卷得分×0.5+2份稽查案卷/2×0.5）×0.5+抽选案卷（n份行政处罚案卷合计得分/n）×0.5
					             		 </span>
					             		 <br/>
					            	  </div>
					            	  </form>
							       </c:when>
							       <c:when test="${init.code=='11'}">
							       		<br/>
							       		<form  id= "searchForm${init.code}" method="post">
					            		 <div style="width:550px;">
					            		   <input name="code"  type="hidden"  value="${init.code}">
					             		<span><b>县(直辖市下辖区县):</b>&nbsp; 
					             			县级【专家】案卷质量得分=推选案卷（2份行政处罚案卷得分/2×0.6+移送公安案卷×0.4）×0.5+抽选案卷（2份一般行政处罚案卷合计得分/2×0.6+适用四个配套办法案卷×0.4）×0.5
					            		 </span>
					            		 <br/>
					            		 <br/>
					             		 <span><b>市:</b>&nbsp;  
					             			市级【专家】案卷质量得分=推选案卷（2份行政处罚案卷得分/2×0.6+移送公安案卷×0.4）×0.5+抽选案卷（2份一般行政处罚案卷合计得分/2×0.6+适用四个配套办法案卷×0.4）×0.5
					             		 </span>
					             		 <br/>
					             		 <br/>
					             		 <span><b>省(直辖市):</b>&nbsp; 
					             		 	省级【专家】案卷质量得分=推选案卷（行政处罚案卷得分）×0.5+抽选案卷（n份行政处罚案卷合计得分/n）×0.5
					             		 </span>
					             		 <br/>
					            	  </div>
					            	  </form>
							       </c:when>
							       <c:when test="${init.code=='12'}">
							       		<br/>
							       		<form  id= "searchForm${init.code}" method="post">
					            		 <div style="width:550px;">
					            		   <input name="code"  type="hidden"  value="${init.code}">
					             		<span>
					             			<!-- “环境执法案卷质量”指标得分=【交叉评分】“环境执法案卷质量”指标得分×0.3+【专家评分】“环境执法案卷质量”指标得分×0.7 -->
					             			集体质量分 = 【（推荐案卷总分/应推荐案卷数）*0.1】+【（随机案卷总分/应随机案卷数）*0.1】
					            		 </span>
					            	  </div>
					            	  </form>
							       </c:when>
							       
							       <c:when test="${init.code=='42'}">
							       		<br/>
							       		<form  id= "searchForm${init.code}" method="post">
					            		 <div style="width:550px;">
					            		   <input name="code"  type="hidden"  value="${init.code}">
					             		<span>
					             			“环境执法案卷质量”指标得分=个人案卷质量得分=个人案卷得分之和/2；
					            		 </span>
					            	  </div>
					            	  </form>
							       </c:when>
							       <c:otherwise>
							       </c:otherwise>
							</c:choose>
		             </td>
		             <td>
		                 <c:if test="${init.flag=='1' }">
             				<button  class="btn btn-primary" name="signup" value="Sign up" style="font-size:16px;width:250px; margin-top:5px;background-color: gray;">${status.index+1 }、&nbsp;&nbsp;执行操作...</button>
                         </c:if>
                         <c:if test="${init.flag=='0' }">
							<button onclick="submitConfig('${init.code }')" type="button" class="btn btn-primary" name="signup" value="Sign up" style="font-size:16px;width:250px; margin-top:5px;">${status.index+1 }、&nbsp;&nbsp;执行操作...</button>
                         </c:if>
		             </td>
				</tr>
				<tr>
		           		<td colspan="4">
		           			<hr/>
		           		</td>
               </tr>
           </c:forEach>
         </tbody>
       </table>
  </div>
</div>
<script type="text/javascript">
function submitConfig(code){
	    $.ajax({
             type: "POST",
             url: WEBPATH+"/sys/sysInitSubmit2017.do",
             data:{code:code},
             async:false,
             success: function(data){
	             if(data.type=="error"){
	               	swal({title: data.result ,text: "",type:"error"});
	                return false;
	             }else if(data.type=="success"){
	              	swal({title: data.result ,text: "",type:"success"});
	             }
           }
     });
     business.addMainContentParserHtml(WEBPATH+'/sys/sysFilesAndQuality2017.do',null);
}

function submitConfigType(code){
	var weightBeanList =  $(".weightBean"+code);

	var regexpCh = /(?!^0\.0?0$)^[0]?(\.[0-9]{1,2})?$|^(1|0\.0|0\.00|1\.0|1\.00)$/;
	
/* 	for (var i = 0; i < weightBeanList.size(); i++) {
		var inputVal = weightBeanList.eq(i).val();
		// 这里需要校验
		if($.trim(inputVal)==""){
			swal("权重比例不能为空", "信息操作失败了!", "error");
			return false;
		}
		if(!regexpCh.test(inputVal))
		{
			swal("权重区间0~1,小数点保留两位", "信息操作失败了!", "error");
			return false;
		}
	}	 */
	    /* $.ajax({
	        type: "POST",
	        url: WEBPATH+"/sys/sysInitSubmit2017.do",
	        data:$("#searchForm"+code).serialize(),
	        async:false,
	        success: function(data){
	            if(data.type=="error"){
	              	swal({title: data.result ,text: "",type:"error"});
	               return false;
	            }else if(data.type=="success"){
	             	swal({title: data.result ,text: "",type:"success"});
	            }
	      }
	});
	business.addMainContentParserHtml(WEBPATH+'/sys/sysFilesAndQuality2017.do',null); */
}
</script>

 
 
