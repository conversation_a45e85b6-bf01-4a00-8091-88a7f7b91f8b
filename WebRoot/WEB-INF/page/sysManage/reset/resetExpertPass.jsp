<%@ page language="java" import="java.util.*,java.lang.*" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>

<script type="text/javascript">
   $(document).ready(function(){
   });
    function resetChildrenPass(id){
        swal({
                        title: "您确定执行此操作吗？",
                        text: "您确定要重置当前登录密码吗？",
                        type: "warning",
                        showCancelButton: true,
                        closeOnConfirm: false,
                        confirmButtonText: "是的，我要重置",
                        confirmButtonColor: "#ec6c62"
                    }, function() {
                        $.ajax({
                            url: WEBPATH+"/sys/resetExpPass.do",
                            type: "POST",
                            data:{id:id}
                        }).done(function(data) {
                        
                            if(data.type="success"){
                            	swal("操作成功!", "已成功重置密码！", "success");
                            	macroMgr.onLevelTwoMenuClick(null, 'sys/resetExpPassword.do');
                            }else{
                                swal("OMG", "重置密码失败了!", "error");
                            }
                            
                        }).error(function(data) {
                            swal("OMG", "删除操作失败了!", "error");
                        });
                    });
    }


           
                    
</script>
<div class="center_weizhi">当前位置：系统管理 - 专家评审用户密码重置</div>
<!--框架内容 开始-->
<div class="center">
<div class="center_list">
    <div class="panel-group" id="accordion">
          <h4 style="color:#F00;">
			  提示：系统初始密码是：Hjzfdlb12369           
          </h4>
    </div>
        <!---案件列表--->
        <table class="table table-bordered table-hover table-condensed">
         <thead>
      <!--    <tr><td colspan="5"  bgcolor="#efefef" align="center" style="font-size:15px; font-weight:bold;">专家评审用户密码重置列表 </td></tr>
      -->      <tr>
             <td width="50" height="30" bgcolor="#efefef">序号</td>
             <td bgcolor="#efefef">专家姓名</td>
             <td width="200" bgcolor="#efefef">专家账号</td>
             <td width="200" bgcolor="#efefef">密码重置</td>
             <td width="200" bgcolor="#efefef">备注</td>
           </tr>
         </thead>
         <tbody>
              <c:forEach items="${expertUserList}" var="area" varStatus="status">
           			<tr>
           			     <td>${status.index+1}</td>
			             <td>${area.name}</td>
			             <td>${area.loginname }</td>
			             <td>
			             		<c:choose>
			             		     <c:when test="${area.loginpass=='dfc2c79046d493936ae5aa30e2a04f01' }">
			             		     		  <button type="button" class="btn btn-primary btn-xs" disabled="disabled"  style="width:100px; margin:1px;background-color: gray;">重置密码</button>
			             		     </c:when>
			             		     <c:otherwise>
			             		        	 <a href="#" onclick="resetChildrenPass(${area.id})"><button type="button" class="btn btn-primary btn-xs" style="width:100px; margin:1px;">重置密码</button></a>
			             		     </c:otherwise>
			             		</c:choose>
			             </td>
			             <td>
			             	${area.remark }
			             </td>
                   </tr>
             </c:forEach>
          
         </tbody>
       </table>
       
    </div>
  
</div>





 
 
