<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<script type="text/javascript" src="${webpath}/static/vue/vue.js">
</script>

<div class="center_weizhi">当前位置：系统管理 - 信息统计 - 案卷成绩汇总</div>
<!--框架内容 开始-->
<div class="center">
<div class="center_list">

            <div class="panel-group" id="accordion">

                <!---搜索--->
                <div style="width:260px; height: 50px; float: right; margin: 15px 70px" class="btn-group">
                    <button   class="btn btn-danger pull-right" id="outputExcel">导出excel</button>
                </div>
            </div>
            <div style="text-align: center; margin-top: 50px; margin-bottom: 15px">
               <span style="font-size:24px;">各推荐集体案卷整体分数一览表</span>
            </div>
            <table id="show" class="table table-bordered table-hover table-condensed" style="width:1350px;margin-left: 120px">
             <thead>
               <tr style="text-align: center;">
                   <td bgcolor="#00B050"><b>省</b></td>
                   <td bgcolor="#00B050"><b>市</b></td>
                   <td bgcolor="#00B050"><b>区</b></td>
                   <td bgcolor="#00B050"><b>案卷得分总和</b></td>
                   <td bgcolor="#00B050"><b>推荐案卷数</b></td>
                   <td bgcolor="#00B050"><b>随机案卷数</b></td>
                   <td bgcolor="#00B050"><b>参评案卷总数</b></td>
                   <td bgcolor="#00B050"><b>应参评案卷总数</b></td>
               </tr>
             </thead>
             <tbody class="table table-bordered table-hover table-condensed">
                 <c:forEach items="${pageBean.list}" var="item" varStatus="status">
                    <tr>
                        <td>${item.province}</td>
                        <td>${item.city}</td>
                        <td>${item.country}</td>
                        <td>${item.finalScore}</td>
                        <td>${item.recommendCount}</td>
                        <td>${item.randomCount}</td>
                        <td>${item.rateeCount}</td>
                        <td>${item.shouldRateeCount}</td>
                    </tr>
                </c:forEach>
             </tbody>
           </table>
            <div class="page">
                <span style="padding:12px; float:left; color:#0099cc;">共${pageBean.total}条记录</span>
                <ul class="pagination" id="pageCon">
                </ul>
            </div>
    </div>
</div>


<script type="text/javascript">

//分页
$(document).ready(function(){

    var curentPage = eval('${pageBean.pageNum}');
    var totalPage = eval('${pageBean.pages}');
    if(totalPage>0){
        var options = {
            bootstrapMajorVersion: 3,
            currentPage: curentPage,
            totalPages: totalPage,
            numberOfPages: 5,
            itemTexts: function (type, page, current) {
                switch (type) {
                    case "first":
                        return "首页";
                    case "prev":
                        return "&laquo;";
                    case "next":
                        return "&raquo;";
                    case "last":
                        return "尾页";
                    case "page":
                        return page;
                }
            },
            onPageClicked: function (event, originalEvent, type, page) {
                business.addMainContentParserHtml(WEBPATH+'/sys2021/caseGrade.do?pageNum='+page);
            }
        };
        $('#pageCon').bootstrapPaginator(options);
    }


});



    $("#outputExcel").click(function(){
        window.location.href= WEBPATH+"/sys2021/downCasweGrade.do"
    });
	
</script>
