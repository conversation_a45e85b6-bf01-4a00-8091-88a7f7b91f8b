<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<script type="text/javascript" src="${webpath}/static/vue/vue.js">
</script>

<div class="center_weizhi">当前位置：系统管理 - 信息统计 - 案卷质量分析表</div>
<!--框架内容 开始-->
<div class="center">
    <div class="center_list">
        <div class="panel-group" id="accordion">
            <!---快速查询--->
            <form role="form" id="searchForm" class="form-horizontal" style="margin-top: 20px">
                <div class="row">
                    <div class="col-lg-1" style="width:375px">
                        <label  class="col-sm-3 control-label">所属行政区</label>
                        <div style="display: flex">
                            <div>
                                <select class="form-control" style="width:70px;height: 32px;padding: 0" id="province" name="provinceCode" onchange="business.cascadedful($('#province').val(),'city');">
                                    <c:if test="${fn:length(provinceList)>1 }">
                                        <option value="">-省(市)-</option>
                                    </c:if>
                                    <c:forEach items="${provinceList }" var="prolist">
                                        <option value="${prolist.code }" <c:if test="${fn:substring(areacode,0,2).concat('000000')==prolist.code }">selected</c:if> >${prolist.name }</option>
                                    </c:forEach>
                                </select>
                            </div>
                            <div >
                                <select class="form-control" style="width:70px;height: 32px;padding: 0" id="city" name="cityCode" onchange="business.cascadedful($('#city').val(),'country');">
                                    <c:if test="${tarea.arealevel!=3 && tarea.arealevel!=2}">
                                        <option value="">-地(市)-</option>
                                    </c:if>
                                    <c:forEach items="${cityList }" var="citylist">
                                        <option value="${citylist.code }" <c:if test="${fn:substring(areacode,0,4).concat('0000')==citylist.code }">selected</c:if> >${citylist.name }</option>
                                    </c:forEach>
                                </select>
                            </div>
                            <div>
                                <select class="form-control" style="width:70px;height: 32px;padding: 0" id="country" name="countryCode">
                                    <c:if test="${tarea.arealevel!=3}">
                                        <option value="">-区(县)-</option>
                                    </c:if>
                                    <c:forEach var="county" items="${countyList }" >
                                        <option value="${county.code }" <c:if test="${fn:substring(areacode,0,6).concat('00')==county.code }">selected</c:if> >${county.name }</option>
                                    </c:forEach>
                                </select>
                                <input type="hidden" name="belongAreaId"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-1" style="width:375px;">
                        <label for="filename" class="col-sm-3 control-label">案卷名称</label>
                        <div class="col-sm-10" style="width:180px;">
                            <input autocomplete="off" type="text" class="form-control" id="filename" name="filename" value="${files.filename}" >
                        </div>
                    </div>
                    <div class="col-lg-1" style="width:375px;">
                        <label class="col-sm-3 control-label">分数</label>
                        <div class="col-sm-10" style="width:250px;">
                            <input autocomplete="off"  class="form-control" style="width:100px;height: 32px;" id="startFinalScore" name="startFinalScore"
                                   value="${files.startFinalScore}"  onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')" ><span style="color: #333"> 至</span>

                            <input autocomplete="off" style="width:100px;height: 32px;border:1px solid #ccc;border-radius: 4px" id="endFinalScore" name="endFinalScore"
                                   value="${files.endFinalScore}" onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')">
                        </div>
                    </div>
                    <div class="col-lg-1" style="width:375px;">
                        <label for="filename" class="col-sm-3 control-label">是否材料严重不全</label>
                        <div class="col-sm-10" style="width:180px;">
                            <select class="form-control" style="width:70px;height: 32px;padding: 0" id="fileMaterials" name="fileMaterials">
                                    <option value=""></option>
                                    <option value="1">是</option>
                                    <option value="0">否</option>
                            </select>
                        </div>
                    </div>
                </div>
            </form>
            <!---搜索--->
            <div style="width:80%">
                <div class="row">
                    <div class="col-lg-12" style="margin-top: 15px">
                           <span class="input-group-btn" style="float: right">
                              <button class="btn btn-success" type="button" onclick="search()">
                                 查询
                              </button>
                              <button type="button" style="margin-left: 20px" class="btn btn-danger" id="outputExcel">导出EXCEL</button>
                           </span>
                    </div>
                </div>
            </div>
            <div>
            </div>
            <div>
                <div style=" height: 55%;margin-left: 200px">
                    <table  class="table table-bordered table-hover table-condensed" style="color:#666666;width: 1000px">
                        <thead>
                        <tr style="text-align: center;">
                           <td>序号</td>
                           <td>省</td>
                           <td>市</td>
                           <td>县</td>
                           <td>行政区级别</td>
                           <td>文号</td>
                           <td>分数</td>
                           <td>是否材料严重不全</td>
                        </tr>
                        </thead>
                        <tbody>
                        <c:if test="${not empty pageBean.list }">
                            <c:forEach items="${pageBean.list}" var="item" varStatus="id">
                                <tr>
                                    <td height="30" >${ id.index + 1}</td>
                                    <td>${item.areaName}</td>
                                    <td>${item.cityName}</td>
                                    <td>${item.countryName}</td>
                                    <td>${item.areaLevel}</td>
                                    <td>${item.filecode}</td>
                                    <td>${item.finalscore}</td>
                                    <td>${item.fileMaterials}</td>

                                </tr>
                            </c:forEach>
                        </c:if>
                        </tbody>
                    </table>
                </div>
                <div class="page" style="margin-top: 50px">
                    <span style="padding:12px; float:left; color:#0099cc;">共${pageBean.total}条记录</span>
                    <ul class="pagination" id="pageCon">
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>


<script type="text/javascript">
    var WEBPATH = '${webpath}';

    $(document).ready(function(){
        if("${files.fileMaterials}"){
            $("#fileMaterials").val("${files.fileMaterials}");
        }
        var areacode="${tarea.code }";//行政区划编码
        var arealevel="${tarea.arealevel}";
        if(typeof(areacode)!="undefined"){
            if(typeof(arealevel)!="undefined"&&arealevel!="3"){
                if(areacode.substring(2,4)!="00"){//行政区已具体到市级加载县级
                    business.cascadedful(areacode.substring(0,4)+"0000",'country',areacode);
                }
            }
        };


        //查询回显
        $("#province").find("option[value='${files.provinceCode}']").attr("selected",'selected').change();
        $("#city").find("option[value='${files.cityCode}']").attr("selected",'selected').change();
        $("#country").find("option[value='${files.countryCode}']").attr("selected",'selected').change();

    });

        //分页
    $(document).ready(function(){
        var curentPage = eval('${pageBean.pageNum}');
        var totalPage = eval('${pageBean.pages}');
        if(totalPage>0){
            var options = {
                bootstrapMajorVersion: 3,
                currentPage: curentPage,
                totalPages: totalPage,
                numberOfPages: 5,
                itemTexts: function (type, page, current) {
                    switch (type) {
                        case "first":
                            return "首页";
                        case "prev":
                            return "&laquo;";
                        case "next":
                            return "&raquo;";
                        case "last":
                            return "尾页";
                        case "page":
                            return page;
                    }
                },
                onPageClicked: function (event, originalEvent, type, page) {
                    business.addMainContentParserHtml(WEBPATH+'/sys2021/dossierQuality.do?pageNum='+page,$("#searchForm").serialize());
                }
            };
            $('#pageCon').bootstrapPaginator(options);
        }
    });

    //查询
    function search(){
        business.addMainContentParserHtml(WEBPATH+'/sys2021/dossierQuality.do?'+$("#searchForm").serialize());
    }
   //导出
    $("#outputExcel").click(function(){
        window.location.href= WEBPATH+"/sys2021/downCasweQuality.do?"+$("#searchForm").serialize();
    });

</script>
