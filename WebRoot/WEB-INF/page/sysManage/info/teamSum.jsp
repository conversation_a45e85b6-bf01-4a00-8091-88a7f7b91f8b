<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<script type="text/javascript" src="${webpath}/static/vue/vue.js">
</script>

<div class="center_weizhi">当前位置：系统管理 - 信息统计 - 推荐集体数量</div>
<!--框架内容 开始-->
<div class="center">
<div class="center_list">
    <div class="panel-group" id="accordion">

            <!---搜索--->
            <div style="width:260px; height: 50px; float: right; margin: 15px 70px" class="btn-group">
                <button  onclick="search()" class="btn btn-danger pull-right" id="outputExcel">导出excel</button>
            </div>
    </div>
        <div style="text-align: center; margin-top: 50px; margin-bottom: 15px">
           <span style="font-size:24px;">各省推荐集体详情表</span>
           <input value="0" name="fileType"  style="font-size:12px;" type="checkbox" id="ischange"/>上报不足
        </div>
        <table id="show" class="table table-bordered table-hover table-condensed" style="width:1350px;margin-left: 40px">
         <thead>
           <tr style="text-align: center;">
             <td bgcolor="#efefef">省份</td>
             <td bgcolor="#efefef">市级应推荐数量(4个)</td>
             <td bgcolor="#efefef">县级应推荐数量(6个)</td>
             <td bgcolor="#efefef">个人应推荐数量(10个)</td>
           </tr>
         </thead>
         <tbody class="table table-bordered table-hover table-condensed">
        <c:forEach items="${jiTiNumberList}" var="item" varStatus="status">
            <tr>
                <td>${item.provincePro}</td>
                <td>${item.shi}</td>
                <td>${item.xian}</td>
                <td>${item.geRen}</td>
            </tr>
        </c:forEach>
         </tbody>
       </table>
    </div>
</div>


<script type="text/javascript">
    var fileType ="";
    $("#ischange").each(function () {

        $("#ischange").push($(this).val());
    })

    var scoreType = "${fileType}"

    if (scoreType == '1'){
        $("#ischange").prop("checked",true)
    }



    // function changeP(){
    //
    //     alert("checked");
    // }
    $("#ischange").change(function() {

        scoreType = "0"
        if ($("#ischange").prop("checked")) {
            scoreType = "1"
        }
        business.addMainContentParserHtml(WEBPATH+'/sys2021//teamSum.do?fileType='+fileType+'&scoreType='+scoreType);



    });

    $("#outputExcel").click(function(){
        scoreType = "0"
        if ($("#ischange").prop("checked")) {
            scoreType = "1"
        }
        window.location.href= WEBPATH+'/sys2021/outJiTiNumberExcel.do?scoreType='+scoreType;
    });


</script>