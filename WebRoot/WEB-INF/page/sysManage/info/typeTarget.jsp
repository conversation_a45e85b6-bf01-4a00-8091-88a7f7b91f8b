<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<script type="text/javascript" src="${webpath}/static/vue/vue.js">
</script>
<style>
    table{
        table-layout:fixed;
        word-wrap:break-word;
    }
    td {
        white-space:nowrap;
        overflow:hidden;
        text-overflow: ellipsis;
    }
    th {
        text-align: center;
    }
</style>
<div class="center_weizhi">当前位置：系统管理 - 信息统计 - 各项类型指标表</div>
<!--框架内容 开始-->
<div class="center">
<div class="center_list">
    <div class="panel-group" id="accordion">
        <form role="form" id="formSearch" style="float: left;margin-bottom: 20px;">
            <!---搜索--->
            <div>
                <label>案卷类型：</label>
                <input style="font-size:12px; margin-left: 5px;"   name="fileType"  type="checkbox" value="0" /> 一般行政处罚
                <input style="font-size:12px; margin-left: 10px;" name="fileType" type="checkbox" value="1"/> 按日连续处罚
                <input style="font-size:12px; margin-left: 10px;" name="fileType" type="checkbox" value="6"/> 查封扣押
                <input style="font-size:12px; margin-left: 10px;" name="fileType" type="checkbox" value="7"/> 限产停产
                <input style="font-size:12px; margin-left: 10px;" name="fileType" type="checkbox" value="2"/> 移送行政拘留
                <input style="font-size:12px; margin-left: 10px;" name="fileType" type="checkbox" value="3"/> 涉嫌环境违法犯罪

                <input style="font-size:12px; margin-left: 20px;"  id="typest" name="scoreType" type="checkbox" value="1"/> 实体(只限一票否决率存在)
                <input style="font-size:12px; margin-left: 10px;" id="fileMaterials" name="fileMaterials" type="checkbox" value="1"/> 剔除材料严重不全案卷
            </div>
        </form>
        <!---搜索--->
        <div style="width:260px;float: left;margin-left: 20px;margin-top: -5px">
            <form class="bs-example bs-example-form" role="form">
                <div class="row">
                    <div class="col-lg-6">
                        <div class="input-group">

                           <span class="input-group-btn">
                              <button class="btn btn-success" type="button" onclick="search()">
                                 查询
                              </button>
                              <button type="button" style="margin-left: 20px" class="btn btn-danger" id="outputExcel">导出EXCEL</button>
                           </span>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
        <c:if test="${scoreType == '1' }">
            <table  class="table table-bordered table-hover table-condensed" style="width:80%;margin-left: 100px;">
                <thead>
                <tr style="text-align: center;">
                    <th bgcolor="#efefef" style="width: 50px;">序号</th>
                    <th bgcolor="#efefef" style="width: 150px;">案卷类型</th>
                    <th bgcolor="#efefef" style="width: 300px;">分指标名称</th>
                    <th bgcolor="#efefef" style="width: 120px;">案卷数量</th>
                    <th bgcolor="#efefef" style="width: 120px;">实体得0分案卷数</th>
                    <th bgcolor="#efefef" style="width: 120px;">实体得0分案卷比例</th>
                </tr>
                </thead>
                <tbody>
                <c:forEach var="list" items="${indicatorTypeVos}" varStatus="index">
                    <tr>
                        <td height="30" align="center">${index.index+1}</td>
                        <c:if test="${list.fileType == '0' }">
                            <td>一般行政处罚</td>
                        </c:if>
                        <c:if test="${list.fileType == '1' }">
                            <td>按日连续处罚</td>
                        </c:if>
                        <c:if test="${list.fileType == '2' }">
                            <td>移送行政拘留</td>
                        </c:if>
                        <c:if test="${list.fileType == '3' }">
                            <td>涉嫌环境违法犯罪</td>
                        </c:if>
                        <c:if test="${list.fileType == '6' }">
                            <td>查封扣押</td>
                        </c:if>
                        <c:if test="${list.fileType == '7' }">
                            <td>限产停产</td>
                        </c:if>
                        <td>${list.indexName}</td>
                        <td>${list.fileNumber}</td>
                        <td>${list.fileNumberq}</td>
                        <td>${list.indexAvg}</td>
                    </tr>
                </c:forEach>
                </tbody>
            </table>
        </c:if>
        <c:if test="${scoreType == '0' }">
           <table  class="table table-bordered table-hover table-condensed" style="width:80%;margin-left: 100px;">
             <thead>
               <tr style="text-align: center;">
                 <th bgcolor="#efefef" style="width: 50px;">序号</th>
                 <th bgcolor="#efefef" style="width: 150px;">案卷类型</th>
                 <th bgcolor="#efefef" style="width: 300px;">分指标名称</th>
                 <th bgcolor="#efefef" style="width: 120px;">案卷数量</th>
                 <th bgcolor="#efefef" style="width: 120px;">应评数量（不包含无需评查）</th>
                 <th bgcolor="#efefef" style="width: 120px;">指标得分和</th>
                 <th bgcolor="#efefef" style="width: 120px;">指标分值</th>
                 <th bgcolor="#efefef" style="width: 120px;">指标得分率</th>
                 <th bgcolor="#efefef" style="width: 120px;">一票否决案卷数量(得0分)</th>
                 <th bgcolor="#efefef" style="width: 120px;">一票否决率</th>
               </tr>
             </thead>
             <tbody>
                <c:forEach var="list" items="${indicatorTypeVos}" varStatus="index">
                     <tr>
                         <td height="30" align="center">${index.index+1}</td>
                         <c:if test="${list.fileType == '0' }">
                             <td>一般行政处罚</td>
                         </c:if>
                         <c:if test="${list.fileType == '1' }">
                             <td>按日连续处罚</td>
                         </c:if>
                         <c:if test="${list.fileType == '2' }">
                             <td>移送行政拘留</td>
                         </c:if>
                         <c:if test="${list.fileType == '3' }">
                             <td>涉嫌环境违法犯罪</td>
                         </c:if>
                         <c:if test="${list.fileType == '6' }">
                             <td>查封扣押</td>
                         </c:if>
                         <c:if test="${list.fileType == '7' }">
                             <td>限产停产</td>
                         </c:if>
                         <td>${list.indexName}</td>
                         <td>${list.fileNumber}</td>
                         <td>${list.fileNumberq}</td>
                         <td>${list.indexSum}</td>
                         <td>${list.indexSumSj}</td>
                         <td>${list.indexAvg}</td>
                         <td>${list.defeateSum}</td>
                         <td>${list.defeateAvg}</td>
                     </tr>
                    </c:forEach>
             </tbody>
           </table>
        </c:if>
    </div>
</div>


<script type="text/javascript">
var webPath = '${webpath}';

$(document).ready(function() {

    $("#typest").click(function () {
        if ($("#typest").prop('checked')){
            $("input[name='fileType']").attr("checked",false);
        }
    });
    $("input[name='fileType']").click(function () {

        var flag = false;
        $("input[name='fileType']").each(function(){
            if ($(this).prop("checked")) {
                flag = true;
            }
        })
        if (flag){
            $("#typest").prop("checked",false)
        }
    })
    var fileType = "${fileType}";
    var arr=new Array();
    var files = $("input[name='fileType']")
    arr = fileType.split(',');
    for(var n=0; n<files.length;n++){
        for(var i=0;i<arr.length;i++)
        {
            if (arr[i]==files[n].value) {
                files[n].checked = true;
            }
        }
    }
    var scoreType1 = "${scoreType}"
    if (scoreType1 == '1'){
        $("#typest").prop("checked",true)
    }
    var fileMaterials = "${fileMaterials}"
    if (fileMaterials == '1'){
        $("#fileMaterials").prop("checked",true)
    }

    var chk_value =[];
    $("input[name='fileType']:checked").each(function () {
        chk_value.push($(this).val());
    })

    var scoreType = "0";
    if ($("#typest").prop("checked")) {
        scoreType = "1"
    }
    var fileMaterials = "0";
    if ($("#fileMaterials").prop("checked")) {
        fileMaterials = "1"
    }

    //导出execl表单
    $("#outputExcel").click(function(){
        if (scoreType == "0"){
            if (chk_value==null||chk_value.length==0){
                swal("提示", "请选择案卷类型！", "error");
                return;
            }
        }
        window.location.href= WEBPATH+'/sys2021/downtypeTarget.do?fileMaterials='+fileMaterials+'&scoreType='+scoreType+'&fileType='+chk_value.join();
    });

});

//查询
function search(){
    var chk_value =[];
    $("input[name='fileType']:checked").each(function () {
        chk_value.push($(this).val());
    })
    var scoreType = "0";
    if ($("#typest").prop("checked")) {
        scoreType = "1"
    }
    var fileMaterials = "0";
    if ($("#fileMaterials").prop("checked")) {
        fileMaterials = "1"
    }
    if (scoreType == "0"){
        if (chk_value==null||chk_value.length==0){
            swal("提示", "请选择案卷类型！", "error");
            return;
        }
    }
    business.addMainContentParserHtml(WEBPATH+'/sys2021/typeTarget.do?fileType='+chk_value.join()+'&scoreType='+scoreType+'&fileMaterials='+fileMaterials);
}
</script>
