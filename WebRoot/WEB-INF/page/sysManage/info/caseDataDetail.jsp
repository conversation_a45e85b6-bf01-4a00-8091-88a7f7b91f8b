<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<style>
    table{
        table-layout:fixed;
        word-wrap:break-word;
    }
    td {
        white-space:nowrap;
        overflow:hidden;
        text-overflow: ellipsis;
    }
    th {
        text-align: center;
    }
</style>
<script type="text/javascript" src="${webpath}/static/vue/vue.js">
</script>

<div class="center_weizhi">当前位置：系统管理 - 信息统计 - 案卷成绩详情表</div>
<!--框架内容 开始-->
<div class="center">
<div class="center_list">
    <div class="panel-group" id="accordion">
            <!---快速查询--->
            <form role="form" id="searchForm" class="form-horizontal" style="margin-top: 20px">
                <div class="row">
                    <div class="col-lg-1" style="width:375px;">
                        <label  class="col-sm-3 control-label">参选单位</label>
                        <div style="display: flex">
                        <div>
                            <select class="form-control" style="width:65px;height: 32px;padding: 0"  id="province" name="provinceCode" onchange="business.cascadedful($('#province').val(),'city');">
                                <c:if test="${fn:length(provinceList)>1 }">
                                    <option value="">-省(市)-</option>
                                </c:if>
                                <c:forEach items="${provinceList }" var="prolist">
                                    <option value="${prolist.code }" <c:if test="${fn:substring(areacode,0,2).concat('000000')==prolist.code }">selected</c:if> >${prolist.name }</option>
                                </c:forEach>
                            </select>
                        </div>
                        <div >
                            <select class="form-control" style="height: 32px;padding: 0"  id="city" name="cityCode" onchange="business.cascadedful($('#city').val(),'country');">
                                <c:if test="${tarea.arealevel!=3 && tarea.arealevel!=2}">
                                    <option value="">-地(市)-</option>
                                </c:if>
                                <c:forEach items="${cityList }" var="citylist">
                                    <option value="${citylist.code }" <c:if test="${fn:substring(areacode,0,4).concat('0000')==citylist.code }">selected</c:if> >${citylist.name }</option>
                                </c:forEach>
                            </select>
                        </div>
                        <div>
                            <select class="form-control" style="height: 32px;padding: 0"  id="country" name="countryCode">
                                <c:if test="${tarea.arealevel!=3}">
                                    <option value="">-区(县)-</option>
                                </c:if>
                                <c:forEach var="county" items="${countyList }" >
                                    <option value="${county.code }" <c:if test="${fn:substring(areacode,0,6).concat('00')==county.code }">selected</c:if> >${county.name }</option>
                                </c:forEach>
                            </select>
                            <input type="hidden" name="belongAreaId"/>
                        </div>
                    </div>
                    </div>
                    <div class="col-lg-1" style="width:375px">
                        <label for="fileType" class="col-sm-3 control-label">案卷类型</label>
                        <div class="col-sm-10" style="width:180px">
                            <select class="form-control" id="fileType"  name="fileType">
                                <option value="">请选择案卷类型</option>
                                <option value="0">行政处罚案卷</option>
                                <option value="1">按日计罚案卷</option>
                                <option value="6">查封扣押案卷</option>
                                <option value="7">限产停产案卷</option>
                                <option value="2">移送行政拘留案卷</option>
                                <option value="3">涉嫌犯罪移送案卷</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-lg-1" style="width:375px">
                        <label for="isDiscuss" class="col-sm-3 control-label">案卷轮次</label>
                        <div class="col-sm-10" style="width:150px">
                            <select class="form-control" id="isDiscuss" name="isDiscuss">
                                <option value="">请选择</option>
                                <option value="0">第一轮</option>
                                <option value="1">第二轮</option>
                                <option value="2">第三轮</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-lg-1" style="width:375px">
                        <label for="typicalCase" class="col-sm-3 control-label">典型案例</label>
                        <div class="col-sm-10" style="width:150px">
                            <select class="form-control" id="typicalCase"   name="typicalCase">
                                <option value="">请选择</option>
                                <option value="0">较优</option>
                                <option value="1">反面</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-lg-1" style="width:375px;">
                        <label for="fileMaterials" class="col-sm-5 control-label">是否材料严重不全</label>
                        <div class="col-sm-7" style="width:150px">
                            <select class="form-control" style="margin-left: -20px"   id="fileMaterials"  name="fileMaterials">
                                <option value="">请选择</option>
                                <option value="0">否</option>
                                <option value="1">是</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-lg-1" style="width:335px;">
                        <label for="fileCode" class="col-sm-3 control-label">案卷号</label>
                        <div class="col-sm-10" style="width:150px;margin-left: 10px">
                            <input type="text" class="form-control" id="fileCode" value="${scoreDetailsVo.fileCode}"  name="fileCode">
                        </div>
                    </div>
                    <div class="col-lg-1" style="width:600px;">
                        <label class="col-sm-3 control-label">查询选项：</label>
                        <div class="col-sm-10" style="width:400px;margin-top: 7px;">
                            <input name="searchType" value="0" style="font-size:12px;" type="checkbox"/> 专家评分详情
                            <input name="searchType" value="1" style="font-size:12px;margin-left: 10px" type="checkbox"/> 是否典型案例
                            <input name="searchType" value="2" style="font-size:12px;margin-left: 10px" type="checkbox"/> 推荐理由
                            <input name="searchType" value="3" style="font-size:12px;margin-left: 10px" type="checkbox"/> 相关处罚情况
                        </div>
                    </div>
                </div>
            </form>
            <!---搜索--->
            <div style="width:80%">
                <div class="row">
                    <div class="col-lg-12" style="margin-top: 15px">

                           <span class="input-group-btn" style="float: right">
                              <button class="btn btn-success" type="button" onclick="search()">
                                 查询
                              </button>
                              <button type="button" style="margin-left: 20px" class="btn btn-danger" id="caseDataDetailExcel">导出EXCEL</button>
                           </span>
                    </div>
                </div>
            </div>
        <div>
    </div>
    <div>
        <div style="overflow: scroll; height: 70%;">
            <table id="show" class="table table-bordered table-hover table-condensed" style="color:#666666;">
             <thead>
               <tr style="text-align: center;">
                   <th rowspan="2" bgcolor="#e3f6dd" style="width: 50px;">序号</th>
                   <th rowspan="2" bgcolor="#e3f6dd" style="width: 50px;">区域</th>
                   <th rowspan="2" bgcolor="#e3f6dd" style="width: 50px;">大区</th>
                   <th colspan="3" bgcolor="#e3f6dd" style="width: 300px;">参选单位</th>
                   <th rowspan="2" bgcolor="#e3f6dd" style="width: 80px;">参选单位行政区级别</th>
                   <th rowspan="2" bgcolor="#e3f6dd" style="width: 100px;">案卷类型</th>
                   <th rowspan="2" bgcolor="#e3f6dd" style="width: 200px;">案卷文号</th>
                   <th colspan="3" bgcolor="#e3f6dd" style="width: 300px;">案卷所属地</th>
                   <th rowspan="2" bgcolor="#e3f6dd" style="width: 120px;">案卷使用情况</th>
                   <th rowspan="2" bgcolor="#e3f6dd" style="width: 200px;">参选个人</th>
                   <th rowspan="2" bgcolor="#e3f6dd" style="width: 200px;">身份证号</th>
                   <th colspan="3" bgcolor="#e3f6dd" style="width: 300px;">参选个人所属行政区</th>
                   <th rowspan="2" bgcolor="#e3f6dd" style="width: 80px;">案卷参选类型</th>
                   <th rowspan="2" bgcolor="#e3f6dd" style="width: 80px;">案卷得分</th>
                   <th rowspan="2" bgcolor="#e3f6dd" style="width: 100px;">是否材料严重不全</th>

                   <th rowspan="2" class="handleInfo" bgcolor="#fbe4d7" style="width: 80px;">违法类型</th>
                   <th rowspan="2" class="handleInfo" bgcolor="#fbe4d7" style="width: 80px;">违法行为</th>
                   <th rowspan="2" class="handleInfo" bgcolor="#fbe4d7" style="width: 80px;">处罚依据</th>
                   <th rowspan="2" class="handleInfo" bgcolor="#fbe4d7" style="width: 80px;">处罚种类</th>
                   <th rowspan="2" class="handleInfo" bgcolor="#fbe4d7" style="width: 80px;">罚款金额(万元)</th>
                   <th rowspan="2" class="handleInfo" bgcolor="#fbe4d7" style="width: 80px;">行政命令</th>

                   <th rowspan="2" class="zjxq" bgcolor="#fbe4d7" style="width: 80px;">专家A姓名</th>
                   <th rowspan="2" class="zjxq" bgcolor="#fbe4d7" style="width: 80px;">专家A卷面评分</th>
                   <th rowspan="2" class="zjxq" bgcolor="#fbe4d7" style="width: 80px;">专家A实体评分</th>
                   <th rowspan="2" class="zjxq" bgcolor="#fbe4d7" style="width: 80px;">专家B姓名</th>
                   <th rowspan="2" class="zjxq" bgcolor="#fbe4d7" style="width: 80px;">专家B卷面评分</th>
                   <th rowspan="2" class="zjxq" bgcolor="#fbe4d7" style="width: 80px;">专家B实体评分</th>
                   <th rowspan="2" class="zjxq" bgcolor="#fbe4d7" style="width: 80px;">委员是否合议</th>
                   <th rowspan="2" class="zjxq" bgcolor="#fbe4d7" style="width: 80px;">委员A姓名</th>
                   <th rowspan="2" class="zjxq" bgcolor="#fbe4d7" style="width: 80px;">委员A卷面评分</th>
                   <th rowspan="2" class="zjxq" bgcolor="#fbe4d7" style="width: 80px;">委员A实体评分</th>
                   <th rowspan="2" class="zjxq" bgcolor="#fbe4d7" style="width: 80px;">委员B姓名</th>
                   <th rowspan="2" class="zjxq" bgcolor="#fbe4d7" style="width: 80px;">委员B卷面评分</th>
                   <th rowspan="2" class="zjxq" bgcolor="#fbe4d7" style="width: 80px;">委员B实体评分</th>
                   <th rowspan="2" class="zjxq" bgcolor="#fbe4d7" style="width: 80px;">首席专家是否合议</th>


                   <th rowspan="2" class="isyxuan" bgcolor="#fff1ce" style="width: 80px;">是否推优案件</th>
                   <th rowspan="2" class="zjpy" bgcolor="#fff1ce" style="width: 500px;">推优评语（普通专家A）</th>
                   <th rowspan="2" class="zjpy" bgcolor="#fff1ce" style="width: 500px;">推优评语（普通专家B）</th>
                   <th rowspan="2" class="zjpy" bgcolor="#fff1ce" style="width: 500px;">推优评语（专家委员A）</th>
                   <th rowspan="2" class="zjpy" bgcolor="#fff1ce" style="width: 500px;">推优评语（专家委员B）</th>
                   <th rowspan="2" class="zjpy" bgcolor="#fff1ce" style="width: 80px;">推优评语（首席专家）</th>
                   <th rowspan="2" class="isyxuan" bgcolor="#fff1ce" style="width: 80px;">是否反面典型</th>
                   <th rowspan="2" class="zjpy" bgcolor="#fff1ce" style="width: 500px;">评语（普通专家A）</th>
                   <th rowspan="2" class="zjpy" bgcolor="#fff1ce" style="width: 500px;">评语（普通专家B）</th>
                   <th rowspan="2" class="zjpy" bgcolor="#fff1ce" style="width: 500px;">评语（专家委员A）</th>
                   <th rowspan="2" class="zjpy" bgcolor="#fff1ce" style="width: 500px;">评语（专家委员B）</th>
                   <th rowspan="2" class="zjpy" bgcolor="#fff1ce" style="width: 80px;">评语（首席专家）</th>

               </tr>
               <tr style="text-align: center;">
                   <th bgcolor="#e3f6dd" style="word-wrap:break-all; width: 100px">省</th>
                   <th bgcolor="#e3f6dd" style="word-wrap:break-all; width: 100px">市</th>
                   <th bgcolor="#e3f6dd" style="word-wrap:break-all; width: 100px">县</th>
                   <th bgcolor="#e3f6dd" style="word-wrap:break-all; width: 100px">省</th>
                   <th bgcolor="#e3f6dd" style="word-wrap:break-all; width: 100px">市</th>
                   <th bgcolor="#e3f6dd" style="word-wrap:break-all; width: 100px">县</th>
                   <th bgcolor="#e3f6dd" style="word-wrap:break-all; width: 100px">省</th>
                   <th bgcolor="#e3f6dd" style="word-wrap:break-all; width: 100px">市</th>
                   <th bgcolor="#e3f6dd" style="word-wrap:break-all; width: 100px">县</th>
               </tr>
             </thead>
             <tbody>
                <c:if test="${not empty pageBean.list }">
                    <c:forEach  varStatus="id"  items="${pageBean.list}" var="scoreDetail">
                        <tr>
                            <td height="30" align="center" >${ id.index + 1}</td>
                            <td>${scoreDetail.part}</td>
                            <td>${scoreDetail.areaGroup}</td>
                            <td>${scoreDetail.electionProvince}</td>
                            <td>${scoreDetail.electionCity}</td>
                            <td>${scoreDetail.electionCountry}</td>
                            <td>${scoreDetail.electionLevel}</td>
                            <td>${scoreDetail.fileTypeName}</td>
                            <td>${scoreDetail.fileCode}</td>
                            <td>${scoreDetail.belongProvince}</td>
                            <td>${scoreDetail.belongCity}</td>
                            <td>${scoreDetail.belongCountry}</td>
                            <td>${scoreDetail.type}</td>
                            <td>${scoreDetail.proName}</td>
                            <td>${scoreDetail.proCode}</td>
                            <td>${scoreDetail.proProvince}</td>
                            <td>${scoreDetail.proCity}</td>
                            <td>${scoreDetail.proCountry}</td>
                            <td>${scoreDetail.recommendFiles}</td>
                            <td>${scoreDetail.expertConsiderScore}</td>
                            <td>${scoreDetail.fileMaterials}</td>

                            <td class="handleInfo">${scoreDetail.weifaleixing}</td>
                            <td class="handleInfo">${scoreDetail.weiFaXingWei}</td>
                            <td class="handleInfo">${scoreDetail.chuFaYiJu}</td>
                            <td class="handleInfo">${scoreDetail.chuFaZhongLei}</td>
                            <td class="handleInfo">${scoreDetail.faKuanShuE}</td>
                            <td class="handleInfo">${scoreDetail.xingZhengMingLing}</td>

                            <td class="zjxq">${scoreDetail.expertAName}</td>
                            <td class="zjxq">${scoreDetail.paperScore1}</td>
                            <td class="zjxq">${scoreDetail.entityScore1}</td>
                            <td class="zjxq">${scoreDetail.expertBName}</td>
                            <td class="zjxq">${scoreDetail.paperScore2}</td>
                            <td class="zjxq">${scoreDetail.entityScore2}</td>
                            <td class="zjxq">${scoreDetail.isConsider}</td>
                            <td class="zjxq">${scoreDetail.expertCommitAName}</td>
                            <td class="zjxq">${scoreDetail.paperScore3}</td>
                            <td class="zjxq">${scoreDetail.entityScore3}</td>
                            <td class="zjxq">${scoreDetail.expertCommitBName}</td>
                            <td class="zjxq">${scoreDetail.paperScore4}</td>
                            <td class="zjxq">${scoreDetail.entityScore4}</td>
                            <td class="zjxq">${scoreDetail.isConsiderExpCommit}</td>

                            <td class="isyxuan">${scoreDetail.yxdxAnLiTuiJian}</td>
                            <td class="zjpy" title="${scoreDetail.yxdxAnLiTuiJianReviews1}">${scoreDetail.yxdxAnLiTuiJianReviews1}</td>
                            <td class="zjpy" title="${scoreDetail.yxdxAnLiTuiJianReviews2}">${scoreDetail.yxdxAnLiTuiJianReviews2}</td>
                            <td class="zjpy" title="${scoreDetail.yxdxAnLiTuiJianReviews3}">${scoreDetail.yxdxAnLiTuiJianReviews3}</td>
                            <td class="zjpy" title="${scoreDetail.yxdxAnLiTuiJianReviews4}">${scoreDetail.yxdxAnLiTuiJianReviews4}</td>
                            <td class="zjpy" title="${scoreDetail.yxdxAnLiTuiJianReviews5}">${scoreDetail.yxdxAnLiTuiJianReviews5}</td>
                            <td class="isyxuan">${scoreDetail.ajpjYxdxanlituijian}</td>
                            <td class="zjpy" title="${scoreDetail.ajpjYxdxanlituijianreviews1}">${scoreDetail.ajpjYxdxanlituijianreviews1}</td>
                            <td class="zjpy" title="${scoreDetail.ajpjYxdxanlituijianreviews2}">${scoreDetail.ajpjYxdxanlituijianreviews2}</td>
                            <td class="zjpy" title="${scoreDetail.ajpjYxdxanlituijianreviews3}">${scoreDetail.ajpjYxdxanlituijianreviews3}</td>
                            <td class="zjpy" title="${scoreDetail.ajpjYxdxanlituijianreviews4}">${scoreDetail.ajpjYxdxanlituijianreviews4}</td>
                            <td class="zjpy" title="${scoreDetail.ajpjYxdxanlituijianreviews5}">${scoreDetail.ajpjYxdxanlituijianreviews5}</td>

                        </tr>
                    </c:forEach>
                </c:if>
             </tbody>
           </table>

            <div class="page" style="margin-top: 28px">
                <span style="padding:12px; float:left; color:#0099cc;">共${pageBean.total}条记录</span>
                <ul class="pagination" id="pageCon">
                </ul>
            </div>
        </div>
    </div>
    </div>
</div>
</div>


<script type="text/javascript">
    var WEBPATH = '${webpath}';

$(document).ready(function(){
    var areacode="${tarea.code }";//行政区划编码
    var arealevel="${tarea.arealevel}";
    if(typeof(areacode)!="undefined"){
        if(typeof(arealevel)!="undefined"&&arealevel!="3"){
            if(areacode.substring(2,4)!="00"){//行政区已具体到市级加载县级
                business.cascadedful(areacode.substring(0,4)+"0000",'country',areacode);
            }
        }
    };

});
//分页
$(document).ready(function(){

    //导出execl表单
    $("#caseDataDetailExcel").click(function(){
        window.location.href= WEBPATH+'/sys2021/caseDataDetailExcel.do?'+$("#searchForm").serialize();
    });


    //查询回显
    $("#province").find("option[value='${scoreDetailsVo.provinceCode}']").attr("selected",'selected').change();
    $("#city").find("option[value='${scoreDetailsVo.cityCode}']").attr("selected",'selected').change();
    $("#country").find("option[value='${scoreDetailsVo.countryCode}']").attr("selected",'selected').change();
    $("#fileType").find("option[value='${scoreDetailsVo.fileType}']").attr("selected",'selected');
    $("#isDiscuss").find("option[value='${scoreDetailsVo.isDiscuss}']").attr("selected",'selected');
    $("#typicalCase").find("option[value='${scoreDetailsVo.typicalCase}']").attr("selected",'selected');
    $("#fileMaterials").find("option[value='${scoreDetailsVo.fileMaterials}']").attr("selected",'selected');

    var searchType = '${searchType}';
    $("input[name='searchType']").each(function () {
        if (searchType.indexOf($(this).val())!=-1){
            $(this).prop('checked',true)
        }
    });
    if (searchType.indexOf('0')!=-1){
        $(".zjxq").show();
    }else {
        $(".zjxq").hide();
    }
    if (searchType.indexOf('1')!=-1){
        $(".isyxuan").show();
    }else {
        $(".isyxuan").hide();
    }
    if (searchType.indexOf('2')!=-1){
        $(".zjpy").show();
    }else {
        $(".zjpy").hide();
    }

    if (searchType.indexOf('3')!=-1){
        $(".handleInfo").show();
    }else {
        $(".handleInfo").hide();
    }
    


    var curentPage = eval('${pageBean.pageNum}');
    var totalPage = eval('${pageBean.pages}');
    if(totalPage>0){
        var options = {
            bootstrapMajorVersion: 3,
            currentPage: curentPage,
            totalPages: totalPage,
            numberOfPages: 5,
            itemTexts: function (type, page, current) {
                switch (type) {
                    case "first":
                        return "首页";
                    case "prev":
                        return "&laquo;";
                    case "next":
                        return "&raquo;";
                    case "last":
                        return "尾页";
                    case "page":
                        return page;
                }
            },
            onPageClicked: function (event, originalEvent, type, page) {
                business.addMainContentParserHtml(WEBPATH+'/sys2021/caseDataDetail.do?pageNum='+page,$("#searchForm").serialize());
            }
        };
        $('#pageCon').bootstrapPaginator(options);
    }
});

//查询
function search(){
    business.addMainContentParserHtml(WEBPATH+'/sys2021/caseDataDetail.do?'+$("#searchForm").serialize());
}
</script>
