<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<script type="text/javascript" src="${webpath}/static/vue/vue.js">
</script>

<div class="center_weizhi">当前位置：系统管理 - 信息统计 - 案卷抽取数量</div>
<!--框架内容 开始-->
<div class="center">
<div class="center_list">
    <div class="panel-group" id="accordion">
            <!---搜索--->
            <div style="width:260px; height: 50px; float: right; margin: 15px 0" class="btn-group">
                <button class="btn btn-danger pull-right" id="outputExcel">导出excel</button>
            </div>            
    </div>
    <div style="text-align: center; margin-top: 50px; margin-bottom: 10px">
       <span style="font-size:24px;">各推荐集体抽取、推荐案卷数量表</span>
    </div>
    <div>
        <button class="btn btn-danger " id="team">集体</button>
        <button class="btn btn-primary " style="margin-left:-5px" id="person"  >个人</button>
    </div>


        <div style="overflow: scroll; height: 80%;">
                    <%--集体--%>
                    <c:if test="${collPerType == '0'}">
                    <div id="teamDiv">
                        <table id="tab" class="table table-bordered table-hover table-condensed" style="max-width: 1100px;margin-left: 100px">
                            <thead>
                            <tr style="text-align: center;">
                                <td bgcolor="#efefef" width="50px">序号</td>
                                <td bgcolor="#efefef" width="150px">省</td>
                                <td bgcolor="#efefef" width="150px">市</td>
                                <td bgcolor="#efefef" width="150px">县</td>
                                <td bgcolor="#efefef" width="150px">级别</td>
                                <td bgcolor="#efefef" width="150px">案卷总数</td>
                                <td bgcolor="#efefef" width="150px">应抽数量</td>
                                <td bgcolor="#efefef" width="150px">实抽数量</td>
                                <td bgcolor="#efefef" width="150px">一般行政处罚</td>
                                <td bgcolor="#efefef" width="150px">按日联系处罚</td>
                                <td bgcolor="#efefef" width="150px">查封扣押</td>
                                <td bgcolor="#efefef" width="150px">限产停产</td>
                                <td bgcolor="#efefef" width="150px">行政拘留</td>
                                <td bgcolor="#efefef" width="150px">涉嫌犯罪</td>
                                <td bgcolor="#efefef" width="150px">申请法院强制执行</td>
                            </tr>
                            </thead>
                            <tbody  class="table table-bordered table-hover table-condensed">
                            <c:forEach items="${pageBean.list}" var="item" varStatus="status">
                                <tr>
                                    <td>${ status.index + 1}</td>
                                    <td>${item.province}</td>
                                    <td style="text-overflow:ellipsis;overflow:hidden; white-space:nowrap">${item.city}</td>
                                    <td style="text-overflow:ellipsis;overflow:hidden; white-space:nowrap">${item.country}</td>
                                    <td>
                                        <c:choose>
                                            <c:when test="${item.areaLevel == '1'}">
                                                省
                                            </c:when>
                                            <c:when test="${item.areaLevel == '2'}">
                                                市
                                            </c:when>
                                            <c:when test="${item.areaLevel == '3'}">
                                                县
                                            </c:when>
                                        </c:choose>

                                    </td>
                                    <td>${item.caseCount}</td>
                                    <td>${item.shouldCount}</td>
                                    <td>${item.practicalCount}</td>
                                    <td>${item.yiBan}</td>
                                    <td>${item.anRi}</td>
                                    <td>${item.chaFeng}</td>
                                    <td>${item.xianChan}</td>
                                    <td>${item.xingZheng}</td>
                                    <td>${item.sheXian}</td>
                                    <td>${item.shenQing}</td>
                                </tr>
                            </c:forEach>
                            </tbody>
                        </table>
                    </div>
                    </c:if>
                     <%--个人--%>
                     <c:if test="${collPerType == '1'}">
                         <div id="personDiv" >
                             <table id="gerenTab" class="table table-bordered table-hover table-condensed" style="max-width: 1100px;margin-left: 100px">
                                 <thead>
                                 <tr style="text-align: center;">
                                     <td bgcolor="#efefef" width="50px">序号</td>
                                     <td bgcolor="#efefef" width="150px">省</td>
                                     <td bgcolor="#efefef" width="150px">市</td>
                                     <td bgcolor="#efefef" width="150px">县</td>
                                     <td bgcolor="#efefef" width="150px">个人姓名</td>
                                     <td bgcolor="#efefef" width="150px">身份证号</td>
                                     <td bgcolor="#efefef" width="150px">应推荐数量</td>
                                     <td bgcolor="#efefef" width="150px">实际推荐数量</td>
                                 </tr>
                                 </thead>
                                 <tbody  class="table table-bordered table-hover table-condensed">
                                 <c:forEach items="${pageBean.list}" var="item" varStatus="status">
                                     <tr>
                                         <td>${ status.index + 1}</td>
                                         <td>${item.provincePro}</td>
                                         <td>${item.cityPro}</td>
                                         <td>${item.countryPro}</td>
                                         <td>${item.namePro}</td>
                                         <td>${item.cardidPro}</td>
                                         <td>${item.shouldRecomCount}</td>
                                         <td>${item.realityRecomCount}</td>
                                     </tr>
                                 </c:forEach>
                                 </tbody>
                             </table>
                         </div>
                     </c:if>
                        <div class="page" style="margin-top: 50px">
                            <span style="padding:12px; float:left; color:#0099cc;">共${pageBean.total}条记录</span>
                            <ul class="pagination" id="pageCon">
                            </ul>
                        </div>
        </div>

    </div>

</div>


<script type="text/javascript">
var webPath = '${webpath}';
   var collPerType = '${collPerType}' ;

$(document).ready(function() {
    // 集体 分页
	var curentPage = eval('${pageBean.pageNum}');
	var totalPage = eval('${pageBean.pages}');
        if(totalPage>0){
            var options = {
                bootstrapMajorVersion: 3,
                currentPage: curentPage,
                totalPages: totalPage,
                numberOfPages: 5,
                itemTexts: function (type, page, current) {
                    switch (type) {
                        case "first":
                            return "首页";
                        case "prev":
                            return "&laquo;";
                        case "next":
                            return "&raquo;";
                        case "last":
                            return "尾页";
                        case "page":
                            return page;
                    }
                },
                onPageClicked: function (event, originalEvent, type, page) {
                    console.log(collPerType)
                    business.addMainContentParserHtml(WEBPATH+'/sys2021/extractedSum.do?pageNum='+page+"&collPerType="+collPerType);
                }
            };
            $('#pageCon').bootstrapPaginator(options);
        }
	});

/*   // 集体个人回显
   if(collPerType == '0'){
       $("#teamDiv").show();
       $("#personDiv").hide();
   }
   if(collPerType == '1'){
       $("#teamDiv").hide();
       $("#personDiv").show();
   }*/


    //个人 显示与隐藏
    $("#person").click(function(){

        business.addMainContentParserHtml(WEBPATH+'/sys2021/extractedSum.do?pageNum=0&collPerType=1');
        $("#teamDiv").hide();
        $("#personDiv").show();
    })
    //集体 显示与隐藏
    $("#team").click(function(){

        business.addMainContentParserHtml(WEBPATH+'/sys2021/extractedSum.do?pageNum=0&collPerType=0');
        $("#teamDiv").show();
        $("#personDiv").hide();
    })

    //导出
    $("#outputExcel").click(function(){
        if(collPerType === '0'){
            window.location.href= WEBPATH+"/sys2021/downExtracted.do?collPerType=0";
        }
        if(collPerType === '1'){
            window.location.href= WEBPATH+"/sys2021/downExtracted.do?collPerType=1";
        }

    });
	
</script>
