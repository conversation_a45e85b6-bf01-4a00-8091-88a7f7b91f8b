<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<script type="text/javascript" src="${webpath}/static/vue/vue.js">
</script>



<div class="center_weizhi">当前位置：系统管理 - 信息统计 - 各地案卷分数表</div>
<!--框架内容 开始-->
<div class="center">
<div class="center_list">
    <div class="panel-group" id="accordion">
            <!---快速查询--->
            <form role="form" class="form-horizontal">
                <div class="row">
                    <div class="col-lg-1" style="width:375px">
                        <label for="areaCode" class="col-sm-3 control-label">行政区划</label>
                        <div class="col-sm-10" style="width:150px">
                            <select class="form-control" id="areaCode" name="areaCode">
                                <option value="11000000">北京市</option>
                                <option value="12000000">天津市</option>
                                <option value="13000000">河北省</option>
                                <option value="14000000">山西省</option>
                                <option value="15000000">内蒙古自治区</option>
                                <option value="16000000">河南省</option>
                                <option value="21000000">辽宁省</option>
                                <option value="22000000">吉林省</option>
                                <option value="23000000">黑龙江省</option>
                                <option value="31000000">上海市</option>
                                <option value="32000000">江苏省</option>
                                <option value="33000000">浙江省</option>
                                <option value="34000000">安徽省</option>
                                <option value="35000000">福建省</option>
                                <option value="36000000">江西省</option>
                                <option value="37000000">山东省</option>
                                <option value="42000000">湖北省</option>
                                <option value="43000000">湖南省</option>
                                <option value="44000000">广东省</option>
                                <option value="45000000">广西壮族自治区</option>
                                <option value="46000000">海南省</option>
                                <option value="51000000">四川省</option>
                                <option value="52000000">贵州省</option>
                                <option value="53000000">云南省</option>
                                <option value="54000000">西藏自治区</option>
                                <option value="55000000">重庆市</option>
                                <option value="61000000">陕西省</option>
                                <option value="62000000">甘肃省</option>
                                <option value="63000000">青海省</option>
                                <option value="64000000">宁夏回族自治区</option>
                                <option value="65000000">新疆维吾尔自治区</option>
                                <option value="66000000">新疆建设兵团</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-lg-1" style="width:375px">
                        <label for="caseSort" class="col-sm-3 control-label">案卷排序</label>
                        <div class="col-sm-10" style="width:150px">
                            <select class="form-control" id="caseSort" name="caseSort">
                                <option value="0">高</option>
                                <option value="1">低</option>
                            </select>
                        </div>
                    </div>
                </div>
            </form>
            <!---搜索--->
            <div class="btn-group" style="width:90%">
                <button class="btn btn-danger pull-right" id="submit" style="width:87px;">查询</button>
            </div>
    </div>
    <div>
        <div style="width:90%" class="btn-group">
            <button class="btn btn-danger pull-right" id="outputExcel">导出excel</button>
        </div>
        <div>
            <table id="show" class="table table-bordered table-hover table-condensed" style="width:1200px;margin-left: 100px">
             <thead>
               <tr style="text-align: center;">
                   <td bgcolor="#efefef" style="width:500px;">序号</td>
                   <td bgcolor="#efefef" style="width:500px;">行政区</td>
                   <td bgcolor="#efefef" style="width:1200px;">案卷文号</td>
                   <td bgcolor="#efefef" style="width:500px;">分数</td>
             </thead>
             <tbody>
                <tr>
                    <td>1</td>
                    <td>珠海市</td>
                    <td>珠环罚字[2021]52号</td>
                    <td>89.19</td>
                </tr>
                <tr>
                    <td>2</td>
                    <td>广东省</td>
                    <td>汕环陆丰罚字〔2021〕4号</td>
                    <td>87.43</td>
                </tr>
             </tbody>
           </table>
        </div>
    </div>
    </div>
</div>


<script type="text/javascript">
var webPath = '${webpath}';

$(document).ready(function() {
	var curentPage = eval('${pageBean.pageNum}');
	var totalPage = eval('${pageBean.pages}');
	});

	
</script>
