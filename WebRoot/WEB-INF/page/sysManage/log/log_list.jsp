<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<html>
<head>
</head>
<script type="text/javascript">
	//分页
	$(document).ready(function(){
		var curentPage = eval('${pageBean.pageNum}');
		var totalPage = eval('${pageBean.pages}');
		if(totalPage>0){
			var options = {
				bootstrapMajorVersion: 3,
			    currentPage: curentPage,
			    totalPages: totalPage,
			    numberOfPages: 5,
			    itemTexts: function (type, page, current) {
			            switch (type) {
			            case "first":
			                return "首页";
			            case "prev":
			                return "&laquo;";
			            case "next":
			                return "&raquo;";
			            case "last":
			                return "尾页";
			            case "page":
			                return page;
			            }
			    },
			    onPageClicked: function (event, originalEvent, type, page) {
	            	business.addMainContentParserHtml(WEBPATH+'/sys/logSeach.do?pageNum='+page,$("#searchForm").serialize());
	            }
	    	};
	    	$('#pageCon').bootstrapPaginator(options);
    	}
	});
	
			 $(document).ready(function(){
				    $("#goods-info-start").datetimepicker({
				        format:'yyyy-mm-dd',
				        todayBtn: true,
				        autoclose: true,
				        minView:'year',
				          maxView:'decade'
				        
				        }).on("click",function(ev){
				            $("#goods-info-start").datetimepicker("setEndDate", $("#goods-info-start2").val());
				      });
				    $("#goods-info-start2").datetimepicker({
				        format:'yyyy-mm-dd',
				        todayBtn: true,
				        autoclose: true,
				        minView:'year',
				          maxView:'decade'
				        
				        }).on("click",function(ev){
				        	 $("#goods-info-start2").datetimepicker("setStartDate", $("#goods-info-start").val());
				      });
		    
		    $("#searchButt").click(function(){
		    var dateStart = $("#goods-info-start").val();
		    var dateEnd = $("#goods-info-start2").val();
		    var ip = $("#ip").val();

		/*      if(dateStart == null || dateStart == '') {
		    	swal("错误", "请输入开始查询时间！", "error");
		    } else if(dateEnd == null || dateEnd == ''){
		    	swal("错误", "请输入结束查询时间！", "error");
		    }else{
		    }  */
		    	business.addMainContentParserHtml("sys/logSeach.do",$("#searchForm").serialize());
		   /*  if((dateStart == null || dateStart == '') && (dateEnd == null || dateEnd == '')){
		    	business.addMainContentParserHtml("sys/logSeach.do",$("#searchForm").serialize());
		    } */
		    });
		  });
</script>
<body>
<div class="center_weizhi" id ="top_title">当前位置:日志列表</div>
<div class="center">
<div class="center_list">
    <div class="panel-group" id="accordion">
        <h4 class="panel-title">
         <form role="form" method="post" id ="searchForm" name ="form">
             <div  style="height:40px; float: left; font-weight: normal; font-size:15px; padding-top: 7px" >
          	    开始时间： </div>
          	 <div  style="width:190px; height:40px; float: left; padding-top: 1px"><input type="text" id="goods-info-start"    value="${dateStart}"   name="dateStart" class="form-control"  placeholder="开始查询时间"  data-date-format="yyyy-mm-dd"></div>
              <div  style="height:40px; float: left; font-weight: normal; font-size:15px; padding-top: 7px" >
              &nbsp;&nbsp; 结束时间： </div>
              <div  style="width:190px; height:40px; float: left; padding-top: 1px"><input type="text" id="goods-info-start2"   value="${dateEnd}" name="dateEnd"  class="form-control" placeholder="结束查询时间" data-date-format="yyyy-mm-dd"></div>
              &nbsp;
                  <div  style="height:40px; float: left; font-weight: normal; font-size:15px; padding-top: 7px" > &nbsp; &nbsp;IP：</div>
              <div  style="width:190px; height:40px; float: left; padding-top: 1px"> <input type="text" style ="height:35px; border-radius:7px" id ="ip" class="form-control" value ="${ip }" name="ip" placeholder="ip关键字" /></div>
              &nbsp;
              <button class="btn btn-success" id ="searchButt" type="button">
                                 快速搜索
              </button>
           </form>
          </h4>
        <br/>
      </div>
      <form id ="searchForm" method="post">
        <table class="table table-bordered table-hover table-condensed" >
         <thead>
           <tr>
             <td width="50" height="30" bgcolor="#efefef">序号</td>
             <td bgcolor="#efefef">登录id</td>
             <td width="100" bgcolor="#efefef">操作人姓名</td>
             <td width="100" bgcolor="#efefef">登录ip</td>
             <td width="120" bgcolor="#efefef">操作数据库类别</td>
             <td width="100" bgcolor="#efefef">操作业务类型</td>
             <td width="100" bgcolor="#efefef">执行结果</td>
             <td width="100" bgcolor="#efefef">执行时间</td>
             <td width="100" bgcolor="#efefef">执行sql</td>
             <td width="100" bgcolor="#efefef">操作方法名</td>
             <td width="100" bgcolor="#efefef">备注</td>
           </tr>
         </thead>
         <tbody>
         <c:if test="${not empty pageBean.list }">
         <c:forEach  varStatus="id"  items="${pageBean.list}" var="logList">
         	<tr>
         		<td height="30" align="center" >${ id.index + 1}</td>
         		<td>${logList.loginid}</td>
         		<td>${logList.username}</td>
         		<td>${logList.ip }</td>
         	    <td>${logList.dbtype }</td>
         		<td>${logList.systype }</td>
         		<td>${logList.result }</td>
         	 	<td><fmt:formatDate value="${logList.optdate}" pattern="yyyy-MM-dd HH:mm:ss"/></td>
              	<td>${logList.optsql }</td>
              	<td>${logList.methodname }</td>
         		<td>${logList.remark }</td>
         	</tr>
         </c:forEach>
         </c:if> 
         </tbody>
       </table>
       <input type="hidden" value ="${ dateStart}" id ="dateStart" name ="dateStart">
       <input type="hidden" value ="${ dateEnd}" id ="dateEnd" name ="dateEnd">
       </form>
    </div>
      <div class="page">
    <span style="padding:12px; float:left; color:#0099cc;">共${pageBean.total}条记录</span>
        <ul class="pagination" id="pageCon">
        </ul>
    </div>
</div>
</body>
</html>