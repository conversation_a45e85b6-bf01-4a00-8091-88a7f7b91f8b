<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>

<script type="text/javascript">
	//分页
	$(document).ready(function(){
		business.listenEnter("searchButt");
		var curentPage = eval('${UnitSlpf_List.pageNum}');
		var totalPage = eval('${UnitSlpf_List.pages}');
		if(totalPage>0){
			var options = {
				bootstrapMajorVersion: 3,
			    currentPage: curentPage,
			    totalPages: totalPage,
			    numberOfPages: 5,
			    itemTexts: function (type, page, current) {
			            switch (type) {
			            case "first":
			                return "首页";
			            case "prev":
			                return "&laquo;";
			            case "next":
			                return "&raquo;";
			            case "last":
			                return "尾页";
			            case "page":
			                return page;
			            }
			    },
			    onPageClicked: function (event, originalEvent, type, page) {
	            	business.addMainContentParserHtml(WEBPATH+'/slpf/numTerms.do?pageNum='+page,$("#searchForm").serialize());
	            }
	    	};
	    	$('#pageCon').bootstrapPaginator(options);
    	}
	});
	
	$(document).ready(function(){
		//快速查询
		$("#areaType").change(function(){
			business.addMainContentParserHtml("slpf/numTerms.do",$("#searchForm").serialize());
		});
		
		//导出excel
		$("#viewExcel").click(function(){
			var areaType = $("#areaType").val();	
			window.location.href= WEBPATH+'/slpf/exportUnitJZBL.do?areaType='+areaType;
		});
		
	});
	
</script>
<div class="center_weizhi">当前位置：信息汇总 - 行政区基准比例列表</div>
<!--框架内容 开始-->

<div class="center">
<div class="center_list">
    <div class="panel-group" id="accordion">
          <!---快速查询--->
          <form id="searchForm" name="searchForm" role="form">
              <select id="areaType" name="areaType" class="form-control" style="width:150px;margin-right:5px;">
                 <option value="" <c:if test="${areaType==''}">selected</c:if>>请选择查询类型</option>
                 <option value="1" <c:if test="${areaType=='1'}">selected</c:if>>省级</option>
                 <option value="2" <c:if test="${areaType=='2'}">selected</c:if>>市级</option>
                 <option value="3" <c:if test="${areaType=='3'}">selected</c:if>>县级</option>
              </select>
        </form>
          
            <!---导出--->
            <div style="width:260px;" class="btn-group">
            <a href="#"><button type="button" class="btn btn-primary" id ="viewExcel">导出EXCEL</button></a>
            </div>            
    </div>
    <table class="table table-bordered table-hover table-condensed">
      <thead>
        <tr>
          <td height="30" bgcolor="#efefef">行政区</td>
          <td width="200" bgcolor="#efefef">行政区域内重点排污企业数量</td>
          <td bgcolor="#efefef">全国重点排污单位总数</td>
          <td bgcolor="#efefef">本单位的基准比例</td>
          <td bgcolor="#efefef">全国查处案件总数</td>
          <td width="180" bgcolor="#efefef">本单位案件数量基准值</td>
          <td bgcolor="#efefef">全国罚款总金额(万元)</td>
          <td width="180" bgcolor="#efefef">本单位罚款金额基准值</td>
          <td bgcolor="#efefef">全国重大案件总数</td>
          <td width="180" bgcolor="#efefef">本单位重大案件数量基准值</td>
        </tr>
      </thead>
      <tbody>
      	<c:forEach items="${UnitSlpf_List.list}" var="UnitSlpf" varStatus="sta">
        <tr>
          <td height="30">${UnitSlpf.province }${UnitSlpf.city }${UnitSlpf.country }</td>
          <td>
          			${UnitSlpf.areaguokongqynum }
          </td>
          <td>
       			<c:if test="${not empty sysFileNumItems }">
       			${sysFileNumItems.countrygkenterprisenum }
       			</c:if>
          </td>
          <td>
          <c:choose>
          	<c:when test="${UnitSlpf.unitjizhunbili==null}">
          	${UnitSlpf.unitjizhunbili }
          	</c:when>
          	<c:otherwise>
          	${UnitSlpf.unitjizhunbili }%
          	</c:otherwise>
          </c:choose>
          </td>
          <td>
          <c:if test="${not empty sysFileNumItems }">
   			${sysFileNumItems.countryhandlcasenum }
   		  </c:if>
          </td>
          <td>${UnitSlpf.unitcasenumbasevalue }</td>
          <td>
          <c:if test="${not empty sysFileNumItems }">
   			${sysFileNumItems.countryfinetotal }
   		  </c:if>
          </td>
          <td>${UnitSlpf.unitfinetotalbasevalue }</td>
          <td>
          <c:if test="${not empty sysFileNumItems }">
   			${sysFileNumItems.countrymajorcasesnum }
   		  </c:if>
          </td>
          <td>${UnitSlpf.unitmajorcasebasevalue }</td>
        </tr>
        </c:forEach>
      </tbody>
    </table>
</div>
    <div class="page">
        <span style="padding:12px; float:left; color:#0099cc;">共${UnitSlpf_List.total}条记录</span>
        <ul class="pagination" id="pageCon">
            
        </ul>
    </div>
</div>




 
 
