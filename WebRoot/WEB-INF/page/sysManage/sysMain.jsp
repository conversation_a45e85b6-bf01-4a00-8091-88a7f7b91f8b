<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<c:set var="webpath">${pageContext.request.contextPath}</c:set>

<script type="text/javascript" src="${webpath}/static/businessJs/sysManage/sysMain.js"></script>
 
<!--框架左侧菜单 开始-->
<div class="frame_left">
	<div class="left_menu">
        <div class="left_menu_bt"><img src="${webpath}/static/images/caiji_1.png" style="margin-right:5px;" />系统管理</div>
        <div class="tree">
            <ul class="topnav" id="secondMenu">
                <li><a href="#" onclick="macroMgr.onLevelTwoMenuClick(null, 'sys/password.do')"  class="active"><img src="${webpath}/static/images/shubiao.png" />密码修改</a></li>
                <c:if test="${sessionScope.sa_session.arealevel == '1'}">
                <li><a href="#"  onclick="macroMgr.onLevelTwoMenuClick(null, 'sys2018/resetPassWord.do')"> <img src="${webpath}/static/images/shubiao.png" />重置下级密码</a></li>
                 </c:if>
<%--                  <div class="left_menu_bt"><img src="${webpath}/static/images/caiji_1.png" style="margin-right:5px;"/>初始化参选单位</div> --%>
<!--                 	 <div class="tree"> -->
<%--                 	 	<li><a href="#"  onclick="macroMgr.onLevelTwoMenuClick(null, 'sys2017/choseCity.do')"> <img src="${webpath}/static/images/shubiao.png" />推选市级</a></li> --%>
<%--                 	 	<c:if test="${sessionScope.sa_session.areaCode != '11000000' and sessionScope.sa_session.areaCode != '12000000'  and sessionScope.sa_session.areaCode != '66000000' and sessionScope.sa_session.areaCode != '31000000' and sessionScope.sa_session.areaCode != '55000000' }"> --%>
                	 	
<%--                 	 	<li><a href="#"  onclick="macroMgr.onLevelTwoMenuClick(null, 'sys2017/choseCounty.do')"> <img src="${webpath}/static/images/shubiao.png" />推选县级</a></li> --%>
                	
<%--                 	</c:if> --%>
                	
                	<!--  </div> -->
<%--                  <div class="left_menu_bt"><img src="${webpath}/static/images/caiji_1.png" style="margin-right:5px;"/>初始化参选个人</div> --%>

<!--                 	 <div class="tree"> -->
<%--                 	 	<li><a href="#"  onclick="macroMgr.onLevelTwoMenuClick(null, 'sys2017/chosePerson.do')"> <img src="${webpath}/static/images/shubiao.png" />参选个人信息</a></li> --%>
<!--                 	 </div> -->
<%--                 </c:if> --%>
                
                <c:if test="${sessionScope.sa_session.loginid == 'changneng' or sessionScope.sa_session.loginid == 'sysAdmin'}"> 
               <li><a href="#"  onclick="macroMgr.onLevelTwoMenuClick(null, 'sys2018/resetPassWord.do')"> <img src="${webpath}/static/images/shubiao.png" />重置下级密码</a></li>
<%--                <li><a href="#"  onclick="macroMgr.onLevelTwoMenuClick(null, 'sys/resetExpPassword.do')"> <img src="${webpath}/static/images/shubiao.png" />重置专家用户密码</a></li>
	                <li><a href="#"  onclick="macroMgr.onLevelTwoMenuClick(null, 'sys/resetCroPassword.do')"> <img src="${webpath}/static/images/shubiao.png" />重置交叉用户密码</a></li> --%>
               <li><a href="#" onclick="macroMgr.onLevelTwoMenuClick(null, 'sys/expertUser.do')"><img src="${webpath}/static/images/shubiao.png" />专家用户管理</a></li>
               <%-- <li><a href="#" onclick="macroMgr.onLevelTwoMenuClick(null, 'sys/crossUser.do')"><img src="${webpath}/static/images/shubiao.png" />交叉用户管理</a></li> --%>
               <li><a href="#" onclick="macroMgr.onLevelTwoMenuClick(null, 'sys2017/countUnit.do')"><img src="${webpath}/static/images/shubiao.png" />参选单位统计</a></li>
<!--               <li><a href="#" onclick="macroMgr.onLevelTwoMenuClick(null, 'slpf/numTerms.do')"><img src="${webpath}/static/images/shubiao.png" />数量评分指标值列表</a></li>-->
       
                </c:if>
             	<c:if test="${sessionScope.sa_session.loginid == 'changneng'}">
                		<li><a href="#" onclick="macroMgr.onLevelTwoMenuClick(null, 'sys/systemInit.do')"><img src="${webpath}/static/images/shubiao.png" />系统初始化设置</a></li>
                		<%--  --%>
                		<li><a href="#" onclick="macroMgr.onLevelTwoMenuClick(null, 'sys/systemStartInt.do')"><img src="${webpath}/static/images/shubiao.png" />2019专家评分及合议模块</a></li>
                		<%-- <li><a href="#" onclick="macroMgr.onLevelTwoMenuClick(null, 'sys/sysFilesAndQuality.do')"><img src="${webpath}/static/images/shubiao.png" />2016案卷&质量、得分模块</a></li> --%>
                		<li><a href="#" onclick="macroMgr.onLevelTwoMenuClick(null, 'sys/sysFilesAndQuality2017.do')"><img src="${webpath}/static/images/shubiao.png" />2019质量得分初始化</a></li>
           				<li><a href="#" onclick="macroMgr.onLevelTwoMenuClick(null, 'sys/numScoreAndRes.do')"><img src="${webpath}/static/images/shubiao.png" />数量&综合、评选结果模块</a></li>
           				<li><a href="#" onclick="macroMgr.onLevelTwoMenuClick(null, 'sys/logList.do')"><img src="${webpath}/static/images/shubiao.png" />日志查询</a></li>
                		<li><a href="#" onclick="macroMgr.onLevelTwoMenuClick(null, 'sys/systemPrivilege.do')"><img src="${webpath}/static/images/shubiao.png" />初始化导入/导出</a></li>
                		<li><a href="#" onclick="macroMgr.onLevelTwoMenuClick(null, 'sys/uploadFile.do')"><img src="${webpath}/static/images/shubiao.png" />单个附件补传</a></li>
                </c:if>
                
                 <c:if test="${sessionScope.sa_session.loginid == 'changneng' or sessionScope.sa_session.loginid == 'sysAdmin'}"> 
                 <div class="left_menu_bt"><img src="${webpath}/static/images/caiji_1.png" style="margin-right:5px;" />随机管理</div>
<%--                  <li><a href="#" onclick="macroMgr.onLevelTwoMenuClick(null, 'sys2017/extractProvince.do')"><img src="${webpath}/static/images/shubiao.png" />省级抽选</a></li> --%>
                 <li><a href="#" onclick="macroMgr.onLevelTwoMenuClick(null, 'sys2017/extractCity.do')"><img src="${webpath}/static/images/shubiao.png" />市级抽选</a></li>
                 <li><a  href="#" onclick="macroMgr.onLevelTwoMenuClick(null, 'sys2017/extractConutry.do')" ><img src="${webpath}/static/images/shubiao.png" />县级抽选</a></li>
                 <c:if test="${sessionScope.sa_session.loginid == 'changneng'}">
              	<div class="left_menu_bt"><img src="${webpath}/static/images/caiji_1.png" style="margin-right:5px;" />评分指标配置</div>
                		<li><a href="#"onclick="macroMgr.onLevelTwoMenuClick(null, 'sys2017/crossQuota.do')"><img src="${webpath}/static/images/shubiao.png"  />交叉指标</a></li>
                		<li><a href="#"onclick="macroMgr.onLevelTwoMenuClick(null, 'sys2017/expertQuota.do')"><img src="${webpath}/static/images/shubiao.png" />专家指标</a></li>
                 </c:if>
                <div class="left_menu_bt"><img src="${webpath}/static/images/caiji_1.png" style="margin-right:5px;" />案卷公开情况</div>
                		<li><a onclick="macroMgr.onLevelTwoMenuClick(null, 'sys2017/filePublic.do')"><img src="${webpath}/static/images/shubiao.png" />参选案卷公开情况</a></li>
               			<li><a onclick="macroMgr.onLevelTwoMenuClick(null, 'sys2017/randomPublic.do')"><img src="${webpath}/static/images/shubiao.png" />随机抽选案卷公开情况</a></li>
                </c:if>
                <c:if test="${sessionScope.sa_session.loginid == 'changneng' or sessionScope.sa_session.loginid == 'sysAdmin'}">
                <div class="left_menu_bt"><img src="${webpath}/static/images/caiji_1.png" style="margin-right:5px;" />信息统计</div>
                <%--<li><a onclick="macroMgr.onLevelTwoMenuClick(null, 'sys2021/caseUpDetail.do')"><img src="${webpath}/static/images/shubiao.png" />案件上报详情</a></li>--%>
                <li><a onclick="macroMgr.onLevelTwoMenuClick(null, 'sys2021/teamSum.do?scoreType=0')"><img src="${webpath}/static/images/shubiao.png" />推荐集体数量</a></li>
                <li><a onclick="macroMgr.onLevelTwoMenuClick(null, 'sys2021/extractedSum.do?collPerType=0')"><img src="${webpath}/static/images/shubiao.png" />案卷抽取数量</a></li>
                <li>
                    <a onclick="macroMgr.onLevelTwoMenuClick(null, 'sys2021/caseGrade.do')"><img src="${webpath}/static/images/shubiao.png" />案卷成绩汇总</a>
                    <ul style="display: block">
                        <li> <a onclick="macroMgr.onLevelTwoMenuClick(null, 'sys2021/dossierQuality.do')"><img src="${webpath}/static/images/shubiao.png" />案卷质量分析表</a></li>
                        <li><a onclick="macroMgr.onLevelTwoMenuClick(null, 'sys2021/caseDataDetail.do')"><img src="${webpath}/static/images/shubiao.png" />案卷成绩详情表</a></li>
                        <li><a onclick="macroMgr.onLevelTwoMenuClick(null, 'sys2021/typeTarget.do?fileType=0&scoreType=0')"><img src="${webpath}/static/images/shubiao.png" />各项类型指标表</a></li>
                    </ul>
                </li>
                <li><a onclick="macroMgr.onLevelTwoMenuClick(null, 'sys2021/caseQuality.do')"><img src="${webpath}/static/images/shubiao.png" />各地案卷分数表</a></li>
                <li><a onclick="macroMgr.onLevelTwoMenuClick(null, 'sys2021/caseDetail.do?fileType=0&targetChoose=0')"><img src="${webpath}/static/images/shubiao.png" />案卷数据明细</a></li>
                </c:if>
            </ul>
        </div>
    </div>
</div>


<!--框架左侧菜单 结束-->

<!-- 页面中部~中间 start  -->
<div id="main_content" class="main_content">


</div>