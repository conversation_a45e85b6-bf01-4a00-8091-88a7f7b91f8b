<%@ page language="java" import="java.util.*,java.lang.*" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<style>
    table{width: 900px;}
    tr{width:100%;}
    td{text-align: left; }
</style>

<div class="center_weizhi">当前位置：系统管理(2019) - 系统初始化设置</div>
<!--框架内容 开始-->
  
<div class="center">
      <div class="center_list">
     <span id ="sysBiaoZhi" hidden="hidden"></span>
        <table class="table_input" style="margin-left: 100px">
         <tbody>
           <tr>
           		<td colspan="3">
           			<hr/>
           		</td>
           </tr>
           <c:forEach items="${initList }" var="init" varStatus="status">
           			<tr>
					             <td>${status.index+1 }、${init.actionname }</td>
					            <td >
					                                         执行结果：<label >${init.result }</label>
					             </td>
					             <td>
					                 <c:if test="${init.flag=='1' }">
                							<button  class="btn btn-primary"  name="signup" value="Sign up" style="font-size:16px;width:250px; margin-top:5px;background-color: gray;">${status.index+1 }、&nbsp;&nbsp;执行操作...</button>
                                     </c:if>
                                     <c:if test="${init.flag=='0' }">
										      <button onclick="submitConfig('${init.code }')" type="button" class="btn btn-primary" name="signup" value="Sign up" style="font-size:16px;width:250px; margin-top:5px;">${status.index+1 }、&nbsp;&nbsp;执行操作...</button>
                                     </c:if>
					             	
					             </td>
					           </tr>
					           <tr>
					           		<td colspan="3">
					           			<hr/>
					           		</td>
                   </tr>
           
           </c:forEach>
           
         </tbody>
       </table>
  </div>
  
</div>
<script type="text/javascript">

function submitConfig(code){
	 var scoredState= eval('${sessionScope.sa_session.sysStatus}');

		if(code =='6' || code =='2' ||code =='7'){// code  =='1'|| code =='46' ||code =='47' ||code =='48' ||
			if(scoredState != 2){
			 	swal({title: "您不在当前阶段！请将系统状态设置到'交叉评审阶段'" ,text: "",type:"info"});
			 	return false;
			}
		}else{
			if(scoredState != 5){
				swal({title: "您不在当前阶段！请将系统状态设置到'专家评审阶段'" ,text: "",type:"info"});
			 	return false;
			}
		}
	    $.ajax({
             type: "POST",
             url: WEBPATH+"/sys2017/sysInitSubmit.do",
             data:{code:code},
             async:false,
             success: function(data){
	             if(data.type=="error"){
	               	swal({title: data.result ,text: "",type:"error"});
	                return false;
	             }else if(data.type=="success"){
	              	swal({title: data.result ,text: "",type:"success"});
	             }
           }
     });
     business.addMainContentParserHtml(WEBPATH+'/sys/systemStartInt.do',null);
}
 
</script>

 
 
