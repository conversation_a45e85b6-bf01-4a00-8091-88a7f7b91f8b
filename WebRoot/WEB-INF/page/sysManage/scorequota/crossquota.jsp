<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<c:set var="vEnter" value="\n" scope="request"/>

<% request.setAttribute("vEnter", "\n"); %>

<div class="center_weizhi">当前位置：系统管理 - 评分指标配置 - 交叉指标</div>
<!--框架内容 开始-->
<div class="center">
<div class="center_list">
    <div class="panel-group" id="accordion">
          <h4 class="panel-title">      
            	<select style="width:200px;margin-right:5px;" class="form-control" id="type">
                      <option value="">案卷类型</option>
                      <!-- <option value="0">行政处罚案卷</option>
                      <option value="1">按日计罚案卷</option>
                      <option value="6">查封扣押案卷</option>
                      <option value="7">限产停产案卷</option>
                      <option value="2">移送行政拘留案卷</option>
                      <option value="3">涉嫌犯罪移送案卷</option>          
                      <option value="5">污染源现场监督检查稽查案卷</option> -->  
                       <option value="99">突出组织评分指标</option>        
                </select>
            <!---搜索--->
            <div style="width:360px;" class="btn-group">
            	<button class="btn btn-success" type="button" style="width:120px;" data-toggle="modal" data-target="#fzb" id="add">
                 增加分指标
              </button>
            </div>            
          </h4>
    </div>
        <table class="table table-bordered table-hover table-condensed">
         <thead>
           <tr>
             <td width="50" height="30" bgcolor="#efefef">序号</td>
             <td bgcolor="#efefef">分指标名称</td>
             <td bgcolor="#efefef" style="width:200px;">分值</td>
             <td bgcolor="#efefef">指标说明</td>
             <td bgcolor="#efefef" style="width:200px;">案卷类型</td>
             <td bgcolor="#efefef" style="width:200px;">状态</td>
             <td bgcolor="#efefef" style="width:100px;">本分指标为加分项或减分项</td>
             <td bgcolor="#efefef" style="width:100px;">本分指标是否适用一票否决</td>
             <td bgcolor="#efefef" style="width:100px;">本分指标是否参与评查</td>
             <td bgcolor="#efefef" style="width:100px;">操作</td>
           </tr>
         </thead>
         <tbody>
           <c:forEach var="list" items="${list}" varStatus="index">
           <tr id = "${list.id}" >
           <td>${index.index+1}</td>
           <td onclick="selectIndex(${list.id})">${list.indexname}</td>
           <td>${list.indexscore}</td>
            <td>${fn:replace(list.indexdesc,vEnter,"<br>")}</td>
           <c:if test="${list.filetype == '0' }">
           <td>行政处罚案卷</td>
           </c:if>
           <c:if test="${list.filetype == '1' }">
           <td>按日计罚案卷</td>
           </c:if>
           <c:if test="${list.filetype == '2' }">
           <td>移送行政拘留案卷</td>
           </c:if>
           <c:if test="${list.filetype == '3' }">
           <td>涉嫌环境污染犯罪</td>
           </c:if>
           <c:if test="${list.filetype == '4' }">
           <td>申请法院强制执行案卷</td>
           </c:if>
           <c:if test="${list.filetype == '5' }">
           <td>发现问题的污染源现场监督检查稽查案卷</td>
           </c:if>
           <c:if test="${list.filetype == '6' }">
           <td>查封扣押案卷</td>
           </c:if>
           <c:if test="${list.filetype == '7' }">
           <td>限产停产案卷</td>
           </c:if>
           <c:if test="${list.filetype == '99' }">
           <td>突出组织评分指标</td>
           </c:if>
           <c:if test="${list.state =='0' }">
           <td>启用</td>
           </c:if>
           <c:if test="${list.state =='1' }">
           <td>未启用</td>
           </c:if>
           <c:if test="${list.isAdd == '1' }">
           	 <td >加项</td>
           </c:if>
           <c:if test="${list.isAdd == '0' }">
           	 <td >减项</td>
           </c:if>
           <c:if test="${list.isVoteDown == '0' }">
           	 <td >否</td>
           </c:if>
           <c:if test="${list.isVoteDown == '1' }">
           	 <td >是</td>
           </c:if>
           <c:if test="${list.isInCheck == '1' }">
           	 <td >否</td>
           </c:if>
           <c:if test="${list.isInCheck == '0' }">
           	 <td >是</td>
           </c:if>
           <td><button class="btn btn-success btn-xs" data-toggle="modal" data-target="#fzb" onclick="edit(${list.id},'${list.indexname}',${list.indexscore},${list.state},${list.sort},'${list.indexDescStr}',${list.isAdd},${list.isVoteDown},${list.isInCheck})">编辑</button> <button class="btn btn-danger btn-xs" onclick="deleteFirst(${list.id})">删除</button></td>
           </tr>
           
           </c:forEach>
           
         </tbody>
       </table>
    </div>
    <div class="panel-group" id="accordion">
          <h4 class="panel-title">      
          
          
            <!---搜索--->
            <div style="width:260px;" class="btn-group">
            	<button id="addItem" class="btn btn-success" type="button" style="width:120px;" data-toggle="modal" data-target="#zbx">
                 增加指标项
              </button>
            </div>            
          </h4>
    </div>
        <table class="table table-bordered table-hover table-condensed">
         <thead>
           <tr>
             <td width="50" height="30" bgcolor="#efefef">序号</td>
             <td bgcolor="#efefef">指标项名称</td>
             <td bgcolor="#efefef" style="width:200px;">分值</td>
             <td bgcolor="#efefef" style="width:200px;">状态</td>
             <td bgcolor="#efefef" style="width:200px;">是否无需评审</td>
             <td bgcolor="#efefef" style="width:100px;">操作</td>
           </tr>
         </thead>
         <tbody>
          
          <c:forEach var="list" items="${listItem}" varStatus="index">
          	<tr>
          		<td height="30" align="center">${index.index+1}</td>
          		<td>${list.itemname}</td>
          		<td>${list.itemscore}</td>
          		<c:if test="${list.state == '0' }">
          			<td>启用</td>
          		</c:if>
          		<c:if test="${list.state == '1' }">
          			<td>未启用</td>
          		</c:if>
         		<c:if test="${list.isNoReview == '0' }">
         			<td>需要评审</td>
          		</c:if>
          		<c:if test="${list.isNoReview == '1' }">
          			<td>无需评审</td>
          		</c:if>
          		 <td><button class="btn btn-success btn-xs" data-toggle="modal" data-target="#zbx" onclick="editDown(${list.id},'${list.itemname}','${list.itemscore}','${list.state}','${list.sort}','${list.isNoReview}')" >编辑</button> <button class="btn btn-danger btn-xs" onclick="deleteItem(${list.id})" >删除</button></td>
          	</tr>
          </c:forEach>
           
         </tbody>
       </table>
    </div>
    
</div>
<!-- 增加分指标（Modal） -->
<div class="modal fade" id="fzb" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title" id="myModalLabel">分指标</h4>
            </div>
            <div class="modal-body form-horizontal"  font-size:16px;">
            	<div class="form-group" style="padding:10px;">
                	<label class="col-lg-3 control-label">分指标名称</label>
                    <div class="col-lg-8">
                		<input type="text" class="form-control" id="scoreName" placeholder="请输入分指标名称">
                    </div>
                </div>
                <div class="form-group" style="padding:2px;">
                	<label class="col-lg-3 control-label">指标说明</label>
                    <div class="col-lg-8">
                		<textarea type="text"  id="indexDesc"  class="form-control" rows="5" placeholder="请输入指标说明"></textarea>
                    </div>
                </div>
            	<div class="form-group" style="padding:10px;">
                	<label class="col-lg-3 control-label">分值</label>
                    <div class="col-lg-8">
                		<input type="text" class="form-control" id="score" placeholder="请输入分值(小数形式)">
                    </div>
                </div>
                <div class="form-group" style="padding:10px;">
                	<label class="col-lg-3 control-label">状态</label>
                    <div class="col-lg-8">
                		<select class="form-control" id="scoreState">
                            <option value = '0'>启用</option>
                            <option value = '1'>未启用</option>
                        </select>
                    </div>
                </div>
                <div class="form-group" style="padding:10px;">
                	<label class="col-lg-3 control-label">排序</label>
                    <div class="col-lg-8">
                		<input type="text" class="form-control" id="scoreSort" placeholder="请输入排序位置">
                    </div>
                </div>
                <div class="form-group" style="padding:2px;">
                	<label class="col-lg-3 control-label">本分指标为加分项或减分项</label>
                    <div class="col-lg-8">
                		<select class="form-control" id="isAdd">
                            <option value="1" >加分项</option>
                            <option value="0">减分项</option>
                        </select>
                    </div>
                </div>
                <div class="form-group" style="padding:2px;">
                	<label class="col-lg-3 control-label">本分指标是否适用一票否决</label>
                    <div class="col-lg-8">
                		<select class="form-control" id="voteDown">
                		  <option value="0">否</option>
                		    <option value="1">是</option>
                          
                        </select>
                    </div>
                </div>
                <div class="form-group" style="padding:2px;">
                	<label class="col-lg-3 control-label">本分指标是否参与评查</label>
                    <div class="col-lg-8">
                    <!-- 为了迎合评分页面，这里0代表是，1代表否，与是否适用一票否决不一样， 切记-->
                		<select class="form-control" id="inCheck">
                            <option value="0">是</option>
                            <option value="1">否</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" onclick="addSorre()" >确定</button>
                <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
<!-- 增加指标项（Modal） -->
<div class="modal fade" id="zbx" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title" id="myModalLabel">指标项</h4>
            </div>
            <div class="modal-body form-horizontal" style="height:300px; font-size:16px;">
            	<div class="form-group" style="padding:10px;">
                	<label class="col-lg-3 control-label">指标项名称</label>
                    <div class="col-lg-8">
                		<input type="text" class="form-control" id="itemName" placeholder="请输入分指标名称">
                    </div>
                </div>
            	<div class="form-group" style="padding:10px;">
                	<label class="col-lg-3 control-label">分值</label>
                    <div class="col-lg-8">
                		<input type="text" class="form-control" id="itemScore" placeholder="请输入分值">
                    </div>
                </div>
                <div class="form-group" style="padding:10px;">
                	<label class="col-lg-3 control-label">状态</label>
                    <div class="col-lg-8">
                		<select class="form-control" id="itemState">
                            <option value = '0'>启用</option>
                            <option value= '1'>未启用</option>
                        </select>
                    </div>
                </div>
                <div class="form-group" style="padding:10px;">
                	<label class="col-lg-3 control-label">排序</label>
                    <div class="col-lg-8">
                		<input type="text" class="form-control" id="itemSort" placeholder="请输入排序位置">
                    </div>
                </div>
                <div class="form-group" style="padding:10px;">
                	<label class="col-lg-3 control-label">是否无需评审</label>
                    <div class="col-lg-8">
                		<select class="form-control" id="isNoReview">
                            <option value = '0'>否</option>
                            <option value = '1'>是</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary"  onclick="submitItem()">确定</button>
                <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!--验证是保存还是编辑  -->
<input hidden="true" value='0' id="isSave">
<input hidden="true" value='' id="scoreID">
<input hidden="true" value='' id="indexID">
<input hidden="true" value='0' id="isSaveDown">
<input hidden="true" value='' id="ItemID">

 
<script type="text/javascript">
var webPath = '${webpath}';
//验证正数
var regSort = /^[1-9]\d*$/;
//验证浮点数
var reg =  new RegExp("(?!^0\.0?0$)^([0-9]|[1-3][0-9])(\.[0-9]{1,2})?$|^(40|40\.0|40\.00|0\.0|0\.00)");

//案卷类型
var fileType = '${type}';

var indexID = '${indexID}';

	function addSorre() {
		var type = $("#type option:selected").val();
		
		
		var scoreName = $("#scoreName").val();
		if(scoreName==''){
			 swal("OMG", "分指标名称不能为空", "error");
			 return false;
		}
		var score = $("#score").val();
		if(score == ''){
			 swal("OMG", "分值不能为空!", "error");
			 return false;
		}else if(!reg.test(score)||score>40){
			 swal("OMG", "分值不符合小数约束,且不能大于40!", "error");
			 return false;
		}
		var scoreState = $("#scoreState option:selected").val();
		if(scoreState==''){
			 swal("OMG", "状态不能为空", "error");
			 return false;
		}
		var scoreSort = $("#scoreSort").val();
		if(scoreSort==''){
			 swal("OMG", "排序不能为空!", "error");
			 return false;
		}else if(!regSort.test(scoreSort)||scoreSort>127){
			swal("OMG", "排序不符合大于0的正整数约束，且不能大于127!", "error");
			 return false;
		}
		var isSave = $("#isSave").val();
		var indexDesc = $("#indexDesc").val();
		if(indexDesc.length>750){
			swal("OMG", "分指标说明不能超过750字符", "error");
			return ;
		}
		var isAdd = $("#isAdd").val();
		var voteDown = $("#voteDown").val();
		var inCheck = $("#inCheck").val();

		/* if(voteDown=='1'&&inCheck=='1'){
			swal("OMG", "是否一票否决为是时，是否参与评查不能为否", "error");
			return ;
		} */
		if(isSave == '0'){
			$.ajax({
				data:{indexname:scoreName,indexscore:score,state:scoreState,sort:scoreSort,isSave:isSave,filetype:fileType,indexDesc:indexDesc,isAdd:isAdd,isVoteDown:voteDown,isInCheck:inCheck},
				dataType:"json",
				type:"POST",
				url:webPath+'/sys2017/addCrossScore.do',
				success:function(data){
					if(data.text =='ok'){
						  $("#fzb").modal('hide');
						 swal("success", "插入成功", "success");
						 macroMgr.onLevelTwoMenuClick(null, 'sys2017/crossQuota.do?fileType='+fileType);
					}else{
						swal("OMG", "插入失败!", "error");
						macroMgr.onLevelTwoMenuClick(null, 'sys2017/crossQuota.do?fileType='+fileType);
					}
				}
			});
		}else{
			var scoreID = $("#scoreID").val();
			$.ajax({
				data:{indexname:scoreName,indexscore:score,state:scoreState,sort:scoreSort,isSave:isSave,filetype:fileType,indexDesc:indexDesc,isAdd:isAdd,isVoteDown:voteDown,isInCheck:inCheck,id:scoreID},
				//data:{scoreName:scoreName,score:score,scoreState:scoreState,scoreSort:scoreSort,isSave:isSave,scoreID:scoreID,scoreType:fileType},
				dataType:"json",
				type:"POST",
				url:webPath+'/sys2017/addCrossScore.do',
				success:function(data){
					if(data.text =='ok'){
						 $("#fzb").modal('hide');
						 swal("success", "更新成功", "success");
						 macroMgr.onLevelTwoMenuClick(null, 'sys2017/crossQuota.do?fileType='+fileType);
					}else{
						swal("OMG", "更新失败!", "error");
						macroMgr.onLevelTwoMenuClick(null, 'sys2017/crossQuota.do?fileType='+fileType);
					}
				}
			});
		}
		
		
		
	}
	function edit(id,indexname,indexscore,state,sort,indexDesc,isAdd,VoteDown,InCheck){

		$("#scoreName").val(indexname);
		$("#score").val(indexscore);
		$("#scoreState").val(state);
		$("#scoreSort").val(sort); 
		$("#isSave").val("1");
		$("#scoreID").val(id);
		$("#indexDesc").val(indexDesc);
		$("#isAdd").val(isAdd);
		$("#voteDown").val(VoteDown);
		$("#inCheck").val(InCheck);
	};
	function editDown(id,itemname,itemscore,state,sort,isNoReview){
		$("#itemName").val(itemname);
		$("#itemScore").val(itemscore);
		$("#itemState").val(state);
		$("#itemSort").val(sort);
		$("#isSaveDown").val("1");
		$("#ItemID").val(id);
		$("#isNoReview").val(isNoReview);
	}
	function deleteItem(id){
		var indexID = $("#indexID").val();
		  swal({
              title: "您确定执行此操作吗？",
              text: "您确定要删除当前项吗？",
              type: "warning",
              showCancelButton: true,
              closeOnConfirm: false,
              confirmButtonText: "是的，我要删除",
              confirmButtonColor: "#ec6c62"
          }, function() {
              $.ajax({
                  url: webPath+"/sys2017/deleteScoreSecond.do",
                  type: "POST",
                  data:{id:id,deletetype:'2'}
              }).done(function(data) {
              
                  if(data=="ok"){
                  	swal("操作成功!", "已成功删除！", "success");
                  	macroMgr.onLevelTwoMenuClick(null, 'sys2017/crossQuota.do?fileType='+fileType+"&indexID="+indexID);
                  }else{
                      swal("OMG", "删除失败了!", "error");
                  }
                  
              }).error(function(data) {
                  swal("OMG", "发生错误", "error");
              });
          });
	}
	function deleteFirst(id){
        swal({
                        title: "您确定执行此操作吗？",
                        text: "您确定要删除当前项吗？",
                        type: "warning",
                        showCancelButton: true,
                        closeOnConfirm: false,
                        confirmButtonText: "是的，我要删除",
                        confirmButtonColor: "#ec6c62"
                    }, function() {
                        $.ajax({
                            url: webPath+"/sys2017/deleteScoreFirst.do",
                            type: "POST",
                            data:{id:id,deletetype:'1'}
                        }).done(function(data) {
                        
                            if(data=="ok"){
                            	swal("操作成功!", "已成功删除！", "success");
                            	macroMgr.onLevelTwoMenuClick(null, 'sys2017/crossQuota.do?fileType='+fileType);
                            }else{
                                swal("OMG", "删除失败了!", "error");
                            }
                            
                        }).error(function(data) {
                            swal("OMG", "发生错误", "error");
                        });
                    });
    }
	
	/* $("#addItem").click(function(){
		
	}); */
	function selectIndex(id){
		//var indexID = $("#indexID").val();
		
		
		$("#indexID").val(id);
		console.log("id"+id);
		
		// macroMgr.onLevelTwoMenuClick(null, 'sys2017/crossQuota.do?fileType='+fileType+"&indexID="+id);
		business.addMainContentParserHtml(WEBPATH+'/sys2017/crossQuota.do?fileType='+fileType+"&indexID="+id);
		$("#addItem").attr("disabled",false);
	}
	function submitItem(){

		var indexId = null;
		if( $("#indexID").val()!=null){
			indexId = $("#indexID").val();
		}else{
			indexId=indexID;
		}
		var itemName = $("#itemName").val();
		if(itemName==''){
			 swal("OMG", "分指标名称不能为空", "error");
			 return false;
		}
		console.log("scoreName..."+itemName);
		var itemScore = $("#itemScore").val();
		if(itemScore == ''){
			 swal("OMG", "分值不能为空!", "error");
			 return false;
		}else if(!reg.test(itemScore)||itemScore>127){
			 swal("OMG", "分值不符合小数约束!", "error");
			 return false;
		}
		var itemState = $("#itemState option:selected").val();
		if(itemState==''){
			 swal("OMG", "状态不能为空", "error");
			 return false;
		}
		var itemSort = $("#itemSort").val();
		console.log("...."+itemSort);
		if(itemSort==''){
			 swal("OMG", "排序不能为空!", "error");
			 return false;
		}else if(!regSort.test(itemSort)||itemSort>127){
			swal("OMG", "排序不符合大于0正整数约束，且不能大于127!", "error");
			 return false;
		}
		var isNoReview = $("#isNoReview option:selected").val();
		if(isNoReview==''){
			 swal("OMG", "是否无需评审不能为空", "error");
			 return false;
		}
		var indexID = $("#indexID").val();
		console.log("..indexID..."+indexID);
		var isSaveDown = $("#isSaveDown").val();
		var itemID = $("#ItemID").val();
		if(isSaveDown == '0'){
			$.ajax({
				data:{indexid:indexId,itemname:itemName,itemscore:itemScore,sort:itemSort,state:itemState,isSaveDown:isSaveDown,isNoReview:isNoReview},
				//dataType:"JSON",
				type:"POST",
				url:webPath+'/sys2017/addScoringItem.do',
				success:function(data){
					if(data == 'ok'){
						 swal("success", "插入成功", "success");
						 $("#zbx").modal('hide');
						 macroMgr.onLevelTwoMenuClick(null, 'sys2017/crossQuota.do?fileType='+fileType+"&indexID="+indexID);
					}else{
						 swal("OMG", "插入失败", "error");
						 macroMgr.onLevelTwoMenuClick(null, 'sys2017/crossQuota.do?fileType='+fileType);
					}
				}
			});
			 
		}else if(isSaveDown == '1'){
			$.ajax({
				data:{indexid:indexId,itemname:itemName,itemscore:itemScore,sort:itemSort,state:itemState,isSaveDown:isSaveDown,id:itemID,isNoReview:isNoReview},
				//dataType:"JSON",
				type:"POST",
				url:webPath+'/sys2017/addScoringItem.do',
				success:function(data){
					if(data == 'ok'){
						 swal("success", "更新成功", "success");
						 $("#zbx").modal('hide');
						 macroMgr.onLevelTwoMenuClick(null, 'sys2017/crossQuota.do?fileType='+fileType+"&indexID="+indexID);
					}else{
						 swal("OMG", "更新失败", "error");
						 macroMgr.onLevelTwoMenuClick(null, 'sys2017/crossQuota.do?fileType='+fileType);
					}
				}
			});
		}
		
		
	}
	
	
	
$(document).ready(function(){
	$("#fzb").on('hide.bs.modal',function(){
		   $(".modal-backdrop").remove();
		   $("#scoreName").val("");
			$("#score").val("");
			$("#scoreState").val("0");
			$("#scoreSort").val(""); 
			$("#indexDesc").val("");
			$("#isAdd").val("1");
			$("#voteDown").val("0");
			$("#inCheck").val("1");
		  
	 });
	
	
	$("#zbx").on('hide.bs.modal',function(){
		  $(".modal-backdrop").remove();
		  $("this").removeData("bs.modal");
	 });
	
	
	
	if(fileType != ''){
		$("#type").val(fileType);
	}
	//当点击增加指标项时，类型为空，不能点击
	$("#add").click(function(){
		var type = $("#type").val();
		if(type == ''){
			swal("OMG", "案卷类型为空!", "error");
			return false;
		}
		
	});
	$("#type").change(function(){
		var type = $("#type").val();
		macroMgr.onLevelTwoMenuClick(null, 'sys2017/crossQuota.do?fileType='+type);
	})
	//控制增加指标项是否可以点击，
	
	if(indexID !=''&& indexID != null){
		$("#indexID").val(indexID);
		$("#"+indexID).css("background-color","#00CC99");
	}
	if($("#indexID").val()!=''){
		$("#addItem").attr("disabled",false);
	}else{
		$("#addItem").attr("disabled",true);
	}
	
})
	
</script>
