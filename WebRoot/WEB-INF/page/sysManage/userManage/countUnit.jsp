<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>


<div class="center_weizhi">当前位置：系统管理 - 参选单位统计</div>
<!--框架内容 开始-->
<div class="center">
   <div class="center_list">
	<div class="panel-group" id="accordion">
          
            <!---搜索--->
            <div  class="btn-group">
              <button type="button" class="btn btn-danger" id="viewExcel">导出EXCEL</button>
              <%--<button type="button" class="btn btn-danger" id="viewExcel1">人员抽取</button>
              <button type="button" class="btn btn-danger" id="viewExcel2">县级抽取</button>
              <button type="button" class="btn btn-danger" id="viewExcel3">市级抽取</button>--%>
            </div>
    </div>
        <table class="table table-bordered table-hover table-condensed">
         <thead>
           <tr>
             <td width="50" height="30" bgcolor="#efefef">序号</td>
             <td bgcolor="#efefef">参选省</td>
             <td bgcolor="#efefef">参选市级个数</td>
             <td bgcolor="#efefef">参选县级个数</td>
           </tr>
         </thead>
         <tbody>
           
          <c:forEach var="list" varStatus="index" items="${list }">
          <tr>
          	 <td height="30" align="center">${index.index+1}</td>
          	 <td>${list.provinceName }</td>
          	 <td>${list.cityCount}</td>
          	 <td>${list.countryCount}</td>
          </tr>
          </c:forEach>
          
         </tbody>
       </table>
    </div>      
</div>


<!--框架内容 结束-->


<script type="text/javascript">

$(document).ready(function(){
	$("#viewExcel").click(function(){

		window.location.href= WEBPATH+'/sys2017/outCountUnitExcel.do';
	});
	$("#viewExcel1").click(function(){
		fileHandle(1);
	});
	$("#viewExcel2").click(function(){
		fileHandle(2);
	});
	$("#viewExcel3").click(function(){
		fileHandle(3);
	});
});

function fileHandle(type){
    $.ajax({
        type: "POST",
        url: "${webpath}/filesDrawActivity/extractDetail.do",
        data:{type:type},
        async:false,
        success: function(data){
            alert("执行完毕");
            if(data.result=="success"){
                alert("抽取成功");
            }else if(data.result=="error"){
                swal({title: "执行失败",text: "",type:"error"});
            }
        }
    });
}
	
</script>