<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>


<div class="center_weizhi">当前位置：系统管理 - 初始化参选单位 - 推选县级</div>
<!--框架内容 开始-->
<div class="center">
<div class="center_list">

<div class="panel-group" id="accordion">
          <h4 class="panel-title">      
          
            <!---搜索--->
            <div style="width:260px;" class="btn-group">
              <button id="addNewUnit" class="btn btn-success" type="button" style="width:120px;" data-toggle="modal" data-target="#myModal">
                 增加参选单位
              </button>
              <button id="submit_Button"  type="button" class="btn btn-danger" style="width:120px;" data-toggle="modal" data-target="#tijiao">提交</button>
            </div> 
            <div id="tip" hidden="true"><h4 style="color:#F00;">
          提示：已经提交，无法操作         
          </h4></div>
                       
          </h4>
    </div>
<!-- 新增县级参选单位（Modal） -->
<div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title" id="myModalLabel">新增县级参选单位</h4>
            </div>
            <div class="modal-body form-horizontal" style="height:300px; font-size:16px;">
            	<div class="form-group" style="padding:10px;">
                	<label class="col-lg-3 control-label">省份</label>
                    <div class="col-lg-8" style="margin-top:7px;">
                	${jsonCity.get(0).province}
                    </div>
                </div>
            	<div class="form-group" style="padding:10px;">
                	<label class="col-lg-3 control-label">市级行政区</label>
                    <div class="col-lg-8">
                		<select class="form-control" style="width:300px;margin-right:5px;" id="selectCity">
                             <option value="">请选择</option>
                             <c:forEach varStatus="index" items="${jsonCity}" var="list">
                             	<option value="${list.code}">${list.name}</option>
                             </c:forEach>
                          </select>
                    </div>
                </div>
                <div class="form-group" style="padding:10px;">
                	<label class="col-lg-3 control-label">县级行政区</label>
                    <div class="col-lg-8">
                		<select class="form-control" style="width:300px;margin-right:5px;" id="County">
                             <option value="">请选择</option>
                            
                          </select>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                <button   type="button" class="btn btn-primary" id="submitCounty"   >确定</button>
            </div>
        </div>
    </div>
</div>
<!-- 提交（Modal） -->
<div class="modal fade" id="tijiao" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title" id="myModalLabel">提示</h4>
            </div>
            <div class="modal-body form-horizontal text-center" style="height:200px; font-size:18px; padding:80px 0; color:red;">
            	每省可推选1--4个县级单位，请全部填好后再提交。提交后将无法再选择或更改推选单位。确认提交？
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                <button id="saveState" type="button" class="btn btn-primary" data-dismiss="modal">确定</button>
            </div>
        </div>
    </div>
</div>
 	 <table class="table table-bordered table-hover table-condensed">
         <thead>
           <tr>
             <td width="50" height="30" bgcolor="#efefef">序号</td>
             <td bgcolor="#efefef">所属行政区</td>
             <td bgcolor="#efefef">县级参选单位</td>
             <td bgcolor="#efefef" style="width:250px;">登录账号</td>
             <td bgcolor="#efefef" style="width:250px;">初始密码</td>
             <td bgcolor="#efefef" style="width:250px;">操作</td>
           </tr>
         </thead>
         <tbody>
          
          <c:forEach var="list" items="${json_County}" varStatus="index">
          <tr>
           <td height="30" align="center">${index.index+1}</td>
           <td>${list.city}</td>
           <td>${list.name}</td>
			<c:choose>
				<c:when test="${list.electCountyState == 1}">
				<td>${list.loginname}</td>
				  <td>dlb12369</td>
				</c:when>
				<c:otherwise>
				<td>&nbsp;</td>
				<td>&nbsp;</td>
				</c:otherwise>
			</c:choose>
           
         
          	<td><button class="btn btn-danger btn-xs" onclick="deleteCounty(${list.areacode})" id="deleteCounty${list.areacode}">删除</button></td>
           </tr>
          </c:forEach>
          
          
         </tbody>
       </table>
 
 
</div>
</div>





<!--框架内容 结束-->


 
<script type="text/javascript">
var webPath = '${webpath}';
$(document).ready(function() {
	$("#myModal").bind('hide.bs.modal',function(){
		$(".modal-backdrop").remove();
	});
	$("#tijiao").bind('hide.bs.modal',function(){
		$(".modal-backdrop").remove();
	});
	
	var json_Country = eval('${json_County}');
	if(json_Country.length >= 4){
		$("#addNewUnit").attr("disabled","true");
	}
	if(json_Country.length == 0){
		$("#submit_Button").attr("disabled","true");
	}
	if(json_Country.length != 0){
		var state = json_Country[0].electCountyState;
		if(state == 1){
			
			$("#submit_Button").hide();
			$("#tip").show();
			$("#addNewUnit").hide();
			for(var i =0;i<json_Country.length;i++){
				$("#deleteCounty"+json_Country[i].areacode).attr("disabled",true);
			}
			$("#addNewUnit").attr("disabled","true");
		}
	};
	
 $("#selectCity").change(function(){
	 var value = $("#selectCity").val();
	 if(value !=''){
		 $.ajax({
			 url:webPath+'/sys2017/getCounty.do',
			 data:{"code":value},
			 dataType:"json",
			 type:"post",
			 success:function(data){
				 var jsonCounty = eval(data);
				 $("#County option").remove();
				 $("#County").append("<option value=''>请选择</option>");
				 for(var i=0;i<jsonCounty.length;i++){
					 $("#County").append("<option value='"+jsonCounty[i].code+"'>"+jsonCounty[i].name+"</option>")
				 }
			 }
		 });
	 }
 });
 });
	 $("#submitCounty").click(function(){

		var value = $("#County").val();
		if(business.isNull(value)){

			swal("操作失败!", "请选择推选县级单位！", "error");
			 
		}else{
		$.ajax({
			url:webPath+'/sys2017/saveCounty.do',
			data:{"areaCode":value},
			// dataType:"json",
			 type:"post",
			 success:function(data){
				 if(data == "ok"){
				  swal("操作成功!", "已成功保存！", "success");
				 macroMgr.onLevelTwoMenuClick(null, 'sys2017/choseCounty.do');
				 $("#myModal").modal('hide');
				 }else if(data ==''){ 
					 swal("操作失败!", "无法保存，已存在4条数据！", "success");
				 }else if(data='report_save'){
					 swal("操作失败!", "省级已将填报信息报送，无法操作！", "error");
				 }else{
					 swal("操作失败!", "联系管理员", "error");
				 }
			 }
		});
		}
	});    
	
	
	
	   $("#saveState").click(function(){
		   $.ajax({
			   data:{"areaCode":"dd"},
		       url:webPath+'/sys2017/submitCounty.do',
		       type:"post",
		       success:function(data){
		    	   console.log(data);
		    	   if(data == 'ok'){
		    		   swal("操作成功!", "已成功提交！", "success");
		    		   macroMgr.onLevelTwoMenuClick(null, 'sys2017/choseCounty.do')
		    	   }else if(data == 'report_save'){
		    		   swal("操作失败!", "省级已将填报信息报送，无法操作！", "error");
		    	   }else {
		    		   swal("操作失败!", "保存失败！", "error");
		    	   }
		       }
		   });
	   });
	/* function deleteCounty(areaCode){
		console.log(areaCode);
		$.ajax({
			   data:{"areaCode":areaCode},
		      // dataType:"json",
		       url:webPath+'/sys2017/deleteCity.do',
		       type:"post",
		       success:function(data){
		    	   console.log(data);
		    	   if(data == 'ok'){
		    		   console.log("成功");
		    		   macroMgr.onLevelTwoMenuClick(null, 'sys2017/choseCounty.do')
		    	   }else{
		    		   console.log("失败");
		    	   }
		       }
		});
	} */
	//删除数据
	function deleteCounty(areaCode){
        swal({
                        title: "您确定执行此操作吗？",
                        text: "您确定要删除当前区划吗？",
                        type: "warning",
                        showCancelButton: true,
                        closeOnConfirm: false,
                        confirmButtonText: "是的，我要删除",
                        confirmButtonColor: "#ec6c62"
                    }, function() {
                        $.ajax({
                            url: webPath+'/sys2017/deleteCity.do',
                            type: "POST",
                            data:{"areaCode":areaCode}
                        }).done(function(data) {
                        
                            if(data=="ok"){
                            	swal("操作成功!", "已成功删除！", "success");
                            	 macroMgr.onLevelTwoMenuClick(null, 'sys2017/choseCounty.do')
                            }else if(data == "report_save"){
        						swal("省级已将填报信息报送，无法操作！", "", "error");
        					}else{
                                swal("OMG", "删除失败了!", "error");
                            }
                            
                        }).error(function(data) {
                            swal("OMG", "删除操作失败了!", "error");
                        });
                    });
    }
	
	
</script>
