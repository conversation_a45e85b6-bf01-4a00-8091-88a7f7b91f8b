<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>


<div class="center_weizhi">当前位置：系统管理 - 初始化参选单位 - 推选市级</div>
<!--框架内容 开始-->
<div class="center">
<div class="center_list">
<div class="panel-group" id="accordion">
          <h4 class="panel-title">      
          
          
            <!---搜索--->
            <div style="width:260px;" class="btn-group">
            	<button id="addCity" class="btn btn-success" type="button" style="width:120px;" data-toggle="modal" data-target="#sysModel">
                 增加参选单位
              </button>
              <button id="saveCity" type="button" class="btn btn-danger" style="width:120px;" data-toggle="modal" data-target="#tijiao">提交</button>
            </div>   
             <div id="tip" hidden="true" ><h4 style="color:#F00;">
                      提示：已经提交，无法操作         
          </h4></div>         
          </h4>
    </div>
 	<table class="table table-bordered table-hover table-condensed">
         <thead>
           <tr>
             <td width="50" height="30" bgcolor="#efefef">序号</td>
             <td bgcolor="#efefef">行政区</td>
             <td bgcolor="#efefef" style="width:250px;">登录账号</td>
             <td bgcolor="#efefef" style="width:250px;">初始密码</td>
             <td bgcolor="#efefef" style="width:250px;">操作</td>
           </tr>
         </thead>
         <tbody>
           
          
           <c:forEach var="list" items="${listCity}" varStatus="index" >
           <tr>
           <td height="30" align="center">${index.index+1}</td>
           <td>${list.name}</td>
           <c:choose>
           		<c:when test="${list.electcitystate==1}">
           			 <td>${list.loginname}</td>
           		</c:when>
           		<c:otherwise>
           			<td>&nbsp;</td>
           		</c:otherwise>
           </c:choose>
			<c:choose>
				<c:when test="${list.electcitystate==1}">
					 <td>dlb12369</td>
				</c:when>
				<c:otherwise>
					<td>&nbsp;</td>
				</c:otherwise>
			</c:choose>
          
           <td><button class="btn btn-danger btn-xs" onclick="deleteCity(${list.areacode})" id="deleteCity${list.areacode}">删除</button></td>
           </tr>
           </c:forEach>
          
         </tbody>
       </table>
  
  
</div>
</div>
<!-- 新增市级参选单位（Modal） -->
<div class="modal fade" id="sysModel" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title" id="myModalLabel">新增市级参选单位</h4>
            </div>
            <div class="modal-body form-horizontal" style="height:300px; font-size:16px;">
            	<div class="form-group" style="padding:10px;">
                	<label class="col-lg-3 control-label">省份</label>
                    <div class="col-lg-8" style="margin-top:7px;">
                	${json.get(0).province}
                    </div>
                </div>
            	<div class="form-group" style="padding:10px;">
                	<label class="col-lg-3 control-label">市级行政区</label>
                    <div class="col-lg-8">
                		<select class="form-control" style="width:300px;margin-right:5px;" id="city">
                             <option value="">请选择行政区</option>
                             <c:forEach var="list" items="${json}" varStatus="index">
                             	<option value="${index.index}">${list.name}</option>
                              </c:forEach>
                             
                          </select>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
            <!--class="btn btn-primary  -->
                <button type="button" class="btn btn-default" data-dismiss="modal" id="closeCity">关闭</button>
                <button type="button" class="btn btn-default"   onclick="stCity();"  >确定</button>
            </div>
        </div>
    </div>
</div>
<!-- 提交（Modal） -->
<div class="modal fade" id="tijiao" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title" id="myModalLabel">提示</h4>
            </div>
            <div class="modal-body form-horizontal text-center" style="height:200px; font-size:18px; padding:80px 0; color:red;">
            	每省可推选1--4个市级单位，请全部填好后再提交。提交后将无法再选择或更改推选单位。确认提交？
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal" >关闭</button>
                <button type="button" class="btn btn-primary" id="saveDefine" data-dismiss="modal" >确定</button>
            </div>
        </div>
    </div>
</div>




<!--框架内容 结束-->


 
<script type="text/javascript">
var webPath = '${webpath}';
	
	//删除数据
	function deleteCity(index){
        swal({
                        title: "您确定执行此操作吗？",
                        text: "您确定要删除当前区划吗？",
                        type: "warning",
                        showCancelButton: true,
                        closeOnConfirm: false,
                        confirmButtonText: "是的，我要删除",
                        confirmButtonColor: "#ec6c62"
                    }, function() {
                        $.ajax({
                            url: webPath+"/sys2017/deleteCity.do",
                            type: "POST",
                            data:{"areaCode":index}
                        }).done(function(data) {

                            if(data=="ok"){
                            	swal("操作成功!", "已成功删除！", "success");
                            	macroMgr.onLevelTwoMenuClick(null, 'sys2017/choseCity.do');
                            }else if(data == "report_save"){
        						swal("省级已将填报信息报送，无法操作！", "", "error");
        					}else{
                                swal("OMG", "删除失败了!", "error");
                            }
                            
                        }).error(function(data) {
                            swal("OMG", "所选行政区为空,无法保存", "error");
                        });
                    });
    }
	
	
	
	
//提交地区

   function stCity(){
        
		 var str = '${json}';
		var json = eval(str);
		
		var index = $("#city").val();
		if(business.isNull(index)){

			swal("操作失败!", "请选择推选市级单位！", "error");
			 
		}else{
		
		//对象转json
		var param = JSON.stringify(json[index]) ;
		//json 转对象
		//var param = JSON.parse(eval(json[index]));
		 $.ajax({
				url:webPath+"/sys2017/submitCity.do",
				data: {tarea:param} ,
				dateType:"json",
				type: "POST",
				success:function(data){
					if(data == 'ok'){
							swal("操作成功!", "已成功保存！", "success");
							 $("#sysModel").modal('hide');
	                        macroMgr.onLevelTwoMenuClick(null, 'sys2017/choseCity.do');
	                       
					} else if(data == '007'){
						swal("不能重复提交", "", "error");
					}else if(data == "error_sum"){
						swal("数据库中已存在4条记录，无法提交", "", "error");
					}else if(data == "report_save"){
						swal("省级已将填报信息报送，无法操作！", "", "error");
					}
					else{
						swal("发生错误，保存失败", "", "error");
					}
					 
				}
			});
		}
	
};
	 
	
	$("#saveDefine").click(function(){
		console.log("ddd");
		$.ajax({
			url:webPath+"/sys2017/saveCity.do",
			type:"POST",
			dateType:"json",
			success:function(data){
				if(data == 'ok'){
					swal("操作成功!", "已成功提交！", "success");
					macroMgr.onLevelTwoMenuClick(null, 'sys2017/choseCity.do');
				}else if(data == 'report_save'){
					swal("提交失败!", "省级已将填报信息报送，无法操作！", "error");
				}
				else{
					swal("提交失败!", "已存在4条数据，无法提交！", "error");
				}
			}
		}); 
	});
	

 
$(document).ready(function() {
	
	//最外层的modal关闭时，触发删除所有遮罩
	$("#sysModel").bind('hide.bs.modal',function(){
		  $(".modal-backdrop").remove();
	 });
	$("#tijiao").bind('hide.bs.modal',function(){
		  $(".modal-backdrop").remove();
	 })
	
	  var listCity = eval('${listCity}');
	  var length = listCity.length;
 	  if(length >= 4){
 		  console.log("dengyu 4");
 		  $("#addCity").attr("disabled",true);
 	  }
 	  if(length == 0){
 		 $("#saveCity").attr("disabled",true);
 	  }
	  if(length != 0){
		  var state = listCity[0].electcitystate;
		  console.log("state...."+state);
		  if(state == 1){
			  $("#addCity").hide();
			  $("#saveCity").hide();
			  $("#tip").show();
			  for(var i=0;i<listCity.length;i++){
				  $("#deleteCity"+listCity[i].areacode).attr("disabled",true);
			  }
		  }
 
	  }
	 
	});
	
	
</script>
