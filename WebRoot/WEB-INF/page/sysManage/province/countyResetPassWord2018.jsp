<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>


<div class="center_weizhi">当前位置：系统管理 - 下级密码重置

</div>
	
<!--框架内容 开始-->
<div class="center">
<div class="center_list">
<div class="panel-group" id="accordion">
 <h4 style="color:#F00;">
          提示：系统初始密码是：dlb@2021
 </h4>
</div>
   <table class="table table-bordered table-hover table-condensed">
         <thead>
           <tr>
             <td width="50" height="30" bgcolor="#efefef">序号</td>
             <td bgcolor="#efefef">行政区</td>
             <td width="200" bgcolor="#efefef">用户账号</td>
             <td width="200" bgcolor="#efefef">密码重置</td>
             <td width="200" bgcolor="#efefef">备注</td>
           </tr>
         </thead>
         <tbody>
          <c:forEach var="list" items="${listAll}" varStatus="index">
          <tr>
          	 <td height="30" align="center">${index.index+1}</td>
             <td>${list.name}</td>
             <td>${list.loginname}</td>
             <c:choose>
             	<c:when test="${list.loginpass == 'c1ddd7ae30223981c0b0ac509ef1eacc' }">
             	<td><button type="button" class="btn btn-danger btn-xs" disabled="disabled"  style="width:100px; margin:1px;background-color: gray;">已重置</button></td>
             	</c:when>
             	<c:otherwise>
             	<td><button id="'btn'+${index.count}" class="btn btn-danger btn-xs" style="width:100px;" data-toggle="modal" data-target="#myModal" onclick="resetPass(${list.areacode},this)">重置密码</button></td>
             	</c:otherwise>
             </c:choose>
             <td>&nbsp;</td>
          </tr>
          </c:forEach>
         </tbody>
       </table>
</div>
</div>
<!--框架内容 结束-->
<script type="text/javascript">
var webPath = '${webpath}';
function resetPass(areaCode,element){
	var resetId=document.getElementById(element.id);
    swal({
                    title: "您确定执行此操作吗？",
                    text: "您确定要重置当前区划登录密码吗？",
                    type: "warning",
                    showCancelButton: true,
                    closeOnConfirm: false,
                    confirmButtonText: "是的，我要重置",
                    confirmButtonColor: "#ec6c62"
                }, function() {
                    $.ajax({
                        url: WEBPATH+"/sys2017/resetPass.do",
                        type: "POST",
                        data:{"areaCode":areaCode,"passWord":"dlb@2021"}
                    }).done(function(data) {
                    
                        if(data.type="ok"){
                        	swal("操作成功!", "已成功重置密码！", "success");
                        	resetId.innerHTML="已重置";
                        	resetId.disabled="disabled";
                        	resetId.style.cssText="background-color: gray;width:100px; margin:1px;";
                        	/* $("#btn").text("已重置");
                        	$("#btn").attr("disabled", true); 
                        	$("#btn").css("background-color","gray"); */
                        	//macroMgr.onLevelTwoMenuClick(null, 'sys2018/resetPassWord.do');
                        }else{
                            swal("OMG", "重置密码失败了!", "error");
                        }
                        
                    }).error(function(data) {
                        swal("OMG", "删除操作失败了!", "error");
                    });
                });
}
$(document).ready(function() {		    
});
</script>
