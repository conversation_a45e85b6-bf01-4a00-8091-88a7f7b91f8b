<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>


<div class="center_weizhi">当前位置：系统管理 - 密码修改</div>
<!--框架内容 开始-->
<div class="center">
<div class="center_list">
   
    <div class="panel-group" id="accordion">
          <h4 style="color:#F00;">
          提示：(1)候选个人登录本系统的账号分配如下，初始密码是：per12369  &nbsp; &nbsp; (2)请根据本省实际情况推选先进个人，最多可推荐10人          
          </h4>
           
    </div>
        <table class="table table-bordered table-hover table-condensed">
         <thead>
           <tr>
             <td width="50" height="30" bgcolor="#efefef">序号</td>
             <td bgcolor="#efefef">登录账号</td>
             <td style="width:200px;" bgcolor="#efefef">操作</td>
           </tr>
         </thead>
         <tbody>
          
         <c:forEach var="list" items="${list}" varStatus="index">
          <tr>
           <td>${index.index+1}</td>
           <td>${list.loginname}</td>
           
           <c:choose>
           <c:when test="${list.loginpass!='b8555d34991cb831c0b0ac509ef1eacc'}">
           <td><button class="btn btn-success btn-xs" style="width:100px;" id="resetButton" onclick="resetPass('${list.loginname}')" >重置密码</button></td>
           </c:when>
           <c:otherwise>
          <td><button type="button" class="btn btn-primary btn-xs" disabled="disabled"  style="width:100px; margin:1px;background-color: gray;">重置密码</button></td>
           </c:otherwise>
           </c:choose>
         
           </tr>
           </c:forEach>
         </tbody>
       </table>
    </div>
</div>





<!--框架内容 结束-->


 
<script type="text/javascript">
var webPath = '${webpath}';
 function resetPass(loginName){
	//console.log(loginName);
	 $.ajax({
		data:{"loginName":loginName},
		//dataType:"JSON",
		Type:"POST",
		url:webPath+"/sys2017/restPersonPassWrod.do",
		success:function(data){
			if(data ==  'ok'){
				swal("操作成功!", "已成功重置！", "success");
				macroMgr.onLevelTwoMenuClick(null, 'sys2017/resetPassWord.do')
			}else{
				swal("提示!", "重置失败！", "error");
			}
		} 
	});
}; 
 
$(document).ready(function() {
	  
 
	    
	});
</script>
