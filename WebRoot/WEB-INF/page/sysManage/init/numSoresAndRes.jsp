<%@ page language="java" import="java.util.*,java.lang.*" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>

<style>
    table{width: 900px;}
    tr{width:100%;}
    td{text-align: left; }
</style>

<div class="center_weizhi">当前位置：系统管理 - 系统初始化设置</div>
<!--框架内容 开始-->
  
<div class="center">
      <div class="center_list">
     <span id ="sysBiaoZhi" hidden="hidden"></span>
        <table class="table_input" style="margin-left: 100px">
         <tbody>
           <c:forEach items="${initList }" var="init" varStatus="status">
           			<tr>
					             <td>${status.index+1 }、${init.actionname }</td>
					            <td >
					                                         执行结果：<label >${init.result }</label>
					             </td>
					             <td>
					                 <c:if test="${init.flag=='1' }">
                							<button  class="btn btn-primary" name="signup" value="Sign up" style="font-size:16px;width:250px; margin-top:5px;background-color: gray;">${status.index+1 }、&nbsp;&nbsp;执行操作...</button>
                                     </c:if>
                                      <c:if test="${init.flag=='0' }">
                                      	<c:choose>
										       <c:when test="${init.code=='15'}">
										       		<button  class="btn btn-primary"   data-toggle="modal" data-target="#myModal" style="font-size:16px;width:250px; margin-top:5px;" >${status.index+1 }、&nbsp;&nbsp;执行操作...</button>  
										       </c:when>
										       <c:when test="${init.code=='1'}">
										       		<button  class="btn btn-primary"   data-toggle="modal" data-target="#myMode" style="font-size:16px;width:250px; margin-top:5px;" >${status.index+1 }、&nbsp;&nbsp;执行操作...</button>  
										       </c:when>
										       <c:when test="${init.code=='27'}">
										       		<button  class="btn btn-primary"   data-toggle="modal" data-target="#myMode1" style="font-size:16px;width:250px; margin-top:5px;" >${status.index+1 }、&nbsp;&nbsp;执行操作...</button>  
										       </c:when>
										       <c:otherwise>
										      		<button onclick="submitConfig('${init.code }')" type="button" class="btn btn-primary" name="signup" value="Sign up" style="font-size:16px;width:250px; margin-top:5px;">${status.index+1 }、&nbsp;&nbsp;执行操作...</button>
										       </c:otherwise>
										</c:choose>
                                     </c:if>
					             	
					             </td>
					           </tr>
					           <tr>
					           		<td colspan="3">
					           			<hr/>
					           		</td>
                   </tr>
           
           </c:forEach>
           
         </tbody>
       </table>
  </div>



            

           
  
</div>
<script type="text/javascript">
//分页
$(document).ready(function(){
	
	
	
});


function submitConfig(code){
	    $.ajax({
             type: "POST",
             url: WEBPATH+"/sys/sysInitSubmit.do",
             data:{code:code},
             async:false,
             success: function(data){
	             if(data.type=="error"){
	               	swal({title: data.result ,text: "",type:"error"});
	                return false;
	             }else if(data.type=="success"){
	              	swal({title: data.result ,text: "",type:"success"});
	             }
           }
     });
     business.addMainContentParserHtml(WEBPATH+'/sys/numScoreAndRes.do',null);
}
 
</script>

 
 
