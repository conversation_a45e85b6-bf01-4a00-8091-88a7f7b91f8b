<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
<script type="text/javascript">
$(document).ready(function(){
	 //文件上传1、
	 $("#Profile").fileinput({
        uploadUrl: WEBPATH+'/pdffileupload.do', // you must set a valid URL here else you will get an error
        allowedFileExtensions : ['pdf','rar','zip','doc','docx','jpg','png'],
        language:'zh',
        //overwriteInitial: true,
        minFileCount: 1,
        maxFileCount: 1,
        minFileSize:1,
        maxFileSize:1050000,
        enctype: 'multipart/form-data',        
        dropZoneTitle:"可拖拽文件到此处...",
        initialPreviewShowDelete: false,
        msgFilesTooMany:'选择上传的文件数量({n}) 超过允许的最大数值{m}！',
        msgZoomModalHeading:'文件预览',
        msgInvalidFileExtension:'不正确的文件类型"{name}"只支持"{extensions}"类型的文件',
        msgNoFilesSelected:'请选择文件',
        msgValidationError:'文件类型不正确或文件过大',
        initialPreviewFileType:'pdf',
        browseLabel:"选择文件",
        removeLabel:'删除',
        removeTitle:'删除文件',
        uploadLabel:'上传',
        uploadTitle:'上传文件',
        cancelLabel: '取消',
        cancelTitle: '取消上传',
        showPreview:false,
        autoReplace:true,
        slugCallback: function(filename) {
            return filename.replace('(', '_').replace(']', '_');
        }
	}).on('filepreupload', function(event, data, previewId, index) { //文件开始上传操作
		
	   $("#xxcj_AJSL_Butt").prop('disabled', true);
	
	}).on('fileuploaded', function(event, data, previewId, index) {//文件上传完成执行操作

		$("#province_Fileurl").val(data.response.url);
	  	$("#province_Filename").val(data.response.fileRealName);
	  	$("#wsc").text(data.response.fileRealName);
	  	$("#xxcj_AJSL_Butt").prop('disabled',false);
	  	
	})/* .on('fileclear', function(event) {  //文件上传删除文件执行的操作
		
    	$("#province_Fileurl").val("");
	  	$("#province_Filename").val("");
	  	$("#wsc").text("未上传");
    }); */
    
    $("#reButton").click(function(){
    	$("#uploadTr").attr("style","");
    });
    	
});
</script>
</head>
<body>
<div class="center_weizhi">当前位置：信息采集 - 系统管理  - 单个附件补传</div>
<div class="center">
<div class="center_list">
  <table align="center" class="table_input">
    <tbody>
      
		      <tr>
		        <td width="250">&nbsp;</td>
		        <td><span style="color:#295A5F; font-size:18px; float:left; font-weight:bold;">${xxcj_AJSL.areaname }</span></td>
		      </tr>
		      <tr id="uploadTr" <c:if test="${xxcj_AJSL.provinceofficialdocument!='' && xxcj_AJSL.provinceofficialdocument!=null }">style='display:none'</c:if>>
		        <td>&nbsp;</td>
		        <td style="text-align:left;">
		        <input id="Profile" type="file" name="Profile" class="file-loading">
		        </td>
		      </tr>
		      <tr>
		      	<td>附件名称</td>
		        <td style="text-align:left;">
		        <input type="text" class="form-control" name="provinceofficialdocument" id="province_Filename" value="${xxcj_AJSL.provinceofficialdocument}">
		        </td>
		      </tr>
		      <tr>
		      	<td>上传成功后的路径</td>
		        <td style="text-align:left;">
				<input type="text" class="form-control" name="provinceofficialdocumenturl" id="province_Fileurl" value="${xxcj_AJSL.provinceofficialdocumenturl}">
		        </td>
		      </tr>
      <c:if test="${1==1 }">
	      <tr>
	        <td align="center">&nbsp;
	        <input type="hidden" name="id" value="${xxcj_AJSL.id }"/>
	        </td>
	        <td style="text-align:left;"><a href="#">
	          <button type="button"  class="btn btn-primary"   data-toggle="modal" data-target="#myMode"  style="font-size:16px;width:150px; margin-top:5px;">查看修改SQL</button>
	          </a></td>
	      </tr>
      </c:if>
    </tbody>
  </table>
    </div>
</div>



<!-- 模态框（Modal） -->
<div class="modal fade" id="myMode" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog" style="width: 800px;">
        <div class="modal-content" style="margin-top:100px;">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                <h3 class="modal-title" id="myModalLabel">置入数据库SQL</h3>
            </div>
 			<div  class="modal-body">
				<p style="text-indent:2em;">
				确认是否修改附件名称或其他字段，酌情添加:</br>
				update files set fileUrl={返回的上传路径 } where id={要修改案卷ID } and belongAreacode ={要修改的案卷行政区 }
				</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div>


<script language="JavaScript">
	
	//表单提交
	$(document).ready(function(){
		$("#xxcj_AJSL_Butt").click(function(){
			var validate = false;
		});
	});
	
	
	


</script>
</body>
</html>
