<%@ page language="java" import="java.util.*,java.lang.*" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>

<script type="text/javascript">
   $(document).ready(function(){
   		$("#sysSubmit").click(function(){

   			var sysStatus =$("#sysStatus").val();
	   		  swal({
	              title: "您确定执行此操作吗？",
	              text: "您确定要更改系统状态吗？",
	              type: "warning",
	              showCancelButton: true,
	              closeOnConfirm: false,
	              confirmButtonText: "是的",
	              confirmButtonColor: "#ec6c62"
	          }, function() {
	              $.ajax({
	                  url: WEBPATH+"/sys/sysStatus.do",
	                  type: "POST",
	                  data:{sysStatus:sysStatus}
	              }).done(function(data) {
	            	 
	                  if(data.type="success"){
	                  	swal("操作成功!", "", "success");
	                  	if(sysStatus == '1'){
	                  	$("#sysBiaoZhi1").html("信息采集阶段");
	                  	}else if(sysStatus =='6'){
	                     $("#sysBiaoZhi1").html("省级初始化参选信息阶段");
	                  	}else if(sysStatus =='2'){
	                     $("#sysBiaoZhi1").html("交叉评审阶段");
	                  	}else if(sysStatus =='5'){
	                     $("#sysBiaoZhi1").html("专家评审阶段");
	                  	}else if(sysStatus =='3'){
	                     $("#sysBiaoZhi1").html("系统汇总阶段");
	                  	}else if(sysStatus =='7'){
							$("#sysBiaoZhi1").html("案卷复核阶段");
						}else {
	                     $("#sysBiaoZhi1").html("结果公示阶段");
	                  	}
	                  	
	                  }else{
	                      swal("操作失败!", "", "error");
	                  }
	              }).error(function(data) {
	                  swal("操作失败!", "删除操作失败了!", "error");
	              });
	          });
   		});
   });
</script>
<style>
    table{width: 900px;}
    tr{width:100%;}
    td{text-align: left; }
</style>

<div class="center_weizhi">当前位置：系统管理 - 系统初始化设置</div>
<!--框架内容 开始-->
  
<div class="center">
      <div class="center_list">
     <span id ="sysBiaoZhi" hidden="hidden"></span>
        <table class="table_input" style="margin-left: 100px">
         <tbody>
           <tr> 
             <td width="300px">设置系统状态:</td>
             <td width="300px">
             <!-- 状态的标记 1 为省级上报状态 2质量评审阶段 3系统汇总分数 4结果公示阶段  -->
             	<select  id ="sysStatus" name="select3"  class="form-control">
              		  <option value="1" <c:if test="${sessionScope.sa_session.sysStatus == '1' }">selected</c:if> >信息采集阶段</option>
              		  <option value="2" <c:if test="${sessionScope.sa_session.sysStatus == '2' }">selected</c:if> >交叉评审阶段</option>
              		  <option value="5" <c:if test="${sessionScope.sa_session.sysStatus == '5' }">selected</c:if> >专家评审阶段</option>
              		  <option value="3" <c:if test="${sessionScope.sa_session.sysStatus == '3' }">selected</c:if> >系统汇总分数</option>
              		  <option value="4" <c:if test="${sessionScope.sa_session.sysStatus == '4' }">selected</c:if> >结果公示阶段</option>
              		  <option value="7" <c:if test="${sessionScope.sa_session.sysStatus == '4' }">selected</c:if> >案卷复核阶段</option>
              		  <option value="6" <c:if test="${sessionScope.sa_session.sysStatus == '6' }">selected</c:if> >省级初始化参选信息阶段</option>
                </select>
             </td>
             <td>
             	<button id = "sysSubmit"  class="btn btn-primary" name="signup"  style="font-size:16px;width:250px; margin-top:5px;"> 系统状态设置提交</button>
             </td>
           </tr>
           <tr>
           		<td colspan="3">
           			<hr/>
           		</td>
           </tr>
           <c:forEach items="${initList }" var="init" varStatus="status">
           			<tr>
					             <td>${status.index+1 }、${init.actionname }</td>
					            <td >
					                                         执行结果：<label >${init.result }</label>
					             </td>
					             <td>
					                 <c:if test="${init.flag=='1' }">
                							<button  class="btn btn-primary" name="signup" value="Sign up" style="font-size:16px;width:250px; margin-top:5px;background-color: gray;">${status.index+1 }、&nbsp;&nbsp;执行操作...</button>
                                     </c:if>
                                      <c:if test="${init.flag=='0' }">
                                      	<c:choose>
										       <c:when test="${init.code=='15'}">
										       		<button  class="btn btn-primary"   data-toggle="modal" data-target="#myModal" style="font-size:16px;width:250px; margin-top:5px;" >${status.index+1 }、&nbsp;&nbsp;执行操作...</button>  
										       </c:when>
										       <c:when test="${init.code=='1'}">
										       		<button  class="btn btn-primary"   data-toggle="modal" data-target="#myMode" style="font-size:16px;width:250px; margin-top:5px;" >${status.index+1 }、&nbsp;&nbsp;执行操作...</button>  
										       </c:when>
										       <c:when test="${init.code=='27'}">
										       		<button  class="btn btn-primary"   data-toggle="modal" data-target="#myMode1" style="font-size:16px;width:250px; margin-top:5px;" >${status.index+1 }、&nbsp;&nbsp;执行操作...</button>  
										       </c:when>
										       <c:otherwise>
										      		<button onclick="submitConfig('${init.code }')" type="button" class="btn btn-primary" name="signup" value="Sign up" style="font-size:16px;width:250px; margin-top:5px;">${status.index+1 }、&nbsp;&nbsp;执行操作...</button>
										       </c:otherwise>
										</c:choose>
                                     </c:if>
					             	
					             </td>
					           </tr>
					           <tr>
					           		<td colspan="3">
					           			<hr/>
					           		</td>
                   </tr>
           
           </c:forEach>
           
         </tbody>
       </table>
  </div>
  

  <!-- 模态框（Modal） -->
<div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content" style="margin-top:100px;">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                <h3 class="modal-title" id="myModalLabel">报社导出信息</h3>
            </div>
 			<div  class="modal-body">
					<p style="text-indent:2em;">案卷列表:</p>
					<p style="text-indent:2em;">
						select s.areaCode,u.province,u.city,u.country, s.fileName,tc.name as types ,s.fileCode,s.fileSimpleDesc,s.fileDetiailDesc 
						from files s 
						LEFT JOIN electionUnit as u ON u.areaCode = s.areaCode 
						LEFT JOIN TC_dictionary as tc ON tc.code = s.fileType
						where tc.temp='fileType' and s.type!='1' 
					 </p>
					<p style="text-indent:2em;">个人列表:</p>
					<p style="text-indent:2em;">
						select e.name,e.cardID,e.areaCode,s.fileName ,tc.name as types , s.fileCode,e.fileSimpleDesc,e.fileDetiailDesc 
						from files s   
						LEFT JOIN electionPersonal  as e ON e.fileId =  s.id 
						LEFT JOIN TC_dictionary as tc ON tc.code = s.fileType 
						where tc.temp='fileType' and s.type='1'
					</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div>

<!-- 专家 市县  基准分 -->
<div class="modal fade" id="myMode1" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content" style="margin-top:100px;">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                <h3 class="modal-title" id="myModalLabel">报社导出信息</h3>
            </div>
 			<div  class="modal-body">
					<p style="text-indent:2em;">专家基准分市:</p>
					<p style="text-indent:2em;">
					update files as fl
						set fl.expertConsiderScore = (select  ROUND(avgScore.score/avgScore.countnum,3) as ttt from 
								(	select count(1) as countnum ,sum(expertConsiderScore) as score 
									from files f  where f.areaCode in 
										(SELECT e.areaCode from electionUnit e where e.areaType='2') 
									and f.expertConsiderScore is not NULL
									) as avgScore
								)
						where fl.expertConsiderScore  is NULL  
						and fl.areaCode in (SELECT e.areaCode from electionUnit e where e.areaType='2');
					 </p>
					<p style="text-indent:2em;">专家基准分县:</p>
					<p style="text-indent:2em;">
						update files as fl
						set fl.expertConsiderScore = (select  ROUND(avgScore.score/avgScore.countnum,3) as ttt from 
								(	select count(1) as countnum ,sum(expertConsiderScore) as score 
									from files f  where f.areaCode in 
										(SELECT e.areaCode from electionUnit e where e.areaType='3') 
									and f.expertConsiderScore is not NULL
									) as avgScore
								)
						where fl.expertConsiderScore  is NULL  
						and fl.areaCode in (SELECT e.areaCode from electionUnit e where e.areaType='3');
					</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div>
            
<!-- 模态框（Modal） -->
<div class="modal fade" id="myMode" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog" style="width: 800px;">
        <div class="modal-content" style="margin-top:100px;">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                <h3 class="modal-title" id="myModalLabel">行政处罚导表语句</h3>
            </div>
 			<div  class="modal-body">
				<p style="text-indent:2em;">
				SELECT areaCode as areaCode, '0' as fileType , 
						CASE isnull(attachFileName,'') when '' then chufajuedinganjuan else attachFileName end as fileName, 
						chuFaWenHao as fileCode, id as oldId 
				from T_Pena 
				where T_Pena.jueDingRiQi >='2017-01-01' and T_Pena.jueDingRiQi <='2017-10-31' 
					and isnull(chuFaWenHao,'')<>'' and delFlag='0' and infoFlag is NULL
				union all  
				</p>
				<p style="text-indent:2em;">
				select d_loginAreaCode as areaCode, '1' as fileType, 
						CASE isnull(d_attachFileName,'') when '' then d_chuFaAnjuanwenben else d_attachFileName end as fileName, 
						d_jueDingShuWenHao as fileCode, id as oldId 
				from T_info_daysPena 
				where d_jueDingXiaDaRiQi>='2017-01-01' and d_jueDingXiaDaRiQi<='2017-10-31' 
					and isnull(d_jueDingShuWenHao,'')<>'' and delFlag='0' 
				union all 
				</p>
				<p style="text-indent:2em;">
				select j_loginAreaCode as areaCode, '2' as fileType , 
						j_anjuanWenBen as fileName, j_yisongAnJuanBianHao as fileCode, id as oldId 
				from T_info_detention 
				where j_yiSongShiJian >='2017-01-01' and j_yiSongShiJian<='2017-10-31' 
					and isnull(j_yisongAnJuanBianHao,'')<>'' and delFlag='0' 
				union all
				</p>
				<p style="text-indent:2em;">
				select c_loginAreaCode as areaCode, '3' as fileType, 
						c_anJuanWenBen as fileName, c_yisongAnJuanBianHao as fileCode, id as oldId 
				from T_info_hjwrCrime 
				where c_yiSongShiJian>='2017-01-01' and c_yiSongShiJian<='2017-10-31' 
					and isnull(c_yisongAnJuanBianHao,'')<>'' and delFlag='0' 
				union all				
				 </p>
				<p style="text-indent:2em;">
				select c_loginAreaCode as areaCode, '6' as fileType, 
						c_attachFileName as fileName, c_kouyajuedingwenshuhao as fileCode, id as oldId 
				from T_info_attachment 
				where c_shiShiQiXian_start>='2017-01-01' and c_shiShiQiXian_start<='2017-10-31' 
					and isnull(c_kouyajuedingwenshuhao,'')<>'' and delFlag='0' 
				union all		
				 </p>
				<p style="text-indent:2em;">
				select l_loginAreaCode as areaCode, '7' as fileType, 
						l_attachFileName as fileName, l_jueDingWenHao as fileCode, id as oldId 
				from T_info_limitANDstop 
				where l_shiShiQiXian_start>='2017-01-01' and l_shiShiQiXian_start<='2017-10-31' 
					and isnull(l_jueDingWenHao,'')<>'' and L_TYPE = '1' and delFlag='0' 
				union all
				</p>
				<p style="text-indent:2em;">
				select l_loginAreaCode as areaCode, '7' as fileType, 
					l_attachFileName as fileName, l_jueDingWenHao as fileCode, id as oldId 
				from T_info_limitANDstop 
				where tczz_beiAnRiQi>='2017-01-01' and tczz_beiAnRiQi<='2017-10-31' and isnull(l_jueDingWenHao,'')<>'' 
					and L_TYPE = '2' and delFlag='0'
				</p>
				<p style="text-indent:2em;">
				2017抽取的条件允许附件为空。去重过程：1.先把所有案卷放如同步表.2.在同步案卷表去重（参考下面SQL）
				</p>
				<p style="text-indent:2em;">
				/*查询所有重复的数据做标记置为1*/
				CREATE table temp_repeatID(
					select C.id,1 as flag  from 
					(
					SELECT fileCode,areaCode,fileType,max(oldId) as asd
					FROM penalizeSyncFile
					GROUP by fileCode,areaCode,fileType
					having count(*)>1
					) as B LEFT JOIN penalizeSyncFileTemp as C ON B.fileCode= C.fileCode AND B.areaCode= C.areaCode and B.fileType= C.fileType
				);
				</p>
				<p style="text-indent:2em;">
				/*查询所有保留的数据*/
				CREATE TABLE temp_notDeleteID (
					select C.id,1 as flag  from 
					(
					SELECT fileCode,areaCode,fileType,max(oldId) as oldId
					FROM penalizeSyncFile
					GROUP by fileCode,areaCode,fileType
					having count(*)>1
					) as B LEFT JOIN penalizeSyncFileTemp as C ON B.fileCode= C.fileCode AND B.areaCode= C.areaCode and B.fileType= C.fileType and B.oldId = C.oldId
				);
				</p>
				<p style="text-indent:2em;">
				/*把要删除的标记为2*/
				UPDATE temp_repeatID SET flag=2 WHERE id not in(select id from temp_notDeleteID);
				</p>
				<p style="text-indent:2em;">
				/*删除重复的*/
				DELETE from penalizeSyncFile where id in (select id from temp_repeatID where flag=2);
				</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div>
           
  
</div>
<script type="text/javascript">
//分页
$(document).ready(function(){
	
	
	
});


function submitConfig(code){
	    $.ajax({
             type: "POST",
             url: WEBPATH+"/sys/sysInitSubmit.do",
             data:{code:code},
             async:false,
             success: function(data){
	             if(data.type=="error"){
	               	swal({title: data.result ,text: "",type:"error"});
	                return false;
	             }else if(data.type=="success"){
	              	swal({title: data.result ,text: "",type:"success"});
	             }
           }
     });
     business.addMainContentParserHtml(WEBPATH+'/sys/systemInit.do',null);
}
 
</script>

 
 
