<%@ page language="java" import="java.util.*,java.lang.*" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>

<script type="text/javascript">
   $(document).ready(function(){
	 //监听enter
		business.listenEnter("searchButt");
	 //导出execl表单
		$("#viewExcel").click(function(){

			window.location.href= WEBPATH+'/sys/downCrossUserExecl.do';
		});
		 
   });
   function resetChildrenPass(id){
       swal({
                       title: "您确定执行此操作吗？",
                       text: "您确定要重置当前登录密码吗？",
                       type: "warning",
                       showCancelButton: true,
                       closeOnConfirm: false,
                       confirmButtonText: "是的，我要重置",
                       confirmButtonColor: "#ec6c62"
                   }, function() {
                       $.ajax({
                           url: WEBPATH+"/sys/resetCroPass.do",
                           type: "POST",
                           data:{id:id}
                       }).done(function(data) {
                           if(data.type="success"){
                           	swal("操作成功!", "已成功重置密码！", "success");
                           //	macroMgr.onLevelTwoMenuClick(null, 'sys/crossUser.do');
                          	business.addMainContentParserHtml('sys/crossUser.do','pageNum=1');
                           }else{
                               swal("OMG", "重置密码失败了!", "error");
                           }
                           
                       }).error(function(data) {
                           swal("OMG", "删除操作失败了!", "error");
                       });
                   });
   }


                    
</script>

<div class="center_weizhi">当前位置：系统管理 - 交叉用户管理</div>
<!--框架内容 开始-->

<div class="center">
<div class="center_list">
    <div class="panel-group" id="accordion">
          <h4 class="panel-title">
            
          <!---快速查询--->
        
              <select id="areacode" class="form-control" style="width:160px;margin-right:5px;">
                 <option value=''>全部</option>
                 <c:forEach items="${areaList }" var="area"> 
                 			<option value="${area.code }">${area.name }</option>
                 </c:forEach>
                 
              </select>
            <!---搜索--->
            <div style="width:260px;" class="btn-group">
               <form class="bs-example bs-example-form" role="form">
                  <div class="row">                     
                     <div class="col-lg-6">
                        <div class="input-group">
                           <input type="text" id="name" placeholder="真实姓名关键字"  class="form-control" style="width:200px;">
                           <span class="input-group-btn">
                              <button onclick="search()" id="searchButt" class="btn btn-success" type="button">
                                 快速搜索
                              </button>
                           </span>
                        </div><!-- /input-group -->
                     </div><!-- /.col-lg-6 -->
                  </div><!-- /.row -->
               </form>
            </div>    
             <div style="width:260px;" class="btn-group">
                 &nbsp;&nbsp;&nbsp;&nbsp; <a href ="#"><button type="button" id ="viewExcel" class="btn btn-primary">导出EXCEL</button></a>  
            </div>           
            	<span style="color:#F00;">提示：系统初始密码是：jcps666666   </span>        
          </h4>
    </div>
        <table class="table table-bordered table-hover table-condensed">
         <thead>
            
           <tr>
             <td width="50" height="30" bgcolor="#efefef">序号</td>
             <td bgcolor="#efefef">用户账号</td>
             <td bgcolor="#efefef">所属行政区</td>
             <td bgcolor="#efefef">真实姓名</td>
             <td bgcolor="#efefef">手机号码</td>
             <td bgcolor="#efefef">备注</td>
             <td width="200" bgcolor="#efefef">密码重置</td>
           </tr>
         </thead>
         <tbody>
	         <c:forEach items="${crossResult.list }" var="cross" varStatus="status">
		           <tr>
		             <td height="30" align="center">${status.index+1}</td>
		             <td>${cross.loginname }</td>
		             <td>${cross.areaname }</td>
		             <td>${cross.name }</td>
		             <td>${cross.phone }</td>
		             <td>${cross.remark }</td>
		             <td>
	             		<c:choose>
	             		     <c:when test="${cross.loginpass=='dfc2c79046d493936ae5aa30e2a04f01' }">
	             		     		  <button type="button" class="btn btn-primary btn-xs" disabled="disabled"  style="width:100px; margin:1px;background-color: gray;">重置密码</button>
	             		     </c:when>
	             		     <c:otherwise>
	             		        	 <a href="#" onclick="resetChildrenPass(${cross.id})"><button type="button" class="btn btn-primary btn-xs" style="width:100px; margin:1px;">重置密码</button></a>
	             		     </c:otherwise>
	             		</c:choose>
			         </td>
		           </tr>
	           </c:forEach>
         </tbody>
       </table>
    </div>
       <!--列表翻页 开始-->
    <div class="page">
    	<span style="padding:12px; float:left; color:#0099cc;">共${crossResult.total}条记录</span>
        <ul class="pagination" id="pageCon">
            
        </ul>
    </div>
    <!--列表翻页 结束-->
</div>
<script type="text/javascript">
//分页
$(document).ready(function(){
	
	
	var curentPage = eval('${crossResult.pageNum}');
	var totalPage = eval('${crossResult.pages}');
	//var type = eval('${areaType}');
	if(totalPage>0){
		var options = {
			bootstrapMajorVersion: 3,
		    currentPage: curentPage,
		    totalPages: totalPage,
		    numberOfPages: 5,
		    itemTexts: function (type, page, current) {
		            switch (type) {
		            case "first":
		                return "首页";
		            case "prev":
		                return "&laquo;";
		            case "next":
		                return "&raquo;";
		            case "last":
		                return "尾页";
		            case "page":
		                return page;
		            }
		    },
		    onPageClicked: function (event, originalEvent, type, page) {
		    	
		    	var name = $('#name').val();
		    	
            	business.addMainContentParserHtml(WEBPATH+'/sys/crossUser.do?areacode='+$('#areacode').val()+'&name='+$('#name').val()+'&pageNum='+page,null);
            }
    	};
    	$('#pageCon').bootstrapPaginator(options);
    	
   	}
	var areacode='${areacode}';
	var name='${name}';
	if(!business.isNull(areacode)){
	 
		$("#areacode option[value='"+areacode+"']").attr("selected", true);
	}
	if(!business.isNull(name)){
		$("#name").val(name);
	}
});

$("#areacode").change(function(){
	business.addMainContentParserHtml(WEBPATH+'/sys/crossUser.do?areacode='+$('#areacode').val()+'&pageNum=1',null);
});

function search(){
	
	business.addMainContentParserHtml(WEBPATH+'/sys/crossUser.do?areacode='+$('#areacode').val()+'&name='+$('#name').val()+'&pageNum=1',null);
}
</script>

 
 
