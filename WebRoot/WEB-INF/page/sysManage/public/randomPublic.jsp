<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<script type="text/javascript" src="${webpath}/static/vue/vue.js">
</script>

<div class="center_weizhi">当前位置：系统管理 - 案卷公开情况 - 随机抽选案卷公开情况</div>
<!--框架内容 开始-->
<div class="center">
<div class="center_list">
    <div class="panel-group" id="accordion">
          <!---快速查询--->
          <form role="form" id="formSearch">
              
          <select class="form-control" style="width:300px;margin-right:5px;" name="filetype" id="filetype">
          <option value="">请选择案卷类型</option>
          <option value="0">行政处罚案卷</option>
          <option value="1">按日计罚案卷</option>
          <option value="6">查封扣押案卷</option>
          <option value="7">限产停产案卷</option>
          <option value="2">移送行政拘留案卷</option>
          <option value="3">涉嫌犯罪移送案卷</option>
          <option value="5">污染源现场监督检查稽查案卷</option>
          </select>
           <input type="text" class="form-control" id="filecode" name="filecode" style="width:300px;" placeholder="请输入案卷文号">
        </form>
          
            <!---搜索--->
            <div style="width:260px;" class="btn-group">
               <form class="bs-example bs-example-form" role="form">
                  <div class="row">                     
                     <div class="col-lg-6">
                        <div class="input-group">
                          
                           <span class="input-group-btn">
                              <button class="btn btn-danger" type="button" onclick="search()">
                                 快速搜索
                              </button>
                              <button type="button" class="btn btn-danger" id="outputExcel">导出EXCEL</button>
                           </span>
                        </div>
                     </div>
                  </div>
               </form>
            </div>            
    </div>
        <table id="show" class="table table-bordered table-hover table-condensed">
         <thead>
           <tr>
             <td width="50" height="30" bgcolor="#efefef">序号</td>
             <td bgcolor="#efefef">省</td>
             <td bgcolor="#efefef">地市</td>
             <td bgcolor="#efefef">区县</td>
             <td bgcolor="#efefef">案卷类型</td>
             <td bgcolor="#efefef">案卷文号</td>
             <td bgcolor="#efefef">是否公开</td>
             <td bgcolor="#efefef">公开方式</td>
             <td bgcolor="#efefef" style="width:200px; word-break:break-all;">公开网址</td>
             <td bgcolor="#efefef">其他公开方式描述</td>
           </tr>
         </thead>
         <tbody>
         	<template v-for="(item,index) in items">
         		<tr>
         			<td>{{index+1}}</td>
         			<td>{{item.province}}</td> 
         			<td>{{item.city}}</td> 
         			<td>{{item.country}}</td>
         			<td>{{item.fileTypeName}}</td>
         			<td>{{item.fileCode}}</td>
         			<td v-if="item.isPublicPenalize === '01'">是</td>
         			<td v-else-if="item.isPublicPenalize === '02'">否</td>
         			<td v-else>&nbsp;</td>
         			<!-- <td>{{item.isPublicPenalize}}</td> -->
         			<td v-if="item.publicTypePenalize === '01'">网站公开</td>
         			<td v-else-if="item.publicTypePenalize === '02'">其他方式公开</td>
         			<td v-else>&nbsp;</td>
         			<td style="width:200px; word-break:break-all;">{{item.publicAddressPenalize}}</td> 
         			<td>{{item.publicDescPenalize}}</td> 
         		</tr>
         	</template>
         
        
         </tbody>
       </table>
    </div>
     <div class="page">
   			 <span style="padding:12px; float:left; color:#0099cc;">共${pageBean.total}条记录</span>
        	 <ul class="pagination" id="pageCon">
        	 </ul>
    	   </div>
</div>
<!-- 新增市级参选单位（Modal） -->

<!--框架内容 结束-->


 
<script type="text/javascript">
var webPath = '${webpath}';
var json = '${jsonList}';
//console.log(json);
var jsonlist = eval(json);
console.log(jsonlist);
  var exl = new Vue({
	el:"#show",
	data:{
		items:jsonlist
			
	}
});  

//提交地区

$(document).ready(function() {
	var filecode = '${filecode}';

	var filetype = '${filetype}';
	var curentPage = eval('${pageBean.pageNum}');
	var totalPage = eval('${pageBean.pages}');
if(filecode != ''){
	$("#filecode").val(filecode);
}
if(filetype != ''){
	$("#filetype").val(filetype);
}
if(totalPage>0){
	var options = {
		bootstrapMajorVersion: 3,
	    currentPage: curentPage,
	    totalPages: totalPage,
	    numberOfPages: 5,
	    itemTexts: function (type, page, current) {
	            switch (type) {
	            case "first":
	                return "首页";
	            case "prev":
	                return "&laquo;";
	            case "next":
	                return "&raquo;";
	            case "last":
	                return "尾页";
	            case "page":
	                return page;
	            }
	    },
	    onPageClicked: function (event, originalEvent, type, page) {
	    	var filetype = $("#filetype").val();
	    	var filecode = $("#filecode").val();
        	business.addMainContentParserHtml(WEBPATH+'/sys2017/randomPublic.do?pageNum='+page+'&filetype='+filetype+'&filecode='+filecode);
        }
	};
	$('#pageCon').bootstrapPaginator(options);
}
	
	$("#outputExcel").click(function(){
		var filecode = $("#filecode").val();
		var filetype = $("#filetype").val();
		window.location.href= WEBPATH+'/sys2017/outPutPublicExcel.do?filecode='+filecode+"&filetype="+filetype+"&type=1";
	});
	});
//查询
function search(){
	console.log($("#formSearch").serialize());
	var filecode = $("#filecode").val();
	var filetype = $("#filetype").val();
	business.addMainContentParserHtml(WEBPATH+'/sys2017/randomPublic.do?filecode='+filecode+'&filetype='+filetype);
}
	
</script>
