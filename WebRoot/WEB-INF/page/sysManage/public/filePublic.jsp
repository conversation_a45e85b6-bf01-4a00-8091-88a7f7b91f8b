<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>


<div class="center_weizhi">当前位置：系统管理 - 案卷公开情况 - 参选案卷公开情况</div>
<!--框架内容 开始-->
<div class="center">
<div class="center_list">
    <div class="panel-group" id="accordion">
          <!---快速查询--->
          <form role="form" id="formSearch">
              
          <select class="form-control" style="width:300px;margin-right:5px;" name="filetype" id="filetype">
          <option value="">请选择案卷类型</option>
          <option value="0">行政处罚案卷</option>
          <option value="1">按日计罚案卷</option>
          <option value="6">查封扣押案卷</option>
          <option value="7">限产停产案卷</option>
          <option value="2">移送行政拘留案卷</option>
          <option value="3">涉嫌犯罪移送案卷</option>
          <option value="5">污染源现场监督检查稽查案卷</option>
          </select>
           <input type="text" class="form-control" id="filecode" name="filecode" style="width:300px;" placeholder="请输入案卷文号">
           <select class="form-control" style="width:150px;margin-right:5px;" name="type" id="typeSelect">
                 <option value="" >请选择案卷类别</option>
                 <option value="0" <c:if test="${type =='0' }">selected</c:if>  >集体案卷类型</option>
                 <option value="1" <c:if test="${type =='1' }">selected</c:if> >个人案卷类型</option>
                 <option value="2" <c:if test="${type =='2' }">selected</c:if> >集体与个人共同案卷类型</option>
              </select>
        </form>
          
            <!---搜索--->
            <div style="width:260px;" class="btn-group">
               <form class="bs-example bs-example-form" role="form">
                  <div class="row">                     
                     <div class="col-lg-6">
                        <div class="input-group">
                          
                           <span class="input-group-btn">
                              <button class="btn btn-danger" type="button" onclick="search()">
                                 快速搜索
                              </button>
                              <button type="button" class="btn btn-danger" id="outputExcel">导出EXCEL</button>
                           </span>
                        </div>
                     </div>
                  </div>
               </form>
            </div>            
    </div>
        <table class="table table-bordered table-hover table-condensed">
         <thead>
           <tr>
             <td rowspan="2" width="50" height="30" bgcolor="#efefef">序号</td>
             <td rowspan="2" bgcolor="#efefef">省</td>
             <td rowspan="2" bgcolor="#efefef">地市</td>
             <td rowspan="2" bgcolor="#efefef">区县</td>
             <td rowspan="2" bgcolor="#efefef" style="word-wrap:break-all; width:100px;">案卷类型</td>
             <td rowspan="2" bgcolor="#efefef" style="word-wrap:break-all; width:100px;">案卷文号</td>
             <td colspan="2" bgcolor="#efefef">大练兵填报</td>
             <td colspan="4" bgcolor="#efefef">行政处罚填报</td>
            <!--
             <td bgcolor="#efefef">公开方式</td>
             <td bgcolor="#efefef">公开网址</td>
             <td bgcolor="#efefef">其他公开方式描述</td> -->
           </tr>
           <tr>
           	<td bgcolor="#efefef" style="word-wrap:break-all; width:90px;">是否在门户网站公开</td>
           	<td bgcolor="#efefef" style="word-wrap:break-all; width:160px;">公开网址</td>
           	<td bgcolor="#efefef" style="word-wrap:break-all; width:70px;">决定是否社会公开</td>
           	<td bgcolor="#efefef" style="word-wrap:break-all; width:40px;">公开方式</td>
            <td bgcolor="#efefef" style="word-wrap:break-all; width:160px;">公开网址</td>
            <td bgcolor="#efefef" style="word-wrap:break-all; width:100px;">其他公开方式描述</td>
           </tr>
         </thead>
         <tbody>
          <c:forEach var="list" items="${pageBean.list}" varStatus="index">
          <tr>
           <td height="30" align="center">${index.index+1}</td>
             <td>${list.province}</td>
             <td>${list.city}</td>
             <td>${list.country}</td>
             <c:if test="${list.fileType =='0'}">
             	<td style="word-wrap:break-all; width:120px;">行政处罚案卷</td>
             </c:if>
             <c:if test="${list.fileType =='1'}">
             	<td style="word-wrap:break-all; width:120px;">按日计罚案卷</td>
             </c:if>
             <c:if test="${list.fileType =='2'}">
             	<td style="word-wrap:break-all; width:120px;">移送行政拘留案卷</td>
             </c:if>
             <c:if test="${list.fileType =='3'}">
             	<td style="word-wrap:break-all; width:120px;">涉嫌犯罪移送案卷</td>
             </c:if>
             <c:if test="${list.fileType =='4'}">
             	<td style="word-wrap:break-all; width:120px;">申请法院强制执行案卷</td>
             </c:if>
             <c:if test="${list.fileType =='5'}">
             	<td style="word-wrap:break-all; width:120px;">发现问题的污染源现场监督检查稽查案卷</td>
             </c:if>
             <c:if test="${list.fileType =='6'}">
             	<td style="word-wrap:break-all; width:120px;">查封扣押案卷</td>
             </c:if>
             <c:if test="${list.fileType =='7'}">
             	<td style="word-wrap:break-all; width:120px;">限产停产案卷</td>
             </c:if>
             <td style="word-wrap:break-all; width:120px;">${list.fileCode}</td>
             <c:choose>
             	<c:when test="${list.isPublic =='1'}">
             		<td>是</td>
             	</c:when>
             	<c:when test="${list.isPublic =='0'}">
             		<td>否</td>
             	</c:when>
             	<c:otherwise>
             		<td></td>
             	</c:otherwise>
             </c:choose>
             
             <td style="width:160px; word-break:break-all;">${list.publicAddress}</td>
             <c:choose>
             	<c:when test="${list.isPublicPenalize =='01'}">
             		<td>是</td>
             	</c:when>
             	<c:when test="${list.isPublicPenalize =='02'}">
             		<td>否</td>
             	</c:when>
             	<c:otherwise>
             		<td></td>
             	</c:otherwise>
           </c:choose>
           <c:choose>
             	<c:when test="${list.publicTypePenalize =='01'}">
             		<td style="word-wrap:break-all; width:40px;">网站公开</td>
             	</c:when>
             	<c:when test="${list.publicTypePenalize =='02'}">
             		<td style="word-wrap:break-all; width:40px;">其它</td>
             	</c:when>
             	<c:otherwise>
             		<td></td>
             	</c:otherwise>
           </c:choose>
             <td style="width:160px; word-break:break-all;">${list.publicAddressPenalize}</td>
             <td>${list.publicDescPenalize }</td>
             </tr>
          </c:forEach>
          
         </tbody>
       </table>
    </div>
     <div class="page">
   			 <span style="padding:12px; float:left; color:#0099cc;">共${pageBean.total}条记录</span>
        	 <ul class="pagination" id="pageCon">
        	 </ul>
    	   </div>
</div>
<!-- 新增市级参选单位（Modal） -->

<!--框架内容 结束-->


 
<script type="text/javascript">
var webPath = '${webpath}';
	

//提交地区

$(document).ready(function() {
	var filecode = '${filecode}';
	var type = '${type}';
	var filetype = '${filetype}';
	var curentPage = eval('${pageBean.pageNum}');
	var totalPage = eval('${pageBean.pages}');
if(filecode != ''){
	$("#filecode").val(filecode);
}
if(filetype != ''){
	$("#filetype").val(filetype);
}
if(type != ''){
	$("#typeSelect").val(type);
}

if(totalPage>0){
	var options = {
		bootstrapMajorVersion: 3,
	    currentPage: curentPage,
	    totalPages: totalPage,
	    numberOfPages: 5,
	    itemTexts: function (type, page, current) {
	            switch (type) {
	            case "first":
	                return "首页";
	            case "prev":
	                return "&laquo;";
	            case "next":
	                return "&raquo;";
	            case "last":
	                return "尾页";
	            case "page":
	                return page;
	            }
	    },
	    onPageClicked: function (event, originalEvent, type, page) {
	    	var filetype = $("#filetype").val();
	    	var filecode = $("#filecode").val();
	    	var ftype=$("#typeSelect").val();
        	business.addMainContentParserHtml(WEBPATH+'/sys2017/filePublic.do?pageNum='+page+'&filetype='+filetype+'&filecode='+filecode+'&type='+ftype);
        }
	};
	$('#pageCon').bootstrapPaginator(options);
}
	
	$("#outputExcel").click(function(){
		var filecode = $("#filecode").val();
		var filetype = $("#filetype").val();
		var type = $("#typeSelect").val();
		window.location.href= WEBPATH+'/sys2017/outPutPublicFileExcel.do?filecode='+filecode+"&filetype="+filetype+"&reporttype=0&type="+type;
	});
	});
//查询
function search(){
	console.log($("#formSearch").serialize());
	var filecode = $("#filecode").val();
	var filetype = $("#filetype").val();
	var type = $("#typeSelect").val();
	business.addMainContentParserHtml(WEBPATH+'/sys2017/filePublic.do?filecode='+filecode+'&filetype='+filetype+'&type='+type);
}
	
</script>
