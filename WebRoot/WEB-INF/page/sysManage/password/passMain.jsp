<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>


<div class="center_weizhi">当前位置：系统管理 - 密码修改</div>
<!--框架内容 开始-->
<div class="center">
<div class="center_list">
 <form id="passForm" >
  <table align="center" class="table_input">
    <tbody>
      <tr>
        <td width="13%" align="center">用户名</td>
        <td width="38%"> 
        <strong style="color:#33688c; font-size:16px;">${sessionScope.sa_session.loginid}</strong>
        </td>
        <td width="13%">&nbsp;</td>
        <td width="38%">&nbsp;</td>
      </tr>
      <tr>
        <td align="center">原密码</td>
        <td class="form-group"><input type="password" class="form-control input-sm" id="name3" name="oldPass"  placeholder="请输入原密码"></td>
        <td>&nbsp;</td>
        <td>&nbsp;</td>
      </tr>
      <tr>
        <td align="center">新密码</td>
        <td class="form-group"><input type="password" class="form-control input-sm" name="password"  id="password" placeholder="请输入新密码"></td>
        <td>&nbsp;</td>
        <td>&nbsp;</td>
      </tr>
      <tr>
        <td align="center">新密码确认</td>
        <td class="form-group"><input type="password" class="form-control input-sm" id="repassword" name="repassword" placeholder="请再次输入新密码"></td>
        <td>&nbsp;</td>
        <td>&nbsp;</td>
      </tr>
     
    </tbody>
  </table>
  </form>
  <div style="margin-left: 110px">
          <a href="#">
          <button onclick="reset()"  class="btn btn-danger" name="signup" value="Sign up" style="font-size:16px;width:150px; margin-top:5px;">信息保存</button>
          </a>
  </div>
</div>
</div>





<!--框架内容 结束-->


 
<script type="text/javascript">
 
$(document).ready(function() {
	  
 
	    $("#passForm").formValidation({
		   
	        framework: 'bootstrap',
	        message: 'This value is not valid',
	        icon:{
		            valid: 'glyphicon glyphicon-ok',
		            invalid: 'glyphicon glyphicon-remove',
		            validating: 'glyphicon glyphicon-refresh'
                   },
	        fields: {
	            oldPass: {
	                message: '原密码不正确',
	                //Using the verbose option is solution for this approach. 
	                //Setting verbose: false will stop validations when there is one failure validator.
	                verbose: false,
	                validators: {
	                    notEmpty: {
	                        message: '原密码为必填项'
	                    },
	                    stringLength: {
	                        min: 8,
	                        max: 20,
	                        message: '原密码为8-20个字符'
	                    },
	                    /*regexp: {
	                        regexp: /^[a-zA-Z0-9_\.]+$/,
	                        message: '原密码由字母、数字、下划线或. 组成'
	                    },*/
	                    remote:{//远程异步校验
	                         url: WEBPATH+'/sys/checkOldPass.do',
	                         type: 'POST',
	                         data:{oldPass:$(this).val()}
	                         
	                       }
	                    }
	                
	            },
	            password: {
	                validators: {
	                    notEmpty: {
	                        message: '新密码不能为空'
	                    },
	                    different: {
	                        field: 'oldPass',
	                        message: '新密码不能和原密码相同'
	                    },stringLength: {
	                        min: 8,
	                        max: 20,
	                        message: '密码长度为8-20个字符'
	                    },
	                    regexp: {
	                    	regexp: /^(?=.*[0-9])(?=.*[a-zA-Z])(?=.*[@]).{8,20}$/,
	                        message: '密码由字母、数字和@组成'
	                    }
	                }
	            },
	            repassword: {
	                validators: {
	                    notEmpty: {
	                        message: '新密码确认为必填项'
	                    },
	                    identical:{
	                    	 field: 'password',
       						 message: '确认密码与新密码不相同'
	                    }
	                }
	            } 
	            
	        }
	    });

	});
	
	//重置密码
	function reset(){

	    var validate = $("#passForm").data('formValidation').isValid();
	    //校验通过:null，false  ,true
	    if(validate){
	        	
	    	business.ajaxPostJson(WEBPATH+"/sys/resetPass.do", {password:$("#password").val()}, function(ret){
	    		
	    		if(ret.type=="error"){
	                 swal("修改密码失败!", "", "error");
	                 return false;
	             }else if(ret.type=="success"){
	                swal("修改密码成功！", "", "success");
	                $("#passForm").data('formValidation').resetForm();
	                $("#passForm")[0].reset();
	             }
	    	});
	    }else if(validate==null){
	      //某些表单未填写
	      
	        $("#passForm").data('formValidation').validate();
	       //$("#passForm").formValidation('validateField', 'oldPass');
	      // $("#passForm").formValidation('validateField', 'password');
	      /// $("#passForm").formValidation('validateField', 'repassword');
	    }
	   
	}
</script>
