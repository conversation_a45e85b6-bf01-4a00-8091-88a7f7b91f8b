<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://java.sun.com/xml/ns/javaee" xmlns:web="http://java.sun.com/xml/ns/javaee" xmlns:web_1="http://java.sun.com/xml/ns/javaee/web-app_2_5.xsd" xsi:schemaLocation="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-app_2_5.xsd" id="WebApp_ID" version="2.5">
  <!-- log4j配置 -->
  <context-param>
    <param-name>log4jConfigLocation</param-name>
    <param-value>classpath:log4j.properties</param-value>
  </context-param>
  <listener>
    <listener-class>org.springframework.web.util.Log4jConfigListener</listener-class>
  </listener>
  
  <listener>
  	<listener-class>com.changneng.sa.listener.MyListener</listener-class>  
  </listener>
  
  
  <!-- spring配置 -->
  <context-param>
    <param-name>contextConfigLocation</param-name>
     <param-value>classpath:config/applicationContext.xml,classpath:config/spring-redis.xml</param-value>
  </context-param>
 
  <listener>
    <listener-class>org.springframework.web.context.ContextLoaderListener</listener-class>
  </listener>
  
  <context-param>
	<param-name>webAppRootKey</param-name>
	<param-value>DLB2019.root</param-value>
  </context-param>
  
  
  <!-- hibernate OpenSessionInViewFilter -->
 <!--  <filter>
    <filter-name>hibernateFilter</filter-name>
    <filter-class>org.springframework.orm.hibernate4.support.OpenSessionInViewFilter</filter-class>
    <init-param>
      <param-name>singleSession</param-name>
      <param-value>true</param-value>
    </init-param>
  </filter>
  <filter-mapping>
    <filter-name>hibernateFilter</filter-name>
    <url-pattern>*.do</url-pattern>
  </filter-mapping> -->
  
  <!-- 字符编码过滤器  -->
  <filter>
    <filter-name>encoding-filter</filter-name>
    <filter-class>org.springframework.web.filter.CharacterEncodingFilter</filter-class>
    <init-param>
      <param-name>encoding</param-name>
      <param-value>UTF-8</param-value>
    </init-param>
  </filter>
  
  <filter-mapping>
    <filter-name>encoding-filter</filter-name>
    <url-pattern>*.do</url-pattern>
  </filter-mapping>
 
  <!-- 请求分派器 -->
  <servlet>
    <servlet-name>springmvc</servlet-name>
    <servlet-class>org.springframework.web.servlet.DispatcherServlet</servlet-class>
    <init-param>
      <param-name>contextConfigLocation</param-name>
      <param-value>classpath*:config/springmvc-servlet.xml</param-value>
    </init-param>
    <load-on-startup>1</load-on-startup>
  </servlet>
  <servlet-mapping>
    <servlet-name>springmvc</servlet-name>
    <url-pattern>*.do</url-pattern>
  </servlet-mapping>
    
  
  
  <filter>  
        <filter-name>DruidWebStatFilter</filter-name>  
        <filter-class>com.alibaba.druid.support.http.WebStatFilter</filter-class>  
        <init-param>  
            <!-- 排除不必要的url .js,/jslib/等等。配置在init-param中 -->  
            <param-name>exclusions</param-name>  
            <param-value>*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*</param-value>  
        </init-param>  
        <!-- 缺省sessionStatMaxCount是1000个。你可以按需要进行配置 -->  
        <init-param>  
            <param-name>sessionStatMaxCount</param-name>  
            <param-value>1000</param-value>  
        </init-param>  
        <!-- druid 0.2.7版本开始支持profile，配置profileEnable能够监控单个url调用的sql列表 -->  
        <init-param>  
            <param-name>profileEnable</param-name>  
            <param-value>true</param-value>  
        </init-param>  
       
        <!-- session统计功能   -->  
        <init-param>   
            <param-name>sessionStatEnable</param-name>   
            <param-value>true</param-value>  
        </init-param> 
         <init-param>
                 <param-name>principalSessionName</param-name>
                 <param-value>MoniterSessionUser</param-value>
      </init-param>
       
       
    </filter>  
    <filter-mapping>  
        <filter-name>DruidWebStatFilter</filter-name>  
        <url-pattern>/*</url-pattern>  
    </filter-mapping>
  
  	<!-- 配置session超时时间，单位分钟 -->
	<session-config>
		<session-timeout>360</session-timeout>
	</session-config>
  
  <!-- druid web 监控 -->  
    <servlet>  
        <servlet-name>DruidStatView</servlet-name>  
        <servlet-class>com.alibaba.druid.support.http.StatViewServlet</servlet-class>  
        <!--   
            配置ip访问权限目前不限制：deny优先于allow，如果在deny列表中，就算在allow列表中，也会被拒绝。  
            如果allow没有配置或者为空，则允许所有访问  
         -->  
       <!--  <init-param>  
            <param-name>allow</param-name>  
            <param-value>ip,ip</param-value>  
        </init-param>  
        <init-param>  
            <param-name>deny</param-name>  
            <param-value>ip,ip</param-value>  
        </init-param>  --> 
        <!-- 在StatViewSerlvet输出的html页面中，有一个功能是Reset All，执行这个操作之后，会导致所有计数器清零，重新计数 -->  
         <init-param>  
            <param-name>resetEnable</param-name>  
            <param-value>true</param-value>  
        </init-param>  
       
       <!--  用户名和密码 -->  
       <init-param>  
            <param-name>loginUsername</param-name>  
            <param-value>changneng</param-value>  
        </init-param>  
        <init-param>  
            <param-name>loginPassword</param-name>  
            <param-value>chn12369</param-value>  
        </init-param>  
    </servlet>  
    <servlet-mapping>  
        <servlet-name>DruidStatView</servlet-name>  
        <url-pattern>/druid/*</url-pattern>
    </servlet-mapping>  
    <!-- 页面找不到错误 -->  
    <error-page>
        <error-code>404</error-code>  
        <location>/error/404.jsp</location>  
    </error-page>  
     <!-- 系统错误-->
    <error-page>  
        <error-code>500</error-code>  
        <location>/error/500.jsp</location>  
    </error-page>




    <!-- 欢迎页 -->
  <welcome-file-list>
    <welcome-file>index.html</welcome-file>
  </welcome-file-list>
</web-app>